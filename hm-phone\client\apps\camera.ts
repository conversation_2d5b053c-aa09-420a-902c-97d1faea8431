/**
 * Camera App - Client Side
 *
 * This file handles client-side functionality for the Camera app.
 * The actual camera view is handled by the GameView component in the UI.
 * We use the first-person gameplay camera instead of creating a separate camera entity.
 *
 * Key bindings are used to control the camera when direct UI interaction isn't possible.
 */

import { registerAppHandler } from '../nui';
import { cameraControlsService } from '../services/cameraControls';
import { sendToNUI } from '../nui';

// Camera state
let isFlashEnabled = false;
let isCameraActive = false;
let isSelfieMode = false;
let isPhotoMode = true;
let isRecording = false;
let recordingStartTime = 0;
let cameraHelpTickId: number | null = null;

/**
 * Initialize the camera app
 */
export function initializeCameraApp(): void {
    console.log('[Camera] Initializing client-side camera app');

    // Register client events
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();

    // Register camera commands and key bindings
    registerCameraCommands();
}

/**
 * Register client events for the camera app
 */
function registerClientEvents(): void {
    // Register event for photo saved
    onNet('hm-phone:photoSaved', (photoData: any) => {
        console.log('[Camera] Photo saved event received:', photoData);

        // Send the photo data to the UI
        sendToUI('photoSaved', photoData);
    });

    // Register event for photo save error
    onNet('hm-phone:photoSaveError', (errorMessage: string) => {
        console.error('[Camera] Photo save error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });

    // Register event for video saved
    onNet('hm-phone:videoSaved', (videoData: any) => {
        console.log('[Camera] Video saved event received:', videoData);

        // Send the video data to the UI
        sendToUI('videoSaved', videoData);
    });

    // Register event for video save error
    onNet('hm-phone:videoSaveError', (errorMessage: string) => {
        console.error('[Camera] Video save error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });
}

/**
 * Register camera commands and key bindings
 */
function registerCameraCommands(): void {
    // Register command for taking photos
    RegisterCommand(
        'camera_takephoto',
        () => {
            if (isCameraActive && isPhotoMode) {
                takePhoto();
            }
        },
        false
    );

    // Register command for toggling recording
    RegisterCommand(
        'camera_togglerecording',
        () => {
            if (isCameraActive && !isPhotoMode) {
                toggleRecording();
            }
        },
        false
    );

    // Register command for switching camera modes
    RegisterCommand(
        'camera_togglemode',
        () => {
            if (isCameraActive) {
                toggleCameraMode();
            }
        },
        false
    );

    // Register command for toggling selfie mode
    RegisterCommand(
        'camera_toggleselfie',
        () => {
            if (isCameraActive) {
                toggleSelfieMode();
            }
        },
        false
    );

    // Register command for toggling flash
    RegisterCommand(
        'camera_toggleflash',
        () => {
            if (isCameraActive) {
                toggleFlash();
            }
        },
        false
    );

    // Filter functionality has been removed

    // Register key mappings to match the control names in help text
    // INPUT_FRONTEND_X = SPACEBAR (X on controller) - Take Photo/Record
    RegisterKeyMapping('camera_takephoto', 'Take Photo/Record', 'keyboard', 'SPACE');

    // INPUT_FRONTEND_Y = TAB (Y on controller) - Toggle Selfie Mode
    RegisterKeyMapping('camera_toggleselfie', 'Toggle Selfie Mode', 'keyboard', 'TAB');

    // INPUT_FRONTEND_LB = Q (LB on controller) - Switch Camera Mode
    RegisterKeyMapping('camera_togglemode', 'Switch Camera Mode', 'keyboard', 'Q');

    // INPUT_FRONTEND_RB = R (RB on controller) - Toggle Flash
    RegisterKeyMapping('camera_toggleflash', 'Toggle Flash', 'keyboard', 'R');

    // Filter key mapping removed

    console.log('[Camera] Registered camera commands and key bindings');
}

/**
 * Register NUI handlers for the camera app
 */
function registerNUIHandlers(): void {
    // Register handler for opening the camera
    registerAppHandler('camera', 'openCamera', async (data: any) => {
        console.log('[Camera] Received openCamera request from UI:', data);

        try {
            // Extract settings
            const { settings } = data;
            isFlashEnabled = settings?.flashEnabled || false;
            isSelfieMode = settings?.currentMode === 'selfie';

            // Enable first-person camera mode if not already active
            if (!isCameraActive) {
                // Enable first-person camera and looking controls
                cameraControlsService.enableFirstPersonCamera();

                // Set camera as active
                isCameraActive = true;

                // Start showing camera controls help
                startCameraHelpText();

                console.log('[Camera] Enabled first-person camera mode');
            }

            // Apply flash if enabled
            if (isFlashEnabled) {
                enableFlash();
            }

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Camera] Error opening camera:', error);
            return { success: false, error: 'Failed to open camera' };
        }
    });

    // Register handler for closing the camera
    registerAppHandler('camera', 'closeCamera', async () => {
        console.log('[Camera] Received closeCamera request from UI');

        try {
            // Disable flash if enabled
            if (isFlashEnabled) {
                disableFlash();
            }

            // Disable first-person camera mode
            if (isCameraActive) {
                // Disable first-person camera and restore original view
                cameraControlsService.disableFirstPersonCamera();

                // Set camera as inactive
                isCameraActive = false;

                // Stop showing camera controls help
                stopCameraHelpText();

                console.log('[Camera] Disabled first-person camera mode');
            }

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Camera] Error closing camera:', error);
            return { success: false, error: 'Failed to close camera' };
        }
    });

    // Register handler for toggling flash
    registerAppHandler('camera', 'toggleFlash', async () => {
        console.log('[Camera] Received toggleFlash request from UI');

        try {
            toggleFlash();

            // Return success to the UI with the new flash state
            return { success: true, flashEnabled: isFlashEnabled };
        } catch (error) {
            console.error('[Camera] Error toggling flash:', error);
            return { success: false, error: 'Failed to toggle flash' };
        }
    });

    // Register handler for toggling selfie mode
    registerAppHandler('camera', 'toggleSelfieMode', async () => {
        console.log('[Camera] Received toggleSelfieMode request from UI');

        try {
            toggleSelfieMode();

            // Return success to the UI with the new selfie mode state
            return { success: true, selfieMode: isSelfieMode };
        } catch (error) {
            console.error('[Camera] Error toggling selfie mode:', error);
            return { success: false, error: 'Failed to toggle selfie mode' };
        }
    });

    // Register handler for switching camera mode
    registerAppHandler('camera', 'switchMode', async (data: any) => {
        console.log('[Camera] Received switchMode request from UI:', data);

        try {
            // Extract mode
            const { mode } = data;
            isPhotoMode = mode === 'photo';

            // Return success to the UI
            return { success: true, mode: isPhotoMode ? 'photo' : 'video' };
        } catch (error) {
            console.error('[Camera] Error switching mode:', error);
            return { success: false, error: 'Failed to switch mode' };
        }
    });

    // Register NUI callback for photo taken
    RegisterNuiCallbackType('photoTaken');
    on('__cfx_nui:photoTaken', (data: { image: string }, cb: (response: any) => void) => {
        console.log('[Camera] Received photoTaken callback from UI');

        // Process the photo
        const { image } = data;

        // Send the photo data to the server via the photos app
        emitNet('hm-phone:savePhoto', image, {
            mode: isSelfieMode ? 'selfie' : 'photo',
            flash: isFlashEnabled
        });

        // Play camera shutter sound
        PlaySoundFrontend(-1, 'Camera_Shoot', 'Phone_Soundset_Franklin', true);

        // Send response back to NUI
        cb({ success: true });
    });

    // Register NUI callback for video taken
    RegisterNuiCallbackType('videoTaken');
    on('__cfx_nui:videoTaken', (data: { video: string }, cb: (response: any) => void) => {
        console.log('[Camera] Received videoTaken callback from UI');

        // Process the video
        const { video } = data;

        // Send the video data to the server
        emitNet('hm-phone:saveVideo', video, {
            mode: isSelfieMode ? 'selfie' : 'video',
            flash: isFlashEnabled
        });

        // Play a sound for video capture
        PlaySoundFrontend(-1, 'Camera_Shoot', 'Phone_Soundset_Franklin', true);

        // Send response back to NUI
        cb({ success: true });
    });

    // Register NUI callback for toggling preview mode
    RegisterNuiCallbackType('togglePreviewMode');
    on('__cfx_nui:togglePreviewMode', (data: { enabled: boolean }, cb: (response: any) => void) => {
        console.log('[Camera] Received togglePreviewMode callback from UI:', data);

        try {
            if (data.enabled) {
                // Enable full NUI focus for preview mode
                console.log('[Camera] Enabling full NUI focus for preview mode');
                SetNuiFocus(true, true);
                SetNuiFocusKeepInput(false); // Disable game input while in preview mode
            } else {
                // Restore camera controls when exiting preview mode
                console.log('[Camera] Restoring camera controls for camera mode');
                // Restore the camera controls settings
                SetNuiFocus(false, true); // Keep mouse focus for UI but disable keyboard focus
                SetNuiFocusKeepInput(true); // Allow game input while NUI is focused
            }

            // Send response back to NUI
            cb({ success: true });
        } catch (error) {
            console.error('[Camera] Error toggling preview mode:', error);
            cb({ success: false, error: 'Failed to toggle preview mode' });
        }
    });
}

/**
 * Take a photo
 */
function takePhoto(): void {
    if (!isCameraActive || !isPhotoMode) return;

    console.log('[Camera] Taking photo');

    // Tell the UI to capture a photo
    sendToNUI('camera', 'capturePhoto', {});

    // Play camera shutter sound
    PlaySoundFrontend(-1, 'Camera_Shoot', 'Phone_Soundset_Franklin', true);

    // Apply flash effect if enabled
    if (isFlashEnabled) {
        // Create a brief flash effect
        SetTimecycleModifier('phone_cam_flash');

        // Remove flash effect after a short delay
        setTimeout(() => {
            if (isFlashEnabled) {
                // Only clear if flash is still enabled
                ClearTimecycleModifier();
                SetTimecycleModifier('phone_cam');
            } else {
                ClearTimecycleModifier();
            }
        }, 200);
    }
}

/**
 * Toggle recording
 */
function toggleRecording(): void {
    if (!isCameraActive || isPhotoMode) return;

    // Toggle recording state
    isRecording = !isRecording;

    if (isRecording) {
        // Start recording
        recordingStartTime = GetGameTimer();

        // Tell the UI to start recording
        sendToNUI('camera', 'startRecording', {});

        // Play start recording sound
        PlaySoundFrontend(-1, 'Start_Recording', 'CELL_CAMERA_SOUNDSET', true);

        console.log('[Camera] Started recording');
    } else {
        // Stop recording
        const recordingDuration = (GetGameTimer() - recordingStartTime) / 1000; // in seconds

        // Tell the UI to stop recording
        sendToNUI('camera', 'stopRecording', {
            duration: recordingDuration
        });

        // Play stop recording sound
        PlaySoundFrontend(-1, 'Stop_Recording', 'CELL_CAMERA_SOUNDSET', true);

        console.log('[Camera] Stopped recording, duration:', recordingDuration);
    }
}

/**
 * Toggle camera mode between photo and video
 */
function toggleCameraMode(): void {
    if (!isCameraActive) return;

    // Toggle photo mode
    isPhotoMode = !isPhotoMode;

    // If we were recording, stop recording
    if (!isPhotoMode && isRecording) {
        toggleRecording();
    }

    // Tell the UI to switch modes
    sendToNUI('camera', 'setMode', {
        mode: isPhotoMode ? 'photo' : 'video'
    });

    // Play mode switch sound
    PlaySoundFrontend(-1, 'Menu_Navigate', 'Phone_SoundSet_Default', true);

    console.log('[Camera] Switched to', isPhotoMode ? 'photo' : 'video', 'mode');
}

/**
 * Toggle selfie mode
 */
function toggleSelfieMode(): void {
    if (!isCameraActive) return;

    // Toggle selfie mode
    isSelfieMode = !isSelfieMode;

    // Implement selfie mode by rotating the player
    const playerPed = PlayerPedId();
    if (isSelfieMode) {
        // Get current heading
        const currentHeading = GetEntityHeading(playerPed);
        // Rotate player 180 degrees
        SetEntityHeading(playerPed, currentHeading + 180.0);
    } else {
        // Get current heading
        const currentHeading = GetEntityHeading(playerPed);
        // Rotate player back
        SetEntityHeading(playerPed, currentHeading + 180.0);
    }

    // Tell the UI to update selfie mode
    sendToNUI('camera', 'setSelfieMode', {
        selfieMode: isSelfieMode
    });

    // Play selfie mode toggle sound
    PlaySoundFrontend(-1, 'Menu_Navigate', 'Phone_SoundSet_Default', true);

    console.log('[Camera] Toggled selfie mode:', isSelfieMode);
}

/**
 * Toggle flash
 */
function toggleFlash(): void {
    if (!isCameraActive) return;

    // Toggle flash
    isFlashEnabled = !isFlashEnabled;

    if (isFlashEnabled) {
        enableFlash();
    } else {
        disableFlash();
    }

    // Tell the UI to update flash state
    sendToNUI('camera', 'setFlash', {
        flashEnabled: isFlashEnabled
    });

    // Play flash toggle sound
    PlaySoundFrontend(-1, 'Menu_Navigate', 'Phone_SoundSet_Default', true);

    console.log('[Camera] Toggled flash:', isFlashEnabled);
}

// Filter functionality has been removed

/**
 * Start showing camera controls help text
 *
 * Using official FiveM control names:
 * INPUT_FRONTEND_ACCEPT = ENTER / NUMPAD ENTER (A on controller)
 * INPUT_FRONTEND_CANCEL = BACKSPACE / ESC (B on controller)
 * INPUT_FRONTEND_X = SPACEBAR (X on controller)
 * INPUT_FRONTEND_Y = TAB (Y on controller)
 * INPUT_FRONTEND_RB = R (RB on controller)
 * INPUT_FRONTEND_LB = Q (LB on controller)
 */
function startCameraHelpText(): void {
    if (cameraHelpTickId !== null) return;

    cameraHelpTickId = setTick(() => {
        if (isCameraActive) {
            // Display different help text based on mode
            if (isPhotoMode) {
                BeginTextCommandDisplayHelp('STRING');
                AddTextComponentSubstringPlayerName(
                    '~INPUT_FRONTEND_X~ Take Photo   ~INPUT_FRONTEND_CANCEL~ Exit   ~INPUT_FRONTEND_RB~ Toggle Flash   ~INPUT_FRONTEND_Y~ Toggle Selfie   ~INPUT_FRONTEND_LB~ Switch Mode'
                );
                EndTextCommandDisplayHelp(0, false, true, 5000);
            } else {
                BeginTextCommandDisplayHelp('STRING');
                AddTextComponentSubstringPlayerName(
                    '~INPUT_FRONTEND_X~ ' +
                        (isRecording ? 'Stop Recording' : 'Start Recording') +
                        '   ~INPUT_FRONTEND_CANCEL~ Exit   ~INPUT_FRONTEND_RB~ Toggle Flash   ~INPUT_FRONTEND_Y~ Toggle Selfie   ~INPUT_FRONTEND_LB~ Switch Mode'
                );
                EndTextCommandDisplayHelp(0, false, true, 5000);
            }
        }
    });
}

/**
 * Stop showing camera controls help text
 */
function stopCameraHelpText(): void {
    if (cameraHelpTickId !== null) {
        clearTick(cameraHelpTickId);
        cameraHelpTickId = null;
    }
}

/**
 * Enable flash effect
 */
function enableFlash(): void {
    // Implement flash effect using a bright timecycle modifier
    SetTimecycleModifier('phone_cam_flash');
}

/**
 * Disable flash effect
 */
function disableFlash(): void {
    // Remove flash effect
    ClearTimecycleModifier();
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'camera',
        type,
        data
    });
}
