import React from 'react';
import { useNavigation } from '../../../navigation/hooks';
import SongCard from './SongCard';
import { useMusicStore } from '../stores/musicStore';

const Artist: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const { artists, albums, songs, currentArtistId, setCurrentArtistId, getCurrentArtist } =
    useMusicStore();

  // Get the current artist
  const artist = getCurrentArtist();

  // Get albums for this artist
  const artistAlbums = currentArtistId ? albums.filter(a => a.artistId === currentArtistId) : [];

  // Get songs for this artist
  const artistSongs = currentArtistId ? songs.filter(s => s.artistId === currentArtistId) : [];

  // Get similar artists (just other artists in this case)
  const similarArtists = currentArtistId
    ? artists.filter(a => a.id !== currentArtistId).slice(0, 3)
    : [];

  if (!artist) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-[#121212] text-white">
        Loading...
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white pt-[32px]">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-2">
        <button onClick={goBack} className="text-white cursor-pointer">
          <i className="fas fa-arrow-left text-xl"></i>
        </button>
        <div className="text-center">
          <h1 className="text-lg font-medium">Artist</h1>
        </div>
        <button className="text-white cursor-pointer">
          <i className="fas fa-ellipsis-v"></i>
        </button>
      </div>

      {/* Artist Compact Header */}
      <div className="px-4 py-3 flex items-center border-b border-gray-800">
        <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-pink-500/30 mr-4">
          <img
            src={artist.imageUrl || `https://picsum.photos/400/400?random=${100 + artist.id}`}
            alt={artist.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1">
          <div className="flex items-center">
            <h2 className="text-xl font-bold mr-1">{artist.name}</h2>
            <i className="fas fa-check-circle text-pink-500 text-sm"></i>
          </div>
          <p className="text-sm text-gray-400 line-clamp-1">Artist from the music industry...</p>
          <p className="text-xs text-gray-500 mt-1">
            {artistSongs.length} songs • {artistAlbums.length} albums
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex px-4 py-2 space-x-2">
        <button className="bg-pink-500 text-white px-4 py-1.5 rounded-full text-sm font-medium flex-1 cursor-pointer">
          Play All
        </button>
        <button className="border border-white/30 text-white px-4 py-1.5 rounded-full text-sm flex-1 cursor-pointer">
          Add to Playlist
        </button>
        <button className="border border-white/30 text-white w-9 h-9 rounded-full flex items-center justify-center cursor-pointer">
          <i className="fas fa-heart"></i>
        </button>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto pt-2">
        {/* Bio section removed since we have a preview in the header */}

        {/* Top Songs */}
        <div className="mb-4">
          <div className="px-4 flex justify-between items-center mb-2">
            <h2 className="text-base font-semibold">Top Songs</h2>
            <button className="text-xs text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="space-y-2 px-4">
            {artistSongs.slice(0, 5).map(song => (
              <SongCard
                key={song.id}
                song={{
                  id: song.id,
                  title: song.title,
                  artist: artist,
                  imageUrl: song.imageUrl,
                  duration: `${Math.floor(song.duration / 60)}:${(song.duration % 60)
                    .toString()
                    .padStart(2, '0')}`,
                  plays: `${Math.floor(Math.random() * 900) + 100}K`
                }}
                showDuration={true}
                showPlays={true}
                compact={true}
              />
            ))}
          </div>
        </div>

        {/* Albums */}
        <div className="mb-4">
          <div className="px-4 flex justify-between items-center mb-2">
            <h2 className="text-base font-semibold">Albums</h2>
            <button className="text-xs text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="flex overflow-x-auto space-x-4 px-4 pb-2">
            {artistAlbums.map(album => (
              <div
                key={album.id}
                className="flex-shrink-0 w-28 cursor-pointer"
                onClick={() => openView('album', { albumId: album.id })}
              >
                <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
                  <img
                    src={album.imageUrl}
                    alt={album.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-sm font-medium text-white truncate">{album.title}</h3>
                <p className="text-xs text-gray-400 truncate">{new Date().getFullYear()}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Similar Artists */}
        <div className="mb-4">
          <div className="px-4 flex justify-between items-center mb-2">
            <h2 className="text-base font-semibold">Similar Artists</h2>
            <button className="text-xs text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="flex overflow-x-auto space-x-5 px-4 pb-2">
            {similarArtists.map(similarArtist => (
              <div
                key={similarArtist.id}
                className="flex-shrink-0 flex flex-col items-center cursor-pointer"
                onClick={() => {
                  setCurrentArtistId(similarArtist.id);
                  openView('artist', { artistId: similarArtist.id });
                }}
              >
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-2 border-2 border-pink-500/30">
                  <img
                    src={
                      similarArtist.imageUrl ||
                      `https://picsum.photos/200/200?random=${100 + similarArtist.id}`
                    }
                    alt={similarArtist.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xs font-medium text-white text-center w-20 truncate">
                  {similarArtist.name}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Artist;
