/**
 * Client-side inventory zone management
 * Integrates with hm-polyzones for inventory region detection
 */

// Track active inventory zones
const activeInventoryZones = new Map<string, {
    position: { x: number, y: number, z: number },
    radius: number,
    items: Record<string, any>
}>();

// Current zones player is in
const currentZones = new Set<string>();

// Debug configuration
let debugMode = false;
const debugMarkers = new Map<string, number>(); // zoneId -> markerId

/**
 * Initialize polyzone integration for inventory
 */
function initializeInventoryZones(): void {
    console.log('[Inventory] Initializing inventory zone integration');
    
    // Listen for polyzone enter/exit events
    on('hm-polyzones:enterZone', (zoneName: string) => {
        if (zoneName.startsWith('inventory_zone_')) {
            console.log(`[Inventory] Entered inventory zone: ${zoneName}`);
            currentZones.add(zoneName);
            updateGroundPanelFromZones();
        }
    });
    
    on('hm-polyzones:exitZone', (zoneName: string) => {
        if (zoneName.startsWith('inventory_zone_')) {
            console.log(`[Inventory] Exited inventory zone: ${zoneName}`);
            currentZones.delete(zoneName);
            updateGroundPanelFromZones();
        }
    });
}

/**
 * Update ground panel based on current zones
 */
function updateGroundPanelFromZones(): void {
    // Collect items from all current zones
    const allItems: any[] = [];
    
    for (const zoneId of currentZones) {
        const zone = activeInventoryZones.get(zoneId);
        if (zone && zone.items) {
            for (const [itemId, item] of Object.entries(zone.items)) {
                allItems.push({
                    entityId: parseInt(itemId),
                    definitionId: item.definitionId,
                    quantity: item.quantity,
                    distanceCategory: 'close' as const,
                    distance: 1.0, // Zone-based items are considered close
                    metadata: item.metadata
                });
            }
        }
    }
    
    // Update UI with zone-based items
    if (allItems.length > 0) {
        console.log(`[Inventory] Updating ground panel with ${allItems.length} zone items`);
        // TODO: Replace with proper UI update when SecondaryInventoryPanel is accessible
        // For now, emit to existing ground panel system
        emit('hm-inventory:client:updateGroundPanel', allItems);
    } else {
        // Clear ground panel when no zone items
        emit('hm-inventory:client:updateGroundPanel', []);
    }
}

/**
 * Create inventory zone using polyzones
 */
function createInventoryZone(data: {
    zoneId: string,
    position: { x: number, y: number, z: number },
    radius: number,
    items?: Record<string, any>
}): void {
    console.log(`[Inventory] Creating inventory zone ${data.zoneId} at`, data.position);
    
    // Store zone data
    const zoneData = {
        position: data.position,
        radius: data.radius,
        items: data.items || {}
    };
    activeInventoryZones.set(data.zoneId, zoneData);
    
    // Create debug marker if debug mode is active
    if (debugMode) {
        createDebugMarker(data.zoneId, zoneData);
    }
    
    // Create circle zone using polyzones exports
    try {
        const polyzoneManager = exports['hm-polyzones'];
        if (polyzoneManager && polyzoneManager.createCircleZone) {
            polyzoneManager.createCircleZone(
                data.zoneId,
                { x: data.position.x, y: data.position.y },
                data.radius,
                {
                    minZ: data.position.z - 2.0,
                    maxZ: data.position.z + 2.0,
                    debugColor: [100, 200, 255, 150] // Light blue for inventory zones
                }
            );
            console.log(`[Inventory] Successfully created polyzone ${data.zoneId}`);
        } else {
            console.error('[Inventory] hm-polyzones exports not available');
        }
    } catch (error) {
        console.error(`[Inventory] Error creating polyzone for ${data.zoneId}:`, error);
    }
    
    // Register with target system for pickup interactions
    registerInventoryZoneWithTarget(data.zoneId, data.position);
}

/**
 * Update existing inventory zone items
 */
function updateInventoryZone(data: {
    zoneId: string,
    items: Record<string, any>
}): void {
    const zone = activeInventoryZones.get(data.zoneId);
    if (zone) {
        zone.items = data.items;
        console.log(`[Inventory] Updated zone ${data.zoneId} with ${Object.keys(data.items).length} items`);
        
        // Update debug marker if debug mode is active
        if (debugMode) {
            createDebugMarker(data.zoneId, zone);
        }
        
        // If player is currently in this zone, update UI
        if (currentZones.has(data.zoneId)) {
            updateGroundPanelFromZones();
        }
    }
}

/**
 * Destroy inventory zone
 */
function destroyInventoryZone(data: { zoneId: string }): void {
    console.log(`[Inventory] Destroying inventory zone ${data.zoneId}`);
    
    // Unregister from target system first
    unregisterInventoryZoneFromTarget(data.zoneId);
    
    // Remove debug marker if it exists
    debugMarkers.delete(data.zoneId);
    
    // Remove from active zones
    activeInventoryZones.delete(data.zoneId);
    currentZones.delete(data.zoneId);
    
    // Remove polyzone
    try {
        const polyzoneManager = exports['hm-polyzones'];
        if (polyzoneManager && polyzoneManager.removeZone) {
            polyzoneManager.removeZone(data.zoneId);
        }
    } catch (error) {
        console.error(`[Inventory] Error removing polyzone ${data.zoneId}:`, error);
    }
    
    // Update ground panel if needed
    updateGroundPanelFromZones();
}

/**
 * Register inventory zone with target system for pickup interactions
 */
function registerInventoryZoneWithTarget(zoneId: string, position: { x: number, y: number, z: number }): void {
    try {
        const targetAPI = exports['hm-target'];
        if (targetAPI && targetAPI.addSphereZone) {
            // Create target zone for pickup interactions
            targetAPI.addSphereZone(
                `pickup_${zoneId}`,
                position,
                2.0, // radius
                [
                    {
                        id: `pickup_zone_${zoneId}`,
                        label: 'Pick up items',
                        icon: 'fas fa-hand-paper',
                        action: 'hm-inventory:pickupFromZone',
                        canInteract: () => {
                            // Only show if zone has items
                            const zone = activeInventoryZones.get(zoneId);
                            return zone && Object.keys(zone.items).length > 0;
                        }
                    }
                ]
            );
            console.log(`[Inventory] Registered target zone for ${zoneId}`);
        } else {
            console.warn('[Inventory] hm-target not available for zone registration');
        }
    } catch (error) {
        console.error(`[Inventory] Error registering target zone for ${zoneId}:`, error);
    }
}

/**
 * Unregister inventory zone from target system
 */
function unregisterInventoryZoneFromTarget(zoneId: string): void {
    try {
        const targetAPI = exports['hm-target'];
        if (targetAPI && targetAPI.removeZone) {
            targetAPI.removeZone(`pickup_${zoneId}`);
            console.log(`[Inventory] Unregistered target zone for ${zoneId}`);
        }
    } catch (error) {
        console.error(`[Inventory] Error unregistering target zone for ${zoneId}:`, error);
    }
}

/**
 * Toggle debug mode for inventory zones
 */
function toggleInventoryZoneDebug(): void {
    debugMode = !debugMode;
    console.log(`[Inventory] Debug mode ${debugMode ? 'enabled' : 'disabled'}`);
    
    if (debugMode) {
        // Show all existing zones
        for (const [zoneId, zone] of activeInventoryZones) {
            createDebugMarker(zoneId, zone);
        }
        startDebugThread();
    } else {
        // Remove all debug markers
        clearAllDebugMarkers();
        stopDebugThread();
    }
}

/**
 * Create debug marker for zone
 */
function createDebugMarker(zoneId: string, zone: { position: { x: number, y: number, z: number }, radius: number, items: Record<string, any> }): void {
    if (!debugMode) return;
    
    // Count items in zone
    const itemCount = Object.keys(zone.items).length;
    
    // Create marker with different colors based on item count
    let markerType = 1; // Circle marker
    let r = 0, g = 255, b = 0; // Green for empty/low
    
    if (itemCount > 5) {
        r = 255; g = 165; b = 0; // Orange for medium
    }
    if (itemCount > 10) {
        r = 255; g = 0; b = 0; // Red for high
    }
    
    console.log(`[Inventory Debug] Creating marker for zone ${zoneId} with ${itemCount} items at`, zone.position);
    
    // Store marker info for cleanup
    debugMarkers.set(zoneId, Date.now());
}

/**
 * Clear all debug markers
 */
function clearAllDebugMarkers(): void {
    debugMarkers.clear();
    console.log('[Inventory Debug] Cleared all debug markers');
}

/**
 * Debug drawing thread
 */
let debugThreadActive = false;

function startDebugThread(): void {
    if (debugThreadActive) return;
    debugThreadActive = true;
    
    const debugTick = setTick(() => {
        if (!debugMode) {
            clearTick(debugTick);
            debugThreadActive = false;
            return;
        }
        
        // Draw zone boundaries and info
        for (const [zoneId, zone] of activeInventoryZones) {
            const { position, radius, items } = zone;
            const itemCount = Object.keys(items).length;
            
            // Draw zone circle
            DrawMarker(
                1, // Circle marker
                position.x, position.y, position.z + 0.1,
                0.0, 0.0, 0.0,
                0.0, 0.0, 0.0,
                radius * 2.0, radius * 2.0, 0.5,
                0, 255, 0, 100, // Green with transparency
                false, true, 2, false, "", "", false
            );
            
            // Draw text info above zone
            const screenPos = World3dToScreen2d(position.x, position.y, position.z + 2.0);
            if (screenPos[0]) {
                SetTextScale(0.35, 0.35);
                SetTextFont(4);
                SetTextProportional(true);
                SetTextColour(255, 255, 255, 215);
                SetTextOutline();
                SetTextEntry("STRING");
                AddTextComponentString(`Zone: ${zoneId.substring(15, 25)}...`);
                DrawText(screenPos[1], screenPos[2]);
                
                // Draw item count below
                const itemScreenPos = World3dToScreen2d(position.x, position.y, position.z + 1.5);
                if (itemScreenPos[0]) {
                    SetTextScale(0.3, 0.3);
                    SetTextFont(4);
                    SetTextProportional(true);
                    SetTextColour(255, 255, 0, 215);
                    SetTextOutline();
                    SetTextEntry("STRING");
                    AddTextComponentString(`Items: ${itemCount}`);
                    DrawText(itemScreenPos[1], itemScreenPos[2]);
                }
            }
            
            // Draw individual item positions if zone has items
            for (const [itemId, item] of Object.entries(items)) {
                if (item.position) {
                    // Small marker for each item
                    DrawMarker(
                        0, // Up arrow
                        item.position[0], item.position[1], item.position[2] + 1.0,
                        0.0, 0.0, 0.0,
                        0.0, 0.0, 0.0,
                        0.3, 0.3, 0.5,
                        255, 255, 0, 200, // Yellow
                        true, true, 2, false, "", "", false
                    );
                }
            }
        }
        
        // Draw player's current zones
        if (currentZones.size > 0) {
            let yOffset = 0.02;
            for (const zoneId of currentZones) {
                SetTextScale(0.4, 0.4);
                SetTextFont(4);
                SetTextProportional(true);
                SetTextColour(0, 255, 0, 255);
                SetTextOutline();
                SetTextEntry("STRING");
                AddTextComponentString(`In Zone: ${zoneId.substring(15, 25)}...`);
                DrawText(0.02, 0.1 + yOffset);
                yOffset += 0.03;
            }
        }
    });
}

function stopDebugThread(): void {
    debugThreadActive = false;
}

// Additional debug commands
RegisterCommand('invzoneinfo', () => {
    console.log(`[Inventory Debug] Active zones: ${activeInventoryZones.size}`);
    console.log(`[Inventory Debug] Player currently in zones:`, Array.from(currentZones));
    
    for (const [zoneId, zone] of activeInventoryZones) {
        const itemCount = Object.keys(zone.items).length;
        console.log(`[Inventory Debug] Zone ${zoneId.substring(15, 25)}... - Items: ${itemCount}, Position:`, zone.position);
    }
}, false);

RegisterCommand('invzoneclear', () => {
    console.log('[Inventory Debug] Clearing all zones...');
    
    // Clear client-side zones (this won't affect server state)
    for (const zoneId of activeInventoryZones.keys()) {
        destroyInventoryZone({ zoneId });
    }
    
    activeInventoryZones.clear();
    currentZones.clear();
    clearAllDebugMarkers();
    
    console.log('[Inventory Debug] All zones cleared from client');
}, false);

RegisterCommand('invzonerefresh', () => {
    console.log('[Inventory Debug] Requesting zone refresh from server...');
    emitNet('hm-inventory:server:requestAllZones');
}, false);

// Command to toggle debug mode
RegisterCommand('invzonedebug', () => {
    toggleInventoryZoneDebug();
}, false);

// ===== EVENT HANDLERS =====

// Server events for zone management
onNet('hm-inventory:createInventoryZone', createInventoryZone);
onNet('hm-inventory:updateInventoryZone', updateInventoryZone);  
onNet('hm-inventory:destroyInventoryZone', destroyInventoryZone);

// Initialize when resource starts
on('onClientResourceStart', (resourceName: string) => {
    if (GetCurrentResourceName() === resourceName) {
        console.log('[Inventory] Starting inventory zones client integration');
        
        // Wait a bit for polyzones to be ready
        setTimeout(() => {
            initializeInventoryZones();
        }, 1000);
    }
});

// Cleanup on resource stop
on('onClientResourceStop', (resourceName: string) => {
    if (GetCurrentResourceName() === resourceName) {
        console.log('[Inventory] Cleaning up inventory zones');
        
        // Clear all zones
        for (const zoneId of activeInventoryZones.keys()) {
            destroyInventoryZone({ zoneId });
        }
    }
});

// ===== TARGET SYSTEM EVENT HANDLERS =====

// Handle pickup action from target system
on('hm-inventory:pickupFromZone', (data: any) => {
    console.log('[Inventory] Pickup from zone action triggered:', data);
    
    // Extract zone ID from target data
    const targetZoneId = data.target?.zone?.id;
    if (targetZoneId && targetZoneId.startsWith('pickup_')) {
        const zoneId = targetZoneId.replace('pickup_', '');
        const zone = activeInventoryZones.get(zoneId);
        
        if (zone && Object.keys(zone.items).length > 0) {
            // Get first available item for pickup
            const firstItemId = Object.keys(zone.items)[0];
            const firstItem = zone.items[firstItemId];
            
            console.log(`[Inventory] Requesting pickup of item ${firstItemId} from zone ${zoneId}`);
            
            // Send pickup request to server
            emitNet('hm-inventory:server:pickupFromZone', {
                zoneId: zoneId,
                itemId: firstItemId,
                entityId: parseInt(firstItemId)
            });
        }
    }
});
