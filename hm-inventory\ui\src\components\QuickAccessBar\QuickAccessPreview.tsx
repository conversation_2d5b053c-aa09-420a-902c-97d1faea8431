import React from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import PreviewSlot from './PreviewSlot';

/**
 * Simple live preview that shows actual items from configured main inventory and action slots
 */
const QuickAccessPreview: React.FC = () => {
  const store = useInventoryStore();
  
  // Don't render if not visible
  if (!store.isQuickAccessVisible || !store.quickAccessConfig?.visible) {
    return null;
  }

  const config = store.quickAccessConfig;
  const isVertical = config.position === 'left' || config.position === 'right';
  
  // Position classes
  const positionClasses = {
    top: 'fixed top-8 left-1/2 -translate-x-1/2',
    left: 'fixed left-8 top-1/2 -translate-y-1/2',
    right: 'fixed right-8 top-1/2 -translate-y-1/2',
    bottom: 'fixed bottom-8 left-1/2 -translate-x-1/2'
  }[config.position || 'bottom'];  return (
    <div className={`${positionClasses} flex ${isVertical ? 'flex-col' : 'flex-row'} gap-6 z-30 pointer-events-none`}>
      {[...config.slots]
        .sort((a, b) => a.slot - b.slot)
        .map((slotConfig) => {
          if (!slotConfig.enabled) {
            return (
              <PreviewSlot
                key={slotConfig.slot}
                keyBinding={slotConfig.keyBinding}
              />
            );
          }

          // Get item directly from store
          let sourceSlot = null;
          if (slotConfig.source === 'main') {
            sourceSlot = store.gridItems.find(slot => slot.index === slotConfig.sourceSlot);
          } else if (slotConfig.source === 'action') {
            sourceSlot = store.actionSlots.find(slot => slot.index === slotConfig.sourceSlot);
          }

          return (
            <PreviewSlot
              key={slotConfig.slot}
              inventoryItem={sourceSlot?.item}
              quantity={sourceSlot?.quantity}
              slotType={sourceSlot?.slotType}
              keyBinding={slotConfig.keyBinding}
            />
          );
        })}
    </div>
  );
};

export default QuickAccessPreview;
