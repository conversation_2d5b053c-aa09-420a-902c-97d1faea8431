import { create } from 'zustand';
import { Match, Message, LoveLinkProfile } from '../types/loveLinkTypes';

interface LoveLinkState {
  // State
  profiles: LoveLinkProfile[];
  matches: Match[];
  messages: Message[];
  loading: boolean;
  currentProfile: LoveLinkProfile | null;
}

export const useLoveLinkStore = create<LoveLinkState>()(() => ({
  // State
  profiles: [],
  matches: [],
  messages: [],
  loading: false,
  currentProfile: null // Initialize with first profile
}));
