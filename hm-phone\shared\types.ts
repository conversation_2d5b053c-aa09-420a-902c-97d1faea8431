export interface Contact {
    id?: number; // AUTO_INCREMENT primary key
    identifier: string; // varchar(20) NOT NULL - Phone number of the player who owns this contact
    stateid: string; // varchar(20) NOT NULL - State ID of the player who owns this contact
    number: string; // varchar(20) NOT NULL - Phone number of the contact
    name: string; // varchar(50) NOT NULL - How the contact appears to the owner
    avatar: string | null; // varchar(255) DEFAULT NULL - Profile picture URL/path
    favorite: number; // tinyint(1) DEFAULT 0 - Flag for quick access to important contacts
    is_blocked?: number; // tinyint(1) DEFAULT 0 - Flag for privacy and filtering
    notes?: string | null; // varchar(255) DEFAULT NULL - Additional context for roleplay
    created_at: string; // timestamp NOT NULL DEFAULT current_timestamp()
    updated_at: string; // timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
}

export interface ContactShareRequest {
    contactId: number;
    contact: Contact;
    senderId: number;
    senderName: string;
}

export interface ContactShareStatus {
    success: boolean;
    contactId: number;
    targetPlayerId: number;
    status: 'sent' | 'accepted' | 'declined' | 'error';
}

export type CallStatus = 'missed' | 'answered' | 'outgoing';

export interface Call {
    id: number;
    owner_number: string; // Phone number of the player who owns this call record
    contact_number: string; // Phone number of the contact who was called/called the player
    timestamp: number; // Unix timestamp in seconds
    duration: number; // Call duration in seconds
    status: CallStatus;
    created_at?: string; // timestamp NOT NULL DEFAULT current_timestamp()
}

export interface BaseMessage {
    id: number;
    conversation_id: number;
    sender: string;
    message: string;
    type: 'text' | 'image' | 'location' | 'contact';
    metadata: string | null; // JSON string in database
    timestamp: string;
    is_deleted: number; // tinyint(1) in database (0 or 1)
}

export interface ConversationMember {
    display_name: string;
    unread_count: number;
    is_muted: boolean;
    is_pinned: boolean;
    is_admin: boolean;
    joined_at: string;
    left_at: string | null;
    avatar?: string; // Profile picture URL
}

export interface Conversation {
    id: number;
    name: string | null;
    type: 'direct' | 'group';
    participants?: string[]; // Array of phone numbers (for database storage)
    members: Record<string, ConversationMember>; // Phone number -> member data (for UI)
    player_settings?: Record<string, ConversationMember>; // Database field for player-specific settings
    created_at: string;
    updated_at: string;
    latest_message_id?: number;
    latest_message?: Message;
    messages?: Message[]; // Array of messages in the conversation
    avatar?: string; // Profile picture URL
}

// Message type definitions
export interface TextMessageType {
    id?: number;
    conversation_id?: number;
    type: 'text';
    content: string;
    metadata?: any;
}

export interface ImageMessageType {
    id?: number;
    conversation_id?: number;
    type: 'image';
    content: any; // URL to image or object with url property
    caption?: string;
    metadata?: any;
}

export interface LocationMessageType {
    id?: number;
    conversation_id?: number;
    type: 'location';
    content: {
        x: number;
        y: number;
        z: number;
    };
    caption?: string;
    metadata?: any;
}

export interface ContactMessageType {
    id?: number;
    conversation_id?: number;
    type: 'contact';
    content: {
        display_name: string;
        contact_number: string;
    };
    metadata?: any;
}

export type Message = BaseMessage & (TextMessageType | ImageMessageType | LocationMessageType | ContactMessageType);

/**
 * Unified Vehicle interface compatible with QBCore, QBox, and ESX frameworks
 */
export interface Vehicle {
    // Core identification (common across frameworks)
    id: number; // Database ID
    plate: string; // License plate (primary identifier in game)

    // Owner information
    owner?: string; // ESX owner / QBCore license
    identifier?: string; // Generic identifier (can be license or stateid)

    // Vehicle information
    model: string; // Vehicle model name/hash
    name: string; // Display name
    brand?: string; // Manufacturer
    hash?: string; // Model hash (QBCore)
    type?: string; // Vehicle type (automobile, motorcycle, boat, etc.)
    category?: string; // Vehicle category (sports, super, etc.)

    // Location information
    garage?: string; // Current garage
    location?: string; // Human-readable location
    state: string | number; // Vehicle state (in/out/impound)
    stored?: number; // ESX stored status
    parking?: string; // ESX parking location
    pound?: string; // ESX impound location

    // Condition
    fuel: number; // Fuel level
    engine: number; // Engine health
    body: number; // Body health
    drivingdistance?: number; // Odometer (QBCore)

    // Appearance
    mods?: Record<string, any>; // Vehicle modifications
    color?: string | number; // Primary color
    color2?: string | number; // Secondary color

    // Financial
    balance?: number; // Remaining balance (QBCore)
    paymentamount?: number; // Payment amount (QBCore)
    paymentsleft?: number; // Payments left (QBCore)
    financetime?: number; // Finance time (QBCore)

    // Additional
    fakeplate?: string; // Fake plate (QBCore)
    depotprice?: number; // Impound fee (QBCore)
    job?: string; // Job vehicle (ESX)
    status?: string; // UI status (Ready, Impounded, etc.)

    // UI specific
    imageUrl?: string; // Vehicle image
    licensePlate?: string; // Formatted license plate for display
}
