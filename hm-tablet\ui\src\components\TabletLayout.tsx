import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBatteryThreeQuarters, 
  faWifi, 
  faSignal,
  faHome,
  faSquare,
  faChevronUp
} from '@fortawesome/free-solid-svg-icons';
import { useTabletStore } from '../stores/tabletStore';
import HomeScreen from './HomeScreen';
import AppContainer from './AppContainer';

const TabletLayout: React.FC = () => {
  const { 
    currentApp, 
    isHomeScreen, 
    isNotificationPanelOpen,
    // config,
    goHome,
    toggleNotificationPanel
  } = useTabletStore();

  const currentTime = new Date().toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  });  const currentDate = new Date().toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric'   });  return (
    <div className="tablet-screen w-[80vw] h-[80vh] flex flex-col">
      {/* Status Bar */}
      <div className="status-bar">
        <div className="flex items-center gap-2 text-tablet-text-secondary">
          <span className="text-xs font-medium">{currentTime}</span>
          <span className="text-xs text-tablet-text-muted">•</span>
          <span className="text-xs">{currentDate}</span>
        </div>
        
        <div className="flex items-center gap-2 text-tablet-text-secondary">
          <FontAwesomeIcon icon={faSignal} className="text-xs" />
          <FontAwesomeIcon icon={faWifi} className="text-xs" />
          <FontAwesomeIcon icon={faBatteryThreeQuarters} className="text-sm" />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 relative overflow-hidden">
        <AnimatePresence mode="wait">
          {isHomeScreen ? (
            <motion.div
              key="home"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{ duration: 0.2 }}
              className="absolute inset-0"
            >
              <HomeScreen />
            </motion.div>
          ) : currentApp ? (
            <motion.div
              key={currentApp}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.25 }}
              className="absolute inset-0"
            >
              <AppContainer appId={currentApp} />
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      {/* Navigation Bar */}
      <div className="nav-bar">
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={goHome}
          className={`p-2 rounded-full transition-colors duration-200 ${
            isHomeScreen 
              ? 'text-tablet-accent-primary' 
              : 'text-tablet-text-secondary hover:text-tablet-text-primary'
          }`}
        >
          <FontAwesomeIcon icon={faHome} className="text-lg" />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={toggleNotificationPanel}
          className={`p-2 rounded-full transition-colors duration-200 ${
            isNotificationPanelOpen 
              ? 'text-tablet-accent-primary' 
              : 'text-tablet-text-secondary hover:text-tablet-text-primary'
          }`}
        >
          <FontAwesomeIcon icon={faChevronUp} className="text-lg" />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="p-2 rounded-full text-tablet-text-secondary hover:text-tablet-text-primary transition-colors duration-200"
        >
          <FontAwesomeIcon icon={faSquare} className="text-lg" />
        </motion.button>
      </div>
    </div>
  );
};

export default TabletLayout;
