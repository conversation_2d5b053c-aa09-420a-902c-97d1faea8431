{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "22.16.0"}, "scripts": {"dev": "vite --config vite.config.ts --force", "build": "tsc && vite build --config vite.config.ts", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@iconify/react": "^5.2.0", "framer-motion": "^11.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.1.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.15.30", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "chokidar": "^4.0.3", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "lightningcss": "^1.29.3", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}