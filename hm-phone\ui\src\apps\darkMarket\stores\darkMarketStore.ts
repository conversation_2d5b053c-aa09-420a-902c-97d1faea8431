import { create } from 'zustand';

interface DarkMarketListing {
  id: number;
  title: string;
  description: string;
  price: number;
  seller: string;
  rating: number;
  image?: string;
  category: string;
}

interface DarkMarketState {
  listings: DarkMarketListing[];
  searchListings: (query: string) => DarkMarketListing[];
  addListing: (listing: Omit<DarkMarketListing, 'id'>) => void;
  removeListing: (id: number) => void;
}

const mockListings: DarkMarketListing[] = [
  {
    id: 1,
    title: 'Neural Implant X1',
    description: 'Enhanced cognitive processor',
    price: 3500,
    seller: 'TechVault',
    rating: 4.8,
    category: 'tech'
  },
  {
    id: 2,
    title: 'Crypto Vault Pass',
    description: 'Secure digital storage',
    price: 1200,
    seller: 'CryptoSafe',
    rating: 4.9,
    category: 'security'
  },
  {
    id: 3,
    title: 'Data Stream Access',
    description: 'Premium bandwidth package',
    price: 800,
    seller: 'NetStream',
    rating: 4.7,
    category: 'services'
  },
  {
    id: 4,
    title: 'AI Assistant Core',
    description: 'Advanced AI module',
    price: 2500,
    seller: 'AILabs',
    rating: 4.6,
    category: 'tech'
  },
  {
    id: 5,
    title: 'Quantum Encryption',
    description: 'Military-grade security',
    price: 4000,
    seller: 'QuantumSec',
    rating: 5.0,
    category: 'security'
  },
  {
    id: 6,
    title: 'Network Bridge Pro',
    description: 'High-speed connection',
    price: 1500,
    seller: 'NetBridge',
    rating: 4.5,
    category: 'hardware'
  }
];

export const useDarkMarketStore = create<DarkMarketState>((set, get) => ({
  listings: mockListings,

  searchListings: (query: string) => {
    const state = get();
    const searchTerm = query.toLowerCase();
    return state.listings.filter(
      listing =>
        listing.title.toLowerCase().includes(searchTerm) ||
        listing.description.toLowerCase().includes(searchTerm) ||
        listing.category.toLowerCase().includes(searchTerm)
    );
  },

  addListing: listing =>
    set(state => ({
      listings: [{ ...listing, id: Date.now() }, ...state.listings]
    })),

  removeListing: id =>
    set(state => ({
      listings: state.listings.filter(listing => listing.id !== id)
    }))
}));
