import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { sendNuiMessage, NuiActions, onNuiEvent } from '../utils/nui';

// Item and Weapon interfaces (moved from player.tsx)
export interface ItemDefinition {
  name: string;
  label: string;
}

export interface WeaponDefinition {
  name: string;
  label: string;
}

// Context interface
interface AdminDataContextType {
  items: ItemDefinition[];
  weapons: WeaponDefinition[];
  isLoading: boolean;
  refreshData: () => void;
}

// Create the context with default values
const AdminDataContext = createContext<AdminDataContextType>({
  items: [],
  weapons: [],
  isLoading: false,
  refreshData: () => {}
});

// Provider component
export const AdminDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [items, setItems] = useState<ItemDefinition[]>([]);
  const [weapons, setWeapons] = useState<WeaponDefinition[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchData = () => {
    setIsLoading(true);
    
    // Fetch items
    sendNuiMessage(NuiActions.GET_ITEM_LIST, {});
    
    // Fetch weapons
    sendNuiMessage(NuiActions.GET_WEAPON_LIST, {});
  };

  useEffect(() => {
    // Set up event listeners for item and weapon data
    const itemListCleanup = onNuiEvent('itemList', (response: { data: ItemDefinition[] }) => {
      const items = response.data || [];
      setItems(items);
      setIsLoading(false);
    });
    
    const weaponListCleanup = onNuiEvent('weaponList', (response: { data: WeaponDefinition[] }) => {
      const weapons = response.data || [];
      setWeapons(weapons);
      setIsLoading(false);
    });

    // Initial data fetch
    fetchData();
    
    // Clean up event listeners on unmount
    return () => { 
      itemListCleanup(); 
      weaponListCleanup(); 
    };
  }, []);

  const refreshData = () => {
    fetchData();
  };

  // Provide the context value to children
  return (
    <AdminDataContext.Provider value={{ items, weapons, isLoading, refreshData }}>
      {children}
    </AdminDataContext.Provider>
  );
};

// Custom hook for using the admin data context
export const useAdminData = () => useContext(AdminDataContext);
