import { NotificationUtils } from '../shared/utilities';

/**
 * Weather and Time Control Manager
 * Handles all weather and time manipulation for admin system
 */
export class WeatherManager {
    private static instance: WeatherManager;
    private currentWeather: string = 'CLEAR';
    private isTimeFrozen: boolean = false;
    private timeScale: number = 1.0;

    // Available weather types in FiveM
    private readonly weatherTypes = [
        'CLEAR', 'EXTRASUNNY', 'CLOUDS', 'OVERCAST', 'RAIN', 'CLEARING',
        'THUNDER', 'SMOG', 'FOGGY', 'XMAS', 'SNOWLIGHT', 'BLIZZARD'
    ];

    private constructor() {
        this.init();
    }

    public static getInstance(): WeatherManager {
        if (!WeatherManager.instance) {
            WeatherManager.instance = new WeatherManager();
        }
        return WeatherManager.instance;
    }

    private init(): void {
        // Initialize weather manager
        console.log('[WeatherManager] Weather and Time Control System initialized');
    }

    /**
     * Change weather type with smooth transition
     */
    public async changeWeather(weatherType: string): Promise<boolean> {        
        try {
            const upperWeatherType = weatherType.toUpperCase();
            
            if (!this.weatherTypes.includes(upperWeatherType)) {
                NotificationUtils.show(`Invalid weather type: ${weatherType}`, 'error');
                return false;
            }

            // Set weather with transition
            SetOverrideWeather(upperWeatherType); // 15 second transition
            this.currentWeather = upperWeatherType;

            NotificationUtils.show(`Weather changed to ${upperWeatherType}`, 'success');
            console.log(`[WeatherManager] Weather changed to: ${upperWeatherType}`);
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error changing weather:', error);
            NotificationUtils.show('Failed to change weather', 'error');
            return false;
        }
    }

    /**
     * Set weather immediately without transition
     */
    public setWeatherInstant(weatherType: string): boolean {
        try {
            const upperWeatherType = weatherType.toUpperCase();
            
            if (!this.weatherTypes.includes(upperWeatherType)) {
                NotificationUtils.show(`Invalid weather type: ${weatherType}`, 'error');
                return false;
            }
            // Set weather instantly
            
            SetWeatherTypeNowPersist(upperWeatherType);
            
            this.currentWeather = upperWeatherType;
            NotificationUtils.show(`Weather instantly set to ${upperWeatherType}`, 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error setting weather instantly:', error);
            NotificationUtils.show('Failed to set weather', 'error');
            return false;
        }
    }

    /**
     * Set specific time (hours and minutes)
     */
    public setTime(hours: number, minutes: number = 0): boolean {
        try {
            if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
                NotificationUtils.show('Invalid time format (Hours: 0-23, Minutes: 0-59)', 'error');
                return false;
            }

            NetworkOverrideClockTime(hours, minutes, 0);
            NotificationUtils.show(`Time set to ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`, 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error setting time:', error);
            NotificationUtils.show('Failed to set time', 'error');
            return false;
        }
    }

    /**
     * Freeze or unfreeze time
     */
    public freezeTime(freeze: boolean): boolean {
        try {
            if (freeze) {
                PauseClock(true);
                this.isTimeFrozen = true;
                NotificationUtils.show('Time frozen', 'success');
            } else {
                PauseClock(false);
                this.isTimeFrozen = false;
                NotificationUtils.show('Time unfrozen', 'success');
            }
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error freezing/unfreezing time:', error);
            NotificationUtils.show('Failed to change time freeze state', 'error');
            return false;
        }
    }

    /**
     * Set time scale (how fast time passes)
     * Default is 1.0, higher values make time pass faster
     */
    public setTimeScale(scale: number): boolean {
        try {
            if (scale <= 0 || scale > 100) {
                NotificationUtils.show('Time scale must be between 0.1 and 100', 'error');
                return false;
            }

            // Note: FiveM doesn't have a direct time scale native
            // This would need to be implemented with a custom timer system
            this.timeScale = scale;
            NotificationUtils.show(`Time scale set to ${scale}x`, 'success');
            console.log(`[WeatherManager] Time scale set to: ${scale}`);
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error setting time scale:', error);
            NotificationUtils.show('Failed to set time scale', 'error');
            return false;
        }
    }

    /**
     * Get current weather information
     */
    public getCurrentWeather(): string {
        return this.currentWeather;
    }

    /**
     * Get available weather types
     */
    public getAvailableWeatherTypes(): string[] {
        return [...this.weatherTypes];
    }

    /**
     * Get current time freeze status
     */
    public isTimeFrozenStatus(): boolean {
        return this.isTimeFrozen;
    }

    /**
     * Get current time scale
     */
    public getCurrentTimeScale(): number {
        return this.timeScale;
    }

    /**
     * Get current game time
     */
    public getCurrentTime(): { hours: number; minutes: number; seconds: number } {
        const hours = GetClockHours();
        const minutes = GetClockMinutes();
        const seconds = GetClockSeconds();
        
        return { hours, minutes, seconds };
    }

    /**
     * Advanced weather effects
     */    public setWindSpeed(speed: number): boolean {
        try {
            if (speed < 0 || speed > 12) {
                NotificationUtils.show('Wind speed must be between 0 and 12', 'error');
                return false;
            }

            SetWind(speed);
            NotificationUtils.show(`Wind speed set to ${speed}`, 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error setting wind speed:', error);
            NotificationUtils.show('Failed to set wind speed', 'error');
            return false;
        }
    }

    /**
     * Set wind direction
     */    public setWindDirection(direction: number): boolean {
        try {
            if (direction < 0 || direction > 360) {
                NotificationUtils.show('Wind direction must be between 0 and 360 degrees', 'error');
                return false;
            }

            SetWindDirection(direction);
            NotificationUtils.show(`Wind direction set to ${direction} degrees`, 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error setting wind direction:', error);
            NotificationUtils.show('Failed to set wind direction', 'error');
            return false;
        }
    }

    /**
     * Clear all weather overrides and reset to normal
     */    public resetWeather(): boolean {
        try {
            ClearOverrideWeather();
            ClearWeatherTypePersist();
            this.currentWeather = 'CLEAR';
            NotificationUtils.show('Weather reset to normal', 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error resetting weather:', error);
            NotificationUtils.show('Failed to reset weather', 'error');
            return false;
        }
    }

    /**
     * Reset time to normal flow
     */    public resetTime(): boolean {
        try {
            PauseClock(false);
            this.isTimeFrozen = false;
            this.timeScale = 1.0;
            NotificationUtils.show('Time reset to normal', 'success');
            return true;

        } catch (error) {
            console.error('[WeatherManager] Error resetting time:', error);
            NotificationUtils.show('Failed to reset time', 'error');
            return false;
        }
    }
}

// Initialize the weather manager
export const weatherManager = WeatherManager.getInstance();
