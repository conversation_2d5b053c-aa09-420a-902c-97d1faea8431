// Entry point for Inventory UI
import React from 'react';
import { createRoot } from 'react-dom/client';
import InventoryScreen from './components/InventoryScreen';
import './index.css';
import { setupNuiListener } from './services/nui';

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Failed to find the root element');
}

// Register NUI listener for inventory show/hide
setupNuiListener();

const root = createRoot(rootElement);
root.render(
  <React.StrictMode>
    <InventoryScreen />
  </React.StrictMode>,
);
