import { HandlingProfile, BaseHandlingData } from '../shared/types';
import * as fs from 'fs/promises';
import * as path from 'path';

// Storage configuration
const STORAGE_DIR = GetResourcePath(GetCurrentResourceName());
const PROFILES_FILE = path.join(STORAGE_DIR, 'profiles.json');
const BACKUP_FILE = path.join(STORAGE_DIR, 'profiles.backup.json');
const MAX_BACKUPS = 3;

// File operation locks to prevent corruption
let saveInProgress = false;
let loadInProgress = false;

// In-memory storage for profiles (you can replace with database later)
const profiles: Map<string, HandlingProfile> = new Map();

// Performance optimization: Caching and optimized data structures
const profileCache = new Map<string, HandlingProfile[]>();
const PROFILE_CACHE_DURATION = 300000; // 5 minutes in milliseconds
const profileCacheTimestamps = new Map<string, number>();

// Performance optimization: Use Map for O(1) profile lookups instead of array operations
const profileMaps = new Map<string, Map<string, HandlingProfile>>();

// Performance: Automatic cache cleanup to prevent memory leaks
const CACHE_CLEANUP_INTERVAL = 600000; // 10 minutes in milliseconds
const MAX_CACHE_SIZE = 100; // Maximum number of cached vehicle models

// Generate unique ID for profiles
function generateId(): string {
  return `profile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Validate profile data
function validateProfileData(profileData: Omit<HandlingProfile, 'id' | 'createdAt'>): string | null {
  if (!profileData.name || typeof profileData.name !== 'string' || profileData.name.trim().length === 0) {
    return 'Profile name is required and must be a non-empty string';
  }
  
  if (profileData.name.length > 100) {
    return 'Profile name must be 100 characters or less';
  }
  
  if (!profileData.createdBy || typeof profileData.createdBy !== 'string' || profileData.createdBy.trim().length === 0) {
    return 'Created by field is required and must be a non-empty string';
  }
  
  if (!profileData.handlingData || typeof profileData.handlingData !== 'object') {
    return 'Handling data is required and must be an object';
  }
  
  if (profileData.description && typeof profileData.description !== 'string') {
    return 'Description must be a string';
  }
  
  if (profileData.vehicleModel && typeof profileData.vehicleModel !== 'string') {
    return 'Vehicle model must be a string';
  }
  
  if (typeof profileData.isPublic !== 'boolean') {
    return 'isPublic must be a boolean value';
  }
  
  if (profileData.category && !['drift', 'racing', 'offroad', 'comfort', 'sport', 'custom'].includes(profileData.category)) {
    return 'Invalid category specified';
  }
  
  if (profileData.tags && (!Array.isArray(profileData.tags) || !profileData.tags.every(tag => typeof tag === 'string'))) {
    return 'Tags must be an array of strings';
  }
  
  return null;
}

// Optimized profile loading with caching
function getCachedProfiles(vehicleModel: string): HandlingProfile[] | null {
  const cached = profileCache.get(vehicleModel);
  const timestamp = profileCacheTimestamps.get(vehicleModel);
  
  if (cached && timestamp && (Date.now() - timestamp) < PROFILE_CACHE_DURATION) {
    return cached;
  }
  
  return null;
}

// Cache profiles for faster subsequent access
function setCachedProfiles(vehicleModel: string, profiles: HandlingProfile[]): void {
  profileCache.set(vehicleModel, profiles);
  profileCacheTimestamps.set(vehicleModel, Date.now());
  
  // Create Map for O(1) lookups
  const profileMap = new Map<string, HandlingProfile>();
  for (const profile of profiles) {
    profileMap.set(profile.name, profile);
  }
  profileMaps.set(vehicleModel, profileMap);
}

// Optimized profile search using Map
function findProfile(vehicleModel: string, profileName: string): HandlingProfile | null {
  const profileMap = profileMaps.get(vehicleModel);
  return profileMap?.get(profileName) || null;
}

// Performance: Batch validation to reduce redundant checks
function validateHandlingDataBatch(data: Partial<BaseHandlingData>[]): boolean {
  const fields = Object.keys(data[0] || {}) as (keyof BaseHandlingData)[];
  
  for (const item of data) {
    for (const field of fields) {
      const value = item[field];
      // Basic validation - extend as needed
      if (typeof value === 'number' && (isNaN(value) || !isFinite(value))) {
        return false;
      }
    }
  }
  
  return true;
}

// Periodic cache cleanup
setInterval(() => {
  cleanupCaches();
}, CACHE_CLEANUP_INTERVAL);

function cleanupCaches(): void {
  const now = Date.now();
  
  // Clean up expired profile cache entries
  for (const [vehicleModel, timestamp] of profileCacheTimestamps.entries()) {
    if (now - timestamp > PROFILE_CACHE_DURATION) {
      profileCache.delete(vehicleModel);
      profileCacheTimestamps.delete(vehicleModel);
      profileMaps.delete(vehicleModel);
    }
  }
  
  // Limit cache size to prevent excessive memory usage
  if (profileCache.size > MAX_CACHE_SIZE) {
    const sortedEntries = Array.from(profileCacheTimestamps.entries())
      .sort(([, a], [, b]) => a - b); // Sort by timestamp, oldest first
    
    const entriesToRemove = sortedEntries.slice(0, profileCache.size - MAX_CACHE_SIZE);
    
    for (const [vehicleModel] of entriesToRemove) {
      profileCache.delete(vehicleModel);
      profileCacheTimestamps.delete(vehicleModel);
      profileMaps.delete(vehicleModel);
    }
  }
  
  console.log(`[hm-handling] Cache cleanup completed. Cached models: ${profileCache.size}`);
}

// Initialize storage system on server start
async function initializeStorage(): Promise<void> {
  console.log('[hm-handling] Initializing persistent storage...');
  
  try {
    // Ensure storage directory exists
    await fs.mkdir(path.dirname(PROFILES_FILE), { recursive: true });
    
    // Load existing profiles
    const loadSuccess = await loadProfilesFromFile();
    
    if (loadSuccess) {
      console.log('[hm-handling] Storage initialization completed successfully');
      
      // Start auto-save scheduling
      scheduleAutoSave();
    } else {
      console.error('[hm-handling] Storage initialization failed, using in-memory only');
    }
  } catch (error) {
    console.error('[hm-handling] Critical error during storage initialization:', error);
  }
}

// Initialize on resource start
setImmediate(async () => {
  await initializeStorage();
});

// Save profiles on resource stop
on('onResourceStop', async (resourceName: string) => {
  if (resourceName === GetCurrentResourceName()) {
    console.log('[hm-handling] Resource stopping, saving profiles...');
    await saveProfilesToFile();
    
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }
  }
});

// Event handlers
// Request profiles with caching optimization
onNet('hm-handling:requestProfiles', () => {
  try {
    const source = global.source;
    if (!source || typeof source !== 'number') {
      console.error('[hm-handling] Invalid source for profile request');
      return;
    }
    
    // Use cached profiles if available
    const allProfiles = Array.from(profiles.values());
    emitNet('hm-handling:profilesLoaded', source, allProfiles);
    console.log(`[hm-handling] Sent ${allProfiles.length} profiles to player ${source}`);
  } catch (error) {
    console.error('[hm-handling] Error handling profile request:', error);
    emitNet('hm-handling:error', source, 'Failed to load profiles');
  }
});

// Save profile with cache invalidation
onNet('hm-handling:saveProfile', (profileData: Omit<HandlingProfile, 'id' | 'createdAt'>) => {
  try {
    const source = global.source;
    if (!source || typeof source !== 'number') {
      console.error('[hm-handling] Invalid source for profile save');
      return;
    }
    
    if (!profileData) {
      emitNet('hm-handling:error', source, 'Profile data is required');
      return;
    }
    
    const validationError = validateProfileData(profileData);
    if (validationError !== null) {
      emitNet('hm-handling:error', source, validationError);
      return;
    }
    
    const profile: HandlingProfile = {
      ...profileData,
      id: generateId(),
      createdAt: new Date()
    };    profiles.set(profile.id, profile);
    
    // Invalidate cache for this vehicle model
    if (profileData.vehicleModel) {
      profileCache.delete(profileData.vehicleModel);
      profileCacheTimestamps.delete(profileData.vehicleModel);
      profileMaps.delete(profileData.vehicleModel);
    }
    
    // Trigger auto-save after successful profile creation
    scheduleAutoSave();
    
    // Send back to the requesting client
    emitNet('hm-handling:profileSaved', source, profile);
    
    console.log(`[hm-handling] Profile "${profile.name}" saved by player ${source}`);
  } catch (error) {
    console.error('[hm-handling] Error saving profile:', error);
    emitNet('hm-handling:error', source, 'Failed to save profile');
  }
});

onNet('hm-handling:deleteProfile', (profileId: string) => {
  try {
    const source = global.source;
    if (!source || typeof source !== 'number') {
      console.error('[hm-handling] Invalid source for profile deletion');
      return;
    }
    
    if (!profileId || typeof profileId !== 'string' || profileId.trim().length === 0) {
      emitNet('hm-handling:error', source, 'Profile ID is required');
      return;
    }
      if (!profiles.has(profileId)) {
      emitNet('hm-handling:error', source, 'Profile not found');
      return;
    }
      const profile = profiles.get(profileId);
    if (!profile) {
      emitNet('hm-handling:error', source, 'Profile not found');
      return;
    }
      profiles.delete(profileId);
    
    // Trigger auto-save after successful profile deletion
    scheduleAutoSave();
    
    // Notify the requesting client
    emitNet('hm-handling:profileDeleted', source, profileId);
    
    console.log(`[hm-handling] Profile "${profile.name}" deleted by player ${source}`);  } catch (error) {
    console.error('[hm-handling] Error deleting profile:', error);
    emitNet('hm-handling:error', source, 'Failed to delete profile');
  }
});

// Add profile search functionality
onNet('hm-handling:searchProfiles', (filters: { category?: string; vehicleModel?: string; searchTerm?: string }) => {
  try {
    const source = global.source;
    if (!source || typeof source !== 'number') {
      console.error('[hm-handling] Invalid source for profile search');
      return;
    }
    
    let filteredProfiles = Array.from(profiles.values());
    
    if (filters) {
      if (filters.category && typeof filters.category === 'string') {
        filteredProfiles = filteredProfiles.filter(profile => profile.category === filters.category);
      }
      
      if (filters.vehicleModel && typeof filters.vehicleModel === 'string') {
        filteredProfiles = filteredProfiles.filter(profile => profile.vehicleModel === filters.vehicleModel);
      }
      
      if (filters.searchTerm && typeof filters.searchTerm === 'string') {
        const searchTerm = filters.searchTerm.toLowerCase();
        filteredProfiles = filteredProfiles.filter(profile => 
          profile.name.toLowerCase().includes(searchTerm) ||
          (profile.description && profile.description.toLowerCase().includes(searchTerm)) ||
          (profile.tags && profile.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
        );
      }
    }
    
    emitNet('hm-handling:profileSearchResults', source, filteredProfiles);
    console.log(`[hm-handling] Sent ${filteredProfiles.length} filtered profiles to player ${source}`);
  } catch (error) {
    console.error('[hm-handling] Error searching profiles:', error);
    emitNet('hm-handling:error', source, 'Failed to search profiles');
  }
});

// Utility function to get profile by ID (for internal use)
function getProfileById(profileId: string): HandlingProfile | undefined {
  if (!profileId || typeof profileId !== 'string') {
    return undefined;
  }
  return profiles.get(profileId);
}

// Utility function to get profiles by creator (for internal use)
function getProfilesByCreator(createdBy: string): HandlingProfile[] {
  if (!createdBy || typeof createdBy !== 'string') {
    return [];
  }
  return Array.from(profiles.values()).filter(profile => profile.createdBy === createdBy);
}

// Persistent storage functions
async function saveProfilesToFile(): Promise<boolean> {
  if (saveInProgress) {
    console.warn('[hm-handling] Save already in progress, skipping');
    return false;
  }

  saveInProgress = true;
  
  try {
    const profilesArray = Array.from(profiles.values());
    const jsonData = JSON.stringify(profilesArray, null, 2);
    
    // Atomic write: write to temp file first, then rename
    const tempFile = `${PROFILES_FILE}.tmp`;
    await fs.writeFile(tempFile, jsonData, 'utf8');
    
    // Create backup before replacing
    try {
      if (await fileExists(PROFILES_FILE)) {
        await fs.copyFile(PROFILES_FILE, BACKUP_FILE);
      }
    } catch (backupError) {
      console.warn('[hm-handling] Failed to create backup:', backupError);
    }
    
    // Atomic rename
    await fs.rename(tempFile, PROFILES_FILE);
    
    console.log(`[hm-handling] Saved ${profilesArray.length} profiles to file`);
    return true;
  } catch (error) {
    console.error('[hm-handling] Failed to save profiles:', error);
    return false;
  } finally {
    saveInProgress = false;
  }
}

async function loadProfilesFromFile(): Promise<boolean> {
  if (loadInProgress) {
    console.warn('[hm-handling] Load already in progress, skipping');
    return false;
  }

  loadInProgress = true;
  
  try {
    if (!(await fileExists(PROFILES_FILE))) {
      console.log('[hm-handling] No profiles file found, starting with empty profiles');
      return true;
    }
    
    const jsonData = await fs.readFile(PROFILES_FILE, 'utf8');
    const profilesArray: HandlingProfile[] = JSON.parse(jsonData);
    
    // Validate loaded data
    if (!Array.isArray(profilesArray)) {
      throw new Error('Invalid profiles data format');
    }
    
    // Clear existing profiles and load from file
    profiles.clear();
    let loadedCount = 0;
    
    for (const profile of profilesArray) {
      // Validate each profile
      if (isValidProfile(profile)) {
        profiles.set(profile.id, profile);
        loadedCount++;
      } else {
        console.warn(`[hm-handling] Skipping invalid profile: ${(profile as any)?.id || 'unknown'}`);
      }
    }
    
    console.log(`[hm-handling] Loaded ${loadedCount} profiles from file`);
    return true;
  } catch (error) {
    console.error('[hm-handling] Failed to load profiles:', error);
    
    // Try to load from backup
    if (await tryLoadFromBackup()) {
      console.log('[hm-handling] Successfully recovered from backup');
      return true;
    }
    
    return false;
  } finally {
    loadInProgress = false;
  }
}

async function tryLoadFromBackup(): Promise<boolean> {
  try {
    if (!(await fileExists(BACKUP_FILE))) {
      return false;
    }
    
    const jsonData = await fs.readFile(BACKUP_FILE, 'utf8');
    const profilesArray: HandlingProfile[] = JSON.parse(jsonData);
    
    if (!Array.isArray(profilesArray)) {
      return false;
    }
    
    profiles.clear();
    let loadedCount = 0;
    
    for (const profile of profilesArray) {
      if (isValidProfile(profile)) {
        profiles.set(profile.id, profile);
        loadedCount++;
      }
    }
    
    console.log(`[hm-handling] Recovered ${loadedCount} profiles from backup`);
    return true;
  } catch (error) {
    console.error('[hm-handling] Failed to load from backup:', error);
    return false;
  }
}

async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

function isValidProfile(profile: any): profile is HandlingProfile {
  return (
    profile &&
    typeof profile === 'object' &&
    typeof profile.id === 'string' &&
    typeof profile.name === 'string' &&
    typeof profile.createdBy === 'string' &&
    typeof profile.isPublic === 'boolean' &&
    profile.handlingData &&
    typeof profile.handlingData === 'object' &&
    (profile.createdAt instanceof Date || typeof profile.createdAt === 'string')
  );
}

// Auto-save functionality
let autoSaveTimer: NodeJS.Timeout | null = null;
const AUTO_SAVE_INTERVAL = 300000; // 5 minutes

function scheduleAutoSave(): void {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer);
  }
  
  autoSaveTimer = setTimeout(async () => {
    await saveProfilesToFile();
    scheduleAutoSave(); // Reschedule
  }, AUTO_SAVE_INTERVAL);
}

// Admin command to manually save profiles
RegisterCommand('hm-handling:save', async (source: number, args: string[], rawCommand: string) => {
  // Check if source is server console (0) or has admin permissions
  if (source !== 0 && !IsPlayerAceAllowed(source.toString(), 'hm-handling.admin')) {
    if (source > 0) {
      emitNet('chat:addMessage', source, {
        color: [255, 0, 0],
        multiline: true,
        args: ['[hm-handling]', 'You do not have permission to use this command.']
      });
    }
    return;
  }
  
  const success = await saveProfilesToFile();
  const message = success 
    ? `Profiles saved successfully (${profiles.size} profiles)`
    : 'Failed to save profiles - check server logs';
  
  if (source === 0) {
    console.log(`[hm-handling] ${message}`);
  } else {
    emitNet('chat:addMessage', source, {
      color: success ? [0, 255, 0] : [255, 0, 0],
      multiline: true,
      args: ['[hm-handling]', message]
    });
  }
}, false);

// Admin command to reload profiles from file
RegisterCommand('hm-handling:reload', async (source: number, args: string[], rawCommand: string) => {
  // Check if source is server console (0) or has admin permissions
  if (source !== 0 && !IsPlayerAceAllowed(source.toString(), 'hm-handling.admin')) {
    if (source > 0) {
      emitNet('chat:addMessage', source, {
        color: [255, 0, 0],
        multiline: true,
        args: ['[hm-handling]', 'You do not have permission to use this command.']
      });
    }
    return;
  }
  
  const success = await loadProfilesFromFile();
  const message = success 
    ? `Profiles reloaded successfully (${profiles.size} profiles loaded)`
    : 'Failed to reload profiles - check server logs';
  
  if (source === 0) {
    console.log(`[hm-handling] ${message}`);
  } else {
    emitNet('chat:addMessage', source, {
      color: success ? [0, 255, 0] : [255, 0, 0],
      multiline: true,
      args: ['[hm-handling]', message]
    });
  }
}, false);

// Global exports for FiveM resource interoperability
global.exports('getProfiles', () => {
  return Array.from(profiles.values());
});

global.exports('getProfileById', (profileId: string): HandlingProfile | undefined => {
  if (!profileId || typeof profileId !== 'string') {
    return undefined;
  }
  return profiles.get(profileId);
});

global.exports('getProfilesByCreator', (createdBy: string): HandlingProfile[] => {
  if (!createdBy || typeof createdBy !== 'string') {
    return [];
  }
  return Array.from(profiles.values()).filter(profile => profile.createdBy === createdBy);
});

global.exports('getProfilesByVehicleModel', (vehicleModel: string): HandlingProfile[] => {
  if (!vehicleModel || typeof vehicleModel !== 'string') {
    return [];
  }
  return Array.from(profiles.values()).filter(profile => profile.vehicleModel === vehicleModel);
});

global.exports('saveProfileToStorage', async (): Promise<boolean> => {
  return await saveProfilesToFile();
});

global.exports('loadProfileFromStorage', async (): Promise<boolean> => {
  return await loadProfilesFromFile();
});
