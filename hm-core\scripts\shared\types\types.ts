export interface PlayerData {
    id: number; // Unique player identifier
    license: string; // License identifier (shorthand)
    name: string; // Player name
    discord?: string; // Discord identifier
    steam?: string; // Steam identifier
    created_at: Date; // Account creation date
    last_seen: Date; // Last seen date
    play_time: number; // Total playtime in seconds
    is_online: boolean; // Online status
}

export interface CharacterData {
    id: number; // Unique character ID
    identifier: string; // Character identifier (e.g., license or unique ID)
    stateid: string; // State identifier (e.g., for state management)
    first_name: string; // First name
    last_name: string; // Last name
    birthdate: Date; // Birthdate
    gender: 'male' | 'female' | 'other'; // Gender
    state: 'alive' | 'dead' | 'unconscious'; // Character state
    money: {
        cash: number; // Cash amount
        bank: number; // Bank amount
        crypto?: number; // Crypto amount
    };
    position: Vector4; // Character position in the world
    appearance: [any]; // Character appearance data (e.g., clothing, features)
    tattoos: [any]; // Character tattoos data
    metadata: [any]; // Additional metadata (e.g., custom data)
    created_at: Date; // Character creation date
    updated_at: Date; // Last update date
}

export interface Vector4 {
    x: number; // X coordinate
    y: number; // Y coordinate
    z: number; // Z coordinate
    heading?: number; // W coordinate (usually represents the heading or rotation)
}

export enum PlayerEvents {
    PLAYER_LOADED = 'player:loaded', // Triggered when a player is loaded
    CHARACTER_LOADED = 'character:loaded', // Triggered when a character is loaded
    CHARACTER_LIST = 'character:list', // Triggered to request the character list

    REQUEST_CHARACTER_LIST = 'player:requestCharacters', // Request character list from the server
    SELECT_CHARACTER = 'player:selectCharacter', // Select a character
    CREATE_CHARACTER = 'player:createCharacter', // Create a new character
    DELETE_CHARACTER = 'player:deleteCharacter', // Delete a character

    PLAYER_UPDATE = 'player:update', // Update player data
    CHARACTER_UPDATE = 'character:update', // Update character data
}
