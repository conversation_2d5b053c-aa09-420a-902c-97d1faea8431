{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext", // Changed to ESNext for Vite
    "moduleResolution": "node",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["scripts/shared/types/*"],
      "@shared": ["scripts/shared/types/index.ts"],
      "@client/*": ["scripts/client/*"],
      "@server/*": ["scripts/server/*"]
    },
    "outDir": "./build",
    "rootDirs": ["scripts/client", "scripts/server", "scripts/shared"],
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "lib": ["ES2020"],
    "types": [
      "node",
      "@citizenfx/client", // Added client types
      "@citizenfx/server" // Added server types
    ]
  },
  "include": [
    "scripts/client/**/*",
    "scripts/server/**/*",
    "scripts/shared/**/*"
  ],
  "exclude": [
    "node_modules",
    "ui"
  ]
}
