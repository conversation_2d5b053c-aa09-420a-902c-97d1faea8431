import React, { useState } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import NearbyPlayersPanel from '../../../common/components/NearbyPlayersPanel';

const UserProfileComponent: React.FC = () => {
  const { userProfile, isLoading } = usePhoneStore();
  const [isEditingName, setIsEditingName] = useState(false);
  const [newName, setNewName] = useState(userProfile.name);
  const [showAirdrop, setShowAirdrop] = useState(false);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewName(e.target.value);
  };

  const confirmNameChange = () => {
    setIsEditingName(false);
  };

  return (
    <div className="p-2">
      {/* Profile Card - Ultra Compact Design */}
      <div className="relative overflow-hidden rounded-xl bg-[#0a0f1a] border-white/10 backdrop-blur-sm border">
        {/* Share Button Column */}
        <div className="absolute top-3 right-3">
          <button
            onClick={() => setShowAirdrop(true)}
            className="w-9 h-9 rounded-xl bg-gradient-to-r from-blue-500/20 to-blue-500/20 hover:from-blue-500/30 hover:to-blue-500/30 border border-blue-500/20
                   transition-colors flex items-center justify-center"
          >
            <i className="fas fa-qrcode text-blue-400 text-sm"></i>
          </button>
        </div>
        {/* Main Content */}
        <div className="p-3">
          <div className="flex items-center">
            {' '}
            {/* Changed to items-center for better vertical alignment */}
            {/* Profile Image with Hover Camera */}
            <div className="relative mr-3 group">
              <div className="w-[3.25rem] h-[3.25rem] rounded-lg overflow-hidden border border-pink-500/30">
                {userProfile.imageUrl ? (
                  <img
                    src={userProfile.imageUrl}
                    className="w-full h-full object-cover"
                    alt="Profile"
                  />
                ) : (
                  <div className="w-full h-full bg-white/[0.07] flex items-center justify-center">
                    <i className="fas fa-user text-white/40 text-lg"></i>
                  </div>
                )}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <button
                    onClick={() => {}}
                    className="w-8 h-8 rounded-full bg-pink-500/80 hover:bg-pink-500/90
                           shadow-sm flex items-center justify-center transition-colors"
                  >
                    <i className="fas fa-camera text-white text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
            {/* User Info - Compact */}
            <div className="flex-1">
              {/* Name with Edit */}
              {isEditingName ? (
                <div className="flex items-center gap-1 mb-1">
                  <input
                    type="text"
                    value={newName}
                    onChange={handleNameChange}
                    className="flex-1 bg-white/[0.07] text-white focus:ring-pink-500/30 px-2 py-1 rounded-lg text-sm
                           focus:outline-none focus:ring-1"
                    autoFocus
                  />
                  <button
                    onClick={confirmNameChange}
                    className="w-6 h-6 rounded-lg bg-pink-500/20 hover:bg-pink-500/30
                           transition-colors flex items-center justify-center"
                  >
                    <i className="fas fa-check text-pink-400 text-xs"></i>
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-1.5 mb-0.5">
                  <h2 className="text-base font-medium text-white">{userProfile.name}</h2>
                  <button
                    onClick={() => setIsEditingName(true)}
                    className="w-5 h-5 rounded-md bg-white/[0.07] hover:bg-white/[0.12]
                           transition-colors flex items-center justify-center"
                  >
                    <i className="fas fa-pencil-alt text-white/70 text-[10px]"></i>
                  </button>
                </div>
              )}

              {/* ID */}
              <div className="flex items-center mb-1.5">
                <span className="text-xs text-white/50 font-medium">#{userProfile.stateid}</span>
                {isLoading && <span className="ml-2 text-xs text-pink-400">Loading...</span>}
              </div>

              {/* Inline Contact Info */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1">
                  <i className="fas fa-phone text-pink-400 text-xs"></i>
                  <span className="text-xs text-white">{userProfile.phoneNumber}</span>
                </div>

                {userProfile.job && (
                  <div className="flex items-center gap-1">
                    <i className="fas fa-briefcase text-pink-400 text-xs"></i>
                    <span className="text-xs text-white truncate max-w-[100px]">
                      {userProfile.job.label}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Nearby Players Panel */}
      <NearbyPlayersPanel
        show={showAirdrop}
        onClose={() => setShowAirdrop(false)}
        title="Share My Profile"
        onPlayerSelect={playerId => {
          // Create a contact object from the user profile
          const userContact = {
            id: 0, // Special ID for user's own contact
            name: userProfile.name,
            phone: userProfile.phoneNumber,
            email: '',
            address: '',
            favorite: false,
            photo: userProfile.imageUrl || ''
          };

          // Share the contact
          console.log(`Sharing profile with player ${playerId}`, userContact);
          // TODO: Implement actual sharing logic here
          // Example: shareContact(userContact, playerId);
        }}
        contentAbove={
          <div className="bg-white/5 rounded-lg p-3 mb-2 flex items-center">
            {userProfile.imageUrl ? (
              <div
                className="w-10 h-10 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${userProfile.imageUrl})` }}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-lg font-medium">
                  {userProfile.name ? userProfile.name[0].toUpperCase() : '#'}
                </span>
              </div>
            )}
            <div className="ml-3">
              <div className="text-white font-medium">{userProfile.name}</div>
              <div className="text-white/60 text-sm">{userProfile.phoneNumber}</div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default UserProfileComponent;
