/**
 * Phone Animation Service
 *
 * Handles all phone-related animations and prop management.
 * This service is responsible for:
 * - Playing phone open/close animations
 * - Creating and managing the phone prop
 * - Handling call animations
 * - Loading animation dictionaries
 */

// Import globals and services
import '../globals';
import { PhoneStateEnum } from '../types';
import gameService from './game';

// Animation constants
const PHONE_ANIM_DICT       = 'cellphone@';
const PHONE_ANIM_OPEN       = 'cellphone_pick_up'; // Changed from cellphone_text_in
const PHONE_ANIM_TEXT_IN    = 'cellphone_text_in'; // Added for clarity if used separately
const PHONE_ANIM_CLOSE      = 'cellphone_text_out';
const PHONE_ANIM_CALL       = 'cellphone_call_listen_base';

// Additional phone animation clips
const PHONE_ANIM_IDLE        = 'cellphone_text_read_base';
const PHONE_ANIM_ANSWER      = 'cellphone_call_to_text';
const PHONE_ANIM_SWIPE       = 'cellphone_swipe_screen';
const PHONE_ANIM_KEYPRESS    = 'cellphone_short_press';
const PHONE_ANIM_HANGUP      = 'cellphone_put_down'; // Changed from cellphone_putdown
const PHONE_ANIM_CANCEL      = 'cellphone_cancel_out'; // Assuming this is still valid if putting down is different

const PHONE_PROP_NAME        = 'prop_player_phone_01'; // Changed from prop_npc_phone_02
const PHONE_PROP_BONE        = 28422;

// Animation flags based on spec
const ANIM_FLAG_NONE = 0; // For one-shot, full-body
const ANIM_FLAG_LOOP_UPPER_HOLD = 49; // 1 (loop) + 16 (stay last frame/upper) + 32 (upper-body only/secondary)

// Original PHONE_ANIM_FLAGS = 50; // Kept for reference, will replace usage

// Helper function for delays
const delay = (ms: number) => new Promise(res => setTimeout(res, ms));

// Animation state
let isAnimationPlaying = false;
let animationTimeout: NodeJS.Timeout | null = null;
let animationInterval: NodeJS.Timeout | null = null;

/**
 * Create and attach phone prop to player
 * @returns Promise that resolves when prop is created or rejects on error
 */
export async function createPhoneProp(): Promise<void> {
    try {
        // If prop already exists, do nothing
        if (global.phoneEntity !== 0) {
            return;
        }

        const playerPed = PlayerPedId();
        const phoneModel = GetHashKey(PHONE_PROP_NAME);

        // Load model using Game Service
        await gameService.loadModel(phoneModel);

        // Create phone prop
        const coords = GetEntityCoords(playerPed, false);
        global.phoneEntity = CreateObject(phoneModel, coords[0], coords[1], coords[2], true, true, false);

        // Attach phone prop to player
        AttachEntityToEntity(
            global.phoneEntity,
            playerPed,
            GetPedBoneIndex(playerPed, PHONE_PROP_BONE),
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            true,
            true,
            false,
            true,
            1,
            true
        );

        // Set as no longer needed
        SetModelAsNoLongerNeeded(phoneModel);
    } catch (error) {
        console.error('[Phone] Error creating phone prop:', error);
        throw error;
    }
}

/**
 * Remove phone prop
 */
export function removePhoneProp(): void {
    if (global.phoneEntity !== 0) {
        DeleteObject(global.phoneEntity);
        global.phoneEntity = 0;
    }
}

/**
 * Play phone open animation (cellphone_pick_up)
 */
async function playPhonePickupAnimation(): Promise<void> {
    const playerPed = PlayerPedId();
    const dict = IsPedInAnyVehicle(playerPed, true) ? 'anim@cellphone@in_car@ps' : PHONE_ANIM_DICT;
    await gameService.loadAnimDict(dict);
    SetCurrentPedWeapon(playerPed, 0xa2719263, true); // Unarmed
    TaskPlayAnim(playerPed, dict, PHONE_ANIM_OPEN, 8.0, -8.0, -1, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play phone text in animation (cellphone_text_in)
 */
export async function playPhoneTextIn(): Promise<void> {
    const ped = PlayerPedId();
    await gameService.loadAnimDict(PHONE_ANIM_DICT);
    TaskPlayAnim(ped, PHONE_ANIM_DICT, PHONE_ANIM_TEXT_IN, 8.0, -8.0, 200, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play phone close animation (cellphone_text_out)
 */
async function playPhoneTextOutAnimation(): Promise<void> {
    const playerPed = PlayerPedId();
    const dict = IsPedInAnyVehicle(playerPed, true) ? 'anim@cellphone@in_car@ps' : PHONE_ANIM_DICT;
    await gameService.loadAnimDict(dict);
    TaskPlayAnim(playerPed, dict, PHONE_ANIM_CLOSE, 8.0, -8.0, 200, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play idle/read animation (cellphone_text_read_base)
 */
export async function playPhoneIdle(): Promise<void> {
    const ped = PlayerPedId();
    await gameService.loadAnimDict(PHONE_ANIM_DICT);
    TaskPlayAnim(ped, PHONE_ANIM_DICT, PHONE_ANIM_IDLE, 4.0, -4.0, -1, ANIM_FLAG_LOOP_UPPER_HOLD, 0, false, false, false);
}

/**
 * Play answer animation (cellphone_call_to_text)
 */
export async function playPhoneAnswer(): Promise<void> {
    const ped = PlayerPedId();
    await gameService.loadAnimDict(PHONE_ANIM_DICT);
    TaskPlayAnim(ped, PHONE_ANIM_DICT, PHONE_ANIM_ANSWER, 8.0, -8.0, 300, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play swipe navigation animation (cellphone_swipe_screen)
 */
export function swipePhone(): void {
    const ped = PlayerPedId();
    // No need to load anim dict here if it's always loaded when phone is open
    TaskPlayAnim(ped, PHONE_ANIM_DICT, PHONE_ANIM_SWIPE, 4.0, -4.0, 300, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play short keypress navigation animation (cellphone_short_press)
 */
export function pressPhoneKey(): void {
    const ped = PlayerPedId();
    // No need to load anim dict here if it's always loaded when phone is open
    TaskPlayAnim(ped, PHONE_ANIM_DICT, PHONE_ANIM_KEYPRESS, 4.0, -4.0, 100, ANIM_FLAG_NONE, 0, false, false, false);
}

/**
 * Play hangup/put down animation (cellphone_put_down or cellphone_cancel_out)
 * @param useCancelAnim If true, plays 'cellphone_cancel_out' instead of 'cellphone_put_down'.
 */
export async function playPhoneHangupAnimation(useCancelAnim = false): Promise<void> {
    const ped = PlayerPedId();
    const clip = useCancelAnim ? PHONE_ANIM_CANCEL : PHONE_ANIM_HANGUP;
    await gameService.loadAnimDict(PHONE_ANIM_DICT);
    TaskPlayAnim(ped, PHONE_ANIM_DICT, clip, 8.0, -8.0, -1, ANIM_FLAG_NONE, 0, false, false, false);
    // Prop removal should be handled by the caller after this animation seems complete,
    // as its duration is -1 (plays until stopped or another anim overrides).
}

/**
 * Handles the overall process of opening the phone.
 * Plays pickup animation, then text_in, then idle.
 */
export async function openPhone(): Promise<void> {
    if (isAnimationPlaying) return;
    isAnimationPlaying = true;
    try {
        await createPhoneProp();
        await playPhonePickupAnimation();
        // Wait for pickup to be somewhat visible before text_in
        await delay(500); // Adjust as needed
        await playPhoneTextIn();
        // Wait for text_in to complete before idle
        await delay(200); // Duration of text_in
        await playPhoneIdle();
    } catch (error) {
        console.error('[Phone] Error opening phone:', error);
        removePhoneProp(); // Clean up prop if opening failed
    } finally {
        isAnimationPlaying = false;
    }
}

/**
 * Handles the overall process of closing the phone.
 * Plays text_out animation, then removes prop.
 */
export async function closePhone(force = false): Promise<void> {
    if (force) {
        removePhoneProp();
        if (animationTimeout) clearTimeout(animationTimeout);
        isAnimationPlaying = false;
        return;
    }
    if (isAnimationPlaying) return;
    isAnimationPlaying = true;
    try {
        await playPhoneTextOutAnimation();
        // Wait for text_out to complete before removing prop
        await delay(200); // Duration of text_out
        removePhoneProp();
    } catch (error) {
        console.error('[Phone] Error closing phone:', error);
        removePhoneProp(); // Ensure prop is cleaned up on error
    } finally {
        isAnimationPlaying = false;
    }
}

/**
 * Play phone call animation (cellphone_call_listen_base for incoming, then cellphone_call_to_text on answer)
 * or (cellphone_put_down/cellphone_cancel_out for ending call)
 * @param start True to start/answer a call, false to end it.
 * @param isAnswering True if this is to play the answer animation after ringing.
 */
export async function playPhoneCallAnimation(start: boolean, isAnswering: boolean = false): Promise<void> {
    if (isAnimationPlaying && !isAnswering) return; // Allow answering to interrupt
    isAnimationPlaying = true;
    try {
        const playerPed = PlayerPedId();
        await gameService.loadAnimDict(PHONE_ANIM_DICT);

        if (global.phoneEntity === 0 && start) {
            await createPhoneProp(); // Ensure prop exists for incoming/outgoing calls
        }

        global.activeCall = start;

        if (start) {
            if (isAnswering) {
                await playPhoneAnswer(); // cellphone_call_to_text
                // After answering, transition to an idle call state, perhaps back to text_read_base or a call_talk_base if available
                await delay(300); // Duration of answer anim
                await playPhoneIdle(); // Revert to idle for now
            } else {
                // Incoming call ringing
                TaskPlayAnim(playerPed, PHONE_ANIM_DICT, PHONE_ANIM_CALL, 8.0, -8.0, -1, ANIM_FLAG_LOOP_UPPER_HOLD, 0, false, false, false);
            }
        } else {
            // Ending a call
            await playPhoneHangupAnimation(false); // Or determine if cancel is needed
            await delay(1000); // Arbitrary wait for hangup anim as duration is -1
            removePhoneProp();
            // Potentially play a final close animation if phone was open
            // await playPhoneTextOutAnimation();
            // await delay(200);
            // removePhoneProp();
        }
    } catch (error) {
        console.error('[Phone] Error in call animation:', error);
        if (!start) removePhoneProp(); // Ensure prop cleanup if ending call failed
    } finally {
        isAnimationPlaying = false;
    }
}

// Hook into menu navigation inputs (example calls, integrate in controlsService)
// Called on swipe input
export function onPhoneSwipe(): void {
    swipePhone();
}

// Called on soft-key press input
export function onPhoneKeyPress(): void {
    pressPhoneKey();
}

/**
 * Check if animation is currently playing
 * @returns True if animation is playing
 */
export function isAnimPlaying(): boolean {
    return isAnimationPlaying;
}

/**
 * End phone call animation and clean up
 */
export function endPhoneCall(): void {
    if (global.activeCall) {
        global.activeCall = false;
        playPhoneCallAnimation(false).catch(error => {
            console.error('[Phone] Error ending phone call:', error);
        });
    }
}

/**
 * Clean up animation resources
 */
export function cleanupAnimationResources(): void {
    // Remove phone prop
    removePhoneProp();

    // Clear any pending timeouts
    if (animationTimeout) {
        clearTimeout(animationTimeout);
        animationTimeout = null;
    }

    // Clear any pending intervals
    if (animationInterval) {
        clearInterval(animationInterval);
        animationInterval = null;
    }

    // Reset animation state
    isAnimationPlaying = false;
    global.activeCall = false;
}

// Export animation service
export const animationService = {
    openPhone,
    closePhone,
    createPhoneProp,
    removePhoneProp,
    playPhoneCallAnimation,
    isAnimPlaying,
    endPhoneCall,
    cleanupAnimationResources,
    playPhoneIdle,
    playPhoneAnswer,
    swipePhone,
    pressPhoneKey,
    playPhoneHangupAnimation, // Renamed from hangUpPhone
    playPhoneTextIn
};

export default animationService;
