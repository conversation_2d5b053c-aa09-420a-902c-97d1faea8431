{"projects": [{"id": "5488aa5d-91e5-48bc-a782-309b923fea52", "name": "HM-Target Development", "description": "Development of hm-target - a professional-grade FiveM targeting system with better performance than ox_target, featuring modern UI with Tailwind CSS and TypeScript implementation", "createdAt": "2025-06-06T20:34:30.186Z", "updatedAt": "2025-06-06T20:34:30.186Z"}], "tasks": [{"id": "54522251-44d0-4568-a0c1-ee4d8c71561d", "name": "Create hm-target folder structure", "details": "Set up the complete folder structure for hm-target with TypeScript configuration, UI setup, and build system matching hm-phone patterns. This includes creating directories for client, server, shared, ui, and all necessary configuration files.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:12.822Z", "updatedAt": "2025-06-06T20:53:07.624Z", "dependsOn": [], "priority": 10, "complexity": 3, "status": "done", "tags": ["setup", "configuration", "folder-structure"], "estimatedHours": 2, "actualHours": 3}, {"id": "bd3ae3a7-f01a-44f6-b1b1-77a67754cd68", "name": "Implement core targeting system", "details": "Implement the core TypeScript targeting system including the Target class, TargetAPI exports, raycast engine, proximity detection, and entity management. This is the heart of the targeting system that will handle all the game logic.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:19.322Z", "updatedAt": "2025-06-06T20:53:11.381Z", "dependsOn": ["54522251-44d0-4568-a0c1-ee4d8c71561d"], "priority": 9, "complexity": 6, "status": "done", "tags": ["core", "typescript", "targeting", "raycast"], "estimatedHours": 8, "actualHours": 4}, {"id": "4baafa10-3c87-492c-9a09-0b78e1fb50d9", "name": "Build NUI interface with Tailwind CSS", "details": "Create a modern, professional UI interface using React, TypeScript, and Tailwind CSS. Design targeting overlays, option menus, progress bars, and animations that match the quality and style of NoPixel/Prodigy servers while being unique.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:26.307Z", "updatedAt": "2025-06-06T20:53:16.006Z", "dependsOn": ["54522251-44d0-4568-a0c1-ee4d8c71561d"], "priority": 8, "complexity": 5, "status": "done", "tags": ["ui", "tailwind", "react", "nui"], "estimatedHours": 6, "actualHours": 3}, {"id": "3c2ca22a-bad1-4afe-a2dc-32b1f01302b6", "name": "Configure build system and fxmanifest", "details": "Create the fxmanifest.lua file with proper resource definition, set up webpack/vite build configuration for TypeScript compilation and UI building, and create npm scripts for development and production builds.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:32.412Z", "updatedAt": "2025-06-06T20:53:20.541Z", "dependsOn": ["bd3ae3a7-f01a-44f6-b1b1-77a67754cd68", "4baafa10-3c87-492c-9a09-0b78e1fb50d9"], "priority": 8, "complexity": 4, "status": "done", "tags": ["build", "webpack", "fxmanifest", "configuration"], "estimatedHours": 3, "actualHours": 2}, {"id": "31c1baa7-df13-4a27-89ac-e450d01e3867", "name": "Implement export API functions", "details": "Create simple, FiveM-friendly export functions that other resources can use to register targeting options. Implement exports like addBox(), addCircle(), addEntity(), addModel(), removeTarget(), etc. with clean API design.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:39.024Z", "updatedAt": "2025-06-06T20:35:39.024Z", "dependsOn": ["bd3ae3a7-f01a-44f6-b1b1-77a67754cd68"], "priority": 7, "complexity": 3, "status": "pending", "tags": ["api", "exports", "fivem", "integration"], "estimatedHours": 4}, {"id": "e899af73-4fa6-4092-8a45-2cd28e1701da", "name": "Create documentation and examples", "details": "Create comprehensive documentation with usage examples, API reference, and sample integrations. Include README.md with setup instructions, examples for common use cases, and code snippets for other resource developers.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:44.954Z", "updatedAt": "2025-06-06T20:35:44.954Z", "dependsOn": ["31c1baa7-df13-4a27-89ac-e450d01e3867", "3c2ca22a-bad1-4afe-a2dc-32b1f01302b6"], "priority": 6, "complexity": 2, "status": "pending", "tags": ["documentation", "examples", "readme"], "estimatedHours": 3}, {"id": "d664ca33-3227-426c-a55a-6d13f20ef384", "name": "Performance optimization and testing", "details": "Optimize performance with efficient raycast algorithms, spatial indexing for zones, smart caching mechanisms, and memory management. Test the system under load and ensure smooth operation with multiple active targets.", "projectId": "5488aa5d-91e5-48bc-a782-309b923fea52", "completed": false, "createdAt": "2025-06-06T20:35:50.727Z", "updatedAt": "2025-06-06T20:35:50.727Z", "dependsOn": ["e899af73-4fa6-4092-8a45-2cd28e1701da"], "priority": 5, "complexity": 4, "status": "pending", "tags": ["performance", "optimization", "testing", "benchmarking"], "estimatedHours": 5}], "subtasks": []}