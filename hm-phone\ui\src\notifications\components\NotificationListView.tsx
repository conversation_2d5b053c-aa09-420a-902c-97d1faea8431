import React, { useState } from 'react';
import { NotificationData } from '../types/notificationTypes';

interface NotificationListViewProps {
  notifications: NotificationData[];
  formatTimeAgo: (timestamp: number) => string;
}

const NotificationListView: React.FC<NotificationListViewProps> = ({
  notifications,
  formatTimeAgo
}) => {
  const [showAll, setShowAll] = useState(false);

  // Sort notifications by timestamp (newest first)
  const sortedNotifications = [...notifications].sort((a, b) => b.timestamp - a.timestamp);

  // Show all notifications or just the first 3
  const notificationsToShow = showAll ? sortedNotifications : sortedNotifications.slice(0, 3);

  return (
    <div className="space-y-3">
      {/* Badge row - show first 3 as badges */}
      {notifications.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {notifications.slice(0, Math.min(3, notifications.length)).map(notification => (
            <div
              key={`badge-${notification.id}`}
              className="bg-white/10 text-white text-xs px-2 py-1 rounded-full flex items-center"
            >
              <span className="truncate max-w-[120px]">{notification.message}</span>
            </div>
          ))}
        </div>
      )}

      {/* Notification list */}
      <div className="space-y-1 pl-2 border-l-2 border-white/10 max-h-40 overflow-y-auto pr-1">
        {notificationsToShow.map(notification => (
          <div key={notification.id} className="flex justify-between items-start py-1">
            <div className="text-white/80 text-xs flex-1 pr-2">{notification.message}</div>
            <div className="text-white/40 text-[10px] flex-shrink-0">
              {formatTimeAgo(notification.timestamp)}
            </div>
          </div>
        ))}

        {/* Show More/Less button */}
        {notifications.length > 3 && (
          <div className="flex justify-center mt-1">
            <button
              onClick={e => {
                e.stopPropagation();
                setShowAll(!showAll);
              }}
              className="text-blue-400 text-xs hover:text-blue-300 flex items-center"
            >
              {showAll ? (
                <>
                  <i className="fas fa-chevron-up mr-1 text-[10px]"></i>
                  Show Less
                </>
              ) : (
                <>
                  <i className="fas fa-chevron-down mr-1 text-[10px]"></i>
                  Show More ({notifications.length - 3} more)
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationListView;
