import { Target } from "./Target";
import { TargetOption, TargetZone, Vector3 } from "../shared/types";

export class TargetAPI {
  private target: Target;

  constructor() {
    this.target = Target.getInstance();
  }

  /**
   * Add a spherical target zone
   */
  public addSphereZone(
    id: string,
    coords: Vector3,
    radius: number,
    options: TargetOption[]
  ): void {
    const zone: Omit<TargetZone, "id"> = {
      type: "circle",
      position: coords,
      radius,
      options,
    };

    this.target.addZone(id, zone);
  }

  /**
   * Add a box target zone
   */
  public addBoxZone(
    id: string,
    coords: Vector3,
    length: number,
    width: number,
    height: number,
    heading: number,
    options: TargetOption[]
  ): void {
    const zone: Omit<TargetZone, "id"> = {
      type: "box",
      position: coords,
      size: { x: length, y: width, z: height },
      rotation: { x: 0, y: 0, z: heading },
      options,
    };

    this.target.addZone(id, zone);
  }

  /**
   * Add a polygon target zone
   */
  public addPolyZone(
    id: string,
    points: Vector3[],
    minZ: number,
    maxZ: number,
    options: TargetOption[]
  ): void {
    // Calculate center point
    const centerX = points.reduce((sum, p) => sum + p.x, 0) / points.length;
    const centerY = points.reduce((sum, p) => sum + p.y, 0) / points.length;
    const centerZ = (minZ + maxZ) / 2;

    const zone: Omit<TargetZone, "id"> = {
      type: "polygon",
      position: { x: centerX, y: centerY, z: centerZ },
      points,
      minZ,
      maxZ,
      options,
    };

    this.target.addZone(id, zone);
  }

  /**
   * Remove a target zone
   */
  public removeZone(id: string): void {
    this.target.removeZone(id);
  }

  /**
   * Add target to a specific entity
   */
  public addTargetEntity(entity: number, options: TargetOption[]): void {
    this.target.addEntity(entity, options);
  }

  /**
   * Remove target from a specific entity
   */
  public removeTargetEntity(entity: number): void {
    this.target.removeEntity(entity);
  }

  /**
   * Add target to entities by model hash
   */
  public addTargetModel(
    models: number | number[],
    options: TargetOption[]
  ): void {
    if (Array.isArray(models)) {
      models.forEach((modelHash) => {
        this.target.addTargetModel(modelHash, options);
      });
    } else {
      this.target.addTargetModel(models, options);
    }
  }

  /**
   * Remove target from entities by model hash
   */
  public removeTargetModel(_models: string | string[]): void {
    console.warn("[hm-target] removeTargetModel not fully implemented yet");
  }

  /**
   * Add global target (works on all entities of this type)
   */
  public addGlobalType(
    type: "player" | "vehicle" | "ped" | "object",
    options: TargetOption[]
  ): void {
    this.target.addGlobalType(type, options);
  }

  /**
   * Remove global target
   */
  public removeGlobalType(type: "player" | "vehicle" | "ped" | "object"): void {
    this.target.removeGlobalType(type);
  }

  /**
   * Enable targeting system
   */
  public enableTargeting(): void {
    this.target.show();
  }

  /**
   * Disable targeting system
   */
  public disableTargeting(): void {
    this.target.hide();
  }

  /**
   * Check if targeting is currently active
   */
  public isTargeting(): boolean {
    return this.target.isActive();
  }

  /**
   * Get current target state (for debugging)
   */
  public getTargetState() {
    return this.target.getState();
  }

  /**
   * Create a temporary target zone that auto-removes after time
   */
  public addTempZone(
    id: string,
    coords: Vector3,
    radius: number,
    options: TargetOption[],
    duration = 30000
  ): void {
    this.addSphereZone(id, coords, radius, options);

    setTimeout(() => {
      this.removeZone(id);
    }, duration);
  }

  /**
   * Add a vehicle target with specific options
   */
  public addVehicleTarget(vehicle: number, options: TargetOption[]): void {
    if (!DoesEntityExist(vehicle) || !IsEntityAVehicle(vehicle)) {
      console.error("[hm-target] Invalid vehicle entity");
      return;
    }

    this.addTargetEntity(vehicle, options);
  }

  /**
   * Add a ped target with specific options
   */
  public addPedTarget(ped: number, options: TargetOption[]): void {
    if (!DoesEntityExist(ped) || !IsPedAPlayer(ped)) {
      console.error("[hm-target] Invalid ped entity");
      return;
    }

    this.addTargetEntity(ped, options);
  }

  /**
   * Bulk add multiple zones
   */
  public addMultipleZones(
    zones: Array<{
      id: string;
      type: "sphere" | "box" | "poly";
      coords: Vector3;
      options: TargetOption[];
      [key: string]: any;
    }>
  ): void {
    zones.forEach((zone) => {
      switch (zone.type) {
        case "sphere":
          this.addSphereZone(
            zone.id,
            zone.coords,
            zone.radius || 2.0,
            zone.options
          );
          break;
        case "box":
          this.addBoxZone(
            zone.id,
            zone.coords,
            zone.length || 2.0,
            zone.width || 2.0,
            zone.height || 2.0,
            zone.heading || 0.0,
            zone.options
          );
          break;
        case "poly":
          this.addPolyZone(
            zone.id,
            zone.points || [],
            zone.minZ || zone.coords.z - 1,
            zone.maxZ || zone.coords.z + 1,
            zone.options
          );
          break;
      }
    });
  }

  /**
   * Get all active zones (for debugging)
   */
  public getActiveZones(): string[] {
    // This would require access to the Target class's private zones map
    console.warn("[hm-target] getActiveZones not fully implemented yet");
    return [];
  }

  /**
   * Check if a specific zone exists
   */
  public hasZone(_id: string): boolean {
    // This would require access to the Target class's private zones map
    console.warn("[hm-target] hasZone not fully implemented yet");
    return false;
  }
}
