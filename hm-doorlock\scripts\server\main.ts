/**
 * HM Door System - Server Implementation
 * Server-side handles state management, permissions, and networking
 * Client-side handles actual door registration with FiveM door system
 */

import { DOOR_DEFINITIONS } from "../shared/config";
import { DoorState } from "../shared/types/door.types";

// ==================== NATIVE DECLARATIONS ====================
declare function GetCurrentResourceName(): string;
declare function onNet(
  eventName: string,
  callback: (...args: any[]) => void
): void;
declare function emitNet(
  eventName: string,
  target: number | string,
  ...args: any[]
): void;
declare function on(
  eventName: string,
  callback: (...args: any[]) => void
): void;

// ==================== STATE MANAGEMENT ====================

const doorStates = new Map<string, DoorState>();
const activeLocks = new Set<string>();

// ==================== DOOR STATE MANAGEMENT ====================

function setDoorState(doorId: string, state: DoorState): boolean {
  const door = DOOR_DEFINITIONS.find((d) => d.id === doorId);
  if (!door) {
    console.error(`[HM-DoorLock] Door not found: ${doorId}`);
    return false;
  }

  doorStates.set(doorId, state);

  if (state === DoorState.LOCKED) {
    activeLocks.add(doorId);
  } else {
    activeLocks.delete(doorId);
  }

  emitNet("hm-doorlock:set-door-state", -1, doorId, state);
  emitNet("hm-doorlock:door-state-changed", -1, doorId, state);

  console.log(`[HM-DoorLock] Set door ${doorId} to state: ${state}`);
  return true;
}

function lockDoor(doorId: string): boolean {
  return setDoorState(doorId, DoorState.LOCKED);
}

function unlockDoor(doorId: string): boolean {
  return setDoorState(doorId, DoorState.UNLOCKED);
}

function toggleDoorLock(doorId: string): boolean {
  const currentState = doorStates.get(doorId);
  if (currentState === DoorState.LOCKED) {
    return unlockDoor(doorId);
  } else {
    return lockDoor(doorId);
  }
}

function getDoorState(doorId: string): DoorState | undefined {
  return doorStates.get(doorId);
}

// ==================== PERMISSION SYSTEM ====================

function checkDoorPermissions(source: number, doorId: string): boolean {
  const door = DOOR_DEFINITIONS.find((d) => d.id === doorId);
  if (!door || !door.accessControl) {
    return false;
  }

  // TODO: Implement framework-specific permission checks
  console.log(
    `[HM-DoorLock] Permission check for player ${source} on door ${doorId}: PLACEHOLDER - ALLOWING ACCESS`
  );
  return true;
}

// ==================== NETWORK EVENTS ====================

onNet("hm-doorlock:toggle-door", (doorId: string) => {
  const source = (global as any).source;

  console.log(
    `[HM-DoorLock] Toggle request from player ${source} for door ${doorId}`
  );

  if (!checkDoorPermissions(source, doorId)) {
    emitNet("hm-doorlock:access-denied", source, doorId);
    console.log(
      `[HM-DoorLock] Access denied for player ${source} on door ${doorId}`
    );
    return;
  }

  const success = toggleDoorLock(doorId);

  if (success) {
    const newState = getDoorState(doorId);
    emitNet("hm-doorlock:toggle-success", source, doorId, newState);
  } else {
    emitNet("hm-doorlock:toggle-failed", source, doorId);
  }
});

onNet("hm-doorlock:request-door-state", (doorId: string) => {
  const source = (global as any).source;
  const state = getDoorState(doorId);
  emitNet("hm-doorlock:door-state-response", source, doorId, state);
});

onNet("hm-doorlock:request-all-states", () => {
  const source = (global as any).source;
  const allStates: Record<string, DoorState> = {};

  for (const [doorId, state] of doorStates.entries()) {
    allStates[doorId] = state;
  }

  emitNet("hm-doorlock:all-states-response", source, allStates);
});

onNet("hm-doorlock:door-registered", (doorId: string) => {
  const source = (global as any).source;
  console.log(`[HM-DoorLock] Client ${source} registered door: ${doorId}`);

  if (!doorStates.has(doorId)) {
    const door = DOOR_DEFINITIONS.find((d) => d.id === doorId);
    if (door) {
      doorStates.set(doorId, door.defaultState);
    }
  }
});

// ==================== INITIALIZATION ====================

on("onResourceStart", (resourceName: string) => {
  if (GetCurrentResourceName() !== resourceName) return;

  console.log(`[HM-DoorLock] Starting door system server...`);

  for (const door of DOOR_DEFINITIONS) {
    doorStates.set(door.id, door.defaultState);
    console.log(
      `[HM-DoorLock] Initialized door ${door.id} with state: ${door.defaultState}`
    );
  }

  console.log(
    `[HM-DoorLock] Server initialized with ${DOOR_DEFINITIONS.length} doors!`
  );
});

on("playerJoining", (source: string) => {
  const playerId = parseInt(source);

  setTimeout(() => {
    const allStates: Record<string, DoorState> = {};

    for (const [doorId, state] of doorStates.entries()) {
      allStates[doorId] = state;
    }

    emitNet("hm-doorlock:all-states-response", playerId, allStates);
    console.log(`[HM-DoorLock] Sent door states to player ${playerId}`);
  }, 2000);
});

on("onResourceStop", (resourceName: string) => {
  if (GetCurrentResourceName() !== resourceName) return;

  console.log(`[HM-DoorLock] Shutting down door system server...`);
  console.log(`[HM-DoorLock] Server shutdown complete.`);
});

// ==================== EXPORTS ====================

export {
  lockDoor,
  unlockDoor,
  toggleDoorLock,
  getDoorState,
  checkDoorPermissions,
};

(global as any).exports("lockDoor", lockDoor);
(global as any).exports("unlockDoor", unlockDoor);
(global as any).exports("toggleDoorLock", toggleDoorLock);
(global as any).exports("getDoorState", getDoorState);
