import React from 'react';
import { LocationMessageType } from '../../../../../../shared/types';

// Extended LocationMessageType with UI-specific properties
interface UILocationMessage extends Omit<LocationMessageType, 'content'> {
  message?: string;
  timestamp?: string;
  content?: {
    x: number;
    y: number;
    z: number;
  };
  metadata?: {
    location: {
      name: string;
      address: string;
      coordinates?: {
        lat: number;
        lng: number;
      };
    };
    timestamp?: number;
  };
}

interface LocationMessageProps {
  message: UILocationMessage;
}

export const LocationMessage: React.FC<LocationMessageProps> = ({ message }) => {
  const handleSetGPS = (coords: { x: number; y: number }) => {
    fetch('https://hm-phone/setGPS', {
      method: 'POST',
      body: JSON.stringify(coords)
    });
  };

  // Extract location data from metadata
  const location = message.metadata?.location || {
    name: 'Shared Location',
    address: 'Los Santos',
    coordinates: { lat: 0, lng: 0 }
  };

  // Create content object if it doesn't exist
  const content = message.content || {
    x: location.coordinates?.lng || 0,
    y: location.coordinates?.lat || 0,
    z: 0
  };

  return (
    <div className="w-fit min-w-0 max-w-full">
      <div className="p-1.5 flex items-center gap-3">
        <div className="relative flex-shrink-0">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-rose-500/20 to-red-600/20 flex items-center justify-center">
            <i className="fas fa-location-dot text-rose-400"></i>
          </div>
        </div>

        <div className="flex-1 min-w-0 border-r border-white/10 pr-3">
          <div className="text-sm font-medium text-white/90 truncate">
            {location.name || 'Shared Location'}
          </div>
          <div className="text-xs font-medium text-white/60 truncate">
            {location.address || 'Los Santos'}
          </div>
        </div>

        <button
          onClick={() =>
            handleSetGPS(content)
          }
          className="w-8 h-8 rounded-lg bg-white/5 flex items-center justify-center hover:bg-white/10 transition-colors active:bg-white/20 cursor-pointer"
        >
          <i className="fas fa-compass text-white/60 hover:text-white transition-colors"></i>
        </button>
      </div>
    </div>
  );
};
