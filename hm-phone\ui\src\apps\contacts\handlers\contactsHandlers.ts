/**
 * Contacts app event handlers
 *
 * These handlers receive events from the FiveM client and update the contacts store.
 *
 * Flow:
 * 1. Client sends contacts data using SendNUIMessage({app: 'contacts', type: 'contacts', data: [...]})
 * 2. clientEventReceiver dispatches the event to the registered handler
 * 3. <PERSON><PERSON> validates the data and updates the contacts store
 * 4. UI components re-render with the new data
 */
import { Contact } from '@shared/types';
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useContactsStore } from '../stores/contactsStore';
import { ContactShareRequest, ContactShareStatus } from '../types/contactsTypes';

// Register handler for contacts data events
registerEventHandler('contacts', 'setContacts', data => {
  if (Array.isArray(data)) {
    useContactsStore.getState().handlers.onSetContacts(data as Contact[]);
  } else {
    console.error('[Contacts] Received invalid contacts data (not an array)');
  }
});

// Register handler for contact added event
registerEventHandler('contacts', 'contactAdded', data => {
  if (typeof data === 'object' && data !== null) {
    // Cast to Contact type to access properties safely
    const contact = data as Contact;
    // Log only the name of the contact
    console.log(`[Contacts] Contact added: ${contact.name || 'Unknown'}`);
    useContactsStore.getState().handlers.onContactAdded(contact);
  } else {
    console.error('[Contacts] Received invalid contact added data');
  }
});

// Register handler for contact updated event
registerEventHandler('contacts', 'contactUpdated', data => {
  if (typeof data === 'object' && data !== null) {
    // Cast to Contact type to access properties safely
    const contact = data as Contact;
    // Log only the name of the contact
    console.log(`[Contacts] Contact updated: ${contact.name || 'Unknown'}`);
    useContactsStore.getState().handlers.onContactUpdated(contact);
  } else {
    console.error('[Contacts] Received invalid contact updated data');
  }
});

// Register handler for contact deleted event
registerEventHandler('contacts', 'contactDeleted', data => {
  if (typeof data === 'number' || typeof data === 'string') {
    console.log(`[Contacts] Contact deleted: ID ${data}`);
    useContactsStore.getState().handlers.onContactDeleted(Number(data));
  } else {
    console.error('[Contacts] Received invalid contact deleted data');
  }
});

// Register handler for contact share request (airdrop)
registerEventHandler('contacts', 'contactShareRequest', data => {
  if (
    typeof data === 'object' &&
    data !== null &&
    'contactId' in data &&
    'contact' in data &&
    'senderId' in data &&
    'senderName' in data
  ) {
    console.log(`[Contacts] Contact share request from: ${data.senderName}`);
    useContactsStore.getState().handlers.onContactShareRequest(data as ContactShareRequest);
  } else {
    console.error('[Contacts] Received invalid contact share request data');
  }
});

// Register handler for contact share sent confirmation
registerEventHandler('contacts', 'contactShareSent', data => {
  if (
    typeof data === 'object' &&
    data !== null &&
    'success' in data &&
    'contactId' in data &&
    'targetPlayerId' in data &&
    'status' in data
  ) {
    console.log(`[Contacts] Contact share sent: ${data.success ? 'Success' : 'Failed'}`);
    useContactsStore.getState().handlers.onContactShareSent(data as ContactShareStatus);
  } else {
    console.error('[Contacts] Received invalid contact share sent data');
  }
});

// Register handler for contact share status update
registerEventHandler('contacts', 'contactShareUpdated', data => {
  if (
    typeof data === 'object' &&
    data !== null &&
    'success' in data &&
    'contactId' in data &&
    'targetPlayerId' in data &&
    'status' in data
  ) {
    console.log(`[Contacts] Contact share status: ${data.status}`);
    useContactsStore.getState().handlers.onContactShareUpdated(data as ContactShareStatus);
  } else {
    console.error('[Contacts] Received invalid contact share update data');
  }
});

// Register handler for contact share accepted
registerEventHandler('contacts', 'contactShareAccepted', data => {
  if (typeof data === 'number' || typeof data === 'string') {
    console.log(`[Contacts] Contact share accepted: ID ${data}`);
    useContactsStore.getState().handlers.onContactShareAccepted(Number(data));
  } else {
    console.error('[Contacts] Received invalid contact share accepted data');
  }
});
