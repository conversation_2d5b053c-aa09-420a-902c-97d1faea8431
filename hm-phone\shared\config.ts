export const generalConfig = {
    resourceName: 'hm-phone',
    debug: true,
    framework: {
        forceFramework: 'hm-core',
        debug: true
    },
    database: {
        autoCreateTables: false // Disabled - use init_database.sql at resource root instead
    }
};

export const uiConfig = {
    defaultSettings: {
        theme: 'dark',
        wallpaper: '1',
        ringtone: 'default',
        notificationSound: 'default',
        fontSize: 'medium',
        language: 'en',
        doNotDisturb: false,
        airplaneMode: false,
        showNotificationsOnLockScreen: true,
        vibrate: true,
        volume: 80,
        brightness: 80,
        autoLock: 30,
        lockScreenType: 'swipe'
    },
    animations: {
        enabled: true,
        peek: {
            percentage: 18
        }
    }
};

export const webhookConfig = {
    discord: {
        photoWebhookUrl:
            'https://discord.com/api/webhooks/1364246741498724536/cEqSvcjgVkgexQSLxlija9-9C-H_H9Qlwfu7RiFwvNYx6jUBXr2HVjtKwbGLvqywYvJq',
        username: 'FiveM Phone',
        avatarUrl: '',
        includeMetadata: true
    },
    general: {
        defaultProvider: 'discord',
        enabled: true
    }
};

export const servicesConfig = {
    availableServices: [
        {
            id: 'taxi',
            name: 'Taxi',
            icon: 'taxi',
            description: 'Request a taxi to your location',
            color: 'yellow',
            job: 'taxi',
            enabled: true
        },
        {
            id: 'mechanic',
            name: 'Mechanic',
            icon: 'wrench',
            description: 'Request a mechanic for vehicle repairs',
            color: 'blue',
            job: 'mechanic',
            enabled: true
        },
        {
            id: 'police',
            name: 'Police',
            icon: 'shield-alt',
            description: 'Contact law enforcement',
            color: 'blue',
            job: 'police',
            enabled: true
        },
        {
            id: 'ambulance',
            name: 'Ambulance',
            icon: 'ambulance',
            description: 'Request medical assistance',
            color: 'red',
            job: 'ambulance',
            enabled: true
        },
        {
            id: 'delivery',
            name: 'Food Delivery',
            icon: 'utensils',
            description: 'Order food delivery',
            color: 'green',
            job: 'delivery',
            enabled: true
        }
    ],
    requests: {
        enabled: true,
        showLocation: true,
        notifyAllJobMembers: true
    }
};

export const cameraConfig = {
    defaultSettings: {
        flashEnabled: false,
        currentMode: 'photo'
    },
    video: {
        maxRecordingDuration: 15 // Maximum recording duration in seconds
    }
};

export default {
    general: generalConfig,
    ui: uiConfig,
    webhook: webhookConfig,
    services: servicesConfig,
    camera: cameraConfig
};
