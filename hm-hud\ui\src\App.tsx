import { useEffect, useState } from 'react';
import { useHudStore } from './store/hudStore';
import StatusBars from './components/StatusBars';
import Speedometer from './components/Speedometer';
import TestSpeedometer from './components/TestSpeedometer';
import VehicleIndicators from './components/VehicleIndicators';
import StreetNames from './components/StreetNames';
import { HudData } from '../../scripts/shared/types';

// Mock data for development - Simplified for optimized system
const mockData: HudData = {
  health: 75,
  armor: 60,        // Set armor for testing segments
  oxygen: 75,       // Below 100 to show oxygen bar
  hunger: 85,       // Added hunger
  thirst: 70,       // Added thirst
  stress: 15,       // Added stress
  vehicle: {
    isInVehicle: true, // Show vehicle HUD by default in browser
    speed: 124,
    fuel: 65,
    engineHealth: 850,
    bodyHealth: 900,
    gear: 4,
    seatbelt: true,
    nitrous: 75       // Added nitrous for future use
  },
  location: {
    streetName: "Vinewood Boulevard",
    crossingName: "Elgin Avenue",
    zoneName: "Vinewood"
  }
};

// Define the message event interface
interface NuiMessageEvent {
  data: {
    type: 'setVisible';
    data: { visible: boolean };
  } | {
    type: 'updateHUD';
    data: Partial<HudData>;
  };
}

const App = () => {
  const { visible, setVisible, updateHudData } = useHudStore();
  const [isDevelopment, setIsDevelopment] = useState(false);

  useEffect(() => {
    // Check if we're in a browser environment (development)
    const isBrowser = window.self === window.top;
    setIsDevelopment(isBrowser);

    // If in development, use mock data and show UI
    if (isBrowser) {
      updateHudData(mockData);
      setVisible(true);
      console.log('Development mode: Using mock data and showing UI');
    }

    // Listen for NUI messages from the client script
    const messageHandler = (event: NuiMessageEvent) => {
      const { type, data } = event.data;

      switch (type) {
        case 'setVisible':
          setVisible(data.visible);
          break;
        case 'updateHUD':
          updateHudData(data);
          break;
        default:
          break;
      }
    };

    window.addEventListener('message', messageHandler);

    return () => {
      window.removeEventListener('message', messageHandler);
    };
  }, [setVisible, updateHudData]);

  if (!visible) return null;

  return (
    <div className="fixed inset-0 pointer-events-none font-sans antialiased">
      {/* Background image from public folder - only visible in development */}
      {isDevelopment && (
        <div
          className="absolute inset-0 z-0 bg-cover bg-center w-full h-full"
          style={{
            backgroundImage: 'url(/bg.jpg)',
            width: '100vw',
            height: '100vh',
            filter: 'brightness(0.8)' // Slightly darken the background to make HUD more visible
          }}
        />
      )}

      <div className="relative z-10 w-full h-full flex flex-col">
        {/* Status bars (health, armor, oxygen) */}
        <div className="absolute left-[5.2%] bottom-[5.1%]">
          <StatusBars />
        </div>

        {/* Vehicle Speedometers - Side by side layout */}
        <div className="absolute right-[5%] bottom-[5%] flex flex-col items-center hud-gap-sm">
          {/* Test Speedometer (Blue/Cyan) with Vehicle Indicators below */}
          <div className="flex flex-col items-center">
            <TestSpeedometer />
            <VehicleIndicators />
          </div>
          
          {/* Original Speedometer (Emerald) - Hidden for now */}
          {false && (
            <div>
              <Speedometer />
            </div>
          )}
        </div>

        {/* Street names - Only shows when in vehicle */}
        <div className="absolute top-[2%] left-1/2 transform -translate-x-1/2">
          <StreetNames />
        </div>
      </div>
    </div>
  );
};

export default App;
