/**
 * HM Target - Shared Utilities
 *
 * Utility functions used across client and server.
 */

import { Vector3, Vector2 } from "./types";

export class Utils {
  /**
   * Calculate distance between two Vector3 points
   */
  static getDistance(pos1: Vector3, pos2: Vector3): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * Calculate 2D distance (ignoring Z axis)
   */
  static getDistance2D(pos1: Vector3, pos2: Vector3): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Check if a point is inside a box
   */
  static isPointInBox(
    point: Vector3,
    center: Vector3,
    size: Vector3,
    _rotation?: Vector3
  ): boolean {
    // Simple AABB check for now - can be enhanced with rotation later
    const halfSize = {
      x: size.x / 2,
      y: size.y / 2,
      z: size.z / 2,
    };

    return (
      point.x >= center.x - halfSize.x &&
      point.x <= center.x + halfSize.x &&
      point.y >= center.y - halfSize.y &&
      point.y <= center.y + halfSize.y &&
      point.z >= center.z - halfSize.z &&
      point.z <= center.z + halfSize.z
    );
  }

  /**
   * Check if a point is inside a circle
   */
  static isPointInCircle(
    point: Vector3,
    center: Vector3,
    radius: number
  ): boolean {
    return this.getDistance2D(point, center) <= radius;
  }

  /**
   * Generate a unique ID
   */
  static generateId(): string {
    return `target_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clamp a value between min and max
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * Lerp between two values
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * Convert world coordinates to screen coordinates
   */
  static worldToScreen(worldPos: Vector3): Vector2 | null {
    const [onScreen, screenX, screenY] = GetScreenCoordFromWorldCoord(
      worldPos.x,
      worldPos.y,
      worldPos.z
    );
    if (onScreen) {
      return { x: screenX, y: screenY };
    }
    return null;
  }

  /**
   * Check if player has required job
   */
  static hasJob(playerJob: string, requiredJob: string | string[]): boolean {
    if (Array.isArray(requiredJob)) {
      return requiredJob.includes(playerJob);
    }
    return playerJob === requiredJob;
  }

  /**
   * Check if player has required item
   */
  static hasItem(
    playerItems: Record<string, any>,
    requiredItem: string
  ): boolean {
    return requiredItem in playerItems && playerItems[requiredItem] > 0;
  }

  /**
   * Validate target option
   */
  static validateOption(option: any): boolean {
    if (!option || typeof option !== "object") {
      return false;
    }

    // Required fields
    if (!option.id || typeof option.id !== "string") {
      return false;
    }

    if (!option.label || typeof option.label !== "string") {
      return false;
    }

    // Must have either action or event
    if (!option.action && !option.event) {
      return false;
    }

    if (option.action && typeof option.action !== "string") {
      return false;
    }

    if (option.event && typeof option.event !== "string") {
      return false;
    }

    if (option.type && !["client", "server"].includes(option.type)) {
      return false;
    }

    return true;
  }

  /**
   * Deep clone an object
   */
  static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }
}

/**
 * Convert degrees to radians
 */
export function degToRad(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculate 3D distance between two Vector3 points
 */
export function getDistance3D(pos1: Vector3, pos2: Vector3): number {
  return Utils.getDistance(pos1, pos2);
}

/**
 * Check if a point is inside a defined zone
 */
export function isInZone(point: Vector3, zone: any): boolean {
  switch (zone.type) {
    case "box":
      return Utils.isPointInBox(point, zone.position, zone.size, zone.rotation);
    case "circle":
      return Utils.isPointInCircle(point, zone.position, zone.radius);
    case "sphere":
      return Utils.getDistance(point, zone.position) <= zone.radius;
    case "polygon":
      // Basic polygon check - could be enhanced
      return isPointInPolygon(point, zone.points);
    default:
      return false;
  }
}

/**
 * Check if a point is inside a polygon (2D check)
 */
function isPointInPolygon(point: Vector3, vertices: Vector3[]): boolean {
  let inside = false;
  const x = point.x;
  const y = point.y;

  for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
    const xi = vertices[i].x;
    const yi = vertices[i].y;
    const xj = vertices[j].x;
    const yj = vertices[j].y;

    if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
      inside = !inside;
    }
  }

  return inside;
}

/**
 * Check if an entity is valid and exists
 */
export function isValidEntity(entity: number): boolean {
  return (
    entity !== null &&
    entity !== undefined &&
    entity !== 0 &&
    DoesEntityExist(entity)
  );
}

/**
 * Validate target options array
 */
export function validateOptions(options: any[]): boolean {
  if (!Array.isArray(options) || options.length === 0) {
    return false;
  }

  return options.every((option) => {
    if (!option || typeof option !== "object") {
      return false;
    }

    // Required fields
    if (!option.id || typeof option.id !== "string") {
      return false;
    }

    if (!option.label || typeof option.label !== "string") {
      return false;
    }

    // Must have either action or event
    if (!option.action && !option.event) {
      return false;
    }

    if (option.action && typeof option.action !== "string") {
      return false;
    }

    if (option.event && typeof option.event !== "string") {
      return false;
    }

    if (option.type && !["client", "server"].includes(option.type)) {
      return false;
    }

    return true;
  });
}
