/**
 * HM-Admin Client
 * Main client-side file for the admin system
 */

import { AdminEvents, AdminPlayerInfo, AdminCommandResult, AdminPermissionLevel } from '@shared/main';
import { AdminManager } from './admin-manager';

// Variables
let adminMenuOpen = false;
let playerList: AdminPlayerInfo[] = [];
let adminLevel: AdminPermissionLevel = AdminPermissionLevel.NONE;
let noclipEnabled = false;
let spectatingPlayer: number | null = null;
let adminManager: AdminManager;

// Initialize admin system
const init = async () => {
  console.log('Initializing HM-Admin client...');
  
  // Initialize the admin manager (handles F6 toggle)
  adminManager = new AdminManager();
  
  // Register noclip command (keep this separate from the admin panel)
  RegisterCommand('noclip', () => {
    toggleNoclip();
  }, false);
  
  // Listen for player list updates
  onNet(AdminEvents.PLAYER_LIST_UPDATE, (players: AdminPlayerInfo[]) => {
    playerList = players;
    console.log(`Received player list update: ${players.length} players`);
  });
  
  // Listen for command results
  onNet(AdminEvents.COMMAND_RESULT, (result: AdminCommandResult) => {
    console.log(`Command result: ${result.command} - ${result.success ? 'Success' : 'Failed'} - ${result.message}`);
    // Display notification to player
    showNotification(result.message, result.success);
  });
  
  // Only request player list when the admin menu is opened
  // This is now handled in the admin-manager.ts when the UI is opened
  // 
  // OPTIMIZATION: Removed continuous setInterval that caused CPU usage
  // Player list updates are now requested on-demand when admin panel opens
  // This achieves 0.00ms idle CPU usage when admin panel is closed
  
  console.log('HM-Admin client initialized');
};

// Toggle admin menu
const toggleAdminMenu = () => {
  adminMenuOpen = !adminMenuOpen;
  SetNuiFocus(adminMenuOpen, adminMenuOpen);
  
  if (adminMenuOpen) {
    // Request fresh player list when opening menu
    emitNet(AdminEvents.REQUEST_PLAYER_LIST);
    // TODO: Send NUI message to show admin menu
    console.log('Admin menu opened');
  } else {
    // TODO: Send NUI message to hide admin menu
    console.log('Admin menu closed');
  }
};

// Toggle noclip mode
const toggleNoclip = () => {
  noclipEnabled = !noclipEnabled;
  
  if (noclipEnabled) {
    // Enable noclip
    console.log('Noclip enabled');
    showNotification('Noclip enabled', true);
    // TODO: Implement noclip functionality
  } else {
    // Disable noclip
    console.log('Noclip disabled');
    showNotification('Noclip disabled', true);
    // TODO: Disable noclip functionality
  }
};

// Show notification
const showNotification = (message: string, success: boolean) => {
  // TODO: Implement proper notification system
  // For now, use basic GTA notification
  SetNotificationTextEntry('STRING');
  AddTextComponentString(message);
  DrawNotification(false, false);
};

// Execute admin command
const executeAdminCommand = (command: string, targetId?: number, args?: string[]) => {
  emitNet(AdminEvents.EXECUTE_COMMAND, {
    command,
    targetId,
    args
  });
};

// Start the admin system
init();

// Export functions for NUI callbacks
global.exports('executeAdminCommand', executeAdminCommand);
global.exports('toggleAdminMenu', toggleAdminMenu);
global.exports('toggleNoclip', toggleNoclip);

console.log('HM-Admin client module loaded');