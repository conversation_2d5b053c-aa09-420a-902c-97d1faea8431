import { createRoot } from 'react-dom/client';
import { useState, useEffect } from 'react';
import './index.css';
import { initializeNuiSystem, onNuiEvent, sendNuiMessage, NuiActions, NuiMessageTypes } from './utils/nui';
import { isBrowser } from './utils/environment';
import { SpawnVehicleFeature, RepairVehicleFeature, VehicleColorFeature, DeleteVehicleFeature, SetVehiclePlateFeature } from './features/vehicle';
import { ChangeWeatherFeature, TimeControlFeature, WindControlFeature, ResetWeatherFeature } from './features/weather';
import { GiveItemFeature, GiveWeaponFeature, TeleportPlayerFeature, KickPlayerFeature, BanPlayerFeature, PlayerInfoFeature, HealPlayerFeature } from './features/player';
import { AdminStoreProvider } from './providers/AdminStoreProvider';
import InfoPanel from './components/InfoPanel';
import ScanningIndicator from './components/ScanningIndicator';
import { useInfoPanelStore } from './stores/infoPanelStore';

// Extend window interface for NUI
declare global {
  interface Window {
    invokeNative?: (event: string, data?: any) => void;
  }
}

// Admin menu sections - Removed "ALL" category
const MENU_SECTIONS = [
  { id: 'players', label: 'Players', icon: 'fa-users', color: 'purple' },
  { id: 'vehicles', label: 'Vehicles', icon: 'fa-car', color: 'green' },
  { id: 'weather', label: 'Weather', icon: 'fa-cloud-sun', color: 'blue' },
];

// Individual feature component
interface FeatureProps {
  id: string;
  title: string;
  isFavorited: boolean;
  isExpanded: boolean;
  onToggleFavorite: () => void;
  onToggleExpand: () => void;
  children?: React.ReactNode;
}

const Feature: React.FC<FeatureProps> = ({ 
  title, 
  isFavorited, 
  isExpanded, 
  onToggleFavorite, 
  onToggleExpand, 
  children 
}) => {
  return (
    <div className="bg-neutral-900/50 border border-neutral-700/50 rounded-lg overflow-hidden shadow-sm">
      {/* Feature Header - Made entire header clickable */}
      <div 
        onClick={onToggleExpand}
        className="flex items-center justify-between px-3 py-2 bg-neutral-800/30 border-b border-neutral-700/30 cursor-pointer hover:bg-neutral-800/40 transition-colors"
      >
        <div className="flex items-center gap-2">
          <i className={`fas ${isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'} text-xs text-neutral-400`} />
          <h3 className="text-neutral-100 font-medium text-xs">{title}</h3>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent header click from triggering
            onToggleFavorite();
          }}
          className={`transition-colors ${
            isFavorited 
              ? 'text-yellow-400 hover:text-yellow-300' 
              : 'text-neutral-500 hover:text-yellow-400'
          }`}
          title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
        >
          <i className={`fas ${isFavorited ? 'fa-star' : 'fa-star'} text-xs`} />
        </button>
      </div>

      {/* Feature Content */}
      {isExpanded && (
        <div className="px-3 py-2">
          {children}
        </div>
      )}
    </div>
  );
};

// Content for each section
const SectionContent = ({ section }: { section: string }) => {
  const [features, setFeatures] = useState<{[key: string]: { favorited: boolean; expanded: boolean }}>({
    'give-item': { favorited: false, expanded: false },
    'give-weapon': { favorited: false, expanded: false },
    'teleport-player': { favorited: false, expanded: false },
    'kick-player': { favorited: false, expanded: false },
    'ban-player': { favorited: false, expanded: false },
    'player-info': { favorited: false, expanded: false },
    'spawn-vehicle': { favorited: false, expanded: false },
    'repair-vehicle': { favorited: false, expanded: false },
    'set-vehicle-color': { favorited: false, expanded: false },
    'set-vehicle-plate': { favorited: false, expanded: false },
    'delete-vehicle': { favorited: false, expanded: false },
    'change-weather': { favorited: false, expanded: false },
    'time-control': { favorited: false, expanded: false },
    'wind-control': { favorited: false, expanded: false },
    'reset-weather': { favorited: false, expanded: false }
  });

  const [searchTerm, setSearchTerm] = useState('');

  const toggleFeatureFavorite = (featureId: string) => {
    setFeatures(prev => ({
      ...prev,
      [featureId]: {
        ...prev[featureId],
        favorited: !prev[featureId]?.favorited
      }
    }));
  };

  const toggleFeatureExpand = (featureId: string) => {
    setFeatures(prev => ({
      ...prev,
      [featureId]: {
        ...prev[featureId],
        expanded: !prev[featureId]?.expanded
      }
    }));
  };

  // Helper function to sort features by favorited status and filter by search term
  const sortFeatures = (featureList: any[]) => {
    return featureList
      .filter(feature => 
        feature.title.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => {
        const aFavorited = features[a.id]?.favorited || false;
        const bFavorited = features[b.id]?.favorited || false;
        if (aFavorited && !bFavorited) return -1;
        if (!aFavorited && bFavorited) return 1;
        return 0;
      });
  };

  if (section === 'players') {
    // Define player features and sort by favorited status (favorited first)
    const playerFeatures = sortFeatures([
      {
        id: 'give-item',
        title: 'Give Item',
        component: <GiveItemFeature />
      },
      {
        id: 'give-weapon',
        title: 'Give Weapon',
        component: <GiveWeaponFeature />
      },
      {
        id: 'heal-player',
        title: 'Heal Player',
        component: <HealPlayerFeature />
      },
      {
        id: 'teleport-player',
        title: 'Teleport Player',
        component: <TeleportPlayerFeature />
      },
      {
        id: 'kick-player',
        title: 'Kick Player',
        component: <KickPlayerFeature />
      },
      {
        id: 'ban-player',
        title: 'Ban Player',
        component: <BanPlayerFeature />
      },
      {
        id: 'player-info',
        title: 'Player Info',
        component: <PlayerInfoFeature />
      }
    ]);

    return (
      <div className="p-4 h-full overflow-y-auto">
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search player features..."
              className="w-full px-3 py-2 pl-9 bg-neutral-700 border border-neutral-600 rounded-md text-neutral-100 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 text-sm" />
          </div>
        </div>

        <div className="space-y-2">
          {playerFeatures.length === 0 ? (
            <div className="text-center py-8 text-neutral-400">
              <i className="fas fa-search text-2xl mb-2" />
              <p>No player features found matching "{searchTerm}"</p>
            </div>
          ) : (
            playerFeatures.map(feature => (
              <Feature
                key={feature.id}
                id={feature.id}
                title={feature.title}
                isFavorited={features[feature.id]?.favorited || false}
                isExpanded={features[feature.id]?.expanded || false}
                onToggleFavorite={() => toggleFeatureFavorite(feature.id)}
                onToggleExpand={() => toggleFeatureExpand(feature.id)}
              >
                {feature.component}
              </Feature>
            ))
          )}
        </div>
      </div>
    );
  }

  if (section === 'vehicles') {
    // Define features and sort by favorited status (favorited first)
    const vehicleFeatures = sortFeatures([
      {
        id: 'spawn-vehicle',
        title: 'Spawn Vehicle',
        component: <SpawnVehicleFeature />
      },
      {
        id: 'repair-vehicle', 
        title: 'Repair Vehicle',
        component: <RepairVehicleFeature />
      },
      {
        id: 'set-vehicle-color',
        title: 'Set Vehicle Color',
        component: <VehicleColorFeature />
      },
      {
        id: 'set-vehicle-plate',
        title: 'Set Vehicle Plate',
        component: <SetVehiclePlateFeature />
      },
      {
        id: 'delete-vehicle',
        title: 'Delete Vehicle',
        component: <DeleteVehicleFeature />
      }
    ]);

    return (
      <div className="p-4 h-full overflow-y-auto">
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search vehicle features..."
              className="w-full px-3 py-2 pl-9 bg-neutral-700 border border-neutral-600 rounded-md text-neutral-100 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 text-sm" />
          </div>
        </div>

        <div className="space-y-2">
          {vehicleFeatures.length === 0 ? (
            <div className="text-center py-8 text-neutral-400">
              <i className="fas fa-search text-2xl mb-2" />
              <p>No vehicle features found matching "{searchTerm}"</p>
            </div>
          ) : (
            vehicleFeatures.map(feature => (
              <Feature
                key={feature.id}
                id={feature.id}
                title={feature.title}
                isFavorited={features[feature.id]?.favorited || false}
                isExpanded={features[feature.id]?.expanded || false}
                onToggleFavorite={() => toggleFeatureFavorite(feature.id)}
                onToggleExpand={() => toggleFeatureExpand(feature.id)}
              >
                {feature.component}
              </Feature>
            ))
          )}
        </div>
      </div>
    );
  }

  if (section === 'weather') {
    // Define weather features and sort by favorited status (favorited first)
    const weatherFeatures = sortFeatures([
      {
        id: 'change-weather',
        title: 'Change Weather',
        component: <ChangeWeatherFeature />
      },
      {
        id: 'time-control',
        title: 'Time Control',
        component: <TimeControlFeature />
      },
      {
        id: 'wind-control',
        title: 'Wind Control',
        component: <WindControlFeature />
      },
      {
        id: 'reset-weather',
        title: 'Reset Weather',
        component: <ResetWeatherFeature />
      }
    ]);

    return (
      <div className="p-4 h-full overflow-y-auto">
        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search weather features..."
              className="w-full px-3 py-2 pl-9 bg-neutral-700 border border-neutral-600 rounded-md text-neutral-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 text-sm" />
          </div>
        </div>

        <div className="space-y-2">
          {weatherFeatures.length === 0 ? (
            <div className="text-center py-8 text-neutral-400">
              <i className="fas fa-search text-2xl mb-2" />
              <p>No weather features found matching "{searchTerm}"</p>
            </div>
          ) : (
            weatherFeatures.map(feature => (
              <Feature
                key={feature.id}
                id={feature.id}
                title={feature.title}
                isFavorited={features[feature.id]?.favorited || false}
                isExpanded={features[feature.id]?.expanded || false}
                onToggleFavorite={() => toggleFeatureFavorite(feature.id)}
                onToggleExpand={() => toggleFeatureExpand(feature.id)}
              >
                {feature.component}
              </Feature>
            ))
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 h-full flex items-center justify-center">
      <div className="text-center text-neutral-400">
        <i className="fas fa-exclamation-triangle text-2xl mb-2" />
        <p>Section not found</p>
      </div>
    </div>
  );
};

// Main App component
const App = () => {
  const [visible, setVisible] = useState(isBrowser() ? true : false);
  const [hidden, setHidden] = useState(false); // Hidden state preserves focus but hides UI
  const [activeSection, setActiveSection] = useState('players'); // Default to players since "all" is removed
  const [debugMode, setDebugMode] = useState(false);
  
  // InfoPanel state
  const { 
    isVisible: infoPanelVisible, 
    targetInfo, 
    showPanel, 
    hidePanel, 
    handleButtonAction,
    // Scanning state and actions
    isScanning,
    scanTarget,
    startScanning,
    stopScanning,
    updateScanTarget,
    clearScanTarget
  } = useInfoPanelStore();

  // Initialize NUI system and listen for messages
  useEffect(() => {
    const cleanup = initializeNuiSystem();

    // Set initially invisible to prevent auto data loading
    // This ensures data isn't loaded when resource starts
    if (isBrowser()) {
      // In development mode, we can show the UI
      setVisible(true);
    } else {
      // In production, start hidden until explicitly shown
      setVisible(false);
    }

    // Listen for visibility changes
    const unsubscribeVisible = onNuiEvent(NuiMessageTypes.SET_VISIBLE, (data: { visible: boolean }) => {
      console.log('Received setVisible message:', JSON.stringify(data, null, 2));
      setVisible(data.visible);
    });
    
    // Add keyboard event listeners
    const handleKeyDown = (event: KeyboardEvent) => {
      if (visible) {
        // F6 - Close UI (matches the key used to open it)
        if (event.key === 'F6' || event.keyCode === 117) {
          console.log('F6 pressed - closing admin UI');
          sendNuiMessage(NuiActions.CLOSE_PANEL, {});
          event.preventDefault();
        }
        
        // ESC - Close UI
        if (event.key === 'Escape' || event.keyCode === 27) {
          console.log('Escape pressed - closing admin UI');
          sendNuiMessage(NuiActions.CLOSE_PANEL, {});
          event.preventDefault();
        }
      }
      
      // Global hotkeys (work even when admin panel is not visible)
      // I key - Toggle InfoPanel
      if (event.key === 'i' || event.key === 'I') {
        console.log('I key pressed - toggling InfoPanel');
        if (infoPanelVisible) {
          hidePanel();
        } else {
          // Show panel with mock target data for testing
          // In production, this would get real target data from raycast
          const mockTarget = {
            entity: 1234,
            modelHash: -123456789,
            position: { x: 123.45, y: 678.90, z: 21.34 },
            options: [],
            distance: 2.5,
            type: "entity" as const
          };
          showPanel(mockTarget);
        }
        event.preventDefault();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    // Cleanup function
    return () => {
      cleanup();
      unsubscribeVisible();
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []); // Remove visibility dependency to prevent re-initialization

  // Second useEffect for NUI event listeners
  useEffect(() => {
    // Listen for hidden state changes (hide UI but keep focus for controls)
    const unsubscribeHidden = onNuiEvent(NuiMessageTypes.SET_HIDDEN, (data: { hidden: boolean }) => {
      console.log('Received setHidden message:', JSON.stringify(data, null, 2));
      // Set hidden state immediately without transition delay
      setHidden(data.hidden);
      
      // Additional debug logging
      if (data.hidden) {
        console.log('UI: Hiding admin panel for ghost placement');
      } else {
        console.log('UI: Showing admin panel from ghost placement');
      }
    });

    // Listen for InfoPanel events
    const unsubscribeShowInfoPanel = onNuiEvent(NuiMessageTypes.SHOW_INFO_PANEL, (data: { target: any }) => {
      console.log('Received showInfoPanel message:', JSON.stringify(data, null, 2));
      if (data.target) {
        showPanel(data.target);
      }
    });

    const unsubscribeHideInfoPanel = onNuiEvent(NuiMessageTypes.HIDE_INFO_PANEL, () => {
      console.log('Received hideInfoPanel message');
      hidePanel();
    });

    const unsubscribeUpdateTargetInfo = onNuiEvent(NuiMessageTypes.UPDATE_TARGET_INFO, (data: { target: any }) => {
      console.log('Received updateTargetInfo message:', JSON.stringify(data, null, 2));
      if (data.target && infoPanelVisible) {
        showPanel(data.target);
      }
    });

    // Listen for scanning events
    const unsubscribeStartScanning = onNuiEvent('startScanning', () => {
      console.log('Received startScanning message');
      startScanning();
    });

    const unsubscribeStopScanning = onNuiEvent('stopScanning', () => {
      console.log('Received stopScanning message');
      stopScanning();
    });

    const unsubscribeUpdateScanTarget = onNuiEvent('updateScanTarget', (data: { target: any }) => {
      console.log('Received updateScanTarget message:', JSON.stringify(data, null, 2));
      if (data.target) {
        updateScanTarget(data.target);
      }
    });

    const unsubscribeClearScanTarget = onNuiEvent('clearScanTarget', () => {
      console.log('Received clearScanTarget message');
      clearScanTarget();
    });

    // Cleanup on unmount
    return () => {
      unsubscribeHidden();
      unsubscribeShowInfoPanel();
      unsubscribeHideInfoPanel();
      unsubscribeUpdateTargetInfo();
      unsubscribeStartScanning();
      unsubscribeStopScanning();
      unsubscribeUpdateScanTarget();
      unsubscribeClearScanTarget();
    };
  }, [showPanel, hidePanel, infoPanelVisible]);

  // Send debug mode changes to client
  useEffect(() => {
    sendNuiMessage(NuiActions.SET_DEBUG_MODE, { enabled: debugMode });
  }, [debugMode]);

  if (!visible) return null;

  return (
    <div className="fixed top-0 right-0 h-screen flex items-center justify-end z-50 pointer-events-none">
      <div className={`flex items-stretch h-full max-h-[80%] w-[30vw] mr-8 pointer-events-auto ${
        hidden ? 'opacity-0 pointer-events-none transition-none' : 'opacity-100 transition-opacity duration-300'
      }`}>
        
        {/* Main admin panel */}
        <div className="flex w-full bg-neutral-800 rounded-2xl shadow-xl border-t-4 border-green-500/20 relative overflow-hidden">
          {/* Top gradient bar */}
          <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-green-400/10 via-green-500/10 to-green-600/10 opacity-30 z-10 rounded-t-2xl" />
          
          {/* Unified Header Row */}
          <div className="absolute top-0 left-0 w-full h-16 flex items-center border-b border-neutral-700/50 z-20 bg-neutral-800">
            {/* Left side - Main title */}
            <div className="flex items-center" style={{ paddingLeft: '34px' }}>
              <h2 className="text-base font-bold text-neutral-100 tracking-wide flex-shrink-0">
                Hi, Admin.
              </h2>
            </div>
            
            {/* Center - Server Stats */}
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-3 text-xs">
                <div className="flex items-center gap-1.5 text-neutral-300">
                  <i className="fas fa-users text-blue-400 text-xs" />
                  <span className="font-medium">24/64</span>
                </div>
                
                <div className="flex items-center gap-1.5 text-neutral-300">
                  <i className="fas fa-clock text-purple-400 text-xs" />
                  <span className="font-medium">127ms</span>
                </div>
                
                <div className="flex items-center gap-1.5 text-neutral-300">
                  <i className="fas fa-database text-orange-400 text-xs" />
                  <span className="font-medium">MySQL</span>
                </div>
                
                {/* Debug Mode Toggle */}
                <div className="flex items-center gap-1.5 text-neutral-300 border-l border-neutral-600 pl-3 ml-1">
                  <i className="fas fa-bug text-red-400 text-xs" />
                  <label className="flex items-center gap-1 cursor-pointer">
                    <span className="font-medium text-xs">Debug</span>
                    <input
                      type="checkbox"
                      checked={debugMode}
                      onChange={(e) => setDebugMode(e.target.checked)}
                      className="w-3 h-3 text-red-500 bg-neutral-700 border-neutral-600 rounded focus:ring-red-500 focus:ring-2"
                    />
                  </label>
                </div>
              </div>
            </div>
            
            {/* Right side - Close button */}
            <div className="px-4">
              <button 
                onClick={() => {
                  // Send close message to client using new NUI system
                  sendNuiMessage(NuiActions.CLOSE_PANEL);
                }}
                className="text-neutral-400 hover:text-red-400 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 rounded-full p-2"
                title="Close Admin Panel"
              >
                <i className="fas fa-times text-lg" />
              </button>
            </div>
          </div>

          {/* Left Sidebar - Categories */}
          <div className="w-32 bg-neutral-800 flex flex-col pt-16">
            {/* Navigation Categories */}
            <div className="flex-1 py-2 px-2">
              {MENU_SECTIONS.map(section => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full h-10 mb-2 flex items-center gap-2 px-3 rounded-lg transition-all duration-200 group ${
                    activeSection === section.id
                      ? 'bg-green-600/30 border border-green-500/50 text-green-400 shadow-lg'
                      : 'text-neutral-500 hover:text-neutral-300 hover:bg-neutral-800/50 border border-transparent'
                  }`}
                  title={section.label}
                >
                  <i className={`fas ${section.icon} text-sm flex-shrink-0`} />
                  <span className="text-xs font-medium truncate">{section.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 flex flex-col pt-16">
            {/* Content Area */}
            <div className="flex-1 overflow-hidden">
              <SectionContent section={activeSection} />
            </div>

            {/* Footer */}
            <div className="p-4 pt-3 border-t border-neutral-700/50 flex items-center justify-between text-xs text-neutral-400">
              <span>HM Admin v1.0.0</span>
              <span className="flex items-center gap-2">
                <span>Last updated: {new Date().toLocaleTimeString()}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* InfoPanel - rendered independently */}
      {infoPanelVisible && targetInfo && (
        <InfoPanel
          target={targetInfo}
          onButtonClick={handleButtonAction}
        />
      )}
      
      {/* ScanningIndicator - rendered independently */}
      <ScanningIndicator
        isScanning={isScanning}
        scanTarget={scanTarget}
      />
    </div>
  );
};

// Initialize the application
const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Failed to find the root element');
}

try {
  const root = createRoot(rootElement);
  root.render(
    <AdminStoreProvider>
      <App />
    </AdminStoreProvider>
  );
} catch (error) {
  console.error('Error rendering the application:', error);
}