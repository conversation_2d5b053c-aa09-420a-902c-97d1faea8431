import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PhotoPreviewProps {
  photoUrl: string;
  onSave: () => void;
  onDiscard: () => void;
  onBack: () => void;
}

const PhotoPreview: React.FC<PhotoPreviewProps> = ({ photoUrl, onSave, onDiscard, onBack }) => {
  // State to track if the image is in fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className="h-full w-full flex flex-col bg-black">
      {/* Header */}
      <div className="p-4 flex justify-between items-center">
        <button onClick={onBack} className="w-10 h-10 flex items-center justify-center text-white">
          <i className="fas fa-arrow-left"></i>
        </button>
        <div className="text-white font-medium">Preview</div>
        <div className="w-10"></div> {/* Empty div for spacing */}
      </div>

      {/* Photo Preview */}
      <div className="flex-1 relative overflow-hidden bg-black flex items-center justify-center">
        {/* Photo display - using object-contain to maintain aspect ratio */}
        <motion.img
          src={photoUrl}
          alt="Preview"
          className="w-full h-full object-contain cursor-pointer"
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            imageRendering: 'auto'
          }}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          onClick={toggleFullscreen}
        />

        {/* Location tag */}
        <div className="absolute bottom-4 left-4">
          <div className="bg-black/60 backdrop-blur-sm rounded-lg px-3 py-1.5 flex items-center">
            <i className="fas fa-map-marker-alt text-white/80 mr-2"></i>
            <span className="text-white/80 text-xs">Los Santos, San Andreas</span>
          </div>
        </div>

        {/* Tap to view hint */}
        <div className="absolute top-4 right-4">
          <div className="bg-black/60 backdrop-blur-sm rounded-lg px-3 py-1.5 flex items-center">
            <i className="fas fa-search-plus text-white/80 mr-2"></i>
            <span className="text-white/80 text-xs">Tap to view full image</span>
          </div>
        </div>
      </div>

      {/* Fullscreen view */}
      <AnimatePresence>
        {isFullscreen && (
          <motion.div
            className="fixed inset-0 z-50 bg-black flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={toggleFullscreen}
          >
            <motion.img
              src={photoUrl}
              alt="Fullscreen Preview"
              className="max-w-full max-h-full object-contain"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            />

            {/* Close button */}
            <button
              className="absolute top-4 right-4 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white"
              onClick={toggleFullscreen}
            >
              <i className="fas fa-times"></i>
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Action Buttons */}
      <div className="p-4 flex justify-between items-center bg-black">
        <button
          onClick={onDiscard}
          className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center text-red-500"
        >
          <i className="fas fa-trash"></i>
        </button>

        <button
          onClick={onSave}
          className="w-12 h-12 rounded-full bg-cyan-500 flex items-center justify-center text-white"
        >
          <i className="fas fa-check"></i>
        </button>
      </div>
    </div>
  );
};

export default PhotoPreview;
