/**
 * Utility functions for notification actions
 */

import { Notification } from '../types/notificationTypes';

/**
 * Add a notification to the notification state
 * @param state Current notification state
 * @param notification Notification to add
 * @returns Updated notification state
 */
export const addNotification = (
  state: Record<number, Notification[]>,
  notification: Notification
): Record<number, Notification[]> => {
  const newState = { ...state };

  if (!newState[notification.appId]) {
    newState[notification.appId] = [];
  }

  newState[notification.appId].push(notification);

  return newState;
};

/**
 * Remove a notification from the notification state
 * @param state Current notification state
 * @param appId App ID
 * @param notificationId Notification ID
 * @returns Updated notification state
 */
export const removeNotification = (
  state: Record<number, Notification[]>,
  appId: number,
  notificationId: number
): Record<number, Notification[]> => {
  const newState = { ...state };

  if (newState[appId]) {
    newState[appId] = newState[appId].filter(n => n.id !== notificationId);
  }

  return newState;
};

/**
 * Clear notifications for an app or all apps
 * @param state Current notification state
 * @param appId Optional app ID to clear notifications for
 * @returns Updated notification state
 */
export const clearNotifications = (
  state: Record<number, Notification[]>,
  appId?: number
): Record<number, Notification[]> => {
  if (appId !== undefined) {
    const newState = { ...state };
    delete newState[appId];
    return newState;
  }

  return {};
};

/**
 * Mark a notification as read
 * @param state Current notification state
 * @param appId App ID
 * @param notificationId Notification ID
 * @returns Updated notification state
 */
export const markAsRead = (
  state: Record<number, Notification[]>,
  appId: number,
  notificationId: number
): Record<number, Notification[]> => {
  const newState = { ...state };

  if (newState[appId]) {
    newState[appId] = newState[appId].map(n =>
      n.id === notificationId ? { ...n, read: true } : n
    );
  }

  return newState;
};

/**
 * Move a notification to the top bar
 * @param state Current notification state
 * @param appId App ID
 * @param notificationId Notification ID
 * @returns Updated notification state
 */
export const moveToTopBar = (
  state: Record<number, Notification[]>,
  appId: number,
  notificationId: number
): Record<number, Notification[]> => {
  const newState = { ...state };

  if (newState[appId]) {
    newState[appId] = newState[appId].map(n =>
      n.id === notificationId ? { ...n, movedToTopBar: true } : n
    );
  }

  return newState;
};

/**
 * Toggle notification expansion
 * @param state Current notification state
 * @param appId App ID
 * @param notificationId Notification ID
 * @returns Updated notification state
 */
export const toggleExpansion = (
  state: Record<number, Notification[]>,
  appId: number,
  notificationId: number
): Record<number, Notification[]> => {
  const newState = { ...state };

  if (newState[appId]) {
    newState[appId] = newState[appId].map(n =>
      n.id === notificationId ? { ...n, expandable: !n.expandable } : n
    );
  }

  return newState;
};

/**
 * Get unread notification count by app
 * @param state Current notification state
 * @returns Record of unread counts by app ID
 */
export const getUnreadCountByApp = (
  state: Record<number, Notification[]>
): Record<number, number> => {
  const counts: Record<number, number> = {};

  Object.entries(state).forEach(([appId, appNotifications]) => {
    const unreadCount = appNotifications.filter(n => !n.read).length;
    if (unreadCount > 0) {
      counts[parseInt(appId)] = unreadCount;
    }
  });

  return counts;
};
