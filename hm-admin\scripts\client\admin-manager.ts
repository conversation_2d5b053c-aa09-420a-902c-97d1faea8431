/**
 * HM Admin Manager
 * Handles the admin panel toggle system with F6 key
 * Manages all event handlers and NUI callbacks lifecycle
 */

import { VehicleManager } from './vehicles';
import { weatherManager } from './weather';
import { NotificationUtils, setDebugMode, renderDebugVisualizations } from '../shared/utilities';
import TargetIntegration from './target-integration';

export class AdminManager {
  private isOpen: boolean = false;
  private vehicleManager: VehicleManager | null = null;  constructor() {
    this.registerToggleKey();
    this.registerNUICallbacks();
    this.registerServerEvents();
    const targetIntegration = TargetIntegration.getInstance();
  }
  private registerToggleKey(): void {
    // Use RegisterKeyMapping + RegisterCommand for 0.00ms idle CPU usage like ox_target
    // This eliminates the continuous setTick that was consuming CPU
    RegisterKeyMapping(
      "hm-admin-toggle",
      "Toggle Admin Panel",
      "keyboard",
      "F6"
    );

    RegisterCommand(
      "hm-admin-toggle",
      () => {
        this.toggle();
      },
      false
    );
  }

  public toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }  public open(): void {
    this.isOpen = true;
    SetNuiFocus(true, true);
    
    // Explicitly send NUI message to initialize the UI with visible state
    SendNUIMessage({
      type: 'setVisible',
      visible: true
    } as any);
    
    // Request player list when opening the UI
    emitNet('hm-admin:requestPlayerList');
    
    this.vehicleManager = new VehicleManager(this);
    NotificationUtils.show('Admin panel opened', 'info');
  }public close(): void {
    this.isOpen = false;
    SetNuiFocus(false, false);
    SendNUIMessage({
      type: 'setVisible',
      visible: false
    } as any);
    if (this.vehicleManager) {
      // Stop ghost placement if active
      if (this.vehicleManager.isGhostPlacementActive()) {
        this.vehicleManager.stopGhostPlacement();
      }
      this.vehicleManager = null;
    }
    // The store will handle its own cleanup when it receives the setVisible event
    NotificationUtils.show('Admin panel closed', 'info');
  }
  public show(): void {
    this.isOpen = true;
    SetNuiFocus(true, true);
    SendNUIMessage({
      type: 'setVisible',
      visible: true
    } as any);
  }
  public hide(): void {
    this.isOpen = false;
    SetNuiFocus(false, false);
    SendNUIMessage({
      type: 'setVisible',
      visible: false
    } as any);
  }
  private registerNUICallbacks(): void {
    // Close panel callback
    RegisterNuiCallbackType('hm-admin:closePanel');
    on('__cfx_nui:hm-admin:closePanel', (data: any, cb: Function) => {
      this.close();
      cb('ok');
    });
    
    // Vehicle spawn callback
    RegisterNuiCallbackType('hm-admin:spawnVehicle');
    on('__cfx_nui:hm-admin:spawnVehicle', (data: any, cb: Function) => {
      if (this.vehicleManager) {
        this.vehicleManager.spawnVehicle(data.model);
      }
      cb('ok');
    });

    // Vehicle list request callback
    RegisterNuiCallbackType('hm-admin:getVehicleList');
    on('__cfx_nui:hm-admin:getVehicleList', (data: any, cb: Function) => {
      emitNet('hm-admin:server:requestVehicleList');
      cb('ok');
    });

    // Vehicle delete callback
    RegisterNuiCallbackType('hm-admin:deleteVehicle');
    on('__cfx_nui:hm-admin:deleteVehicle', (data: any, cb: Function) => {
      if (this.vehicleManager) {
        this.vehicleManager.deleteVehicle();
      }
      cb('ok');
    });

    // Weather callbacks    RegisterNuiCallbackType('hm-admin:changeWeather');
    on('__cfx_nui:hm-admin:changeWeather', (data: any, cb: Function) => {
      weatherManager.changeWeather(data.weather);
      cb('ok');
    });RegisterNuiCallbackType('hm-admin:setTime');
    on('__cfx_nui:hm-admin:setTime', (data: any, cb: Function) => {
      weatherManager.setTime(data.hour, data.minute || 0);
      cb('ok');
    });RegisterNuiCallbackType('hm-admin:freezeTime');
    on('__cfx_nui:hm-admin:freezeTime', (data: any, cb: Function) => {
      weatherManager.freezeTime(data.freeze);
      cb('ok');
    });    // Weather instant change callback
    RegisterNuiCallbackType('hm-admin:setWeatherInstant');
    on('__cfx_nui:hm-admin:setWeatherInstant', (data: any, cb: Function) => {
      weatherManager.setWeatherInstant(data.weather);
      cb('ok');
    });

    // Time scale callback
    RegisterNuiCallbackType('hm-admin:setTimeScale');
    on('__cfx_nui:hm-admin:setTimeScale', (data: any, cb: Function) => {
      weatherManager.setTimeScale(data.scale);
      cb('ok');
    });

    // Wind speed callback
    RegisterNuiCallbackType('hm-admin:setWindSpeed');
    on('__cfx_nui:hm-admin:setWindSpeed', (data: any, cb: Function) => {
      weatherManager.setWindSpeed(data.speed);
      cb('ok');
    });

    // Wind direction callback
    RegisterNuiCallbackType('hm-admin:setWindDirection');
    on('__cfx_nui:hm-admin:setWindDirection', (data: any, cb: Function) => {
      weatherManager.setWindDirection(data.direction);
      cb('ok');
    });

    // Reset weather callback
    RegisterNuiCallbackType('hm-admin:resetWeather');
    on('__cfx_nui:hm-admin:resetWeather', (data: any, cb: Function) => {
      weatherManager.resetWeather();
      cb('ok');
    });

    // Reset time callback
    RegisterNuiCallbackType('hm-admin:resetTime');
    on('__cfx_nui:hm-admin:resetTime', (data: any, cb: Function) => {
      weatherManager.resetTime();
      cb('ok');
    });    // Close panel callback
    RegisterNuiCallbackType('hm-admin:closePanel');
    on('__cfx_nui:hm-admin:closePanel', (data: any, cb: Function) => {
      this.close();
      cb('ok');
    });

    // Player management callbacks
    RegisterNuiCallbackType('hm-admin:getPlayerList');
    on('__cfx_nui:hm-admin:getPlayerList', (data: any, cb: Function) => {
      emitNet('hm-admin:server:requestPlayerList');
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:getPlayerInfo');
    on('__cfx_nui:hm-admin:getPlayerInfo', (data: { playerId: number }, cb: Function) => {
      emitNet('hm-admin:server:requestPlayerInfo', data.playerId);
      cb('ok');
    });    RegisterNuiCallbackType('hm-admin:getItemList');
    on('__cfx_nui:hm-admin:getItemList', (data: any, cb: Function) => {
      emitNet('hm-admin:server:requestItemList');
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:getWeaponList');
    on('__cfx_nui:hm-admin:getWeaponList', (data: any, cb: Function) => {
      emitNet('hm-admin:server:requestWeaponList');
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:giveItem');
    on('__cfx_nui:hm-admin:giveItem', (data: { targetId: number; itemName: string; quantity: number }, cb: Function) => {
      emitNet('hm-admin:server:giveItem', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:giveWeapon');
    on('__cfx_nui:hm-admin:giveWeapon', (data: { targetId: number; weaponName: string; ammo?: number }, cb: Function) => {
      emitNet('hm-admin:server:giveWeapon', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:teleportPlayerToPlayer');
    on('__cfx_nui:hm-admin:teleportPlayerToPlayer', (data: { targetId: number; destinationPlayerId: number }, cb: Function) => {
      emitNet('hm-admin:server:teleportPlayerToPlayer', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:teleportPlayerToCoords');
    on('__cfx_nui:hm-admin:teleportPlayerToCoords', (data: { targetId: number; x: number; y: number; z: number }, cb: Function) => {
      emitNet('hm-admin:server:teleportPlayerToCoords', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:kickPlayer');
    on('__cfx_nui:hm-admin:kickPlayer', (data: { targetId: number; reason: string }, cb: Function) => {
      emitNet('hm-admin:server:kickPlayer', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:banPlayer');
    on('__cfx_nui:hm-admin:banPlayer', (data: { targetId: number; reason: string; duration?: number }, cb: Function) => {
      emitNet('hm-admin:server:banPlayer', data);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:healPlayer');
    on('__cfx_nui:hm-admin:healPlayer', (data: { targetId: number; health: number }, cb: Function) => {
      emitNet('hm-admin:server:healPlayer', data);
      cb('ok');
    });
  }
  private registerServerEvents(): void {
    // Handle vehicle list updates from server
    onNet('hm-admin:client:vehicleListUpdate', (vehicles: any[]) => {
      SendNUIMessage({
        type: 'vehicleList',
        data: vehicles
      });
    });

    // Handle player list updates from server
    onNet('hm-admin:client:playerListUpdate', (players: any[]) => {
      SendNUIMessage({
        type: 'playerList',
        data: players
      });
    });    // Handle item list updates from server
    onNet('hm-admin:client:sendItemList', (items: any[]) => {
      SendNUIMessage({
        type: 'itemList',
        data: items
      });
    });

    // Handle weapon list updates from server
    onNet('hm-admin:client:sendWeaponList', (weapons: any[]) => {
      SendNUIMessage({
        type: 'weaponList',
        data: weapons
      });
    });

    // Handle operation results from server
    onNet('hm-admin:client:sendOperationResult', (result: any) => {
      SendNUIMessage({
        type: 'operationResult',
        data: result
      });

      // Show notification based on result
      if (result.success) {
        NotificationUtils.show(result.message, 'success');
      } else {
        NotificationUtils.show(result.message, 'error');
      }
    });

    // Handle player info updates from server
    onNet('hm-admin:client:playerInfoUpdate', (playerInfo: any) => {
      SendNUIMessage({
        type: 'playerInfo',
        data: playerInfo
      });
    });
  }
}
