import React, { useState, useEffect, memo } from "react";

interface TargetOverlayProps {
  target: {
    position: { x: number; y: number; z: number };
    distance: number;
    options: any[];
    entity?: number;
    zone?: any;
  };
}

const TargetOverlay: React.FC<TargetOverlayProps> = memo(({ target }) => {
  const [isVisible, setIsVisible] = useState(false);

  // Animation entrance effect
  useEffect(() => {
    setIsVisible(true);
  }, []);
  return (
    <div className="target-overlay fixed inset-0 pointer-events-none z-50">
      {/* Diamond Target with Eye Icon - Always centered */}
      <div
        className={`fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300 ease-out ${
          isVisible ? "opacity-100 scale-100" : "opacity-0 scale-90"
        }`}
        style={{ zIndex: 1000 }}
      >
        <div className="relative w-8 h-8">
          {/* Outer diamond border */}
          <div className="absolute inset-0 transform rotate-45 border-2 border-green-500/80 bg-black/20 backdrop-blur-sm transition-all duration-500" />
          {/* Inner diamond accent */}
          <div className="absolute inset-1 transform rotate-45 border border-green-400/60 transition-all duration-500" />
          {/* Eye icon with green accent */}
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <svg
              className="w-4 h-4 text-green-400 transition-all duration-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path
                fillRule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          {/* Corner accent dots */}
          <div className="absolute -top-1 -left-1 w-1 h-1 bg-green-400 rounded-full animate-pulse" />
          <div
            className="absolute -top-1 -right-1 w-1 h-1 bg-green-500 rounded-full animate-pulse"
            style={{ animationDelay: "0.5s" }}
          />
          <div
            className="absolute -bottom-1 -left-1 w-1 h-1 bg-green-500 rounded-full animate-pulse"
            style={{ animationDelay: "1s" }}
          />
          <div
            className="absolute -bottom-1 -right-1 w-1 h-1 bg-green-400 rounded-full animate-pulse"
            style={{ animationDelay: "1.5s" }}
          />{" "}
        </div>
      </div>
    </div>
  );
});

TargetOverlay.displayName = "TargetOverlay";

export default TargetOverlay;
