const fs = require('fs');
const path = require('path');

// ANSI colors
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    red: '\x1b[31m'
};

const resourcesWithBuilds = [
    'hm-admin',
    'hm-banking',
    'hm-core',
    'hm-doorlock',
    'hm-events',
    'hm-garages',
    'hm-hud',
    'hm-inventory',
    'hm-multicharacter',
    'hm-phone',
    'hm-polyzones',
    'hm-target',
    'hm-weapons'
];

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function deleteFolderRecursive(folderPath) {
    if (fs.existsSync(folderPath)) {
        fs.readdirSync(folderPath).forEach((file) => {
            const curPath = path.join(folderPath, file);
            if (fs.lstatSync(curPath).isDirectory()) {
                deleteFolderRecursive(curPath);
            } else {
                fs.unlinkSync(curPath);
            }
        });
        fs.rmdirSync(folderPath);
    }
}

function cleanResource(resourceName) {
    const resourcePath = path.resolve(__dirname, resourceName);
    
    if (!fs.existsSync(resourcePath)) {
        log(`⚠️  ${resourceName} not found, skipping...`, 'yellow');
        return false;
    }

    let cleaned = false;

    // Clean build directory
    const buildPath = path.join(resourcePath, 'build');
    if (fs.existsSync(buildPath)) {
        deleteFolderRecursive(buildPath);
        log(`🗑️  Cleaned build directory for ${resourceName}`, 'blue');
        cleaned = true;
    }

    // Clean node_modules
    const nodeModulesPath = path.join(resourcePath, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
        deleteFolderRecursive(nodeModulesPath);
        log(`🗑️  Cleaned node_modules for ${resourceName}`, 'blue');
        cleaned = true;
    }

    // Clean UI build and node_modules
    const uiPath = path.join(resourcePath, 'ui');
    if (fs.existsSync(uiPath)) {
        const uiBuildPath = path.join(uiPath, 'dist');
        const uiNodeModulesPath = path.join(uiPath, 'node_modules');
        
        if (fs.existsSync(uiBuildPath)) {
            deleteFolderRecursive(uiBuildPath);
            log(`🗑️  Cleaned UI dist directory for ${resourceName}`, 'blue');
            cleaned = true;
        }
        
        if (fs.existsSync(uiNodeModulesPath)) {
            deleteFolderRecursive(uiNodeModulesPath);
            log(`🗑️  Cleaned UI node_modules for ${resourceName}`, 'blue');
            cleaned = true;
        }
    }

    return cleaned;
}

async function cleanAll() {
    log('\n🧹 Cleaning all HM Framework resources', 'cyan');
    log('======================================', 'cyan');

    let totalCleaned = 0;

    for (const resourceName of resourcesWithBuilds) {
        if (cleanResource(resourceName)) {
            totalCleaned++;
            log(`✅ ${resourceName} cleaned`, 'green');
        } else {
            log(`ℹ️  ${resourceName} - nothing to clean`, 'blue');
        }
    }

    log('\n📊 Cleaning Summary', 'cyan');
    log('===================', 'cyan');
    log(`Resources cleaned: ${totalCleaned}`);
    
    if (totalCleaned > 0) {
        log('\n🎉 Cleaning completed successfully!', 'green');
    } else {
        log('\nℹ️  Nothing to clean', 'blue');
    }
}

cleanAll().catch(error => {
    log(`Fatal error: ${error.message}`, 'red');
    process.exit(1);
});
