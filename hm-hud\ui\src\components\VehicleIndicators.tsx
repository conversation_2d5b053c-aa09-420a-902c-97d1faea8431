import React from 'react';
import { useHudStore } from '../store/hudStore';

const VehicleIndicators: React.FC = () => {
  const { hudData } = useHudStore();
  
  // Check for browser testing
  const isBrowser = typeof window !== 'undefined' && !(window as unknown as { invokeNative?: unknown }).invokeNative;
  
  // Don't render if not in a vehicle (except in browser for testing)
  if (!isBrowser && !hudData.vehicle.isInVehicle) {
    return null;
  }

  return (
    <div className="flex justify-center hud-gap-md mt-4">
      <img src="./engine_off.svg" alt="engine" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./battery_off.svg" alt="battery" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./oil_off.svg" alt="oil" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./fuel.svg" alt="fuel" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./abs_off.svg" alt="abs" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./handbrake_off.svg" alt="handbrake" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./seatbelt.svg" alt="seatbelt" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./headlights_off.svg" alt="headlights" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./fullbeam_off.svg" alt="fullbeam" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./indicator_left_off.svg" alt="indicator_left" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
      <img src="./indicator_right_off.svg" alt="indicator_right" className="hud-icon" style={{ filter: 'brightness(0) invert(1)' }} />
    </div>
  );
};

export default VehicleIndicators;
