import React, { memo, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface TargetOptionsProps {
  target: {
    position: { x: number; y: number; z: number };
    options: Array<{
      id: string;
      label: string;
      icon?: string;
      disabled?: boolean;
    }>;
  };
  onOptionSelect: (option: any, index: number) => void;
  onClose: () => void;
}

const TargetOptions: React.FC<TargetOptionsProps> = memo(
  ({ target, onOptionSelect, onClose }) => {
    return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      {" "}
      <motion.div
        className="target-options pointer-events-auto absolute flex flex-col gap-1 min-w-36 max-w-44"
        style={{
          left: "calc(50% + 16px)", // Start from right edge of 32px (w-8) diamond
          top: "50%",
          transform: "translate(0, -50%)",
        }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
      >
        {" "}
        {/* Sleek Modern Card-Style Vertical List - Compact */}
        <AnimatePresence>
          {target.options.map((option, originalIndex) => {
            // Skip disabled options
            if (option.disabled) return null;
            
            // Calculate animation delay based on visible options order
            const visibleIndex = target.options.slice(0, originalIndex).filter(o => !o.disabled).length;
            
            return (
              <motion.button
                key={option.id}
                onClick={() => {
                  console.log(`[TargetOptions] Button clicked: ${option.label} (index: ${originalIndex})`);
                  onOptionSelect(option, originalIndex);
                }}
                disabled={option.disabled}
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{
                  delay: visibleIndex * 0.05,
                  duration: 0.25,
                  ease: "easeOut",
                }}
                whileHover={{
                  scale: option.disabled ? 1 : 1.02,
                  y: option.disabled ? 0 : -1,
                }}
                whileTap={{ scale: option.disabled ? 1 : 0.98 }}
                className={`
                  relative flex items-center gap-2 px-3 py-2 rounded-md
                  border transition-all duration-250 group
                  ${
                    option.disabled
                      ? "bg-gray-900/30 border-gray-700/50 text-gray-500 cursor-not-allowed"
                      : `bg-black/60 border-green-500/30 text-white
                       hover:bg-black/80 hover:border-green-400/60
                       hover:shadow-lg hover:shadow-green-500/10`
                  }
                `}
              >
                {/* Subtle glow effect */}
                {!option.disabled && (
                  <div
                    className="absolute inset-0 rounded-md bg-gradient-to-r 
                                  from-green-500/8 via-green-400/5 to-transparent 
                                  opacity-0 group-hover:opacity-100 transition-all duration-250"
                  />
                )}

                {/* Icon container - smaller */}
                {option.icon && (
                  <div
                    className={`
                    relative flex items-center justify-center w-5 h-5 rounded
                    transition-all duration-250
                    ${
                      option.disabled
                        ? "bg-gray-800/50 text-gray-600"
                        : `bg-green-500/15 text-green-400 border border-green-500/20
                         group-hover:bg-green-400/20 group-hover:text-green-300
                         group-hover:border-green-400/30 group-hover:shadow-sm
                         group-hover:shadow-green-500/20`
                    }
                  `}
                  >
                    <i className={`fas fa-${option.icon} text-xs`} />
                  </div>
                )}

                {/* Label - smaller text */}
                <div className="flex-1 relative z-10">
                  <span
                    className={`
                    text-xs font-medium tracking-normal
                    transition-all duration-250
                    ${
                      option.disabled
                        ? "text-gray-500"
                        : `text-gray-100 group-hover:text-white`
                    }
                  `}
                  >
                    {option.label}
                  </span>
                </div>

                {/* Arrow indicator - smaller */}
                {!option.disabled && (
                  <div
                    className="flex items-center justify-center w-4 h-4 
                                  text-green-500/60 group-hover:text-green-400 
                                  group-hover:translate-x-1 transition-all duration-250"
                  >
                    <i className="fas fa-chevron-right text-xs" />
                  </div>
                )}
              </motion.button>
            );
          })}
        </AnimatePresence>
      </motion.div>
    </div>
    );
  }
);

TargetOptions.displayName = "TargetOptions";

export default TargetOptions;
