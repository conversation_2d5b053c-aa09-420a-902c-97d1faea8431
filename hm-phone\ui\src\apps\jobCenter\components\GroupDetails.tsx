import React, { useState } from 'react';
import { Job, JobGroup } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';

interface GroupDetailsProps {
  group: JobGroup;
  job: Job;
  onBack: () => void;
  onLeaveGroup: (groupId: number, memberId?: string) => void;
  onStartJob: (groupId: number) => void;
  onCompleteJob: (groupId: number, success: boolean) => void;
  onUpdateSettings: (groupId: number, updates: Partial<JobGroup>) => void;
}

const GroupDetails: React.FC<GroupDetailsProps> = ({
  group,
  job,
  onBack,
  onLeaveGroup,
  onStartJob,
  onCompleteJob
}) => {
  const { userProfile } = usePhoneStore();
  const [showSettings, setShowSettings] = useState(false);
  const [editedName, setEditedName] = useState(group.name);
  const [editedPassword, setEditedPassword] = useState(group.password || '');

  const [isPrivate, setIsPrivate] = useState(group.isPrivate);

  const isLeader = group.leader.id === userProfile.stateid;
  const canStart =
    isLeader &&
    group.status === 'RECRUITING' &&
    (!job.minPlayers || group.members.length >= job.minPlayers);

  const canComplete = isLeader && group.status === 'IN_PROGRESS';

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (showSettings) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="flex flex-col h-full"
      >
        {/* Header */}
        <div className="p-4 border-b border-white/10 flex items-center">
          <button
            onClick={() => setShowSettings(false)}
            className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
          >
            <i className="fas fa-arrow-left"></i>
          </button>
          <h2 className="text-white font-medium ml-2">Group Settings</h2>
        </div>

        {/* Settings form */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-white/70 text-sm mb-1">Group Name</label>
              <input
                type="text"
                value={editedName}
                onChange={e => setEditedName(e.target.value)}
                className="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:outline-none focus:border-teal-500"
              />
            </div>

            <div className="flex items-center">
              <label className="flex items-center text-white/70 text-sm cursor-pointer">
                <input
                  type="checkbox"
                  checked={isPrivate}
                  onChange={e => setIsPrivate(e.target.checked)}
                  className="mr-2 h-4 w-4 rounded border-white/20 text-teal-500 focus:ring-teal-500"
                />
                Private Group (Password Protected)
              </label>
            </div>

            {isPrivate && (
              <div>
                <label className="block text-white/70 text-sm mb-1">Password</label>
                <input
                  type="password"
                  value={editedPassword}
                  onChange={e => setEditedPassword(e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:outline-none focus:border-teal-500"
                />
              </div>
            )}
          </div>
        </div>

        {/* Action buttons */}
        <div className="p-4 border-t border-white/10 flex gap-2">
          <button
            onClick={() => setShowSettings(false)}
            className="flex-1 py-3 rounded-lg bg-white/10 text-white/70 font-medium hover:bg-white/20 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {}}
            className="flex-1 py-3 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors"
          >
            Save Changes
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex items-center">
        <button
          onClick={onBack}
          className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <h2 className="text-white font-medium ml-2">{group.name}</h2>

        {isLeader && (
          <button
            onClick={() => setShowSettings(true)}
            className="ml-auto w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
          >
            <i className="fas fa-cog"></i>
          </button>
        )}
      </div>

      {/* Group details */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-white font-medium text-lg">{job.title}</h3>
              <p className="text-white/70 text-sm">{job.company}</p>
            </div>

            <span
              className={`text-xs px-2 py-1 rounded-full ${
                group.status === 'RECRUITING'
                  ? 'bg-blue-500/20 text-blue-400'
                  : group.status === 'IN_PROGRESS'
                  ? 'bg-yellow-500/20 text-yellow-400'
                  : group.status === 'COMPLETED'
                  ? 'bg-green-500/20 text-green-400'
                  : 'bg-red-500/20 text-red-400'
              }`}
            >
              {group.status.replace('_', ' ')}
            </span>
          </div>

          <div className="mt-4 grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-user text-white/50"></i>
              <span>Leader: {group.leader.name}</span>
            </div>

            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-users text-white/50"></i>
              <span>
                {group.members.length}/{job.maxPlayers || '∞'} members
              </span>
            </div>

            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-calendar-alt text-white/50"></i>
              <span>Created: {formatDate(group.createdAt)}</span>
            </div>

            {group.startedAt && (
              <div className="flex items-center gap-2 text-white/70">
                <i className="fas fa-play text-white/50"></i>
                <span>Started: {formatTime(group.startedAt)}</span>
              </div>
            )}

            {group.completedAt && (
              <div className="flex items-center gap-2 text-white/70">
                <i className="fas fa-flag-checkered text-white/50"></i>
                <span>Completed: {formatTime(group.completedAt)}</span>
              </div>
            )}

            {group.isPrivate && (
              <div className="flex items-center gap-2 text-white/70">
                <i className="fas fa-lock text-white/50"></i>
                <span>Private Group</span>
              </div>
            )}
          </div>
          <div className="mt-5">
            <h3 className="text-white font-medium mb-2">Members</h3>
            <div className="space-y-2">
              {group.members.map(member => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-2 bg-white/5 rounded-lg"
                >
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                      <i className="fas fa-user text-white/70"></i>
                    </div>
                    <div>
                      <p className="text-white text-sm">{member.name}</p>
                      {member.role && <p className="text-white/50 text-xs">{member.role}</p>}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {member.id === group.leader.id ? (
                      <span className="text-xs px-2 py-0.5 bg-yellow-500/20 text-yellow-400 rounded-full">
                        Leader
                      </span>
                    ) : (
                      isLeader && (
                        <button
                          onClick={e => {
                            e.stopPropagation();
                            onLeaveGroup(group.id, member.id);
                          }}
                          className="text-xs px-2 py-0.5 bg-red-500/20 text-red-400 rounded-full hover:bg-red-500/30 transition-colors"
                        >
                          <i className="fas fa-times mr-1"></i>
                          Kick
                        </button>
                      )
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="p-4 border-t border-white/10">
        {canStart && (
          <button
            onClick={() => onStartJob(group.id)}
            className="w-full py-3 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors mb-2"
          >
            <i className="fas fa-play mr-2"></i>
            Start Job
          </button>
        )}

        {canComplete && (
          <div className="flex gap-2 mb-2">
            <button
              onClick={() => onCompleteJob(group.id, true)}
              className="flex-1 py-3 rounded-lg bg-green-500 text-white font-medium hover:bg-green-600 transition-colors"
            >
              <i className="fas fa-check mr-2"></i>
              Complete
            </button>
            <button
              onClick={() => onCompleteJob(group.id, false)}
              className="flex-1 py-3 rounded-lg bg-red-500 text-white font-medium hover:bg-red-600 transition-colors"
            >
              <i className="fas fa-times mr-2"></i>
              Failed
            </button>
          </div>
        )}

        <button
          onClick={() => onLeaveGroup(group.id)}
          className="w-full py-3 rounded-lg bg-red-500/20 text-red-400 font-medium hover:bg-red-500/30 transition-colors"
        >
          {isLeader ? (
            <>
              <i className="fas fa-trash-alt mr-2"></i>
              Disband Group
            </>
          ) : (
            <>
              <i className="fas fa-sign-out-alt mr-2"></i>
              Leave Group
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default GroupDetails;
