/**
 * Messages App - Server Side
 *
 * This file handles server-side functionality for the Messages app.
 * Provides complete conversation and message management including:
 * - Conversation creation, deletion, and updates
 * - Message sending and retrieval with pagination
 * - Group chat management (add/remove members)
 * - Direct integration with hm-core for player/character data
 */

import { BaseMessage, Conversation, ConversationMember, Message } from '@shared/types';

// Type definitions for internal database use
interface DatabaseConversation {
    id: number;
    name: string | null;
    participants: string; // JSON string array of phone numbers
    type: 'direct' | 'group';
    player_settings: string; // JSON string of player-specific settings
    created_at: string;
    updated_at: string;
    latest_message?: string; // JSON string from JOIN query
}

interface DatabaseMessage {
    id: number;
    conversation_id: number;
    sender: string;
    message: string;
    type: 'text' | 'image' | 'location' | 'contact';
    metadata: string | null;
    timestamp: string;    is_deleted: number; // 0 or 1
}

/**
 * Initialize the messages app
 */
export function initializeMessagesApp(): void {
    console.log('[Messages] Initializing messages app server-side');
    registerServerEvents();
    
    // Listen for player loaded event to ensure message data is available
    onNet('hm-core:playerLoaded', async (data: { characterId: number; stateid: string; characterData: any }) => {
        const source = global.source;
        console.log(`[Messages] Player ${source} character loaded (stateid: ${data.stateid})`);
        await ensurePlayerMessageData(source);
    });
}

/**
 * Ensure player has message data initialized
 */
async function ensurePlayerMessageData(source: number): Promise<void> {
    try {
        const characterData = getActiveCharacterData(source);
        if (!characterData) {
            console.log(`[Messages] No active character found for player ${source}`);
            return;
        }

        // Validate phone number exists
        const phoneNumber = getPlayerPhoneNumber(source);
        if (!phoneNumber) {
            console.warn(`[Messages] No phone number found for player ${source} (${characterData.stateid})`);
            return;
        }

        console.log(`[Messages] Message data available for player ${source} (${characterData.stateid}, phone: ${phoneNumber})`);
    } catch (error) {
        console.error(`[Messages] Error ensuring player message data:`, error);
    }
}

/**
 * Get active character data using hm-core exports
 */
function getActiveCharacterData(source: number): any {
    try {
        // Use hm-core export to get active character
        const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        return activeCharacter;
    } catch (error) {
        console.error(`[Messages] Error getting active character for player ${source}:`, error);
        return null;
    }
}

/**
 * Get player phone number from character data
 */
function getPlayerPhoneNumber(source: number): string | null {
    try {
        const characterData = getActiveCharacterData(source);
        if (!characterData) {
            return null;
        }

        // Check both possible phone number fields based on HM Core structure
        const phoneNumber = characterData.phoneNumber || characterData.phone_number || characterData.phone;
        
        if (!phoneNumber) {
            console.warn(`[Messages] No phone number field found in character data for player ${source}`);
            return null;
        }

        return phoneNumber;
    } catch (error) {
        console.error(`[Messages] Error getting player phone number:`, error);
        return null;
    }
}

/**
 * Get player stateid from character data
 */
function getPlayerStateid(source: number): string | null {
    try {
        const characterData = getActiveCharacterData(source);
        return characterData?.stateid || null;
    } catch (error) {
        console.error(`[Messages] Error getting player stateid:`, error);
        return null;
    }
}

/**
 * Register server events for the messages app
 */
function registerServerEvents(): void {
    // Get conversations with pagination
    onNet('hm-phone:getConversations', async (data: { limit?: number; offset?: number }) => {
        const source = global.source;
        console.log(`[Messages] Received getConversations from player ${source}:`, data);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No active character found');
                return;
            }

            const phoneNumber = getPlayerPhoneNumber(source);
            if (!phoneNumber) {
                console.error(`[Messages] No phone number found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No phone number found');
                return;
            }

            const limit = data?.limit || 20;
            const offset = data?.offset || 0;

            const conversations = await getConversations(characterData.stateid, phoneNumber, limit, offset);
            emitNet('hm-phone:conversations', source, conversations);
            console.log(`[Messages] Sent ${conversations.length} conversations to player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error getting conversations:`, error);
            emitNet('hm-phone:messagesError', source, 'Error getting conversations');
        }
    });

    // Send a message
    onNet('hm-phone:sendMessage', async (messageData: any) => {
        const source = global.source;
        console.log(`[Messages] Received sendMessage from player ${source}:`, messageData);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No active character found');
                return;
            }

            const phoneNumber = getPlayerPhoneNumber(source);
            if (!phoneNumber) {
                console.error(`[Messages] No phone number found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No phone number found');
                return;
            }

            // Create properly formatted message
            const message: BaseMessage = {
                id: 0, // Will be set by database
                conversation_id: messageData.conversationId,
                sender: phoneNumber,
                message: messageData.content || messageData.message || '',
                type: messageData.type || messageData.messageType || 'text',
                metadata: messageData.metadata ? JSON.stringify(messageData.metadata) : null,
                timestamp: new Date().toISOString(),
                is_deleted: 0
            };

            await sendMessage(characterData.stateid, message);
            
            // Get updated conversation to send back to client
            const updatedConversation = await getConversation(characterData.stateid, messageData.conversationId.toString());
            if (updatedConversation) {
                emitNet('hm-phone:conversationUpdated', source, updatedConversation);
            }
            
            console.log(`[Messages] Message sent successfully by player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error sending message:`, error);
            emitNet('hm-phone:messagesError', source, 'Error sending message');
        }
    });

    // Create a conversation
    onNet('hm-phone:createConversation', async (conversationData: any) => {
        const source = global.source;
        console.log(`[Messages] Received createConversation from player ${source}:`, conversationData);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No active character found');
                return;
            }

            const phoneNumber = getPlayerPhoneNumber(source);
            if (!phoneNumber) {
                console.error(`[Messages] No phone number found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No phone number found');
                return;
            }

            const participants = conversationData.members || conversationData.participants || [];
            const isGroup = conversationData.type === 'group' || participants.length > 2;
            
            const newConversation = await createConversation(
                phoneNumber,
                participants,
                conversationData.name || '',
                isGroup
            );

            emitNet('hm-phone:conversationCreated', source, newConversation);
            console.log(`[Messages] Conversation created successfully by player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error creating conversation:`, error);
            emitNet('hm-phone:messagesError', source, 'Error creating conversation');
        }
    });

    // Delete a conversation
    onNet('hm-phone:deleteConversation', async (conversationId: number) => {
        const source = global.source;
        console.log(`[Messages] Received deleteConversation from player ${source}: ${conversationId}`);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No active character found');
                return;
            }

            await deleteConversation(characterData.stateid, conversationId.toString());
            emitNet('hm-phone:conversationDeleted', source, conversationId);
            console.log(`[Messages] Conversation deleted successfully by player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error deleting conversation:`, error);
            emitNet('hm-phone:messagesError', source, 'Error deleting conversation');
        }
    });

    // Get messages with pagination
    onNet('hm-phone:getMessages', async (data: { conversationId: number; limit?: number; offset?: number }) => {
        const source = global.source;
        console.log(`[Messages] Received getMessages from player ${source}:`, data);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                emitNet('hm-phone:messagesError', source, 'No active character found');
                return;
            }

            const limit = data?.limit || 20;
            const offset = data?.offset || 0;

            const messages = await getMessages(characterData.stateid, data.conversationId.toString(), limit, offset);
            emitNet('hm-phone:messages', source, { conversationId: data.conversationId, messages });
            console.log(`[Messages] Sent ${messages.length} messages to player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error getting messages:`, error);
            emitNet('hm-phone:messagesError', source, 'Error getting messages');
        }
    });

    // Mark conversation as read
    onNet('hm-phone:markConversationAsRead', async (conversationId: number) => {
        const source = global.source;
        console.log(`[Messages] Received markConversationAsRead from player ${source}: ${conversationId}`);
        
        try {
            const characterData = getActiveCharacterData(source);
            if (!characterData) {
                console.error(`[Messages] No active character found for player ${source}`);
                return;
            }

            const phoneNumber = getPlayerPhoneNumber(source);
            if (!phoneNumber) {
                console.error(`[Messages] No phone number found for player ${source}`);
                return;
            }

            await markConversationAsRead(phoneNumber, conversationId.toString());
            console.log(`[Messages] Conversation marked as read by player ${source}`);
        } catch (error) {
            console.error(`[Messages] Error marking conversation as read:`, error);
        }
    });
}

/**
 * Get conversations for a player with pagination
 */
async function getConversations(
    stateid: string,
    phoneNumber: string,
    limit = 20,
    offset = 0
): Promise<Conversation[]> {
    try {
        console.log(
            `[Messages] Getting conversations for stateid: ${stateid}, phone: ${phoneNumber}, limit: ${limit}, offset: ${offset}`
        );

        const sql = `
            SELECT c.*,
                (SELECT JSON_OBJECT(
                    'id', m.id,
                    'conversation_id', m.conversation_id,
                    'sender', m.sender,
                    'message', m.message,
                    'type', m.type,
                    'metadata', m.metadata,
                    'timestamp', m.timestamp,
                    'is_deleted', m.is_deleted
                )
                FROM phone_messages m
                WHERE m.conversation_id = c.id AND m.is_deleted = 0
                ORDER BY m.timestamp DESC
                LIMIT 1) as latest_message
            FROM phone_conversations c
            WHERE JSON_CONTAINS(c.participants, ?)
            ORDER BY c.updated_at DESC
            LIMIT ? OFFSET ?
        `;

        const params = [JSON.stringify(phoneNumber), limit, offset];
        console.log(`[Messages] SQL query:`, sql);
        console.log(`[Messages] SQL params:`, params);

        const conversations = await globalThis.exports.oxmysql.query_async(sql, params);

        if (!conversations || !Array.isArray(conversations)) {
            console.log(`[Messages] No conversations found or invalid result`);
            return [];
        }

        if (conversations.length === 0) {
            console.log(`[Messages] No conversations found for phone ${phoneNumber}`);
            return [];
        }

        // Format the conversations for UI
        return conversations.map((conv: DatabaseConversation) => formatConversationForUI(conv, phoneNumber));
    } catch (error) {
        console.error(`[Messages] Error getting conversations from database:`, error);
        return [];
    }
}

/**
 * Format database conversation for UI
 */
function formatConversationForUI(dbConversation: DatabaseConversation, currentPhoneNumber: string): Conversation {
    // Parse participants
    let participants: string[] = [];
    try {
        participants = JSON.parse(dbConversation.participants);
    } catch (error) {
        console.error(`[Messages] Error parsing participants:`, error);
        participants = [];
    }

    // Parse player settings
    let playerSettings: Record<string, ConversationMember> = {};
    try {
        playerSettings = JSON.parse(dbConversation.player_settings);
    } catch (error) {
        console.error(`[Messages] Error parsing player settings:`, error);
        playerSettings = {};
    }

    // Create members object from participants
    const members: Record<string, ConversationMember> = {};
    participants.forEach(phone => {
        members[phone] = playerSettings[phone] || {
            display_name: phone,
            unread_count: 0,
            is_muted: false,
            is_pinned: false,
            is_admin: false,
            joined_at: dbConversation.created_at,
            left_at: null
        };
    });

    // Parse latest message
    let latestMessage: Message | undefined;
    if (dbConversation.latest_message) {
        try {
            const msgData = JSON.parse(dbConversation.latest_message);
            latestMessage = {
                ...msgData,
                metadata: msgData.metadata ? JSON.parse(msgData.metadata) : null
            } as Message;
        } catch (error) {
            console.error(`[Messages] Error parsing latest message:`, error);
        }
    }

    return {
        id: dbConversation.id,
        name: dbConversation.name,
        type: dbConversation.type,
        participants,
        members,
        player_settings: playerSettings,
        created_at: dbConversation.created_at,
        updated_at: dbConversation.updated_at,
        latest_message: latestMessage,
        messages: [],
        avatar: ''
    };
}

/**
 * Create a new conversation
 */
async function createConversation(
    creatorPhone: string,
    participants: string[],
    name = '',
    isGroup = false
): Promise<Conversation> {
    try {
        console.log(`[Messages] Creating conversation for ${creatorPhone} with participants:`, participants);

        // Ensure creator is in participants
        if (!participants.includes(creatorPhone)) {
            participants.push(creatorPhone);
        }

        // Create player settings for all participants
        const playerSettings: Record<string, ConversationMember> = {};
        participants.forEach(phone => {
            playerSettings[phone] = {
                display_name: name || '',
                unread_count: phone === creatorPhone ? 0 : 1, // Creator doesn't have unread, others do
                is_muted: false,
                is_pinned: false,
                is_admin: phone === creatorPhone, // Creator is admin for group chats
                joined_at: new Date().toISOString(),
                left_at: null
            };
        });

        const type = isGroup ? 'group' : 'direct';

        // Insert into database
        const result = await globalThis.exports.oxmysql.insert_async(
            'INSERT INTO phone_conversations (name, participants, type, player_settings) VALUES (?, ?, ?, ?)',
            [name, JSON.stringify(participants), type, JSON.stringify(playerSettings)]
        );

        const conversationId = result;
        console.log(`[Messages] Conversation created with ID: ${conversationId}`);

        const now = new Date().toISOString();

        // Create members object from participants
        const members: Record<string, ConversationMember> = {};
        participants.forEach(phone => {
            members[phone] = playerSettings[phone];
        });

        return {
            id: conversationId,
            name: name,
            type: type,
            participants: participants,
            members,
            player_settings: playerSettings,
            created_at: now,
            updated_at: now,
            messages: [],
            avatar: ''
        };
    } catch (error) {
        console.error(`[Messages] Error creating conversation:`, error);
        throw error;
    }
}

/**
 * Delete a conversation
 */
async function deleteConversation(stateid: string, conversationId: string): Promise<void> {
    try {
        // For now, we'll do a hard delete. In production, you might want to do a soft delete
        await globalThis.exports.oxmysql.query_async(
            'DELETE FROM phone_conversations WHERE id = ?',
            [conversationId]
        );
        
        // Also delete associated messages
        await globalThis.exports.oxmysql.query_async(
            'DELETE FROM phone_messages WHERE conversation_id = ?',
            [conversationId]
        );
        
        console.log(`[Messages] Conversation ${conversationId} deleted successfully`);
    } catch (error) {
        console.error(`[Messages] Error deleting conversation:`, error);
        throw error;
    }
}

/**
 * Get a conversation by ID
 */
async function getConversation(stateid: string, conversationId: string): Promise<Conversation | null> {
    try {
        const result = await globalThis.exports.oxmysql.query_async(
            'SELECT * FROM phone_conversations WHERE id = ?',
            [conversationId]
        );

        if (!result || !Array.isArray(result) || result.length === 0) {
            return null;
        }

        const dbConversation = result[0] as DatabaseConversation;
        
        // We need to know which phone number to format for - let's get it from character data
        const characterData = await getActiveCharacterDataByStateid(stateid);
        const phoneNumber = characterData?.phoneNumber || characterData?.phone || '';
        
        return formatConversationForUI(dbConversation, phoneNumber);
    } catch (error) {
        console.error(`[Messages] Error getting conversation:`, error);
        return null;
    }
}

/**
 * Get character data by stateid (helper function for database operations)
 */
async function getActiveCharacterDataByStateid(stateid: string): Promise<any> {
    try {
        // Query the characters table to get character data by stateid
        const result = await globalThis.exports.oxmysql.query_async(
            'SELECT * FROM characters WHERE stateid = ? LIMIT 1',
            [stateid]
        );

        if (!result || !Array.isArray(result) || result.length === 0) {
            console.warn(`[Messages] No character found for stateid: ${stateid}`);
            return null;
        }

        return result[0];
    } catch (error) {
        console.error(`[Messages] Error getting character data by stateid:`, error);
        return null;
    }
}

/**
 * Send a message
 */
async function sendMessage(stateid: string, message: BaseMessage): Promise<void> {
    try {
        console.log(`[Messages] Sending message from ${stateid}:`, message);

        const result = await globalThis.exports.oxmysql.insert_async(
            'INSERT INTO phone_messages (conversation_id, sender, message, type, metadata) VALUES (?, ?, ?, ?, ?)',
            [
                message.conversation_id,
                message.sender,
                message.message,
                message.type,
                message.metadata
            ]
        );

        // Update conversation's updated_at timestamp
        await globalThis.exports.oxmysql.query_async(
            'UPDATE phone_conversations SET updated_at = NOW() WHERE id = ?',
            [message.conversation_id]
        );

        console.log(`[Messages] Message sent successfully with ID: ${result}`);
    } catch (error) {
        console.error(`[Messages] Error sending message:`, error);
        throw error;
    }
}

/**
 * Get messages for a conversation with pagination
 */
async function getMessages(
    stateid: string,
    conversationId: string,
    limit = 20,
    offset = 0
): Promise<BaseMessage[]> {
    try {
        console.log(
            `[Messages] Getting messages for conversation: ${conversationId}, limit: ${limit}, offset: ${offset}`
        );

        const messages = await globalThis.exports.oxmysql.query_async(
            'SELECT * FROM phone_messages WHERE conversation_id = ? AND is_deleted = 0 ORDER BY timestamp DESC LIMIT ? OFFSET ?',
            [conversationId, limit, offset]
        );

        if (!messages || !Array.isArray(messages)) {
            console.log(`[Messages] No messages found or invalid result`);
            return [];
        }

        // Format messages for UI
        return messages.map((msg: DatabaseMessage) => ({
            id: msg.id,
            conversation_id: msg.conversation_id,
            sender: msg.sender,
            message: msg.message,
            type: msg.type,
            metadata: msg.metadata,
            timestamp: msg.timestamp,
            is_deleted: msg.is_deleted
        }));
    } catch (error) {
        console.error(`[Messages] Error getting messages from database:`, error);
        return [];
    }
}

/**
 * Mark a conversation as read
 */
async function markConversationAsRead(phoneNumber: string, conversationId: string): Promise<void> {
    try {
        console.log(`[Messages] Marking conversation ${conversationId} as read for ${phoneNumber}`);

        // Get current conversation
        const result = await globalThis.exports.oxmysql.query_async(
            'SELECT player_settings FROM phone_conversations WHERE id = ?',
            [conversationId]
        );

        if (!result || !Array.isArray(result) || result.length === 0) {
            console.error(`[Messages] Conversation ${conversationId} not found`);
            return;
        }

        // Parse and update player settings
        let playerSettings: Record<string, ConversationMember> = {};
        try {
            playerSettings = JSON.parse(result[0].player_settings);
        } catch (error) {
            console.error(`[Messages] Error parsing player settings:`, error);
            return;
        }

        // Update unread count for this player
        if (playerSettings[phoneNumber]) {
            playerSettings[phoneNumber].unread_count = 0;
        }

        // Save back to database
        await globalThis.exports.oxmysql.query_async(
            'UPDATE phone_conversations SET player_settings = ? WHERE id = ?',
            [JSON.stringify(playerSettings), conversationId]
        );

        console.log(`[Messages] Conversation marked as read successfully`);
    } catch (error) {
        console.error(`[Messages] Error marking conversation as read:`, error);
        throw error;
    }
}
