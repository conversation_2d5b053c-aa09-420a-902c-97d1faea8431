import { create } from 'zustand';
import { sendNuiMessage, NuiActions } from '../utils/nui';

// Type definitions for target information
export interface TargetInfo {
  entity?: number;
  modelHash?: number;
  zone?: any;
  position: { x: number; y: number; z: number };
  options: any[];
  distance: number;
  type?: "entity" | "zone" | "model";
  entityType?: "player" | "vehicle" | "ped" | "object"; // Add entity type for admin actions
}

// InfoPanel store state interface
interface InfoPanelStoreState {
  // State
  isVisible: boolean;
  targetInfo: TargetInfo | null;
  isScanning: boolean;
  scanTarget: TargetInfo | null;
  
  // Actions
  showPanel: (target: TargetInfo) => void;
  hidePanel: () => void;
  togglePanel: (target?: TargetInfo) => void;
  updateTarget: (target: TargetInfo) => void;
  
  // Scanning actions
  startScanning: () => void;
  stopScanning: () => void;
  updateScanTarget: (target: TargetInfo | null) => void;
  clearScanTarget: () => void;
  
  // Utility actions
  copyToClipboard: (text: string) => void;
  handleButtonAction: (action: string, data?: any) => void;
}

// Create the InfoPanel store
export const useInfoPanelStore = create<InfoPanelStoreState>((set, get) => ({
  // Initial state
  isVisible: false,
  targetInfo: null,
  isScanning: false,
  scanTarget: null,

  // Actions
  showPanel: (target: TargetInfo) => {
    set({ 
      isVisible: true, 
      targetInfo: target 
    });
    console.log('[InfoPanel] Panel shown with target:', target);
  },

  hidePanel: () => {
    set({ 
      isVisible: false, 
      targetInfo: null 
    });
    console.log('[InfoPanel] Panel hidden');
  },

  togglePanel: (target?: TargetInfo) => {
    const { isVisible } = get();
    if (isVisible) {
      get().hidePanel();
    } else if (target) {
      get().showPanel(target);
    }
  },
  updateTarget: (target: TargetInfo) => {
    set({ targetInfo: target });
    console.log('[InfoPanel] Target updated:', target);
  },

  // Scanning actions
  startScanning: () => {
    set({ 
      isScanning: true, 
      scanTarget: null 
    });
    console.log('[InfoPanel] Started scanning mode');
  },

  stopScanning: () => {
    set({ 
      isScanning: false, 
      scanTarget: null 
    });
    console.log('[InfoPanel] Stopped scanning mode');
  },

  updateScanTarget: (target: TargetInfo | null) => {
    set({ scanTarget: target });
    if (target) {
      console.log('[InfoPanel] Scan target updated:', target);
    }
  },

  clearScanTarget: () => {
    set({ scanTarget: null });
    console.log('[InfoPanel] Scan target cleared');
  },

  // Utility actions
  copyToClipboard: (text: string) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text).then(() => {
        console.log('[InfoPanel] Copied to clipboard:', text);
        // You could add a toast notification here
      }).catch(err => {
        console.error('[InfoPanel] Failed to copy to clipboard:', err);
      });
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        console.log('[InfoPanel] Copied to clipboard (fallback):', text);
      } catch (err) {
        console.error('[InfoPanel] Failed to copy to clipboard (fallback):', err);
      }
      document.body.removeChild(textArea);
    }
  },

  handleButtonAction: (action: string, data?: any) => {
    console.log('[InfoPanel] Button action:', action, data);
    
    switch (action) {
      case 'close':
        get().hidePanel();
        break;
        
      case 'copyId':
      case 'copyHash':
      case 'copyCoords':
        if (data) {
          get().copyToClipboard(data.toString());
        }
        break;
          case 'teleport':
        if (data && typeof data === 'object' && 'x' in data && 'y' in data && 'z' in data) {
          // Send teleport command to client
          console.log('[InfoPanel] Teleporting to coordinates:', data);
          sendNuiMessage(NuiActions.TELEPORT_TO_COORDS, data);
        }
        break;
        
      case 'debugInfo':
        if (data) {
          console.log('[InfoPanel] Debug info for target:', data);
          // Could open a debug modal or send to console
          alert(`Debug Info:\n${JSON.stringify(data, null, 2)}`);
        }
        break;
          case 'freeze':
        if (data) {
          console.log('[InfoPanel] Freezing entity:', data);
          sendNuiMessage(NuiActions.FREEZE_ENTITY, { entity: data });
        }
        break;
        
      case 'healPlayer':
        if (data && data.targetId && data.health) {
          console.log('[InfoPanel] Healing player:', data);
          sendNuiMessage(NuiActions.HEAL_PLAYER, data);
        }
        break;
        
      case 'revivePlayer':
        if (data && data.entity) {
          console.log('[InfoPanel] Reviving player:', data.entity);
          // TODO: Add revive player action
        }
        break;
        
      case 'spectatePlayer':
        if (data && data.entity) {
          console.log('[InfoPanel] Spectating player:', data.entity);
          // TODO: Add spectate player action
        }
        break;
        
      case 'repairVehicle':
        if (data && data.entity) {
          console.log('[InfoPanel] Repairing vehicle:', data.entity);
          // TODO: Add repair vehicle action
        }
        break;
        
      case 'deleteVehicle':
        if (data && data.entity) {
          console.log('[InfoPanel] Deleting vehicle:', data.entity);
          // TODO: Add delete vehicle action
        }
        break;
        
      case 'flipVehicle':
        if (data && data.entity) {
          console.log('[InfoPanel] Flipping vehicle:', data.entity);
          // TODO: Add flip vehicle action
        }
        break;
        
      default:
        console.warn('[InfoPanel] Unknown action:', action);
    }
  }
}));

// Export a hook for using the InfoPanel store
export const useInfoPanel = () => {
  return useInfoPanelStore();
};
