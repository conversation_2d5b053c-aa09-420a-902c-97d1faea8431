import { create } from 'zustand';
import { Service } from '../types/servicesTypes';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { useDialerStore } from '../../contacts/stores/dialerStore';
import { useMessagesStore } from '../../messages/stores/messagesStore';
import { useNavigationStore } from '../../../navigation/navigationStore';

interface ServicesState {
  // State
  services: Service[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  favorites: number[];

  // Actions
  actions: {
    getServices: () => Promise<void>;
    callService: (service: Service) => void;
    messageService: (service: Service) => void;
    toggleFavorite: (serviceId: number) => void;
  };

  // Handlers
  handlers: {
    onSetServices: (services: Service[]) => void;
  };

  // UI
  ui: {
    searchServices: (query: string) => Service[];
    setSearchTerm: (term: string) => void;
    getServicesByCategory: (category: string) => Service[];
    getAllCategories: () => string[];
  };
}

export const useServicesStore = create<ServicesState>((set, get) => ({
  // State
  services: [],
  loading: false,
  error: null,
  searchTerm: '',
  favorites: [],

  // Actions
  actions: {
    getServices: async () => {
      console.log('[ServicesStore] getServices called');
      const state = get();
      if (state.loading || state.services.length > 0) {
        // Skip if already loading or if we already have data (config is static)
        console.log('[ServicesStore] Already loaded or loading, skipping request');
        return;
      }
      set({ loading: true });
      try {
        await clientRequests.send(
          'services',
          'getServices',
          {},
          [],
          state.handlers.onSetServices as (data: unknown) => void
        );
      } catch (error) {
        console.error('[ServicesStore] Error loading services:', error);
        set({ loading: false, error: 'Failed to load services. Please try again.' });
      } finally {
        setTimeout(() => {
          set({ loading: false });
        }, 500);
      }
    },

    callService: (service: Service) => {
      console.log('[ServicesStore] Calling service:', service.name);
      // Use the dialer store to make the call
      const dialerStore = useDialerStore.getState();
      dialerStore.actions.makeCall(service.phone, service.name);
    },

    messageService: async (service: Service) => {
      console.log('[ServicesStore] Messaging service:', service.name);
      // Use the messages store to create a new conversation
      const messagesStore = useMessagesStore.getState();

      // Check if there's an existing conversation with this service
      const existingConversation = messagesStore.conversations.find(
        conv => conv.type !== 'group' && Object.keys(conv.members).includes(service.phone)
      );

      if (existingConversation) {
        // Navigate to the existing conversation
        // Use the navigation store to navigate to the conversation
        const { openAppView } = useNavigationStore.getState();
        openAppView('messages', 'conversation', { id: existingConversation.id });
      } else {
        // Create a new conversation with this service
        // First create a contact object for the service
        const serviceContact = {
          id: Date.now(),
          owner_number: '',  // Will be set by the server
          identifier: service.phone,
          stateid: service.phone,  // Using phone as stateid for simplicity
          number: service.phone,
          name: service.name,
          favorite: 0,
          avatar: service.icon || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Create a default initial message
        const initialMessage = {
          id: 0,
          conversation_id: 0,
          sender: '',  // Will be set by the server
          message: 'Hello, I would like to request your services.',
          type: 'text' as const,
          metadata: null,
          timestamp: new Date().toISOString(),
          is_deleted: 0,
          content: 'Hello, I would like to request your services.'
        };

        // Create the conversation with the service contact
        const conversationId = await messagesStore.actions.createConversation([serviceContact], initialMessage);

        if (conversationId !== null) {
          // Navigate to the new conversation
          const { openAppView } = useNavigationStore.getState();
          openAppView('messages', 'conversation', { id: conversationId });
        }
      }
    },

    toggleFavorite: (serviceId: number) => {
      console.log('[ServicesStore] Toggling favorite for service:', serviceId);
      set(state => {
        const isFavorite = state.favorites.includes(serviceId);
        return {
          favorites: isFavorite
            ? state.favorites.filter(id => id !== serviceId)
            : [...state.favorites, serviceId]
        };
      });
    }
  },

  // Handlers
  handlers: {
    onSetServices: (services: Service[]) => {
      console.log('[ServicesStore] Setting services:', services ? services.length : 'null');
      set({ services, loading: false });
    }
  },

  // UI
  ui: {
    searchServices: (query: string) => {
      const state = get();
      const searchTerm = query.toLowerCase();
      return state.services.filter(
        service =>
          service.name.toLowerCase().includes(searchTerm) ||
          service.description.toLowerCase().includes(searchTerm) ||
          service.category.toLowerCase().includes(searchTerm) ||
          service.phone.toLowerCase().includes(searchTerm)
      );
    },
    setSearchTerm: (term: string) => set({ searchTerm: term }),
    getServicesByCategory: (category: string) => {
      const state = get();
      return state.services.filter(
        service => service.category.toLowerCase() === category.toLowerCase()
      );
    },
    getAllCategories: () => {
      const state = get();
      const categories = state.services.map(service => service.category);
      return [...new Set(categories)]; // Remove duplicates
    }
  }
}));
