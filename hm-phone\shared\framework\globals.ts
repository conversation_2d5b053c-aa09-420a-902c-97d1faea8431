/**
 * Framework Globals
 *
 * This file declares and initializes global variables and functions for the framework integration.
 * These globals make it easy to access framework functionality from any script without imports.
 */

import { IFramework, FrameworkPlayer } from './types';
import { getFramework, getFrameworkName, isFramework } from './framework';

/**
 * Declare global variables and functions
 */
declare global {
    // Framework information
    var Framework: IFramework;
    var frameworkName: string;

    // Framework type check functions
    var isHmCore: () => boolean;

    // Player data (client-side only)
    var playerData: FrameworkPlayer | null;

    // Player data getter functions
    var getPlayerData: () => FrameworkPlayer | null;
    var getServerPlayerData: (source: number) => FrameworkPlayer | null;
    var getPlayerIdentifier: () => string | null;
    var getPlayerStateId: () => string | null;
    var getPlayerPhoneNumber: () => string | null;
    var getPlayerName: () => string | null;
    var getPlayerJob: () => any | null;
    var getPlayerMoney: (type?: string) => number;

    // Framework utility functions
    var notifyPlayer: (message: string, type?: string, duration?: number) => void;
    var hasPermission: (permission: string) => boolean;
}

/**
 * Initialize framework globals
 */
export function initializeFrameworkGlobals(): void {
    // Set framework information
    global.Framework = getFramework();
    global.frameworkName = getFrameworkName();

    // Set framework type check functions
    global.isHmCore = () => isFramework('hm-core');

    // Initialize player data
    global.playerData = null;

    // Set player data getter functions
    global.getPlayerData = () => {
        // If we're on the client side, get the local player
        if (IsDuplicityVersion()) {
            return null;
        }

        const source = GetPlayerServerId(PlayerId());
        return Framework.getPlayer(source);
    };

    // Server-side function to get player data by source
    global.getServerPlayerData = (source: number) => {
        // If we're on the client side, this function shouldn't be called
        if (!IsDuplicityVersion()) {
            return null;
        }

        return Framework.getPlayer(source);
    };

    global.getPlayerIdentifier = () => {
        const player = global.getPlayerData();
        return player ? player.identifier : null;
    };

    global.getPlayerStateId = () => {
        const player = global.getPlayerData();
        return player ? player.stateid || null : null;
    };

    global.getPlayerPhoneNumber = () => {
        const player = global.getPlayerData();
        return player ? player.phoneNumber || null : null;
    };

    global.getPlayerName = () => {
        const player = global.getPlayerData();
        return player ? player.name || null : null;
    };

    global.getPlayerJob = () => {
        const player = global.getPlayerData();
        return player ? player.job || null : null;
    };

    global.getPlayerMoney = (type = 'cash') => {
        if (IsDuplicityVersion()) {
            return 0;
        }

        const source = GetPlayerServerId(PlayerId());
        return Framework.getMoney(source, type);
    };

    // Set framework utility functions
    global.notifyPlayer = (message: string, type = 'info', duration = 5000) => {
        if (IsDuplicityVersion()) {
            return;
        }

        const source = GetPlayerServerId(PlayerId());
        Framework.notify(source, message, type, duration);
    };

    global.hasPermission = (permission: string) => {
        if (IsDuplicityVersion()) {
            return false;
        }

        const source = GetPlayerServerId(PlayerId());
        return Framework.hasPermission(source, permission);
    };
}

/**
 * Update player data globals
 * This should be called periodically to keep player data up to date
 */
export function updatePlayerDataGlobals(): void {
    if (IsDuplicityVersion()) {
        return;
    }

    const source = GetPlayerServerId(PlayerId());
    global.playerData = Framework.getPlayer(source);
}

/**
 * Reset framework globals
 * This should be called when the resource stops
 */
export function resetFrameworkGlobals(): void {
    // Reset framework information
    global.Framework = null as any;
    global.frameworkName = '';

    // Reset player data
    global.playerData = null;
}
