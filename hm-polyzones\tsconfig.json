{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@shared/*": ["scripts/shared/*"], "@client/*": ["scripts/client/*"], "@server/*": ["scripts/server/*"]}, "outDir": "./build", "rootDirs": ["scripts/client", "scripts/server", "scripts/shared"], "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "lib": ["ES2020", "DOM"], "types": ["@types/node", "@citizenfx/client", "@citizenfx/server"]}, "include": ["scripts/client/**/*", "scripts/server/**/*", "scripts/shared/**/*"], "exclude": ["node_modules"]}