/**
 * Centralized mock data for browser testing
 * Import app-specific types as needed
 */

// Mock data for WiFi networks
export const wifiMockData = {
  networks: [
    {
      id: 1,
      ssid: 'Home_WiFi',
      signal: 90, // Signal strength (0-100)
      secured: true,
      connected: true
    },
    {
      id: 2,
      ssid: 'Cafe_Public',
      signal: 75,
      secured: false,
      connected: false
    },
    {
      id: 3,
      ssid: 'Office_Network',
      signal: 85,
      secured: true,
      connected: false
    },
    {
      id: 4,
      ssid: 'Library_Free_WiFi',
      signal: 60,
      secured: false,
      connected: false
    },
    {
      id: 5,
      ssid: 'Hotel_Guest',
      signal: 70,
      secured: true,
      connected: false
    },
    {
      id: 6,
      ssid: 'Airport_WiFi',
      signal: 50,
      secured: false,
      connected: false
    },
    {
      id: 7,
      ssid: 'Neighbor_5G',
      signal: 40,
      secured: true,
      connected: false
    }
  ]
};

// Mock data for contacts
export const contactsMockData = {
  contacts: [
    // Original contacts
    {
      id: 1,
      identifier: 1,
      stateid: 1,
      number: '555-1234',
      name: '<PERSON>',
      favorite: 1,
      avatar: 'https://i.pravatar.cc/150?img=1',
      created_at: '2023-01-01 10:00:00',
      updated_at: '2023-01-01 10:00:00'
    },
    {
      id: 2,
      identifier: 2,
      stateid: 2,
      number: '555-5678',
      name: 'Jane Smith',
      favorite: 0,
      avatar: 'https://i.pravatar.cc/150?img=2',
      created_at: '2023-01-02 10:00:00',
      updated_at: '2023-01-02 10:00:00'
    },
    {
      id: 3,
      identifier: 3,
      stateid: 3,
      number: '555-9012',
      name: 'Bob Johnson',
      favorite: 1,
      avatar: 'https://i.pravatar.cc/150?img=3',
      created_at: '2023-01-03 10:00:00',
      updated_at: '2023-01-03 10:00:00'
    },
    {
      id: 4,
      identifier: 4,
      stateid: 4,
      number: '555-4321',
      name: 'Mike Wilson',
      favorite: 0,
      avatar: 'https://i.pravatar.cc/150?img=5',
      created_at: '2023-01-04 10:00:00',
      updated_at: '2023-01-04 10:00:00'
    },

    // New contacts from conversations (only some of them)
    {
      id: 5,
      identifier: 5,
      stateid: 5,
      number: '555-6002',
      name: 'Alex Johnson',
      favorite: 1,
      avatar: 'https://i.pravatar.cc/150?img=16',
      created_at: '2025-04-15 10:00:00',
      updated_at: '2025-04-15 10:00:00'
    },
    {
      id: 6,
      identifier: 6,
      stateid: 6,
      number: '555-6004',
      name: 'David Brown',
      favorite: 0,
      avatar: 'https://i.pravatar.cc/150?img=18',
      created_at: '2025-04-20 10:00:00',
      updated_at: '2025-04-20 10:00:00'
    },
    {
      id: 7,
      identifier: 7,
      stateid: 7,
      number: '555-6006',
      name: 'Jennifer Clark',
      favorite: 1,
      avatar: 'https://i.pravatar.cc/150?img=20',
      created_at: '2025-04-25 10:00:00',
      updated_at: '2025-04-25 10:00:00'
    }
  ]
};

// Mock data uses May 3, 2025 as the reference date for "today"

// Mock data for messages
export const messagesMockData = {
  messages: [
    // Messages for conversation 108 (Lisa Anderson - Last year conversation)
    {
      id: 1008,
      conversation_id: 108,
      sender: '555-6008',
      message: 'Merry Christmas!',
      type: 'text',
      timestamp: '2024-12-25T12:00:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2024-12-25T12:00:00Z').getTime()
      }
    },
    {
      id: 2008,
      conversation_id: 108,
      sender: '555-0000',
      message: 'Merry Christmas to you too! How are you doing?',
      type: 'text',
      timestamp: '2024-12-25T12:05:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2024-12-25T12:05:00Z').getTime()
      }
    },
    {
      id: 3008,
      conversation_id: 108,
      sender: '555-6008',
      message: 'I\'m great! Enjoying the holidays with family.',
      type: 'text',
      timestamp: '2024-12-25T12:10:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2024-12-25T12:10:00Z').getTime()
      }
    },

    // Messages for conversation 107 (Robert Davis - Earlier this year)
    {
      id: 1007,
      conversation_id: 107,
      sender: '555-6007',
      message: 'Happy New Year!',
      type: 'text',
      timestamp: '2025-01-15T16:45:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-01-15T16:45:00Z').getTime()
      }
    },
    {
      id: 2007,
      conversation_id: 107,
      sender: '555-0000',
      message: 'Happy New Year to you too! Any resolutions?',
      type: 'text',
      timestamp: '2025-01-15T16:50:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-01-15T16:50:00Z').getTime()
      }
    },
    {
      id: 3007,
      conversation_id: 107,
      sender: '555-6007',
      message: 'Just trying to be more active this year!',
      type: 'text',
      timestamp: '2025-01-15T16:55:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-01-15T16:55:00Z').getTime()
      }
    },

    // Messages for conversation 106 (Jennifer Clark - Earlier this week)
    {
      id: 1006,
      conversation_id: 106,
      sender: '555-6006',
      message: 'See you this weekend!',
      type: 'text',
      timestamp: '2025-04-30T14:15:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-04-30T14:15:00Z').getTime()
      }
    },
    {
      id: 2006,
      conversation_id: 106,
      sender: '555-0000',
      message: 'Looking forward to it! What time should we meet?',
      type: 'text',
      timestamp: '2025-04-30T14:20:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-04-30T14:20:00Z').getTime()
      }
    },
    {
      id: 3006,
      conversation_id: 106,
      sender: '555-6006',
      message: 'How about 2pm at the usual spot?',
      type: 'text',
      timestamp: '2025-04-30T14:25:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-04-30T14:25:00Z').getTime()
      }
    },

    // Messages for conversation 105 (Michael Taylor - Yesterday)
    {
      id: 1005,
      conversation_id: 105,
      sender: '555-6005',
      message: 'Let\'s talk about this tomorrow',
      type: 'text',
      timestamp: '2025-05-02T18:30:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-02T18:30:00Z').getTime()
      }
    },
    {
      id: 2005,
      conversation_id: 105,
      sender: '555-0000',
      message: 'Sure, what time works for you?',
      type: 'text',
      timestamp: '2025-05-02T18:35:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-02T18:35:00Z').getTime()
      }
    },
    {
      id: 3005,
      conversation_id: 105,
      sender: '555-6005',
      message: 'Morning would be best, around 10am?',
      type: 'text',
      timestamp: '2025-05-02T18:40:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-02T18:40:00Z').getTime()
      }
    },

    // Messages for conversation 104 (David Brown - Today with contact)
    {
      id: 1004,
      conversation_id: 104,
      sender: '555-6004',
      message: 'Here\'s the contact',
      type: 'contact',
      timestamp: '2025-05-03T17:05:00Z',
      is_deleted: false,
      metadata: {
        contact: {
          id: 50,
          owner_number: '555-0000',
          contact_number: '555-9999',
          display_name: 'James Wilson',
          is_favorite: 0,
          is_blocked: 0,
          avatar: 'https://i.pravatar.cc/150?img=25',
          notes: 'Mechanic',
          created_at: '2025-05-03T17:05:00Z',
          updated_at: '2025-05-03T17:05:00Z'
        },
        timestamp: new Date('2025-05-03T17:05:00Z').getTime()
      }
    },
    {
      id: 2004,
      conversation_id: 104,
      sender: '555-0000',
      message: 'Thanks! I\'ll give him a call.',
      type: 'text',
      timestamp: '2025-05-03T17:10:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T17:10:00Z').getTime()
      }
    },
    {
      id: 3004,
      conversation_id: 104,
      sender: '555-6004',
      message: 'He\'s the best mechanic in town!',
      type: 'text',
      timestamp: '2025-05-03T17:15:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T17:15:00Z').getTime()
      }
    },

    // Messages for conversation 103 (Sarah Miller - Today with location)
    {
      id: 1003,
      conversation_id: 103,
      sender: '555-6003',
      message: 'Meet me here',
      type: 'location',
      timestamp: '2025-05-03T16:20:00Z',
      is_deleted: false,
      metadata: {
        location: {
          name: 'Downtown Cafe',
          address: '123 Main St, Los Santos',
          coordinates: {
            lat: 34.0522,
            lng: -118.2437
          }
        },
        timestamp: new Date('2025-05-03T16:20:00Z').getTime()
      }
    },
    {
      id: 2003,
      conversation_id: 103,
      sender: '555-0000',
      message: 'I know that place! I\'ll be there in 15 minutes.',
      type: 'text',
      timestamp: '2025-05-03T16:25:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T16:25:00Z').getTime()
      }
    },
    {
      id: 3003,
      conversation_id: 103,
      sender: '555-6003',
      message: 'Great! I\'ll grab us a table.',
      type: 'text',
      timestamp: '2025-05-03T16:30:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T16:30:00Z').getTime()
      }
    },

    // Messages for conversation 102 (Alex Johnson - Today with image)
    {
      id: 1002,
      conversation_id: 102,
      sender: '555-0000',
      message: 'Check out this photo!',
      type: 'image',
      timestamp: '2025-05-03T15:45:00Z',
      is_deleted: false,
      metadata: {
        mediaUrls: ['https://picsum.photos/500/500?random=10'],
        thumbnailUrls: ['https://picsum.photos/200/200?random=10'],
        hasText: true,
        timestamp: new Date('2025-05-03T15:45:00Z').getTime()
      }
    },
    {
      id: 2002,
      conversation_id: 102,
      sender: '555-6002',
      message: 'Wow, that looks amazing! Where was this taken?',
      type: 'text',
      timestamp: '2025-05-03T15:50:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T15:50:00Z').getTime()
      }
    },
    {
      id: 3002,
      conversation_id: 102,
      sender: '555-0000',
      message: 'At the new spot in Vinewood Hills. We should go there sometime!',
      type: 'text',
      timestamp: '2025-05-03T15:55:00Z',
      is_deleted: false,
      metadata: {
        timestamp: new Date('2025-05-03T15:55:00Z').getTime()
      }
    }
  ],
  conversations: [
    // Last year conversation (December 25, 2024)
    {
      id: 108,
      name: 'Lisa Anderson', // Using contact name
      type: 'direct',
      members: {
        '555-0000': {
          display_name: 'Lisa Anderson',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2024-12-25T10:00:00Z',
          left_at: null
        },
        '555-6008': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2024-12-25T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2024-12-25T10:00:00Z',
      updated_at: '2024-12-25T12:00:00Z', // December 25, 2024
      latest_message: {
        id: 1008,
        conversation_id: 108,
        sender: '555-6008',
        message: 'Merry Christmas!',
        type: 'text',
        timestamp: '2024-12-25T12:00:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2024-12-25T12:00:00Z').getTime()
        }
      }
    },

    // Earlier this year conversation (January 15, 2025)
    {
      id: 107,
      name: 'Robert Davis', // Using contact name
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: 'Robert Davis',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-01-15T10:00:00Z',
          left_at: null
        },
        '555-6007': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-01-15T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-01-15T10:00:00Z',
      updated_at: '2025-01-15T16:45:00Z', // January 15, 2025
      latest_message: {
        id: 1007,
        conversation_id: 107,
        sender: '555-6007',
        message: 'Happy New Year!',
        type: 'text',
        timestamp: '2025-01-15T16:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2025-01-15T16:45:00Z').getTime()
        }
      }
    },

    // Earlier this week conversation (April 30, 2025 - Wednesday)
    {
      id: 106,
      name: 'Jennifer Clark', // Using contact name from contacts
      type: 'direct',
      avatar: 'https://i.pravatar.cc/150?img=20', // Using contact avatar from contacts
      members: {
        '555-0000': {
          display_name: 'Jennifer Clark',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-04-30T09:00:00Z',
          left_at: null
        },
        '555-6006': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-04-30T09:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-04-30T09:00:00Z',
      updated_at: '2025-04-30T14:15:00Z', // Wednesday, 2:15 PM
      latest_message: {
        id: 1006,
        conversation_id: 106,
        sender: '555-6006',
        message: 'See you this weekend!',
        type: 'text',
        timestamp: '2025-04-30T14:15:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2025-04-30T14:15:00Z').getTime()
        }
      }
    },

    // Yesterday's conversation (May 2, 2025)
    {
      id: 105,
      name: 'Michael Taylor', // Using contact name
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: 'Michael Taylor',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-02T10:00:00Z',
          left_at: null
        },
        '555-6005': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-02T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-05-02T10:00:00Z',
      updated_at: '2025-05-02T18:30:00Z', // Yesterday, 6:30 PM
      latest_message: {
        id: 1005,
        conversation_id: 105,
        sender: '555-6005',
        message: 'Let\'s talk about this tomorrow',
        type: 'text',
        timestamp: '2025-05-02T18:30:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2025-05-02T18:30:00Z').getTime()
        }
      }
    },

    // Today's conversation with contact message (May 3, 2025)
    {
      id: 104,
      name: 'David Brown', // Using contact name from contacts
      type: 'direct',
      avatar: 'https://i.pravatar.cc/150?img=18', // Using contact avatar from contacts
      members: {
        '555-0000': {
          display_name: 'David Brown',
          unread_count: 3,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T07:00:00Z',
          left_at: null
        },
        '555-6004': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T07:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-05-03T07:00:00Z',
      updated_at: '2025-05-03T17:05:00Z', // Today, 5:05 PM
      latest_message: {
        id: 1004,
        conversation_id: 104,
        sender: '555-6004',
        message: 'Here\'s the contact',
        type: 'contact',
        timestamp: '2025-05-03T17:05:00Z',
        is_deleted: 0,
        metadata: {
          contact: {
            id: 50,
            owner_number: '555-0000',
            contact_number: '555-9999',
            display_name: 'James Wilson',
            is_favorite: 0,
            is_blocked: 0,
            avatar: 'https://i.pravatar.cc/150?img=25',
            notes: 'Mechanic',
            created_at: '2025-05-03T17:05:00Z',
            updated_at: '2025-05-03T17:05:00Z'
          },
          timestamp: new Date('2025-05-03T17:05:00Z').getTime()
        }
      }
    },

    // Today's conversation with location message (May 3, 2025)
    {
      id: 103,
      name: 'Sarah Miller', // Using contact name
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: 'Sarah Miller',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T08:00:00Z',
          left_at: null
        },
        '555-6003': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T08:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-05-03T08:00:00Z',
      updated_at: '2025-05-03T16:20:00Z', // Today, 4:20 PM
      latest_message: {
        id: 1003,
        conversation_id: 103,
        sender: '555-6003',
        message: 'Meet me here',
        type: 'location',
        timestamp: '2025-05-03T16:20:00Z',
        is_deleted: 0,
        metadata: {
          location: {
            name: 'Downtown Cafe',
            address: '123 Main St, Los Santos',
            coordinates: {
              lat: 34.0522,
              lng: -118.2437
            }
          },
          timestamp: new Date('2025-05-03T16:20:00Z').getTime()
        }
      }
    },

    // Today's conversation with image message (May 3, 2025)
    {
      id: 102,
      name: 'Alex Johnson', // Using contact name from contacts
      type: 'direct',
      avatar: 'https://i.pravatar.cc/150?img=16', // Using contact avatar from contacts
      members: {
        '555-0000': {
          display_name: 'Alex Johnson',
          unread_count: 1,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T09:00:00Z',
          left_at: null
        },
        '555-6002': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T09:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-05-03T09:00:00Z',
      updated_at: '2025-05-03T15:45:00Z', // Today, 3:45 PM
      latest_message: {
        id: 1002,
        conversation_id: 102,
        sender: '555-6002',
        message: 'Check out this photo!',
        type: 'image',
        timestamp: '2025-05-03T15:45:00Z',
        is_deleted: 0,
        metadata: {
          mediaUrls: ['https://picsum.photos/500/500?random=10'],
          thumbnailUrls: ['https://picsum.photos/200/200?random=10'],
          hasText: true,
          timestamp: new Date('2025-05-03T15:45:00Z').getTime()
        }
      }
    },

    // Today's conversation with text message (May 3, 2025)
    {
      id: 101,
      name: 'Emma Wilson', // Using contact name
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: 'Emma Wilson',
          unread_count: 2,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T10:00:00Z',
          left_at: null
        },
        '555-6001': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2025-05-03T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2025-05-03T10:00:00Z',
      updated_at: '2025-05-03T14:30:00Z', // Today, 2:30 PM
      latest_message: {
        id: 1001,
        conversation_id: 101,
        sender: '555-6001',
        message: 'Hey, are we still meeting for coffee today?',
        type: 'text',
        timestamp: '2025-05-03T14:30:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2025-05-03T14:30:00Z').getTime()
        }
      },
    },
    {
      id: 1,
      name: 'John Doe', // Using contact name from contacts
      type: 'direct',
      avatar: 'https://i.pravatar.cc/150?img=1', // Using contact avatar from contacts
      members: {
        '555-0000': {
          display_name: 'John Doe',
          unread_count: 2,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-01T10:00:00Z',
          left_at: null
        },
        '555-1234': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-01T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-01-01T10:00:00Z',
      updated_at: '2023-01-01T12:01:00Z', // Matches latest message timestamp
      latest_message: {
        id: 2,
        conversation_id: 1,
        sender: '555-0000',
        message: 'Hi! How are you doing?',
        type: 'text',
        timestamp: '2023-01-01T12:01:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: 1672574460000
        }
      },
    },
    {
      id: 2,
      name: 'Jane Smith', // Using contact name from contacts
      type: 'direct',
      avatar: 'https://i.pravatar.cc/150?img=2', // Using contact avatar from contacts
      members: {
        '555-0000': {
          display_name: 'Jane Smith',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-02T10:00:00Z',
          left_at: null
        },
        '555-5678': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-02T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-01-02T10:00:00Z',
      updated_at: '2023-01-02T15:00:00Z', // Matches latest message timestamp
      latest_message: {
        id: 3,
        conversation_id: 2,
        sender: '555-5678',
        message: 'Hey, got a minute?',
        type: 'text',
        timestamp: '2023-01-02T15:00:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: 1672671600000
        }
      },
    },
    {
      id: 3,
      name: 'Friends Group',
      type: 'group',
      avatar: null,
      members: {
        '555-0000': {
          display_name: 'Friends Group',
          unread_count: 1,
          is_muted: false,
          is_pinned: false,
          is_admin: true, // Player is admin in group chat
          joined_at: '2023-01-03T10:00:00Z',
          left_at: null
        },
        '555-1234': {
          display_name: 'Friends Group',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-03T10:00:00Z',
          left_at: null,
          avatar: 'https://i.pravatar.cc/150?img=1' // Using contact avatar from contacts
        },
        '555-5678': {
          display_name: 'Friends Group',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-03T10:00:00Z',
          left_at: null,
          avatar: 'https://i.pravatar.cc/150?img=2' // Using contact avatar from contacts
        },
        '555-9012': {
          display_name: 'Friends Group',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-03T10:00:00Z',
          left_at: null,
          avatar: 'https://i.pravatar.cc/150?img=3' // Using contact avatar from contacts
        }
      },
      created_at: '2023-01-03T10:00:00Z',
      updated_at: '2023-01-03T15:50:00Z', // Updated to match latest message timestamp
      latest_message: {
        id: 9,
        conversation_id: 3,
        sender: '555-0000',
        message: 'Let\'s meet tomorrow at 10am',
        type: 'text',
        timestamp: '2023-01-03T15:50:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: 1672764200000,
          isEdited: true,
          originalMessage: 'Let\'s meet tomorrow'
        }
      },
    },
    {
      id: 4,
      name: '555-7890', // Unsaved contact - just shows the phone number
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: '555-7890',
          unread_count: 1,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-04T10:00:00Z',
          left_at: null
        },
        '555-7890': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-04T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-01-04T10:00:00Z',
      updated_at: '2023-01-04T11:30:00Z', // Matches latest message timestamp
      latest_message: {
        id: 10,
        conversation_id: 4,
        sender: '555-7890',
        message: 'Hey, this is an unsaved contact',
        type: 'text',
        timestamp: '2023-01-04T11:30:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: 1672830600000
        }
      },
    },
    // Additional conversations for pagination testing (10 more)
    {
      id: 201,
      name: '555-8001', // Unsaved contact
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: '555-8001',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-01T10:00:00Z',
          left_at: null
        },
        '555-8001': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-01T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-01T10:00:00Z',
      updated_at: '2023-02-01T14:45:00Z',
      latest_message: {
        id: 2001,
        conversation_id: 201,
        sender: '555-8001',
        message: 'Pagination test conversation 1',
        type: 'text',
        timestamp: '2023-02-01T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-01T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 202,
      name: '555-8002',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8002',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-02T10:00:00Z',
          left_at: null
        },
        '555-8002': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-02T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-02T10:00:00Z',
      updated_at: '2023-02-02T14:45:00Z',
      latest_message: {
        id: 2002,
        conversation_id: 202,
        sender: '555-8002',
        message: 'Pagination test conversation 2',
        type: 'text',
        timestamp: '2023-02-02T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-02T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 203,
      name: '555-8003',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8003',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-03T10:00:00Z',
          left_at: null
        },
        '555-8003': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-03T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-03T10:00:00Z',
      updated_at: '2023-02-03T14:45:00Z',
      latest_message: {
        id: 2003,
        conversation_id: 203,
        sender: '555-8003',
        message: 'Pagination test conversation 3',
        type: 'text',
        timestamp: '2023-02-03T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-03T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 204,
      name: '555-8004',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8004',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-04T10:00:00Z',
          left_at: null
        },
        '555-8004': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-04T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-04T10:00:00Z',
      updated_at: '2023-02-04T14:45:00Z',
      latest_message: {
        id: 2004,
        conversation_id: 204,
        sender: '555-8004',
        message: 'Pagination test conversation 4',
        type: 'text',
        timestamp: '2023-02-04T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-04T14:45:00Z').getTime()
        }
      },

    },
    {
      id: 205,
      name: '555-8005',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8005',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-05T10:00:00Z',
          left_at: null
        },
        '555-8005': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-05T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-05T10:00:00Z',
      updated_at: '2023-02-05T14:45:00Z',
      latest_message: {
        id: 2005,
        conversation_id: 205,
        sender: '555-8005',
        message: 'Pagination test conversation 5',
        type: 'text',
        timestamp: '2023-02-05T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-05T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 206,
      name: '555-8006',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8006',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-06T10:00:00Z',
          left_at: null
        },
        '555-8006': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-06T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-06T10:00:00Z',
      updated_at: '2023-02-06T14:45:00Z',
      latest_message: {
        id: 2006,
        conversation_id: 206,
        sender: '555-8006',
        message: 'Pagination test conversation 6',
        type: 'text',
        timestamp: '2023-02-06T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-06T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 207,
      name: '555-8007',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8007',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-07T10:00:00Z',
          left_at: null
        },
        '555-8007': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-07T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-07T10:00:00Z',
      updated_at: '2023-02-07T14:45:00Z',
      latest_message: {
        id: 2007,
        conversation_id: 207,
        sender: '555-8007',
        message: 'Pagination test conversation 7',
        type: 'text',
        timestamp: '2023-02-07T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-07T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 208,
      name: '555-8008',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8008',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-08T10:00:00Z',
          left_at: null
        },
        '555-8008': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-08T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-08T10:00:00Z',
      updated_at: '2023-02-08T14:45:00Z',
      latest_message: {
        id: 2008,
        conversation_id: 208,
        sender: '555-8008',
        message: 'Pagination test conversation 8',
        type: 'text',
        timestamp: '2023-02-08T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-08T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 209,
      name: '555-8009',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8009',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-09T10:00:00Z',
          left_at: null
        },
        '555-8009': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-09T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-09T10:00:00Z',
      updated_at: '2023-02-09T14:45:00Z',
      latest_message: {
        id: 2009,
        conversation_id: 209,
        sender: '555-8009',
        message: 'Pagination test conversation 9',
        type: 'text',
        timestamp: '2023-02-09T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-09T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 210,
      name: '555-8010',
      avatar: null,
      type: 'direct',
      members: {
        '555-0000': {
          display_name: '555-8010',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-10T10:00:00Z',
          left_at: null
        },
        '555-8010': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-02-10T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-02-10T10:00:00Z',
      updated_at: '2023-02-10T14:45:00Z',
      latest_message: {
        id: 2010,
        conversation_id: 210,
        sender: '555-8010',
        message: 'Pagination test conversation 10',
        type: 'text',
        timestamp: '2023-02-10T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: new Date('2023-02-10T14:45:00Z').getTime()
        }
      },
    },
    {
      id: 5,
      name: '555-3456', // Another unsaved contact
      type: 'direct',
      avatar: null,
      members: {
        '555-0000': {
          display_name: '555-3456',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-05T10:00:00Z',
          left_at: null
        },
        '555-3456': {
          display_name: 'You',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: '2023-01-05T10:00:00Z',
          left_at: null
        }
      },
      created_at: '2023-01-05T10:00:00Z',
      updated_at: '2023-01-05T14:45:00Z', // Matches latest message timestamp
      latest_message: {
        id: 11,
        conversation_id: 5,
        sender: '555-3456',
        message: 'This is another unsaved contact, but with an avatar',
        type: 'text',
        timestamp: '2023-01-05T14:45:00Z',
        is_deleted: 0,
        metadata: {
          timestamp: 1672930500000
        }
      },
    }
  ]
};

// Mock data for notes
// All notes belong to the player with identifier: 'FXG8T3OE'
export const notesMockData = {
  notes: [
    {
      id: 1,
      identifier: 'FXG8T3OE', // Player's identifier
      title: 'Welcome to Notes',
      content:
        'This is your first note. You can create, edit, and organize your notes here.\n\nFeatures:\n- Create and edit notes\n- Organize with categories\n- Pin important notes\n- Add attachments\n- Customize text and colors',
      timestamp: Date.now(),
      color: '#4B5563',
      isPinned: true,
      categories: [],
      attachments: [],
      isFavorite: false,
      lastEdited: Date.now(),
      createdAt: Date.now(),
      fontSize: 16,
      backgroundColor: 'transparent'
    }
  ],
  categories: [
    { id: '1', name: 'Personal', color: '#FF5733' },
    { id: '2', name: 'Work', color: '#33FF57' },
    { id: '3', name: 'Ideas', color: '#3357FF' }
  ]
};

// Mock data for Yellow Pages
// Yellow Pages ads are visible to all players (no identifier filtering)
export const yellowPagesMockData = {
  ads: [
    {
      id: 1,
      description:
        'Pristine condition, only 12,000 miles. Red exterior, black leather interior. Full service history.',
      imageUrl: 'https://picsum.photos/800/600',
      contactNumber: '555-0123',
      price: 185000,
      timestamp: Date.now() - 3600000, // 1 hour ago
      characterName: 'John Smith',
      category: 'SELL'
    },
    {
      id: 2,
      description:
        'Looking to buy a used motorcycle in good condition. Preferably a Dinka or Pegassi. Budget up to $15,000.',
      contactNumber: '555-0124',
      timestamp: Date.now() - 7200000, // 2 hours ago
      characterName: 'Mike Johnson',
      category: 'BUY'
    },
    {
      id: 3,
      description:
        'Professional mechanic available. Specialized in sports cars and motorcycles. 10 years experience. Reasonable rates.',
      contactNumber: '555-0300',
      price: 150, // hourly rate
      timestamp: Date.now() - 86400000, // 1 day ago
      characterName: 'Tony Martinez',
      category: 'SERVICES'
    },
    {
      id: 4,
      description:
        'Hiring experienced bartenders and waitstaff for new upscale restaurant in Vinewood. Competitive pay and benefits.',
      contactNumber: '555-0000', // Player's own number
      timestamp: Date.now() - 43200000, // 12 hours ago
      characterName: 'Le H', // Player's name
      category: 'HIRING'
    },
    {
      id: 5,
      description:
        'Live music event this weekend at Mirror Park. Local bands, food trucks, and craft beer. Family friendly until 8pm.',
      contactNumber: '555-7777',
      timestamp: Date.now() - 129600000, // 36 hours ago
      characterName: 'Event Promotions',
      category: 'EVENTS',
      imageUrl: 'https://picsum.photos/800/500'
    }
  ]
};

// Mock data for LifeSnap
export const lifeSnapMockData = {
  currentProfile: {
    id: 'FXG8T3OE',
    username: 'alexrivera',
    fullName: 'Alex Rivera',
    profilePicture: 'https://picsum.photos/150/150',
    bio: '📍 Los Santos | 📸 Content Creator | 🎥 Digital Storyteller\nCollabs: <EMAIL>',
    followers: ['steam:110000187654321', 'steam:110000123456789'],
    following: ['steam:110000187654321', 'steam:110000123456789'],
    posts: [],
    isVerified: true,
    businessCategory: 'Digital Creator'
  },
  posts: [
    {
      id: 1,
      userId: 'FXG8T3OE',
      username: 'alexrivera',
      profilePicture: 'https://picsum.photos/150/150',
      imageUrl: 'https://picsum.photos/500/500',
      caption: 'Beautiful day in Los Santos! #sunset #views',
      likes: [2, 3, 4],
      comments: [
        {
          id: 1,
          userId: 'steam:110000187654321',
          text: 'Amazing view!',
          timestamp: Date.now() - 3000000
        },
        {
          id: 2,
          userId: 'steam:110000123456789',
          text: 'Where is this?',
          timestamp: Date.now() - 2500000
        }
      ],
      timestamp: Date.now() - 3600000, // 1 hour ago
      location: 'Vinewood Hills',
      taggedUsers: [2, 3]
    },
    {
      id: 2,
      userId: 'steam:110000187654321',
      username: 'sarahj',
      profilePicture: 'https://picsum.photos/150/151',
      imageUrl: 'https://picsum.photos/500/501',
      caption: 'New car day! 🚗 #newride #luxury',
      likes: [1, 3, 4, 5],
      comments: [
        { id: 3, userId: 'FXG8T3OE', text: 'Nice ride!', timestamp: Date.now() - 7000000 },
        {
          id: 4,
          userId: 'steam:110000123456789',
          text: 'Congrats!',
          timestamp: Date.now() - 6800000
        }
      ],
      timestamp: Date.now() - 7200000, // 2 hours ago
      location: 'Premium Deluxe Motorsport',
      taggedUsers: []
    }
  ],
  stories: [
    {
      id: 1,
      userId: 'FXG8T3OE',
      username: 'alexrivera',
      profilePicture: 'https://picsum.photos/150/150',
      imageUrl: 'https://picsum.photos/400/700',
      views: 12,
      timestamp: Date.now() - 1800000, // 30 minutes ago
      expiresAt: Date.now() + 86400000 // 24 hours from now
    },
    {
      id: 2,
      userId: 'steam:110000187654321',
      username: 'sarahj',
      profilePicture: 'https://picsum.photos/150/151',
      imageUrl: 'https://picsum.photos/400/701',
      views: 8,
      timestamp: Date.now() - 3600000, // 1 hour ago
      expiresAt: Date.now() + 86400000 // 24 hours from now
    }
  ]
};

// Mock data for Settings
export const settingsMockData = {
  phoneSettings: {
    theme: 'dark',
    wallpaper: 1,
    ringtone: 'default',
    notificationSound: 'default',
    brightness: 80,
    doNotDisturb: false,
    airplaneMode: false,
    hourFormat24: true,
    language: 'en',
    fontSize: 'medium'
  },
  playerData: {
    name: 'Le H',
    stateid: 'FXG8T3OE',
    firstName: 'John',
    lastName: 'Doe',
    phoneNumber: '555-0000', // Player's phone number
    identifier: 'FXG8T3OE',
    job: {
      name: 'Criminal',
      label: 'Criminal',
      grade: 2,
      gradeName: 'sergeant',
      payment: 1500,
      onDuty: true,
      isBoss: false
    },
    gang: {
      name: 'none',
      label: 'No Gang',
      grade: 0,
      isBoss: false
    },
    status: {
      hunger: 85,
      thirst: 90,
      stress: 10,
      isDead: false
    },
    money: {
      cash: 5000,
      bank: 25000
    },
    imageUrl: 'https://i.pravatar.cc/150?img=3'
  }
};

// Mock data for LoveLink
// All matches and messages belong to the player with identifier: 'FXG8T3OE'
export const lovelinkMockData = {
  profiles: [
    {
      id: 1,
      name: 'Sarah Johnson',
      age: 25,
      photos: [
        'https://picsum.photos/400/600',
        'https://picsum.photos/400/601',
        'https://picsum.photos/400/602'
      ],
      bio: 'Living life to the fullest! Restaurant owner by day, foodie by night. 🍝',
      occupation: 'Restaurant Owner',
      location: 'Downtown LS',
      interests: ['Music', 'Food', 'Travel'],
      lastActive: new Date(),
      verificationStatus: 'verified',
      premium: false
    },
    {
      id: 2,
      name: 'Emily Davis',
      age: 28,
      photos: [
        'https://picsum.photos/400/604',
        'https://picsum.photos/400/605',
        'https://picsum.photos/400/606'
      ],
      bio: "Adventure seeker and adrenaline junkie. Let's go skydiving! ✈️",
      occupation: 'Fitness Trainer',
      location: 'Vinewood Hills',
      interests: ['Sports', 'Fitness', 'Adventure'],
      lastActive: new Date(),
      verificationStatus: 'verified',
      premium: true
    }
  ],
  matches: [
    {
      id: 1,
      profileId: 1,
      matchedId: 2,
      timestamp: new Date(),
      lastMessage: {
        text: "Hey, how's it going?",
        timestamp: new Date(),
        isRead: false
      }
    }
  ],
  messages: [
    {
      id: 1,
      matchId: 1,
      senderId: 1,
      text: "Hey, how's it going?",
      timestamp: new Date(),
      status: 'delivered'
    }
  ]
};

// Mock data for Music
// All playlists and favorites belong to the player with identifier: 'FXG8T3OE'
export const musicMockData = {
  artists: [
    {
      id: 0,
      name: 'Le H', // Using player name from settingsMockData
      imageUrl: 'https://i.pravatar.cc/150?img=3', // Using player avatar from settingsMockData
      bio: 'Music lover | Singer | Songwriter',
      isUserProfile: true,
      isActive: true,
      followers: 125,
      following: 87,
      playlists: 5,
      identifier: 'FXG8T3OE' // Player's identifier
    },
    { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
    { id: 2, name: 'Epic Sound', imageUrl: 'https://picsum.photos/200/200?random=102' },
    { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' }
  ],
  albums: [
    {
      id: 0,
      title: 'My First Album',
      artistId: 0,
      artist: { id: 0, name: 'Le H', imageUrl: 'https://i.pravatar.cc/150?img=3' },
      imageUrl: 'https://picsum.photos/200/200?random=200'
    },
    {
      id: 1,
      title: 'Magician',
      artistId: 1,
      artist: { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
      imageUrl: 'https://picsum.photos/200/200?random=201'
    },
    {
      id: 2,
      title: 'Trailer Tension',
      artistId: 2,
      artist: { id: 2, name: 'Epic Sound', imageUrl: 'https://picsum.photos/200/200?random=102' },
      imageUrl: 'https://picsum.photos/200/200?random=202'
    },
    {
      id: 3,
      title: 'Neon Dreams',
      artistId: 3,
      artist: { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' },
      imageUrl: 'https://picsum.photos/200/200?random=203'
    }
  ],
  songs: [
    {
      id: 0,
      title: 'My First Song',
      albumId: 0,
      album: {
        id: 0,
        title: 'My First Album',
        artistId: 0,
        artist: { id: 0, name: 'Le H', imageUrl: 'https://i.pravatar.cc/150?img=3' },
        imageUrl: 'https://picsum.photos/200/200?random=200'
      },
      artistId: 0,
      artist: { id: 0, name: 'Le H', imageUrl: 'https://i.pravatar.cc/150?img=3' },
      duration: 180, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=300'
    },
    {
      id: 6,
      title: 'City Lights',
      albumId: 0,
      album: {
        id: 0,
        title: 'My First Album',
        artistId: 0,
        artist: { id: 0, name: 'Le H', imageUrl: 'https://i.pravatar.cc/150?img=3' },
        imageUrl: 'https://picsum.photos/200/200?random=200'
      },
      artistId: 0,
      artist: { id: 0, name: 'Le H', imageUrl: 'https://i.pravatar.cc/150?img=3' },
      duration: 195, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=306'
    },
    {
      id: 1,
      title: 'Stay with me',
      albumId: 1,
      album: {
        id: 1,
        title: 'Magician',
        artistId: 1,
        artist: { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
        imageUrl: 'https://picsum.photos/200/200?random=201'
      },
      artistId: 1,
      artist: { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
      duration: 210, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=301'
    },
    {
      id: 2,
      title: 'Midnight Dreams',
      albumId: 1,
      album: {
        id: 1,
        title: 'Magician',
        artistId: 1,
        artist: { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
        imageUrl: 'https://picsum.photos/200/200?random=201'
      },
      artistId: 1,
      artist: { id: 1, name: 'Dreamscape', imageUrl: 'https://picsum.photos/200/200?random=101' },
      duration: 195, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=302'
    },
    {
      id: 3,
      title: 'Epic Journey',
      albumId: 2,
      album: {
        id: 2,
        title: 'Trailer Tension',
        artistId: 2,
        artist: { id: 2, name: 'Epic Sound', imageUrl: 'https://picsum.photos/200/200?random=102' },
        imageUrl: 'https://picsum.photos/200/200?random=202'
      },
      artistId: 2,
      artist: { id: 2, name: 'Epic Sound', imageUrl: 'https://picsum.photos/200/200?random=102' },
      duration: 240, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=303'
    },
    {
      id: 4,
      title: 'Neon City',
      albumId: 3,
      album: {
        id: 3,
        title: 'Neon Dreams',
        artistId: 3,
        artist: { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' },
        imageUrl: 'https://picsum.photos/200/200?random=203'
      },
      artistId: 3,
      artist: { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' },
      duration: 280, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=304'
    },
    {
      id: 5,
      title: 'Retro Future',
      albumId: 3,
      album: {
        id: 3,
        title: 'Neon Dreams',
        artistId: 3,
        artist: { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' },
        imageUrl: 'https://picsum.photos/200/200?random=203'
      },
      artistId: 3,
      artist: { id: 3, name: 'Synthwave', imageUrl: 'https://picsum.photos/200/200?random=103' },
      duration: 225, // in seconds
      imageUrl: 'https://picsum.photos/200/200?random=305'
    }
  ],
  favorites: [1, 3, 4]
};

// Mock data for Garage app with realistic GTA V vehicles
export const garageMockData = {
  vehicles: [
    // Sports car - out and about
    {
      id: 1,
      plate: 'ABC123',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'adder',
      name: 'Adder',
      brand: 'Truffade',
      hash: '**********',
      type: 'automobile',
      category: 'super',
      garage: 'Legion Square',
      location: 'Last seen in Vinewood',
      state: 'out',
      fuel: 85,
      engine: 950,
      body: 900,
      drivingdistance: 1250,
      mods: { color1: 12, color2: 0, spoiler: 1, wheels: 3 },
      color: 12,
      color2: 0,
      status: 'Out',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/c/c2/Adder-GTAV-front.png',
      licensePlate: 'ABC 123'
    },

    // Motorcycle - parked in garage
    {
      id: 2,
      plate: 'BIKE001',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'bati',
      name: 'Bati 801',
      brand: 'Pegassi',
      hash: '-114291515',
      type: 'motorcycle',
      category: 'motorcycles',
      garage: 'Sandy Shores',
      location: 'Sandy Shores Garage',
      state: 'in',
      stored: 1,
      fuel: 100,
      engine: 1000,
      body: 1000,
      drivingdistance: 450,
      mods: { color1: 5, color2: 0, exhaust: 2 },
      color: 5,
      color2: 0,
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/d/d9/Bati801-GTAV-front.png',
      licensePlate: 'BIKE 001'
    },

    // SUV - impounded
    {
      id: 3,
      plate: 'SUV5555',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'granger',
      name: 'Granger',
      brand: 'Declasse',
      hash: '-**********',
      type: 'automobile',
      category: 'suvs',
      garage: 'Impound Lot',
      location: 'Davis Impound',
      state: 'impound',
      pound: 'Davis',
      fuel: 25,
      engine: 650,
      body: 450,
      drivingdistance: 3200,
      depotprice: 500,
      mods: { color1: 0, color2: 0 },
      color: 0,
      color2: 0,
      status: 'Impounded',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/c/c1/Granger-GTAV-front.png',
      licensePlate: 'SUV 5555'
    },

    // Financed sports car
    {
      id: 4,
      plate: 'FIN1234',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'zentorno',
      name: 'Zentorno',
      brand: 'Pegassi',
      hash: '-**********',
      type: 'automobile',
      category: 'super',
      garage: 'Pillbox Hill',
      location: 'Pillbox Hill Garage',
      state: 'in',
      stored: 1,
      fuel: 90,
      engine: 1000,
      body: 980,
      drivingdistance: 320,
      mods: { color1: 27, color2: 0, spoiler: 5, wheels: 7 },
      color: 27,
      color2: 0,
      balance: 150000,
      paymentamount: 15000,
      paymentsleft: 10,
      financetime: 10080, // 1 week in minutes
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/5/58/Zentorno-GTAV-front.png',
      licensePlate: 'FIN 1234'
    },

    // Job vehicle (police)
    {
      id: 5,
      plate: 'LSPD001',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'police3',
      name: 'Police Cruiser',
      brand: 'Vapid',
      hash: '**********',
      type: 'automobile',
      category: 'emergency',
      garage: 'Mission Row PD',
      location: 'Police Garage',
      state: 'in',
      stored: 1,
      job: 'police',
      fuel: 100,
      engine: 1000,
      body: 1000,
      drivingdistance: 5600,
      mods: { livery: 1 },
      color: 111,
      color2: 0,
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/e/eb/PoliceCruiser-GTAV-front.png',
      licensePlate: 'LSPD 001'
    },

    // Muscle car
    {
      id: 6,
      plate: 'MUSL555',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'dominator',
      name: 'Dominator',
      brand: 'Vapid',
      hash: '80636076',
      type: 'automobile',
      category: 'muscle',
      garage: 'Harmony',
      location: 'Harmony Garage',
      state: 'in',
      stored: 1,
      fuel: 75,
      engine: 850,
      body: 920,
      drivingdistance: 2100,
      mods: { color1: 28, color2: 0, spoiler: 2, exhaust: 1 },
      color: 28,
      color2: 0,
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/5/54/Dominator-GTAV-front.png',
      licensePlate: 'MUSL 555'
    },

    // Boat
    {
      id: 7,
      plate: 'BOAT001',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'dinghy',
      name: 'Dinghy',
      brand: 'Nagasaki',
      hash: '**********',
      type: 'boat',
      category: 'boats',
      garage: 'LSIA Dock',
      location: 'LSIA Boat Dock',
      state: 'in',
      stored: 1,
      fuel: 100,
      engine: 1000,
      body: 1000,
      drivingdistance: 120,
      mods: { color1: 3, color2: 0 },
      color: 3,
      color2: 0,
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/9/9e/Dinghy-GTAV-front.png',
      licensePlate: 'BOAT 001'
    },

    // Aircraft
    {
      id: 8,
      plate: 'HELI001',
      owner: 'license:123456789',
      stateid: 'ABC123',
      identifier: 'FXG8T3OE', // Player's identifier
      model: 'frogger',
      name: 'Frogger',
      brand: 'Maibatsu',
      hash: '744705981',
      type: 'aircraft',
      category: 'helicopters',
      garage: 'LSIA Hangar',
      location: 'LSIA Hangar',
      state: 'in',
      stored: 1,
      fuel: 100,
      engine: 1000,
      body: 1000,
      drivingdistance: 350,
      mods: { color1: 0, color2: 0 },
      color: 0,
      color2: 0,
      status: 'Ready',
      imageUrl: 'https://static.wikia.nocookie.net/gtawiki/images/8/8c/Frogger-GTAV-front.png',
      licensePlate: 'HELI 001'
    }
  ]
};

// Mock data for Dialer
// All calls belong to the player with phone number: '555-0000'


export const dialerMockData = {
  calls: [
    {
      id: 1,
      owner_number: '555-0000', // Player's phone number
      contact_number: '555-1234', // Contact's phone number
      timestamp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      duration: 120, // 2 minutes
      status: 'answered',
      created_at: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
    },
    {
      id: 2,
      owner_number: '555-0000', // Player's phone number
      contact_number: '555-5678', // Contact's phone number
      timestamp: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
      duration: 0,
      status: 'missed',
      created_at: new Date(Date.now() - 7200000).toISOString() // 2 hours ago
    },
    {
      id: 3,
      owner_number: '555-0000', // Player's phone number
      contact_number: '555-9012', // Contact's phone number
      timestamp: Math.floor(Date.now() / 1000) - 10800, // 3 hours ago
      duration: 45, // 45 seconds
      status: 'outgoing',
      created_at: new Date(Date.now() - 10800000).toISOString() // 3 hours ago
    },
    {
      id: 4,
      owner_number: '555-0000', // Player's phone number
      contact_number: '555-3456', // Contact's phone number
      timestamp: Math.floor(Date.now() / 1000) - 86400, // 1 day ago
      duration: 0,
      status: 'missed',
      created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    },
    {
      id: 5,
      owner_number: '555-0000', // Player's phone number
      contact_number: '555-7890', // Contact's phone number
      timestamp: Math.floor(Date.now() / 1000) - 172800, // 2 days ago
      duration: 300, // 5 minutes
      status: 'answered',
      created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
    }
  ]
};
