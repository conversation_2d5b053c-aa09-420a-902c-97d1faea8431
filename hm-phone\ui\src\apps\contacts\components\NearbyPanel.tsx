import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useContactsStore } from '../stores/contactsStore';

interface NearbyPanelProps {
  contact: {
    id: number;
    name: string;
    phone: string;
    photo?: string;
  };
  onClose: () => void;
}

interface NearbyPlayer {
  id: number;
  name: string;
  distance: number;
  avatar?: string;
}

const NearbyPanel: React.FC<NearbyPanelProps> = ({ contact, onClose }) => {
  const [nearbyPlayers, setNearbyPlayers] = useState<NearbyPlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const { shareContact } = useContactsStore().actions;
  const { sharingStatus } = useContactsStore();

  // Get sharing status for a specific player
  const getShareStatus = (playerId: number) => {
    const key = `${contact.id}_${playerId}`;
    return sharingStatus[key]?.status || null;
  };

  // Get status text and color
  const getStatusInfo = (playerId: number) => {
    const status = getShareStatus(playerId);

    switch (status) {
      case 'sent':
        return { text: 'Sent', color: 'text-blue-400' };
      case 'accepted':
        return { text: 'Accepted', color: 'text-green-400' };
      case 'declined':
        return { text: 'Declined', color: 'text-red-400' };
      case 'error':
        return { text: 'Failed', color: 'text-red-400' };
      default:
        return { text: 'Share', color: 'text-white' };
    }
  };

  // Handle share button click
  const handleShare = (player: NearbyPlayer) => {
    // Only allow sharing if not already shared or if declined
    const status = getShareStatus(player.id);
    if (status && status !== 'declined') return;

    // Convert the contact to the expected format
    const contactToShare = {
      id: contact.id,
      identifier: contact.id.toString(),
      stateid: contact.id.toString(),
      number: contact.phone,
      name: contact.name,
      favorite: 0,
      avatar: contact.photo || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    shareContact(contactToShare, player.id);
  };

  // Fetch nearby players
  useEffect(() => {
    // Simulate fetching nearby players
    // In a real implementation, this would come from the server
    setTimeout(() => {
      setNearbyPlayers([
        // { id: 1, name: 'John Doe', distance: 5.2, avatar: 'https://i.pravatar.cc/150?img=1' },
        // { id: 2, name: 'Jane Smith', distance: 8.7, avatar: 'https://i.pravatar.cc/150?img=2' },
        // { id: 3, name: 'Bob Johnson', distance: 12.3, avatar: 'https://i.pravatar.cc/150?img=3' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="absolute inset-0 z-40 bg-gray-900/95 flex flex-col"
    >
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        <h3 className="text-lg font-medium text-white">Share Contact</h3>
        <button onClick={onClose} className="text-gray-400 hover:text-white">
          <i className="fas fa-times"></i>
        </button>
      </div>

      <div className="p-4">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center mr-3">
            {contact.photo ? (
              <img
                src={contact.photo}
                alt={contact.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <i className="fas fa-user text-gray-400 text-xl"></i>
            )}
          </div>
          <div>
            <p className="text-white font-medium">{contact.name}</p>
            <p className="text-gray-400 text-sm">{contact.phone}</p>
          </div>
        </div>

        <h4 className="text-gray-400 text-sm uppercase mb-2">Nearby Players</h4>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {nearbyPlayers.map(player => {
              const { text, color } = getStatusInfo(player.id);
              const isShared = getShareStatus(player.id) !== null;

              return (
                <div
                  key={player.id}
                  className="flex items-center justify-between bg-gray-800 rounded-lg p-3"
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center mr-3">
                      {player.avatar ? (
                        <img
                          src={player.avatar}
                          alt={player.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <i className="fas fa-user text-gray-400"></i>
                      )}
                    </div>
                    <div>
                      <p className="text-white">{player.name}</p>
                      <p className="text-gray-400 text-xs">{player.distance.toFixed(1)}m away</p>
                    </div>
                  </div>

                  <button
                    onClick={() => handleShare(player)}
                    className={`px-3 py-1 rounded ${
                      isShared ? 'bg-transparent' : 'bg-gray-700 hover:bg-gray-600'
                    } ${color} transition`}
                    disabled={isShared && getShareStatus(player.id) !== 'declined'}
                  >
                    {text}
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default NearbyPanel;
