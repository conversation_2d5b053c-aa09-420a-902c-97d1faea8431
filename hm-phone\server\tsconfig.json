{"compilerOptions": {"target": "ES2020", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "module": "CommonJS", "types": ["@citizenfx/server", "@types/node"], "lib": ["ES2020"], "baseUrl": "..", "paths": {"@shared/*": ["shared/*"], "@client/*": ["client/*"], "@server/*": ["server/*"]}}, "include": ["./**/*", "../shared/**/*"], "exclude": ["**/node_modules", "**/__tests__/*"]}