import React from 'react';
import { Transaction } from '../types/bankingTypes';
import { useNavigation } from '../../../navigation/hooks';

interface TransactionItemProps {
  transaction: Transaction;
}

const TransactionItem: React.FC<TransactionItemProps> = ({ transaction }) => {
  const { openView } = useNavigation();
  const isDebit = transaction.type === 'debit';
  const amount = isDebit ? -transaction.amount : transaction.amount;

  const handleClick = () => {
    openView('transaction', { transactionId: transaction.id });
  };

  return (
    <div
      className="bg-white/5 hover:bg-white/10 border-white/5 hover:border-white/10 rounded-xl p-3 transition-all duration-300 border group cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div
            className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
              isDebit
                ? 'bg-rose-500/20 text-rose-400 group-hover:bg-rose-500/30'
                : 'bg-teal-500/20 text-teal-400 group-hover:bg-teal-500/30'
            }`}
          >
            {transaction.icon ? (
              <img
                src={transaction.icon}
                alt={transaction.merchantName}
                className="w-6 h-6 rounded-lg"
              />
            ) : (
              <i className={`fas fa-${isDebit ? 'shopping-bag' : 'arrow-down'}`}></i>
            )}
          </div>
          <div>
            <div className="text-white font-medium text-sm">{transaction.merchantName}</div>
            {transaction.description && (
              <div className="text-white/60 text-xs mt-0.5 line-clamp-1">
                {transaction.description}
              </div>
            )}
            <div className="text-white/40 text-xs mt-0.5">
              {new Date(transaction.timestamp).toLocaleDateString(undefined, {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
        <div className={`font-medium ${isDebit ? 'text-rose-400' : 'text-teal-400'}`}>
          {isDebit ? '-' : '+'}€{Math.abs(amount).toFixed(2)}
        </div>
      </div>
    </div>
  );
};

export default TransactionItem;
