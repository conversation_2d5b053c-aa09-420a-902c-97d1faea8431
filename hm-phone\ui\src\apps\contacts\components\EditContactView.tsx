import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Contact } from '@shared/types';

interface EditContactViewProps {
  contact: Contact;
  onUpdateContact: (updatedContact: Contact) => void;
  onCancel: () => void;
}

const EditContactView: React.FC<EditContactViewProps> = ({
  contact,
  onUpdateContact,
  onCancel
}) => {
  const [name, setName] = useState(contact.name);
  const [phone, setPhone] = useState(contact.number);
  const [favorite, setFavorite] = useState(contact.favorite === 1);
  const [photo, setPhoto] = useState(contact.avatar || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (!name.trim()) {
      setError('Please enter a name');
      return;
    }

    if (!phone.trim()) {
      setError('Please enter a phone number');
      return;
    }

    setIsSubmitting(true);
    setError('');

    // Keep the same ID and update only the fields we care about
    const updatedContact: Contact = {
      ...contact,
      name: name.trim(),
      number: phone.trim(),
      favorite: favorite ? 1 : 0,
      avatar: photo || null,
      updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
    };

    try {
      // Call the update function and navigate back
      onUpdateContact(updatedContact);

      // We don't need to manually set isSubmitting to false here
      // because we're navigating away from this component
    } catch (err) {
      console.error('Error updating contact:', err);
      setError('Failed to update contact');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-white/10">
        <button onClick={onCancel} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left" />
        </button>
        <h2 className="text-white font-medium">Edit Contact</h2>
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="text-green-500 hover:text-green-400 disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : 'Save'}
        </button>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {error && <div className="bg-red-500/20 text-red-100 px-4 py-2 rounded">{error}</div>}

        {/* Profile Photo */}
        <div className="flex flex-col items-center mb-6">
          <div className="relative w-24 h-24 rounded-full overflow-hidden mb-2">
            {photo ? (
              <div
                className="w-full h-full bg-cover bg-center"
                style={{ backgroundImage: `url(${photo})` }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-2xl font-medium">
                  {name ? name.charAt(0).toUpperCase() : '?'}
                </span>
              </div>
            )}
            <button
              className="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-green-500 flex items-center justify-center shadow-lg"
              onClick={() => {
                // In a real implementation, this would open a photo picker
                const randomId = Math.floor(Math.random() * 70);
                setPhoto(`https://i.pravatar.cc/300?img=${randomId}`);
              }}
            >
              <i className="fas fa-camera text-white text-sm"></i>
            </button>
          </div>
        </div>

        {/* Name Field */}
        <div className="space-y-2">
          <label className="text-white/60 text-sm">Name</label>
          <input
            type="text"
            value={name}
            onChange={e => setName(e.target.value)}
            className="w-full bg-white/10 text-white rounded-lg px-4 py-2
                     focus:outline-none focus:ring-2 focus:ring-white/20"
            placeholder="Enter name"
          />
        </div>

        {/* Phone Field */}
        <div className="space-y-2">
          <label className="text-white/60 text-sm">Phone</label>
          <input
            type="text"
            value={phone}
            onChange={e => setPhone(e.target.value)}
            className="w-full bg-white/10 text-white rounded-lg px-4 py-2
                     focus:outline-none focus:ring-2 focus:ring-white/20"
            placeholder="Enter phone number"
          />
        </div>

        {/* Favorite Toggle */}
        <div className="flex items-center justify-between py-2">
          <span className="text-white/60">Add to Favorites</span>
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setFavorite(!favorite)}
            className="text-2xl"
          >
            <i className={`fas fa-star ${favorite ? 'text-yellow-400' : 'text-white/20'}`} />
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default EditContactView;
