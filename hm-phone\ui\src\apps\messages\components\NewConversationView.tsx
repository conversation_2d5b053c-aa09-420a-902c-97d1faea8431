import React, { useState, useEffect, useMemo } from 'react';
import { useContactsStore } from '../../contacts/stores/contactsStore';
import { Contact, Message } from '@shared/types';

interface NewConversationViewProps {
  onBack: () => void;
  onCreateConversation: (contacts: Contact[], initialMessage: Message) => Promise<void>;
}

const NewConversationView: React.FC<NewConversationViewProps> = ({
  onBack,
  onCreateConversation
}) => {
  console.log('[NewConversationView] Component rendered');

  // State for search and selection
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [initialMessage, setInitialMessage] = useState<Message>({
    id: 0,
    conversation_id: 0,
    sender: '',
    message: '',
    type: 'text',
    content: '',
    metadata: null,
    timestamp: new Date().toISOString(),
    is_deleted: 0
  } as Message);
  const [showMessageInput, setShowMessageInput] = useState(false);

  // Get contacts from store
  const { contacts } = useContactsStore();

  // Check if search term could be a phone number (simpler check)
  const couldBePhoneNumber = useMemo(() => {
    const term = searchTerm.trim();
    // Simple check: contains only digits, +, -, (, ), and spaces
    // and has at least 3 digits
    const simpleRegex = /^[0-9+\-() ]+$/;
    const digitCount = term.replace(/[^0-9]/g, '').length;
    return simpleRegex.test(term) && digitCount >= 3;
  }, [searchTerm]);

  // Filter contacts based on search term
  const filteredContacts = useMemo(() => {
    if (!searchTerm.trim()) {
      // When no search term, show all contacts sorted alphabetically
      return contacts
        .sort((a, b) => a.name.localeCompare(b.name));
    }

    const term = searchTerm.toLowerCase().trim();
    return contacts.filter(
      contact => contact.name.toLowerCase().includes(term) || contact.number.includes(term)
    );
  }, [contacts, searchTerm]);

  // Get favorite contacts
  const favoriteContacts = useMemo(() => {
    return contacts
      .filter(contact => contact.favorite === 1)
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [contacts]);

  // Show message input when contacts are selected
  useEffect(() => {
    if (selectedContacts.length > 0 && !showMessageInput) {
      setShowMessageInput(true);
    } else if (selectedContacts.length === 0 && showMessageInput) {
      setShowMessageInput(false);
    }
  }, [selectedContacts, showMessageInput]);

  // Handle selecting/deselecting a contact
  const handleSelectContact = (contact: Contact) => {
    // Check if already selected
    if (selectedContacts.some(c => c.number === contact.number)) {
      // If already selected, remove it (toggle behavior)
      setSelectedContacts(selectedContacts.filter(c => c.number !== contact.number));
    } else {
      // If not selected, add it
      setSelectedContacts([...selectedContacts, contact]);
      setSearchTerm(''); // Clear search after selection
    }
  };

  // Handle adding a phone number that's not in contacts
  const handleAddPhoneNumber = () => {
    const phone = searchTerm.trim();

    // Basic validation
    if (!phone || phone.length < 3) return;

    // Check if already selected
    if (selectedContacts.some(c => c.number === phone)) {
      return;
    }

    // Create a new contact object
    const newContact: Contact = {
      id: Date.now(),
      identifier: '', // Will be set by the server
      stateid: '', // Will be set by the server
      number: phone,
      name: phone,
      favorite: 0,
      is_blocked: 0,
      avatar: null,
      notes: null,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
    };

    setSelectedContacts([...selectedContacts, newContact]);
    setSearchTerm(''); // Clear search after selection
  };

  // Handle removing a contact
  const handleRemoveContact = (index: number) => {
    const newSelectedContacts = [...selectedContacts];
    newSelectedContacts.splice(index, 1);
    setSelectedContacts(newSelectedContacts);
  };

  // Handle creating the conversation
  const handleCreateConversation = () => {
    if (selectedContacts.length === 0) return;
    const members = selectedContacts as Contact[];

    // Update message properties
    const message = {
      ...initialMessage,
      conversation_id: 0,
      id: 0,
      type: 'text',
      metadata: null,
      timestamp: new Date().toISOString(),
      is_deleted: 0,
      sender: '',
      content: initialMessage.message // Ensure content matches message
    } as Message;

    onCreateConversation(members, message);
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center p-4 border-b border-gray-800">
        <button onClick={onBack} className="text-white mr-4">
          <i className="fas fa-arrow-left" />
        </button>
        <h2 className="text-white text-lg">New Message</h2>
      </div>

      {/* Selected contacts */}
      {selectedContacts.length > 0 && (
        <div className="px-3 py-1 border-b border-gray-800 flex flex-wrap gap-1">
          {selectedContacts.map((contact, index) => (
            <div
              key={index}
              className="flex items-center bg-blue-500/20 text-white text-xs px-1.5 py-0.5 rounded-full"
            >
              {contact.avatar ? (
                <img
                  src={contact.avatar}
                  alt={contact.name}
                  className="w-4 h-4 rounded-full mr-0.5"
                />
              ) : (
                <i className="fas fa-user text-[10px] mr-0.5"></i>
              )}
              <span className="mr-0.5 truncate max-w-[80px]">{contact.name}</span>
              <button
                onClick={() => handleRemoveContact(index)}
                className="text-white/80 hover:text-white"
              >
                <i className="fas fa-times text-[10px]" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Search input */}
      <div className="p-3 border-b border-gray-800">
        <div className="relative flex items-center">
          <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40"></i>
          <input
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="Search contacts or enter phone number"
            className="flex-1 bg-white/10 text-white rounded-lg pl-10 pr-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
          />
          {couldBePhoneNumber && (
            <button
              onClick={handleAddPhoneNumber}
              className="ml-2 bg-blue-500 text-white text-sm px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Add
            </button>
          )}
        </div>
      </div>

      {/* Contact list */}
      <div className="flex-1 overflow-y-auto p-2">
        {/* Filtered contacts */}

        {filteredContacts.length > 0 ? (
          <>
            {!searchTerm.trim() && favoriteContacts.length > 0 && (
              <>
                <div className="text-white/60 text-xs uppercase px-2 py-1">
                  Favorite Contacts ({favoriteContacts.length})
                </div>
                <div className="grid grid-cols-1 gap-1 mb-4">
                  {favoriteContacts.map(contact => (
                    <div
                      key={`fav-${contact.id}`}
                      onClick={() => handleSelectContact(contact)}
                      className="flex items-center px-2 py-1.5 rounded hover:bg-white/5 cursor-pointer transition-colors"
                    >
                      <div className="w-8 h-8 rounded-full ring-2 ring-yellow-500 overflow-hidden mr-2 flex-shrink-0">
                        {contact.avatar ? (
                          <img
                            src={contact.avatar}
                            alt={contact.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500/30 to-blue-600/30">
                            <span className="text-white text-sm font-medium">
                              {contact.name?.[0]?.toUpperCase() || '?'}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-1">
                          <span className="text-white text-sm truncate">{contact.name}</span>
                          <span className="text-yellow-500 text-[10px]">
                            <i className="fas fa-star"></i>
                          </span>
                        </div>
                        <div className="text-white/40 text-xs truncate">{contact.number}</div>
                      </div>
                      {selectedContacts.some(c => c.number === contact.number) && (
                        <div className="ml-auto text-blue-400 flex-shrink-0">
                          <i className="fas fa-check"></i>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}

            <div className="text-white/60 text-xs uppercase px-2 py-1">
              {searchTerm.trim() ? `Search Results (${filteredContacts.length})` : `All Contacts (${filteredContacts.length})`}
            </div>
            <div className="grid grid-cols-1 gap-1">
              {filteredContacts.map(contact => (
                <div
                  key={contact.id}
                  onClick={() => handleSelectContact(contact)}
                  className="flex items-center px-2 py-1.5 rounded hover:bg-white/5 cursor-pointer transition-colors"
                >
                  <div className={`w-8 h-8 rounded-full ${contact.favorite === 1 ? 'ring-2 ring-yellow-500' : 'bg-white/20'} overflow-hidden mr-2 flex-shrink-0`}>
                    {contact.avatar ? (
                      <img
                        src={contact.avatar}
                        alt={contact.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500/30 to-blue-600/30">
                        <span className="text-white text-sm font-medium">
                          {contact.name?.[0]?.toUpperCase() || '?'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1">
                      <span className="text-white text-sm truncate">{contact.name}</span>
                      {contact.favorite === 1 && (
                        <span className="text-yellow-500 text-[10px]">
                          <i className="fas fa-star"></i>
                        </span>
                      )}
                    </div>
                    <div className="text-white/40 text-xs truncate">{contact.number}</div>
                  </div>
                  {selectedContacts.some(c => c.number === contact.number) && (
                    <div className="ml-auto text-blue-400 flex-shrink-0">
                      <i className="fas fa-check"></i>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        ) : searchTerm.trim() ? (
          <div className="flex flex-col items-center justify-center h-32 text-white/40 text-center p-4">
            <i className="fas fa-search text-2xl mb-2"></i>
            <p>No contacts found</p>
            {couldBePhoneNumber && (
              <button
                onClick={handleAddPhoneNumber}
                className="mt-2 bg-blue-500 text-white text-sm px-3 py-1 rounded hover:bg-blue-600 transition-colors"
              >
                Add {searchTerm.trim()} as contact
              </button>
            )}
          </div>
        ) : (
          <div className="p-2">
            <div className="text-white/60 text-xs uppercase px-2 py-1">All Contacts ({contacts.length})</div>
            <div className="grid grid-cols-1 gap-1">
              {contacts
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(contact => (
                  <div
                    key={contact.id}
                    onClick={() => handleSelectContact(contact)}
                    className="flex items-center px-2 py-1.5 rounded hover:bg-white/5 cursor-pointer transition-colors"
                  >
                    <div className={`w-8 h-8 rounded-full ${contact.favorite === 1 ? 'ring-2 ring-yellow-500' : 'bg-white/20'} overflow-hidden mr-2 flex-shrink-0`}>
                      {contact.avatar ? (
                        <img
                          src={contact.avatar}
                          alt={contact.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500/30 to-blue-600/30">
                          <span className="text-white text-sm font-medium">
                            {contact.name?.[0]?.toUpperCase() || '?'}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-1">
                        <span className="text-white text-sm truncate">{contact.name}</span>
                        {contact.favorite === 1 && (
                          <span className="text-yellow-500 text-[10px]">
                            <i className="fas fa-star"></i>
                          </span>
                        )}
                      </div>
                      <div className="text-white/40 text-xs truncate">{contact.number}</div>
                    </div>
                    {selectedContacts.some(c => c.number === contact.number) && (
                      <div className="ml-auto text-blue-400 flex-shrink-0">
                        <i className="fas fa-check"></i>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* Message input and create button */}
      {showMessageInput && (
        <div className="p-3 border-t border-gray-800">
          <textarea
            value={initialMessage.message}
            onChange={e => setInitialMessage({
              ...initialMessage,
              message: e.target.value,
              content: e.target.value
            } as Message)}
            placeholder="Type your first message..."
            className="w-full h-16 bg-white/10 text-white text-sm rounded-lg px-3 py-2 mb-2 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
          />
          <button
            onClick={handleCreateConversation}
            className="w-full bg-blue-500 text-white py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors"
          >
            Start Conversation
          </button>
        </div>
      )}
    </div>
  );
};

export default NewConversationView;
