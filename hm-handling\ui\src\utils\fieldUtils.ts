import { BaseHandlingData, HANDLING_FIELD_LIMITS, Vector3 } from '../../../scripts/shared/types';

/**
 * Validates if a value is within the field's limits
 */
export const isValueInBounds = (field: keyof BaseHandlingData, value: number): boolean => {
  const limits = HANDLING_FIELD_LIMITS[field];
  if (!limits) return true;
  
  const { min, max } = limits;
  if (min !== undefined && value < min) return false;
  if (max !== undefined && value > max) return false;
  return true;
};

/**
 * Validates if all Vector3 components are within the field's limits
 */
export const isVector3InBounds = (field: string, value: Vector3): boolean => {
  const limits = HANDLING_FIELD_LIMITS[field as keyof typeof HANDLING_FIELD_LIMITS];
  if (!limits) return true;
  
  const { min, max } = limits;
  
  const checkComponent = (component: number): boolean => {
    if (min !== undefined && component < min) return false;
    if (max !== undefined && component > max) return false;
    return true;
  };
  
  return checkComponent(value.x) && checkComponent(value.y) && checkComponent(value.z);
};

/**
 * Gets the appropriate step value for a field
 */
export const getFieldStep = (field: keyof BaseHandlingData): number => {
  const limits = HANDLING_FIELD_LIMITS[field];
  return limits?.step ?? 0.001;
};

/**
 * Gets field limits for display
 */
export const getFieldLimits = (field: keyof BaseHandlingData) => {
  return HANDLING_FIELD_LIMITS[field];
};

/**
 * Formats a field name for display (could be enhanced later)
 */
export const formatFieldName = (fieldName: string): string => {
  return fieldName;
};

/**
 * Determines if a field is a Vector3 field
 */
export const isVector3Field = (field: string): boolean => {
  return field === 'vecCentreOfMassOffset' || field === 'vecInertiaMultiplier';
};

/**
 * Gets the default value for a field if available
 */
export const getFieldDefault = (field: keyof BaseHandlingData): number | undefined => {
  const limits = HANDLING_FIELD_LIMITS[field];
  return limits?.default;
};

/**
 * Checks if a field is a string field (not numeric)
 */
export const isStringField = (field: keyof BaseHandlingData): boolean => {
  const stringFields: Array<keyof BaseHandlingData> = [
    'strModelFlags',
    'strHandlingFlags',
    'strDamageFlags',
    'AIHandling'
  ];
  return stringFields.includes(field);
};

/**
 * Checks if a field is an integer field
 */
export const isIntegerField = (field: keyof BaseHandlingData): boolean => {
  const integerFields: Array<keyof BaseHandlingData> = [
    'nInitialDriveGears',
    'nMonetaryValue'
  ];
  return integerFields.includes(field);
};
