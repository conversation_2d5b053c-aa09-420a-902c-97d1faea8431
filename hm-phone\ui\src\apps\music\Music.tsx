import React from 'react';
import { useMusicStore } from './stores/musicStore';
import MusicHome from './components/MusicHome';
import NowPlaying from './components/NowPlaying';
import Profile from './components/Profile';
import Artist from './components/Artist';
import Album from './components/Album';
import MiniPlayer from './components/MiniPlayer';
import MusicNavigation from './components/MusicNavigation';
import PullIndicator from '../../common/components/PullIndicator';
import './styles/music.css';
import { useNavigationStore } from '../../navigation/navigationStore';

const Music: React.FC = () => {
  const { currentView } = useNavigationStore();
  const { currentSong } = useMusicStore();

  // Determine which view to render
  const renderView = () => {
    switch (currentView) {
      case 'nowPlaying':
        return <NowPlaying />;
      case 'profile':
        return <Profile />;
      case 'artist':
        return <Artist />;
      case 'album':
        return <Album />;
      default:
        return <MusicHome />;
    }
  };

  // Show mini player only if there's a current song and we're not in the nowPlaying view
  const showMiniPlayer = currentSong && currentView !== 'nowPlaying';

  // Show navigation only in the main view
  const showNavigation = currentView === 'main';

  // Determine if MiniPlayer is the bottom-most component
  // MiniPlayer is at the bottom when it's visible and we're not in the main view
  const isMiniPlayerBottom = showMiniPlayer && !showNavigation;

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white relative">
      {/* Main content area - takes available space */}
      <div className="flex-1 overflow-auto">{renderView()}</div>

      {/* Fixed height components at bottom - always in DOM but height controlled by CSS */}
      <div
        className={`w-full overflow-hidden transition-all duration-300 ${
          showMiniPlayer ? 'h-14' : 'h-0 opacity-0'
        }`}
      >
        <MiniPlayer onOpen={() => {}} />
      </div>

      {/* Conditional padding element with same styling as MiniPlayer */}
      <div
        className={`w-full bg-black/90 backdrop-blur-md transition-all duration-300 ${
          isMiniPlayerBottom ? 'h-[24px] opacity-100' : 'h-0 opacity-0'
        }`}
      ></div>

      <div
        className={`w-full overflow-hidden transition-all duration-300 ${
          showNavigation ? 'h-[76px]' : 'h-0 opacity-0'
        }`}
      >
        <MusicNavigation />
      </div>

      {/* Pull indicator with 0 opacity */}
      <div className="opacity-0">
        <PullIndicator />
      </div>
    </div>
  );
};

export default Music;
