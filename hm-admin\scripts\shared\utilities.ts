/**
 * Shared utilities for HM Admin
 * This file contains reusable functions that can be used across different features
 */

// ===== DEBUG MODE =====

// Global debug state for raycast visualization
let debugMode = false;

// Debug visualization storage
interface DebugVisualization {
  id: string;
  type: 'line' | 'sphere';
  data: any;
  timestamp: number;
}

let debugVisualizations: DebugVisualization[] = [];
let debugVisualizationId = 0;

/**
 * Set global debug mode state
 */
export function setDebugMode(enabled: boolean): void {
  debugMode = enabled;
  if (!enabled) {
    // Clear all debug visualizations when disabling debug mode
    debugVisualizations = [];
  }
}

/**
 * Get current debug mode state
 */
export function isDebugMode(): boolean {
  return debugMode;
}

/**
 * Add debug line visualization
 */
function addDebugLine(start: Vector3, end: Vector3, color: { r: number; g: number; b: number; a: number } = { r: 255, g: 0, b: 0, a: 255 }): void {
  if (!debugMode) return;
  
  const id = `line_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: 'line',
    data: { start, end, color },
    timestamp: GetGameTimer()
  });
  
  console.log(`[Debug] Added line from ${JSON.stringify(start)} to ${JSON.stringify(end)}`);
}

/**
 * Add debug sphere visualization
 */
function addDebugSphere(position: Vector3, radius: number = 0.1, color: { r: number; g: number; b: number; a: number } = { r: 0, g: 255, b: 0, a: 255 }): void {
  if (!debugMode) return;
  
  const id = `sphere_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: 'sphere',
    data: { position, radius, color },
    timestamp: GetGameTimer()
  });
}

/**
 * Render all debug visualizations (call this in a render loop)
 */
export function renderDebugVisualizations(): void {
  if (!debugMode || debugVisualizations.length === 0) return;
  
  const currentTime = GetGameTimer();
  const maxAge = 5000; // 5 seconds
  
  // Remove old visualizations
  debugVisualizations = debugVisualizations.filter(viz => (currentTime - viz.timestamp) < maxAge);
  
  // Debug logging every few seconds
  if (currentTime % 2000 < 100) { // Log roughly every 2 seconds
    console.log(`[Debug] Rendering ${debugVisualizations.length} visualizations`);
  }
  
  // Render active visualizations
  for (const viz of debugVisualizations) {
    if (viz.type === 'line') {
      const { start, end, color } = viz.data;
      DrawLine(start.x, start.y, start.z, end.x, end.y, end.z, color.r, color.g, color.b, color.a);
    } else if (viz.type === 'sphere') {
      const { position, radius, color } = viz.data;
      // Use a simple sphere visualization with multiple lines
      for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2;
        const x1 = position.x + Math.cos(angle) * radius;
        const y1 = position.y + Math.sin(angle) * radius;
        const x2 = position.x + Math.cos(angle + Math.PI / 4) * radius;
        const y2 = position.y + Math.sin(angle + Math.PI / 4) * radius;
        DrawLine(x1, y1, position.z, x2, y2, position.z, color.r, color.g, color.b, color.a);
        DrawLine(position.x, position.y, position.z - radius, position.x, position.y, position.z + radius, color.r, color.g, color.b, color.a);
      }
    }
  }
}

// ===== BASIC UTILITY FUNCTIONS =====

// Wait for a specified number of milliseconds
export function Wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Check if a value is a valid number
export function isValidNumber(value: any): boolean {
  return !isNaN(parseFloat(value)) && isFinite(value);
}

// Format coordinates to a readable string
export function formatCoords(x: number, y: number, z: number, heading?: number): string {
  const coordsStr = `X: ${x.toFixed(2)}, Y: ${y.toFixed(2)}, Z: ${z.toFixed(2)}`;
  return heading !== undefined ? `${coordsStr}, Heading: ${heading.toFixed(2)}` : coordsStr;
}

// Format time duration from milliseconds to readable string
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Format date to readable string
export function formatDate(date: Date | string | number): string {
  const d = new Date(date);
  return d.toLocaleString();
}

// Parse duration string to milliseconds
export function parseDuration(durationStr: string): number {
  const regex = /^(\d+)([smhdw])$/;
  const match = durationStr.match(regex);

  if (!match) {
    return 0;
  }

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 's': return value * 1000; // seconds
    case 'm': return value * 60 * 1000; // minutes
    case 'h': return value * 60 * 60 * 1000; // hours
    case 'd': return value * 24 * 60 * 60 * 1000; // days
    case 'w': return value * 7 * 24 * 60 * 60 * 1000; // weeks
    default: return 0;
  }
}

// Get distance between two coordinates
export function getDistance(x1: number, y1: number, z1: number, x2: number, y2: number, z2: number): number {
  const dx = x2 - x1;
  const dy = y2 - y1;
  const dz = z2 - z1;

  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

// Capitalize first letter of each word
export function capitalizeWords(str: string): string {
  return str.replace(/\b\w/g, char => char.toUpperCase());
}

// Vector3 interface for position handling
export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

// Vehicle placement configuration
export interface VehiclePlacementConfig {
  maxDistance: number;
  raycastFlags: number;
  ghostAlpha: number;
  rotationStep: number;
}

// Default configuration for vehicle placement
export const DEFAULT_VEHICLE_PLACEMENT_CONFIG: VehiclePlacementConfig = {
  maxDistance: 5.0,
  raycastFlags: 1, // World collision
  ghostAlpha: 100,
  rotationStep: 5.0
};

// Utility functions for math operations
export class MathUtils {
  /**
   * Convert degrees to radians
   */
  static degToRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Convert radians to degrees
   */
  static radToDeg(radians: number): number {
    return radians * (180 / Math.PI);
  }

  /**
   * Calculate distance between two 3D points
   */
  static distance3D(pos1: Vector3, pos2: Vector3): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * Normalize heading to 0-360 range
   */
  static normalizeHeading(heading: number): number {
    while (heading < 0) heading += 360;
    while (heading >= 360) heading -= 360;
    return heading;
  }

  /**
   * Linear interpolation between two values
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }
}

// Utility functions for position calculations
export class PositionUtils {
  /**
   * Get position in front of entity at specified distance
   */
  static getPositionInFront(entity: number, distance: number): Vector3 {
    const coords = GetEntityCoords(entity, false);
    const heading = GetEntityHeading(entity);
    const radians = MathUtils.degToRad(heading);
    
    return {
      x: coords[0] + Math.sin(radians) * distance,
      y: coords[1] + Math.cos(radians) * distance,
      z: coords[2]
    };
  }

  /**
   * Get ground Z coordinate for given X, Y position
   */
  static getGroundZ(x: number, y: number): number {
    const [success, groundZ] = GetGroundZFor_3dCoord(x, y, 1000.0, false);
    return success ? groundZ : 0.0;
  }  /**
   * Convert camera rotation to direction vector (enhanced FiveM method from CameraHelper)
   */
  private static rotToDirection(rot: Vector3): Vector3 {
    const z = MathUtils.degToRad(rot.z);
    const x = MathUtils.degToRad(rot.x);
    const num = Math.abs(Math.cos(x));
    
    return {
      x: -Math.sin(z) * num,
      y: Math.cos(z) * num,
      z: Math.sin(x)
    };
  }

  /**
   * Convert world coordinates to relative screen coordinates
   */
  static worldToScreenRel(worldCoords: Vector3): { success: boolean; x?: number; y?: number } {
    const [success, x, y] = GetScreenCoordFromWorldCoord(worldCoords.x, worldCoords.y, worldCoords.z);
    if (!success) {
      return { success: false };
    }
    
    const screenCoordsX = (x - 0.5) * 2.0;
    const screenCoordsY = (y - 0.5) * 2.0;
    return { success: true, x: screenCoordsX, y: screenCoordsY };
  }
  /**
   * Simplified screen-to-world raycast (basic implementation)
   * Note: This is a simplified version. For complex screen-to-world conversion, 
   * consider implementing the full CameraHelper.ScreenToWorld method
   */
  static raycastToScreenPosition(screenX: number, screenY: number, ignoreEntity: number = 0, maxDistance: number = 100.0): {
    hit: boolean;
    coords: Vector3;
    normal: Vector3;
    entity: number;
  } {
    const camPos = GetGameplayCamCoord();
    const camRot = GetGameplayCamRot(2);
    
    const startCoords = { x: camPos[0], y: camPos[1], z: camPos[2] };
    const rotation = { x: camRot[0], y: camRot[1], z: camRot[2] };
    
    // Convert rotation to direction
    const direction = this.rotToDirection(rotation);
    
    // Apply screen offset to direction (simplified approach)
    // In a full implementation, this would use proper screen-to-world matrix calculations
    const offsetDirection = {
      x: direction.x + (screenX - 0.5) * 0.1, // Simple offset based on screen position
      y: direction.y + (screenY - 0.5) * 0.1,
      z: direction.z
    };
    
    // Calculate target position based on modified direction
    const targetCoords = {
      x: startCoords.x + offsetDirection.x * maxDistance,
      y: startCoords.y + offsetDirection.y * maxDistance,
      z: startCoords.z + offsetDirection.z * maxDistance
    };

    const raycast = StartExpensiveSynchronousShapeTestLosProbe(
      startCoords.x, startCoords.y, startCoords.z,
      targetCoords.x, targetCoords.y, targetCoords.z,
      -1, // All entities
      ignoreEntity,
      4
    );
    
    const [hit, coords, normal, , entity] = GetShapeTestResult(raycast);
    
    const result = {
      hit: hit === 1,
      coords: { x: coords[0], y: coords[1], z: coords[2] },
      normal: { x: normal[0], y: normal[1], z: normal[2] },
      entity
    };
    
    // Debug visualization
    if (isDebugMode()) {
      addDebugLine(startCoords, targetCoords, { r: 0, g: 255, b: 255, a: 255 });
      if (result.hit) {
        addDebugSphere(result.coords, 0.1, { r: 255, g: 0, b: 255, a: 255 });
      }
    }
    
    return result;
  }
  /**
   * Get entity at the center of the screen (similar to CameraHelper example)
   */
  static getEntityAtCenterScreen(maxDistance: number = 10.0): number {
    const result = this.raycastToScreenPosition(0.5, 0.5, PlayerPedId(), maxDistance);
    return result.entity;
  }
  /**
   * Enhanced raycast from camera using improved CameraHelper-based math
   */
  static raycastFromCamera(maxDistance: number): {
    hit: boolean;
    coords: Vector3;
    normal: Vector3;
    entity: number;
    projectedCoords: Vector3; // Always available - where you're looking at fixed distance
  } {
    const playerPed = PlayerPedId();
    
    // Get camera position and rotation using FiveM methods
    const camPos = GetGameplayCamCoord();
    const camRot = GetGameplayCamRot(2); // World space rotation
    
    const startCoords = { 
      x: camPos[0], 
      y: camPos[1], 
      z: camPos[2] 
    };
    
    const rotation = {
      x: camRot[0],
      y: camRot[1], 
      z: camRot[2]
    };
    
    // Convert rotation to direction vector using enhanced CameraHelper method
    const direction = this.rotToDirection(rotation);
    
    // Calculate projected position at fixed distance (always available)
    const projectedCoords = {
      x: startCoords.x + direction.x * maxDistance,
      y: startCoords.y + direction.y * maxDistance,
      z: startCoords.z + direction.z * maxDistance
    };

    const raycast = StartExpensiveSynchronousShapeTestLosProbe(
      startCoords.x, startCoords.y, startCoords.z,
      projectedCoords.x, projectedCoords.y, projectedCoords.z,
      1, // World collision
      playerPed, // Ignore player
      4
    );
    
    const [hit, coords, normal, , entity] = GetShapeTestResult(raycast);
    
    const result = {
      hit: hit === 1,
      coords: { x: coords[0], y: coords[1], z: coords[2] },
      normal: { x: normal[0], y: normal[1], z: normal[2] },
      entity,
      projectedCoords // Always provide where you're looking at fixed distance
    };
    
    // Debug logging
    if (isDebugMode()) {
      console.log(`[Raycast Enhanced] Camera pos: ${JSON.stringify(startCoords)}, Camera rot: ${JSON.stringify(rotation)}, Direction: ${JSON.stringify(direction)}, Hit: ${result.hit}, Hit coords: ${JSON.stringify(result.coords)}, Projected: ${JSON.stringify(result.projectedCoords)}`);
    }
    
    // Debug visualization
    if (isDebugMode()) {
      // Draw raycast line to projected position (enhanced visualization)
      addDebugLine(startCoords, projectedCoords, { r: 255, g: 0, b: 0, a: 255 });
      
      // Draw hit point if we hit something
      if (result.hit) {
        addDebugSphere(result.coords, 0.1, { r: 0, g: 255, b: 0, a: 255 });
      }
      
      // Always draw projected position (where vehicle will be if no hit)
      addDebugSphere(projectedCoords, 0.05, { r: 255, g: 255, b: 0, a: 255 });
    }
    
    return result;
  }

  /**
   * Perform raycast between two points
   */
  static raycast(start: Vector3, end: Vector3, flags: number = 1): {
    hit: boolean;
    coords: Vector3;
    normal: Vector3;
    entity: number;
  } {
    const raycast = StartExpensiveSynchronousShapeTestLosProbe(
      start.x, start.y, start.z,
      end.x, end.y, end.z,
      flags,
      0,
      4
    );
    
    const [hit, coords, normal, , entity] = GetShapeTestResult(raycast);
    
    const result = {
      hit: hit === 1,
      coords: { x: coords[0], y: coords[1], z: coords[2] },
      normal: { x: normal[0], y: normal[1], z: normal[2] },
      entity
    };
    
    // Debug visualization
    if (isDebugMode()) {
      // Draw raycast line
      const finalCoords = result.hit ? result.coords : end;
      addDebugLine(start, finalCoords, { r: 255, g: 0, b: 0, a: 255 });
      
      // Draw hit point if we hit something
      if (result.hit) {
        addDebugSphere(result.coords, 0.1, { r: 0, g: 255, b: 0, a: 255 });
      }
    }
    
    return result;
  }

  /**
   * Perform ground raycast from position
   */
  static raycastToGround(position: Vector3, maxDistance: number = 100.0): {
    hit: boolean;
    coords: Vector3;
    normal: Vector3;
    entity: number;
  } {
    const start = { ...position };
    const end = { x: position.x, y: position.y, z: position.z - maxDistance };
    
    return this.raycast(start, end, 1);
  }
}

// Notification utilities
export class NotificationUtils {  /**
   * Show a notification to the player
   */
  static show(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    // Add type prefix to message for better UX
    const prefix = type === 'success' ? '✓ ' : type === 'error' ? '✗ ' : 'ℹ ';
    const fullMessage = `${prefix}${message}`;
    
    SetNotificationTextEntry('STRING');
    AddTextComponentString(fullMessage);
    DrawNotification(false, false);
  }

  /**
   * Show help text
   */
  static showHelp(message: string): void {
    BeginTextCommandDisplayHelp('STRING');
    AddTextComponentSubstringPlayerName(message);
    EndTextCommandDisplayHelp(0, false, true, -1);
  }
}

// Key binding utilities
export class KeyUtils {
  /**
   * Check if a key was just pressed this frame
   */
  static isKeyJustPressed(key: number): boolean {
    const pressed = IsControlJustPressed(0, key);
    if (pressed && isDebugMode()) {
      console.log(`[KeyUtils] Key ${key} just pressed`);
    }
    return pressed;
  }

  /**
   * Check if a key is currently being held
   */
  static isKeyPressed(key: number): boolean {
    return IsControlPressed(0, key);
  }

  /**
   * Disable all controls except camera
   */  static disableAllControlsExceptCamera(): void {
    DisableAllControlActions(0);
    EnableControlAction(0, 1, true);  // LookLeftRight
    EnableControlAction(0, 2, true);  // LookUpDown
    EnableControlAction(0, 106, true); // VehicleMouseControlOverride
    EnableControlAction(0, 200, true); // PauseMenu (ESC)
    EnableControlAction(0, 180, true); // Mouse wheel up
    EnableControlAction(0, 181, true); // Mouse wheel down
    EnableControlAction(0, 241, true);  // Scrollwheel up (weapon wheel)
    EnableControlAction(0, 14, true);  // Scrollwheel down (weapon wheel)
    EnableControlAction(0, 191, true); // Enter/Accept
  }
  // FiveM Control IDs for common keys - mapped from controls.json
  static readonly CONTROLS = {
    ENTER: 191,           // INPUT_FRONTEND_ACCEPT - "ENTER / NUMPAD ENTER"
    ESC: 200,             // INPUT_FRONTEND_PAUSE_ALTERNATE - "ESC"
    CANCEL: 202,          // INPUT_FRONTEND_CANCEL - "BACKSPACE / ESC"
    SCROLLWHEEL_UP: 241,   // INPUT_WEAPON_WHEEL_PREV - "SCROLLWHEEL UP"
    SCROLLWHEEL_DOWN: 14, // INPUT_WEAPON_WHEEL_NEXT - "SCROLLWHEEL DOWN"
    MOUSE_WHEEL_UP: 180,  // INPUT_CURSOR_SCROLL_UP
    MOUSE_WHEEL_DOWN: 181 // INPUT_CURSOR_SCROLL_DOWN
  } as const;
}

// Vehicle utilities
export class VehicleUtils {
  /**
   * Check if a vehicle model is valid
   */
  static isValidModel(modelName: string): boolean {
    const hash = GetHashKey(modelName);
    return IsModelInCdimage(hash) && IsModelAVehicle(hash);
  }

  /**
   * Request and wait for vehicle model to load
   */
  static async requestModel(modelName: string): Promise<boolean> {
    const hash = GetHashKey(modelName);
    
    if (!this.isValidModel(modelName)) {
      return false;
    }

    RequestModel(hash);
    
    const timeout = GetGameTimer() + 10000; // 10 second timeout
    while (!HasModelLoaded(hash) && GetGameTimer() < timeout) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    return HasModelLoaded(hash);
  }
  /**
   * Create a ghost vehicle (semi-transparent) - Method 1: Standard approach
   */
  static createGhostVehicle(modelHash: number, coords: Vector3, heading: number): number {
    const vehicle = CreateVehicle(modelHash, coords.x, coords.y, coords.z, heading, false, false);
    
    if (DoesEntityExist(vehicle)) {
      SetEntityAlpha(vehicle, DEFAULT_VEHICLE_PLACEMENT_CONFIG.ghostAlpha, false);
      SetEntityCollision(vehicle, false, false);
      FreezeEntityPosition(vehicle, true);
      SetVehicleEngineOn(vehicle, false, false, false);
      SetEntityAsMissionEntity(vehicle, true, true);
    }
    
    return vehicle;
  }

  /**
   * Spawn a ghost vehicle in front of the camera at a given distance (simple method)
   */
  static spawnGhostVehicleSimple(modelHash: number, distance: number = DEFAULT_VEHICLE_PLACEMENT_CONFIG.maxDistance): number {
    // Use raycast from camera to determine spawn position (hit point or projected)
    const ray = PositionUtils.raycastFromCamera(distance);
    const coords = ray.hit ? ray.coords : ray.projectedCoords;
    // Get camera heading for vehicle orientation
    const camRot = GetGameplayCamRot(2);
    const heading = camRot[2];
    // Spawn the ghost at the computed coords with camera heading
    return this.createGhostVehicle(modelHash, coords, heading);
  }
}
