import React, { useEffect, useState, useRef } from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import { getTimeAgo, parseDate } from '../../../utils/timeUtils';

interface StoryViewerProps {
  storyId: number | null;
  onClose: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

const StoryViewer: React.FC<StoryViewerProps> = ({ storyId, onClose, onNext, onPrevious }) => {
  const { stories, profiles } = useLifeSnapStore();
  const [isPaused] = useState(false);
  const progressRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const story = storyId ? stories.find(s => s.id === storyId) : undefined;

  // Helper function to check if a date is active (not expired)
  const isDateActive = (dateValue: Date | number | string | undefined): boolean => {
    const date = parseDate(dateValue);
    if (!date) return false;

    const now = new Date();
    return date > now;
  };

  // Helper function to safely get timestamp value
  const getTimeValue = (timestamp: Date | number | string | undefined) => {
    const date = parseDate(timestamp);
    return date ? date.getTime() : 0;
  };

  const userStories = stories
    .filter(s => s.userId === story?.userId && isDateActive(s.expiresAt))
    .sort((a, b) => {
      const aTime = getTimeValue(a.timestamp);
      const bTime = getTimeValue(b.timestamp);
      return aTime - bTime; // Oldest first for story progression
    });

  const currentStoryIndex = userStories.findIndex(s => s.id === storyId);

  useEffect(() => {
    if (!story) return;

    // viewStory(story.id);

    // Reset and start progress
    if (progressRef.current) {
      progressRef.current.style.width = '0%';
      void progressRef.current.offsetWidth; // Force reflow
      progressRef.current.style.width = '100%';
    }

    // Clear existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // Set new timer
    timerRef.current = setTimeout(() => {
      onNext?.();
    }, 5000);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [storyId, onNext, story]);

  if (!story) return null;

  const profile = profiles.find(p => p.id === story.userId);
  const storyTime =
    story.timestamp instanceof Date
      ? story.timestamp.getTime()
      : typeof story.timestamp === 'number'
      ? story.timestamp
      : new Date(story.timestamp).getTime();
  const timeAgo = getTimeAgo(storyTime);

  return (
    <div className="fixed inset-0 bg-gray-900 z-50 flex flex-col pt-6">
      {/* Story progress bars */}
      <div className="absolute top-0 left-0 right-0 flex gap-1 p-8">
        {userStories.map((s, index) => (
          <div key={s.id} className="h-0.5 bg-white/30 flex-1 overflow-hidden">
            <div
              key={`progress-${s.id}-${currentStoryIndex}`}
              ref={index === currentStoryIndex ? progressRef : null}
              className={`h-full bg-white ${
                index < currentStoryIndex
                  ? 'w-full transition-none'
                  : index === currentStoryIndex
                  ? 'w-0 transition-all duration-[5000ms] ease-linear transition-discrete'
                  : 'w-0 transition-none'
              }`}
              style={{
                transitionBehavior: isPaused ? 'normal' : 'allow-discrete',
                width:
                  index === currentStoryIndex
                    ? undefined
                    : index < currentStoryIndex
                    ? '100%'
                    : '0%'
              }}
            />
          </div>
        ))}
      </div>

      {/* Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-2">
          <img
            src={profile?.profilePicture}
            alt={profile?.username}
            className="w-8 h-8 rounded-full object-cover"
          />
          <div className="flex flex-col">
            <span className="text-xs font-bold text-white">
              {profile?.username || 'Unknown User'}
            </span>
            <span className="text-[10px] text-white/60">{timeAgo}</span>
          </div>
        </div>
        <button onClick={onClose} className="text-white/80 hover:text-white">
          <i className="fas fa-times text-lg" />
        </button>
      </div>

      {/* Story Content */}
      <div className="flex-1 relative">
        <img
          src={story.imageUrl}
          alt="Story"
          className="absolute inset-0 w-full h-full object-cover"
        />

        {/* Touch areas for navigation */}
        <div className="absolute inset-0 flex">
          <div
            className="w-1/2 h-full"
            onClick={e => {
              e.stopPropagation();
              onPrevious?.();
            }}
          />
          <div
            className="w-1/2 h-full"
            onClick={e => {
              e.stopPropagation();
              onNext?.();
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default StoryViewer;
