/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs');
const path = require('path');

// Load the original JSON (strip comments)
const weaponsJsonPath = path.join(__dirname, 'weapons.json');
let text = fs.readFileSync(weaponsJsonPath, 'utf8');
// Remove single-line comments
text = text.replace(/\/\/.*$/gm, '');
const weaponsJson = JSON.parse(text);

// Only include weapons from these categories in the inventory UI
const desiredCategories = [
    'GROUP_MELEE',
    'GROUP_PISTOL',
    'GROUP_SMG',
    'GROUP_RIFLE',
    'GROUP_MG',
    'GROUP_SHOTGUN',
    'GROUP_SNIPER',
    'GROUP_STUNGUN',
    'GROUP_HEAVY',
    'GROUP_THROWN',
    'GROUP_FIREEXTINGUISHER',
    'GROUP_PETROLCAN'
];

// Function to map weapon names to their corresponding image filenames
function getWeaponImageName(weaponName, category) {
    // Create a mapping object for specific weapon names to their image files
    const weaponImageMap = {
        // Melee weapons
        'WEAPON_KNIFE': 'W_ME_Knife.png',
        'WEAPON_NIGHTSTICK': 'W_ME_Nightstick.png',
        'WEAPON_HAMMER': 'W_ME_Hammer.png',
        'WEAPON_BAT': 'W_ME_Bat.png',
        'WEAPON_GOLFCLUB': 'W_ME_GClub.png',
        'WEAPON_CROWBAR': 'W_ME_Crowbar.png',
        'WEAPON_BOTTLE': 'W_ME_Bottle.png',
        'WEAPON_DAGGER': 'W_ME_Dagger.png',
        'WEAPON_HATCHET': 'W_ME_Hatchet.png',
        'WEAPON_KNUCKLE': 'W_ME_KnuckleDuster.png',
        'WEAPON_MACHETE': 'W_ME_Machete.png',
        'WEAPON_FLASHLIGHT': 'W_ME_Torch.png',
        'WEAPON_SWITCHBLADE': 'W_ME_Switchblade.png',
        'WEAPON_POOLCUE': 'W_ME_PoolCue.png',
        'WEAPON_WRENCH': 'W_ME_Wrench.png',
        'WEAPON_BATTLEAXE': 'W_ME_BattleAxe.png',
        'WEAPON_STONE_HATCHET': 'W_ME_StoneHatchet.png',
        
        // Pistols
        'WEAPON_PISTOL': 'W_PI_Pistol.png',
        'WEAPON_COMBATPISTOL': 'W_PI_CombatPistol.png',
        'WEAPON_APPISTOL': 'W_PI_APPistol.png',
        'WEAPON_PISTOL50': 'W_PI_Pistol50.png',
        'WEAPON_REVOLVER': 'W_DA_Revolver.png',
        
        // SMGs
        'WEAPON_MICROSMG': 'W_SB_MicroSMG.png',
        'WEAPON_SMG': 'W_SB_SMG.png',
        'WEAPON_ASSAULTSMG': 'W_SB_AssaultSMG.png',
        
        // Rifles
        'WEAPON_ASSAULTRIFLE': 'W_AR_AssaultRifle.png',
        'WEAPON_CARBINERIFLE': 'W_AR_CarbineRifle.png',
        'WEAPON_CARBINERIFLE_MK2': 'W_AR_CarbineRifleMK2.png',
        'WEAPON_ADVANCEDRIFLE': 'W_AR_AdvancedRifle.png',
        'WEAPON_SPECIALCARBINE': 'W_AR_SpecialCarbine.png',
        'WEAPON_BULLPUPRIFLE': 'W_AR_BullpupRifle.png',
        'WEAPON_COMPACTRIFLE': 'W_AR_CompactRifle.png',
        'WEAPON_BATTLERIFLE': 'W_Win_BattleRifle.png',
        
        // Machine Guns
        'WEAPON_MG': 'W_MG_MG.png',
        'WEAPON_COMBATMG': 'W_MG_CombatMG.png',
        'WEAPON_GUSENBERG': 'W_SB_Gusenberg.png',
        
        // Shotguns
        'WEAPON_PUMPSHOTGUN': 'W_SG_PumpShotgun.png',
        'WEAPON_SAWNOFFSHOTGUN': 'W_SG_SAWNOFF.png',
        'WEAPON_ASSAULTSHOTGUN': 'W_SG_AssaultShotgun.png',
        'WEAPON_BULLPUPSHOTGUN': 'W_SG_BullpupShotgun.png',
        'WEAPON_MUSKET': 'W_AR_Musket.png',
        'WEAPON_HEAVYSHOTGUN': 'W_SG_HeavyShotgun.png',
        'WEAPON_DBSHOTGUN': 'W_SG_DoubleBarrel.png',
        'WEAPON_AUTOSHOTGUN': 'W_SG_AutoShotgun.png',
        
        // Sniper Rifles
        'WEAPON_SNIPERRIFLE': 'W_SR_SniperRifle.png',
        'WEAPON_HEAVYSNIPER': 'W_SR_HeavySniper.png',
        'WEAPON_MARKSMANRIFLE': 'W_SR_MarksmanRifle.png',
        
        // Heavy Weapons
        'WEAPON_GRENADELAUNCHER': 'W_LR_GrenadeLauncher.png',
        'WEAPON_GRENADELAUNCHER_SMOKE': 'W_LR_GrenadeLauncher_smoke.png',
        'WEAPON_RPG': 'W_LR_RPG.png',
        'WEAPON_PASSENGER_ROCKET': 'W_LR_PassengerRocket.png',
        'WEAPON_AIRSTRIKE_ROCKET': 'W_LR_AirstrikeRocket.png',
        'WEAPON_STINGER': 'W_LR_Stinger.png',
        'WEAPON_MINIGUN': 'W_MG_Minigun.png',
        'WEAPON_FIREWORK': 'W_LR_Firework.png',
        'WEAPON_RAILGUN': 'W_AR_Railgun.png',
        'WEAPON_HOMINGLAUNCHER': 'W_LR_HomingLauncher.png',
        'WEAPON_COMPACTLAUNCHER': 'W_LR_CompactLauncher.png',
        'WEAPON_RAYMINIGUN': 'W_MG_RayMinigun.png',
        
        // Thrown/Explosive
        'WEAPON_MOLOTOV': 'W_EX_Molotov.png',
        'WEAPON_GRENADE': 'W_EX_GrenadeSmoke.png',
        'WEAPON_PIPEBOMB': 'W_EX_PipeBomb.png',
        'WEAPON_PROXIMITYMINE': 'W_EX_APmine.png',
        'WEAPON_STICKYBOMB': 'W_EX_C4.png',
        'WEAPON_SMOKEGRENADE': 'W_EX_GrenadeSmoke.png',
        'WEAPON_BZGAS': 'W_EX_BZGas.png',
        'WEAPON_BALL': 'W_EX_Ball.png',
        'WEAPON_SNOWBALL': 'W_EX_Snowball.png',
        'WEAPON_FLARE': 'W_EX_Flare.png',
          // Special
        'WEAPON_STUNGUN': 'W_PI_StunGun.png',
        'WEAPON_PETROLCAN': 'W_AM_JerryCan.png',
        'WEAPON_HAZARDCAN': 'W_AM_HazardCan.png',
        'WEAPON_FERTILIZERCAN': 'W_AM_FertilizerCan.png',
        'WEAPON_FIREEXTINGUISHER': 'W_AM_FireExtinguisher.png',
        'WEAPON_PARACHUTE': 'W_AM_Parachute.png',
    };

    // Check if we have a specific mapping for this weapon
    if (weaponImageMap[weaponName]) {
        return weaponImageMap[weaponName];
    }

    // Fallback to original naming convention if no specific mapping found
    return weaponName.toLowerCase();
}

// Filter valid weapon entries by name, translation, ammoType, and desired categories
const validWeapons = weaponsJson.filter(w => {
    // Must have a name that starts with WEAPON_
    if (!w.Name || !w.Name.startsWith('WEAPON_')) return false;
    
    // Must have ammoType (allow null, we'll handle it)
    if (w.AmmoType === undefined) return false;
    
    // Must be in desired categories
    if (!desiredCategories.includes(w.Category)) return false;
    
    // For TranslatedLabel, we'll use the name if null
    return true;
});

// Map to WeaponDefinition (which extends ItemDefinition)
const out = validWeapons.map(w => {
    let mappedComponents;
    if (w.Components && Array.isArray(w.Components)) {
        mappedComponents = w.Components.map(comp => {
            if (typeof comp !== 'object' || comp === null || !comp.Name) {
                return null; // Skip malformed components
            }
            const componentData = {
                name: comp.Name,
                attachBone: comp.AttachBone || 'gun_root', // Default if not present
                label: comp.TranslatedLabel?.English || comp.Name, // Fallback to comp.Name
                // Description is optional, so it can be undefined if not present
            };
            if (comp.TranslatedDescription?.English) {
                componentData.description = comp.TranslatedDescription.English;
            }
            if (comp.Variants) { // Add variants if they exist
                componentData.variants = comp.Variants;
            }
            return componentData;
        }).filter(comp => comp !== null);

        if (mappedComponents.length === 0) {
            mappedComponents = undefined; // Don't include components property if array is empty
        }
    }    const weaponData = {
        id: w.Name,
        name: w.Name,
        hash: w.Hash, // Add hash field from weapons.json
        label: (w.TranslatedLabel && w.TranslatedLabel.English) ? w.TranslatedLabel.English : w.Name,
        description: (w.TranslatedDescription && w.TranslatedDescription.English) ? w.TranslatedDescription.English : 
                    (w.TranslatedLabel && w.TranslatedLabel.English) ? w.TranslatedLabel.English : w.Name,
        type: 'weapon',
        weight: w.Weight || 1000,
        stackable: false,
        icon: getWeaponImageName(w.Name, w.Category), // Use the new mapping function
        usable: true,
        destroyable: true,
        maxDurability: w.Durability !== undefined ? parseFloat(w.Durability) : 100,
        ammoType: w.AmmoType || 'NULL',
        weaponCategory: w.Category,
    };

    if (mappedComponents) {
        weaponData.components = mappedComponents;
    }
    return weaponData;
});

// Build TypeScript file content
const tsHeader = `// THIS FILE IS AUTO-GENERATED. DO NOT EDIT.
// Generated by scripts/shared/items/generate-weapons.js

import { ItemType } from "../types/items.types"; // Corrected import path
import type { ItemDefinition } from "../types/items.types"; // Corrected import path

export interface WeaponComponent {
  name: string;
  attachBone: string;
  label?: string;
  description?: string;
  variants?: any[];
}

/**
 * Extends ItemDefinition with weapon-specific static properties.
 * This represents the blueprint for a type of weapon.
 */
export interface WeaponDefinition extends ItemDefinition {
  hash: number;
  ammoType: string;
  weaponCategory: string;
  components?: WeaponComponent[];
}

export const weaponDefinitions: Record<string, WeaponDefinition> = `;

// Correctly use ItemType.WEAPON in the generated objects
const processedOut = out.map(item => ({
    ...item,
    type: 'ItemType.WEAPON' // Special string to be handled by custom stringifier
}));

function formatTsValue(key, value, indentLevel = 0) {
    const currentPropertyIndent = '  '.repeat(indentLevel + 1);
    const containerClosingBraceIndent = '  '.repeat(indentLevel);

    if (key === 'type' && value === 'ItemType.WEAPON') {
        return 'ItemType.WEAPON';
    }
    if (typeof value === 'string') {
        // Corrected string escaping for JSON-like output within TS template literal
        const escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r');
        return `'${escapedValue}'`; // Use single quotes for strings in TS output for simplicity, can be changed to double if preferred
    }
    if (typeof value === 'boolean' || typeof value === 'number') {
        return String(value);
    }
    if (value === null) {
        return 'null';
    }
    if (value === undefined) {
        return 'undefined';
    }
    if (Array.isArray(value)) {
        if (value.length === 0) return '[]';
        const elements = value.map(element =>
            `${currentPropertyIndent}${formatTsValue('', element, indentLevel + 1)}`
        ).join(',\n');
        return `[\n${elements}\n${containerClosingBraceIndent}]`;
    }
    if (typeof value === 'object') {
        const properties = Object.entries(value)
            .filter(([, val]) => val !== undefined)
            .map(([k, v]) => `${currentPropertyIndent}${k}: ${formatTsValue(k, v, indentLevel + 1)}`)
            .join(',\n');
        return `{\n${properties}\n${containerClosingBraceIndent}}`;
    }
    return String(value); // Fallback
}

const itemsString = processedOut
    .map(item => { // Each 'item' is a weapon
        const properties = Object.entries(item)
            .filter(([, value]) => value !== undefined)
            .map(([key, value]) => {
                return `    ${key}: ${formatTsValue(key, value, 1)}`;
            })
            .join(',\n');
        return `  ${item.name}: {\n${properties}\n  }`;
    })
    .join(',\n');

let tsContent = `${tsHeader}{\n${itemsString}\n};`;

// Write to weapons.ts (overwrite)
const targetFile = path.join(__dirname, 'weapons.ts');
fs.writeFileSync(targetFile, tsContent);
console.log(`Generated ${out.length} weapons in ${targetFile}`);
