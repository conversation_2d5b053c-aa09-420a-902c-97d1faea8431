// HUD Data interface - Optimized for state bag system
export interface HudData {
    health: number;
    armor: number;
    oxygen: number;
    hunger: number;
    thirst: number;
    stress: number;
    vehicle: {
        isInVehicle: boolean;
        showSpeedometer?: boolean;
        speed: number;
        fuel: number;
        engineHealth: number;
        bodyHealth: number;
        gear?: number | string; // Support for 'N' (neutral) and 'R' (reverse)
        seatbelt: boolean;
        nitrous?: number; // Added for future nitrous system integration
        rpmRatio?: number; // RPM ratio (0-1) for speedometer needle
    };
    location?: {
        streetName: string;
        crossingName?: string;
        zoneName?: string;
    };
}

// HUD Settings interface - Simplified for performance
export interface HudSettings {
    show: {
        health: boolean;
        armor: boolean;
        oxygen: boolean;
        vehicle: boolean;
        location: boolean;
    };
    position: {
        statusBars: 'left' | 'right' | 'bottom';
        vehicle: 'left' | 'right' | 'bottom';
    };
    colors: {
        health: string;
        armor: string;
        oxygen: string;
    };
    scale: number;
}

// Default HUD settings - Performance-focused minimal design
export const DEFAULT_HUD_SETTINGS: HudSettings = {
    show: {
        health: true,
        armor: true,
        oxygen: true,   // Only when underwater
        vehicle: true,  // Only when in vehicle
        location: true  // Only when in vehicle
    },
    position: {
        statusBars: 'left',
        vehicle: 'right'
    },
    colors: {
        health: '#ef4444',
        armor: '#3b82f6',
        oxygen: '#06b6d4'
    },
    scale: 1.0
};
