import React from 'react';
import { StoreApp } from '../../common/types/appTypes';
import { useAppStore } from '../../common/stores/appStore';

/**
 * Modern Glassmorphism AppStore Design
 * Features:
 * - Clean, centered title
 * - Modern search bar with glass effect
 * - Streamlined app cards with app info and compact screenshots
 * - No back button (uses pull indicator to return to home)
 * - No featured app section
 */
const AppStore: React.FC = () => {
  const { apps, toggleAppInstallation } = useAppStore();

  // Get all store apps
  const getStoreApps = () => {
    return apps.filter((app): app is StoreApp => app.type === 'store');
  };

  // Get download count (mock data)
  const getDownloadCount = (appId: number) => {
    const counts = {
      6: '12.5K',
      7: '8.2K',
      8: '15.7K',
      9: '45.3K',
      10: '32.1K',
      11: '28.9K',
      12: '19.4K',
      13: '22.8K',
      14: '10.6K'
    };
    return counts[appId as keyof typeof counts] || '10K+';
  };

  return (
    <div className="h-full w-full flex flex-col bg-gradient-to-br from-[#0f172a] to-[#1e293b] pt-8 pb-3">
      {/* Header */}
      <div className="px-4 py-3">
        <div className="flex items-center justify-center mb-4">
          <div className="text-white font-bold text-xl">App Store</div>
        </div>

        {/* Search Bar */}
        <div className="bg-white/10 backdrop-blur-lg rounded-full px-4 py-2 flex items-center mb-4">
          <i className="fas fa-search text-white/50 mr-2"></i>
          <input
            type="text"
            placeholder="Search apps..."
            className="bg-transparent text-white outline-none w-full text-sm"
          />
        </div>
      </div>

      {/* No category tabs or featured app section as requested */}

      {/* App Grid */}
      <div className="flex-grow overflow-y-auto px-4">
        <div className="grid grid-cols-1 gap-4">
          {getStoreApps().map(app => (
            <div
              key={app.id}
              className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden hover:bg-white/15 transition-all"
            >
              <div className="p-4">
                <div className="flex items-center gap-4">
                  <div
                    className={`${app.colors.bg} w-14 h-14 rounded-xl flex items-center justify-center`}
                  >
                    <i className={`fas fa-${app.icon} text-white text-xl`}></i>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-white font-bold">{app.name}</div>
                        <div className="text-white/40 text-xs">
                          {getDownloadCount(app.id)} downloads
                        </div>
                      </div>
                      <button
                        onClick={() => toggleAppInstallation(app.id)}
                        className={`px-4 py-1.5 rounded-full text-xs font-medium transition-all ${
                          (app.settings as { installed: boolean }).installed
                            ? 'bg-white/10 text-white hover:bg-white/20'
                            : 'bg-blue-500/80 text-white hover:bg-blue-500'
                        }`}
                      >
                        {(app.settings as { installed: boolean }).installed ? 'Uninstall' : 'Get'}
                      </button>
                    </div>
                  </div>
                </div>
                <div className="mt-3 text-white/60 text-xs">{app.description}</div>

                {/* App Screenshots (mockup) */}
                <div className="mt-3 flex space-x-2 overflow-x-auto pb-2">
                  {[1, 2, 3].map(i => (
                    <div
                      key={i}
                      className="flex-shrink-0 w-20 h-32 bg-black/30 rounded-lg overflow-hidden"
                      style={{
                        backgroundImage: `url(https://picsum.photos/200/350?random=${
                          app.id * 10 + i
                        })`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AppStore;
