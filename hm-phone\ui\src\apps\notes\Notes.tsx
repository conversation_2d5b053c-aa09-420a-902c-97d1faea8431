import React, { useState } from 'react';
import { useNavigation } from '../../navigation/hooks';
// Navigation is now handled by the navigation system
import NotesList from './components/NotesList';
import NoteEditor from './components/NoteEditor';
import { useNavigationStore } from '../../navigation/navigationStore';

const Notes: React.FC = () => {
  const { openView } = useNavigation();
  const { currentView } = useNavigationStore();
  // Get the current navigation entry and its data
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};

  // Get note ID from navigation data
  const selectedNoteId = navigationData.noteId ? Number(navigationData.noteId) : null;

  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="flex h-full w-full flex-col bg-[#0a0f1a] pt-8 pb-3">
      {currentView === 'main' || !currentView ? (
        <>
          {/* Search Bar */}
          <div className="px-4 py-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search notes..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full rounded-full bg-white/20 backdrop-blur-sm px-4 py-2 pl-9 text-white
                         placeholder:text-white/70 focus:outline-none focus:ring-2
                         focus:ring-white/30 transition-all duration-200 shadow-md"
              />
              <i
                className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2
                        text-white/60"
              />
            </div>
          </div>

          {/* Notes List */}
          <NotesList
            searchQuery={searchQuery}
            onNoteSelect={id => openView('editor', { noteId: id })}
          />

          {/* Add Note Button */}
          <button
            onClick={() => openView('editor', { noteId: -1 })}
            className="absolute bottom-20 right-4 flex h-12 w-12 items-center
                     justify-center rounded-full bg-blue-500 shadow-lg
                     hover:bg-blue-600 active:bg-blue-700 transition-colors
                     duration-200"
          >
            <i className="fas fa-plus text-white" />
          </button>
        </>
      ) : (
        <NoteEditor noteId={selectedNoteId || -1} onClose={() => openView('main')} />
      )}
    </div>
  );
};

export default Notes;
