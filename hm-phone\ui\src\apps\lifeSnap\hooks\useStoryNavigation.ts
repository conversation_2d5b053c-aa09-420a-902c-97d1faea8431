/**
 * Custom hook for story navigation
 */

import { useCallback } from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import { useNavigation } from '../../../navigation/hooks';
import {
  findNextStoryInSameUser,
  findPrevStoryInSameUser,
  findFirstStoryOfNextUser,
  findLastStoryOfPrevUser
} from '../utils/storyNavigationUtils';

interface UseStoryNavigationProps {
  selectedStoryId: number | null;
}

interface UseStoryNavigationReturn {
  handleStoryNavigation: (direction: 'next' | 'previous') => void;
  handleStoryClose: () => void;
  handleStoryClick: (storyId: number) => void;
}

export const useStoryNavigation = ({
  selectedStoryId
}: UseStoryNavigationProps): UseStoryNavigationReturn => {
  const store = useLifeSnapStore();
  const { openView } = useNavigation();

  /**
   * Navigate to the next or previous story
   */
  const handleStoryNavigation = useCallback(
    (direction: 'next' | 'previous') => {
      if (!selectedStoryId || !store.stories || store.stories.length === 0) {
        openView('main', {}, { replace: true });
        return;
      }

      // Try to find the next/previous story in the same user's stories
      if (direction === 'next') {
        const nextStory = findNextStoryInSameUser(store.stories, selectedStoryId);
        if (nextStory) {
          openView('story', { storyId: nextStory.id }, { replace: true });
          return;
        }

        // If no next story in the same user, try to find the first story of the next user
        const firstStoryOfNextUser = findFirstStoryOfNextUser(store.stories, selectedStoryId);
        if (firstStoryOfNextUser) {
          openView('story', { storyId: firstStoryOfNextUser.id }, { replace: true });
          return;
        }
      } else {
        const prevStory = findPrevStoryInSameUser(store.stories, selectedStoryId);
        if (prevStory) {
          openView('story', { storyId: prevStory.id }, { replace: true });
          return;
        }

        // If no previous story in the same user, try to find the last story of the previous user
        const lastStoryOfPrevUser = findLastStoryOfPrevUser(store.stories, selectedStoryId);
        if (lastStoryOfPrevUser) {
          openView('story', { storyId: lastStoryOfPrevUser.id }, { replace: true });
          return;
        }
      }

      // If no next/previous story found, go back to the main view
      openView('main', {}, { replace: true });
    },
    [selectedStoryId, store.stories, openView]
  );

  /**
   * Close the story viewer and return to the main view
   */
  const handleStoryClose = useCallback(() => {
    openView('main', {}, { replace: true });
  }, [openView]);

  /**
   * Handle clicking on a story in the stories list
   */
  const handleStoryClick = useCallback(
    (storyId: number) => {
      console.log('Story clicked:', storyId);
      openView('story', { storyId }, { replace: true });
    },
    [openView]
  );

  return {
    handleStoryNavigation,
    handleStoryClose,
    handleStoryClick
  };
};

export default useStoryNavigation;
