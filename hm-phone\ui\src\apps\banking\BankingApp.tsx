import React from 'react';
import { useNavigationStore } from '../../navigation/navigationStore';
// Navigation is now handled by the navigation system
import AccountSelector from './components/AccountSelector';
import TransactionsList from './components/TransactionsList';
import TransferView from './components/TransferView';
import TransactionDetailView from './components/TransactionDetailView';

const BankingApp: React.FC = () => {
  const { currentView } = useNavigationStore();
  // Get the current navigation entry and its data
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};

  // Get transfer mode from navigation data
  const transferMode = (navigationData.mode as string) || 'send';

  // Define valid transfer modes
  type TransferMode = 'send' | 'request' | 'transfer';

  // Ensure transferMode is a valid value
  const safeTransferMode: TransferMode =
    (transferMode === 'send' || transferMode === 'request' || transferMode === 'transfer')
      ? transferMode as TransferMode
      : 'send';

  // Render different views based on currentView
  const renderView = () => {
    if (currentView === 'transfer') {
      return <TransferView mode={safeTransferMode} />;
    } else if (currentView === 'transaction' && navigationData.transactionId) {
      return <TransactionDetailView transactionId={Number(navigationData.transactionId)} />;
    } else {
      // Default to main view
      return (
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Fixed content */}
          <div className="flex-shrink-0">
            <AccountSelector />
            <div className="px-4 py-3">
              <div className="text-white/80 font-medium">Recent Transactions</div>
            </div>
          </div>

          {/* Scrollable transactions */}
          <div className="flex-1 min-h-0 scrollable">
            <TransactionsList />
          </div>
        </div>
      );
    }
  };

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 pb-3">
      {renderView()}
    </div>
  );
};

export default BankingApp;
