import { create } from 'zustand';
import config from '@shared/config';

// Define the camera settings interface
interface CameraSettings {
  flashEnabled: boolean;
  isSelfieMode: boolean;
  currentMode: 'photo' | 'video';
}

// Define the camera store state interface
interface CameraState {
  // State
  settings: CameraSettings;
  
  // Actions
  actions: {
    updateSettings: (settings: Partial<CameraSettings>) => void;
  };
}

// Create the camera store
export const useCameraStore = create<CameraState>((set) => ({
  // State
  settings: {
    flashEnabled: config.camera.defaultSettings.flashEnabled !== undefined 
      ? config.camera.defaultSettings.flashEnabled 
      : false,
    isSelfieMode: config.camera.defaultSettings.currentMode === 'selfie',
    currentMode: 'photo'
  },
  
  // Actions
  actions: {
    updateSettings: (newSettings: Partial<CameraSettings>) => {
      set(state => ({
        settings: {
          ...state.settings,
          ...newSettings
        }
      }));
    }
  }
}));