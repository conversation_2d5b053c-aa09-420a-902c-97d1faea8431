// Multicharacter shared config for customization

export interface CharacterPreviewConfig {
  interior: {
    name: string; // Name of the interior, e.g. "apartment_01"
    coords: [number, number, number]; // [x, y, z]
    heading: number;
    interiorId?: number; // Optional, for advanced setups
    ipls?: string[]; // Optional, list of IPLs for the interior
    camera: {
      pos: [number, number, number]; // Camera position [x, y, z]
      rot: [number, number, number]; // Camera rotation [pitch, yaw, roll]
      fov: number; // Field of view for the camera
    };
    useRoutingBucket?: boolean; // Whether to use a routing bucket for player isolation
    selectionBucketBase?: number; // Base number for selection bucket, if used
  };
  spawnPoints: Array<{
    coords: [number, number, number];
    heading: number;
    animation: string; // Animation dict@anim or scenario
  }>;
  defaultModels: string[]; // List of default ped models to use as placeholders
  maxCharacters?: number; // Add maxCharacters property
}

// Load preview config from JSON so it can be shared with UI
import previewConfigJson from './config.json';
export const MULTICHARACTER_PREVIEW_CONFIG: CharacterPreviewConfig = previewConfigJson as CharacterPreviewConfig;
