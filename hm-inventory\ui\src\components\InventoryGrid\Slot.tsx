import React, { useState, useRef } from 'react';
import { DragPanelType, InventoryItem, SlotType } from '@shared';
import { useDndKitSlot } from '../../hooks/useDndKitSlot';
import { useQuickAccessHelper } from '../../stores/inventoryStore';
import { useTooltip } from '../../hooks/useTooltip';
import { useContextMenu } from '../../hooks/useContextMenu';
import ItemTooltip from '../UI/ItemTooltip';
import ContextMenu from '../UI/ContextMenu';

interface SlotProps {
  slotIndex: number;
  panelType: DragPanelType;
  panelId?: string;
  slotType?: SlotType;
  inventoryItem?: InventoryItem;
  quantity?: number;
  isRequiredIngredient?: boolean;
  hasEnoughQuantity?: boolean;
  requiredQuantity?: number;
  hideQuickslotBadge?: boolean;
}

const slotTypeFaClass: Record<SlotType, string> = {
  [SlotType.PHONE]: 'fa-mobile-button',
  [SlotType.ARMOR]: 'fa-vest',
  [SlotType.PRIMARY_WEAPON]: 'fa-gun',
  [SlotType.TABLET]: 'fa-tablet-screen-button',
  [SlotType.BACKPACK]: 'fa-suitcase',
};

const Slot: React.FC<SlotProps> = ({
  slotIndex,
  inventoryItem,
  quantity,
  slotType,
  panelType,
  panelId,
  isRequiredIngredient = false,
  hasEnoughQuantity = true,
  requiredQuantity = 0,
  hideQuickslotBadge = false,
}) => {  // Determine display quantity: prefer prop, else fallback to inventoryItem.quantity or 0
  const displayQuantity = typeof quantity === 'number' ? quantity : inventoryItem?.quantity ?? 0;
    // Check if this slot is configured for quickaccess preview
  const { getQuickAccessInfo } = useQuickAccessHelper();
  const quickAccessInfo = (panelType === 'main' || panelType === 'action') 
    ? getQuickAccessInfo(panelType, slotIndex) 
    : null;
    // Tooltip functionality
  const tooltip = useTooltip(500); // 500ms delay
  
  // Context menu functionality
  const { executeAction } = useContextMenu({
    onActionExecuted: (action, item) => {
      console.log(`Context menu action executed: ${action} on ${item.label}`);
    }
  });
  
  const {
    isDragging, // This is from useDraggable, true if this slot is the active dragged item
    ref,
    dragAttributes,
    dragListeners,
    isOver,
    canDrop,
  } = useDndKitSlot({
    panelType,
    panelId,
    slotIndex,
    item: inventoryItem,
    quantity: displayQuantity,
    slotType,
  });const handleClick = () => {
    // console.log('Slot clicked:', { slotIndex, inventoryItem, panelType, panelId });
  };
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null);
  const [showSplitModal, setShowSplitModal] = useState(false);
  const [splitAmount, setSplitAmount] = useState(1);
  const contextMenuRef = useRef<HTMLDivElement>(null);
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    // Force hide tooltip immediately
    tooltip.forceHide();
    if (!inventoryItem) return;
    setContextMenu({ x: e.clientX, y: e.clientY });
  };  const handleContextMenuAction = (action: string) => {
    if (!inventoryItem) return;

    setContextMenu(null);

    // Handle split action specially to show modal
    if (action === 'split') {
      if (inventoryItem.stackable && (quantity || 0) > 1) {
        setSplitAmount(1);
        setShowSplitModal(true);
      }
      return;
    }

    // Use the context menu system to execute actions
    executeAction(
      action,
      inventoryItem,
      displayQuantity,
      slotIndex,
      panelType,
      panelId
    );
  };const handleConfirmSplit = () => {
    console.log(`Split ${splitAmount} from ${inventoryItem?.label}`);
    setShowSplitModal(false);
    
    // Use dynamic imports to avoid circular dependencies
    import('../../utils/panelTypeConverter').then(({ convertDragPanelToStorePanel }) => {
      import('../../stores/inventoryStore').then(({ useInventoryStore }) => {
        const inventoryStore = useInventoryStore.getState();
        const storePanelType = convertDragPanelToStorePanel(panelType);
        
        // Call the splitStack method from the inventory store
        inventoryStore.splitStack(slotIndex, splitAmount, storePanelType, panelId);
      });
    });
  };

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setContextMenu(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [contextMenu]);const baseStyle =
    'w-[5.5rem] h-[5.5rem] bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg flex flex-col items-center justify-center relative cursor-pointer transition-transform duration-200 ease-in-out overflow-hidden shadow-md';
  // Keep slot visible during drag; no opacity change
  const draggingStyle = '';
  const dropHoverStyle =
    isOver && canDrop
      ? 'bg-green-500/30 border-green-400 ring-2 ring-green-500 border-t-green-400'
      : isOver && !canDrop
      ? 'bg-red-500/30 border-red-400 ring-2 ring-red-500 border-t-red-400'
      : '';
  // Removed isSelected based styles as isSelected was hardcoded to false
  const selectedStyle = 'hover:bg-neutral-700/70 hover:border-neutral-600 hover:border-t-green-400/40';   const hoverStyle = isDragging ? '' : 'hover:scale-105 hover:shadow-lg hover:shadow-green-400/10';
  return (
    <>
      {/* Wrapper container for slot and key badge */}
      <div className="relative w-[5.5rem] h-[5.5rem]">
        {/* Main draggable slot */}        <div
          ref={ref}
          {...dragAttributes}
          {...dragListeners}
          tabIndex={-1}
          {...(inventoryItem ? {
            onMouseEnter: tooltip.mouseEnterProps.onMouseEnter,
            onMouseLeave: tooltip.mouseEnterProps.onMouseLeave,
            onMouseMove: tooltip.mouseEnterProps.onMouseMove,
          } : {})}
          className={`${baseStyle} ${draggingStyle} ${selectedStyle} ${dropHoverStyle} ${hoverStyle}`}
          onClick={handleClick}
          onContextMenu={handleContextMenu}
        >
          {/* Subtle green gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-green-400/5 via-transparent to-transparent pointer-events-none rounded-lg z-0" />
          
          {slotType && !inventoryItem && (
            <i className={`fas ${slotTypeFaClass[slotType]} text-neutral-600 text-3xl absolute opacity-50 z-0`} />
          )}
          {inventoryItem && (
            <>
              {/* Quantity Badge */}
              {displayQuantity > 1 && (
                <div className="absolute top-1 right-1 bg-neutral-900/80 text-white text-xs font-semibold px-1.5 py-0.5 rounded-full shadow-sm z-20">
                  x{displayQuantity}
                </div>
              )}
              <div className="w-18 h-16 flex items-center justify-center z-10 mt-1">
                {inventoryItem.icon && inventoryItem.icon.startsWith('fa-') ? (
                  <i className={`fas ${inventoryItem.icon} text-3xl text-neutral-200`} />
                ) : inventoryItem.icon ? (
                  <img src={inventoryItem.icon} alt={inventoryItem.label} className="max-w-full max-h-full object-contain" />
                ) : (
                  <i className="fas fa-question-circle text-3xl text-neutral-400" />
                )}
              </div>              {typeof inventoryItem.currentDurability === 'number' && typeof inventoryItem.maxDurability === 'number' && inventoryItem.maxDurability > 0 && (
                <div className="absolute top-0 right-0 w-1 h-full bg-neutral-700/50 rounded-r-lg overflow-hidden z-20">
                  <div
                    className="w-full bg-green-500/90 shadow-sm shadow-green-400/20 transition-all duration-300 ease-out absolute bottom-0 rounded-r-lg"
                    style={{ height: `${(inventoryItem.currentDurability / inventoryItem.maxDurability) * 100}%` }}
                  />
                </div>
              )}
              {isRequiredIngredient && (
                <div className={`absolute inset-0 flex items-center justify-center z-30 ${hasEnoughQuantity ? 'bg-green-500/20' : 'bg-red-500/30'}`}>
                  <span className="text-white text-sm font-bold">
                    {requiredQuantity}x
                  </span>
                </div>
              )}
              {/* Item Label with Enhanced Gradient Overlay - Compact within slot */}
              <div className="absolute bottom-0 left-0 right-0 z-25 slot-label-container">
                {/* Accent gradient underline */}
                <div className="absolute bottom-0 left-0 right-0 h-0.5 slot-label-accent" />
                
                {/* Gradient fade background */}
                <div className="absolute inset-0 slot-label-gradient" />
                
                {/* Label text - compact with padding */}
                <div className="relative px-2 py-1">
                  <p className="slot-label-text text-neutral-200 text-[10px] font-medium text-center truncate leading-tight">
                    {inventoryItem.label}
                  </p>
                </div>
              </div>
            </>
          )}
          {!inventoryItem && !slotType && (
            <div className="text-neutral-600 text-sm opacity-0"></div>
          )}
        </div>        {/* QuickAccess Key Badge - Outside draggable area, positioned to fit exactly in top-left corner */}
        {!hideQuickslotBadge && quickAccessInfo && quickAccessInfo.keyBinding && (
          <div className="absolute top-0 left-0 px-1 py-0.5 rounded-tl-lg rounded-br-lg bg-green-900/70 border-r border-b border-green-700/30 text-green-300 text-xs font-medium shadow-none tracking-widest select-none pointer-events-none min-w-[1.2rem] h-[1.2rem] flex items-center justify-center z-40">
            {quickAccessInfo.keyBinding}
          </div>
        )}
      </div>      {contextMenu && inventoryItem && (
        <ContextMenu 
          item={inventoryItem}
          quantity={displayQuantity}
          position={contextMenu}
          onAction={handleContextMenuAction}
          onClose={() => setContextMenu(null)}
          contextMenuRef={contextMenuRef}
        />
      )}

      {showSplitModal && inventoryItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-neutral-800 p-6 rounded-lg shadow-xl border border-neutral-700 w-80">
            <h3 className="text-lg font-semibold text-neutral-100 mb-4">Split {inventoryItem.label}</h3>
            <p className="text-neutral-300 mb-1">Amount to split (Max: {quantity! - 1}):</p>            <input
              type="number"
              min="1"
              max={quantity! - 1}
              value={splitAmount}
              onChange={(e) => setSplitAmount(Math.max(1, Math.min(quantity! - 1, parseInt(e.target.value, 10) || 1)))}
              tabIndex={-1}
              className="w-full p-2 rounded bg-neutral-700 border border-neutral-600 text-neutral-100 focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none mb-4"
            />            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSplitModal(false)}
                tabIndex={-1}
                className="px-4 py-2 bg-neutral-600 hover:bg-neutral-500 text-neutral-100 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSplit}
                tabIndex={-1}
                className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-md transition-colors"
              >
                Confirm Split
              </button>
            </div></div>
        </div>
      )}      {/* Custom Item Tooltip */}
      {inventoryItem && (
        <ItemTooltip
          item={inventoryItem}
          quantity={displayQuantity}
          isVisible={tooltip.isVisible}
          position={tooltip.position}
        />
      )}
    </>
  );
};

export default Slot;
