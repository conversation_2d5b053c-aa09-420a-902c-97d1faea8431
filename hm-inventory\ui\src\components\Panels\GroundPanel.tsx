import React, { useState, useRef } from 'react';
import { useInventoryStore, GroundPanelItem } from '../../stores/inventoryStore';

interface GroundPanelProps {
  title?: string;
  className?: string;
  initiallyOpen?: boolean;
}

const GroundPanel: React.FC<GroundPanelProps> = ({
  title = 'Ground',
  className = '',
  initiallyOpen = true,
}) => {
  const [isOpen, setIsOpen] = useState(initiallyOpen);
  const groundItems = useInventoryStore(state => state.groundItems);
  const scrollRef = useRef<HTMLDivElement>(null);

  const renderGroundItem = (item: GroundPanelItem, index: number) => {
    return (
      <div key={`ground-${item.entityId}-${index}`} className="relative group">
        <div className="w-16 h-16 bg-gray-800 border border-gray-600 rounded-lg flex items-center justify-center cursor-pointer hover:border-green-400/40 transition-colors">
          {/* Item visual representation - could be enhanced with proper item icons */}
          <div className="text-xs text-center text-gray-300">
            <div className="truncate w-12">{item.definitionId}</div>
            <div className="text-green-400">x{item.quantity}</div>
          </div>
        </div>
        
        {/* Distance indicator */}
        <div className="absolute bottom-1 right-1 bg-black/60 text-xs text-green-400 px-1 rounded">
          {item.distance.toFixed(1)}m
        </div>
        
        {/* Distance category indicator */}
        <div className={`absolute top-1 left-1 w-2 h-2 rounded-full ${
          item.distanceCategory === 'close' ? 'bg-green-400' :
          item.distanceCategory === 'nearby' ? 'bg-yellow-400' : 'bg-red-400'
        }`} />
      </div>
    );
  };

  const categorizeItems = () => {
    const categorized = {
      close: groundItems.filter(item => item.distanceCategory === 'close'),
      nearby: groundItems.filter(item => item.distanceCategory === 'nearby'),
      far: groundItems.filter(item => item.distanceCategory === 'far'),
    };
    return categorized;
  };

  const categorizedItems = categorizeItems();
  const hasItems = groundItems.length > 0;

  return (
    <div className={`bg-gray-900/95 border border-gray-700 rounded-lg backdrop-blur-sm ${className}`}>
      {/* Header */}
      <div
        className="flex items-center justify-between p-3 border-b border-gray-700 cursor-pointer select-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-2">
          <span className="text-green-400 font-medium">{title}</span>
          {hasItems && (
            <span className="bg-green-400/20 text-green-400 text-xs px-2 py-1 rounded-full">
              {groundItems.length}
            </span>
          )}
        </div>
        <div className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
          <i className="fas fa-chevron-down text-gray-400"></i>
        </div>
      </div>

      {/* Content */}
      {isOpen && (
        <div className="p-3">
          {!hasItems ? (
            <div className="text-center text-gray-400 py-8">
              <i className="fas fa-search text-2xl mb-2 opacity-50"></i>
              <p>No items on ground nearby</p>
            </div>
          ) : (
            <div ref={scrollRef} className="space-y-4 max-h-96 overflow-y-auto">
              {/* Close items */}
              {categorizedItems.close.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-green-400 font-medium">Close</span>
                  </div>
                  <div className="grid grid-cols-4 gap-2">
                    {categorizedItems.close.map((item, index) => renderGroundItem(item, index))}
                  </div>
                </div>
              )}

              {/* Nearby items */}
              {categorizedItems.nearby.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <span className="text-sm text-yellow-400 font-medium">Nearby</span>
                  </div>
                  <div className="grid grid-cols-4 gap-2">
                    {categorizedItems.nearby.map((item, index) => renderGroundItem(item, index))}
                  </div>
                </div>
              )}

              {/* Far items */}
              {categorizedItems.far.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <span className="text-sm text-red-400 font-medium">Far</span>
                  </div>
                  <div className="grid grid-cols-4 gap-2">
                    {categorizedItems.far.map((item, index) => renderGroundItem(item, index))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GroundPanel;
