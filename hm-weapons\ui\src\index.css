@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  /* Ensure root elements fill viewport */
  html, body, #root {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    background: transparent !important;
    overflow: hidden;
  }

  /* Disable tab navigation for all elements */
  * {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Remove tab focus from all interactive elements */
  button, input, select, textarea, [tabindex] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* Custom slider styles */
@layer components {
  .slider {
    background: #374151;
  }

  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
    border: 2px solid #1F2937;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
  }

  .slider::-webkit-slider-thumb:hover {
    background: #2563EB;
    transform: scale(1.1);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
    border: 2px solid #1F2937;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
  }

  .slider::-moz-range-thumb:hover {
    background: #2563EB;
    transform: scale(1.1);
  }
}
