{"projects": [{"id": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "name": "HM Banking System Development", "description": "Development of the hm-banking FiveM resource, including UI, game scripts, and backend logic.", "createdAt": "2025-06-08T13:19:47.959Z", "updatedAt": "2025-06-08T13:19:47.959Z"}], "tasks": [{"id": "7d4d783b-00ca-4866-aaf5-3555ce3f00a0", "name": "Client Script: UI Interaction & Basic NUI Events", "details": "Implement client-side logic in `scripts/client/client.ts`. This includes handling NUI events to show/hide the UI, sending data to the UI (e.g., account details, transaction history), and receiving events from the UI for actions like deposit, withdraw, transfer.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:19:59.530Z", "updatedAt": "2025-06-08T13:19:59.530Z", "dependsOn": [], "priority": 5, "status": "pending", "tags": []}, {"id": "be120284-8b28-4ded-b9e3-3de11facacc0", "name": "Server Script: Core Transaction Logic & Database Interaction", "details": "Implement server-side logic in `scripts/server/server.ts`. This includes core banking operations (creating accounts, processing deposits, withdrawals, transfers), interacting with a database (e.g., oxmysql) to store and retrieve banking data, and handling security/validation for transactions.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:19:59.612Z", "updatedAt": "2025-06-08T13:19:59.612Z", "dependsOn": [], "priority": 5, "status": "pending", "tags": []}, {"id": "385b2488-d42f-49ce-93b6-e00b3d5e51fe", "name": "Implement Transaction History (UI and Server)", "details": "Implement the transaction history feature. UI part (displaying history in App.tsx) is mostly complete. Server-side logic for fetching, storing, and updating transaction records is pending. Client-side NUI events for history updates need to be fully connected to the server logic.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:19:59.708Z", "updatedAt": "2025-06-08T13:19:59.708Z", "dependsOn": [], "priority": 5, "status": "pending", "tags": []}, {"id": "4e8e3a84-4186-4776-9555-275f7813b370", "name": "Testing and Debugging - Core Features", "details": "Thoroughly test all core banking features: UI interactions, deposits, withdrawals, transfers, transaction history updates, and data persistence. Debug any issues found during testing.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:19:59.892Z", "updatedAt": "2025-06-08T13:19:59.892Z", "dependsOn": [], "priority": 5, "status": "pending", "tags": []}, {"id": "bdf65e25-bd6f-43b0-b2c0-34bce1efc93e", "name": "Design Core Banking UI Mockups/Layouts", "details": "UI design has been iteratively developed and implemented directly in App.tsx, aligning with hm-inventory styling. Mockups are no longer a prerequisite.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:20:24.701Z", "updatedAt": "2025-06-08T13:20:28.743Z", "dependsOn": [], "priority": 5, "status": "done", "tags": []}, {"id": "f3a1eca9-cc35-46d2-bf70-73acb76a3ba5", "name": "Implement Basic Banking UI Structure (React + Tailwind)", "details": "Basic UI structure and styling implemented in App.tsx. Zustand store created for state management. NUI listeners for visibility and data updates are set up. Styling is now very closely aligned with hm-inventory's Slot.tsx, featuring a dark neutral theme, emerald accents, transparency, and layering effects. Key style constants (panelBg, cardBg, cardTopAccent, inputBg) defined and applied for consistency.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:20:24.755Z", "updatedAt": "2025-06-08T13:20:28.834Z", "dependsOn": [], "priority": 5, "status": "done", "tags": []}, {"id": "fff778b2-793c-4956-8676-2964ff96a911", "name": "Shared Scripts: Define Banking Data Types", "details": "Define shared TypeScript types for bank accounts, transactions, UI state, and actions in `scripts/shared/types/index.ts`. This includes `BankAccount`, `Transaction`, `BankingUIState`, and `BankingUIActions`.", "projectId": "25bd63ae-5f67-4731-9f21-3cf30eb3fba7", "completed": false, "createdAt": "2025-06-08T13:20:24.813Z", "updatedAt": "2025-06-08T13:20:28.959Z", "dependsOn": [], "priority": 5, "status": "done", "tags": []}], "subtasks": []}