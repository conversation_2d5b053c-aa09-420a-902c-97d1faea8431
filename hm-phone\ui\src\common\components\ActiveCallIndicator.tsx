import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDialerStore } from '../../apps/contacts/stores/dialerStore';
import { useNavigation } from '../../navigation/hooks';

// Helper function to format call duration
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const ActiveCallIndicator: React.FC = () => {
  const { currentCall, actions } = useDialerStore();
  const { openAppView } = useNavigation();
  const [inCallView, setInCallView] = useState(false);
  const [callMinimized, setCallMinimized] = useState(false);

  // Check if we're in the call view or if call is minimized
  useEffect(() => {
    const checkCallStatus = () => {
      const inCallViewFlag = localStorage.getItem('inCallView') === 'true';
      const callMinimizedFlag = localStorage.getItem('callMinimized') === 'true';

      setInCallView(inCallViewFlag);
      setCallMinimized(callMinimizedFlag);
    };

    // Check initially
    checkCallStatus();

    // Set up an interval to check regularly
    const interval = setInterval(checkCallStatus, 500);

    // Clean up
    return () => clearInterval(interval);
  }, []);

  // Show the indicator if there's an active call and either:
  // 1. We're not in the call view, or
  // 2. The call has been minimized
  const isActiveCall =
    currentCall && currentCall.state !== 'ended' && (!inCallView || callMinimized);

  // Handle returning to call view
  const handleReturnToCall = () => {
    // Clear the minimized flag
    localStorage.removeItem('callMinimized');
    // Navigate to the call view
    openAppView('contacts', 'call');
  };

  // State to track if call is ending
  const [isEnding, setIsEnding] = useState(false);

  // Handle ending the call
  const handleEndCall = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent opening call view when ending call
    setIsEnding(true);

    // Delay actual end call to allow animation to play
    setTimeout(() => {
      actions.endCall();
    }, 300);
  };

  return (
    <AnimatePresence>
      {isActiveCall && (
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: isEnding ? -30 : 0, opacity: isEnding ? 0 : 1 }}
          exit={{ y: -30, opacity: 0 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          className="absolute top-0 left-0 right-0 mx-auto w-[45%] bg-black/90 backdrop-blur-sm rounded-b-xl shadow-md z-[200] overflow-hidden flex items-center h-[32px] border-b border-zinc-700"
          onClick={handleReturnToCall}
        >
          <div className="flex items-center justify-between px-3 h-full w-full">
            {/* Call status with inline info */}
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2.5"></div>
              <div className="flex items-center">
                <span className="text-white text-[10px] font-medium truncate max-w-[60px]">
                  {currentCall.name || currentCall.number}
                </span>
                <span className="text-zinc-500 text-[10px] mx-1.5">•</span>
                <span className="text-white text-[10px] font-medium">
                  {currentCall.state === 'connected'
                    ? formatDuration(currentCall.duration)
                    : currentCall.state === 'calling'
                    ? 'Calling...'
                    : 'Call ended'}
                </span>
              </div>
            </div>

            {/* End call button */}
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={handleEndCall}
              className="w-4 h-4 rounded-full bg-red-600 flex items-center justify-center"
            >
              <i className="fas fa-phone-slash text-white text-[8px]"></i>
            </motion.button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ActiveCallIndicator;
