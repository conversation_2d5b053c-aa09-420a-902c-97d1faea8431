{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "types": ["@citizenfx/client", "@types/node"], "lib": ["ES2020"], "baseUrl": "..", "paths": {"@shared/*": ["shared/*"], "@client/*": ["client/*"], "@server/*": ["server/*"]}}, "include": ["./**/*", "../shared/**/*"], "exclude": ["**/node_modules", "**/__tests__/*"]}