import { create } from 'zustand';
import { Ad } from '../types/yellowPagesTypes';
import { yellowPagesMockData } from '../../../fivem/mockData';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';

interface YellowPagesState {
  // State
  ads: Ad[];
  favorites: number[];
  loading: boolean;
  error: string | null;

  // Actions
  actions: {
    loadAds: () => Promise<void>;
    createAd: (ad: Partial<Ad>) => Promise<void>;
    updateAd: (ad: Ad) => Promise<void>;
    deleteAd: (adId: number) => Promise<void>;
    toggleFavorite: (adId: number) => void;
    searchAds: (query: string) => Ad[];
    getAd: (adId: number) => Ad | undefined;
  };

  // Handlers (for message events from server)
  handlers: {
    onSetAds: (ads: Ad[]) => void;
    onAddAd: (ad: Ad) => void;
    onUpdateAd: (ad: Ad) => void;
    onDeleteAd: (adId: number) => void;
    onClearAdsContent: () => void; // New handler for cleanup
  };
}

export const useYellowPagesStore = create<YellowPagesState>((set, get) => ({
  // State
  ads: yellowPagesMockData.ads,
  favorites: [],
  loading: false,
  error: null,

  // Actions
  actions: {
    // Load all ads
    loadAds: async () => {
      console.log('[YellowPagesStore] Loading ads');
      set({ loading: true, error: null });

      try {
        await clientRequests.send(
          'yellowPages',
          'getAllAds',
          {},
          yellowPagesMockData.ads,
          get().handlers.onSetAds as (data: unknown) => void
        );
        return;
      } catch (error) {
        console.error('[YellowPagesStore] Error loading ads:', error);
        set({ error: 'Failed to load ads. Please try again.', loading: false });
      }
    },

    // Create a new ad
    createAd: async (adData: Partial<Ad>) => {
      console.log('[YellowPagesStore] Creating ad:', adData);
      set({ loading: true, error: null });

      try {
        // Get user profile for contact info
        const userProfile = usePhoneStore.getState().userProfile;
        if (!userProfile) {
          throw new Error('User profile not available');
        }

        // Create ad object
        const newAd: Partial<Ad> = {
          ...adData,
          contactNumber: userProfile.phoneNumber,
          characterName: userProfile.name,
          timestamp: Date.now()
        };

        // Generate a mock ad with ID for browser testing
        const mockAd = {
          id: Date.now(),
          ...newAd
        } as Ad;

        await clientRequests.send(
          'yellowPages',
          'createAd',
          newAd,
          mockAd,
          get().handlers.onAddAd as (data: unknown) => void
        );

        set({ loading: false });
        return;
      } catch (error) {
        console.error('[YellowPagesStore] Error creating ad:', error);
        set({ error: 'Failed to create ad. Please try again.', loading: false });
      }
    },

    // Update an existing ad
    updateAd: async (ad: Ad) => {
      console.log('[YellowPagesStore] Updating ad:', ad);
      set({ loading: true, error: null });

      try {
        await clientRequests.send(
          'yellowPages',
          'updateAd',
          { ...ad },
          ad,
          get().handlers.onUpdateAd as (data: unknown) => void
        );

        set({ loading: false });
        return;
      } catch (error) {
        console.error('[YellowPagesStore] Error updating ad:', error);
        set({ error: 'Failed to update ad. Please try again.', loading: false });
      }
    },

    // Delete an ad
    deleteAd: async (adId: number) => {
      console.log('[YellowPagesStore] Deleting ad:', adId);
      set({ loading: true, error: null });

      try {
        await clientRequests.send(
          'yellowPages',
          'deleteAd',
          { id: adId },
          adId,
          get().handlers.onDeleteAd as (data: unknown) => void
        );

        set({ loading: false });
        return;
      } catch (error) {
        console.error('[YellowPagesStore] Error deleting ad:', error);
        set({ error: 'Failed to delete ad. Please try again.', loading: false });
      }
    },

    // Toggle favorite status for an ad
    toggleFavorite: (adId: number) => {
      set(state => ({
        favorites: state.favorites.includes(adId)
          ? state.favorites.filter(id => id !== adId)
          : [...state.favorites, adId]
      }));
    },

    // Search ads by query
    searchAds: (query: string) => {
      const state = get();
      const searchTerm = query.toLowerCase();
      return state.ads.filter(
        ad =>
          ad.description?.toLowerCase().includes(searchTerm) ||
          ad.characterName?.toLowerCase().includes(searchTerm)
      );
    },

    // Get a specific ad by ID
    getAd: (adId: number) => {
      return get().ads.find(ad => ad.id === adId);
    }
  },

  // Handlers for message events from server
  handlers: {
    // Set all ads
    onSetAds: (ads: Ad[]) => {
      console.log('[YellowPagesStore] Setting ads:', ads.length);
      // Sort ads by timestamp (newest first)
      const sortedAds = [...ads].sort((a, b) => b.timestamp - a.timestamp);
      set({ ads: sortedAds, loading: false });
    },

    // Add a new ad
    onAddAd: (ad: Ad) => {
      console.log('[YellowPagesStore] Adding ad:', ad);
      set(state => {
        // Ensure the ad has a timestamp
        const adWithTimestamp = {
          ...ad,
          timestamp: ad.timestamp || Date.now()
        };
        // Add the new ad and re-sort by timestamp
        const updatedAds = [adWithTimestamp, ...state.ads].sort(
          (a, b) => b.timestamp - a.timestamp
        );
        return {
          ads: updatedAds,
          loading: false
        };
      });
    },

    // Update an existing ad
    onUpdateAd: (ad: Ad) => {
      console.log('[YellowPagesStore] Updating ad:', ad);
      set(state => {
        // Update the ad and re-sort by timestamp
        const updatedAds = state.ads
          .map(a => (a.id === ad.id ? ad : a))
          .sort((a, b) => b.timestamp - a.timestamp);
        return {
          ads: updatedAds,
          loading: false
        };
      });
    },

    // Delete an ad
    onDeleteAd: (adId: number) => {
      console.log('[YellowPagesStore] Deleting ad:', adId);
      set(state => ({
        ads: state.ads.filter(ad => ad.id !== adId),
        loading: false
      }));
    },

    // Clear ads content to save memory
    onClearAdsContent: () => {
      // console.log('[YellowPagesStore] Clearing ads content to save memory');

      const { ads } = get();

      // Keep only essential ad data to save memory
      const lightweightAds = ads.map(ad => ({
        // Keep essential fields
        id: ad.id,
        contactNumber: ad.contactNumber,
        characterName: ad.characterName,
        timestamp: ad.timestamp,
        category: ad.category,
        // Truncate description to save memory
        description: ad.description ? ad.description.substring(0, 50) + '...' : '',
        // Clear image URL to save memory
        imageUrl: ad.imageUrl ? 'thumbnail' : undefined,
        // Keep price as it's a small field
        price: ad.price
      }));

      // Update the store with lightweight versions
      set({ ads: lightweightAds as Ad[] });

      console.log('[YellowPagesStore] Cleared ads content, keeping only essential data');
    }
  }
}));
