/// <reference types="@citizenfx/client" />
/// <reference types="@citizenfx/server" />
/**
 * HM-Admin Shared Permissions
 * This file contains permission definitions shared between client and server
 */

// Admin permission levels
export enum AdminPermissionLevel {
  NONE = 0,
  HELPER = 1,
  MODERATOR = 2,
  ADMIN = 3,
  SENIOR_ADMIN = 4,
  MANAGEMENT = 5,
  OWNER = 6,
}

// Admin permission level names
export const AdminPermissionLevelNames: { [key in AdminPermissionLevel]: string } = {
  [AdminPermissionLevel.NONE]: 'Player',
  [AdminPermissionLevel.HELPER]: 'Helper',
  [AdminPermissionLevel.MODERATOR]: 'Moderator',
  [AdminPermissionLevel.ADMIN]: 'Admin',
  [AdminPermissionLevel.SENIOR_ADMIN]: 'Senior Admin',
  [AdminPermissionLevel.MANAGEMENT]: 'Management',
  [AdminPermissionLevel.OWNER]: 'Owner',
};

// Permission definitions for each command
export const CommandPermissions: { [key: string]: AdminPermissionLevel } = {
  // Helper level commands
  'tpm': AdminPermissionLevel.HELPER,
  'noclip': AdminPermissionLevel.HELPER,
  'coords': AdminPermissionLevel.HELPER,
  'heading': AdminPermissionLevel.HELPER,
  'entinfo': AdminPermissionLevel.HELPER,
  
  // Moderator level commands
  'goto': AdminPermissionLevel.MODERATOR,
  'bring': AdminPermissionLevel.MODERATOR,
  'spectate': AdminPermissionLevel.MODERATOR,
  'freeze': AdminPermissionLevel.MODERATOR,
  'unfreeze': AdminPermissionLevel.MODERATOR,
  'car': AdminPermissionLevel.MODERATOR,
  'dv': AdminPermissionLevel.MODERATOR,
  'fix': AdminPermissionLevel.MODERATOR,
  'clean': AdminPermissionLevel.MODERATOR,
  
  // Admin level commands
  'kill': AdminPermissionLevel.ADMIN,
  'revive': AdminPermissionLevel.ADMIN,
  'tpcoords': AdminPermissionLevel.ADMIN,
  'god': AdminPermissionLevel.ADMIN,
  'invisible': AdminPermissionLevel.ADMIN,
  'cleararea': AdminPermissionLevel.ADMIN,
  'announce': AdminPermissionLevel.ADMIN,
  'ragdoll': AdminPermissionLevel.ADMIN,
  'bursttire': AdminPermissionLevel.ADMIN,
  
  // Senior Admin level commands
  'kick': AdminPermissionLevel.SENIOR_ADMIN,
  'warn': AdminPermissionLevel.SENIOR_ADMIN,
  'weather': AdminPermissionLevel.SENIOR_ADMIN,
  'time': AdminPermissionLevel.SENIOR_ADMIN,
  'fire': AdminPermissionLevel.SENIOR_ADMIN,
  'launch': AdminPermissionLevel.SENIOR_ADMIN,
  
  // Management level commands
  'ban': AdminPermissionLevel.MANAGEMENT,
  'unban': AdminPermissionLevel.MANAGEMENT,
};

// Get required permission level for a command
export function getRequiredPermissionLevel(command: string): AdminPermissionLevel {
  return CommandPermissions[command] || AdminPermissionLevel.OWNER;
}

// Check if a permission level is sufficient for a command
export function hasPermissionForCommand(userLevel: AdminPermissionLevel, command: string): boolean {
  const requiredLevel = getRequiredPermissionLevel(command);
  return userLevel >= requiredLevel;
}

console.log('HM-Admin permissions module loaded');