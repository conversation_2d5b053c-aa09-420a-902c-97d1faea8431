{"name": "hm-garages", "version": "1.0.0", "description": "Garage system for HM Framework", "main": "index.js", "engines": {"node": "22.16.0"}, "scripts": {"build": "node build.js --production", "watch": "node build.js --watch", "watch:game": "node build.js --watch", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\""}, "keywords": ["fivem", "garages", "typescript"], "author": "HM-Core", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "esbuild": "^0.25.5", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "typescript": "^5.8.3"}}