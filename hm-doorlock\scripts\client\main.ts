/**
 * HM Door System - Client Implementation
 * Client-side handles door registration with FiveM door system and target integration
 */

/// <reference types="@citizenfx/client" />

import { DOOR_DEFINITIONS, DOOR_SYSTEM_CONFIG } from "../shared/config";
import {
  DoorDefinition,
  DoorState,
  // DoorLockType, // Commented out as it's not used in this file
  DoorType,
  TargetOption, // Now imported from local shared types
} from "../shared/types/door.types";

// ==================== IMPORTS ====================
/// <reference types="@citizenfx/client" />

// ==================== STATE MANAGEMENT ====================

/**
 * Local door states synced from server
 */
const localDoorStates = new Map<string, DoorState>();

/**
 * Registered doors tracking
 */
const registeredDoors = new Set<string>();

// ==================== DOOR REGISTRATION ====================

/**
 * Register a single door with the FiveM door system
 */
function registerDoor(
  doorId: string,
  doorHash: string,
  coords: { x: number; y: number; z: number },
  model: string | number
): void {
  try {
    // Get model hash
    const modelHash = typeof model === "string" ? GetHashKey(model) : model;

    // Add door to FiveM door system
    // Using false for p5, scriptDoor, and isLocal for basic door system
    AddDoorToSystem(
      doorHash,
      modelHash,
      coords.x,
      coords.y,
      coords.z,
      false,
      false,
      false
    );

    // Set initial state to unlocked
    DoorSystemSetDoorState(doorHash, DoorState.UNLOCKED, false, false);

    // Track locally
    registeredDoors.add(doorId);

    // Notify server
    emitNet("hm-doorlock:door-registered", doorId);

    console.log(`[HM-DoorLock] Registered door: ${doorId} (${doorHash})`);
  } catch (error) {
    console.error(`[HM-DoorLock] Failed to register door ${doorId}:`, error);
  }
}

/**
 * Register all doors from configuration
 */
function registerAllDoors(): void {
  console.log(
    `[HM-DoorLock] Registering ${DOOR_DEFINITIONS.length} doors on client...`
  );

  for (const door of DOOR_DEFINITIONS) {
    try {
      if (door.type === DoorType.SINGLE) {
        // Single door - register the door part
        registerDoor(door.id, door.hash, door.door.coords, door.door.model);
      } else if (door.type === DoorType.DOUBLE) {
        // Double door - register both parts with unique hashes
        registerDoor(
          `${door.id}_part1`,
          `${door.hash}_1`,
          door.doors[0].coords,
          door.doors[0].model
        );
        registerDoor(
          `${door.id}_part2`,
          `${door.hash}_2`,
          door.doors[1].coords,
          door.doors[1].model
        );
      } else if (door.type === DoorType.GATE) {
        // Gate - register all parts
        door.parts.forEach((part, index) => {
          registerDoor(
            `${door.id}_part${index + 1}`,
            `${door.hash}_${index + 1}`,
            part.coords,
            part.model
          );
        });
      }
    } catch (error) {
      console.error(`[HM-DoorLock] Failed to register door ${door.id}:`, error);
    }
  }

  console.log(`[HM-DoorLock] Door registration complete!`);
}

// ==================== DOOR CONTROL ====================

/**
 * Update door state from server
 */
function updateDoorState(doorId: string, state: DoorState): void {
  const door = DOOR_DEFINITIONS.find((d) => d.id === doorId);
  if (!door) {
    console.error(`[HM-DoorLock] Door not found: ${doorId}`);
    return;
  }

  // Update local tracking
  localDoorStates.set(doorId, state);

  try {
    if (door.type === DoorType.SINGLE) {
      DoorSystemSetDoorState(door.hash, state, false, false);
    } else if (door.type === DoorType.DOUBLE) {
      DoorSystemSetDoorState(`${door.hash}_1`, state, false, false);
      DoorSystemSetDoorState(`${door.hash}_2`, state, false, false);
    } else if (door.type === DoorType.GATE) {
      door.parts.forEach((_, index) => {
        DoorSystemSetDoorState(
          `${door.hash}_${index + 1}`,
          state,
          false,
          false
        );
      });
    }

    console.log(`[HM-DoorLock] Updated door ${doorId} to state: ${state}`);
  } catch (error) {
    console.error(`[HM-DoorLock] Failed to update door ${doorId}:`, error);
  }
}

// ==================== NETWORK EVENTS ====================

/**
 * Handle door state updates from server
 */
onNet("hm-doorlock:set-door-state", (doorId: string, state: DoorState) => {
  updateDoorState(doorId, state);
});

/**
 * Handle all door states from server (on join)
 */
onNet(
  "hm-doorlock:all-states-response",
  (allStates: Record<string, DoorState>) => {
    console.log(
      `[HM-DoorLock] Received ${
        Object.keys(allStates).length
      } door states from server`
    );

    for (const [doorId, state] of Object.entries(allStates)) {
      updateDoorState(doorId, state);
    }
  }
);

/**
 * Handle toggle responses
 */
onNet("hm-doorlock:toggle-success", (doorId: string, newState: DoorState) => {
  const stateName = newState === DoorState.LOCKED ? "locked" : "unlocked";
  console.log(`[HM-DoorLock] Door ${doorId} ${stateName}`);

  // TODO: Show notification to player
  // You can integrate with your notification system here
});

onNet("hm-doorlock:toggle-failed", (doorId: string) => {
  console.error(`[HM-DoorLock] Failed to toggle door ${doorId}`);

  // TODO: Show error notification to player
});

onNet("hm-doorlock:access-denied", (doorId: string) => {
  console.log(`[HM-DoorLock] Access denied for door ${doorId}`);

  // TODO: Show access denied notification to player
});

// ==================== INITIALIZATION ====================

/**
 * Initialize the door system on resource start
 */
on("onResourceStart", async (resourceName: string) => {
  if (GetCurrentResourceName() !== resourceName) {
    return;
  }
  registerAllDoors();

  const targetAPI = global.exports["hm-target"];
  if (targetAPI) {
    console.log(
      "[HM-DoorLock] Debug mode enabled, registering doors with hm-target."
    );

    const adminDoorOptions: TargetOption[] = [
      {
        id: "toggle_lock",
        label: "Toggle Lock",
        icon: "fas fa-key",
        action: "hm-doorlock:admin-toggle-door", // This is now an event name
        canInteract: (entity?: number, _distance?: number, data?: any) => {
          if (!entity && !data?.modelHash) return false; // Need either entity or modelHash
          // Further checks can be added here if needed, e.g., player permissions
          return true;
        },
        data: { adminOverride: true }, // Pass admin override flag
      },
    ];

    const uniqueModelHashes = new Set<number>();
    for (const door of DOOR_DEFINITIONS) {
      if (door.type === DoorType.SINGLE) {
        uniqueModelHashes.add(
          typeof door.door.model === "string"
            ? GetHashKey(door.door.model)
            : door.door.model
        );
      } else if (door.type === DoorType.DOUBLE) {
        door.doors.forEach((d) =>
          uniqueModelHashes.add(
            typeof d.model === "string" ? GetHashKey(d.model) : d.model
          )
        );
      } else if (door.type === DoorType.GATE) {
        door.parts.forEach((p) =>
          uniqueModelHashes.add(
            typeof p.model === "string" ? GetHashKey(p.model) : p.model
          )
        );
      }
    }

    uniqueModelHashes.forEach((modelHash) => {
      console.log(
        `[HM-DoorLock] Registering model ${modelHash} with hm-target.`
      );
      global.exports["hm-target"].addTargetModel(modelHash, adminDoorOptions);
    });
  } else {
    console.warn(
      "[HM-DoorLock] hm-target export not found, admin targeting will not be available."
    );
  }
});

// Listener for the admin toggle event
onNet(
  "hm-doorlock:admin-toggle-door",
  (eventData: {
    target: {
      entity?: number;
      modelHash?: number;
      position?: { x: number; y: number; z: number };
    };
    option: TargetOption;
  }) => {
    if (
      !DOOR_SYSTEM_CONFIG.debug.enabled ||
      !eventData.option.data?.adminOverride
    ) {
      console.warn(
        "[HM-DoorLock] Unauthorized admin toggle attempt or debug mode disabled."
      );
      return;
    }

    const { entity, modelHash, position } = eventData.target;
    let doorIdToToggle: string | null = null;

    if (entity) {
      // If an entity is directly targeted (e.g., a door object itself if it were an entity)
      // This part might need more sophisticated logic if doors are not direct entities
      // For now, assume we might find a door near this entity's position
      console.log(`[HM-DoorLock] Admin toggle for entity: ${entity}`);
      const nearestDoor = findNearestDoor(position); // findNearestDoor now accepts optional position
      if (nearestDoor) {
        doorIdToToggle = nearestDoor.id;
      }
    } else if (modelHash && position) {
      // If a model is targeted, find the nearest door with that model at the target position
      console.log(
        `[HM-DoorLock] Admin toggle for model: ${modelHash} at pos:`,
        position
      );
      const nearestDoor = findNearestDoor(position, modelHash); // findNearestDoor now accepts optional modelHash
      if (nearestDoor) {
        doorIdToToggle = nearestDoor.id;
      }
    }

    if (doorIdToToggle) {
      console.log(`[HM-DoorLock] Admin toggling door: ${doorIdToToggle}`);
      const currentState =
        localDoorStates.get(doorIdToToggle) ||
        DOOR_DEFINITIONS.find((d) => d.id === doorIdToToggle)?.defaultState;
      const newState =
        currentState === DoorState.LOCKED
          ? DoorState.UNLOCKED
          : DoorState.LOCKED;
      emitNet(
        "hm-doorlock:request-door-update",
        doorIdToToggle,
        newState,
        true
      ); // Send admin override flag
    } else {
      console.warn(
        "[HM-DoorLock] Admin toggle: Could not determine which door to toggle based on target data:",
        eventData.target
      );
    }
  }
);
// ...existing code...
function findNearestDoor(
  refPosition?: { x: number; y: number; z: number },
  modelHashFilter?: number
): DoorDefinition | null {
  const playerPed = PlayerPedId();
  const checkPosition = refPosition
    ? [refPosition.x, refPosition.y, refPosition.z]
    : GetEntityCoords(playerPed, true);
  const maxDistance = DOOR_SYSTEM_CONFIG.defaultMaxDistance;

  let nearestDoor: DoorDefinition | null = null;
  let nearestDistance = maxDistance;

  for (const door of DOOR_DEFINITIONS) {
    let doorCoords: { x: number; y: number; z: number };

    // Get door coordinates based on type
    if (door.type === DoorType.SINGLE) {
      doorCoords = door.door.coords;
    } else if (door.type === DoorType.DOUBLE) {
      // Use first door coordinates for distance check
      doorCoords = door.doors[0].coords;
    } else if (door.type === DoorType.GATE) {
      // Use first part coordinates for distance check
      doorCoords = door.parts[0].coords;
    } else {
      continue;
    }

    // Calculate distance
    const dx = checkPosition[0] - doorCoords.x;
    const dy = checkPosition[1] - doorCoords.y;
    const dz = checkPosition[2] - doorCoords.z;
    const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

    const modelMatches = modelHashFilter
      ? (door.type === DoorType.SINGLE &&
          (typeof door.door.model === "string"
            ? GetHashKey(door.door.model)
            : door.door.model) === modelHashFilter) ||
        (door.type === DoorType.DOUBLE &&
          door.doors.some(
            (d) =>
              (typeof d.model === "string" ? GetHashKey(d.model) : d.model) ===
              modelHashFilter
          )) ||
        (door.type === DoorType.GATE &&
          door.parts.some(
            (p) =>
              (typeof p.model === "string" ? GetHashKey(p.model) : p.model) ===
              modelHashFilter
          ))
      : true;

    if (distance < nearestDistance && modelMatches) {
      nearestDistance = distance;
      nearestDoor = door;
    }
  }

  return nearestDoor;
}

// ==================== NETWORK EVENTS ====================

/**
 * Handle door state updates from server
 */
onNet("hm-doorlock:set-door-state", (doorId: string, state: DoorState) => {
  updateDoorState(doorId, state);
});

/**
 * Handle all door states from server (on join)
 */
onNet(
  "hm-doorlock:all-states-response",
  (allStates: Record<string, DoorState>) => {
    console.log(
      `[HM-DoorLock] Received ${
        Object.keys(allStates).length
      } door states from server`
    );

    for (const [doorId, state] of Object.entries(allStates)) {
      updateDoorState(doorId, state);
    }
  }
);

/**
 * Handle toggle responses
 */
onNet("hm-doorlock:toggle-success", (doorId: string, newState: DoorState) => {
  const stateName = newState === DoorState.LOCKED ? "locked" : "unlocked";
  console.log(`[HM-DoorLock] Door ${doorId} ${stateName}`);

  // TODO: Show notification to player
  // You can integrate with your notification system here
});

onNet("hm-doorlock:toggle-failed", (doorId: string) => {
  console.error(`[HM-DoorLock] Failed to toggle door ${doorId}`);

  // TODO: Show error notification to player
});

onNet("hm-doorlock:access-denied", (doorId: string) => {
  console.log(`[HM-DoorLock] Access denied for door ${doorId}`);

  // TODO: Show access denied notification to player
});

// ==================== INITIALIZATION ====================

/**
 * Initialize the door system on resource start
 */
on("onResourceStart", async (resourceName: string) => {
  if (GetCurrentResourceName() !== resourceName) {
    return;
  }

  console.log("[HM-DoorLock] Client script started.");
  registerAllDoors();

  if (DOOR_SYSTEM_CONFIG.debug.enabled) {
    const targetAPI = global.exports["hm-target"];
    if (targetAPI) {
      console.log(
        "[HM-DoorLock] Debug mode enabled, registering doors with hm-target."
      );

      const adminDoorOptions: TargetOption[] = [
        {
          id: "admin_toggle_lock",
          label: "Toggle Lock (Admin)",
          icon: "fas fa-key",
          action: "hm-doorlock:admin-toggle-door", // This is now an event name
          canInteract: (entity?: number, _distance?: number, data?: any) => {
            if (!entity && !data?.modelHash) return false; // Need either entity or modelHash
            // Further checks can be added here if needed, e.g., player permissions
            return true;
          },
          data: { adminOverride: true }, // Pass admin override flag
        },
      ];

      const uniqueModelHashes = new Set<number>();
      for (const door of DOOR_DEFINITIONS) {
        if (door.type === DoorType.SINGLE) {
          uniqueModelHashes.add(
            typeof door.door.model === "string"
              ? GetHashKey(door.door.model)
              : door.door.model
          );
        } else if (door.type === DoorType.DOUBLE) {
          door.doors.forEach((d) =>
            uniqueModelHashes.add(
              typeof d.model === "string" ? GetHashKey(d.model) : d.model
            )
          );
        } else if (door.type === DoorType.GATE) {
          door.parts.forEach((p) =>
            uniqueModelHashes.add(
              typeof p.model === "string" ? GetHashKey(p.model) : p.model
            )
          );
        }
      }

      uniqueModelHashes.forEach((modelHash) => {
        console.log(
          `[HM-DoorLock] Registering model ${modelHash} with hm-target.`
        );
        global.exports["hm-target"].addTargetModel(modelHash, adminDoorOptions);
      });
    } else {
      console.warn(
        "[HM-DoorLock] hm-target export not found, admin targeting will not be available."
      );
    }
  }
});

// Listener for the admin toggle event
onNet(
  "hm-doorlock:admin-toggle-door",
  (eventData: {
    target: {
      entity?: number;
      modelHash?: number;
      position?: { x: number; y: number; z: number };
    };
    option: TargetOption;
  }) => {
    if (
      !DOOR_SYSTEM_CONFIG.debug.enabled ||
      !eventData.option.data?.adminOverride
    ) {
      console.warn(
        "[HM-DoorLock] Unauthorized admin toggle attempt or debug mode disabled."
      );
      return;
    }

    const { entity, modelHash, position } = eventData.target;
    let doorIdToToggle: string | null = null;

    if (entity) {
      // If an entity is directly targeted (e.g., a door object itself if it were an entity)
      // This part might need more sophisticated logic if doors are not direct entities
      // For now, assume we might find a door near this entity's position
      console.log(`[HM-DoorLock] Admin toggle for entity: ${entity}`);
      const nearestDoor = findNearestDoor(position); // findNearestDoor now accepts optional position
      if (nearestDoor) {
        doorIdToToggle = nearestDoor.id;
      }
    } else if (modelHash && position) {
      // If a model is targeted, find the nearest door with that model at the target position
      console.log(
        `[HM-DoorLock] Admin toggle for model: ${modelHash} at pos:`,
        position
      );
      const nearestDoor = findNearestDoor(position, modelHash); // findNearestDoor now accepts optional modelHash
      if (nearestDoor) {
        doorIdToToggle = nearestDoor.id;
      }
    }

    if (doorIdToToggle) {
      console.log(`[HM-DoorLock] Admin toggling door: ${doorIdToToggle}`);
      const currentState =
        localDoorStates.get(doorIdToToggle) ||
        DOOR_DEFINITIONS.find((d) => d.id === doorIdToToggle)?.defaultState;
      const newState =
        currentState === DoorState.LOCKED
          ? DoorState.UNLOCKED
          : DoorState.LOCKED;
      emitNet(
        "hm-doorlock:request-door-update",
        doorIdToToggle,
        newState,
        true
      ); // Send admin override flag
    } else {
      console.warn(
        "[HM-DoorLock] Admin toggle: Could not determine which door to toggle based on target data:",
        eventData.target
      );
    }
  }
);

// ==================== EXPORTS ====================

/**
 * Export functions for other resources to use
 */
export { findNearestDoor, updateDoorState };

// Global exports for other resources
(global as any).exports("findNearestDoor", findNearestDoor);
(global as any).exports("updateDoorState", updateDoorState);

// ==================== TESTING COMMANDS ====================

/**
 * Test commands for development and debugging
 */
if (DOOR_SYSTEM_CONFIG.debug.enabled) {
  console.log(
    "[HM-DoorLock] Debug mode enabled - additional logging and test features available"
  );

  // Add test command for manual door toggling
  RegisterCommand(
    "toggledoor",
    () => {
      const nearestDoor = findNearestDoor();
      if (nearestDoor) {
        emitNet("hm-doorlock:toggle-door", nearestDoor.id);
        console.log(`[HM-DoorLock] Toggling nearest door: ${nearestDoor.id}`);
      } else {
        console.log("[HM-DoorLock] No door found nearby");
      }
    },
    false
  );

  // Add command to list all doors
  RegisterCommand(
    "listdoors",
    () => {
      console.log("=== Door System Status ===");
      console.log(`Total doors configured: ${DOOR_DEFINITIONS.length}`);
      console.log(`Registered doors: ${registeredDoors.size}`);
      console.log(`Cached states: ${localDoorStates.size}`);

      DOOR_DEFINITIONS.forEach((door) => {
        const state = localDoorStates.get(door.id) || "UNKNOWN";
        console.log(`- ${door.id}: ${state} (${door.name})`);
      });
    },
    false
  );

  console.log("[HM-DoorLock] Debug commands: /toggledoor, /listdoors");
}
