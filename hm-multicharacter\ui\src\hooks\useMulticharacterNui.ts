import { useState, useEffect, useCallback, useRef } from 'react';
import { registerNuiListener, sendNuiMessage } from '../utils/nui';
import { isBrowser } from '../utils/environment';
import {
  MulticharacterEvents,
  CharactersDataPayload,
  CharacterSelectedPayload,
  CharacterCreatedPayload,
  CharacterDeletedPayload,
  ErrorPayload,
  SelectCharacterPayload,
  CreateCharacterPayload,
  DeleteCharacterPayload,
  CharacterHoverPayload
} from '../../../scripts/shared/events';
import { CharacterSelectionData, CharacterCreationData } from '../../../scripts/shared/character.types';

export interface UseMulticharacterResult {
  characters: CharacterSelectionData[];
  selected?: CharacterSelectedPayload;
  created?: CharacterCreatedPayload;
  deleted?: CharacterDeletedPayload;
  error?: string;
  visible: boolean;
  selectCharacter: (stateid: string) => void;
  createCharacter: (data: CharacterCreationData) => void;
  deleteCharacter: (stateid: string) => void;
  playCharacter: (stateid: string) => void;
  hoverCharacter: (stateid: string, spawnPointIndex: number) => void;
  hoverCharacterEnd: () => void;
  closeUI: () => void;
}

export const useMulticharacterNui = (): UseMulticharacterResult => {
  const [characters, setCharacters] = useState<CharacterSelectionData[]>([]);
  const [selected, setSelected] = useState<CharacterSelectedPayload>();
  const [created, setCreated] = useState<CharacterCreatedPayload>();
  const [deleted, setDeleted] = useState<CharacterDeletedPayload>();
  const [error, setError] = useState<string>();
  const [visible, setVisible] = useState<boolean>(isBrowser() ? true : false);

  // Store the cleanup function in a ref so we can access it later
  const cleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Send ready message to client
    console.log('NUI: Sending ready message to client');
    sendNuiMessage(MulticharacterEvents.NUI_READY);

    // Register listener for all NUI messages
    const cleanup = registerNuiListener((type, data) => {
      console.log(`NUI: Received message type: ${type}`, data);

      switch (type) {
        case 'showUI':
          setVisible(true);
          break;
        case 'hideUI':
          setVisible(false);
          break;
        case 'closeUI':
          // Completely unmount UI and clean up all event listeners
          console.log('NUI: Closing UI and cleaning up event listeners');
          setVisible(false);

          // Call the cleanup function to remove event listeners
          if (cleanupRef.current) {
            cleanupRef.current();
            cleanupRef.current = null;
          }

          // Additional cleanup to ensure UI is fully unmounted
          // Force the body and root element to be transparent
          document.body.style.backgroundColor = 'transparent';
          document.body.style.background = 'none';

          // Add a style element to force transparency
          const styleEl = document.createElement('style');
          styleEl.innerHTML = `
            html, body, #root, #root > div {
              background-color: transparent !important;
              background: none !important;
              backdrop-filter: none !important;
            }

            body::before, body::after {
              content: none !important;
              display: none !important;
            }

            * {
              background-color: transparent !important;
            }
          `;
          document.head.appendChild(styleEl);

          // Get the root element and ensure it's transparent
          const rootElement = document.getElementById('root');
          if (rootElement) {
            rootElement.style.backgroundColor = 'transparent';
            rootElement.style.background = 'none';
            rootElement.style.display = 'none';
            rootElement.style.zIndex = '-9999';
            rootElement.style.opacity = '0';
          }

          // Create an invisible overlay to capture any remaining elements
          const overlay = document.createElement('div');
          overlay.id = 'nui-cleanup-overlay';
          overlay.style.position = 'fixed';
          overlay.style.top = '0';
          overlay.style.left = '0';
          overlay.style.width = '100vw';
          overlay.style.height = '100vh';
          overlay.style.backgroundColor = 'transparent';
          overlay.style.zIndex = '9999';
          overlay.style.pointerEvents = 'none';
          document.body.appendChild(overlay);

          // Remove the overlay after a short delay
          setTimeout(() => {
            if (overlay && overlay.parentNode) {
              overlay.parentNode.removeChild(overlay);
            }
          }, 100);
          break;
        case 'updateCharacters':
          console.log('NUI: Updating characters list', data);
          setCharacters((data as CharactersDataPayload).characters || []);
          break;
        case 'characterSelected':
          setSelected(data as CharacterSelectedPayload);
          break;
        case 'characterCreated':
          console.log('NUI: Character created', data);
          setCreated(data as CharacterCreatedPayload);
          break;
        case 'characterDeleted':
          setDeleted(data as CharacterDeletedPayload);
          break;
        case 'error':
          console.error('NUI: Error received', data);
          setError((data as ErrorPayload).message);
          break;
      }
    });

    // Store the cleanup function in the ref
    cleanupRef.current = cleanup;

    // Return cleanup function
    return cleanup;
  }, []);  const selectCharacter = useCallback((stateid: string) => {
    console.log(`NUI: Selecting character with stateid ${stateid}`);
    const payload: SelectCharacterPayload = { stateid: stateid };
    sendNuiMessage(MulticharacterEvents.NUI_SELECT_CHARACTER, payload);
  }, []);

  const createCharacter = useCallback((data: CharacterCreationData) => {
    const payload: CreateCharacterPayload = { characterData: data };
    sendNuiMessage(MulticharacterEvents.NUI_CREATE_CHARACTER, payload);
  }, []);

  const deleteCharacter = useCallback((stateid: string) => {
    const payload: DeleteCharacterPayload = { stateid: stateid };
    sendNuiMessage(MulticharacterEvents.NUI_DELETE_CHARACTER, payload);
  }, []);
  const playCharacter = useCallback((stateid: string) => {
    console.log(`NUI: Playing character with stateid ${stateid}`);
    const payload: SelectCharacterPayload = { stateid: stateid };
    sendNuiMessage(MulticharacterEvents.NUI_PLAY_CHARACTER, payload);
  }, []);

  const hoverCharacter = useCallback((stateid: string, spawnPointIndex: number) => {
    console.log(`NUI: Hovering character with stateid ${stateid} at spawn point ${spawnPointIndex}`);
    const payload: CharacterHoverPayload = { stateid: stateid, spawnPointIndex: spawnPointIndex };
    sendNuiMessage(MulticharacterEvents.NUI_CHARACTER_HOVER, payload);
  }, []);

  const hoverCharacterEnd = useCallback(() => {
    console.log('NUI: Ending character hover');
    sendNuiMessage(MulticharacterEvents.NUI_CHARACTER_HOVER_END);
  }, []);

  const closeUI = useCallback(() => {
    sendNuiMessage(MulticharacterEvents.NUI_CLOSE);
  }, []);

  return { characters, selected, created, deleted, error, visible, selectCharacter, createCharacter, deleteCharacter, playCharacter, hoverCharacter, hoverCharacterEnd, closeUI };
};
