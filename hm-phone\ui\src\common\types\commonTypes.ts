export interface Vehicle {
  id: number;
  name: string;
  brand: string;
  location: string;
  category: string;
  type: string;
  status: string;
  fuel: number;
  licensePlate: string;
  imageUrl: string;
}

export interface UserProfile {
  id: number;
  name: string;
  stateid: string;
  phoneNumber: string;
  imageUrl: string;
}

// Notification types have been moved to ui/src/notifications/types/types.ts

// JobCenter types have been moved to ui/src/apps/jobCenter/types/jobCenterTypes.ts
