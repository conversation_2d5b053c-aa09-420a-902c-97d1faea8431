/**
 * Dialer store for the Contacts app
 */
import { create } from 'zustand';
import { CurrentCall } from '../types/dialerTypes';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { isBrowser } from '../../../utils/environment';

interface DialerState {
  // State
  currentCall: CurrentCall | null;
  loading: boolean;

  // Actions
  actions: {
    makeCall: (number: string, name?: string, photo?: string) => Promise<void>;
    endCall: () => Promise<void>;
    toggleMute: () => void;
    toggleSpeaker: () => void;
  };

  // Handlers
  handlers: {
    onCallStateChanged: (callState: Partial<CurrentCall>) => void;
    onCallEnded: (reason: 'ended' | 'missed' | 'rejected') => void;
  };
}

export const useDialerStore = create<DialerState>((set, get) => ({
  // State
  currentCall: null,
  loading: false,

  // Actions
  actions: {
    // Make a call
    makeCall: async (number: string, name?: string, photo?: string) => {
      console.log(`[Dialer] Making call to ${number}`);

      // Create a new current call object
      const newCall: CurrentCall = {
        number,
        name,
        photo,
        state: 'calling',
        startTime: Date.now(),
        duration: 0,
        muted: false,
        speakerOn: false
      };

      // Update the store with the new call
      set({ currentCall: newCall });

      try {
        // In browser mode, simulate a call
        if (isBrowser) {
          console.log('[Dialer] Simulating call in browser mode');

          // Simulate call connection after a delay
          setTimeout(() => {
            if (get().currentCall?.state === 'calling') {
              get().handlers.onCallStateChanged({ state: 'connected' });

              // Start the call duration timer
              const intervalId = setInterval(() => {
                const currentCall = get().currentCall;
                if (currentCall && currentCall.state === 'connected') {
                  get().handlers.onCallStateChanged({
                    duration: Math.floor((Date.now() - currentCall.startTime) / 1000)
                  });
                } else {
                  clearInterval(intervalId);
                }
              }, 1000);
            }
          }, 2000);

          return;
        }

        // In FiveM mode, request from client
        console.log('[Dialer] Requesting call from client');

        await clientRequests.send('dialer', 'startCall', {
          number,
          name,
          photo
        });
      } catch (error) {
        console.error('[Dialer] Error making call:', error);

        // End the call on error
        get().actions.endCall();
      }
    },

    // End a call
    endCall: async () => {
      console.log('[Dialer] Ending call');

      const { currentCall } = get();

      if (!currentCall) {
        console.warn('[Dialer] No active call to end');
        return;
      }

      try {
        // In browser mode, just update the store
        if (isBrowser) {
          console.log('[Dialer] Ending call in browser mode');

          // Update the call state
          get().handlers.onCallStateChanged({ state: 'ended' });

          // End the call after a delay
          setTimeout(() => {
            get().handlers.onCallEnded('ended');
          }, 1000);

          return;
        }

        // In FiveM mode, request from client
        console.log('[Dialer] Requesting call end from client');

        await clientRequests.send('dialer', 'endCall');
      } catch (error) {
        console.error('[Dialer] Error ending call:', error);

        // Force end the call on error
        get().handlers.onCallEnded('ended');
      }
    },

    // Toggle mute
    toggleMute: () => {
      console.log('[Dialer] Toggling mute');

      const { currentCall } = get();

      if (!currentCall) {
        console.warn('[Dialer] No active call to toggle mute');
        return;
      }

      // Toggle mute state
      get().handlers.onCallStateChanged({ muted: !currentCall.muted });

      // In FiveM mode, request from client
      if (!isBrowser) {
        clientRequests.send('dialer', 'toggleMute');
      }
    },

    // Toggle speaker
    toggleSpeaker: () => {
      console.log('[Dialer] Toggling speaker');

      const { currentCall } = get();

      if (!currentCall) {
        console.warn('[Dialer] No active call to toggle speaker');
        return;
      }

      // Toggle speaker state
      get().handlers.onCallStateChanged({ speakerOn: !currentCall.speakerOn });

      // In FiveM mode, request from client
      if (!isBrowser) {
        clientRequests.send('dialer', 'toggleSpeaker');
      }
    }
  },

  // Handlers
  handlers: {
    // Update call state
    onCallStateChanged: (callState: Partial<CurrentCall>) => {
      console.log('[Dialer] Updating call state:', callState);

      const { currentCall } = get();

      if (!currentCall) {
        console.warn('[Dialer] No active call to update state');
        return;
      }

      // Update the call state
      set({
        currentCall: {
          ...currentCall,
          ...callState
        }
      });
    },

    // Handle call ended
    onCallEnded: (reason: 'ended' | 'missed' | 'rejected') => {
      console.log(`[Dialer] Call ended with reason: ${reason}`);

      const { currentCall } = get();

      if (!currentCall) {
        console.warn('[Dialer] No active call to end');
        return;
      }

      // Update the call state
      set({
        currentCall: {
          ...currentCall,
          state: 'ended',
          endReason: reason
        }
      });

      // Clear the current call after a delay
      setTimeout(() => {
        set({ currentCall: null });
      }, 3000);
    }
  }
}));
