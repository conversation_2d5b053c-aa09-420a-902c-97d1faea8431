/**
 * Types for the dialer functionality
 */

import { Call as SharedCall, CallStatus as SharedCallStatus } from '@shared/types';

// Re-export the shared types
export type CallStatus = SharedCallStatus;
export type CallState = 'idle' | 'calling' | 'connected' | 'ended';

// Use the shared Call type directly
export type Call = SharedCall;

export interface CurrentCall {
  number: string; // This is the contact_number
  name?: string; // UI-specific, derived from contacts
  photo?: string; // UI-specific, derived from contacts
  state: CallState;
  startTime: number;
  duration: number;
  muted: boolean;
  speakerOn: boolean;
  endReason?: 'ended' | 'missed' | 'rejected';
}

export interface CallData {
  number: string;
  name?: string;
  photo?: string;
  directCall?: boolean;
}
