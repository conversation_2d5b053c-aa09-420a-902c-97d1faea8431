import React, { useEffect, useState, useRef } from 'react';
import { useNavigation } from '../../navigation/hooks';

import Conversation from './components/conversation/Conversation';
import BottomNav from './components/conversation/BottomNav';
import ChatsTab from './components/ChatsTab';
import ProfileTab from './components/ProfileTab';
import GroupInfoScreen from './components/GroupInfoScreen';
import NewConversationView from './components/NewConversationView';
import { useNavigationStore } from '../../navigation/navigationStore';
import { useMessagesStore } from './stores/messagesStore';
import { useDialerStore } from '../contacts/stores/dialerStore';
import { Contact, Message } from '@shared/types';
import { usePhoneStore } from '../../common/stores/phoneStateStore';

const Messages: React.FC = () => {
  const { openAppView, switchTab, openView, goBack } = useNavigation();
  const { currentView = 'main', currentTab, history } = useNavigationStore();
  const { activeChat, conversations, ui, actions } = useMessagesStore();
  const { setActiveChat } = ui;
  const { userProfile } = usePhoneStore();

  // Load conversations when the app is opened
  useEffect(() => {
    actions.getConversations();
  }, [actions]);

  useEffect(() => {
    if (!currentView || currentView === 'home') {
      openView('main');
    }
    // We don't set a default tab here with switchTab,
    // but we use 'chats' as the default in the activeTab variable
  }, [currentView, openView]);

  // Use a ref to track if we've already processed this conversation view
  const processedRef = useRef<number | null>(null);

  useEffect(() => {
    // Only run this effect when the view is 'conversation'
    if (currentView !== 'conversation') return;

    const currentEntry = history.slice(-1)[0];
    const navigationData = currentEntry?.data || {};
    const chatId = navigationData.id || navigationData.chatId;

    // Case 1: We have a chatId from navigation that's different from activeChat
    if (chatId && chatId !== activeChat) {
      console.log('Setting active chat from navigation data:', chatId);
      setActiveChat(Number(chatId));

      // Mark conversation as read if needed
      const conversation = conversations.find(c => c.id === Number(chatId));
      if (conversation?.members && conversation.members[userProfile?.phoneNumber || '']?.unread_count && conversation.members[userProfile?.phoneNumber || '']?.unread_count > 0) {
        useMessagesStore.getState().ui.markAsRead(Number(chatId));
      }

      // Update our processed ref
      processedRef.current = Number(chatId);
    }
    // Case 2: We already have an activeChat and we haven't processed it yet
    else if (activeChat && processedRef.current !== activeChat) {
      console.log('[MessagesApp] Using existing active chat ID:', activeChat);

      // Convert ID to number for consistent format
      const numericId = Number(activeChat);

      // Ensure the conversation exists in our list
      const conversation = conversations.find(c => c.id === numericId);

      if (conversation) {
        // Mark as read if needed
        if (conversation?.members && conversation.members[userProfile?.phoneNumber || '']?.unread_count && conversation.members[userProfile?.phoneNumber || '']?.unread_count > 0) {
          useMessagesStore.getState().ui.markAsRead(conversation.id);
        }
      } else {
        console.warn('Active chat not found in conversations list:', activeChat);
      }

      // Update our processed ref
      processedRef.current =
        typeof activeChat === 'number' ? activeChat : activeChat ? Number(activeChat) : null;
    }
  }, [currentView, history, activeChat, setActiveChat, conversations, userProfile?.phoneNumber]);

  const colors = {
    bg: {
      primary: 'bg-gray-900'
    },
    text: {
      primary: 'text-white',
      secondary: 'text-gray-300',
      tertiary: 'text-gray-500'
    },
    border: {
      primary: 'border-gray-800'
    }
  };
  // Use 'chats' as the default tab if no tab is selected
  const activeTab = currentTab || 'chats';
  const [searchTerm, setSearchTerm] = useState('');
  // We no longer need the showGroupInfo state as we're using navigation

  const handleStartCall = (phone: string) => {
    // Get the dialer store and make the call
    const { actions: dialerActions } = useDialerStore.getState();
    dialerActions.makeCall(phone);

    // Navigate to the contacts app with the dialer tab
    openAppView('contacts', 'main', { tab: 'dialer' });
  };

  const renderContent = () => {
    if (currentView === 'conversation' && activeChat) {
      return (
        <Conversation
          key={`conversation-${activeChat}`}
          activeChat={activeChat}
          handleStartCall={handleStartCall}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
      );
    }

    // Show the selected tab content
    switch (activeTab) {
      case 'chats':
        return (
          <ChatsTab
            conversations={conversations}
            onNewConversation={() => {
              console.log('[Messages] Opening new conversation view');
              openView('newConversation');
            }}
          />
        );
      case 'profile':
        return <ProfileTab />;
      default:
        // This should never happen, but just in case
        return (
          <div className="flex flex-col items-center justify-center h-full p-4 text-center">
            <div className="text-white/60">
              <p>Unknown tab selected</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`h-full w-full flex flex-col ${colors.bg.primary} pt-8 pb-3 relative`}>
      {/* Main view with tabs */}
      {(currentView === 'main' || !currentView) && (
        <>
          <div className="flex-1 overflow-hidden">{renderContent()}</div>
          <BottomNav
            activeTab={activeTab}
            switchTab={switchTab}
            totalUnread={conversations.reduce((total, conv) => total + (conv.members[userProfile?.phoneNumber || '']?.unread_count || 0), 0)}
          />
        </>
      )}
      {/* Conversation view */}
      {currentView === 'conversation' && <>{renderContent()}</>}
      {/* Group info view */}
      {currentView === 'info' && <GroupInfoScreen />}
      {/* New conversation view */}
      {currentView === 'newConversation' && (
        <NewConversationView
          onBack={goBack}
          onCreateConversation={async (contacts: Contact[], initialMessage: Message) => {
            if (contacts.length === 0) return;
            try {
              // Create the conversation using the MessagesStore
              const conversationId = await actions.createConversation(contacts, initialMessage);

              if (conversationId !== null) {
                // Set the active chat and navigate to the conversation view
                setActiveChat(conversationId);
                openView('conversation', { chatId: conversationId });
              }
            } catch (error) {
              console.error('[Messages] Error creating conversation:', error);
              // You could show an error message here
            }
          }}
        />
      )}
    </div>
  );
};

export default Messages;
