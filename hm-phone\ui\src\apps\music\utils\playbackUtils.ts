/**
 * Utility functions for music playback control
 */

import { Song } from '../types/musicTypes';

// Timer management
let playbackTimer: number | null = null;

/**
 * Start a playback timer that updates the current time every second
 * @param getCurrentState Function to get the current player state
 * @param updateState Function to update the player state
 * @param onSongEnd Function to call when the song ends
 * @returns void
 */
export const startPlayback = (
  getCurrentState: () => {
    currentSong: Song | null;
    isPlaying: boolean;
    currentTime: number;
    duration: number;
    isRepeatEnabled: boolean;
  },
  updateState: (
    updates: Partial<{
      currentTime: number;
      isPlaying: boolean;
    }>
  ) => void,
  onSongEnd: () => void
): void => {
  console.log('[PlaybackUtils] Starting playback timer');

  // Don't start if already running
  if (playbackTimer) {
    console.log('[PlaybackUtils] Playback timer already running');
    return;
  }

  // Don't start if no song is selected or if not playing
  const state = getCurrentState();
  if (!state.currentSong || !state.isPlaying) {
    console.log('[PlaybackUtils] Cannot start playback - no song selected or not playing');
    return;
  }

  playbackTimer = window.setInterval(() => {
    const state = getCurrentState();
    if (!state.currentSong) {
      stopPlayback();
      return;
    }

    // Update current time
    const newTime = state.currentTime + 1;

    // Check if song has ended
    if (newTime >= state.duration) {
      // Song ended, play next song or loop
      if (state.isRepeatEnabled) {
        // Loop current song
        updateState({ currentTime: 0 });
      } else {
        // Try to play next song
        onSongEnd();
      }
    } else {
      // Update current time
      updateState({ currentTime: newTime });
      console.log(`[PlaybackUtils] Current time updated: ${newTime}s / ${state.duration}s`);
    }
  }, 1000); // Update every second
};

/**
 * Stop the playback timer
 */
export const stopPlayback = (): void => {
  console.log('[PlaybackUtils] Stopping playback timer');
  if (playbackTimer) {
    window.clearInterval(playbackTimer);
    playbackTimer = null;
    console.log('[PlaybackUtils] Playback timer cleared');
  } else {
    console.log('[PlaybackUtils] No playback timer to clear');
  }
};

/**
 * Queue management utilities
 */

/**
 * Get the next song from the queue
 * @param queue Current queue of songs
 * @param isShuffleEnabled Whether shuffle is enabled
 * @returns The next song and the updated queue
 */
export const getNextSongFromQueue = (
  queue: Song[],
  isShuffleEnabled: boolean
): { nextSong: Song | null; newQueue: Song[] } => {
  if (queue.length === 0) {
    return { nextSong: null, newQueue: [] };
  }

  let nextSongIndex = 0;
  let nextSong = queue[nextSongIndex];

  // If shuffle is enabled, pick a random song from the queue
  if (isShuffleEnabled && queue.length > 1) {
    nextSongIndex = Math.floor(Math.random() * queue.length);
    nextSong = queue[nextSongIndex];
  }

  const newQueue = [...queue.slice(0, nextSongIndex), ...queue.slice(nextSongIndex + 1)];

  return { nextSong, newQueue };
};

/**
 * Volume control utilities
 */

/**
 * Set the volume level, ensuring it's within valid range
 * @param volume Volume level (0-1)
 * @returns Normalized volume and muted state
 */
export const normalizeVolume = (volume: number): { volume: number; isMuted: boolean } => {
  const normalizedVolume = Math.max(0, Math.min(1, volume));
  return {
    volume: normalizedVolume,
    isMuted: normalizedVolume === 0
  };
};

/**
 * Toggle mute state
 * @param isMuted Current mute state
 * @param currentVolume Current volume level
 * @returns New mute state and volume level
 */
export const toggleMuteState = (
  isMuted: boolean,
  currentVolume: number
): { isMuted: boolean; volume: number } => {
  return {
    isMuted: !isMuted,
    volume: isMuted ? currentVolume || 0.7 : 0
  };
};
