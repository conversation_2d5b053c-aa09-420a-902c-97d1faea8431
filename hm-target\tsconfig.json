{"compilerOptions": {"target": "ES2018", "lib": ["ES2018"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./build", "rootDir": ".", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@shared/*": ["scripts/shared/*"], "@client/*": ["scripts/client/*"], "@server/*": ["scripts/server/*"]}}, "include": ["scripts/client/**/*", "scripts/server/**/*", "scripts/shared/**/*"], "exclude": ["node_modules", "build", "ui"]}