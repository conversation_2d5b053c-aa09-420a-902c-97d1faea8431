/**
 * LifeSnap App - Client Side
 *
 * This file handles client-side functionality for the LifeSnap app.
 * The LifeSnap app is initialized on demand when the app is opened.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the LifeSnap app
 */
export function initializeLifeSnapApp(): void {
    console.log('[LifeSnap] Initializing client-side LifeSnap app');

    // Register client events
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();
}

/**
 * Register client events for the LifeSnap app
 */
function registerClientEvents(): void {
    // Register event for feed data
    onNet('hm-phone:lifeSnapFeed', (feedData: any) => {
        console.log('[LifeSnap] Feed data received:', feedData.length);

        // Send the feed data to the UI
        sendToUI('setFeed', feedData);
    });

    // Register event for stories data
    onNet('hm-phone:lifeSnapStories', (storiesData: any) => {
        console.log('[LifeSnap] Stories data received:', storiesData.length);

        // Send the stories data to the UI
        sendToUI('setStories', storiesData);
    });

    // Register event for profile data
    onNet('hm-phone:lifeSnapProfile', (profileData: any) => {
        console.log('[LifeSnap] Profile data received:', profileData);

        // Send the profile data to the UI
        sendToUI('setProfile', profileData);
    });

    // Register event for post created
    onNet('hm-phone:lifeSnapPostCreated', (postData: any) => {
        console.log('[LifeSnap] Post created:', postData);

        // Send the post data to the UI
        sendToUI('postCreated', postData);
    });

    // Register event for story created
    onNet('hm-phone:lifeSnapStoryCreated', (storyData: any) => {
        console.log('[LifeSnap] Story created:', storyData);

        // Send the story data to the UI
        sendToUI('storyCreated', storyData);
    });

    // Register event for post liked
    onNet('hm-phone:lifeSnapPostLiked', (postId: number, likes: number) => {
        console.log(`[LifeSnap] Post ${postId} liked, new likes: ${likes}`);

        // Send the like data to the UI
        sendToUI('postLiked', { postId, likes });
    });

    // Register event for comment added
    onNet('hm-phone:lifeSnapCommentAdded', (postId: number, commentData: any) => {
        console.log(`[LifeSnap] Comment added to post ${postId}:`, commentData);

        // Send the comment data to the UI
        sendToUI('commentAdded', { postId, comment: commentData });
    });

    // Register event for error
    onNet('hm-phone:lifeSnapError', (errorMessage: string) => {
        console.error('[LifeSnap] Error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });
}

/**
 * Register NUI handlers for the LifeSnap app
 */
function registerNUIHandlers(): void {
    // Register handler for getting feed
    registerAppHandler('lifeSnap', 'getFeed', async () => {
        console.log('[LifeSnap] Received getFeed request from UI');

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Getting feed for player: ${stateid || identifier}`);

            // Request feed from the server
            emitNet('hm-phone:getLifeSnapFeed');

            // Return success to the UI
            // The actual feed data will be sent via the hm-phone:lifeSnapFeed event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error getting feed:', error);
            return { success: false, error: 'Failed to get feed' };
        }
    });

    // Register handler for getting stories
    registerAppHandler('lifeSnap', 'getStories', async () => {
        console.log('[LifeSnap] Received getStories request from UI');

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Getting stories for player: ${stateid || identifier}`);

            // Request stories from the server
            emitNet('hm-phone:getLifeSnapStories');

            // Return success to the UI
            // The actual stories data will be sent via the hm-phone:lifeSnapStories event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error getting stories:', error);
            return { success: false, error: 'Failed to get stories' };
        }
    });

    // Register handler for getting profile
    registerAppHandler('lifeSnap', 'getProfile', async (data: any) => {
        console.log('[LifeSnap] Received getProfile request from UI:', data);

        try {
            // Extract the profile data
            const { profileId } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Getting profile for player: ${stateid || identifier}`);

            // Request profile from the server
            emitNet('hm-phone:getLifeSnapProfile', profileId);

            // Return success to the UI
            // The actual profile data will be sent via the hm-phone:lifeSnapProfile event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error getting profile:', error);
            return { success: false, error: 'Failed to get profile' };
        }
    });

    // Register handler for creating a post
    registerAppHandler('lifeSnap', 'createPost', async (data: any) => {
        console.log('[LifeSnap] Received createPost request from UI:', data);

        try {
            // Extract the post data
            const { content, imageUrl, location } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Creating post for player: ${stateid || identifier}`);

            // Send the post data to the server
            emitNet('hm-phone:createLifeSnapPost', content, imageUrl, location);

            // Return success to the UI
            // The actual post data will be sent via the hm-phone:lifeSnapPostCreated event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error creating post:', error);
            return { success: false, error: 'Failed to create post' };
        }
    });

    // Register handler for creating a story
    registerAppHandler('lifeSnap', 'createStory', async (data: any) => {
        console.log('[LifeSnap] Received createStory request from UI:', data);

        try {
            // Extract the story data
            const { content, imageUrl, location } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Creating story for player: ${stateid || identifier}`);

            // Send the story data to the server
            emitNet('hm-phone:createLifeSnapStory', content, imageUrl, location);

            // Return success to the UI
            // The actual story data will be sent via the hm-phone:lifeSnapStoryCreated event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error creating story:', error);
            return { success: false, error: 'Failed to create story' };
        }
    });

    // Register handler for liking a post
    registerAppHandler('lifeSnap', 'likePost', async (data: any) => {
        console.log('[LifeSnap] Received likePost request from UI:', data);

        try {
            // Extract the post data
            const { postId } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Liking post ${postId} for player: ${stateid || identifier}`);

            // Send the like data to the server
            emitNet('hm-phone:likeLifeSnapPost', postId);

            // Return success to the UI
            // The actual like data will be sent via the hm-phone:lifeSnapPostLiked event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error liking post:', error);
            return { success: false, error: 'Failed to like post' };
        }
    });

    // Register handler for adding a comment
    registerAppHandler('lifeSnap', 'addComment', async (data: any) => {
        console.log('[LifeSnap] Received addComment request from UI:', data);

        try {
            // Extract the comment data
            const { postId, content } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[LifeSnap] Adding comment to post ${postId} for player: ${stateid || identifier}`);

            // Send the comment data to the server
            emitNet('hm-phone:addLifeSnapComment', postId, content);

            // Return success to the UI
            // The actual comment data will be sent via the hm-phone:lifeSnapCommentAdded event
            return { success: true };
        } catch (error) {
            console.error('[LifeSnap] Error adding comment:', error);
            return { success: false, error: 'Failed to add comment' };
        }
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'lifeSnap',
        type,
        data
    });
}
