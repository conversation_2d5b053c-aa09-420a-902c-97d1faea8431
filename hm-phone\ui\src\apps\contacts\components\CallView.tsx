import { motion } from 'framer-motion';
import { CurrentCall } from '../types/dialerTypes';
import { useEffect } from 'react';
import { useNavigation } from '../../../navigation/hooks';

interface CallViewProps {
  call: CurrentCall;
  onEndCall: () => void;
  onToggleMute: () => void;
  onToggleSpeaker: () => void;
  onMinimize?: () => void;
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const CallView: React.FC<CallViewProps> = ({
  call,
  onEndCall,
  onToggleMute,
  onToggleSpeaker,
  onMinimize
}) => {
  useNavigation();

  // Hide the topbar active call pill when in the call view
  useEffect(() => {
    // Set a flag in localStorage to indicate we're in the call view
    localStorage.setItem('inCallView', 'true');

    // Clean up when component unmounts
    return () => {
      localStorage.removeItem('inCallView');
    };
  }, []);
  return (
    <div className="h-full w-full flex flex-col bg-black relative overflow-hidden">
      {/* Blurred background */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-800 to-black opacity-80 z-0"></div>

      {/* Minimize button */}
      <div className="absolute top-8 right-4 z-20">
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={onMinimize}
          className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20"
        >
          <i className="fas fa-chevron-down text-white"></i>
        </motion.button>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col h-full items-center pt-16">
        {/* Contact info */}
        <div className="text-center mb-4">
          <h1 className="text-white text-3xl font-semibold">{call.name || call.number}</h1>

          {/* Call duration */}
          <div className="text-white/70 mt-2">
            {call.state === 'connected'
              ? formatDuration(call.duration)
              : call.state === 'calling'
              ? 'Calling...'
              : call.state === 'ended'
              ? 'Call ended'
              : ''}
          </div>
        </div>

        {/* Spacer */}
        <div className="flex-1"></div>

        {/* Action buttons */}
        <div className="grid grid-cols-3 gap-6 mb-8 w-full px-8">
          {/* Mute button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={onToggleMute}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i
                className={`fas ${
                  call.muted ? 'fa-microphone-slash' : 'fa-microphone'
                } text-white text-xl`}
              ></i>
            </motion.button>
            <span className="text-white/70 text-xs">{call.muted ? 'Unmute' : 'Mute'}</span>
          </div>

          {/* Keypad button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i className="fas fa-th text-white text-xl"></i>
            </motion.button>
            <span className="text-white/70 text-xs">Keypad</span>
          </div>

          {/* Speaker button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={onToggleSpeaker}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i className={`fas fa-volume-up text-white text-xl`}></i>
            </motion.button>
            <span className="text-white/70 text-xs">
              {call.speakerOn ? 'Speaker Off' : 'Speaker'}
            </span>
          </div>

          {/* Add call button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i className="fas fa-plus text-white text-xl"></i>
            </motion.button>
            <span className="text-white/70 text-xs">Add call</span>
          </div>

          {/* FaceTime button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i className="fas fa-video text-white text-xl"></i>
            </motion.button>
            <span className="text-white/70 text-xs">FaceTime</span>
          </div>

          {/* Contacts button */}
          <div className="flex flex-col items-center">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2"
            >
              <i className="fas fa-user text-white text-xl"></i>
            </motion.button>
            <span className="text-white/70 text-xs">Contacts</span>
          </div>
        </div>

        {/* End call button */}
        <div className="mb-8">
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={onEndCall}
            className="w-16 h-16 rounded-full bg-red-500 flex items-center justify-center"
          >
            <i className="fas fa-phone-slash text-white text-xl"></i>
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default CallView;
