import { create } from 'zustand';
import { PlayerData } from '../../apps/settings/types/playerTypes';
import { clientRequests } from '../../fivem/clientRequestSender';
import { settingsMockData } from '../../fivem/mockData';
import { useContactsStore } from '../../apps/contacts/stores/contactsStore';
import { useMessagesStore } from '../../apps/messages/stores/messagesStore';
import { useLifeSnapStore } from '../../apps/lifeSnap/stores/lifeSnapStore';
import { useSettingsStore } from '../../apps/settings/stores/settingsStore';
import { useMusicStore } from '../../apps/music/stores/musicStore';
import { useYellowPagesStore } from '../../apps/yellowPages/stores/yellowPagesStore';
import { isBrowser } from '../../utils/environment';

/**
 * Phone states enum - matches the client-side enum
 * CLOSED - Phone is completely hidden
 * PEEK - Phone is partially visible, peeking into view for notifications/calls
 * OPEN - Phone is fully visible
 */
export enum PhoneStateEnum {
  CLOSED = 0,
  PEEK = 1,
  OPEN = 2
}

interface PhoneState {
  // State
  phoneState: PhoneStateEnum;
  isWifiOn: boolean;
  isAirplaneModeOn: boolean;
  isSoundOn: boolean;
  isLoading: boolean;
  isInitialLoad: boolean; // Flag to track initial load
  isLoadingEssentialData: boolean; // Flag to track if essential data is being loaded
  userProfile: PlayerData;

  // Actions
  actions: {
    getPlayerData: () => Promise<void>;
    loadEssentialData: () => Promise<void>;
    loadAppData: (appId: string) => Promise<void>;
    cleanupOnClose: () => void;
    toggleVisibility: () => void;
    setPhoneState: (state: PhoneStateEnum) => void;
    toggleWifi: () => void;
    toggleAirplaneMode: () => void;
    toggleSound: () => void;
  };
  // Handlers
  handlers: {
    onSetPlayerData: (data: PlayerData) => void;
    onSetPhoneState: (state: PhoneStateEnum) => void;
  };
}

export const usePhoneStore = create<PhoneState>((set, get) => ({
  // Initialize with OPEN in browser mode, CLOSED in FiveM mode
  phoneState: isBrowser ? PhoneStateEnum.OPEN : PhoneStateEnum.CLOSED,
  isWifiOn: false,
  isAirplaneModeOn: false,
  isSoundOn: true,
  isLoading: false,
  isInitialLoad: true, // Set to true on first load
  isLoadingEssentialData: false, // Track if essential data is being loaded
  // Default user profile - will be updated when getPlayerData is called
  userProfile: {
    name: 'John Doe',
    stateid: 'FXG8T3OE',
    firstName: 'John',
    lastName: 'Doe',
    phoneNumber: '555-0000',
    identifier: '',
    job: {
      name: 'unemployed',
      label: 'Unemployed',
      grade: 0,
      gradeName: 'unemployed',
      payment: 0,
      onDuty: false,
      isBoss: false
    },
    imageUrl: ''
  },

  actions: {
    getPlayerData: async () => {
      try {
        console.log('[PhoneStore] Requesting player data from client');
        set({ isLoading: true });

        // Use clientRequests directly with mock data for browser mode
        const response = await clientRequests.send(
          'phone',
          'getPlayerData',
          {},
          settingsMockData.playerData
        );

        if (response.success && 'data' in response) {
          // Update the user profile with the received data
          get().handlers.onSetPlayerData(response.data as PlayerData);
        }
      } catch (error) {
        console.error('[PhoneStore] Error requesting player data:', error);
      } finally {
        setTimeout(() => set({ isLoading: false }), 500);
      }
    },

    loadEssentialData: async () => {
      // Check if already loading essential data to prevent duplicate loading
      if (get().isLoadingEssentialData) {
        return;
      }

      // Set loading flag
      set({ isLoadingEssentialData: true });

      try {
        // Load only essential data that's needed for the home screen
        // This includes contacts (small dataset, frequently used)
        await useContactsStore.getState().actions.getContacts();

        // Load settings (small dataset, needed for UI appearance)
        await useSettingsStore.getState().getSettings();

        // Load notification counts only (not full data)
        // This is handled by the notification system automatically
      } catch (error) {
        console.error('[PhoneStore] Error loading essential data:', error);
      } finally {
        // Clear loading flag
        set({ isLoadingEssentialData: false });
      }
    },

    loadAppData: async (appId: string) => {
      try {
        // Load data specific to the requested app
        switch (appId) {
          case 'messages': {
            // Load only conversation list (not message content)
            const messagesStore = useMessagesStore.getState();

            // Check if we already have conversations loaded
            if (messagesStore.conversations.length > 0) {
              // No need to reload conversations
            } else {
              await messagesStore.actions.getConversations();
            }
            break;
          }

          case 'contacts': {
            // Check if contacts are already loaded
            const contactsStore = useContactsStore.getState();

            if (contactsStore.contacts.length > 0) {
              // No need to load contacts again - they're permanently cached until modified
            } else {
              // Load contacts if not cached
              await contactsStore.actions.getContacts();
            }

            // TODO: Load call history when implemented
            // await contactsStore.actions.getCalls();
            break;
          }

          case 'settings': {
            // Check if settings are already loaded
            const settingsStore = useSettingsStore.getState();

            if (settingsStore.lastSettingsUpdate > 0) {
              // Settings already loaded, use cached data
            } else {
              // Load settings if not cached
              await settingsStore.getSettings();
            }
            break;
          }

          case 'lifeSnap':
            await useLifeSnapStore.getState().actions.getPosts();
            await useLifeSnapStore.getState().actions.getStories();
            break;

          case 'music': {
            const musicStore = useMusicStore.getState();
            useMusicStore.setState({ loading: true });
            try {
              await musicStore.actions.getSongs();
            } finally {
              setTimeout(() => useMusicStore.setState({ loading: false }), 500);
            }
            break;
          }

          case 'yellowPages':
            await useYellowPagesStore.getState().actions.loadAds();
            break;

          // Add cases for other apps as needed

          default:
            // No specific data loading for this app
            break;
        }
      } catch (error) {
        console.error(`[PhoneStore] Error loading data for app ${appId}:`, error);
      }
    },

    cleanupOnClose: () => {
      // This function is called when the phone is closed
      // It should clear large datasets from memory but keep essential data

      // Clear message content but keep conversation list
      const messagesStore = useMessagesStore.getState();
      messagesStore.conversations.forEach(conversation => {
        messagesStore.handlers.onSetMessages(conversation.id, []);
      });

      // Clear other large datasets
      // These methods should be implemented in their respective stores
      try {
        useLifeSnapStore.getState().handlers.onClearPostContent();
        useMusicStore.getState().handlers.onClearSongData();
        useYellowPagesStore.getState().handlers.onClearAdsContent();
      } catch (error) {
        console.error('[PhoneStore] Error during cleanup:', error);
      }
    },

    toggleVisibility: () => {
      const currentState = get().phoneState;
      // Toggle between OPEN and CLOSED
      const newState = currentState === PhoneStateEnum.OPEN ? PhoneStateEnum.CLOSED : PhoneStateEnum.OPEN;
      get().actions.setPhoneState(newState);
    },

    setPhoneState: (state: PhoneStateEnum) => {
      // Check if state is already set to avoid redundant updates
      if (get().phoneState === state) {
        return;
      }

      set({ phoneState: state });

      // Don't update the PhoneContext from here to avoid circular updates
      // The context should be the source of truth that updates the store, not vice versa

      // Notify handlers
      get().handlers.onSetPhoneState(state);
    },
    toggleWifi: () => {
      const currentState = get().isWifiOn;
      const newState = !currentState;

      // Don't allow WiFi to be turned on if airplane mode is on
      if (get().isAirplaneModeOn && newState) {
        return;
      }

      set({ isWifiOn: newState });
      // Optionally notify the game client
      try {
        clientRequests.send('phone', 'setWifiState', { enabled: newState });
      } catch (error) {
        console.error('[PhoneStore] Error updating WiFi state:', error);
      }
    },
    toggleAirplaneMode: () => {
      const currentState = get().isAirplaneModeOn;
      const newState = !currentState;

      set(state => ({
        isAirplaneModeOn: newState,
        // Turn off WiFi when airplane mode is enabled
        isWifiOn: newState ? false : state.isWifiOn
      }));

      // Optionally notify the game client
      try {
        clientRequests.send('phone', 'setAirplaneModeState', { enabled: newState });
      } catch (error) {
        console.error('[PhoneStore] Error updating airplane mode state:', error);
      }
    },
    toggleSound: () => {
      const currentState = get().isSoundOn;
      const newState = !currentState;

      set({ isSoundOn: newState });
      // Optionally notify the game client
      try {
        clientRequests.send('phone', 'setSoundState', { enabled: newState });
      } catch (error) {
        console.error('[PhoneStore] Error updating sound state:', error);
      }
    }
  },
  handlers: {
    onSetPlayerData: (data: PlayerData) => {
      set({
        userProfile: data,
        isLoading: false
      });

      // After player data is set, load only essential data
      // This ensures essential data is only loaded after player data is available
      const state = get();
      const currentPhoneState = state.phoneState;
      const { isLoadingEssentialData } = state;

      // Only load essential data if the phone is open and not already loading
      if (currentPhoneState === PhoneStateEnum.OPEN && !isLoadingEssentialData) {
        setTimeout(() => {
          state.actions.loadEssentialData();
        }, 100);
      }
    },

    onSetPhoneState: (state: PhoneStateEnum) => {
      // This handler can be used by other modules to react to phone state changes
      const { isInitialLoad, isLoadingEssentialData } = get();

      // If this is the initial load, don't load data automatically
      // This prevents loading data at startup before handlers are registered
      if (isInitialLoad) {
        console.log('[PhoneStore] Initial load detected, skipping automatic data loading');
        // Set isInitialLoad to false for future state changes
        set({ isInitialLoad: false });
        return;
      }

      // Load essential data when phone is opened (not on initial load)
      if (state === PhoneStateEnum.OPEN) {
        // Only load essential data if we have player data and not already loading
        if (get().userProfile.identifier && !isLoadingEssentialData) {
          get().actions.loadEssentialData();
        }
      }

      // Clean up data when phone is closed
      if (state === PhoneStateEnum.CLOSED) {
        get().actions.cleanupOnClose();
      }
    }
  }
}));
