/**
 * Helper function to safely parse dates from various formats
 * @param dateValue - The date value to parse
 * @returns A Date object or null if parsing fails
 */
export function parseDate(dateValue: Date | number | string | undefined): Date | null {
  if (!dateValue) {
    return null;
  }

  try {
    if (dateValue instanceof Date) {
      return dateValue;
    } else if (typeof dateValue === 'number') {
      // Check if it's a Unix timestamp (seconds) or JavaScript timestamp (milliseconds)
      const date = dateValue > 9999999999 ? new Date(dateValue) : new Date(dateValue * 1000);
      return date;
    } else if (typeof dateValue === 'string') {
      // Handle MySQL datetime format (YYYY-MM-DD HH:MM:SS)
      if (dateValue.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
        const [datePart, timePart] = dateValue.split(' ');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hours, minutes, seconds] = timePart.split(':').map(Number);
        const date = new Date(year, month - 1, day, hours, minutes, seconds);
        return date;
      } else {
        const date = new Date(dateValue);
        return date;
      }
    }
  } catch (error) {
    console.error('[TimeUtils] Error parsing date:', error, 'Value was:', dateValue);
  }

  return null;
}

/**
 * Formats a timestamp into a human-readable relative time string
 * @param timestamp - The timestamp to format (can be Date, number, or string)
 * @returns A string like "just now", "5m ago", "2h ago", "3d ago", etc.
 */
export function getTimeAgo(timestamp: Date | number | string): string {
  // Parse the timestamp
  const date = parseDate(timestamp);
  if (!date) return 'unknown';

  const now = Date.now();
  const timestampMs = date.getTime();
  const secondsAgo = Math.floor((now - timestampMs) / 1000);

  // Less than a minute
  if (secondsAgo < 60) {
    return 'just now';
  }

  // Less than an hour
  if (secondsAgo < 3600) {
    const minutes = Math.floor(secondsAgo / 60);
    return `${minutes}m ago`;
  }

  // Less than a day
  if (secondsAgo < 86400) {
    const hours = Math.floor(secondsAgo / 3600);
    return `${hours}h ago`;
  }

  // Less than a week
  if (secondsAgo < 604800) {
    const days = Math.floor(secondsAgo / 86400);
    return `${days}d ago`;
  }

  // More than a week, use date
  return date.toLocaleDateString();
}

/**
 * Formats a timestamp in WhatsApp style for conversation lists
 * - Today: Shows time (e.g., "14:23")
 * - Yesterday: Shows "Yesterday"
 * - This week: Shows day name (e.g., "Monday")
 * - This year: Shows date without year (e.g., "24 Jan")
 * - Older: Shows date with year (e.g., "24 Jan 2022")
 *
 * @param timestamp - The timestamp to format (can be Date, number, or string)
 * @returns A formatted string following WhatsApp style
 */
export function formatConversationDate(timestamp: Date | number | string): string {
  // Parse the timestamp
  const date = parseDate(timestamp);
  if (!date) return '';

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const timestampDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  // Check if it's today
  if (timestampDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // Check if it's yesterday
  if (timestampDate.getTime() === yesterday.getTime()) {
    return 'Yesterday';
  }

  // Check if it's this week (within the last 7 days)
  const dayDiff = Math.floor((today.getTime() - timestampDate.getTime()) / (1000 * 60 * 60 * 24));
  if (dayDiff < 7) {
    // Return day name (e.g., "Monday")
    return date.toLocaleDateString([], { weekday: 'long' });
  }

  // Check if it's this year
  if (date.getFullYear() === now.getFullYear()) {
    // Return date without year (e.g., "24 Jan")
    return date.toLocaleDateString([], { day: 'numeric', month: 'short' });
  }

  // It's from a previous year
  // Return date with year (e.g., "24 Jan 2022")
  return date.toLocaleDateString([], { day: 'numeric', month: 'short', year: 'numeric' });
}
