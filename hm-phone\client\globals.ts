/**
 * Client-side Global Variables
 *
 * This file declares and initializes all global variables used by the phone client.
 */

import { PhoneStateEnum } from './types';

/**
 * Declare global variables
 */
declare global {
    var phoneState: PhoneStateEnum;
    var playerPhoneNumber: string | null;
    var playerIdentifier: string | null;
    var playerStateId: string | null;
    var phoneEntity: number;
    var activeCall: boolean;
    var phoneControlsDisabled: boolean;
    var playerDataSentToUI: boolean; // Track if player data has been sent to UI
}

/**
 * Initialize all global variables with default values
 */

// Phone state
global.phoneState = PhoneStateEnum.CLOSED;
global.playerPhoneNumber = null;
global.playerIdentifier = null;
global.playerStateId = null;
global.phoneEntity = 0;
global.activeCall = false;
global.phoneControlsDisabled = false;
global.playerDataSentToUI = false;

/**
 * Reset all phone-related global variables to their default values
 * Useful for resource restart or character switching
 */
export const resetPhoneGlobals = (): void => {
    // Phone state
    global.phoneState = PhoneStateEnum.CLOSED;

    // Player data
    global.playerPhoneNumber = null;
    global.playerIdentifier = null;
    global.playerStateId = null;

    // Phone entity
    global.phoneEntity = 0;

    // Call state
    global.activeCall = false;

    // Control state
    global.phoneControlsDisabled = false;

    // UI state
    global.playerDataSentToUI = false;
};
