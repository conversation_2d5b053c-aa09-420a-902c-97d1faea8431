/**
 * Music App - Server Side
 *
 * This file handles server-side functionality for the Music app.
 */

import config from '@shared/config';

// Default tracks for the music app
const DEFAULT_TRACKS = [
    {
        id: 1,
        title: 'Midnight City',
        artist: 'M83',
        album: "Hurry Up, We're Dreaming",
        duration: 242, // in seconds
        stream_url: 'https://example.com/music/midnight_city.mp3',
        cover_url: 'https://example.com/covers/midnight_city.jpg',
        is_favorite: false
    },
    {
        id: 2,
        title: 'Blinding Lights',
        artist: 'The Weeknd',
        album: 'After Hours',
        duration: 203,
        stream_url: 'https://example.com/music/blinding_lights.mp3',
        cover_url: 'https://example.com/covers/blinding_lights.jpg',
        is_favorite: false
    },
    {
        id: 3,
        title: 'Take On Me',
        artist: 'a-ha',
        album: 'Hunting High and Low',
        duration: 225,
        stream_url: 'https://example.com/music/take_on_me.mp3',
        cover_url: 'https://example.com/covers/take_on_me.jpg',
        is_favorite: false
    },
    {
        id: 4,
        title: "Don't Stop Believin'",
        artist: 'Journey',
        album: 'Escape',
        duration: 251,
        stream_url: 'https://example.com/music/dont_stop_believin.mp3',
        cover_url: 'https://example.com/covers/dont_stop_believin.jpg',
        is_favorite: false
    },
    {
        id: 5,
        title: 'Bohemian Rhapsody',
        artist: 'Queen',
        album: 'A Night at the Opera',
        duration: 354,
        stream_url: 'https://example.com/music/bohemian_rhapsody.mp3',
        cover_url: 'https://example.com/covers/bohemian_rhapsody.jpg',
        is_favorite: false
    }
];

// Default playlists for the music app
const DEFAULT_PLAYLISTS = [
    {
        id: 1,
        name: 'Favorites',
        description: 'Your favorite tracks',
        cover_url: 'https://example.com/covers/favorites.jpg',
        is_favorite: true,
        tracks: [1, 3, 5] // Track IDs
    },
    {
        id: 2,
        name: 'Driving',
        description: 'Perfect for cruising around the city',
        cover_url: 'https://example.com/covers/driving.jpg',
        is_favorite: false,
        tracks: [2, 4] // Track IDs
    }
];

/**
 * Initialize the music app
 */
export function initializeMusicApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
}

/**
 * Register server events for the music app
 */
function registerServerEvents(): void {
    // Register event for getting music library
    onNet('hm-phone:getMusicLibrary', async () => {
        const source = global.source;
        console.log(`[Music] Received getMusicLibrary event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Music] Player ${source} not found`);
                emitNet('hm-phone:musicError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Music] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:musicError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support, fallback to identifier
            const playerIdentifier = stateid || identifier;

            // Get music library for the player
            const library = await getMusicLibrary(playerIdentifier);

            // Send the library back to the client
            emitNet('hm-phone:musicLibrary', source, library);
        } catch (error) {
            console.error('[Music] Error getting music library:', error);
            emitNet('hm-phone:musicError', source, 'Failed to get music library');
        }
    });

    // Register event for getting music playlists
    onNet('hm-phone:getMusicPlaylists', async () => {
        const source = global.source;
        console.log(`[Music] Received getMusicPlaylists event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Music] Player ${source} not found`);
                emitNet('hm-phone:musicError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Music] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:musicError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get music playlists for the player
            const playlists = await getMusicPlaylists(playerIdentifier);

            // Send the playlists back to the client
            emitNet('hm-phone:musicPlaylists', source, playlists);
        } catch (error) {
            console.error('[Music] Error getting music playlists:', error);
            emitNet('hm-phone:musicError', source, 'Failed to get music playlists');
        }
    });

    // Register event for creating a music playlist
    onNet('hm-phone:createMusicPlaylist', async (name: string, description: string) => {
        const source = global.source;
        console.log(`[Music] Received createMusicPlaylist event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Music] Player ${source} not found`);
                emitNet('hm-phone:musicError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Music] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:musicError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Create the playlist
            await createMusicPlaylist(playerIdentifier, name, description);

            // Get updated playlists
            const playlists = await getMusicPlaylists(playerIdentifier);

            // Send the updated playlists back to the client
            emitNet('hm-phone:musicPlaylists', source, playlists);
        } catch (error) {
            console.error('[Music] Error creating music playlist:', error);
            emitNet('hm-phone:musicError', source, 'Failed to create music playlist');
        }
    });

    // Register event for adding a track to a playlist
    onNet('hm-phone:addTrackToPlaylist', async (playlistId: number, trackId: number) => {
        const source = global.source;
        console.log(`[Music] Received addTrackToPlaylist event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Music] Player ${source} not found`);
                emitNet('hm-phone:musicError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Music] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:musicError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Add the track to the playlist
            await addTrackToPlaylist(playerIdentifier, playlistId, trackId);

            // Get updated playlists
            const playlists = await getMusicPlaylists(playerIdentifier);

            // Send the updated playlists back to the client
            emitNet('hm-phone:musicPlaylists', source, playlists);
        } catch (error) {
            console.error('[Music] Error adding track to playlist:', error);
            emitNet('hm-phone:musicError', source, 'Failed to add track to playlist');
        }
    });

    // Register event for removing a track from a playlist
    onNet('hm-phone:removeTrackFromPlaylist', async (playlistId: number, trackId: number) => {
        const source = global.source;
        console.log(`[Music] Received removeTrackFromPlaylist event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Music] Player ${source} not found`);
                emitNet('hm-phone:musicError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Music] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:musicError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Remove the track from the playlist
            await removeTrackFromPlaylist(playerIdentifier, playlistId, trackId);

            // Get updated playlists
            const playlists = await getMusicPlaylists(playerIdentifier);

            // Send the updated playlists back to the client
            emitNet('hm-phone:musicPlaylists', source, playlists);
        } catch (error) {
            console.error('[Music] Error removing track from playlist:', error);
            emitNet('hm-phone:musicError', source, 'Failed to remove track from playlist');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[Music] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[Music] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_music_playlists table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_music_playlists (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    stateid VARCHAR(255) NULL,
                    name VARCHAR(255) NOT NULL,
                    description TEXT NULL,
                    cover_url VARCHAR(255) NULL,
                    is_favorite BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_identifier (identifier),
                    INDEX idx_stateid (stateid)
                )
            `,
                [],
                () => {
                    console.log('[Music] phone_music_playlists table initialized');
                }
            );

            // Create the phone_music_tracks table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_music_tracks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    playlist_id INT NULL,
                    title VARCHAR(255) NOT NULL,
                    artist VARCHAR(255) NOT NULL,
                    album VARCHAR(255) NULL,
                    duration INT NULL,
                    stream_url VARCHAR(255) NOT NULL,
                    cover_url VARCHAR(255) NULL,
                    is_favorite BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_playlist_id (playlist_id),
                    INDEX idx_title (title),
                    INDEX idx_artist (artist),
                    FOREIGN KEY (playlist_id) REFERENCES phone_music_playlists(id) ON DELETE SET NULL
                )
            `,
                [],
                () => {
                    console.log('[Music] phone_music_tracks table initialized');
                }
            );

            // Create the phone_music_playlist_tracks table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_music_playlist_tracks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    playlist_id INT NOT NULL,
                    track_id INT NOT NULL,
                    position INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_playlist_id (playlist_id),
                    INDEX idx_track_id (track_id),
                    FOREIGN KEY (playlist_id) REFERENCES phone_music_playlists(id) ON DELETE CASCADE,
                    FOREIGN KEY (track_id) REFERENCES phone_music_tracks(id) ON DELETE CASCADE
                )
            `,
                [],
                () => {
                    console.log('[Music] phone_music_playlist_tracks table initialized');
                }
            );
        } else {
            console.error('[Music] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[Music] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get music library for a player
 * @param identifier Player identifier
 * @returns Array of tracks
 */
async function getMusicLibrary(identifier: string): Promise<any[]> {
    console.log(`[Music] Getting music library for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let tracks = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            tracks = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_music_tracks
                ORDER BY title ASC
            `,
                []
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            tracks = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_music_tracks
                    ORDER BY title ASC
                `,
                    [],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // If no tracks found, return default tracks
        if (!tracks || tracks.length === 0) {
            return DEFAULT_TRACKS;
        }

        // Format tracks for the UI
        return tracks.map((track: any) => ({
            id: track.id,
            title: track.title,
            artist: track.artist,
            album: track.album,
            duration: track.duration,
            stream_url: track.stream_url,
            cover_url: track.cover_url,
            is_favorite: track.is_favorite
        }));
    } catch (error: any) {
        console.error('[Music] Error getting music library:', error);
        throw new Error(`Failed to get music library: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Get music playlists for a player
 * @param identifier Player identifier
 * @returns Array of playlists
 */
async function getMusicPlaylists(identifier: string): Promise<any[]> {
    console.log(`[Music] Getting music playlists for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let playlists = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            playlists = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_music_playlists
                WHERE identifier = ? OR stateid = ?
                ORDER BY name ASC
            `,
                [identifier, identifier]
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            playlists = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_music_playlists
                    WHERE identifier = ? OR stateid = ?
                    ORDER BY name ASC
                `,
                    [identifier, identifier],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // If no playlists found, return default playlists
        if (!playlists || playlists.length === 0) {
            return DEFAULT_PLAYLISTS;
        }

        // Get tracks for each playlist
        const playlistsWithTracks = await Promise.all(
            playlists.map(async (playlist: any) => {
                let playlistTracks = [];

                // Get tracks for this playlist
                if (typeof global.exports.oxmysql.query_async === 'function') {
                    // Use query_async if available
                    playlistTracks = await global.exports.oxmysql.query_async(
                        `
                        SELECT t.* FROM phone_music_tracks t
                        JOIN phone_music_playlist_tracks pt ON t.id = pt.track_id
                        WHERE pt.playlist_id = ?
                        ORDER BY pt.position ASC
                    `,
                        [playlist.id]
                    );
                } else if (typeof global.exports.oxmysql.query === 'function') {
                    // Fall back to query if query_async is not available
                    playlistTracks = await new Promise<any[]>(resolve => {
                        global.exports.oxmysql.query(
                            `
                            SELECT t.* FROM phone_music_tracks t
                            JOIN phone_music_playlist_tracks pt ON t.id = pt.track_id
                            WHERE pt.playlist_id = ?
                            ORDER BY pt.position ASC
                        `,
                            [playlist.id],
                            (result: any) => {
                                resolve(result || []);
                            }
                        );
                    });
                }

                // Format tracks for the UI
                const formattedTracks = playlistTracks.map((track: any) => ({
                    id: track.id,
                    title: track.title,
                    artist: track.artist,
                    album: track.album,
                    duration: track.duration,
                    stream_url: track.stream_url,
                    cover_url: track.cover_url,
                    is_favorite: track.is_favorite
                }));

                // Return playlist with tracks
                return {
                    id: playlist.id,
                    name: playlist.name,
                    description: playlist.description,
                    cover_url: playlist.cover_url,
                    is_favorite: playlist.is_favorite,
                    tracks: formattedTracks
                };
            })
        );

        return playlistsWithTracks;
    } catch (error: any) {
        console.error('[Music] Error getting music playlists:', error);
        throw new Error(`Failed to get music playlists: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create a music playlist
 * @param identifier Player identifier
 * @param name Playlist name
 * @param description Playlist description
 * @returns Playlist ID
 */
async function createMusicPlaylist(identifier: string, name: string, description: string): Promise<number> {
    console.log(`[Music] Creating music playlist for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let playlistId = 0;

        // Check if insert_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            playlistId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_music_playlists (identifier, stateid, name, description)
                VALUES (?, ?, ?, ?)
            `,
                [identifier, identifier, name, description]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            playlistId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_music_playlists (identifier, stateid, name, description)
                    VALUES (?, ?, ?, ?)
                `,
                    [identifier, identifier, name, description],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        return playlistId;
    } catch (error: any) {
        console.error('[Music] Error creating music playlist:', error);
        throw new Error(`Failed to create music playlist: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Add a track to a playlist
 * @param identifier Player identifier
 * @param playlistId Playlist ID
 * @param trackId Track ID
 */
async function addTrackToPlaylist(identifier: string, playlistId: number, trackId: number): Promise<void> {
    console.log(`[Music] Adding track ${trackId} to playlist ${playlistId} for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Get the highest position in the playlist
        let position = 0;

        if (typeof global.exports.oxmysql.scalar_async === 'function') {
            // Use scalar_async if available
            position = await global.exports.oxmysql.scalar_async(
                `
                SELECT COALESCE(MAX(position), 0) FROM phone_music_playlist_tracks
                WHERE playlist_id = ?
            `,
                [playlistId]
            );
        } else if (typeof global.exports.oxmysql.scalar === 'function') {
            // Fall back to scalar if scalar_async is not available
            position = await new Promise<number>(resolve => {
                global.exports.oxmysql.scalar(
                    `
                    SELECT COALESCE(MAX(position), 0) FROM phone_music_playlist_tracks
                    WHERE playlist_id = ?
                `,
                    [playlistId],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        }

        // Increment position for the new track
        position++;

        // Add the track to the playlist
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_music_playlist_tracks (playlist_id, track_id, position)
                VALUES (?, ?, ?)
            `,
                [playlistId, trackId, position]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_music_playlist_tracks (playlist_id, track_id, position)
                    VALUES (?, ?, ?)
                `,
                    [playlistId, trackId, position],
                    () => {
                        resolve();
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }
    } catch (error: any) {
        console.error('[Music] Error adding track to playlist:', error);
        throw new Error(`Failed to add track to playlist: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Remove a track from a playlist
 * @param identifier Player identifier
 * @param playlistId Playlist ID
 * @param trackId Track ID
 */
async function removeTrackFromPlaylist(identifier: string, playlistId: number, trackId: number): Promise<void> {
    console.log(`[Music] Removing track ${trackId} from playlist ${playlistId} for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Remove the track from the playlist
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                DELETE FROM phone_music_playlist_tracks
                WHERE playlist_id = ? AND track_id = ?
            `,
                [playlistId, trackId]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    DELETE FROM phone_music_playlist_tracks
                    WHERE playlist_id = ? AND track_id = ?
                `,
                    [playlistId, trackId],
                    () => {
                        resolve();
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database execute method found');
        }

        // Reorder the remaining tracks
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                SET @pos = 0;
                UPDATE phone_music_playlist_tracks
                SET position = (@pos := @pos + 1)
                WHERE playlist_id = ?
                ORDER BY position ASC
            `,
                [playlistId]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    SET @pos = 0;
                    UPDATE phone_music_playlist_tracks
                    SET position = (@pos := @pos + 1)
                    WHERE playlist_id = ?
                    ORDER BY position ASC
                `,
                    [playlistId],
                    () => {
                        resolve();
                    }
                );
            });
        }
    } catch (error: any) {
        console.error('[Music] Error removing track from playlist:', error);
        throw new Error(`Failed to remove track from playlist: ${error?.message || 'Unknown error'}`);
    }
}
