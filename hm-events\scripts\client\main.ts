/// <reference types="@citizenfx/client" />

import { Config } from '@shared';
import { setupEventHandlers, resetThrottlingCounters } from './event-handlers';

// Define our own logger for consistent logging
const RESOURCE_NAME = GetCurrentResourceName();
const logger = {
  info: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.log(`^2[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.log(data);
  },
  debug: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.debug(`^5[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.debug(data);
  },
  warn: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.warn(`^3[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.warn(data);
  },
  error: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.error(`^1[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.error(data);
  },
};

// Initialize the resource
function initialize() {

  // Set up event handlers
  setupEventHandlers();

  // Set up throttling reset interval
  if (Config.throttling.enabled) {
    setInterval(resetThrottlingCounters, Config.throttling.resetInterval);
    logger.debug(`Throttling enabled with reset interval of ${Config.throttling.resetInterval}ms`, 'config');
  }
}

// Initialize when resource starts
on('onClientResourceStart', (resourceName: string) => {
  if (resourceName === RESOURCE_NAME) {
    initialize();
  }
});