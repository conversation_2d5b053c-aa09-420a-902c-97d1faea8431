@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  /* Ensure root elements fill viewport */
  html, body, #root {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
  }

  /* Disable tab navigation for all elements */
  * {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Remove tab focus from all interactive elements */
  button, input, select, textarea, [tabindex] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

@layer components {
  /* Slot label gradient animations - seamless blending with slot background */
  .slot-label-gradient {
    background: linear-gradient(
      to top,
      rgba(23, 23, 23, 0.95) 0%,
      rgba(23, 23, 23, 0.8) 30%,
      rgba(23, 23, 23, 0.4) 70%,
      transparent 100%
    );
    transition: all 0.3s ease-in-out;
  }

  .slot-label-gradient:hover {
    background: linear-gradient(
      to top,
      rgba(16, 185, 129, 0.25) 0%,
      rgba(23, 23, 23, 0.9) 20%,
      rgba(23, 23, 23, 0.5) 60%,
      transparent 100%
    );
  }

  /* Label container with smooth entrance - compact */
  .slot-label-container {
    height: 18px; /* Slightly increased for padding */
    transform: translateY(1px);
    opacity: 0.9;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slot-label-container:hover {
    transform: translateY(0);
    opacity: 1;
  }

  /* Accent gradient under the label */
  .slot-label-accent {
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(16, 185, 129, 0.3) 20%,
      rgba(16, 185, 129, 0.6) 50%,
      rgba(16, 185, 129, 0.3) 80%,
      transparent 100%
    );
    transition: all 0.3s ease-in-out;
  }

  .slot-label-accent:hover {
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(16, 185, 129, 0.5) 10%,
      rgba(16, 185, 129, 0.8) 50%,
      rgba(16, 185, 129, 0.5) 90%,
      transparent 100%
    );
  }

  /* Label text glow effect on hover - compact */
  .slot-label-text {
    transition: all 0.3s ease-in-out;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
  }

  .slot-label-text:hover {
    color: rgb(255, 255, 255);
    text-shadow: 
      0 1px 1px rgba(0, 0, 0, 0.8),
      0 0 6px rgba(16, 185, 129, 0.4);
  }

  /* Toast and ProgressBar animations */
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes shine {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(100%);
    }
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out forwards;
  }

  .animate-shine {
    animation: shine 2s infinite;
  }

  /* Enhanced drag animations */
  @keyframes trail {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 0.5; }
    100% { transform: translateX(100%); opacity: 0; }
  }

  @keyframes dragBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
  }

  @keyframes rarityPulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
  }

  .animate-trail {
    animation: trail 1.5s infinite;
  }

  .animate-drag-bounce {
    animation: dragBounce 2s infinite;
  }

  .animate-rarity-pulse {
    animation: rarityPulse 2s infinite;
  }
}