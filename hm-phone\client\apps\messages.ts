/**
 * Messages App - Client Side
 *
 * This file handles client-side functionality for the Messages app.
 * Follows the pattern of initializing handlers on start, loading data on demand.
 */

import { registerAppHandler } from '../nui';
import { BaseMessage, Conversation } from '@shared/types';

/**
 * Initialize the messages app
 * This only registers handlers, it doesn't load any data
 */
export function initializeMessagesApp(): void {
    console.log('[Messages] Initializing client-side messages app');
    // Register NUI handlers
    registerNUIHandlers();
    // Register client events
    registerClientEvents();
}

/**
 * Register NUI handlers for the messages app
 */
function registerNUIHandlers(): void {
    // Register handler for getting conversations
    registerAppHandler('messages', 'getConversations', async (data: any) => {
        console.log('[Messages] Received getConversations request from UI:', data);
        try {
            // Get player data
            const phoneNumber = global.playerPhoneNumber;
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(
                `[Messages] Getting conversations for player: ${stateid || identifier}, phone: ${phoneNumber}`
            );

            // Get pagination parameters
            const limit = data?.limit || 20;
            const offset = data?.offset || 0;

            // Get conversations from the server with pagination
            emitNet('hm-phone:getConversations', {
                phoneNumber,
                limit,
                offset
            });
            // The actual conversations will be sent via the hm-phone:conversations event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error getting conversations:', error);
            return { success: false, error };
        }
    });
    // Register handler for sending a message
    registerAppHandler('messages', 'sendMessage', async (data: any) => {
        console.log('[Messages] Received sendMessage request from UI:', data);
        try {
            // Send the message to the server
            emitNet('hm-phone:sendMessage', data);
            // The actual message confirmation will be sent via the hm-phone:messageSent event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error sending message:', error);
            return { success: false, error };
        }
    });
    // Register handler for creating a conversation
    registerAppHandler('messages', 'createConversation', async (data: any) => {
        console.log('[Messages] Received createConversation request from UI :', data);
        try {
            // Create the conversation on the server
            emitNet('hm-phone:createConversation', data);
            // The actual conversation creation confirmation will be sent via the hm-phone:conversationCreated event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error creating conversation:', error);
            return { success: false, error };
        }
    });
    // Register handler for deleting a conversation
    registerAppHandler('messages', 'deleteConversation', async (data: any) => {
        console.log('[Messages] Received deleteConversation request from UI:', data);
        try {
            // Delete the conversation on the server
            emitNet('hm-phone:deleteConversation', data);
            // The actual conversation deletion confirmation will be sent via the hm-phone:conversationDeleted event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error deleting conversation:', error);
            return { success: false, error };
        }
    });
    // Register handler for updating a conversation
    registerAppHandler('messages', 'updateConversation', async (data: any) => {
        console.log('[Messages] Received updateConversation request from UI:', data);
        try {
            // Update the conversation on the server
            emitNet('hm-phone:updateConversation', data);
            // The actual conversation update confirmation will be sent via the hm-phone:conversationUpdated event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error updating conversation:', error);
            return { success: false, error };
        }
    });
    // Register handler for getting a conversation
    registerAppHandler('messages', 'getConversation', async (data: any) => {
        console.log('[Messages] Received getConversation request from UI:', data);
        try {
            // Get the conversation from the server
            emitNet('hm-phone:getConversation', data);
            // The actual conversation will be sent via the hm-phone:conversation event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error getting conversation:', error);
            return { success: false, error };
        }
    });
    // Register handler for getting messages for a conversation
    registerAppHandler('messages', 'getMessages', async (data: any) => {
        console.log('[Messages] Received getMessages request from UI:', data);
        try {
            // Get pagination parameters
            const conversationId = data.conversationId;
            const limit = data.limit || 20;
            const offset = data.offset || 0;

            // Get the messages for the conversation from the server with pagination
            emitNet('hm-phone:getMessages', {
                conversationId,
                limit,
                offset
            });
            // The actual messages will be sent via the hm-phone:messages event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error getting messages:', error);
            return { success: false, error };
        }
    });
    // Register handler for sending a message with attachments
    registerAppHandler('messages', 'sendMessageWithAttachments', async (data: any) => {
        console.log('[Messages] Received sendMessageWithAttachments request from UI:', data);
        try {
            // Send the message with attachments to the server
            emitNet('hm-phone:sendMessageWithAttachments', data);
            // The actual message confirmation will be sent via the hm-phone:messageSent event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error sending message with attachments:', error);
            return { success: false, error };
        }
    });
    // Register handler for deleting a message
    registerAppHandler('messages', 'deleteMessage', async (data: any) => {
        console.log('[Messages] Received deleteMessage request from UI:', data);
        try {
            // Delete the message on the server
            emitNet('hm-phone:deleteMessage', data);
            // The actual message deletion confirmation will be sent via the hm-phone:messageDeleted event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error deleting message:', error);
            return { success: false, error };
        }
    });
    // Register handler for updating group info
    registerAppHandler('messages', 'updateGroupInfo', async (data: any) => {
        console.log('[Messages] Received updateGroupInfo request from UI:', data);
        try {
            // Update the group info on the server
            emitNet('hm-phone:updateGroupInfo', data);
            // The actual group info update confirmation will be sent via the hm-phone:groupInfoUpdated event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error updating group info:', error);
            return { success: false, error };
        }
    });
    // Register handler for getting group info
    registerAppHandler('messages', 'getGroupInfo', async (data: any) => {
        console.log('[Messages] Received getGroupInfo request from UI:', data);
        try {
            // Get the group info from the server
            emitNet('hm-phone:getGroupInfo', data);
            // The actual group info will be sent via the hm-phone:groupInfo event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error getting group info:', error);
            return { success: false, error };
        }
    });
    // Register handler for getting group members
    registerAppHandler('messages', 'getGroupMembers', async (data: any) => {
        console.log('[Messages] Received getGroupMembers request from UI:', data);
        try {
            // Get the group members from the server
            emitNet('hm-phone:getGroupMembers', data);
            // The actual group members will be sent via the hm-phone:groupMembers event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error getting group members:', error);
            return { success: false, error };
        }
    });
    // Register handler for adding a group member
    registerAppHandler('messages', 'addGroupMember', async (data: any) => {
        console.log('[Messages] Received addGroupMember request from UI:', data);
        try {
            // Add a group
            emitNet('hm-phone:addGroupMember', data);
            // The actual group member addition confirmation will be sent via the hm-phone:groupMemberAdded event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error adding group member:', error);
            return { success: false, error };
        }
    });
    // Register handler for removing a group member
    registerAppHandler('messages', 'removeGroupMember', async (data: any) => {
        console.log('[Messages] Received removeGroupMember request from UI:', data);
        try {
            // Remove a group member
            emitNet('hm-phone:removeGroupMember', data);
            // The actual group member removal
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error removing group member:', error);
            return { success: false, error };
        }
    });

    // Register handler for marking a conversation as read
    registerAppHandler('messages', 'markAsRead', async (data: any) => {
        console.log('[Messages] Received markAsRead request from UI:', data);
        try {
            // Mark the conversation as read on the server
            emitNet('hm-phone:markConversationAsRead', data.conversationId);
            // The actual confirmation will be sent via the hm-phone:conversationMarkedAsRead event
            return { success: true };
        } catch (error) {
            console.error('[Messages] Error marking conversation as read:', error);
            return { success: false, error };
        }
    });
}

/**
 * Register client events for the messages app
 */
function registerClientEvents(): void {
    // Register event for receiving conversations
    onNet('hm-phone:conversations', (conversations: any) => {
        console.log('[Messages] Received conversations from server:', conversations);
        console.log('[Messages] Data type:', typeof conversations);
        console.log('[Messages] Is array:', Array.isArray(conversations));

        // Add detailed logging of the data structure
        if (conversations) {
            console.log('[Messages] Data structure details:');
            console.log('[Messages] Keys:', Object.keys(conversations));

            try {
                const jsonStr = JSON.stringify(conversations, null, 2);
                console.log('[Messages] JSON representation:', jsonStr);

                // Log to a file for easier debugging
                console.log('[Messages] FULL DATA DUMP:');
                console.log(jsonStr);
            } catch (e) {
                console.log('[Messages] Could not stringify conversations:', e);
            }

            if (Array.isArray(conversations)) {
                console.log('[Messages] Array length:', conversations.length);
                if (conversations.length > 0) {
                    console.log('[Messages] First item keys:', Object.keys(conversations[0]));
                    console.log('[Messages] First item values:');
                    for (const key of Object.keys(conversations[0])) {
                        console.log(`[Messages] - ${key}:`, conversations[0][key]);
                    }
                }
            } else if (typeof conversations === 'object') {
                console.log('[Messages] Object properties:');
                for (const key in conversations) {
                    const value = (conversations as Record<string, unknown>)[key];
                    console.log(`[Messages] Property ${key}:`, typeof value, value);
                }
            }
        }

        // Always send an array to the UI
        if (Array.isArray(conversations)) {
            // Already an array, send as is
            console.log('[Messages] Sending array of conversations to UI');
            sendToUI('conversations', conversations);
        } else if (conversations && typeof conversations === 'object') {
            // Single conversation object, wrap in array
            console.log('[Messages] Converting single conversation object to array');
            sendToUI('conversations', [conversations]);
        } else {
            // Invalid data, send empty array
            console.error('[Messages] Invalid conversations data received, sending empty array');
            sendToUI('conversations', []);
        }
    });

    // Register event for receiving a conversation
    onNet('hm-phone:conversation', (conversation: Conversation) => {
        console.log('[Messages] Received conversation from server:', conversation);
        // Send the conversation to the UI
        sendToUI('conversation', conversation);
    });
    // Register event for sending a message
    onNet('hm-phone:messageSent', (message: BaseMessage) => {
        console.log('[Messages] Received message sent confirmation from server:', message);
        // Send the message confirmation to the UI
        sendToUI('messageSent', message);
    });
    // Register event for conversation created
    onNet('hm-phone:conversationCreated', (data: any) => {
        console.log('[Messages] Received conversation created confirmation from server:', data);
        // Send the conversation created confirmation to the UI
        sendToUI('conversationCreated', data);
    });

    // Register event for conversation marked as read
    onNet('hm-phone:conversationMarkedAsRead', (conversationId: string) => {
        console.log('[Messages] Received conversation marked as read confirmation from server:', conversationId);
        // Send the conversation marked as read confirmation to the UI
        sendToUI('conversationMarkedAsRead', { conversationId });
    });

    // Register event for receiving messages
    onNet('hm-phone:messages', (messages: any) => {
        console.log('[Messages] Received messages from server:', messages);

        // Get the conversation ID from the first message if available
        let conversationId = null;
        if (Array.isArray(messages) && messages.length > 0 && messages[0].conversation_id) {
            conversationId = messages[0].conversation_id;
        }

        // Send the messages to the UI
        sendToUI('messages', { conversationId, messages });
    });
}

// Function to send data to the UI
function sendToUI(eventName: string, data: any): void {
    SendNUIMessage({
        app: 'messages',
        type: eventName,
        data
    });
}
