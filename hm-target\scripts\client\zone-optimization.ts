/**
 * HM Target - Zone Optimization System
 *
 * Provides spatial optimization for target zones to improve performance
 * when dealing with large numbers of zones.
 */

import { Vector3, TargetZone } from "../shared/types";
import { targetConfig } from "../shared/config";

// Spatial grid cell structure
interface GridCell {
  x: number;
  y: number;
  zones: Set<string>; // Zone IDs in this cell
}

// Zone cache entry
interface ZoneCacheEntry {
  zone: TargetZone;
  lastPlayerDistance: number;
  lastCheckTime: number;
  isPlayerInside: boolean;
  boundingBox: {
    min: Vector3;
    max: Vector3;
  };
}

/**
 * Zone optimization manager
 */
export class ZoneOptimizer {
  private static instance: ZoneOptimizer | null = null;

  // Spatial grid for zone lookup optimization
  private spatialGrid: Map<string, GridCell> = new Map();
  private gridSize: number;

  // Zone cache for performance
  private zoneCache: Map<string, ZoneCacheEntry> = new Map();

  // Player tracking
  private lastPlayerPosition: Vector3 = { x: 0, y: 0, z: 0 };
  private playerMovementThreshold: number;

  // Performance tracking
  private stats = {
    totalZones: 0,
    checkedZones: 0,
    culledZones: 0,
    gridCellsChecked: 0,
    frameTime: 0,
  };

  private constructor() {
    this.gridSize = targetConfig.zoneOptimization.spatialGridSize;
    this.playerMovementThreshold =
      targetConfig.zoneOptimization.playerMovementThreshold;
  }

  public static getInstance(): ZoneOptimizer {
    if (!ZoneOptimizer.instance) {
      ZoneOptimizer.instance = new ZoneOptimizer();
    }
    return ZoneOptimizer.instance;
  }

  /**
   * Add a zone to the optimization system
   */
  public addZone(zone: TargetZone): void {
    if (!targetConfig.zoneOptimization.enabled) return;

    // Calculate bounding box for the zone
    const boundingBox = this.calculateBoundingBox(zone);

    // Create cache entry
    const cacheEntry: ZoneCacheEntry = {
      zone,
      lastPlayerDistance: Infinity,
      lastCheckTime: 0,
      isPlayerInside: false,
      boundingBox,
    };

    this.zoneCache.set(zone.id, cacheEntry);

    // Add to spatial grid
    this.addZoneToGrid(zone, boundingBox);

    this.stats.totalZones++;
  }

  /**
   * Remove a zone from the optimization system
   */
  public removeZone(zoneId: string): void {
    if (!targetConfig.zoneOptimization.enabled) return;

    const cacheEntry = this.zoneCache.get(zoneId);
    if (cacheEntry) {
      // Remove from spatial grid
      this.removeZoneFromGrid(cacheEntry.zone, cacheEntry.boundingBox);

      // Remove from cache
      this.zoneCache.delete(zoneId);
      this.stats.totalZones--;
    }
  }

  /**
   * Get optimized list of zones to check for a given player position
   */
  public getZonesToCheck(playerPosition: Vector3): TargetZone[] {
    const startTime = GetGameTimer();
    this.stats.checkedZones = 0;
    this.stats.culledZones = 0;
    this.stats.gridCellsChecked = 0;

    if (!targetConfig.zoneOptimization.enabled) {
      // Return all zones if optimization is disabled
      const allZones = Array.from(this.zoneCache.values()).map(
        (entry) => entry.zone
      );
      this.stats.checkedZones = allZones.length;
      return allZones;
    }

    // Check if player has moved significantly
    const playerMoved = this.hasPlayerMovedSignificantly(playerPosition);

    if (!playerMoved) {
      // Return cached results for zones that might still be relevant
      return this.getCachedNearbyZones(playerPosition);
    }

    this.lastPlayerPosition = { ...playerPosition };

    const zonesToCheck: TargetZone[] = [];
    const maxChecksPerFrame =
      targetConfig.zoneOptimization.maxZoneChecksPerFrame;

    // Get nearby grid cells
    const nearbyCells = this.getNearbyGridCells(playerPosition);
    this.stats.gridCellsChecked = nearbyCells.length;

    // Collect zones from nearby cells with distance culling
    const candidateZones = new Set<string>();
    for (const cell of nearbyCells) {
      for (const zoneId of cell.zones) {
        candidateZones.add(zoneId);
      }
    }

    // Sort zones by distance and apply frame limits
    const sortedZones = Array.from(candidateZones)
      .map((zoneId) => this.zoneCache.get(zoneId))
      .filter((entry) => entry && this.isZoneInRange(entry, playerPosition))
      .sort((a, b) => {
        const distA = this.getDistance3D(playerPosition, a!.zone.position);
        const distB = this.getDistance3D(playerPosition, b!.zone.position);
        return distA - distB;
      })
      .slice(0, maxChecksPerFrame);

    // Update cache and collect zones
    for (const entry of sortedZones) {
      if (!entry) continue;
      const distance = this.getDistance3D(playerPosition, entry.zone.position);
      entry.lastPlayerDistance = distance;
      entry.lastCheckTime = GetGameTimer();
      zonesToCheck.push(entry.zone);
      this.stats.checkedZones++;
    }

    this.stats.culledZones = candidateZones.size - this.stats.checkedZones;
    this.stats.frameTime = GetGameTimer() - startTime;

    return zonesToCheck;
  }

  /**
   * Get current optimization statistics
   */
  public getStats() {
    return { ...this.stats };
  }

  /**
   * Calculate bounding box for a zone
   */
  private calculateBoundingBox(zone: TargetZone): {
    min: Vector3;
    max: Vector3;
  } {
    switch (zone.type) {
      case "sphere": {
        const radius = zone.radius || 1.0;
        return {
          min: {
            x: zone.position.x - radius,
            y: zone.position.y - radius,
            z: zone.position.z - radius,
          },
          max: {
            x: zone.position.x + radius,
            y: zone.position.y + radius,
            z: zone.position.z + radius,
          },
        };
      }
      case "box": {
        if (!zone.size) {
          return { min: zone.position, max: zone.position };
        }
        const halfSize = {
          x: zone.size.x / 2,
          y: zone.size.y / 2,
          z: zone.size.z / 2,
        };
        return {
          min: {
            x: zone.position.x - halfSize.x,
            y: zone.position.y - halfSize.y,
            z: zone.position.z - halfSize.z,
          },
          max: {
            x: zone.position.x + halfSize.x,
            y: zone.position.y + halfSize.y,
            z: zone.position.z + halfSize.z,
          },
        };
      }
      case "polygon": {
        if (!zone.points || zone.points.length === 0) {
          return { min: zone.position, max: zone.position };
        }
        let minX = zone.points[0].x,
          maxX = zone.points[0].x;
        let minY = zone.points[0].y,
          maxY = zone.points[0].y;
        const minZ = zone.position.z,
          maxZ = zone.position.z + (zone.height || 2.0);

        for (const point of zone.points) {
          minX = Math.min(minX, point.x);
          maxX = Math.max(maxX, point.x);
          minY = Math.min(minY, point.y);
          maxY = Math.max(maxY, point.y);
        }

        return {
          min: { x: minX, y: minY, z: minZ },
          max: { x: maxX, y: maxY, z: maxZ },
        };
      }
      default:
        return { min: zone.position, max: zone.position };
    }
  }

  /**
   * Add zone to spatial grid
   */
  private addZoneToGrid(
    zone: TargetZone,
    boundingBox: { min: Vector3; max: Vector3 }
  ): void {
    const minGridX = Math.floor(boundingBox.min.x / this.gridSize);
    const maxGridX = Math.floor(boundingBox.max.x / this.gridSize);
    const minGridY = Math.floor(boundingBox.min.y / this.gridSize);
    const maxGridY = Math.floor(boundingBox.max.y / this.gridSize);

    for (let gx = minGridX; gx <= maxGridX; gx++) {
      for (let gy = minGridY; gy <= maxGridY; gy++) {
        const cellKey = `${gx},${gy}`;
        let cell = this.spatialGrid.get(cellKey);
        if (!cell) {
          cell = { x: gx, y: gy, zones: new Set() };
          this.spatialGrid.set(cellKey, cell);
        }
        cell.zones.add(zone.id);
      }
    }
  }

  /**
   * Remove zone from spatial grid
   */
  private removeZoneFromGrid(
    zone: TargetZone,
    boundingBox: { min: Vector3; max: Vector3 }
  ): void {
    const minGridX = Math.floor(boundingBox.min.x / this.gridSize);
    const maxGridX = Math.floor(boundingBox.max.x / this.gridSize);
    const minGridY = Math.floor(boundingBox.min.y / this.gridSize);
    const maxGridY = Math.floor(boundingBox.max.y / this.gridSize);

    for (let gx = minGridX; gx <= maxGridX; gx++) {
      for (let gy = minGridY; gy <= maxGridY; gy++) {
        const cellKey = `${gx},${gy}`;
        const cell = this.spatialGrid.get(cellKey);
        if (cell) {
          cell.zones.delete(zone.id);
          // Clean up empty cells
          if (cell.zones.size === 0) {
            this.spatialGrid.delete(cellKey);
          }
        }
      }
    }
  }

  /**
   * Get nearby grid cells for a position
   */
  private getNearbyGridCells(position: Vector3): GridCell[] {
    const centerGridX = Math.floor(position.x / this.gridSize);
    const centerGridY = Math.floor(position.y / this.gridSize);
    const cells: GridCell[] = [];

    // Check 3x3 grid around player position
    for (let dx = -1; dx <= 1; dx++) {
      for (let dy = -1; dy <= 1; dy++) {
        const gx = centerGridX + dx;
        const gy = centerGridY + dy;
        const cellKey = `${gx},${gy}`;
        const cell = this.spatialGrid.get(cellKey);
        if (cell) {
          cells.push(cell);
        }
      }
    }

    return cells;
  }

  /**
   * Check if player has moved significantly
   */
  private hasPlayerMovedSignificantly(currentPosition: Vector3): boolean {
    const distance = this.getDistance3D(
      this.lastPlayerPosition,
      currentPosition
    );
    return distance >= this.playerMovementThreshold;
  }

  /**
   * Get cached nearby zones without full recalculation
   */
  private getCachedNearbyZones(_playerPosition: Vector3): TargetZone[] {
    const zones: TargetZone[] = [];
    const maxDistance = targetConfig.zoneOptimization.distanceCulling;

    for (const entry of this.zoneCache.values()) {
      if (entry.lastPlayerDistance <= maxDistance) {
        zones.push(entry.zone);
      }
    }

    this.stats.checkedZones = zones.length;
    return zones;
  }

  /**
   * Check if zone is within interaction range
   */
  private isZoneInRange(
    entry: ZoneCacheEntry,
    playerPosition: Vector3
  ): boolean {
    const distance = this.getDistance3D(playerPosition, entry.zone.position);
    const maxDistance = targetConfig.zoneOptimization.distanceCulling;
    return distance <= maxDistance;
  }

  /**
   * Calculate 3D distance between two points
   */
  private getDistance3D(pos1: Vector3, pos2: Vector3): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * Clear all optimization data
   */
  public clear(): void {
    this.spatialGrid.clear();
    this.zoneCache.clear();
    this.stats = {
      totalZones: 0,
      checkedZones: 0,
      culledZones: 0,
      gridCellsChecked: 0,
      frameTime: 0,
    };
  }
}

// Export singleton instance
export const zoneOptimizer = ZoneOptimizer.getInstance();
