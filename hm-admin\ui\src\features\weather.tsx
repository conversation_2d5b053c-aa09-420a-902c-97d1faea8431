import React, { useState } from 'react';
import { sendNuiMessage, NuiActions } from '../utils/nui';

// Available weather types (matching the ones in WeatherManager)
const WEATHER_TYPES = [
  { id: 'CLEAR', name: 'Clear', description: 'Clear skies' },
  { id: 'EXTRASUNNY', name: 'Extra Sunny', description: 'Clear sunny weather' },
  { id: 'CLOUDS', name: 'Clouds', description: 'Scattered clouds' },
  { id: 'OVERCAST', name: 'Overcast', description: 'Cloudy skies' },
  { id: 'RAIN', name: 'Rain', description: 'Light rain' },
  { id: 'CLEARING', name: 'Clearing', description: 'Weather clearing up' },
  { id: 'THUNDER', name: 'Thunder', description: 'Thunderstorm' },
  { id: 'SMOG', name: 'Smog', description: 'Smoggy atmosphere' },
  { id: 'FOGGY', name: 'Foggy', description: 'Thick fog' },
  { id: 'XMAS', name: 'Christmas', description: 'Christmas weather' },
  { id: 'SNOWLIGHT', name: 'Light Snow', description: 'Very light snow' },
  { id: 'BLIZZARD', name: 'Blizzard', description: 'Heavy snow storm' }
];

// Weather change feature
export const ChangeWeatherFeature: React.FC = () => {
  const [selectedWeather, setSelectedWeather] = useState('EXTRASUNNY');
  const [transitionTime, setTransitionTime] = useState(5000);
  const [instantChange, setInstantChange] = useState(false);

  const changeWeather = () => {
    console.log(`Changing weather to: ${selectedWeather}, instant: ${instantChange}, transition: ${transitionTime}ms`);
    if (instantChange) {
      sendNuiMessage(NuiActions.SET_WEATHER_INSTANT, { weather: selectedWeather });
    } else {
      sendNuiMessage(NuiActions.CHANGE_WEATHER, { weather: selectedWeather, transitionTime });
    }
  };

  return (
    <div className="space-y-2">
      <select value={selectedWeather} onChange={(e) => setSelectedWeather(e.target.value)} className="w-full px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
        {WEATHER_TYPES.map(weather => <option key={weather.id} value={weather.id}>{weather.name} - {weather.description}</option>)}
      </select>
      <div className="flex items-center gap-2">
        <label className="flex items-center text-sm">
          <input type="checkbox" checked={instantChange} onChange={(e) => setInstantChange(e.target.checked)} className="mr-1" />
          Instant
        </label>
        {!instantChange && (
          <div className="flex items-center gap-1 flex-1">
            <input type="range" min="1" max="30" value={transitionTime / 1000} onChange={(e) => setTransitionTime(parseInt(e.target.value) * 1000)} className="flex-1" />
            <span className="text-xs text-neutral-400 min-w-[24px]">{transitionTime / 1000}s</span>
          </div>
        )}
      </div>
      <button onClick={changeWeather} className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1">
        <i className="fas fa-cloud-sun"></i>Change Weather
      </button>
    </div>
  );
};

// Time control feature
export const TimeControlFeature: React.FC = () => {
  const [hour, setHour] = useState(12);
  const [minute, setMinute] = useState(0);
  const [isFrozen, setIsFrozen] = useState(false);
  const [timeScale, setTimeScale] = useState(1.0);

  const setTime = () => {
    console.log(`Setting time to: ${hour}:${minute.toString().padStart(2, '0')}`);
    sendNuiMessage(NuiActions.SET_TIME, { hour, minute });
  };

  const toggleFreezeTime = () => {
    const newFrozen = !isFrozen;
    setIsFrozen(newFrozen);
    console.log(`${newFrozen ? 'Freezing' : 'Unfreezing'} time`);
    sendNuiMessage(NuiActions.FREEZE_TIME, { freeze: newFrozen });
  };

  const applyTimeScale = () => {
    console.log(`Setting time scale to: ${timeScale}`);
    sendNuiMessage(NuiActions.SET_TIME_SCALE, { scale: timeScale });
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <input type="number" min="0" max="23" value={hour} onChange={(e) => setHour(parseInt(e.target.value) || 0)} placeholder="Hour" className="flex-1 px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" />
        <input type="number" min="0" max="59" value={minute} onChange={(e) => setMinute(parseInt(e.target.value) || 0)} placeholder="Min" className="flex-1 px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" />
        <button onClick={setTime} className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
          <i className="fas fa-clock"></i>
        </button>
      </div>
      <div className="flex gap-2">
        <button onClick={toggleFreezeTime} className={`flex-1 px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1 ${isFrozen ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-yellow-600 hover:bg-yellow-700 text-white'}`}>
          <i className={`fas ${isFrozen ? 'fa-play' : 'fa-pause'}`}></i>{isFrozen ? 'Unfreeze' : 'Freeze'}
        </button>
        <div className="flex items-center gap-1 flex-1">
          <input type="range" min="0.1" max="5.0" step="0.1" value={timeScale} onChange={(e) => setTimeScale(parseFloat(e.target.value))} className="flex-1" />
          <span className="text-xs text-neutral-400 min-w-[30px]">{timeScale}x</span>
        </div>
        <button onClick={applyTimeScale} className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
          <i className="fas fa-tachometer-alt"></i>
        </button>
      </div>
    </div>
  );
};

// Wind control feature
export const WindControlFeature: React.FC = () => {
  const [windSpeed, setWindSpeed] = useState(10.0);
  const [windDirection, setWindDirection] = useState(0.0);

  const applyWind = () => {
    console.log(`Setting wind - Speed: ${windSpeed}, Direction: ${windDirection}°`);
    sendNuiMessage(NuiActions.SET_WIND_SPEED, { speed: windSpeed });
    sendNuiMessage(NuiActions.SET_WIND_DIRECTION, { direction: windDirection });
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-xs text-neutral-400 min-w-[60px]">Speed:</span>
        <input type="range" min="0" max="50" step="0.5" value={windSpeed} onChange={(e) => setWindSpeed(parseFloat(e.target.value))} className="flex-1" />
        <span className="text-xs text-neutral-400 min-w-[20px]">{windSpeed}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-xs text-neutral-400 min-w-[60px]">Direction:</span>
        <input type="range" min="0" max="360" step="5" value={windDirection} onChange={(e) => setWindDirection(parseFloat(e.target.value))} className="flex-1" />
        <span className="text-xs text-neutral-400 min-w-[20px]">{windDirection}°</span>
      </div>
      <button onClick={applyWind} className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1">
        <i className="fas fa-wind"></i>Apply Wind
      </button>
    </div>
  );
};

// Reset weather feature
export const ResetWeatherFeature: React.FC = () => {
  const resetWeather = () => {
    console.log('Resetting weather to default');
    sendNuiMessage(NuiActions.RESET_WEATHER, {});
  };

  const resetTime = () => {
    console.log('Resetting time to default');
    sendNuiMessage(NuiActions.RESET_TIME, {});
  };

  const resetAll = () => {
    console.log('Resetting all weather and time settings');
    resetWeather();
    resetTime();
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <button onClick={resetWeather} className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1">
          <i className="fas fa-undo"></i>Weather
        </button>
        <button onClick={resetTime} className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1">
          <i className="fas fa-clock"></i>Time
        </button>
      </div>
      <button onClick={resetAll} className="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors flex items-center justify-center gap-1">
        <i className="fas fa-refresh"></i>Reset All
      </button>
      <div className="bg-yellow-900/30 border border-yellow-700 rounded p-2">
        <div className="flex items-start gap-2">
          <i className="fas fa-info-circle text-yellow-500 text-sm mt-0.5"></i>
          <div><h4 className="font-medium text-yellow-500 text-sm">Note</h4><p className="text-xs text-neutral-300">Restores to default server settings.</p></div>
        </div>
      </div>
    </div>
  );
};
