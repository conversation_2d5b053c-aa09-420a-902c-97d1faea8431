/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {},
      animation: {
        'spin-slow': 'spin 8s linear infinite',
        'marquee-slow': 'marquee 10s linear infinite',
      },
      keyframes: {
        marquee: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(-100%)' },
        },
      },
    },
  },
  corePlugins: {
    backgroundImage: true,
    gradientColorStops: true
  },
  plugins: [],
}