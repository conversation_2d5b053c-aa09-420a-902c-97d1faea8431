import { ItemType } from '../../../scripts/shared/types/items.types';
import { InventoryItem } from '../../../scripts/shared/types/inventory.types';

/**
 * Context menu option definition
 */
export interface ContextMenuOption {
  id: string;
  label: string | ((item: InventoryItem) => string);
  action: string;
  condition?: (item: InventoryItem, quantity: number) => boolean;
  enabled?: (item: InventoryItem, quantity: number) => boolean;
  className?: string;
  dividerAfter?: boolean;
  icon?: string;
}

/**
 * Context menu configuration for different item types and conditions
 */
export interface ContextMenuConfig {
  // Universal options that appear for all items
  universal: ContextMenuOption[];
  
  // Type-specific options
  typeSpecific: Record<ItemType, ContextMenuOption[]>;
  
  // Item-specific options (by item ID)
  itemSpecific: Record<string, ContextMenuOption[]>;
  
  // Condition-based options
  conditional: Array<{
    condition: (item: InventoryItem, quantity: number) => boolean;
    options: ContextMenuOption[];
  }>;
}

/**
 * Default context menu configuration
 */
export const DEFAULT_CONTEXT_MENU_CONFIG: ContextMenuConfig = {
  // Universal options that appear for ALL items
  universal: [
    {
      id: 'use',
      label: 'Use',
      action: 'use',
      condition: (_, quantity) => quantity > 0
    },
    {
      id: 'give',
      label: 'Give',
      action: 'give',
      condition: (_, quantity) => quantity > 0
    },
    {
      id: 'drop',
      label: 'Drop',
      action: 'drop',
      condition: (_, quantity) => quantity > 0
    }
  ],

  // Type-specific options
  typeSpecific: {
    [ItemType.GENERAL]: [],
    [ItemType.WEAPON]: [],
    [ItemType.CONSUMABLE]: [],
    [ItemType.GADGET]: [],
    [ItemType.AMMO]: [],
    [ItemType.KEY]: []
  },

  // Item-specific configurations
  itemSpecific: {
  },

  // Conditional options based on complex conditions
  conditional: [
    // Stackable items get split option
    {
      condition: (item, quantity) => item.stackable && quantity > 1,
      options: [
        {
          id: 'split',
          label: 'Split Stack',
          action: 'split',
          dividerAfter: true
        }
      ]
    },
  ]
};

/**
 * Generate context menu options for a specific item
 */
export function generateContextMenuOptions(
  item: InventoryItem,
  quantity: number,
  config: ContextMenuConfig = DEFAULT_CONTEXT_MENU_CONFIG
): ContextMenuOption[] {
  const options: ContextMenuOption[] = [];

  // Add universal options
  config.universal.forEach(option => {
    if (!option.condition || option.condition(item, quantity)) {
      options.push(option);
    }
  });

  // Add type-specific options
  const typeOptions = config.typeSpecific[item.type] || [];
  typeOptions.forEach(option => {
    if (!option.condition || option.condition(item, quantity)) {
      options.push(option);
    }
  });

  // Add item-specific options
  const itemOptions = config.itemSpecific[item.definitionId] || [];
  itemOptions.forEach(option => {
    if (!option.condition || option.condition(item, quantity)) {
      options.push(option);
    }
  });

  // Add conditional options
  config.conditional.forEach(({ condition, options: condOptions }) => {
    if (condition(item, quantity)) {
      condOptions.forEach(option => {
        if (!option.condition || option.condition(item, quantity)) {
          options.push(option);
        }
      });
    }
  });

  return options;
}

/**
 * Execute a context menu action
 */
export function executeContextMenuAction(
  action: string,
  item: InventoryItem,
  quantity: number,
  slotIndex: number,
  panelType?: string,
  panelId?: string
): void {
  console.log(`Executing context menu action: ${action}`, { item, quantity, slotIndex, panelType, panelId });
  switch (action) {
    case 'use':
      console.log('Use item:', item.label);
      import('../services/clientRequestSender').then(({ sendUseItemRequest }) => {
        sendUseItemRequest({
          action: 'useItem',
          slotIndex
        });
      });
      break;

    case 'eat':
      console.log('Eat item:', item.label);
      // NuiService.send('eatItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'drink':
      console.log('Drink item:', item.label);
      // NuiService.send('drinkItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'equip':
      console.log('Equip item:', item.label);
      // NuiService.send('equipItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'unequip':
      console.log('Unequip item:', item.label);
      // NuiService.send('unequipItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'give':
      console.log('Give item:', item.label);
      // NuiService.send('giveItem', { itemId: item.definitionId, slotIndex, quantity, panelType, panelId });
      break;

    case 'drop':
      console.log('Drop item:', item.label);
      import('../services/clientRequestSender').then(({ sendDropItemRequest }) => {
        sendDropItemRequest({
          action: 'dropItem',
          slotIndex,
          quantity
        });
      });
      break;

    case 'split':
      console.log('Split stack:', item.label);
      // This should trigger the split modal - handled in the Slot component
      break;

    case 'repair':
      console.log('Repair item:', item.label);
      // NuiService.send('repairItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'inspect':
      console.log('Inspect item:', item.label);
      // NuiService.send('inspectItem', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'modify':
      console.log('Modify weapon:', item.label);
      // NuiService.send('modifyWeapon', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'reload':
      console.log('Reload weapon:', item.label);
      // NuiService.send('reloadWeapon', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'unloadAmmo':
      console.log('Unload ammo from:', item.label);
      // NuiService.send('unloadAmmo', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'makeCall':
      console.log('Make call with:', item.label);
      // NuiService.send('makeCall', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'sendMessage':
      console.log('Send message with:', item.label);
      // NuiService.send('sendMessage', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'applyBandage':
      console.log('Apply bandage:', item.label);
      // NuiService.send('applyBandage', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'charge':
      console.log('Charge device:', item.label);
      // NuiService.send('chargeDevice', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'unlockDoor':
      console.log('Unlock door with:', item.label);
      // NuiService.send('unlockDoor', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'installEngine':
      console.log('Install engine:', item.label);
      // NuiService.send('installEngine', { itemId: item.definitionId, slotIndex, panelType, panelId });
      break;

    case 'secureDrop':
      console.log('Secure drop:', item.label);
      // NuiService.send('secureDrop', { itemId: item.definitionId, slotIndex, quantity, panelType, panelId });
      break;

    case 'useAll':
    case 'eatAll':
    case 'drinkAll':
      console.log(`${action} all:`, item.label, 'quantity:', quantity);
      // NuiService.send(action, { itemId: item.definitionId, slotIndex, quantity, panelType, panelId });
      break;

    default:
      console.warn('Unknown context menu action:', action);
      break;
  }
}

/**
 * Utility function to check if an item has a specific context menu option
 */
export function itemHasContextOption(
  item: InventoryItem,
  quantity: number,
  optionId: string,
  config: ContextMenuConfig = DEFAULT_CONTEXT_MENU_CONFIG
): boolean {
  const options = generateContextMenuOptions(item, quantity, config);
  return options.some(option => option.id === optionId);
}

/**
 * Get a custom context menu configuration for development/testing
 */
export function getCustomContextMenuConfig(): ContextMenuConfig {
  return {
    ...DEFAULT_CONTEXT_MENU_CONFIG,
    // Add custom overrides for testing
    itemSpecific: {
      ...DEFAULT_CONTEXT_MENU_CONFIG.itemSpecific,
      'bread': [
        {
          id: 'toast',
          label: 'Make Toast',
          action: 'makeToast'
        }
      ]
    }
  };
}
