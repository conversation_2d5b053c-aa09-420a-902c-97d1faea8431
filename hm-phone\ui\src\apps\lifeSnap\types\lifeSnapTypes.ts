/**
 * Type definitions for the LifeSnap app
 */

export interface Post {
  id: number;
  userId: string | number; // Can be string (identifier) or number for backward compatibility
  imageUrl: string;
  caption: string;
  content?: string; // Added for cleanup handler
  likes: number[]; // Array of user IDs who liked the post
  comments: Comment[];
  location?: string;
  taggedUsers: number[];
  timestamp: Date | number; // Can be Date or timestamp number from server

  // Embedded profile information
  profileId?: number;
  username?: string;
  fullName?: string;
  profilePicture?: string;
  isVerified?: boolean;
}

export interface Comment {
  id: number;
  userId: string | number; // Can be string (identifier) or number for backward compatibility
  text: string;
  timestamp: Date | number; // Can be Date or timestamp number from server
}

export interface Story {
  id: number;
  userId: string | number; // Can be string (identifier) or number for backward compatibility
  imageUrl: string;
  videoUrl?: string; // Added for cleanup handler
  views: number;
  timestamp: Date | number; // Can be Date or timestamp number from server
  expiresAt: Date | number; // Can be Date or timestamp number from server

  // Embedded profile information
  username?: string;
  fullName?: string;
  profilePicture?: string;
  isVerified?: boolean;
}

export interface Profile {
  id: string | number; // Can be string (identifier) or number for backward compatibility
  username: string;
  fullName: string;
  profilePicture: string;
  bio: string;
  followers: (string | number)[]; // Array of user IDs who follow this profile
  following: (string | number)[]; // Array of user IDs this profile follows
  posts: Post[];
  isVerified?: boolean;
  businessCategory?: string;
}
