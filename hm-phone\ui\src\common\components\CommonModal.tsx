import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode; // Add children to props
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center">
      <div className="bg-[#0a0f1a] p-6 rounded-lg w-full max-w-md relative z-50 shadow-xl">
        <button onClick={onClose} className="absolute top-3 right-3 text-white/80 hover:text-white">
          <i className="fas fa-times text-lg"></i>
        </button>
        {children} {/* Render children here */}
      </div>
    </div>
  );
};

export default Modal;
