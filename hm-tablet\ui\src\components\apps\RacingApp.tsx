import React, { useState } from 'react';

// Mock data - simple and clean
const mockRacerProfile = {
  handle: "StreetPhantom",
  wins: 12,
  losses: 8,
  cashWon: 125000,
  crew: "Night Riders",
  crewColor: "purple"
};

const mockActiveRaces = [
  {
    id: 1,
    name: "Midnight Run",
    track: "Downtown After Dark",
    entryFee: 10000,
    entries: 4,
    maxEntries: 6,
    host: "StreetPhantom",
    startTime: "23:30"
  },
  {
    id: 2,
    name: "Death Wish Circuit", 
    track: "Industrial Complex",
    entryFee: 25000,
    entries: 2,
    maxEntries: 4,
    host: "NightmareKing",
    startTime: "When Full"
  },
  {
    id: 3,
    name: "Quick Cash Sprint",
    track: "Highway Dash", 
    entryFee: 2000,
    entries: 6,
    maxEntries: 8,
    host: "FastLane",
    startTime: "In 10 mins"
  }
];

const mockTracks = [
  {
    id: 1,
    name: "Downtown After Dark",
    distance: "2.3km",
    difficulty: "Medium",
    record: "1:34.567"
  },
  {
    id: 2,
    name: "Industrial Complex",
    distance: "3.1km", 
    difficulty: "Hard",
    record: "2:12.890"
  },
  {
    id: 3,
    name: "Highway Dash",
    distance: "4.2km",
    difficulty: "Easy", 
    record: "1:45.234"
  }
];

const mockLeaderboard = [
  { rank: 1, handle: "SpeedDemon", wins: 45, crew: "Velocity", crewColor: "red" },
  { rank: 2, handle: "GhostRider", wins: 38, crew: "Phantom", crewColor: "blue" },
  { rank: 3, handle: "NightRider", wins: 32, crew: "Dark Angels", crewColor: "green" },
  { rank: 4, handle: "StreetPhantom", wins: 12, crew: "Night Riders", crewColor: "purple" },
  { rank: 5, handle: "FastLane", wins: 18, crew: "Speed Runners", crewColor: "orange" }
];

const mockRaceParticipants = [
  { id: 1, handle: "StreetPhantom", crew: "Night Riders", crewColor: "purple", status: "ready", vehicle: "Infernus" },
  { id: 2, handle: "SpeedDemon", crew: "Velocity", crewColor: "red", status: "ready", vehicle: "Adder" },
  { id: 3, handle: "GhostRider", crew: "Phantom", crewColor: "blue", status: "not ready", vehicle: "Zentorno" },
  { id: 4, handle: "NightRider", crew: "Dark Angels", crewColor: "green", status: "ready", vehicle: "T20" },
  { id: 5, handle: "FastLane", crew: "Speed Runners", crewColor: "orange", status: "ready", vehicle: "Osiris" }
];

const mockChatMessages = [
  { id: 1, handle: "StreetPhantom", message: "Let's make this interesting!", timestamp: "23:15" },
  { id: 2, handle: "SpeedDemon", message: "You're going down!", timestamp: "23:16" },
  { id: 3, handle: "GhostRider", message: "Just warming up the engine", timestamp: "23:17" },
  { id: 4, handle: "NightRider", message: "Good luck everyone", timestamp: "23:18" }
];

type ViewType = 'races' | 'tracks' | 'leaderboards' | 'createRace' | 'profile' | 'raceDetail';

const RacingApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewType>('races');
  const [selectedTrack, setSelectedTrack] = useState('');
  const [raceName, setRaceName] = useState('');
  const [entryFee, setEntryFee] = useState('');
  const [maxRacers, setMaxRacers] = useState('');
  const [selectedRace, setSelectedRace] = useState<any>(null);
  const [chatMessage, setChatMessage] = useState('');

  // Helper function to get crew tag colors
  const getCrewTagColor = (color: string) => {
    const colors = {
      purple: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      red: 'bg-red-500/20 text-red-400 border-red-500/30',
      blue: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      green: 'bg-green-500/20 text-green-400 border-green-500/30',
      orange: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
      yellow: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      pink: 'bg-pink-500/20 text-pink-400 border-pink-500/30',
      cyan: 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30'
    };
    return colors[color as keyof typeof colors] || 'bg-neutral-500/20 text-neutral-400 border-neutral-500/30';
  };
  // Navigation toolbar - clean and minimal
  const renderNavigation = () => (
    <div className="bg-neutral-900 rounded-2xl shadow-xl mb-4 border border-neutral-700/50">
      <div className="p-3">
        <div className="flex gap-1">
          <button
            onClick={() => setCurrentView('races')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              currentView === 'races' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'text-neutral-300 hover:text-white hover:bg-neutral-700/50'
            }`}
          >
            Active Races
          </button>
          <button
            onClick={() => setCurrentView('tracks')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              currentView === 'tracks' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'text-neutral-300 hover:text-white hover:bg-neutral-700/50'
            }`}
          >
            Tracks
          </button>
          <button
            onClick={() => setCurrentView('leaderboards')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              currentView === 'leaderboards' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'text-neutral-300 hover:text-white hover:bg-neutral-700/50'
            }`}
          >
            Leaderboards
          </button>
          <button
            onClick={() => setCurrentView('createRace')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              currentView === 'createRace' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'text-neutral-300 hover:text-white hover:bg-neutral-700/50'
            }`}
          >
            Create Race
          </button>
          <button
            onClick={() => setCurrentView('profile')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              currentView === 'profile' 
                ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                : 'text-neutral-300 hover:text-white hover:bg-neutral-700/50'
            }`}
          >
            Profile
          </button>
        </div>
      </div>
    </div>
  );  // Races view - clean and professional
  const renderRaces = () => (
    <div className="space-y-4">
      <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide">Active Races</h2>
          <span className="text-sm text-neutral-400">{mockActiveRaces.length} races available</span>
        </div>        <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockActiveRaces.map((race) => (
            <div
              key={race.id}
              className="bg-black/50 rounded-lg border border-neutral-700/30 p-4 hover:border-green-500/30 transition-all cursor-pointer flex flex-col"
              onClick={() => {
                setSelectedRace(race);
                setCurrentView('raceDetail');
              }}
            >
              <div className="flex-1">
                <h3 className="text-white font-semibold mb-1">{race.name}</h3>
                <p className="text-neutral-400 text-sm mb-3">{race.track}</p>
                
                <div className="space-y-1 text-xs mb-3">
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Racers:</span>
                    <span className="text-green-400">{race.entries}/{race.maxEntries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Start:</span>
                    <span className="text-blue-400">{race.startTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-400">Host:</span>
                    <span className="text-neutral-300">{race.host}</span>
                  </div>
                </div>

                <div className="text-center mb-3">
                  <div className="text-lg font-bold text-green-400">
                    ${race.entryFee.toLocaleString()}
                  </div>
                  <div className="text-xs text-neutral-400">Entry Fee</div>
                </div>

                {/* Progress bar for race filling */}
                <div className="mb-3 bg-neutral-800 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all"
                    style={{ width: `${(race.entries / race.maxEntries) * 100}%` }}
                  ></div>
                </div>
              </div>              <button 
                className="bg-green-500 hover:bg-green-600 px-4 py-2 rounded text-white text-sm font-medium transition-colors w-full"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle join race logic
                  console.log('Joining race:', race.name);
                }}
              >
                Join Race
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  // Tracks view - clean grid layout
  const renderTracks = () => (
    <div className="space-y-4">
      <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide">Available Tracks</h2>
          <span className="text-sm text-neutral-400">{mockTracks.length} tracks</span>
        </div>
        <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mockTracks.map((track) => (
            <div
              key={track.id}
              className="bg-black/50 rounded-lg border border-neutral-700/30 p-4 hover:border-green-500/30 transition-all"
            >
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-white font-semibold">{track.name}</h3>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  track.difficulty === 'Easy' ? 'bg-green-500/20 text-green-400' :
                  track.difficulty === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' : 
                  'bg-red-500/20 text-red-400'
                }`}>
                  {track.difficulty}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-neutral-400">Distance:</span>
                  <span className="text-white">{track.distance}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-400">Best Time:</span>
                  <span className="text-blue-400 font-mono">{track.record}</span>
                </div>
              </div>
                <button 
                onClick={() => {
                  setSelectedTrack(track.name);
                  setCurrentView('createRace');
                }}
                className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 py-2 px-4 rounded font-medium transition-all text-sm min-w-[140px] mx-auto block"
              >
                Create Race Here
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );  // Leaderboards view - clean table-like layout
  const renderLeaderboards = () => (
    <div className="space-y-4">
      <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide">Leaderboards</h2>
          <span className="text-sm text-neutral-400">Top racers</span>
        </div>
        <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
        
        <div className="space-y-2">
          {mockLeaderboard.map((racer) => (
            <div
              key={racer.rank}
              className={`rounded-lg border p-3 transition-all ${
                racer.handle === mockRacerProfile.handle 
                  ? 'bg-green-500/10 border-green-500/30' 
                  : 'bg-black/30 border-neutral-700/30 hover:border-neutral-600/50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    racer.rank === 1 ? 'bg-green-500/20 text-green-400' :
                    racer.rank === 2 ? 'bg-gray-400/20 text-gray-300' :
                    racer.rank === 3 ? 'bg-orange-500/20 text-orange-400' :
                    'bg-neutral-600/20 text-neutral-400'
                  }`}>
                    {racer.rank}
                  </div>                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-white font-medium">{racer.handle}</h3>
                      {racer.crew && (
                        <span className={`text-xs px-2 py-1 rounded border ${getCrewTagColor(racer.crewColor)}`}>
                          {racer.crew}
                        </span>
                      )}
                    </div>
                    <p className="text-neutral-400 text-sm">{racer.wins} wins</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-neutral-300 font-medium">#{racer.rank}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  // Create race view - clean form
  const renderCreateRace = () => (
    <div className="space-y-4">
      <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide">Create New Race</h2>
        </div>
        <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">Race Name</label>
            <input
              type="text"
              value={raceName}
              onChange={(e) => setRaceName(e.target.value)}
              placeholder="Enter race name..."
              className="w-full p-3 bg-black/50 border border-neutral-700/50 rounded-lg text-white placeholder-neutral-500 focus:border-green-500/50 focus:outline-none transition-colors"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-300 mb-2">Track</label>
            <select
              value={selectedTrack}
              onChange={(e) => setSelectedTrack(e.target.value)}
              className="w-full p-3 bg-black/50 border border-neutral-700/50 rounded-lg text-white focus:border-green-500/50 focus:outline-none transition-colors"
            >
              <option value="">Select a track...</option>
              {mockTracks.map((track) => (
                <option key={track.id} value={track.name}>
                  {track.name} ({track.difficulty})
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">Entry Fee</label>
              <input
                type="number"
                value={entryFee}
                onChange={(e) => setEntryFee(e.target.value)}
                placeholder="0"
                className="w-full p-3 bg-black/50 border border-neutral-700/50 rounded-lg text-white placeholder-neutral-500 focus:border-green-500/50 focus:outline-none transition-colors"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">Max Racers</label>
              <input
                type="number"
                value={maxRacers}
                onChange={(e) => setMaxRacers(e.target.value)}
                placeholder="8"
                className="w-full p-3 bg-black/50 border border-neutral-700/50 rounded-lg text-white placeholder-neutral-500 focus:border-green-500/50 focus:outline-none transition-colors"
              />
            </div>
          </div>          <button 
            className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 py-3 px-6 rounded-lg font-medium transition-all min-w-[150px] mx-auto block"
            onClick={() => {
              // Handle race creation
              console.log('Creating race:', { raceName, selectedTrack, entryFee, maxRacers });
              // Reset form
              setRaceName('');
              setSelectedTrack('');
              setEntryFee('');
              setMaxRacers('');
              // Go back to races view
              setCurrentView('races');
            }}
          >
            Create Race
          </button>
        </div>
      </div>
    </div>
  );

  // Race Detail view - detailed race information with participants and chat
  const renderRaceDetail = () => {
    if (!selectedRace) return null;

    return (
      <div className="space-y-4">
        {/* Header with race info and actions */}
        <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setCurrentView('races')}
              className="flex items-center gap-2 px-3 py-2 bg-neutral-700/50 hover:bg-neutral-600/50 rounded-lg text-neutral-300 hover:text-white transition-all"
            >
              <span className="text-lg">←</span>
              Back to Races
            </button>
            
            <div className="text-center flex-1 mx-4">
              <h2 className="text-xl font-bold text-white mb-1">{selectedRace.name}</h2>
              <p className="text-neutral-400">{selectedRace.track}</p>
            </div>
            
            <button className="bg-green-500 hover:bg-green-600 px-6 py-2 rounded-lg text-white font-medium transition-colors">
              Join Race
            </button>
          </div>
          
          <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
          
          {/* Race Statistics */}
          <div className="grid grid-cols-4 gap-4 text-center">
            <div className="bg-black/50 rounded-lg p-3 border border-neutral-700/30">
              <div className="text-lg font-bold text-green-400">${selectedRace.entryFee.toLocaleString()}</div>
              <div className="text-xs text-neutral-400">Entry Fee</div>
            </div>
            <div className="bg-black/50 rounded-lg p-3 border border-neutral-700/30">
              <div className="text-lg font-bold text-blue-400">{selectedRace.entries}/{selectedRace.maxEntries}</div>
              <div className="text-xs text-neutral-400">Racers</div>
            </div>
            <div className="bg-black/50 rounded-lg p-3 border border-neutral-700/30">
              <div className="text-lg font-bold text-purple-400">{selectedRace.startTime}</div>
              <div className="text-xs text-neutral-400">Start Time</div>
            </div>
            <div className="bg-black/50 rounded-lg p-3 border border-neutral-700/30">
              <div className="text-lg font-bold text-orange-400">{selectedRace.host}</div>
              <div className="text-xs text-neutral-400">Host</div>
            </div>
          </div>
        </div>        {/* Main Content - Participants and Chat in Single Container */}
        <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
          <div className="flex gap-4" style={{ height: '400px' }}>
            {/* Participants List - 3/5 width */}
            <div className="w-3/5 flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white">Race Participants</h3>
                <span className="text-sm text-neutral-400">{mockRaceParticipants.length} joined</span>
              </div>
              <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>                <div className="flex-1 overflow-y-auto custom-scrollbar pr-2">
                <div className="space-y-2">
                  {mockRaceParticipants.map((participant) => (                    <div
                      key={participant.id}
                      className="bg-black/50 rounded-lg border border-neutral-700/30 p-3 flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <h4 className="text-white font-medium">{participant.handle}</h4>
                        <span className="text-neutral-500 text-xs">|</span>
                        <span className={`text-xs px-2 py-1 rounded border ${getCrewTagColor(participant.crewColor)}`}>
                          {participant.crew}
                        </span>
                        <span className="text-neutral-500 text-xs">|</span>
                        <div className="flex items-center gap-1">
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                            <path d="M5 11L6.5 6.5H17.5L19 11M5 11V16H6.5M5 11H19M19 11V16H17.5M6.5 16H17.5M6.5 16V18.5H8.5V16M17.5 16V18.5H15.5V16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <circle cx="8.5" cy="14" r="1.5" stroke="currentColor" strokeWidth="1.5"/>
                            <circle cx="15.5" cy="14" r="1.5" stroke="currentColor" strokeWidth="1.5"/>
                          </svg>
                          <span className="text-blue-400 text-xs font-medium">{participant.vehicle}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Vertical Divider */}
            <div className="w-px bg-gradient-to-b from-transparent via-neutral-600/50 to-transparent"></div>

            {/* Chat Section - 2/5 width */}
            <div className="w-2/5 flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-white">Race Chat</h3>
                <span className="text-sm text-neutral-400">Live</span>
              </div>
              <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
                {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto custom-scrollbar mb-4 pr-2">
                <div className="bg-black/50 rounded-lg border border-neutral-700/30 p-4 h-full">
                  <div className="space-y-3">
                    {mockChatMessages.map((msg) => (
                      <div key={msg.id} className="group">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-green-400 font-medium text-sm">{msg.handle}</span>
                          <span className="text-neutral-500 text-xs opacity-60 group-hover:opacity-100 transition-opacity">{msg.timestamp}</span>
                        </div>
                        <p className="text-neutral-200 text-sm leading-relaxed pl-1">{msg.message}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Chat Input */}
              <div className="flex gap-2">
                <input
                  type="text"
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 p-2 bg-black/50 border border-neutral-700/50 rounded-lg text-white placeholder-neutral-500 focus:border-green-500/50 focus:outline-none transition-colors text-sm"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      // Handle send message
                      console.log('Sending message:', chatMessage);
                      setChatMessage('');
                    }
                  }}
                />
                <button
                  onClick={() => {
                    // Handle send message
                    console.log('Sending message:', chatMessage);
                    setChatMessage('');
                  }}
                  className="bg-green-500 hover:bg-green-600 px-3 py-2 rounded-lg text-white font-medium transition-colors text-sm"
                >
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Profile view - clean and professional
  const renderProfile = () => (
    <div className="space-y-4">
      <div className="bg-neutral-900 rounded-2xl shadow-xl p-4 border border-neutral-700/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide">Racer Profile</h2>
        </div>
        <div className="h-px bg-gradient-to-r from-transparent via-green-500/20 to-transparent mb-4"></div>
          {/* Profile Header */}
        <div className="bg-black/50 rounded-lg border border-neutral-700/30 p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-white mb-1">{mockRacerProfile.handle}</h3>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded border ${getCrewTagColor(mockRacerProfile.crewColor)}`}>
                  {mockRacerProfile.crew}
                </span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-neutral-400">Overall Performance</div>
              <div className="text-lg font-bold text-green-400">
                {Math.round((mockRacerProfile.wins / (mockRacerProfile.wins + mockRacerProfile.losses)) * 100)}% Win Rate
              </div>
            </div>
          </div>
        </div>{/* Stats in Single Row */}
        <div className="bg-black/50 rounded-lg border border-neutral-700/30 p-4 mb-4">
          <div className="flex items-center justify-between text-center">
            <div className="flex-1">
              <div className="text-2xl font-bold text-green-400 mb-1">{mockRacerProfile.wins}</div>
              <div className="text-sm font-medium text-neutral-300">Wins</div>
            </div>
            
            <div className="flex-1">
              <div className="text-2xl font-bold text-red-400 mb-1">{mockRacerProfile.losses}</div>
              <div className="text-sm font-medium text-neutral-300">Losses</div>
            </div>
            
            <div className="flex-1">
              <div className="text-2xl font-bold text-green-400 mb-1">
                ${mockRacerProfile.cashWon.toLocaleString()}
              </div>
              <div className="text-sm font-medium text-neutral-300">Total Earnings</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  // Main render function
  const renderCurrentView = () => {
    switch (currentView) {
      case 'races':
        return renderRaces();
      case 'tracks':
        return renderTracks();
      case 'leaderboards':
        return renderLeaderboards();
      case 'createRace':
        return renderCreateRace();
      case 'profile':
        return renderProfile();
      case 'raceDetail':
        return renderRaceDetail();
      default:
        return renderRaces();
    }
  };  return (
    <div className="h-full w-full flex flex-col bg-black">
      {renderNavigation()}
      <div className="flex-1 overflow-y-auto custom-scrollbar pb-16">
        {renderCurrentView()}
      </div>
    </div>
  );
};

export default RacingApp;