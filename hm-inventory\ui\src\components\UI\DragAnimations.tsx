import React from 'react';
import { DragOverlay } from '@dnd-kit/core';
import Slot from '../InventoryGrid/Slot';
import { DragItem } from '@shared';

interface EnhancedDragOverlayProps {
  activeDragItem: DragItem | null;
}

const EnhancedDragOverlay: React.FC<EnhancedDragOverlayProps> = ({ activeDragItem }) => {
  return (
    <DragOverlay dropAnimation={{ duration: 0 }}>
      {activeDragItem && (
        <div className="pointer-events-none">          <Slot
            panelType={activeDragItem.source.panelType}
            panelId={activeDragItem.source.panelId}
            slotIndex={activeDragItem.source.slotIndex}
            inventoryItem={activeDragItem.item}
            quantity={activeDragItem.source.quantity}
            hideQuickslotBadge={true}
          />
        </div>
      )}
    </DragOverlay>
  );
};

export default EnhancedDragOverlay;

