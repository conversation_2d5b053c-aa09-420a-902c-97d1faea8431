export interface LoveLinkProfile {
  id: number;
  name: string;
  age: number;
  photos: string[];
  bio: string;
  occupation: string;
  location: string;
  interests: string[];
  lastActive: Date;
  verificationStatus: 'verified' | 'unverified';
  premium: boolean;
}

export interface Match {
  id: number;
  profileId: number;
  matchedId: number;
  timestamp: Date;
  lastMessage?: {
    text: string;
    timestamp: Date;
    isRead: boolean;
  };
}

export interface Message {
  id: number;
  matchId: number;
  senderId: number;
  text: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
}
