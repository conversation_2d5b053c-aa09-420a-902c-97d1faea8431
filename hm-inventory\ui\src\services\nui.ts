import { ItemSlot } from '@shared/inventory.types';
import { useInventoryStore, GroundPanelItem, SecondaryPanelType } from '../stores/inventoryStore';

interface NuiMessage {
  action: string;
  data?: {
    visible?: boolean;
    inventory?: ItemSlot[];
    text?: string;
    [key: string]: unknown;
  };
}

/**
 * Handles incoming NUI messages from the client script.
 */
function handleNuiMessage(event: MessageEvent<NuiMessage>) {
  try {    
    const action = event.data?.action;
    const data = event.data?.data;
    
    if (!action) {
      console.warn('[NUI] No action found in message:', event.data);
      return;
    }
    
    const store = useInventoryStore.getState();
    
    switch (action) {
      case 'setVisible':
        console.log('[NUI] Received show inventory message:', event.data);
        if (data && typeof data.visible === 'boolean') {
          store.setInventoryOpen(data.visible);
        } else {
          console.warn('[NUI] Invalid data for setVisible:', data);
        }
        break;
        
      case 'initialize':
        {
          console.log('[NUI] Received initialize message:', event.data);
          // Initialize the inventory system
          if (data?.inventory && Array.isArray(data.inventory)) {
            store.loadInventoryData(data.inventory as ItemSlot[]);
            // console.log('[NUI] Loaded inventory data with', data.inventory.length, 'items');
          }
          // Set initial visibility state
          if (typeof data?.visible === 'boolean') {
            store.setInventoryOpen(data.visible);
          }
          break;
        }
        
      case 'loadInventory':
        {
          
          if (Array.isArray(data)) {
            const inventoryData = data as ItemSlot[];
            console.log('[NUI] Received load inventory message with', inventoryData.length, 'items');
            store.loadInventoryData(inventoryData);
          } else {
            console.warn('[NUI] Invalid inventory data format:', data);
          }
          break;
        }
        
      case 'updateInventory':
        {
          if (Array.isArray(data)) {
            const inventoryData = data as ItemSlot[];
            console.log('[NUI] Received update inventory message with', inventoryData.length, 'items');
            store.loadInventoryData(inventoryData);
          } else {
            console.warn('[NUI] Invalid update inventory data format:', data);
          }
          break;
        }
        
      case 'showPickupPrompt':
        {
          console.log('[NUI] Received show pickup prompt:', data);
          if (data?.text) {
            store.setPickupPrompt(data.text);
          }
          break;
        }
        case 'hidePickupPrompt':
        {
          console.log('[NUI] Received hide pickup prompt');
          store.setPickupPrompt(null);
          break;
        }
        
      case 'updateGroundPanel':
        {
          console.log('[NUI] Received update ground panel:', data);
          if (Array.isArray(data)) {
            const groundItems = data as GroundPanelItem[];
            store.updateGroundItems(groundItems);
          } else {
            console.warn('[NUI] Invalid ground panel data format:', data);
          }
          break;
        }
        
      case 'setSecondaryPanel':
        {
          console.log('[NUI] Received set secondary panel:', data);
          if (typeof data === 'string') {
            // Convert string to SecondaryPanelType
            switch (data) {
              case 'ground':
                store.openSecondaryPanel(SecondaryPanelType.GROUND);
                break;
              case 'container':
                store.openSecondaryPanel(SecondaryPanelType.CONTAINER);
                break;
              case 'stash':
                store.openSecondaryPanel(SecondaryPanelType.STASH);
                break;
              case 'crafting':
                store.openSecondaryPanel(SecondaryPanelType.CRAFTING);
                break;
              case 'none':
              default:
                store.closeSecondaryPanel();
                break;
            }
          }
          break;
        }
        
      default:
        console.warn('[NUI] Unhandled action:', action, 'with data:', data);
        break;
    }
  } catch (error) {
    console.error('[NUI] Error handling NUI message:', error, 'Event:', event.data);
  }
}

/**
 * Sets up the NUI listener for messages.
 */
export function setupNuiListener(): void {
  window.addEventListener('message', handleNuiMessage);
  console.log('[NUI] Inventory listener registered');
}

/**
 * Optionally remove the NUI listener.
 */
export function cleanupNuiListener(): void {
  window.removeEventListener('message', handleNuiMessage);
  console.log('[NUI] Inventory listener removed');
}
