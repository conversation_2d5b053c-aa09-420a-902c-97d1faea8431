/**
 * Multicharacter event definitions
 * All events follow the standardized naming convention: resourceName:target:action
 */

/**
 * Multicharacter events
 * All events follow the standardized naming convention
 */


export const MulticharacterEvents = {
    // Client to server events
    /**
     * Request characters from server
     * @event
     * @param {void} No payload
     */
    REQUEST_CHARACTERS: 'hm-multicharacter:server:requestCharacters',

    /**
     * Select a character
     * @event
     * @param {SelectCharacterPayload} payload - The character selection data
     */
    SELECT_CHARACTER: 'hm-multicharacter:server:selectCharacter',

    /**
     * Create a new character
     * @event
     * @param {CreateCharacterPayload} payload - The character creation data
     */
    CREATE_CHARACTER: 'hm-multicharacter:server:create<PERSON>haracter',

    /**
     * Delete a character
     * @event
     * @param {DeleteCharacterPayload} payload - The character deletion data
     */
    DELETE_CHARACTER: 'hm-multicharacter:server:deleteCharacter',

    // Server to client events
    /**
     * Show the character selection screen
     * @event
     * @param {void} No payload
     */
    SHOW_SELECTION_SCREEN: 'hm-multicharacter:client:showSelectionScreen',

    /**
     * Send characters data to client
     * @event
     * @param {CharactersReceivedPayload} payload - The characters data
     */
    RECEIVE_CHARACTERS: 'hm-multicharacter:client:receiveCharacters', // Renamed from CHARACTERS_DATA

    /**
     * Notify client that a character was selected successfully
     * @event
     * @param {CharacterSelectedPayload} payload - The selected character data
     */
    CHARACTER_SELECTED: 'hm-multicharacter:client:characterSelected',

    /**
     * Notify client that character selection failed
     * @event
     * @param {SelectCharacterFailedPayload} payload - Details of the selection failure
     */
    SELECT_CHARACTER_FAILED: 'hm-multicharacter:client:selectCharacterFailed', // New

    /**
     * Notify client that a character was created successfully
     * @event
     * @param {CharacterCreatedPayload} payload - The created character data (or ID)
     */
    CHARACTER_CREATED: 'hm-multicharacter:client:characterCreated',

    /**
     * Notify client that character creation failed
     * @event
     * @param {CreateCharacterFailedPayload} payload - Details of the creation failure
     */
    CREATE_CHARACTER_FAILED: 'hm-multicharacter:client:createCharacterFailed', // New

    /**
     * Notify client that a character was deleted successfully
     * @event
     * @param {CharacterDeletedPayload} payload - Confirmation of deletion (e.g., deleted character ID)
     */
    CHARACTER_DELETED: 'hm-multicharacter:client:characterDeleted',

    /**
     * Notify client that character deletion failed
     * @event
     * @param {DeleteCharacterFailedPayload} payload - Details of the deletion failure
     */
    DELETE_CHARACTER_FAILED: 'hm-multicharacter:client:deleteCharacterFailed', // New


    // NUI events
    /**
     * NUI is ready
     * @event
     * @param {void} No payload
     */
    NUI_READY: 'hm-multicharacter:nui:ready',

    /**
     * Select a character from NUI
     * @event
     * @param {SelectCharacterPayload} payload - The character selection data
     */
    NUI_SELECT_CHARACTER: 'hm-multicharacter:nui:selectCharacter',

    /**
     * Create a character from NUI
     * @event
     * @param {CreateCharacterPayload} payload - The character creation data
     */
    NUI_CREATE_CHARACTER: 'hm-multicharacter:nui:createCharacter',

    /**
     * Delete a character from NUI
     * @event
     * @param {DeleteCharacterPayload} payload - The character deletion data
     */
    NUI_DELETE_CHARACTER: 'hm-multicharacter:nui:deleteCharacter',

    /**
     * Close NUI
     * @event
     * @param {void} No payload
     */
    NUI_CLOSE: 'hm-multicharacter:nui:close',

    /**
     * Play character (final selection) from NUI
     * @event
     * @param {SelectCharacterPayload} payload - The character selection data
     */
    NUI_PLAY_CHARACTER: 'hm-multicharacter:nui:playCharacter',

    /**
     * Character hover from NUI (for camera transitions)
     * @event
     * @param {CharacterHoverPayload} payload - The character hover data
     */
    NUI_CHARACTER_HOVER: 'hm-multicharacter:nui:characterHover',

    /**
     * Character hover end from NUI (return camera to overview)
     * @event
     * @param {void} No payload
     */
    NUI_CHARACTER_HOVER_END: 'hm-multicharacter:nui:characterHoverEnd',

    /**
     * Update characters in NUI
     * @event
     * @param {CharactersDataPayload} payload - The characters data
     */
    NUI_UPDATE_CHARACTERS: 'hm-multicharacter:nui:updateCharacters',

    /**
     * Notify NUI that a character was selected successfully
     * @event
     * @param {CharacterSelectedPayload} payload - The selected character data
     */
    NUI_CHARACTER_SELECTED: 'hm-multicharacter:nui:characterSelected',

    /**
     * Notify NUI that a character was created successfully
     * @event
     * @param {CharacterCreatedPayload} payload - The created character data
     */
    NUI_CHARACTER_CREATED: 'hm-multicharacter:nui:characterCreated',

    /**
     * Notify NUI that a character was deleted successfully
     * @event
     * @param {CharacterDeletedPayload} payload - The deleted character data
     */
    NUI_CHARACTER_DELETED: 'hm-multicharacter:nui:characterDeleted',

    /**
     * Notify NUI that character selection failed
     * @event
     * @param {object} payload - Error details
     */
    NUI_SELECT_CHARACTER_FAILED: 'hm-multicharacter:nui:selectCharacterFailed',

    /**
     * Notify NUI that character creation failed
     * @event
     * @param {object} payload - Error details
     */
    NUI_CREATE_CHARACTER_FAILED: 'hm-multicharacter:nui:createCharacterFailed',

    /**
     * Notify NUI that character deletion failed
     * @event
     * @param {object} payload - Error details
     */
    NUI_DELETE_CHARACTER_FAILED: 'hm-multicharacter:nui:deleteCharacterFailed',

    /**
     * Generic NUI error
     * @event
     * @param {object} payload - Error details
     */
    NUI_ERROR: 'hm-multicharacter:nui:error',
} as const;

/**
 * Payload types
 */

// Event payload types
export interface RequestCharactersPayload {
    identifier: string;
}

export interface CharactersDataPayload {
    characters: CharacterSelectionData[];
}

export interface SelectCharacterPayload {
    stateid: string;
}

export interface CharacterSelectedPayload {
    success: boolean;
    message: string;
    stateid: string;
}

export interface CreateCharacterPayload {
    characterData: CharacterCreationData;
}

export interface CharacterCreatedPayload {
    success: boolean;
    message: string;
    character: CharacterSelectionData | null; // Allow null for error cases
}

export interface DeleteCharacterPayload {
    stateid: string;
}

export interface CharacterDeletedPayload {
    success: boolean;
    message: string;
    stateid: string;
}

export interface CharacterHoverPayload {
    stateid: string;
    spawnPointIndex: number;
}

export interface ErrorPayload {
    message: string;
    code?: string;
}

// Import character types
import {
    CharacterSelectionData,
    CharacterCreationData
} from './character.types';