export interface Vector2 {
  x: number;
  y: number;
}

export interface Vector3 extends Vector2 {
  z: number;
}

export interface CellPoint {
  cellX: number;
  cellY: number;
}

export interface InventoryItem {
  id: string;
  name: string;
  count: number;
  weight: number;
  metadata?: Record<string, any>;
  createdAt: number;
}

export interface InventoryZoneContext {
  capacity: number;
  type: 'ground' | 'vehicle' | 'crafting' | 'storage';
  allowPickup: boolean;
  allowDrop: boolean;
  expirationTime?: number; // in milliseconds
  items: Map<string, InventoryItem>;
  createdAt: number;
  expiresAt?: number;
}

export interface ZoneOptions {
  minZ?: number;
  maxZ?: number;
  debugColor?: [number, number, number, number]; // RGBA
  // Inventory integration options
  inventoryContext?: InventoryZoneContext;
}

export interface BoxZoneOptions extends ZoneOptions {
  heading?: number; // Optional heading for rotated boxes
}

export interface CircleZoneOptions extends ZoneOptions {
  // specific options for circles
}

export interface PolygonZoneOptions extends ZoneOptions {
  // specific options for polygons
}

export type AnyZoneOptions = ZoneOptions | BoxZoneOptions | CircleZoneOptions | PolygonZoneOptions;

export interface Zone<T extends AnyZoneOptions = AnyZoneOptions> {
  name: string;
  type: 'box' | 'circle' | 'polygon';
  center?: Vector2; // For circle and box
  width?: number;   // For box
  height?: number;  // For box
  radius?: number;  // For circle
  vertices?: Vector2[]; // For polygon
  options: T;
  boundingBox: { minX: number; minY: number; maxX: number; maxY: number };
  cells: Set<string>; // Set of cell keys (e.g., "cellX_cellY") this zone overlaps with
}

// Zone with inventory context
export interface InventoryZone<T extends AnyZoneOptions = AnyZoneOptions> extends Zone<T> {
  inventoryContext: InventoryZoneContext;
  stateBagId: string; // State bag identifier for data sync
}
