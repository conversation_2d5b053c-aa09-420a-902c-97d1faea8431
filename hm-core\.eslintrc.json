{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": ["error", {"endOfLine": "lf"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "no-console": ["warn", {"allow": ["warn", "error"]}], "linebreak-style": ["error", "unix"]}}