/// <reference types="@citizenfx/client" />

import { ItemSlot } from '@shared';
import './inventory.zones'; // Initialize inventory zone integration

let isInventoryOpen = false;
let hasCharacterLoaded = false; // Track if character has been loaded
let nearbyDroppedItems: number[] = []; // Track nearby dropped items for pickup
let pickupPrompt = false; // Track if pickup prompt is shown

// Toggle inventory UI
function toggleInventory() {
    isInventoryOpen = !isInventoryOpen;

    // Send NUI message to show/hide inventory
    SendNUIMessage({
        action: 'setVisible',
        data: {
            visible: isInventoryOpen
        }
    });

    // Set NUI focus for mouse interaction when open
    SetNuiFocus(isInventoryOpen, isInventoryOpen);
}

// Register key mapping for Tab key
RegisterKeyMapping('hm-inventory:toggle', 'Toggle Inventory', 'keyboard', 'TAB');

// Register command handler
RegisterCommand(
    'hm-inventory:toggle',
    () => {
        // Only allow inventory toggle if character is loaded
        if (!hasCharacterLoaded) {
            console.log('[hm-inventory] Cannot open inventory - no character loaded. Use /requestInventory if you believe a character should be loaded.');
            return;
        }
        
        toggleInventory();
        console.log('[Client] Inventory toggle command executed:', isInventoryOpen ? 'Opened' : 'Closed');
    },
    false
);

// Handle NUI callbacks (when UI sends messages back)
RegisterNuiCallbackType('closeInventory');
// eslint-disable-next-line @typescript-eslint/ban-types
on('__cfx_nui:closeInventory', (data: any, cb: (result: string) => void) => {
    console.log('[Client] closeInventory callback received:', data);
    console.log('[Client] Current inventory state - isInventoryOpen:', isInventoryOpen);
    
    isInventoryOpen = false;
    SetNuiFocus(false, false);
    
    // Send message to hide UI
    SendNUIMessage({
        action: 'setVisible',
        data: {
            visible: false
        }
    });
    
    console.log('[Client] Inventory closed, NUI focus removed, UI hidden');
    console.log('[Client] New inventory state - isInventoryOpen:', isInventoryOpen);
    
    cb('ok');
});

// Register NUI callbacks for inventory operations
RegisterNuiCallbackType('MoveItemRequest');
on('__cfx_nui:MoveItemRequest', (data: any, cb: (result: string) => void) => {
    console.log('[hm-inventory] Move item request:', data);
    
    // Send the move request to the server for processing
    emitNet('hm-inventory:server:moveItem', {
        sourceIndex: data.sourceIndex,
        sourcePanelType: data.sourcePanelType,
        sourcePanelId: data.sourcePanelId,
        destinationIndex: data.destinationIndex,
        destinationPanelType: data.destinationPanelType,
        destinationPanelId: data.destinationPanelId,
        quantity: data.quantity
    });
    
    cb('ok');
});

RegisterNuiCallbackType('DropItemRequest');
on('__cfx_nui:DropItemRequest', (data: { slotIndex: number; quantity: number }, cb: (result: string) => void) => {
    console.log(`[hm-inventory] Drop item request for slot ${data.slotIndex}, quantity: ${data.quantity}`);
    
    // Send the drop request to the server for processing
    emitNet('hm-inventory:server:dropItem', data);
    
    cb('ok');
});

RegisterNuiCallbackType('EquipItemRequest');
on('__cfx_nui:EquipItemRequest', (data: any, cb: (result: string) => void) => {
    console.log('[hm-inventory] Equip item request:', data);
    // TODO: Handle equip item logic here
    // For now, just acknowledge the request
    cb('ok');
});

RegisterNuiCallbackType('UseItemRequest');
on('__cfx_nui:UseItemRequest', (data: { slotIndex: number }, cb: (result: string) => void) => {
    console.log(`[hm-inventory] Use item request for slot: ${data.slotIndex}`);
    
    // Send the use request to the server for processing
    emitNet('hm-inventory:server:useItem', data.slotIndex);
    
    cb('ok');
});

// Export for other scripts if needed
global.exports('toggleInventory', toggleInventory);
global.exports('isInventoryOpen', () => isInventoryOpen);

onNet('hm-inventory:client:loadInventory', (inventory: ItemSlot[]) => {
    // Handle loading inventory data from the server
    console.log('[hm-inventory] Inventory loaded:', inventory);

    // Only confirm character is loaded if we receive inventory data
    // Empty inventory from server usually means no character loaded
    if (inventory && inventory.length >= 0) {
        // Even empty inventory is valid if server sent it after character validation
        hasCharacterLoaded = true;
        console.log('[hm-inventory] Character confirmed as loaded - received inventory with', inventory.length, 'items');
    }

    // Initialize NUI with the actual inventory data
    SendNUIMessage({
        action: 'initialize',
        data: {
            inventory: inventory,
            visible: false
        }
    });
});

onNet('hm-inventory:client:updateInventory', (inventory: ItemSlot[]) => {
    // Handle inventory updates from the server
    console.log('[hm-inventory] Inventory updated:', inventory);

    // Update the UI with the new items
    SendNUIMessage({
        action: 'updateInventory',
        data: inventory
    });
});

// Handle ground panel updates from server
onNet('hm-inventory:client:updateGroundPanel', (groundItems: any[]) => {
    console.log('[hm-inventory] Ground panel updated:', groundItems);
    
    // Forward ground panel data to UI
    SendNUIMessage({
        action: 'updateGroundPanel',
        data: groundItems
    });
    
    // Show ground panel if items are available and inventory is open
    if (isInventoryOpen && groundItems.length > 0) {
        SendNUIMessage({
            action: 'setSecondaryPanel',
            data: 'ground'
        });
    } else if (groundItems.length === 0) {
        // Hide ground panel if no items
        SendNUIMessage({
            action: 'setSecondaryPanel',
            data: 'none'
        });
    }
});

// Listen for character creation event and request initial inventory creation
onNet('hm-multicharacter:client:characterCreated', (data: any) => {
    const character = data.character;
    console.log(`[hm-inventory] Character created, requesting initial inventory for stateid: ${character?.stateid}`);

    // Trigger server event to create initial inventory
    emitNet('hm-inventory:server:createInitialInventory', character);
});

// Listen for character load event from hm-core to initialize inventory properly
onNet('hm-core:playerLoaded', (playerData: { characterId: string; stateid: string; characterData: any }) => {
    // Set character loaded flag
    hasCharacterLoaded = true;
    console.log('[hm-inventory] Character loaded from core, inventory ready to use. StateiId:', playerData.stateid);
    
    // The server should automatically send inventory data via hm-inventory:client:loadInventory
    // when it receives the hm-core:playerLoaded event
    console.log('[hm-inventory] Character data loaded, server should send inventory automatically');
});

// Add a client command to manually request inventory (useful for resource restarts)
RegisterCommand('requestInventory', () => {
    console.log('[hm-inventory] Manual inventory request initiated');
    emitNet('hm-inventory:server:requestInventory');
}, false);

// Add export for other resources to check if character is loaded
global.exports('isCharacterLoaded', () => hasCharacterLoaded);
global.exports('requestInventory', () => {
    if (hasCharacterLoaded) {
        emitNet('hm-inventory:server:requestInventory');
        return true;
    } else {
        console.log('[hm-inventory] Cannot request inventory - no character loaded');
        return false;
    }
});

// Handle resource start - check for already loaded character
on('onResourceStart', (resourceName: string) => {
    if (resourceName === GetCurrentResourceName()) {
        console.log('[hm-inventory] Resource started, initializing UI framework');
        
        // Initialize UI with empty state - data will come from character check
        SendNUIMessage({
            action: 'initialize',
            data: {
                inventory: [],
                visible: false
            }
        });
        
        console.log('[hm-inventory] UI framework initialized');
        
        // Don't automatically request inventory on resource start
        // Wait for proper character load events or manual request
        console.log('[hm-inventory] Waiting for character to be loaded via hm-core events');
    }
});

// Handle resource stop - cleanup NUI state
on('onResourceStop', (resourceName: string) => {
    if (resourceName === GetCurrentResourceName()) {
        console.log('[hm-inventory] Resource stopping, cleaning up NUI');
        
        // Close inventory if open and remove NUI focus
        if (isInventoryOpen) {
            isInventoryOpen = false;
            SetNuiFocus(false, false);
            
            // Send message to hide UI
            SendNUIMessage({
                action: 'setVisible',
                data: {
                    visible: false
                }
            });
        }
    }
});

// Dropped Item Pickup System
setTick(() => {
    if (!hasCharacterLoaded) return;
    
    const playerPed = PlayerPedId();
    const playerCoords = GetEntityCoords(playerPed, false);
    const nearbyEntities: number[] = [];
    
    // Find all nearby objects that are dropped items
    const allObjects = GetGamePool('CObject');
    for (const obj of allObjects) {
        if (DoesEntityExist(obj)) {
            const objCoords = GetEntityCoords(obj, false);
            const distance = GetDistanceBetweenCoords(
                playerCoords[0], playerCoords[1], playerCoords[2],
                objCoords[0], objCoords[1], objCoords[2],
                true
            );
            
            // Check if object has item data (indicating it's a dropped item)
            if (distance <= 2.0 && Entity(obj).state.itemData) {
                nearbyEntities.push(obj);
            }
        }
    }
    
    nearbyDroppedItems = nearbyEntities;
    
    // Show pickup prompt if near items
    const shouldShowPrompt = nearbyDroppedItems.length > 0;
    if (shouldShowPrompt !== pickupPrompt) {
        pickupPrompt = shouldShowPrompt;
        
        if (pickupPrompt) {
            // Show pickup text
            SendNUIMessage({
                action: 'showPickupPrompt',
                data: {
                    text: `Press [E] to pickup item${nearbyDroppedItems.length > 1 ? 's' : ''}`
                }
            });
        } else {
            // Hide pickup text
            SendNUIMessage({
                action: 'hidePickupPrompt',
                data: {}
            });
        }
    }
});

// Register pickup key
RegisterKeyMapping('hm-inventory:pickup', 'Pickup Item', 'keyboard', 'E');

RegisterCommand('hm-inventory:pickup', () => {
    if (!hasCharacterLoaded || nearbyDroppedItems.length === 0) return;
    
    // Pick up the nearest item
    const playerPed = PlayerPedId();
    const playerCoords = GetEntityCoords(playerPed, false);
    let closestItem: number | null = null;
    let closestDistance = 999.0;
    
    for (const item of nearbyDroppedItems) {
        const itemCoords = GetEntityCoords(item, false);
        const distance = GetDistanceBetweenCoords(
            playerCoords[0], playerCoords[1], playerCoords[2],
            itemCoords[0], itemCoords[1], itemCoords[2],
            true
        );
        
        if (distance < closestDistance) {
            closestDistance = distance;
            closestItem = item;
        }
    }
    
    if (closestItem && Entity(closestItem).state.itemData) {
        const itemData = Entity(closestItem).state.itemData;
        console.log(`[hm-inventory] Attempting to pickup item:`, itemData);
        
        // Send pickup request to server
        emitNet('hm-inventory:server:pickupItem', closestItem);
    }
}, false);
