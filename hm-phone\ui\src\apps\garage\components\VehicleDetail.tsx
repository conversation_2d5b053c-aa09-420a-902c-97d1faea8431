import React from 'react';
import { motion } from 'framer-motion';
import { Vehicle } from '../types/garageTypes';
import { getStatusColorClass, getCategoryIcon } from '../utils/garageUtils';

interface VehicleDetailProps {
  vehicle: Vehicle;
  onTrack: (id: number) => void;
  onToggleEngine: (id: number) => void;
}

const VehicleDetail: React.FC<VehicleDetailProps> = ({ vehicle, onTrack }) => {
  // Determine if vehicle is financed
  const isFinanced = vehicle.balance && vehicle.balance > 0;

  // Calculate condition percentages
  const engineCondition = (vehicle.engine / 10).toFixed(0);
  const bodyCondition = (vehicle.body / 10).toFixed(0);

  // Get category icon
  const categoryIcon = getCategoryIcon(vehicle.category || '');

  return (
    <motion.div
      layoutId={`vehicle-${vehicle.id}`}
      className="relative h-full flex flex-col bg-gradient-to-b from-black/40 to-black/80 backdrop-blur-sm"
    >
      {/* Header with vehicle image - fixed height to prevent overflow */}
      <div className="p-4">
        <div className="relative mb-8">
          {/* Vehicle image */}
          <div className="h-40 rounded-xl overflow-hidden">
            <img
              src={vehicle.imageUrl}
              alt={vehicle.name}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Floating info card */}
          <div className="absolute -bottom-6 left-0 right-0 backdrop-blur-xl bg-white/10 rounded-xl p-3 border border-white/20 mx-2">
            <div className="flex justify-between items-center">
              <div className="mr-2">
                <h2 className="text-lg font-medium text-white leading-tight">{vehicle.name}</h2>
                <p className="text-white/70 text-sm">{vehicle.brand}</p>
              </div>
              <div className={`px-3 py-1 rounded-full text-xs whitespace-nowrap ${getStatusColorClass(vehicle.status || '')}`}>
                {vehicle.status}
              </div>
            </div>
          </div>
        </div>

        {/* License plate - moved outside the scrollable area */}
        <div className="flex justify-center mb-4 mt-6">
          <div className="bg-yellow-400 text-black px-4 py-1.5 rounded-lg font-bold inline-block">
            {vehicle.licensePlate || vehicle.plate}
          </div>
        </div>
      </div>

      {/* Content area with glassmorphic cards - scrollable with proper padding */}
      <div className="flex-1 overflow-auto px-4 pb-4 custom-scrollbar">
        {/* Stats grid - 2x2 with equal sizing */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          {/* Location */}
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10">
            <div className="flex items-center mb-1.5">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className="fas fa-map-marker-alt text-red-400 text-sm"></i>
              </div>
              <span className="text-white/80 text-sm">Location</span>
            </div>
            <div className="text-white text-sm pl-9 break-words">{vehicle.location || vehicle.garage}</div>
          </div>

          {/* Category */}
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10">
            <div className="flex items-center mb-1.5">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className={`fas ${categoryIcon} text-purple-400 text-sm`}></i>
              </div>
              <span className="text-white/80 text-sm">Category</span>
            </div>
            <div className="text-white text-sm capitalize pl-9">{vehicle.category || 'Unknown'}</div>
          </div>

          {/* Type */}
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10">
            <div className="flex items-center mb-1.5">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className="fas fa-car text-blue-400 text-sm"></i>
              </div>
              <span className="text-white/80 text-sm">Type</span>
            </div>
            <div className="text-white text-sm capitalize pl-9">{vehicle.type || 'Automobile'}</div>
          </div>

          {/* Fuel */}
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10">
            <div className="flex items-center mb-1.5">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className="fas fa-gas-pump text-green-400 text-sm"></i>
              </div>
              <span className="text-white/80 text-sm">Fuel</span>
            </div>
            <div className="pl-9">
              <div className="flex items-center justify-between mb-1">
                <span className="text-white text-sm">{vehicle.fuel}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-1">
                <div
                  className="bg-green-500 h-1 rounded-full"
                  style={{ width: `${vehicle.fuel}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Condition card */}
        <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10 mb-4">
          <h3 className="text-white font-medium text-sm mb-3 flex items-center">
            <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
              <i className="fas fa-heartbeat text-red-400 text-sm"></i>
            </div>
            Vehicle Condition
          </h3>

          <div className="space-y-3 pl-9">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-white/80">Engine</span>
                <span className="text-white">{engineCondition}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-1">
                <div
                  className={`h-1 rounded-full ${
                    Number(engineCondition) > 75 ? 'bg-green-500' :
                    Number(engineCondition) > 40 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${engineCondition}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-white/80">Body</span>
                <span className="text-white">{bodyCondition}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-1">
                <div
                  className={`h-1 rounded-full ${
                    Number(bodyCondition) > 75 ? 'bg-green-500' :
                    Number(bodyCondition) > 40 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${bodyCondition}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Finance information if applicable */}
        {isFinanced && (
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10 mb-4">
            <h3 className="text-white font-medium text-sm mb-3 flex items-center">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className="fas fa-money-bill-wave text-green-400 text-sm"></i>
              </div>
              Finance Information
            </h3>

            <div className="space-y-2 pl-9">
              <div className="flex justify-between text-sm">
                <span className="text-white/80">Balance Due:</span>
                <span className="text-white">${vehicle.balance?.toLocaleString()}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-white/80">Payment Amount:</span>
                <span className="text-white">${vehicle.paymentamount?.toLocaleString()}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-white/80">Payments Left:</span>
                <span className="text-white">{vehicle.paymentsleft}</span>
              </div>
            </div>
          </div>
        )}

        {/* Job information if applicable */}
        {vehicle.job && (
          <div className="backdrop-blur-md bg-white/5 rounded-xl p-3 border border-white/10 mb-4">
            <h3 className="text-white font-medium text-sm mb-3 flex items-center">
              <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                <i className="fas fa-briefcase text-yellow-400 text-sm"></i>
              </div>
              Job Vehicle
            </h3>

            <div className="text-white text-sm capitalize pl-9">{vehicle.job}</div>
          </div>
        )}
      </div>

      {/* Track button - fixed at bottom */}
      <div className="p-4">
        <button
          className="w-full backdrop-blur-md bg-white/10 hover:bg-white/20 text-white py-2.5 rounded-xl transition-colors flex items-center justify-center border border-white/20"
          onClick={() => onTrack(vehicle.id)}
        >
          <i className="fas fa-location-arrow mr-2"></i>
          Track Vehicle
        </button>
      </div>
    </motion.div>
  );
};

export default VehicleDetail;
