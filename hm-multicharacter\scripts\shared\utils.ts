/**
 * Utility functions for the multicharacter system
 */

/**
 * Generates a unique citizen ID based on first name, last name, and a random component
 * Format: [First Initial][Last Initial]-[Last 2 digits of current year][Random 4-digit number]
 * Example: JS-23XXXX where XXXX is a random 4-digit number
 *
 * @param firstName First name of the character
 * @param lastName Last name of the character
 * @param _ Unused parameter (kept for compatibility)
 * @returns A unique state ID string
 */
export function generateStateId(firstName: string, lastName: string, _: string): string {
    // Get first initials
    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    // Get current year's last two digits
    const yearSuffix = new Date().getFullYear().toString().slice(-2);

    // Generate random 4-digit number
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    // Combine to create citizen ID
    return `${firstInitial}${lastInitial}-${yearSuffix}${random}`;
}

/**
 * Validates a citizen ID format
 *
 * @param stateid The citizen ID to validate
 * @returns True if the state ID is valid, false otherwise
 */
export function isValidStateId(stateid: string): boolean {
    // State ID should match the format: XX-YYNNNN
    // Where XX are two uppercase letters, YY are two digits (year), and NNNN are four digits (random)
    const regex = /^[A-Z]{2}-\d{6}$/;
    return regex.test(stateid);
}
