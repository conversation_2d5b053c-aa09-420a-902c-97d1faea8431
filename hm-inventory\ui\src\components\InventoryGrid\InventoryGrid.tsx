import * as SharedTypes from '@shared';
import { PanelType} from '../../stores/inventoryStore';
import React from 'react';
import Slot from './Slot';

interface InventoryGridProps {
  columns: number;
  visibleRows?: number;
  PanelType: PanelType;
  instanceId?: string;
  items?: SharedTypes.ItemSlot[];
  slotCount?: number; // Total number of slots to show
  highlightRequiredItems?: SharedTypes.RecipeIngredient[];
}

const InventoryGrid: React.FC<InventoryGridProps> = ({
  columns,
  visibleRows,
  PanelType,
  instanceId,
  items,
  slotCount,
  highlightRequiredItems = [],
}) => {
  // Calculate total slots based on slotCount if provided, else fallback to columns (for 1 row if nothing else is provided)
  const totalSlots = slotCount !== undefined ? slotCount : columns;
  const numRows = Math.ceil(totalSlots / columns);
  const actualVisibleRows = visibleRows || numRows;

  // Create array of all slots with proper indices
  const gridSlots: (SharedTypes.ItemSlot | null)[] = [];
  
  // Initialize all slots as empty
  for (let i = 0; i < totalSlots; i++) {
    gridSlots.push(null);
  }

  if (items && items.length > 0) {
    items.forEach((item) => {
      // Check if the item has a valid index and is within bounds
      if (item && typeof item.index === 'number' && item.index >= 0 && item.index < totalSlots) {
        gridSlots[item.index] = item;
      }
    });
  }
  // Helper function to check if an item is a required ingredient and get required quantity
  const getIngredientStatus = (item: SharedTypes.InventoryItem | undefined, currentQuantity: number = 0): { isRequired: boolean; hasEnough: boolean; requiredQuantity: number } => {
    if (!item || !highlightRequiredItems || highlightRequiredItems.length === 0) {
      return { isRequired: false, hasEnough: true, requiredQuantity: 0 };
    }
    
    const ingredient = highlightRequiredItems.find(ing => ing.itemId === item.id);
    if (!ingredient) {
      return { isRequired: false, hasEnough: true, requiredQuantity: 0 };
    }
    
    return {
      isRequired: true,
      hasEnough: currentQuantity >= ingredient.quantity,
      requiredQuantity: ingredient.quantity
    };
  };

  const SLOT_HEIGHT_REM = 5.5;
  const GAP_REM = 0.5;
    const containerStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateRows: `repeat(${actualVisibleRows}, ${SLOT_HEIGHT_REM}rem)`,
    gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
    gap: `${GAP_REM}rem`,
    height: numRows < actualVisibleRows
      ? `calc(${numRows} * ${SLOT_HEIGHT_REM}rem + ${(numRows - 1) * GAP_REM}rem)`
      : `calc(${actualVisibleRows} * ${SLOT_HEIGHT_REM}rem + ${(actualVisibleRows - 1) * GAP_REM}rem)`,
    overflowY: numRows > actualVisibleRows ? 'auto' : 'hidden',    overflowX: 'hidden',
    background: 'transparent',
    padding: 0, // Remove padding to prevent border conflicts
    borderRadius: 0, // Remove border radius to avoid conflicts
  };
  return (
    <div
      style={containerStyle}
      className="inventory-grid-container"
    >{gridSlots.map((slot, index) => {
         const ingredientStatus = getIngredientStatus(slot?.item, slot?.quantity || 0);         // Map PanelType to proper panelType with proper typing

         return (           <Slot
             key={`${PanelType}-${instanceId || 'default'}-${index}`}
             panelType={PanelType}
             panelId={instanceId}
             slotIndex={index}
             inventoryItem={slot?.item}
             quantity={slot?.quantity}
             isRequiredIngredient={ingredientStatus.isRequired}
             hasEnoughQuantity={ingredientStatus.hasEnough}
             requiredQuantity={ingredientStatus.requiredQuantity}
           />
         );
       })}
    </div>
  );
};
export default InventoryGrid;
