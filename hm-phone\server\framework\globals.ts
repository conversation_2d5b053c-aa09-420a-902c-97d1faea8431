/**
 * Server-side Framework Globals
 *
 * This file declares and initializes global variables and functions for the framework integration
 * on the server side. These globals make it easy to access framework functionality from any script.
 */

import { IFramework, FrameworkPlayer } from '../../shared/framework/types';
import { getFramework, getFrameworkName, isFramework } from '../../shared/framework';

/**
 * Declare global variables and functions
 */
declare global {
    // Framework information
    var Framework: IFramework;
    var frameworkName: string;

    // Framework type check functions
    var isHmCore: () => boolean;

    // Server-side player data utility functions
    var getServerPlayerData: (source: number) => FrameworkPlayer | null;
    var getPlayerByIdentifier: (identifier: string) => FrameworkPlayer | null;
    var getPlayerByPhoneNumber: (phoneNumber: string) => FrameworkPlayer | null;
    var getServerPlayerIdentifier: (source: number) => string | null;
    var getServerPlayerPhoneNumber: (source: number) => string | null;
    var getServerPlayerMoney: (source: number, type?: string) => number;

    // Server-side framework utility functions
    var notifyServerPlayer: (source: number, message: string, type?: string, duration?: number) => void;
    var hasServerPermission: (source: number, permission: string) => boolean;
    var addMoney: (source: number, amount: number, type?: string) => boolean;
    var removeMoney: (source: number, amount: number, type?: string) => boolean;
}

/**
 * Initialize server-side framework globals
 * This should be called when the framework is detected
 */
export function initializeServerFrameworkGlobals(): void {
    // Set framework information
    global.Framework = getFramework();
    global.frameworkName = getFrameworkName();

    // Set framework type check functions
    global.isHmCore = () => isFramework('hm-core');

    // Set server-side player data utility functions
    global.getServerPlayerData = (source: number) => {
        return Framework.getPlayer(source);
    };

    global.getPlayerByIdentifier = (identifier: string) => {
        return Framework.getPlayerByIdentifier(identifier);
    };

    global.getPlayerByPhoneNumber = (phoneNumber: string) => {
        return Framework.getPlayerByPhoneNumber(phoneNumber);
    };

    global.getServerPlayerIdentifier = (source: number) => {
        return Framework.getPlayerIdentifier(source);
    };

    global.getServerPlayerPhoneNumber = (source: number) => {
        return Framework.getPlayerPhoneNumber(source);
    };

    global.getServerPlayerMoney = (source: number, type = 'cash') => {
        return Framework.getMoney(source, type);
    };

    // Set server-side framework utility functions
    global.notifyServerPlayer = (source: number, message: string, type = 'info', duration = 5000) => {
        Framework.notify(source, message, type, duration);
    };

    global.hasServerPermission = (source: number, permission: string) => {
        return Framework.hasPermission(source, permission);
    };

    global.addMoney = (source: number, amount: number, type = 'cash') => {
        return Framework.addMoney(source, amount, type);
    };

    global.removeMoney = (source: number, amount: number, type = 'cash') => {
        return Framework.removeMoney(source, amount, type);
    };

}

/**
 * Reset server-side framework globals
 * This should be called when the resource stops
 */
export function resetServerFrameworkGlobals(): void {

    // Reset framework information
    global.Framework = null as any;
    global.frameworkName = '';

}
