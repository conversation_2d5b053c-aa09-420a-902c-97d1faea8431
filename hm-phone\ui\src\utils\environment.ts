/**
 * Environment utilities
 *
 * Provides utilities for detecting the environment and platform
 */

/**
 * Check if we're in a browser environment (not in FiveM)
 */
export const isBrowser = !(window as Window & { invokeNative?: unknown }).invokeNative;

/**
 * Check if we're in a development environment
 */
export const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Check if we're in a production environment
 */
export const isProduction = process.env.NODE_ENV === 'production';

/**
 * Check if we're in a test environment
 */
export const isTest = process.env.NODE_ENV === 'test';
