import React, { memo } from 'react';
import { useBankingStore } from '../store/bankingStore';

const AccountSelector: React.FC = memo(() => {
  const accounts = useBankingStore((state) => state.accounts);
  const selectedAccountId = useBankingStore((state) => state.selectedAccountId);
  const selectAccount = useBankingStore((state) => state.selectAccount);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'personal':
        return 'fa-user';
      case 'savings':
        return 'fa-piggy-bank';
      case 'business':
        return 'fa-briefcase';
      default:
        return 'fa-credit-card';
    }
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'personal':
        return 'text-blue-400';
      case 'savings':
        return 'text-green-400';
      case 'business':
        return 'text-orange-400';
      default:
        return 'text-neutral-400';
    }
  };

  const handleAccountSelect = (accountId: string) => {
    // Prevent re-selection of already selected account
    if (selectedAccountId === accountId) {
      return;
    }
    selectAccount(accountId);
  };

  if (accounts.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-12 h-12 bg-neutral-700/50 rounded-full flex items-center justify-center mx-auto mb-3 border border-neutral-600/30">
          <i className="fas fa-credit-card text-neutral-400" />
        </div>
        <p className="text-neutral-400 text-sm">No accounts found</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {accounts.map((account) => {
        const isSelected = selectedAccountId === account.accountId;
        const isDefault = account.isDefault;
        
        return (          <button
            key={account.accountId}
            onClick={() => handleAccountSelect(account.accountId)}
            className={`w-full p-2.5 rounded-lg border transition-all text-left relative overflow-hidden group ${
              isSelected
                ? 'bg-green-500/10 border-green-500/30 border-l-4 border-l-green-400 cursor-default'
                : 'bg-neutral-700/30 border-neutral-600/30 hover:bg-neutral-700/50 hover:border-neutral-500/50 cursor-pointer'
            }`}
          >
            {/* Subtle gradient overlay for selected account */}
            {isSelected && (
              <div className="absolute inset-0 bg-gradient-to-r from-green-400/5 via-transparent to-transparent pointer-events-none" />
            )}
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-1.5">
                <div className="flex items-center gap-2">
                  <i className={`fas ${getAccountTypeIcon(account.accountType)} ${getAccountTypeColor(account.accountType)} text-xs`} />
                  <span className="text-xs font-medium text-neutral-300 uppercase tracking-wide">
                    {account.accountType}
                  </span>
                  {isDefault && (
                    <span className="bg-green-500/20 text-green-400 text-xs px-1 py-0.5 rounded border border-green-500/30">
                      DEFAULT
                    </span>
                  )}
                </div>
              </div>
              
              <div className="text-neutral-200 font-medium text-xs mb-1">
                {account.accountNumber}
              </div>
              
              <div className="text-neutral-100 font-semibold text-sm">
                {formatCurrency(account.balance)}
              </div>
            </div>

            {/* Hover effect */}
            <div className={`absolute inset-0 bg-gradient-to-r from-green-400/0 via-green-400/5 to-green-400/0 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none ${isSelected ? 'hidden' : ''}`} />
          </button>
        );
      })}    </div>
  );
});

AccountSelector.displayName = 'AccountSelector';

export default AccountSelector;
