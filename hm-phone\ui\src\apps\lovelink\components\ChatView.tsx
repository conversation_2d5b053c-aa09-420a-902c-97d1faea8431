import React, { useState, useRef, useEffect } from 'react';
import { useLoveLinkStore } from '../stores/loveLinkStore';

interface ChatViewProps {
  matchId: number;
  onBack: () => void;
}

const ChatView: React.FC<ChatViewProps> = ({ matchId, onBack }) => {
  const { matches } = useLoveLinkStore();
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const match = matches.find(m => m.id === matchId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, []);

  const handleSend = () => {
    if (!messageText.trim()) return;
    // sendMessage(matchId, messageText);
    setMessageText('');
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center p-4 border-b border-white/10">
        <button onClick={onBack} className="text-white/80 hover:text-white mr-3">
          <i className="fas fa-arrow-left"></i>
        </button>
        <div
          className="w-8 h-8 rounded-full bg-cover bg-center mr-2"
          style={{
            backgroundImage: `url(${
              useLoveLinkStore.getState().profiles.find(p => p.id === match?.matchedId)
                ?.photos[0] || 'default-avatar.png'
            })`
          }}
        />
        <h2 className="text-white font-medium">
          {useLoveLinkStore.getState().profiles.find(p => p.id === match?.matchedId)?.name}
        </h2>
      </div>

      {/* Messages */}
      {/* <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map(message => (
            <div
              key={message.id}
              className={`flex ${message.senderId === 0 ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[70%] rounded-lg px-4 py-2 ${
                  message.senderId === 0
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/10 text-white'
                }`}
              >
                <p>{message.text}</p>
                <p className="text-xs opacity-60 mt-1">
                  {new Date(message.timestamp).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div> */}

      {/* Message Input */}
      <div className="p-4 border-t border-white/10">
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={messageText}
            onChange={e => setMessageText(e.target.value)}
            onKeyPress={e => e.key === 'Enter' && handleSend()}
            placeholder="Type a message..."
            className="flex-1 bg-white/10 text-white rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          />
          <button
            onClick={handleSend}
            className="w-10 h-10 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-colors"
          >
            <i className="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatView;
