import React, { useCallback, useEffect, useState } from 'react';
// Navigation is now handled by the navigation system
import { useContactsStore } from './stores/contactsStore';
import { useNavigation } from '../../navigation/hooks';
import { useNavigationStore } from '../../navigation/navigationStore';
import ContactProfileView from './components/ContactProfileView';
import AddContactView from './components/AddContactView';
import EditContactView from './components/EditContactView';
import { useSelectionStore } from '../../common/stores/selectionStore';
import ContactsBottomNav from './components/ContactsBottomNav';
import ContactsTab from './components/ContactsTab';
import FavoritesTab from './components/FavoritesTab';
import CallsTab from './components/CallsTab';
import DialerTab from './components/DialerTab';
import ProfileTab from './components/ProfileTab';
import CallView from './components/CallView';
import { useDialerStore } from './stores/dialerStore';
import { usePhoneStore } from '../../common/stores/phoneStateStore';
import { useMessagesStore } from '../messages/stores/messagesStore';
import MessageModal from '../messages/components/MessageModal';
import { Contact } from '@shared/types';

const Contacts: React.FC = () => {
  const store = useContactsStore();
  const { contacts, searchTerm, loading, error, selectedContacts } = store;
  const { goBack } = useNavigation();

  // Get the dialer store for call handling
  const { currentCall, actions: dialerActions } = useDialerStore();

  // Use contacts from the store
  const displayContacts = contacts;
  // Use loading state from the store
  const isLoading = loading;
  // Use error state from the store
  const displayError = error;

  const { openAppView, openView, switchTab, goBackWithResult } = useNavigation();
  const { currentView, currentTab } = useNavigationStore();
  const { active: selectionActive, endSelection, cancelSelection } = useSelectionStore();

  // State for message modal
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedMessageContact, setSelectedMessageContact] = useState<Contact | null>(null);

  // Set default tab if none is selected
  const activeTab = currentTab || 'contacts';

  // No longer explicitly setting the tab - we'll rely on the default value
  // and the renderContent function's default case

  // Get the current navigation entry and its data
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};

  // Get contact ID from navigation data
  const contactId = navigationData.id ? String(navigationData.id) : undefined;

  // Find selected contact from navigation data
  const selectedContact = contactId ? displayContacts?.find(c => c.id === Number(contactId)) : null;

  // No need to sync view mode with URL params anymore - the navigation system handles this

  const handleContactClick = useCallback(
    (contact: Contact) => {
      // console.log('Contact clicked:', contact);
      openAppView('contacts', 'detail', { id: contact.id });
    },
    [openAppView]
  );

  const handleFavoriteToggle = useCallback(
    (contactId: number) => {
      store.actions.toggleFavorite(contactId);
    },
    [store.actions]
  );

  const handleShareContact = useCallback(
    (contact: Contact, targetPlayerId: number) => {
      store.actions.shareContact(contact, targetPlayerId);
    },
    [store.actions]
  );

  const handleDeleteContact = useCallback(
    (contactId: number) => {
      console.log('[Contacts] Deleting contact with ID:', contactId);
      store.actions.deleteContact(contactId);
      // Navigate back to the main view after deletion
      openView('main');
    },
    [store.actions, openView]
  );

  // Use useEffect for logging contact changes
  useEffect(() => {
    if (contactId && selectedContact) {
      console.log('Contact selected:', selectedContact);
    }
  }, [contactId, selectedContact]);

  // Load calls when the component mounts
  useEffect(() => {
    console.log('[Contacts] Loading calls...');
    store.actions.getCalls();
  }, [store.actions]);

  // Contacts are now loaded during phone initialization
  // No need to load them again when the component mounts

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'contacts':
        return (
          <ContactsTab
            contacts={displayContacts || []}
            searchTerm={searchTerm}
            onContactSelect={handleContactClick}
            onCallClick={contact => {
              // Make the call directly using the dialer store
              dialerActions.makeCall(contact.number, contact.name, contact.avatar || '');
            }}
            onMessageClick={contact => {
              // Create a conversation with this contact
              if (!contact || !contact.name || !contact.number) {
                console.error('Invalid contact data:', contact);
                return;
              }

              // Handle message icon click
              handleMessageClick(contact);
            }}
          />
        );
      case 'favorites':
        return (
          <FavoritesTab
            contacts={displayContacts || []}
            onContactSelect={handleContactClick}
            onCallClick={contact => {
              // Make the call directly using the dialer store
              dialerActions.makeCall(contact.number, contact.name, contact.avatar || '');
            }}
            onMessageClick={contact => {
              // Create a conversation with this contact
              if (!contact || !contact.name || !contact.number) {
                console.error('Invalid contact data:', contact);
                return;
              }

              // Handle message icon click
              handleMessageClick(contact);
            }}
          />
        );
      case 'calls':
        return <CallsTab />;
      case 'dialer':
        return <DialerTab />;
      case 'profile':
        return <ProfileTab />;
      default:
        return (
          <ContactsTab
            contacts={displayContacts || []}
            searchTerm={searchTerm}
            onContactSelect={handleContactClick}
            onCallClick={contact => {
              // Make the call directly using the dialer store
              dialerActions.makeCall(contact.number, contact.name, contact.avatar || '');
            }}
            onMessageClick={contact => {
              // Create a conversation with this contact

              if (!contact || !contact.name || !contact.number) {
                console.error('Invalid contact data:', contact);
                return;
              }

              // Handle message icon click
              handleMessageClick(contact);
            }}
          />
        );
    }
  };

  const handleSelectionDone = () => {
    // Get regular contacts from the display contacts list
    const selectedRegularContacts =
      displayContacts?.filter(c => c.id !== undefined && selectedContacts.includes(c.id)) || [];

    // Check if the user's own contact (ID 0) is selected
    const isMyContactSelected = selectedContacts.includes(0);

    // If the user's own contact is selected, create a contact object for it
    const selectedContactsList = [...selectedRegularContacts];

    if (isMyContactSelected) {
      const { userProfile } = usePhoneStore.getState();
      const myContact = {
        id: 0,
        number: userProfile.phoneNumber || '',
        name: userProfile.name || 'My Profile',
        favorite: 1,
        avatar: userProfile.imageUrl || null,
        created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
        updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
        identifier: userProfile.identifier || '',
        stateid: userProfile.stateid || ''
      };
      // Only add if not already in the list
      if (!selectedContactsList.some(c => c.id === 0)) {
        selectedContactsList.unshift(myContact);
      }
    }

    console.log('[Contacts] Selection done with contacts:', selectedContactsList);

    // Map contacts to SelectedItem shape before passing to endSelection
    const selectedItems = selectedContactsList.map(contact => ({
      id: contact.id,
      name: contact.name,
      phone: contact.number,
      photo: contact.avatar
      // Add other properties required by SelectedItem if necessary
    }));
    endSelection(selectedItems);

    // Then explicitly navigate back with the result
    setTimeout(() => {
      console.log('[Contacts] Calling goBackWithResult with selected contacts');
      goBackWithResult(selectedContactsList);
    }, 100);
  };

  const handleSelectionCancel = () => {
    // First call cancelSelection to reset the selection state
    cancelSelection();

    // Then explicitly navigate back without a result
    // This ensures we return to the conversation view
    setTimeout(() => {
      console.log('[Contacts] Cancelling selection, navigating back');
      goBackWithResult([]);
    }, 100);
  };

  // Function to handle clicking the message icon
  const handleMessageClick = (contact: Contact) => {
    // Check if there's an existing conversation
    const { conversations } = useMessagesStore.getState();
    let existingConversation = null;

    for (const conversation of conversations) {
      if (conversation.type !== 'group' && Object.keys(conversation.members).includes(contact.number)) {
        existingConversation = conversation;
        break;
      }
    }

    if (existingConversation) {
      // If there's an existing conversation, navigate to it
      openAppView('messages', 'conversation', { id: existingConversation.id });
    } else {
      // Otherwise, show the message modal
      setSelectedMessageContact(contact);
      setShowMessageModal(true);
    }
  };

  // If there's an active call, show the call view
  if (currentCall) {
    return (
      <CallView
        call={currentCall}
        onEndCall={dialerActions.endCall}
        onToggleMute={dialerActions.toggleMute}
        onToggleSpeaker={dialerActions.toggleSpeaker}
        onMinimize={() => {
          // Set a flag to indicate the call is minimized
          localStorage.setItem('callMinimized', 'true');
          // Navigate to the contacts app main view
          openView('main');
        }}
      />
    );
  }

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 relative">
      {/* Error message */}
      {displayError && (
        <div className="bg-red-500/20 text-red-100 px-4 py-2 mb-2 mx-2 rounded">
          <p>{displayError}</p>
          <button onClick={() => store.actions.getContacts()} className="text-white underline mt-1">
            Try again
          </button>
        </div>
      )}

      {/* Selection mode header */}
      {selectionActive && (
        <div className="flex justify-between items-center px-4 py-2 bg-[#0a0f1a] border-b border-white/10">
          <button onClick={handleSelectionCancel} className="text-white/80 hover:text-white">
            Cancel
          </button>
          <span className="text-white/60 text-sm">{selectedContacts.length} selected</span>
          <button
            onClick={handleSelectionDone}
            disabled={selectedContacts.length === 0}
            className="text-blue-500 hover:text-blue-400 disabled:opacity-50"
          >
            Done
          </button>
        </div>
      )}

      {/* Content */}
      {currentView === 'detail' && selectedContact ? (
        <ContactProfileView
          contact={selectedContact}
          onBack={goBack}
          onEdit={() => openView('edit', { id: selectedContact.id })}
          onFavoriteToggle={handleFavoriteToggle}
          onShare={playerId => handleShareContact(selectedContact, playerId)}
          onDelete={handleDeleteContact}
          onCall={contact => {
            // Make the call directly using the dialer store
            dialerActions.makeCall(contact.number, contact.name, contact.avatar || '');
          }}
          onMessage={handleMessageClick}
        />
      ) : currentView === 'add' ? (
        <AddContactView
          onAddContact={(newContact: Contact) => {
            store.actions.addContact(newContact);
            openView('main');
          }}
          isSubmitting={loading}
          onCancel={() => openView('main')}
          initialData={navigationData as { phone?: string; name?: string }}
        />
      ) : currentView === 'edit' && selectedContact ? (
        <EditContactView
          contact={selectedContact}
          onUpdateContact={(updatedContact: Contact) => {
            // First update the contact in the store
            store.actions.updateContact(updatedContact);
            // Use goBack() to properly navigate back in the history stack
            goBack();
          }}
          onCancel={() => goBack()}
        />
      ) : isLoading ? (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
        </div>
      ) : (
        <>
          <div className="flex-1 overflow-hidden min-h-0">{renderContent()}</div>
          <ContactsBottomNav activeTab={activeTab} switchTab={switchTab} className="flex-shrink-0" />

          {/* Pull indicator padding */}
          <div className="h-6 bg-[#0a0f1a] flex-shrink-0"></div>
        </>
      )}

      {/* Message Modal */}
      {showMessageModal && selectedMessageContact && (
        <MessageModal
          phoneNumber={selectedMessageContact.number}
          name={selectedMessageContact.name}
          onClose={() => {
            setShowMessageModal(false);
            setSelectedMessageContact(null);
          }}
        />
      )}
    </div>
  );
};

export default React.memo(Contacts);
