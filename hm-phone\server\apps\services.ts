/**
 * Services App - Server Side
 *
 * This file handles server-side functionality for the Services app.
 */

import config from '@shared/config';

// Extract services config for easier access
const { services: servicesConfig } = config;

/**
 * Initialize the services app
 */
export function initializeServicesApp(): void {
    // Register server events
    registerServerEvents();
}

/**
 * Register server events for the services app
 */
function registerServerEvents(): void {
    // Register event for getting services
    onNet('hm-phone:getServices', async () => {
        const source = global.source;
        console.log(`[Services] Received getServices event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Services] Player ${source} not found`);
                emitNet('hm-phone:servicesError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Services] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:servicesError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            player.identifier = stateid || identifier;

            // Get services for the player
            const services = await getServices(player);

            // Send the services back to the client
            emitNet('hm-phone:services', source, services);
        } catch (error) {
            console.error('[Services] Error getting services:', error);
            emitNet('hm-phone:servicesError', source, 'Failed to get services');
        }
    });

    // Register event for requesting a service
    onNet('hm-phone:requestService', async (serviceId: string, message: string, location: any) => {
        const source = global.source;
        console.log(`[Services] Received requestService event from player ${source} for service ${serviceId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Services] Player ${source} not found`);
                emitNet('hm-phone:serviceRequestError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Services] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:serviceRequestError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            player.identifier = stateid || identifier;

            // Request the service
            const result = await requestService(serviceId, player, message, location);

            // Send the result back to the client
            emitNet('hm-phone:serviceRequestResult', source, result);
        } catch (error) {
            console.error('[Services] Error requesting service:', error);
            emitNet('hm-phone:serviceRequestError', source, 'Failed to request service');
        }
    });
}

/**
 * Get services for a player
 * @param player Player data
 * @returns Array of services
 */
async function getServices(player: any): Promise<any[]> {
    console.log(`[Services] Getting services for player ${player.playerIdentifier}`);

    try {
        // Get available services from config
        const availableServices = servicesConfig.availableServices;

        // Convert to the format expected by the UI
        const services = availableServices
            .filter(service => service.enabled)
            .map((service, index) => ({
                id: index + 1, // Use index + 1 as ID
                name: service.name,
                category: getServiceCategory(service.job),
                phone: getServicePhone(service.job),
                description: service.description,
                location: '', // Empty for now
                icon: service.icon,
                color: service.color
            }));

        return services;
    } catch (error) {
        console.error('[Services] Error getting services:', error);
        throw error;
    }
}

/**
 * Request a service
 * @param serviceId Service ID
 * @param player Player data
 * @param message Message from the player
 * @param location Player location
 * @returns Result of the request
 */
async function requestService(serviceId: string, player: any, message: string, location: any): Promise<any> {
    console.log(`[Services] Requesting service ${serviceId} for player ${player.playerIdentifier}`);

    try {
        // Find the service in the config
        const service = servicesConfig.availableServices.find(s => s.id === serviceId);

        if (!service) {
            throw new Error(`Service ${serviceId} not found`);
        }

        // Check if service requests are enabled
        if (!servicesConfig.requests.enabled) {
            throw new Error('Service requests are disabled');
        }

        // Get all online players with the job
        const jobPlayers = getPlayersWithJob(service.job);

        if (jobPlayers.length === 0) {
            return {
                success: false,
                message: `No ${service.name} providers are available right now.`
            };
        }

        // Notify all job players if configured to do so
        if (servicesConfig.requests.notifyAllJobMembers) {
            notifyJobPlayers(jobPlayers, service, player, message, location);
        } else {
            // Notify only the closest job player
            const closestPlayer = getClosestPlayer(jobPlayers, location);
            notifyJobPlayer(closestPlayer, service, player, message, location);
        }

        // Return success
        return {
            success: true,
            message: `Your request has been sent to ${service.name}.`
        };
    } catch (error) {
        console.error('[Services] Error requesting service:', error);
        throw error;
    }
}

/**
 * Get the category for a service based on its job
 * @param job Job name
 * @returns Category name
 */
function getServiceCategory(job: string): string {
    switch (job) {
        case 'police':
        case 'ambulance':
            return 'Emergency';
        case 'taxi':
            return 'Transportation';
        case 'mechanic':
            return 'Automotive';
        case 'delivery':
            return 'Food';
        default:
            return 'Services';
    }
}

/**
 * Get the phone number for a service based on its job
 * @param job Job name
 * @returns Phone number
 */
function getServicePhone(job: string): string {
    switch (job) {
        case 'police':
            return '911';
        case 'ambulance':
            return '912';
        case 'taxi':
            return '555-TAXI';
        case 'mechanic':
            return '555-MECH';
        case 'delivery':
            return '555-FOOD';
        default:
            return '555-' + job.substring(0, 4).toUpperCase();
    }
}

/**
 * Get all online players with a specific job
 * @param job Job name
 * @returns Array of player IDs
 */
function getPlayersWithJob(job: string): number[] {
    // This is a placeholder implementation
    // In a real implementation, you would query the framework for players with the job
    return [];
}

/**
 * Get the closest player to a location
 * @param players Array of player IDs
 * @param location Location to check against
 * @returns The closest player ID
 */
function getClosestPlayer(players: number[], location: any): number {
    // This is a placeholder implementation
    // In a real implementation, you would calculate distances and find the closest player
    return players[0];
}

/**
 * Notify all job players about a service request
 * @param players Array of player IDs
 * @param service Service data
 * @param requester Player data
 * @param message Message from the player
 * @param location Player location
 */
function notifyJobPlayers(players: number[], service: any, requester: any, message: string, location: any): void {
    players.forEach(playerId => {
        notifyJobPlayer(playerId, service, requester, message, location);
    });
}

/**
 * Notify a job player about a service request
 * @param playerId Player ID
 * @param service Service data
 * @param requester Player data
 * @param message Message from the player
 * @param location Player location
 */
function notifyJobPlayer(playerId: number, service: any, requester: any, message: string, location: any): void {
    // Send notification to the player
    emitNet('hm-phone:serviceRequest', playerId, {
        service: service.name,
        requester: requester.name,
        message,
        location: servicesConfig.requests.showLocation ? location : null
    });

    // Add a blip on the map if location sharing is enabled
    if (servicesConfig.requests.showLocation) {
        emitNet('hm-phone:addServiceBlip', playerId, {
            service: service.name,
            requester: requester.name,
            location
        });
    }
}
