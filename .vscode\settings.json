{
    "files.exclude": {
        "**/.git": true,
        "**/node_modules": true,
        "**/build": true,
        "**/dist": true,
        "**/hm-multicharacter.zip": true,
        "**/hm-phone.zip": true,
        "**/hm-admin.zip": true,
        "**/hm-rendertargets": true,
        // "**/hm-radio": true,
        "**/hm-carbomb": true,
        "**/archived_lua.zip": true,
    },
    "search.exclude": {
        "**/.git": true,
        "**/node_modules": true,
        "**/build": true,
        "**/dist": true,
        "**/hm-multicharacter.zip": true,
        "**/hm-phone.zip": true,
        "**/hm-admin.zip": true,
        "**/hm-rendertargets": true,
        "**/hm-radio": true,
        "**/hm-carbomb": true,
        "**/archived_lua.zip": true,
    },
    "github.copilot.chat.languageContext.fix.typescript.enabled": true,
    "github.copilot.chat.languageContext.inline.typescript.enabled": true,
    "github.copilot.chat.languageContext.typescript.enabled": true,
    "github.copilot.nextEditSuggestions.fixes": true,
    "github.copilot.nextEditSuggestions.enabled": true,
}