import React, { useMemo, useState } from 'react';
import {
  DndContext,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  DragStartEvent,
  DragEndEvent,
  closestCenter,
} from '@dnd-kit/core';
import {
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import InventoryGrid from './InventoryGrid/InventoryGrid';
import QuickAccessPreview from './QuickAccessBar/QuickAccessPreview';
import QuickAccessConfigPanel from './QuickAccessBar/QuickAccessConfigPanel';
import ActionPanel from './Panels/ActionPanel';
import InventoryToolbar from './UI/InventoryToolbar';
import EnhancedDragOverlay from './UI/DragAnimations';
import { useInventoryStore } from '../stores/inventoryStore';
import ContextualSecondaryPanel from './ContextualSecondaryPanel';
import ToastContainer from './UI/ToastContainer';
import ProgressBarContainer from './UI/ProgressBarContainer';
import PickupPrompt from './UI/PickupPrompt';
import { DragItem, DropItem } from '@shared';
import { executeDropOperation } from '../hooks/useDndKitSlot';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

const MAX_WEIGHT = 40; // Example max weight, adjust as needed

const InventoryScreen: React.FC = () => {
  const isMainVisible = useInventoryStore((state) => state.isMainVisible);
  const isSecondaryPanelVisible = useInventoryStore((state) => state.isSecondaryPanelVisible);
  const isQuickAccessVisible = useInventoryStore((state) => state.isQuickAccessVisible);
  const getIsAnyVisible = useInventoryStore((state) => state.getIsAnyVisible);
  const gridItems = useInventoryStore((state) => state.gridItems);
  
  // Enable keyboard shortcuts
  useKeyboardShortcuts();
  
  // Local state for QuickAccess configuration modal
  const [showQuickAccessConfig, setShowQuickAccessConfig] = useState(false);
  
  // Configure sensors for @dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Minimum distance to start drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const [activeDragItem, setActiveDragItem] = React.useState<DragItem | null>(null);
  
  // Calculate total weight (always run hook)
  const totalWeight = useMemo(() => {
    let weight = 0;
    gridItems.forEach(slot => {
      if (slot.item && slot.item.weight) {
        weight += (slot.quantity || 1) * slot.item.weight;
      }
    });
    return weight / 1000; // Convert to kg if item.weight is in grams
  }, [gridItems]);

  const handleDragStart = (event: DragStartEvent) => {
    const dragData = event.active.data.current as DragItem;
    setActiveDragItem(dragData);
    
    // Update store drag state
    useInventoryStore.getState().setDragging(true);
  };  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    setActiveDragItem(null);
    useInventoryStore.getState().setDragging(false);

    if (!over) return;    const dragData = active.data.current as DragItem;
    const dropData = over.data.current as DropItem;

    if (!dragData || !dropData) return;

    // Execute the drop operation (validation is now handled internally)
    try {
      executeDropOperation(dragData, dropData);
      console.log('Drop operation successful:', { dragData, dropData });
    } catch (error) {
      console.error('Drop operation failed:', error);
    }
  };const handleDragOver = () => {
    // Optional: Add drag over logic if needed
  };// Don't render anything if no inventory parts are visible
  if (!getIsAnyVisible()) {
    return null;
  }

  // Layout
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70">
        {/* Toast notifications */}
        <ToastContainer isInventoryOpen={isMainVisible} />
        
        {/* Progress bars for item usage */}
        <ProgressBarContainer isQuickAccessVisible={isQuickAccessVisible} />
        
        <div className="flex items-stretch h-full max-h-[80%] w-full px-4">
          
          {/* Center: Main inventory with action panel integrated */}
          <div className="flex flex-col justify-between">
            {isMainVisible && (
              <div className="flex gap-4">
                {/* Action Panel (Gadgets, specials) - part of main inventory */}
                <div className="flex flex-col justify-between">
                  <ActionPanel />
                </div>
                
                {/* Main inventory grid */}
                <div className="mb-4 p-4 bg-neutral-800 rounded-2xl shadow-xl w-full border-t-4 border-sky-500/20 relative">
                  <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-sky-400/10 via-sky-500/10 to-sky-600/10 opacity-30 z-10 rounded-t-2xl" />                  <h2 className="text-lg font-bold text-neutral-100 tracking-wide flex items-center gap-2 mb-3">
                    <i className="fas fa-suitcase text-blue-400" /> Inventory
                  </h2>
                    {/* Inventory Management Toolbar */}
                  <InventoryToolbar onOpenQuickAccessConfig={() => setShowQuickAccessConfig(true)} />
                  
                  {/* Weight bar */}
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-xs text-gray-400">Weight:</span>
                    <div className="flex-1 h-2 bg-neutral-700 rounded-full">
                      <div 
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full" 
                        style={{ width: `${(totalWeight / MAX_WEIGHT) * 100}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-300">{totalWeight.toFixed(1)} / {MAX_WEIGHT} kg</span>
                  </div>

                  {/* Main grid */}
                  <InventoryGrid 
                    PanelType="main"
                    instanceId="main"
                    items={gridItems}
                    visibleRows={4}
                    columns={5}
                    slotCount={25}
                  />
                </div>
              </div>
            )}
              {/* Quick access preview at the bottom - handles its own visibility */}
            <QuickAccessPreview />
          </div>
          
          {/* Right: Secondary inventory (when active) */}
          {isSecondaryPanelVisible && (
            <div className="flex flex-col justify-between ml-auto h-full">
              <ContextualSecondaryPanel />
            </div>
          )}
        </div>        {/* Enhanced Drag Overlay for visual feedback */}
        <EnhancedDragOverlay activeDragItem={activeDragItem} />
        
        {/* QuickAccess Configuration Modal */}
        {showQuickAccessConfig && (
          <QuickAccessConfigPanel onClose={() => setShowQuickAccessConfig(false)} />
        )}
        
        {/* Pickup Prompt */}
        <PickupPrompt />
      </div>
    </DndContext>
  );
  
};

export default InventoryScreen;
