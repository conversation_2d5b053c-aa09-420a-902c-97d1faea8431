import { AttachmentPoint, AttachmentItem, WeaponData } from '@/stores/weaponModdingStore';
import { isBrowserEnv } from './environment';

/**
 * Mock attachment items for testing
 */
const mockAttachmentItems: Record<string, AttachmentItem[]> = {
  WAPClip: [
    {
      id: 'COMPONENT_ASSAULTRIFLE_CLIP_01',
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_01',
      label: 'Default Clip',
      category: 'magazine',
      icon: 'weapon_assaultrifle_clip',
      description: 'Standard 30-round magazine',
      stats: { damage: 0, accuracy: 0, range: 0, stability: 0, concealment: 0 },
      rarity: 'common',
      attachBone: 'WAPClip',
      price: 0
    },
    {
      id: 'COMPONENT_ASSAULTRIFLE_CLIP_02',
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_02',
      label: 'Extended Clip',
      category: 'magazine',
      icon: 'weapon_assaultrifle_clip_extended',
      description: 'Extended 60-round magazine',
      stats: { damage: 0, accuracy: -5, range: 0, stability: -10, concealment: -15 },
      rarity: 'uncommon',
      attachBone: 'WAPClip',
      price: 150
    },
    {
      id: 'COMPONENT_ASSAULTRIFLE_CLIP_03',
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_03',
      label: 'Drum Magazine',
      category: 'magazine',
      icon: 'weapon_assaultrifle_clip_drum',
      description: 'High-capacity 100-round drum magazine',
      stats: { damage: 0, accuracy: -10, range: 0, stability: -20, concealment: -25 },
      rarity: 'rare',
      attachBone: 'WAPClip',
      price: 300
    }
  ],
  WAPScop: [
    {
      id: 'COMPONENT_AT_SCOPE_MACRO',
      name: 'COMPONENT_AT_SCOPE_MACRO',
      label: 'Macro Scope',
      category: 'optics',
      icon: 'weapon_scope_macro',
      description: 'Medium-range tactical scope',
      stats: { damage: 0, accuracy: 15, range: 20, stability: 0, concealment: -10 },
      rarity: 'rare',
      attachBone: 'WAPScop',
      price: 300
    },
    {
      id: 'COMPONENT_AT_SCOPE_LARGE',
      name: 'COMPONENT_AT_SCOPE_LARGE',
      label: 'Large Scope',
      category: 'optics',
      icon: 'weapon_scope_large',
      description: 'Long-range precision scope',
      stats: { damage: 0, accuracy: 25, range: 35, stability: 0, concealment: -20 },
      rarity: 'epic',
      attachBone: 'WAPScop',
      price: 500
    },
    {
      id: 'COMPONENT_AT_SCOPE_SMALL',
      name: 'COMPONENT_AT_SCOPE_SMALL',
      label: 'Red Dot Sight',
      category: 'optics',
      icon: 'weapon_scope_red_dot',
      description: 'Close-range red dot sight',
      stats: { damage: 0, accuracy: 8, range: 5, stability: 0, concealment: -5 },
      rarity: 'uncommon',
      attachBone: 'WAPScop',
      price: 200
    }
  ],
  WAPSupp: [
    {
      id: 'COMPONENT_AT_AR_SUPP_02',
      name: 'COMPONENT_AT_AR_SUPP_02',
      label: 'Suppressor',
      category: 'muzzle',
      icon: 'weapon_suppressor',
      description: 'Sound suppressor for stealth operations',
      stats: { damage: -5, accuracy: 5, range: -10, stability: 10, concealment: 25 },
      rarity: 'rare',
      attachBone: 'WAPSupp',
      price: 400
    },
    {
      id: 'COMPONENT_AT_MUZZLE_01',
      name: 'COMPONENT_AT_MUZZLE_01',
      label: 'Compensator',
      category: 'muzzle',
      icon: 'weapon_compensator',
      description: 'Reduces muzzle climb and recoil',
      stats: { damage: 0, accuracy: 10, range: 0, stability: 15, concealment: -10 },
      rarity: 'uncommon',
      attachBone: 'WAPSupp',
      price: 250
    }
  ],
  WAPFlshLasr: [
    {
      id: 'COMPONENT_AT_AR_FLSH',
      name: 'COMPONENT_AT_AR_FLSH',
      label: 'Flashlight',
      category: 'tactical',
      icon: 'weapon_flashlight',
      description: 'Tactical flashlight attachment',
      stats: { damage: 0, accuracy: 5, range: 0, stability: 0, concealment: -5 },
      rarity: 'common',
      attachBone: 'WAPFlshLasr',
      price: 100
    },
    {
      id: 'COMPONENT_AT_PI_FLSH_02',
      name: 'COMPONENT_AT_PI_FLSH_02',
      label: 'Laser Sight',
      category: 'tactical',
      icon: 'weapon_laser',
      description: 'Red dot laser sight for improved accuracy',
      stats: { damage: 0, accuracy: 10, range: 0, stability: 5, concealment: -10 },
      rarity: 'uncommon',
      attachBone: 'WAPFlshLasr',
      price: 200
    },
    {
      id: 'COMPONENT_AT_PI_FLSH_03',
      name: 'COMPONENT_AT_PI_FLSH_03',
      label: 'Tactical Combo',
      category: 'tactical',
      icon: 'weapon_tactical_combo',
      description: 'Combined flashlight and laser sight',
      stats: { damage: 0, accuracy: 12, range: 0, stability: 8, concealment: -15 },
      rarity: 'rare',
      attachBone: 'WAPFlshLasr',
      price: 350
    }
  ],
  WAPGrip: [
    {
      id: 'COMPONENT_AT_AR_AFGRIP',
      name: 'COMPONENT_AT_AR_AFGRIP',
      label: 'Angled Foregrip',
      category: 'grip',
      icon: 'weapon_grip_angled',
      description: 'Angled foregrip for better control',
      stats: { damage: 0, accuracy: 5, range: 0, stability: 15, concealment: -5 },
      rarity: 'uncommon',
      attachBone: 'WAPGrip',
      price: 180
    },
    {
      id: 'COMPONENT_AT_AR_GRIP_01',
      name: 'COMPONENT_AT_AR_GRIP_01',
      label: 'Vertical Grip',
      category: 'grip',
      icon: 'weapon_grip_vertical',
      description: 'Vertical foregrip for enhanced stability',
      stats: { damage: 0, accuracy: 8, range: 0, stability: 20, concealment: -10 },
      rarity: 'rare',
      attachBone: 'WAPGrip',
      price: 220
    }
  ]
};

/**
 * Generate mock attachment points with realistic screen positions
 */
const generateMockAttachmentPoints = (): AttachmentPoint[] => {
  return [    {
      id: 'WAPClip',
      slotType: 'magazine',
      position: { x: '75%', y: '65%' },
      availableMods: mockAttachmentItems.WAPClip || [],
      currentMod: undefined
    },
    {
      id: 'WAPScop',
      slotType: 'optics',
      position: { x: '70%', y: '35%' },
      availableMods: mockAttachmentItems.WAPScop || [],
      currentMod: undefined
    },
    {
      id: 'WAPSupp',
      slotType: 'muzzle',
      position: { x: '15%', y: '45%' },
      availableMods: mockAttachmentItems.WAPSupp || [],
      currentMod: undefined
    },
    {
      id: 'WAPFlshLasr',
      slotType: 'tactical',
      position: { x: '25%', y: '55%' },
      availableMods: mockAttachmentItems.WAPFlshLasr || [],
      currentMod: undefined
    },
    {
      id: 'WAPGrip',
      slotType: 'grip',
      position: { x: '30%', y: '60%' },
      availableMods: mockAttachmentItems.WAPGrip || [],
      currentMod: undefined
    }
  ];
};

/**
 * Mock weapon data for testing
 */
const mockWeaponData: WeaponData = {
  hash: 'WEAPON_ASSAULTRIFLE',
  name: 'weapon_assaultrifle',
  label: 'Assault Rifle',
  components: Object.values(mockAttachmentItems).flat(),
  stats: {
    damage: 75,
    accuracy: 65,
    range: 80,
    stability: 70,
    concealment: 50
  }
};

/**
 * Initialize mock data for browser environment
 */
export const initializeMockData = () => {
  if (!isBrowserEnv()) {
    return; // Only run in browser
  }

  console.log('[WeaponModding Mock] Initializing mock data for browser testing');

  // Simulate opening weapon modding after a short delay
  setTimeout(() => {
    window.postMessage({
      type: 'OPEN_WEAPON_MODDING',
      data: { weaponHash: 'WEAPON_ASSAULTRIFLE' }
    }, '*');
  }, 1000);

  // Simulate updating attachment points
  setTimeout(() => {
    const mockPoints = generateMockAttachmentPoints();
    window.postMessage({
      type: 'UPDATE_ATTACHMENT_POINTS',
      data: mockPoints.map(point => ({
        id: point.id,
        slotType: point.slotType,
        position: point.position,
        availableMods: point.availableMods,
        currentMod: point.currentMod
      }))
    }, '*');
  }, 1500);

  // Simulate updating available mods
  setTimeout(() => {
    window.postMessage({
      type: 'UPDATE_AVAILABLE_MODS',
      data: mockAttachmentItems
    }, '*');
  }, 2000);

  console.log('[WeaponModding Mock] Mock data initialization complete');
};

/**
 * Mock NUI callbacks for browser testing
 */
export const setupMockNUICallbacks = () => {
  if (!isBrowserEnv()) {
    return; // Only run in browser
  }

  console.log('[WeaponModding Mock] Setting up mock NUI callbacks');  // Mock fetch for NUI callbacks
  const originalFetch = window.fetch;
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    const url = typeof input === 'string' ? input : input.toString();
    if (url.startsWith('https://hm-weapons/')) {
      const endpoint = url.replace('https://hm-weapons/', '');
      console.log(`[WeaponModding Mock] Intercepted NUI callback: ${endpoint}`, init?.body);

      // Simulate different responses based on endpoint
      switch (endpoint) {
        case 'applyMod':
          // Simulate successful mod application
          setTimeout(() => {
            const body = init?.body ? JSON.parse(init.body as string) : {};
            window.postMessage({
              type: 'MOD_APPLIED',
              data: { success: true, componentName: body.componentName }
            }, '*');
          }, 500);
          break;

        case 'removeMod':
          // Simulate successful mod removal
          setTimeout(() => {
            const body = init?.body ? JSON.parse(init.body as string) : {};
            window.postMessage({
              type: 'MOD_REMOVED',
              data: { success: true, componentName: body.componentName }
            }, '*');
          }, 500);
          break;

        case 'stopModding':
          // Simulate closing
          setTimeout(() => {
            window.postMessage({
              type: 'CLOSE_WEAPON_MODDING',
              data: {}
            }, '*');
          }, 100);
          break;        case 'adjustFOV':
        case 'rotateWeapon':
        case 'resetWeaponPosition':
        case 'initializePositions':
          // For camera controls, don't update positions - they should remain stable
          // Only acknowledge the action without moving attachment points
          console.log(`[WeaponModding Mock] Camera action: ${endpoint} - positions remain stable`);
          break;

        case 'startModding':
          // Simulate starting modding sequence
          setTimeout(() => {
            window.postMessage({
              type: 'SET_LOADING',
              data: { loading: true }
            }, '*');
          }, 100);

          setTimeout(() => {
            const mockPoints = generateMockAttachmentPoints();
            window.postMessage({
              type: 'UPDATE_ATTACHMENT_POINTS',
              data: mockPoints
            }, '*');
          }, 800);

          setTimeout(() => {
            window.postMessage({
              type: 'UPDATE_AVAILABLE_MODS',
              data: mockAttachmentItems
            }, '*');
          }, 1200);
          break;
      }

      // Return a mock successful response
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // For non-NUI requests, use original fetch
    return originalFetch(input, init);
  };
};

/**
 * Get mock data for specific attachment bone
 */
export const getMockModsForBone = (boneName: string): AttachmentItem[] => {
  return mockAttachmentItems[boneName] || [];
};

/**
 * Get all mock attachment items
 */
export const getAllMockAttachmentItems = (): Record<string, AttachmentItem[]> => {
  return mockAttachmentItems;
};

/**
 * Get mock weapon data
 */
export const getMockWeaponData = (): WeaponData => {
  return mockWeaponData;
};

/**
 * Get mock attachment points
 */
export const getMockAttachmentPoints = (): AttachmentPoint[] => {
  return generateMockAttachmentPoints();
};
