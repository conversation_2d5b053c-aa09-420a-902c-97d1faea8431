/**
 * Yellow Pages App - Server Side
 *
 * This file handles server-side functionality for the Yellow Pages app.
 */

import config from '@shared/config';

// Ad categories
const AD_CATEGORIES = ['BUY', 'SELL', 'SERVICES', 'HIRING', 'EVENTS'];

// Ad price (cost to post an ad)
const AD_PRICE = 50; // $50 per ad

// Ensure global exports object exists
global.exports = global.exports || {};

/**
 * Initialize the Yellow Pages app
 */
export function initializeYellowPagesApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
}

/**
 * Register server events for the Yellow Pages app
 */
function registerServerEvents(): void {
    // Register event for getting Yellow Pages ads
    onNet('hm-phone:getYellowPagesAds', async () => {
        const source = global.source;
        console.log(`[YellowPages] Received getYellowPagesAds event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[YellowPages] Player ${source} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[YellowPages] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:yellowPagesError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get ads
            const ads = await getYellowPagesAds();

            // Send the ads back to the client
            emitNet('hm-phone:yellowPagesAds', source, ads);
        } catch (error) {
            console.error('[YellowPages] Error getting Yellow Pages ads:', error);
            emitNet('hm-phone:yellowPagesError', source, 'Failed to get Yellow Pages ads');
        }
    });

    // Register event for creating an ad
    onNet('hm-phone:createAd', async (adData: any) => {
        const source = global.source;
        console.log(`[YellowPages] Received createAd event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[YellowPages] Player ${source} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[YellowPages] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:yellowPagesError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Validate ad data
            if (!adData.description) {
                console.error(`[YellowPages] Ad description is required`);
                emitNet('hm-phone:yellowPagesError', source, 'Ad description is required');
                return;
            }

            // Check if player has enough money
            if (!hasEnoughMoney(source, AD_PRICE)) {
                console.error(`[YellowPages] Player ${source} doesn't have enough money to post an ad`);
                emitNet('hm-phone:yellowPagesError', source, `You need $${AD_PRICE} to post an ad`);
                return;
            }

            // Charge the player
            if (!removeMoney(source, AD_PRICE, 'Yellow Pages Ad')) {
                console.error(`[YellowPages] Failed to charge player ${source} for ad`);
                emitNet('hm-phone:yellowPagesError', source, 'Failed to charge for ad');
                return;
            }

            // Create the ad
            const ad = await createAd(playerIdentifier, {
                ...adData,
                contactNumber: player.phoneNumber || adData.contactNumber,
                characterName: player.name || adData.characterName,
                timestamp: Date.now()
            });

            // Send the result back to the client
            emitNet('hm-phone:adCreated', source, {
                success: true,
                ad
            });

            // Broadcast the new ad to all players
            broadcastNewAd(ad);
        } catch (error) {
            console.error('[YellowPages] Error creating ad:', error);
            emitNet('hm-phone:yellowPagesError', source, 'Failed to create ad');
        }
    });

    // Register event for updating an ad
    onNet('hm-phone:updateAd', async (adData: any) => {
        const source = global.source;
        console.log(`[YellowPages] Received updateAd event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[YellowPages] Player ${source} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[YellowPages] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:yellowPagesError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Validate ad data
            if (!adData.id) {
                console.error(`[YellowPages] Ad ID is required`);
                emitNet('hm-phone:yellowPagesError', source, 'Ad ID is required');
                return;
            }

            if (!adData.description) {
                console.error(`[YellowPages] Ad description is required`);
                emitNet('hm-phone:yellowPagesError', source, 'Ad description is required');
                return;
            }

            // Check if the ad exists and belongs to the player
            const existingAd = await getAdById(adData.id);
            if (!existingAd) {
                console.error(`[YellowPages] Ad ${adData.id} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Ad not found');
                return;
            }

            // Check if the ad belongs to the player
            if (existingAd.identifier !== playerIdentifier) {
                console.error(`[YellowPages] Ad ${adData.id} doesn't belong to player ${playerIdentifier}`);
                emitNet('hm-phone:yellowPagesError', source, 'You can only update your own ads');
                return;
            }

            // Update the ad
            const updatedAd = await updateAd(adData.id, {
                ...adData,
                contactNumber: player.phoneNumber || adData.contactNumber,
                characterName: player.name || adData.characterName,
                timestamp: Date.now()
            });

            // Send the result back to the client
            emitNet('hm-phone:adUpdated', source, {
                success: true,
                ad: updatedAd
            });

            // Broadcast the updated ad to all players
            broadcastUpdatedAd(updatedAd);
        } catch (error) {
            console.error('[YellowPages] Error updating ad:', error);
            emitNet('hm-phone:yellowPagesError', source, 'Failed to update ad');
        }
    });

    // Register event for deleting an ad
    onNet('hm-phone:deleteAd', async (adId: number) => {
        const source = global.source;
        console.log(`[YellowPages] Received deleteAd event from player ${source} for ad ${adId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[YellowPages] Player ${source} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[YellowPages] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:yellowPagesError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Check if the ad exists and belongs to the player
            const existingAd = await getAdById(adId);
            if (!existingAd) {
                console.error(`[YellowPages] Ad ${adId} not found`);
                emitNet('hm-phone:yellowPagesError', source, 'Ad not found');
                return;
            }

            // Check if the ad belongs to the player
            if (existingAd.identifier !== playerIdentifier) {
                console.error(`[YellowPages] Ad ${adId} doesn't belong to player ${playerIdentifier}`);
                emitNet('hm-phone:yellowPagesError', source, 'You can only delete your own ads');
                return;
            }

            // Delete the ad
            await deleteAd(adId);

            // Send the result back to the client
            emitNet('hm-phone:adDeleted', source, {
                success: true,
                id: adId
            });

            // Broadcast the deleted ad to all players
            broadcastDeletedAd(adId);
        } catch (error) {
            console.error('[YellowPages] Error deleting ad:', error);
            emitNet('hm-phone:yellowPagesError', source, 'Failed to delete ad');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[YellowPages] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[YellowPages] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_yellow_pages table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_yellow_pages (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    description TEXT NOT NULL,
                    price DECIMAL(10, 2),
                    image_url VARCHAR(255),
                    contact_number VARCHAR(255) NOT NULL,
                    character_name VARCHAR(255) NOT NULL,
                    timestamp BIGINT NOT NULL,
                    category VARCHAR(255) NOT NULL,
                    INDEX idx_identifier (identifier),
                    INDEX idx_timestamp (timestamp)
                )
            `,
                [],
                () => {
                    console.log('[YellowPages] Database tables initialized');
                }
            );
        } else {
            console.error('[YellowPages] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[YellowPages] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get all Yellow Pages ads
 * @returns Array of ads
 */
async function getYellowPagesAds(): Promise<any[]> {
    console.log('[YellowPages] Getting Yellow Pages ads');

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let ads = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            ads = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_yellow_pages
                ORDER BY timestamp DESC
                LIMIT 50
            `,
                []
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            ads = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_yellow_pages
                    ORDER BY timestamp DESC
                    LIMIT 50
                `,
                    [],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // If no ads found, return empty array
        if (!ads || ads.length === 0) {
            return [];
        }

        // Format ads for the UI
        return ads.map((ad: any) => ({
            id: ad.id,
            description: ad.description,
            price: ad.price ? parseFloat(ad.price) : undefined,
            imageUrl: ad.image_url,
            contactNumber: ad.contact_number,
            characterName: ad.character_name,
            timestamp: ad.timestamp,
            category: ad.category
        }));
    } catch (error: any) {
        console.error('[YellowPages] Error getting Yellow Pages ads:', error);
        throw new Error(`Failed to get Yellow Pages ads: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Get an ad by ID
 * @param adId Ad ID
 * @returns Ad data or null if not found
 */
async function getAdById(adId: number): Promise<any | null> {
    console.log(`[YellowPages] Getting ad ${adId}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let ad = null;

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            const result = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_yellow_pages
                WHERE id = ?
            `,
                [adId]
            );

            if (result && result.length > 0) {
                ad = result[0];
            }
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            ad = await new Promise<any>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_yellow_pages
                    WHERE id = ?
                `,
                    [adId],
                    (result: any) => {
                        resolve(result && result.length > 0 ? result[0] : null);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // If ad not found, return null
        if (!ad) {
            return null;
        }

        // Format ad for the UI
        return {
            id: ad.id,
            description: ad.description,
            price: ad.price ? parseFloat(ad.price) : undefined,
            imageUrl: ad.image_url,
            contactNumber: ad.contact_number,
            characterName: ad.character_name,
            timestamp: ad.timestamp,
            category: ad.category,
            identifier: ad.identifier
        };
    } catch (error: any) {
        console.error('[YellowPages] Error getting ad:', error);
        throw new Error(`Failed to get ad: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create a new ad
 * @param identifier Player identifier
 * @param adData Ad data
 * @returns Created ad
 */
async function createAd(identifier: string, adData: any): Promise<any> {
    console.log(`[YellowPages] Creating ad for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Validate category
        const category = Array.isArray(adData.category) ? adData.category[0] : adData.category || 'SELL';

        // Prepare ad data
        const data = {
            identifier,
            description: adData.description,
            price: adData.price || null,
            image_url: adData.imageUrl || null,
            contact_number: adData.contactNumber,
            character_name: adData.characterName,
            timestamp: adData.timestamp,
            category
        };

        let adId = 0;

        // Check if insert_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            adId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_yellow_pages
                (identifier, description, price, image_url, contact_number, character_name, timestamp, category)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `,
                [
                    data.identifier,
                    data.description,
                    data.price,
                    data.image_url,
                    data.contact_number,
                    data.character_name,
                    data.timestamp,
                    data.category
                ]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            adId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_yellow_pages
                    (identifier, description, price, image_url, contact_number, character_name, timestamp, category)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `,
                    [
                        data.identifier,
                        data.description,
                        data.price,
                        data.image_url,
                        data.contact_number,
                        data.character_name,
                        data.timestamp,
                        data.category
                    ],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        // Return the created ad
        return {
            id: adId,
            description: data.description,
            price: data.price,
            imageUrl: data.image_url,
            contactNumber: data.contact_number,
            characterName: data.character_name,
            timestamp: data.timestamp,
            category: data.category
        };
    } catch (error: any) {
        console.error('[YellowPages] Error creating ad:', error);
        throw new Error(`Failed to create ad: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Update an existing ad
 * @param adId Ad ID
 * @param adData Ad data
 * @returns Updated ad
 */
async function updateAd(adId: number, adData: any): Promise<any> {
    console.log(`[YellowPages] Updating ad ${adId}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Validate category
        const category = Array.isArray(adData.category) ? adData.category[0] : adData.category || 'SELL';

        // Prepare ad data
        const data = {
            description: adData.description,
            price: adData.price || null,
            image_url: adData.imageUrl || null,
            contact_number: adData.contactNumber,
            character_name: adData.characterName,
            timestamp: adData.timestamp,
            category
        };

        // Check if execute_async method exists
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                UPDATE phone_yellow_pages
                SET description = ?, price = ?, image_url = ?, contact_number = ?, character_name = ?, timestamp = ?, category = ?
                WHERE id = ?
            `,
                [
                    data.description,
                    data.price,
                    data.image_url,
                    data.contact_number,
                    data.character_name,
                    data.timestamp,
                    data.category,
                    adId
                ]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    UPDATE phone_yellow_pages
                    SET description = ?, price = ?, image_url = ?, contact_number = ?, character_name = ?, timestamp = ?, category = ?
                    WHERE id = ?
                `,
                    [
                        data.description,
                        data.price,
                        data.image_url,
                        data.contact_number,
                        data.character_name,
                        data.timestamp,
                        data.category,
                        adId
                    ],
                    () => {
                        resolve();
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database execute method found');
        }

        // Return the updated ad
        return {
            id: adId,
            description: data.description,
            price: data.price,
            imageUrl: data.image_url,
            contactNumber: data.contact_number,
            characterName: data.character_name,
            timestamp: data.timestamp,
            category: data.category
        };
    } catch (error: any) {
        console.error('[YellowPages] Error updating ad:', error);
        throw new Error(`Failed to update ad: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Delete an ad
 * @param adId Ad ID
 */
async function deleteAd(adId: number): Promise<void> {
    console.log(`[YellowPages] Deleting ad ${adId}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Check if execute_async method exists
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                DELETE FROM phone_yellow_pages
                WHERE id = ?
            `,
                [adId]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    DELETE FROM phone_yellow_pages
                    WHERE id = ?
                `,
                    [adId],
                    () => {
                        resolve();
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database execute method found');
        }
    } catch (error: any) {
        console.error('[YellowPages] Error deleting ad:', error);
        throw new Error(`Failed to delete ad: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Broadcast a new ad to all players
 * @param ad Ad data
 */
function broadcastNewAd(ad: any): void {
    console.log(`[YellowPages] Broadcasting new ad ${ad.id}`);

    // Get all players
    const players = getPlayers();

    // Send the ad to all players
    for (const playerId of players) {
        emitNet('hm-phone:adCreated', parseInt(playerId), {
            success: true,
            ad
        });
    }
}

/**
 * Get all players
 * @returns Array of player IDs
 */
function getPlayers(): string[] {
    return global.exports['qb-core']?.GetPlayers() || [];
}

/**
 * Broadcast an updated ad to all players
 * @param ad Ad data
 */
function broadcastUpdatedAd(ad: any): void {
    console.log(`[YellowPages] Broadcasting updated ad ${ad.id}`);

    // Get all players
    const players = getPlayers();

    // Send the ad to all players
    for (const playerId of players) {
        emitNet('hm-phone:adUpdated', parseInt(playerId), {
            success: true,
            ad
        });
    }
}

/**
 * Broadcast a deleted ad to all players
 * @param adId Ad ID
 */
function broadcastDeletedAd(adId: number): void {
    console.log(`[YellowPages] Broadcasting deleted ad ${adId}`);

    // Get all players
    const players = getPlayers();

    // Send the ad to all players
    for (const playerId of players) {
        emitNet('hm-phone:adDeleted', parseInt(playerId), {
            success: true,
            id: adId
        });
    }
}

/**
 * Check if a player has enough money
 * @param source Player source
 * @param amount Amount to check
 * @returns True if player has enough money, false otherwise
 */
function hasEnoughMoney(source: number, amount: number): boolean {
    try {
        // Get the framework
        const framework = getFrameworkName();

        // Check if player has enough money based on framework
        if (framework === 'qbcore') {
            // QBCore
            const QBCore = global.exports['qb-core'].GetCoreObject();
            const Player = QBCore.Functions.GetPlayer(source);
            if (Player) {
                return Player.Functions.GetMoney('bank') >= amount;
            }
        } else if (framework === 'qbox') {
            // QBox
            return global.exports['qbx_core'].GetMoney(source, 'bank') >= amount;
        }

        return false;
    } catch (error) {
        console.error('[YellowPages] Error checking if player has enough money:', error);
        return false;
    }
}

/**
 * Get the framework name
 * @returns Framework name
 */
function getFrameworkName(): string {
    try {
        if (global.exports['hm-core']) {
            return 'hm-core';
        } else if (global.exports['qb-core']) {
            return 'qbcore';
        } else if (global.exports['qbx_core']) {
            return 'qbox';
        } else {
            return 'standalone';
        }
    } catch (error) {
        console.error('[YellowPages] Error getting framework name:', error);
        return 'standalone';
    }
}

/**
 * Remove money from a player
 * @param source Player source
 * @param amount Amount to remove
 * @param reason Reason for removing money
 * @returns True if money was removed, false otherwise
 */
function removeMoney(source: number, amount: number, reason: string): boolean {
    try {
        // Get the framework
        const framework = getFrameworkName();

        // Remove money based on framework
        if (framework === 'qbcore') {
            // QBCore
            const QBCore = global.exports['qb-core'].GetCoreObject();
            const Player = QBCore.Functions.GetPlayer(source);
            if (Player) {
                return Player.Functions.RemoveMoney('bank', amount, reason);
            }
        } else if (framework === 'qbox') {
            // QBox
            return global.exports['qbx_core'].RemoveMoney(source, 'bank', amount, reason);
        }

        return false;
    } catch (error) {
        console.error('[YellowPages] Error removing money from player:', error);
        return false;
    }
}
