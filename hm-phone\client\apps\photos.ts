/**
 * Photos App - Client Side
 *
 * This file handles client-side functionality for the Photos app.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the photos app
 */
export function initializePhotosApp(): void {
    console.log('[Photos] Initializing client-side photos app');

    // Register client events
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();
}

/**
 * Register client events for the photos app
 */
function registerClientEvents(): void {
    // Register event for photo saved
    onNet('hm-phone:photoSaved', (photoData: any) => {
        console.log('[Photos] Photo saved event received:', photoData);

        // Send the photo data to the UI
        sendToUI('photoSaved', photoData);
    });

    // Register event for photo save error
    onNet('hm-phone:photoSaveError', (errorMessage: string) => {
        console.error('[Photos] Photo save error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });

    // Register event for photos data
    onNet('hm-phone:photos', (photosData: any) => {
        console.log('[Photos] Photos data received:', photosData.length);

        // Send the photos data to the UI
        sendToUI('photos', photosData);
    });

    // Register event for photos error
    onNet('hm-phone:photosError', (errorMessage: string) => {
        console.error('[Photos] Photos error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });

    // Register event for photo deleted
    onNet('hm-phone:photoDeleted', (photoId: number) => {
        console.log('[Photos] Photo deleted event received:', photoId);

        // Send the photo ID to the UI
        sendToUI('photoDeleted', photoId);
    });

    // Register event for photo delete error
    onNet('hm-phone:photoDeleteError', (errorMessage: string) => {
        console.error('[Photos] Photo delete error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });
}

/**
 * Register NUI handlers for the photos app
 */
function registerNUIHandlers(): void {
    // Register handler for creating a photo
    registerAppHandler('photos', 'createPhoto', async (data: any) => {
        console.log('[Photos] Received createPhoto request from UI:', data);

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Photos] Creating photo for player: ${stateid || identifier}`);

            // Extract the image data and metadata
            const { imageUrl, metadata } = data;

            // Add player identifier to metadata
            const enhancedMetadata = {
                ...metadata,
                playerIdentifier: stateid || identifier
            };

            // Send the photo data to the server
            emitNet('hm-phone:savePhoto', imageUrl, enhancedMetadata);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Photos] Error creating photo:', error);
            return { success: false, error: 'Failed to create photo' };
        }
    });

    // Register handler for getting all photos
    registerAppHandler('photos', 'getAllPhotos', async () => {
        console.log('[Photos] Received getAllPhotos request from UI');

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Photos] Getting photos for player: ${stateid || identifier}`);

            // Request photos from the server
            emitNet('hm-phone:getPhotos');

            // Return success to the UI
            // The actual photos will be sent via the hm-phone:photos event
            return { success: true };
        } catch (error) {
            console.error('[Photos] Error getting photos:', error);
            return { success: false, error: 'Failed to get photos' };
        }
    });

    // Register handler for deleting a photo
    registerAppHandler('photos', 'deletePhoto', async (data: any) => {
        console.log('[Photos] Received deletePhoto request from UI:', data);

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Photos] Deleting photo for player: ${stateid || identifier}`);

            // Extract the photo ID
            const { id } = data;

            // Send the delete request to the server
            emitNet('hm-phone:deletePhoto', id);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Photos] Error deleting photo:', error);
            return { success: false, error: 'Failed to delete photo' };
        }
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'photos',
        type,
        data
    });
}
