local QBCore = exports['qb-core']:GetCoreObject()
local isBombArmed = false
local bombEntity = nil
-- local bombType = nil
local bombCountdown = 0
local countdownSound = nil
-- local targetSpeed = 60 -- Set your desired target speed in units/s
local isCountdownActive = false

RegisterNetEvent('hm_carbomb:armbomb')
AddEventHandler('hm_carbomb:armbomb', function(bombType)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 70)
    if DoesEntityExist(vehicle) and IsEntityAVehicle(vehicle) then -- Trigger the server-side event to attempt arming the bomb
        local plate = GetVehicleNumberPlateText(vehicle)
        QBCore.Functions.Progressbar("random_task", "Planting Bomb", 5000, false, true, {
            disableMovement = true,
            disableCarMovement = false,
            disableMouse = false,
            disableCombat = true,
         }, {
            animDict = "mp_suicide",
            anim = "pill",
            flags = 49,
         }, {}, {}, function()
            TriggerServerEvent('hm_carbomb:armbomb', vehicle, plate, bombType)
            print("TriggerServerEvent : hm_carbomb:armbomb")
         end, function()
            -- Cancel
         end)    
    else
        print("No vehicle found in front of you to arm the bomb.")
    end
end)

RegisterNetEvent('hm_carbomb:armed')
AddEventHandler('hm_carbomb:armed', function(success, vehicle, plate, bombType)
    if success then
        PlaySoundFromEntity(-1, "Bomb_Armed", vehicle, "GTAO_DLC_IE_Velocity_Sounds", false, 25)
        print("Bomb successfully armed on vehicle with plate: " .. "_"..plate.."_"..bombType)-- Add UI updates or other client-side actions for a successful arming
    else
        print("Failed to arm the bomb on vehicle with plate: " .. "_"..plate.."_"..bombType)-- Add UI updates or other client-side actions for a failed arming
    end
end)

RegisterNetEvent('hm_carbomb:disarmbomb')
AddEventHandler('hm_carbomb:disarmbomb', function(bombType)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 70)

    if DoesEntityExist(vehicle) and IsEntityAVehicle(vehicle) then
        local plate = GetVehicleNumberPlateText(vehicle)
        TriggerServerEvent('hm_carbomb:disarmbomb', vehicle, plate, bombType)
        print("TriggerServerEvent : hm_carbomb:disarmbomb")
    else
        print("No vehicle found in front of you to disarm the bomb.")
    end
end)

RegisterNetEvent('hm_carbomb:disarmed')
AddEventHandler('hm_carbomb:disarmed', function(success, vehicle, plate, bombType)
    if success then
        PlaySoundFromEntity(-1, "Bomb_Disarmed", vehicle, "GTAO_DLC_IE_Velocity_Sounds", false, 25)
        print("Bomb successfully disarmed on vehicle with plate: ".."_"..vehicle.."_"..plate)
    else
        print("Failed to disarm the bomb on vehicle with plate: ".."_"..vehicle.."_"..plate)
    end
end)
local countdowns = {}  -- Table to store ongoing countdowns

RegisterNetEvent('hm_carbomb:countdown')
AddEventHandler('hm_carbomb:countdown', function(vehicle, bombType)
    -- Check if a countdown is already running for this vehicle
    if countdowns[vehicle] then
        print("Countdown is already running for vehicle with plate " .. GetVehicleNumberPlateText(vehicle))
        return
    end

    local bombConfig = Config.Bombs[bombType]
    if not bombConfig then
        print("Invalid bomb type: " .. bombType)
        return
    end
    local duration = bombConfig.duration
    local remainingTime = duration
    local soundid = nil
    local isSoundPlaying = false
    local stopCountdown = false

    -- Store countdown information in the table
    countdowns[vehicle] = {
        duration = duration,
        remainingTime = remainingTime,
        soundid = soundid,
        isSoundPlaying = isSoundPlaying,
        stopCountdown = stopCountdown
    }

    Citizen.CreateThread(function()
        while countdowns[vehicle].remainingTime > 0 and not stopCountdown do
            local speed = GetEntitySpeed(vehicle) * 3.6
            local targetSpeed = bombConfig.targetSpeed

            if bombType == "speed" then
                if speed < targetSpeed and not isSoundPlaying then
                    soundid = GetSoundId()
                    PlaySoundFromEntity(soundid, "Explosion_Countdown", vehicle, "GTAO_FM_Events_Soundset", false, 25)
                    SetVariableOnSound(soundid, "Time", (duration / 1000))
                    isSoundPlaying = true
                    print("speed_soundid: "..speed.."_"..soundid.."___"..vehicle)
                elseif speed >= targetSpeed and isSoundPlaying then
                    StopSound(soundid)
                    ReleaseSoundId(soundid)
                    soundid = nil -- Reset soundid
                    isSoundPlaying = false
                    print("speed2 : "..speed)
                    remainingTime = duration
                    -- break
                end
            end
            if isSoundPlaying then
                Wait(1000)
                remainingTime = remainingTime - 1000
                -- Check if the countdown reached zero
                if remainingTime <= 0 then
                    print("Countdown reached zero. Triggering detonation.")
                    NetworkExplodeVehicle(vehicle, true, false, false)
                    if isSoundPlaying then
                        StopSound(soundid)
                        ReleaseSoundId(soundid)
                        soundid = nil
                        isSoundPlaying = false
                    end
                    print("End of event")
                    break
                    -- TriggerEvent('hm_carbomb:detonate', vehicle)
                end
                print("Countdown: " .. (remainingTime / 1000) .. " seconds remaining for vehicle with plate " .. GetVehicleNumberPlateText(vehicle))
            else
                Wait(500)
            end
        end
        countdowns[vehicle] = nil
    end)
end)

RegisterCommand('testcarbomb', function(source, args, rawCommand)
    local command = args[1]
    if command == 'armbomb' then
        local bombType = args[2] or 'timed'
        TriggerEvent('hm_carbomb:armbomb', bombType)
    elseif command == 'disarmbomb' then
        local bombType = args[2] or 'timed'
        TriggerEvent('hm_carbomb:disarmbomb', bombType)
    elseif command == 'countdown' then
        local bombType = args[2] or 'timed'
        TriggerEvent('hm_carbomb:countdown', bombType)
    elseif command == 'detonate' then
        TriggerEvent('hm_carbomb:detonate')
    elseif command == 'stopcountdown' then
        TriggerEvent('hm_carbomb:stopcountdown', 6121474)
    else
        print("Invalid test command.")
    end
end, false)

RegisterCommand('testpt', function(source, args, rawCommand)
    print("run it")
    RequestScriptAudioBank("DLC_SECURITY_MUSIC\\NO_DIGGITY" ,true, -1)
    local soundId = GetSoundId() -- Get a unique sound ID
    print(soundId)
    PlaySoundFromEntity(-1, "no_diggity", PlayerPedId(),"DLC_SECURITY_MUSIC", false, 0)
    Wait(10000)
    StopSound(soundId)
end, false)

RegisterCommand('clip', function(source, args, rawCommand)
    print("run it")
    ret = GetPedMovementClipset(PlayerPedId())
    print(ret)
end, false)


-- Event to stop the countdown for a specific vehicle
RegisterNetEvent('hm_carbomb:stopcountdown')
AddEventHandler('hm_carbomb:stopcountdown', function(vehicle)
    if countdowns[vehicle] then
        print("Stopping countdown for vehicle with plate " .. GetVehicleNumberPlateText(vehicle))
        countdowns[vehicle].stopCountdown = true
        countdowns[vehicle].remainingTime = 0
        countdowns[vehicle].duration = 0
        countdowns[vehicle] = nil
        
    else
        print("No countdown to stop for vehicle with plate " .. GetVehicleNumberPlateText(vehicle))
    end
end)