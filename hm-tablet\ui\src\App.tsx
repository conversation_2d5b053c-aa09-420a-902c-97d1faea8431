import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTabletStore } from './stores/tabletStore';
import { isDevMode, setDevMode } from './services/nui';
import TabletLayout from './components/TabletLayout';

const App: React.FC = () => {
  const isTabletVisible = useTabletStore((state) => state.isTabletVisible);
//   const isLocked = useTabletStore((state) => state.isLocked);
  const setTabletVisible = useTabletStore((state) => state.setTabletVisible);

  useEffect(() => {
    // Enable dev mode if not in FiveM
    if (isDevMode()) {
      setDevMode(true);
      setTabletVisible(true);
    }
  }, [setTabletVisible]);

  if (!isTabletVisible) {
    return null;
  }

  return (
    <div className="w-full h-full flex items-center justify-center bg-transparent">
      <AnimatePresence mode="wait">
        <motion.div
          key="tablet"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ 
            duration: 0.3, 
            ease: "easeOut" 
          }}
          className="relative"
        >          {/* Tablet Device Frame */}
          <div className="tablet-frame relative">
            {/* Tablet Speaker Grilles */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 flex gap-1">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="w-0.5 h-2 bg-gradient-to-b from-gray-600 to-gray-800 rounded-full"></div>
              ))}
            </div>
            
            {/* Tablet Volume Buttons */}
            <div className="absolute -left-1 top-16 space-y-1">
              <div className="w-1 h-6 bg-gradient-to-r from-gray-600 to-gray-800 rounded-r-sm shadow-md"></div>
              <div className="w-1 h-8 bg-gradient-to-r from-gray-600 to-gray-800 rounded-r-sm shadow-md"></div>
            </div>
            
            {/* Tablet Power Button */}
            <div className="absolute -right-1 top-16">
              <div className="w-1 h-6 bg-gradient-to-r from-gray-800 to-gray-600 rounded-l-sm shadow-md"></div>
            </div>
            
            <TabletLayout />
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default App;
