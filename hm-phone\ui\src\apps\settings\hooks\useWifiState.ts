import { useEffect } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { useSettingsStore } from '../stores/settingsStore';
import { wifiMockData } from '../../../fivem/mockData';

/**
 * Custom hook to manage WiFi state and connected network
 * This hook ensures that the connected WiFi is cleared when WiFi is toggled off
 */
export const useWifiState = () => {
  const { isWifiOn, actions } = usePhoneStore();
  const { connectedWifi, setConnectedWifi } = useSettingsStore();

  // Initialize connected WiFi from mock data when component mounts
  useEffect(() => {
    if (isWifiOn && !connectedWifi) {
      const connectedNetwork = wifiMockData.networks.find((n) => n.connected);
      if (connectedNetwork) {
        setConnectedWifi(connectedNetwork.ssid);
      }
    }
  }, [isWifiOn, connectedWifi, setConnectedWifi]);

  // Clear connected WiFi when WiFi is toggled off
  useEffect(() => {
    if (!isWifiOn && connectedWifi) {
      setConnectedWifi(null);
    }
  }, [isWifiOn, connectedWifi, setConnectedWifi]);

  // Custom toggleWifi function that also clears the connected WiFi
  const toggleWifi = () => {
    const newState = !isWifiOn;
    
    // If turning off WiFi, clear the connected WiFi
    if (!newState && connectedWifi) {
      setConnectedWifi(null);
    }
    
    // Toggle WiFi state
    actions.toggleWifi();
  };

  return {
    isWifiOn,
    connectedWifi,
    setConnectedWifi,
    toggleWifi
  };
};
