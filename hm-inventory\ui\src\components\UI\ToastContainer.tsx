import React from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import { Toast } from '@shared/inventory.types';

interface ToastItemProps {
  toast: Toast;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast }) => {
  const typeStyles = {
    info: 'border-blue-500/20 border-l-blue-400/80',
    success: 'border-green-500/20 border-l-green-400/80',
    warning: 'border-orange-500/20 border-l-orange-400/80',
    error: 'border-red-500/20 border-l-red-400/80'
  };

  const gradients = {
    info: 'from-blue-400/10 via-blue-500/10 to-blue-600/10',
    success: 'from-green-400/10 via-green-500/10 to-green-600/10',
    warning: 'from-orange-400/10 via-orange-500/10 to-orange-600/10',
    error: 'from-red-400/10 via-red-500/10 to-red-600/10'
  };
  
  const iconColors = {
    info: 'text-blue-400/80',
    success: 'text-green-400/80',
    warning: 'text-orange-400/80',
    error: 'text-red-400/80'
  };
  return (
    <div 
      className={`
        bg-neutral-800 ${typeStyles[toast.type]} 
        border border-neutral-700/50 border-l-4 rounded-lg shadow-lg p-3 mb-2 
        inline-block max-w-[320px] whitespace-normal
        transform transition-all duration-300
        animate-fade-in relative overflow-hidden
      `}
      style={{
        animation: 'fadeIn 0.3s ease-in-out forwards'
      }}
    >
      {/* Top accent gradient */}
      <div className={`absolute left-0 top-0 w-full h-1 bg-gradient-to-r ${gradients[toast.type]} opacity-30 z-10 rounded-t-lg`} />
      
      <div className="flex items-center gap-3 relative z-20">
        {/* Main icon */}
        {toast.icon && (
          <div className="flex-shrink-0">
            <i className={`fas ${toast.icon} ${iconColors[toast.type]} text-lg`} />
          </div>
        )}
        
        {/* Item icon if present */}
        {toast.itemIcon && (
          <div className="flex-shrink-0 w-8 h-8 bg-neutral-900 border border-neutral-700/50 rounded-lg flex items-center justify-center shadow-sm">
            {toast.itemIcon.startsWith('fa-') ? (
              <i className={`fas ${toast.itemIcon} text-neutral-200 text-sm`} />
            ) : (
              <img src={toast.itemIcon} alt="" className="w-6 h-6 object-contain" />
            )}
          </div>
        )}
        
        {/* Message content */}
        <div className="flex-1 text-neutral-200">
          <div className="font-medium text-sm">{toast.message}</div>
          
          {/* Additional info row */}
          <div className="flex items-center gap-3 mt-1 text-xs text-neutral-400">
            {typeof toast.quantity === 'number' && toast.quantity > 0 && (
              <span className="bg-neutral-700/80 px-1.5 py-0.5 rounded shadow-inner">x{toast.quantity}</span>
            )}
            {typeof toast.amount === 'number' && toast.amount > 0 && (
              <span className="bg-neutral-700/80 px-1.5 py-0.5 rounded shadow-inner">${toast.amount.toFixed(2)}</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface ToastContainerProps {
  isInventoryOpen: boolean;
}

const ToastContainer: React.FC<ToastContainerProps> = ({ isInventoryOpen }) => {
  const toasts = useInventoryStore(state => state.toasts);

  if (toasts.length === 0) return null;

  // Position based on inventory state
  const positionClasses = isInventoryOpen 
    ? 'top-6 right-6' // When inventory is open, position at top-right
    : 'bottom-20 right-6'; // When inventory is closed, position above quick access bar
    return (
    <div className={`fixed ${positionClasses} z-[100] pointer-events-none`}>
      <div className="pointer-events-auto flex flex-col items-end space-y-2 filter drop-shadow-xl max-h-[70vh] overflow-y-auto pr-1 pb-1">
        {toasts.map((toast) => (
          <ToastItem 
            key={toast.id} 
            toast={toast}
          />
        ))}
      </div>
    </div>
  );
};

export default ToastContainer;
