// New drag and drop types - completely separate from store types
import { InventoryItem, SlotType } from '@shared/inventory.types';
import { PanelType } from '../../../ui/src/stores/inventoryStore';

// Include specific secondary panel types to allow proper mapping
export type DragPanelType = PanelType;

export interface DragContext {
    panelType: DragPanelType;
    panelId?: string; // For secondary panels
    slotIndex: number;
}

export interface DragPayload {
    inventoryItem: InventoryItem;
    quantity: number;
    sourceContext: DragContext;
}

export interface DropResult {
    targetContext: DragContext;
    operation: 'move' | 'transfer' | 'swap';
}

export interface DragItem {
    item: InventoryItem;
    source: {
        panelType: DragPanelType;
        panelId?: string;
        slotIndex: number;
        quantity?: number;
    };
}

export interface DropItem {
    item: InventoryItem;
    target: {
        panelType: DragPanelType;
        slotType?: SlotType;
        panelId?: string;
        slotIndex: number;
        quantity?: number;
    };
}
