import { isBrowserEnv } from '@/utils/environment';
import { initializeMockData, setupMockNUICallbacks } from '@/utils/mockData';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// Interfaces for weapon modding
export interface AttachmentItem {
  id: string;
  name: string;
  label: string;
  category: 'optics' | 'muzzle' | 'magazine' | 'grip' | 'tactical' | 'misc';
  icon?: string;
  description?: string;
  stats?: {
    damage?: number;
    accuracy?: number;
    range?: number;
    stability?: number;
    concealment?: number;
  };
  rarity?: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  attachBone?: string;
  price?: number;
}

export interface AttachmentPoint {
  id: string;
  slotType: string;
  position: {
    x: string;
    y: string;
  };
  availableMods: AttachmentItem[];
  currentMod?: AttachmentItem;
}

export interface WeaponData {
  hash: string;
  name: string;
  label: string;
  components?: AttachmentItem[];
  stats?: {
    damage: number;
    accuracy: number;
    range: number;
    stability: number;
    concealment: number;
  };
}

// State interface
export interface WeaponModdingState {
  // UI State
  isVisible: boolean;
  isLoading: boolean;
  error: string | null;
  isApplyingMod: boolean; // Add this for visual feedback
  
  // Weapon Data
  currentWeapon: WeaponData | null;
  attachmentPoints: AttachmentPoint[];
  availableMods: Record<string, AttachmentItem[]>;
  playerInventory: string[];
    // Camera & 3D State
  cameraActive: boolean;
  weaponEntity: number | null;
  cameraFOV: number;
  weaponHeading: number;
  
  // Positioning Controls
  showPositioningControls: boolean;
  currentOffsets: {
    weaponPosition: { x: number; y: number; z: number };
    weaponRotation: { x: number; y: number; z: number };
    cameraPosition: { x: number; y: number; z: number };
    cameraRotation: { x: number; y: number; z: number };
  };
  
  // Actions
  actions: {
    // UI Actions
    setVisible: (visible: boolean) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    
    // Weapon Actions
    startModding: (weaponHash: string) => void;
    stopModding: () => void;
    setCurrentWeapon: (weapon: WeaponData | null) => void;
    
    // Attachment Actions
    updateAttachmentPoints: (points: AttachmentPoint[]) => void;
    updateAvailableMods: (mods: Record<string, AttachmentItem[]>) => void;
    updatePlayerInventory: (inventory: string[]) => void;
    applyMod: (pointId: string, mod: AttachmentItem) => void;
    removeMod: (pointId: string) => void;
      // Camera Actions
    setCameraActive: (active: boolean) => void;
    setWeaponEntity: (entity: number | null) => void;
    adjustCameraFOV: (fov: number) => void;    rotateWeapon: (headingOffset: number) => void;
    resetWeaponPosition: () => void;
    initializeAttachmentPositions: () => void;
    
    // Positioning Control Actions
    setShowPositioningControls: (show: boolean) => void;
    updateOffsets: (offsets: {
      weaponPosition: { x: number; y: number; z: number };
      weaponRotation: { x: number; y: number; z: number };
      cameraPosition: { x: number; y: number; z: number };
      cameraRotation: { x: number; y: number; z: number };
    }) => void;    adjustWeaponPosition: (axis: 'x' | 'y' | 'z', amount: number) => void;
    adjustWeaponRotation: (axis: 'x' | 'y' | 'z', amount: number) => void;
    adjustCameraPosition: (axis: 'x' | 'y' | 'z', amount: number) => void;
    adjustCameraRotation: (axis: 'x' | 'y' | 'z', amount: number) => void;
    setWeaponPosition: (axis: 'x' | 'y' | 'z', value: number) => void;
    setWeaponRotation: (axis: 'x' | 'y' | 'z', value: number) => void;
    setCameraPosition: (axis: 'x' | 'y' | 'z', value: number) => void;
    setCameraRotation: (axis: 'x' | 'y' | 'z', value: number) => void;
    copyOffsets: () => void;
  };
  
  // Handlers for NUI messages
  handlers: {
    onOpenWeaponModding: (data: { weaponHash: string }) => void;
    onCloseWeaponModding: () => void;
    onUpdateAttachmentPoints: (points: any[]) => void;
    onUpdateAvailableMods: (mods: Record<string, AttachmentItem[]>) => void;
    onModApplied: (data: { success: boolean; componentName: string }) => void;
    onModRemoved: (data: { success: boolean; componentName: string }) => void;
    onShowPositioningControls: (data: { show: boolean }) => void;
    onUpdateOffsets: (data: {
      weaponPosition: { x: number; y: number; z: number };
      weaponRotation: { x: number; y: number; z: number };
      cameraPosition: { x: number; y: number; z: number };
      cameraRotation: { x: number; y: number; z: number };
    }) => void;
  };
}

// Create the store
export const useWeaponModdingStore = create<WeaponModdingState>()(
  immer((set, get) => ({    // Initial State
    isVisible: isBrowserEnv() ? true : false, // Default to true in server-side rendering
    isLoading: false,
    error: null,
    isApplyingMod: false,
    
    currentWeapon: null,
    attachmentPoints: [],
    availableMods: {},
    playerInventory: [],
      cameraActive: false,
    weaponEntity: null,
    cameraFOV: 50.0,
    weaponHeading: 0,
    
    // Positioning Controls
    showPositioningControls: false,
    currentOffsets: {
      weaponPosition: { x: 0, y: 0, z: 0 },
      weaponRotation: { x: 0, y: 0, z: 0 },
      cameraPosition: { x: 0, y: 0, z: 0 },
      cameraRotation: { x: 0, y: 0, z: 0 },
    },
    
    // Actions
    actions: {
      // UI Actions
      setVisible: (visible) => {
        set((state) => {
          state.isVisible = visible;
          console.log(`[WeaponModding] UI visibility set to: ${visible}`);
        });
      },
      
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error) => {
        set((state) => {
          state.error = error;
          if (error) {
            console.error(`[WeaponModding] Error: ${error}`);
          }
        });
      },
      
      // Weapon Actions
      startModding: (weaponHash) => {
        console.log(`[WeaponModding] Starting modding for weapon: ${weaponHash}`);
        set((state) => {
          state.isLoading = true;
          state.error = null;
          state.isVisible = true;
        });
          // Send message to client script to start modding
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/startModding', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ weaponHash })
          }).catch(console.error);
        }
      },
      
      stopModding: () => {
        console.log(`[WeaponModding] Stopping modding`);
        set((state) => {
          state.isVisible = false;
          state.currentWeapon = null;
          state.attachmentPoints = [];
          state.availableMods = {};
          state.cameraActive = false;
          state.weaponEntity = null;
          state.error = null;
        });
          // Send message to client script to stop modding
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/stopModding', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          }).catch(console.error);
        }
      },
      
      setCurrentWeapon: (weapon) => {
        set((state) => {
          state.currentWeapon = weapon;
          console.log(`[WeaponModding] Current weapon set:`, weapon);
        });
      },
      
      // Attachment Actions
      updateAttachmentPoints: (points) => {
        set((state) => {
          state.attachmentPoints = points;
          console.log(`[WeaponModding] Updated ${points.length} attachment points`);
        });
      },      updateAvailableMods: (mods) => {
        console.log(`[WeaponModding] Received mods from server:`, mods);
        
        // Debug: Check if mods have id field
        Object.keys(mods).forEach(bone => {
          mods[bone].forEach(mod => {
            if (!mod.id) {
              console.error(`[WeaponModding] ERROR: mod missing id field for bone ${bone}:`, mod);
            } else {
              console.log(`[WeaponModding] Mod ${mod.name} has id: ${mod.id}`);
            }
          });
        });
        
        set((state) => {
          state.availableMods = mods;
          const totalMods = Object.values(mods).reduce((sum, arr) => sum + arr.length, 0);
          console.log(`[WeaponModding] Available mod categories:`, Object.keys(mods).join(','));
          Object.entries(mods).forEach(([category, items]) => {
            console.log(`[WeaponModding] Category '${category}': ${items.length} mods`);
          });
          console.log(`[WeaponModding] Available mods updated - Total: ${totalMods}`);
          console.log(`[WeaponModding] Available mods by category:`, Object.entries(mods).map(([cat, items]) => `${cat}: ${items.length}`).join(', '));
        });
      },
      
      updatePlayerInventory: (inventory) => {
        set((state) => {
          state.playerInventory = inventory;
          console.log(`[WeaponModding] Updated player inventory: ${inventory.length} items`);
        });
      },      applyMod: (pointId, mod) => {
        console.log(`[WeaponModding] Applying mod ${mod.name} (ID: ${mod.id}) (category: ${mod.category}) to point ${pointId}`);
        
        // Debug: Check if mod.id is actually defined
        if (!mod.id) {
          console.error(`[WeaponModding] ERROR: mod.id is undefined!`, mod);
          return;
        }
        
        // Set applying state for visual feedback
        set((state) => {
          state.isApplyingMod = true;
        });
        
        const state = get();
        
        // Check if there's already a mod of the same category equipped on any attachment point
        const existingPointWithSameCategory = state.attachmentPoints.find(
          p => p.currentMod && p.currentMod.category === mod.category && p.id !== pointId
        );
        
        if (existingPointWithSameCategory) {
          console.log(`[WeaponModding] Removing existing ${mod.category} mod: ${existingPointWithSameCategory.currentMod!.name} from point ${existingPointWithSameCategory.id}`);
          
          // Remove the existing mod from the server
          if (typeof window !== 'undefined') {
            fetch('https://hm-weapons/removeMod', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ componentName: existingPointWithSameCategory.currentMod!.id })
            }).catch(console.error);
          }
        }
        
        set((state) => {
          // Remove any existing mod of the same category from other attachment points
          state.attachmentPoints.forEach(p => {
            if (p.currentMod && p.currentMod.category === mod.category && p.id !== pointId) {
              console.log(`[WeaponModding] Clearing ${p.currentMod.name} from point ${p.id}`);
              p.currentMod = undefined;
            }
          });
          
          // Apply the new mod to the target point
          const point = state.attachmentPoints.find(p => p.id === pointId);
          if (point) {
            // If there's already a mod on this point, remove it first
            if (point.currentMod) {
              console.log(`[WeaponModding] Replacing existing mod ${point.currentMod.name} on point ${pointId}`);
              
              // Remove the old mod from server
              if (typeof window !== 'undefined') {
                fetch('https://hm-weapons/removeMod', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ componentName: point.currentMod.id })
                }).catch(console.error);
              }
            }
            
            point.currentMod = mod;
          }
        });
          // Apply the new mod to the server
        if (typeof window !== 'undefined') {
          console.log(`[WeaponModding] Sending to server: componentName=${mod.id}`);
          fetch('https://hm-weapons/applyMod', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ componentName: mod.id })
          }).then(() => {
            console.log(`[WeaponModding] Successfully applied mod: ${mod.id}`);
            // Clear applying state after a short delay
            setTimeout(() => {
              set((state) => {
                state.isApplyingMod = false;
              });
            }, 500);
          }).catch((error) => {
            console.error('[WeaponModding] Error applying mod:', error);
            set((state) => {
              state.isApplyingMod = false;
            });
          });
        }
      },
        removeMod: (pointId) => {
        const state = get();
        const point = state.attachmentPoints.find(p => p.id === pointId);
        const modName = point?.currentMod?.name || 'unknown';
        const componentName = point?.currentMod?.id;
        
        if (!point || !point.currentMod) {
          console.log(`[WeaponModding] No mod to remove from point ${pointId}`);
          return;
        }
        
        console.log(`[WeaponModding] Removing mod ${modName} from point ${pointId}`);
        
        set((state) => {
          const point = state.attachmentPoints.find(p => p.id === pointId);
          if (point && point.currentMod) {
            point.currentMod = undefined;
          }
        });
        
        // Send to client script
        if (typeof window !== 'undefined' && componentName) {
          fetch('https://hm-weapons/removeMod', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ componentName })
          }).catch(console.error);
        }
      },
      
      // Camera Actions
      setCameraActive: (active) => {
        set((state) => {
          state.cameraActive = active;
          console.log(`[WeaponModding] Camera active: ${active}`);
        });
      },
        setWeaponEntity: (entity) => {
        set((state) => {
          state.weaponEntity = entity;
          console.log(`[WeaponModding] Weapon entity: ${entity}`);
        });
      },
      
      adjustCameraFOV: (fov) => {
        set((state) => {
          state.cameraFOV = Math.max(10.0, Math.min(120.0, fov)); // Clamp between 10-120
          console.log(`[WeaponModding] Camera FOV adjusted to: ${state.cameraFOV}`);
        });
        
        // Send to client script
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustFOV', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ fov })
          }).catch(console.error);
        }
      },
      
      rotateWeapon: (headingOffset) => {
        set((state) => {
          state.weaponHeading += headingOffset;
          console.log(`[WeaponModding] Weapon rotated by ${headingOffset}°, new heading: ${state.weaponHeading}`);
        });
        
        // Send to client script
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/rotateWeapon', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ headingOffset })
          }).catch(console.error);
        }
      },
      
      resetWeaponPosition: () => {
        set((state) => {
          state.weaponHeading = 0;
          console.log(`[WeaponModding] Weapon position reset`);
        });
        
        // Send to client script
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/resetWeaponPosition', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          }).catch(console.error);
        }
      },
        initializeAttachmentPositions: () => {
        console.log(`[WeaponModding] Initializing attachment positions`);
        
        // Send to client script to ensure proper positioning
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/initializePositions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          }).catch(console.error);
        }
      },
      
      // Positioning Control Actions
      setShowPositioningControls: (show) => {
        set((state) => {
          state.showPositioningControls = show;
          console.log(`[WeaponModding] Positioning controls visibility: ${show}`);
        });
      },
      
      updateOffsets: (offsets) => {
        set((state) => {
          state.currentOffsets = offsets;
          console.log(`[WeaponModding] Offsets updated:`, offsets);
        });
      },
        adjustWeaponPosition: (axis, amount) => {
        console.log(`[WeaponModding] Adjusting weapon position ${axis} by ${amount}`);
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustWeaponPosition', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },
      
      adjustWeaponRotation: (axis, amount) => {
        console.log(`[WeaponModding] Adjusting weapon rotation ${axis} by ${amount}`);
          if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustWeaponRotation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },
      
      adjustCameraPosition: (axis, amount) => {
        console.log(`[WeaponModding] Adjusting camera position ${axis} by ${amount}`);
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustCameraPosition', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },
        adjustCameraRotation: (axis, amount) => {
        console.log(`[WeaponModding] Adjusting camera rotation ${axis} by ${amount}`);
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustCameraRotation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },

      // Direct set functions for sliders
      setWeaponPosition: (axis, value) => {
        console.log(`[WeaponModding] Setting weapon position ${axis} to ${value}`);
        
        // Calculate the difference from current value
        const currentValue = get().currentOffsets.weaponPosition[axis];
        const amount = value - currentValue;
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustWeaponPosition', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },

      setWeaponRotation: (axis, value) => {
        console.log(`[WeaponModding] Setting weapon rotation ${axis} to ${value}`);
        
        // Calculate the difference from current value
        const currentValue = get().currentOffsets.weaponRotation[axis];
        const amount = value - currentValue;
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustWeaponRotation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },

      setCameraPosition: (axis, value) => {
        console.log(`[WeaponModding] Setting camera position ${axis} to ${value}`);
        
        // Calculate the difference from current value
        const currentValue = get().currentOffsets.cameraPosition[axis];
        const amount = value - currentValue;
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustCameraPosition', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },

      setCameraRotation: (axis, value) => {
        console.log(`[WeaponModding] Setting camera rotation ${axis} to ${value}`);
        
        // Calculate the difference from current value
        const currentValue = get().currentOffsets.cameraRotation[axis];
        const amount = value - currentValue;
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/adjustCameraRotation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ axis, direction: amount })
          }).catch(console.error);
        }
      },
        copyOffsets: () => {
        console.log(`[WeaponModding] Copying offsets to clipboard`);
        
        if (typeof window !== 'undefined') {
          fetch('https://hm-weapons/copyOffsets', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          }).catch(console.error);
        }
      }
    },    // Handlers for NUI messages
    handlers: {
      onOpenWeaponModding: (data) => {
        console.log(`[WeaponModding] Handler: Opening weapon modding`, data);
        
        // Set UI to visible and loading state immediately
        set((state) => {
          state.isVisible = true;
          state.isLoading = true;
          state.error = null;
        });
        
        // Don't call startModding here - let the client handle it
        // The client will start modding and send us available mods
      },
      
      onCloseWeaponModding: () => {
        console.log(`[WeaponModding] Handler: Closing weapon modding`);
        get().actions.stopModding();
      },
        onUpdateAttachmentPoints: (points) => {
        console.log(`[WeaponModding] Handler: Updating attachment points`, points);
        const mappedPoints: AttachmentPoint[] = points.map(point => ({
          id: point.id,
          slotType: point.slotType || point.id,
          position: point.position,
          availableMods: point.availableMods || [],
          currentMod: point.currentMod
        }));
        get().actions.updateAttachmentPoints(mappedPoints);
        // Initialize positions to ensure proper placement
        get().actions.initializeAttachmentPositions();
      },
        onUpdateAvailableMods: (mods) => {
        // stringify the mods for better logging
        console.log(`[WeaponModding] Handler: Updating available mods`, JSON.stringify(mods, null, 2));
        console.log(`[WeaponModding] Available mod categories:`, Object.keys(mods));
        Object.entries(mods).forEach(([category, items]) => {
          console.log(`[WeaponModding] Category '${category}': ${items.length} mods`);
        });
        get().actions.updateAvailableMods(mods);
        get().actions.setLoading(false);
      },
      
      onModApplied: (data) => {
        console.log(`[WeaponModding] Handler: Mod applied`, data);
        if (!data.success) {
          get().actions.setError(`Failed to apply mod: ${data.componentName}`);
        }
      },
        onModRemoved: (data) => {
        console.log(`[WeaponModding] Handler: Mod removed`, data);
        if (!data.success) {
          get().actions.setError(`Failed to remove mod: ${data.componentName}`);
        }
      },
      
      onShowPositioningControls: (data) => {
        console.log(`[WeaponModding] Handler: Show positioning controls`, data);
        get().actions.setShowPositioningControls(data.show);
      },
      
      onUpdateOffsets: (data) => {
        console.log(`[WeaponModding] Handler: Update offsets`, data);
        get().actions.updateOffsets(data);
      }
    }
  }))
);

// Helper selectors
export const useWeaponModdingActions = () => useWeaponModdingStore(state => state.actions);
export const useWeaponModdingHandlers = () => useWeaponModdingStore(state => state.handlers);

// Initialize event handlers for NUI communication
export const initializeWeaponModdingStore = (): (() => void) => {
  console.log('[WeaponModding Store] Initializing NUI event handlers');
  
  // Set up mock data for browser environment
  if (isBrowserEnv()) {
    console.log('[WeaponModding Store] Browser environment detected - setting up mock data');
    setupMockNUICallbacks();
    initializeMockData();
  }
  
  const handleMessage = (event: MessageEvent) => {
    const { type, data } = event.data;
    const handlers = useWeaponModdingStore.getState().handlers;
    
    console.log(`[WeaponModding Store] Received NUI message:`, { type, data });
      switch (type) {
      case 'OPEN_WEAPON_MODDING':
        handlers.onOpenWeaponModding(data);
        break;
      case 'CLOSE_WEAPON_MODDING':
        handlers.onCloseWeaponModding();
        break;
      case 'UPDATE_ATTACHMENT_POINTS':
        handlers.onUpdateAttachmentPoints(data);
        break;
      case 'UPDATE_AVAILABLE_MODS':
        handlers.onUpdateAvailableMods(data);
        break;
      case 'SET_LOADING':
        useWeaponModdingStore.getState().actions.setLoading(data.loading);
        break;
      case 'MOD_APPLIED':
        handlers.onModApplied(data);
        break;      case 'MOD_REMOVED':
        handlers.onModRemoved(data);
        break;
      case 'SHOW_POSITIONING_CONTROLS':
        handlers.onShowPositioningControls(data);
        break;
      case 'UPDATE_OFFSETS':
        handlers.onUpdateOffsets(data);
        break;
      default:
        console.log(`[WeaponModding Store] Unknown message type: ${type}`);
    }
  };
  
  window.addEventListener('message', handleMessage);
  
  // Return cleanup function
  return () => {
    console.log('[WeaponModding Store] Cleaning up NUI event handlers');
    window.removeEventListener('message', handleMessage);
  };
};
