import React from 'react';
import { usePhoneStore } from '../../common/stores/phoneStateStore';
import { useNavigation } from '../../navigation/hooks';
import { useNavigationStore } from '../../navigation/navigationStore';
import NotificationSettingsScreen from './components/notifications/NotificationSettingsScreen';
import SoundSettingsScreen from './components/sounds/SoundSettingsScreen';
import UserProfileComponent from './components/UserProfileComponent';
import WiFiSettings from './components/WiFiSettings';
import { useWifiState } from './hooks/useWifiState';

interface BaseSettingType {
  id: number;
  name: string;
  icon: string;
  description: string;
  rightContent?: React.ReactNode;
}

interface ToggleSetting extends BaseSettingType {
  type: 'toggle';
  value: boolean;
  action?: () => void;
}

interface ActionSetting extends BaseSettingType {
  type: 'action' | 'page';
  action?: () => void;
}

type Setting = ToggleSetting | ActionSetting;

const Settings: React.FC = () => {
  const { isAirplaneModeOn, actions } = usePhoneStore();
  const { toggleAirplaneMode } = actions;
  const { isWifiOn, connectedWifi, toggleWifi } = useWifiState();
  const { openView } = useNavigation();
  const { currentView } = useNavigationStore();

  // Player data fetching removed

  if (currentView === 'notifications') {
    return <NotificationSettingsScreen onBack={() => openView('main')} />;
  }

  if (currentView === 'wifi') {
    return <WiFiSettings onBack={() => openView('main')} />;
  }

  if (currentView === 'sounds') {
    return <SoundSettingsScreen onBack={() => openView('main')} />;
  }

  const quickActions = [
    {
      icon: 'wifi',
      label: 'WiFi',
      active: isWifiOn,
      onClick: toggleWifi
    },
    {
      icon: isAirplaneModeOn ? 'plane' : 'plane-slash',
      label: 'Airplane',
      active: isAirplaneModeOn,
      onClick: toggleAirplaneMode
    },
    {
      icon: 'bell',
      label: 'Notif',
      active: false,
      onClick: () => openView('notifications')
    },
    {
      icon: 'volume-up',
      label: 'Sound',
      active: false,
      onClick: () => openView('sounds')
    }
  ];

  if (currentView === 'profile') {
    // Return to main view when profile editing is done
    return (
      <div className="h-full w-full flex flex-col bg-[#0a0f1a]">
        {/* Header with back button */}
        <div className="flex items-center px-4 py-3 border-b border-white/10">
          <button onClick={() => openView('main')} className="text-white/80 hover:text-white">
            <i className="fas fa-arrow-left"></i>
          </button>
          <h1 className="text-white font-medium ml-3">Edit Profile</h1>
        </div>
        {/* Profile content with scrolling */}
        <div className="flex-1 overflow-y-auto pt-4 pb-6">
          <UserProfileComponent />
        </div>
      </div>
    );
  }

  const settingsGroups: Array<{
    id: string;
    title: string;
    icon: string;
    settings: Setting[];
  }> = [
    {
      id: 'connectivity',
      title: 'Connectivity',
      icon: 'network-wired',
      settings: [
        {
          id: 1,
          name: 'WiFi',
          type: 'page',
          action: () => openView('wifi'),
          icon: 'wifi',
          description: '',
          rightContent: connectedWifi ? (
            <div className="text-xs text-white/50 mr-2">{connectedWifi}</div>
          ) : null
        }
      ]
    },
    {
      id: 'sounds',
      title: 'Sounds',
      icon: 'volume-up',
      settings: [
        {
          id: 6,
          name: 'Sound & Ringtone',
          type: 'page',
          action: () => openView('sounds'),
          icon: 'music',
          description: ''
        }
      ]
    },
    {
      id: 'privacy',
      title: 'Privacy',
      icon: 'shield-alt',
      settings: [
        {
          id: 4,
          name: 'Notifications',
          type: 'page',
          action: () => openView('notifications'),
          icon: 'bell',
          description: ''
        }
      ]
    }
  ];

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 pb-3">
      {/* Gradient Background */}
      <div className="absolute top-0 left-0 right-0 h-36 bg-gradient-to-b from-blue-500/10 to-transparent" />

      <div className="flex-grow overflow-y-auto relative">
        {/* Profile Section with inline actions */}
        <div className="mb-4">
          <UserProfileComponent />
          <div className="flex flex-col gap-2 px-3 mt-3">
            {/* Quick Actions */}
            <div className="grid grid-cols-4 w-full gap-[2%]">
              {quickActions.map(action => (
                <button
                  key={action.icon}
                  onClick={action.onClick}
                  className="relative w-full"
                >
                  <div
                    className={`
										w-full
										aspect-square
										rounded-xl
										flex flex-col
										items-center
										justify-center
										gap-[0.3rem]
										transition-all duration-300
										border
										${
                      action.active
                        ? 'bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-blue-500/30'
                        : 'bg-white/[0.03] border-white/[0.06] hover:bg-white/[0.06]'
                    }
									`}
                  >
                    <i
                      className={`
											fas fa-${action.icon}
											text-[0.9rem]
											transition-all duration-300
											${action.active ? 'text-blue-400' : 'text-white/40 group-hover:text-white/60'}
										`}
                    ></i>
                    <span
                      className={`
											text-[0.65rem]
											font-medium
											transition-colors duration-300
											${action.active ? 'text-blue-400' : 'text-white/40 group-hover:text-white/60'}
										`}
                    >
                      {action.label}
                    </span>
                  </div>
                  {action.active && (
                    <div className="absolute -inset-[2px] bg-blue-500/10 rounded-xl -z-10 blur-sm" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Settings Groups */}
        <div className="px-3 pb-4 space-y-3">
          {settingsGroups
            .filter(group => group.settings.length > 0)
            .map(group => (
              <div key={group.id} className="space-y-1.5">
                <div className="flex items-center gap-2 px-1">
                  <i className={`fas fa-${group.icon} text-white/40 w-4 text-sm text-center`}></i>
                  <h2 className="text-white/40 text-xs font-medium">{group.title}</h2>
                </div>
                <div className="bg-white/[0.06] backdrop-blur-xl rounded-lg overflow-hidden">
                  {group.settings.map((setting, idx) => (
                    <div
                      key={setting.id}
                      className={`flex items-center gap-2 p-2.5
												${idx !== group.settings.length - 1 ? 'border-b border-white/[0.06]' : ''}`}
                    >
                      <div className="w-7 h-7 rounded-lg bg-white/[0.06] flex items-center justify-center">
                        <i className={`fas fa-${setting.icon} text-white/80 text-sm`}></i>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-white text-sm font-medium">{setting.name}</span>
                          <div className="flex items-center">
                            {setting.rightContent}</div>
                          {setting.type === 'toggle' ? (
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                className="sr-only peer"
                                checked={setting.type === 'toggle' && setting.value}
                                onChange={() => setting.action && setting.action()}
                              />
                              <div
                                className="w-9 h-5 bg-white/10 rounded-full peer
																			peer-checked:bg-blue-500/80 peer-checked:after:translate-x-full
																			after:content-[''] after:absolute after:top-[2px] after:left-[2px]
																			after:bg-white after:rounded-full after:h-4 after:w-4
																			after:transition-all"
                              ></div>
                            </label>
                          ) : (
                            <button
                              onClick={() => setting.action && setting.action()}
                              className="w-7 h-7 rounded-lg bg-white/[0.06] hover:bg-white/10 flex items-center justify-center
																		transition-colors"
                            >
                              <i className="fas fa-chevron-right text-white/40 text-xs"></i>
                            </button>
                          )}
                        </div>
                        <p className="text-xs text-white/40 mt-0.5">{setting.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default Settings;
