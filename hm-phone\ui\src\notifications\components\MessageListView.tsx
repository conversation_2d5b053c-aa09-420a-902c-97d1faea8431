import React, { useState } from 'react';
import { NotificationData } from '../types/notificationTypes';
import { useNavigationStore } from '../../navigation/navigationStore';
import { usePhoneStore, PhoneStateEnum } from '../../common/stores/phoneStateStore';

interface MessageListViewProps {
  notifications: NotificationData[];
  formatTimeAgo: (timestamp: number) => string;
  skipFirstMessage?: boolean; // Option to skip the first message (which is shown in the header)
  closeDrawer?: () => void; // Function to close the drawer
  removeNotification?: (appId: number, notificationId: number) => void; // Function to remove a notification
}

const MessageListView: React.FC<MessageListViewProps> = ({
  notifications,
  formatTimeAgo,
  skipFirstMessage = true, // Default to skipping the first message
  closeDrawer,
  removeNotification
}) => {
  const [showAll, setShowAll] = useState(false);

  // Add navigation hooks for deep linking
  const { openAppView } = useNavigationStore();
  const phoneStore = usePhoneStore();

  // Handle deep linking to conversation
  const handleMessageClick = (notification: NotificationData, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling

    const conversationId = notification.metadata?.conversationId;
    if (conversationId) {
      // Close the drawer if provided
      if (closeDrawer) closeDrawer();

      // Dismiss the clicked notification
      if (removeNotification && notification.appId && notification.id) {
        removeNotification(notification.appId, notification.id);
      }

      // Make sure the phone is visible
      phoneStore.actions.setPhoneState(PhoneStateEnum.OPEN);

      // Navigate to the conversation
      openAppView('messages', 'conversation', { id: conversationId });
    }
  };

  // Sort messages by timestamp (oldest first)
  const sortedMessages = [...notifications].sort((a, b) => a.timestamp - b.timestamp);

  // Skip the first message if requested (since it's already shown in the header)
  const filteredMessages = skipFirstMessage && sortedMessages.length > 1
    ? sortedMessages.slice(1)
    : sortedMessages;

  // Show all messages or just the first 3
  const messagesToShow = showAll
    ? filteredMessages
    : filteredMessages.length > 3
      ? filteredMessages.slice(0, 3)
      : filteredMessages;

  return (
    <div className="px-3 pt-0 pb-2 max-h-60 overflow-y-auto">
      {/* Simple message list - just messages in a vertical stack */}
      <div className="space-y-1 pl-11 -mt-1"> {/* Indent to align with the message in header, remove top margin */}
        {messagesToShow.map(notification => (
          <div
            key={notification.id}
            className="flex justify-between items-start cursor-pointer hover:bg-white/5 rounded px-1"
            onClick={(e) => handleMessageClick(notification, e)}
          >
            <div className="text-white/80 text-xs flex-1 min-w-0 pr-2 break-words">
              {notification.message}
            </div>
            <div className="text-white/40 text-[10px] flex-shrink-0 ml-2 whitespace-nowrap">
              {formatTimeAgo(notification.timestamp)}
            </div>
          </div>
        ))}
      </div>

      {/* Show More/Less button */}
      {filteredMessages.length > 3 && (
        <div className="flex justify-center mt-2">
          <button
            onClick={e => {
              e.stopPropagation();
              setShowAll(!showAll);
            }}
            className="text-[#E2D0F9] text-[10px] hover:text-[#F0E6FA] flex items-center"
          >
            {showAll ? (
              <>
                <i className="fas fa-chevron-up mr-1 text-[8px]"></i>
                Show Less
              </>
            ) : (
              <>
                <i className="fas fa-chevron-down mr-1 text-[8px]"></i>
                Show More ({filteredMessages.length - 3} more)
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default MessageListView;
