// filepath: e:\Fivem\HMProject\resources\[hm]\hm-inventory\ui\src\stores\inventoryStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import {
  ItemSlot,
  Toast,
  ProgressBar,
  QuickAccessPreviewConfig,
  DEFAULT_QUICKACCESS_CONFIG,
} from '@shared';
import {
  sendDropItemRequest,
  sendEquipItemRequest,
} from '../hooks/useDndKitSlot';
import { handleSplitStack } from '../shared/inventoryHelpers';
import { ToastParams, ToastType } from '../utils/toastUtils';
import { resolveInventoryFromJSON } from '../../src/mockdata/playerInventory.mock';
import { mockGridItems, mockActionSlots } from '../../src/mockdata/playerInventory.mock'; // ADDED

export enum SecondaryPanelType {
  NONE = 'none',
  GROUND = 'ground',
  CONTAINER = 'container',
  CRAFTING = 'crafting',
  SHOP = 'shop',
  STASH = 'stash',
}

// Panels for drag-and-drop context
export type PanelType = 'main' | 'quickAccess' | 'action' | 'none' | 'ground' | 'container' | 'crafting' | 'shop' | 'stash';

// Ground panel item interface matching server
export interface GroundPanelItem {
  entityId: number;
  definitionId: string;
  quantity: number;
  distanceCategory: 'close' | 'nearby' | 'far';
  distance: number;
}

export interface InventoryState {
  gridItems: ItemSlot[];
  actionSlots: ItemSlot[];
  secondaryInventories: Record<string, { id: string; title: string; rows: number; columns: number; items: ItemSlot[] }>;
  secondaryPanel: SecondaryPanelType;
  groundItems: GroundPanelItem[];
  quickAccessConfig: QuickAccessPreviewConfig;
  toasts: Toast[];
  progressBars: ProgressBar[];
  pickupPrompt: string | null;
  isDragging: boolean;
  isMainVisible: boolean;
  isQuickAccessVisible: boolean;
  isSecondaryPanelVisible: boolean;
}

export interface InventoryActions {
  setDragging: (dragging: boolean) => void;
  setPickupPrompt: (prompt: string | null) => void;
  moveItem: (
    srcPanelType: PanelType,
    srcPanelId: string | undefined,
    srcIndex: number,
    dstPanelType: PanelType,
    dstPanelId: string | undefined,
    dstIndex: number,
    qty?: number
  ) => void;
  // Transfer items between any two panels (main, quickAccess, action, secondary)
  dropItem: (slotIndex: number, quantity: number) => void;
  equipItem: (sourceIndex: number) => void;
  // Split stack functionality
  splitStack: (slotIndex: number, quantity: number, panelType?: PanelType, panelId?: string) => void;
  // Inventory organization
  updateGridItems: (items: ItemSlot[]) => void;
  updateActionSlots: (items: ItemSlot[]) => void;
  updateGroundItems: (items: GroundPanelItem[]) => void;
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  // Standardized toast method
  showToast: (toastType: ToastType, params?: ToastParams) => void;
  addProgressBar: (progressBar: Omit<ProgressBar, 'id'>) => void;
  updateProgressBar: (id: string, progress: number) => void;
  removeProgressBar: (id: string) => void;
  loadSecondaryInventory: (id: string, title: string, rows: number, columns: number, items: ItemSlot[]) => void;
  updateSecondaryInventoryItems: (id: string, items: ItemSlot[]) => void;
  closeSecondaryInventory: (id: string) => void;
  openSecondaryPanel: (type: SecondaryPanelType) => void;
  closeSecondaryPanel: () => void;  // Granular visibility controls
  setMainVisible: (visible: boolean) => void;
  setQuickAccessVisible: (visible: boolean) => void;  setSecondaryPanelVisible: (visible: boolean) => void;
  setAllVisible: (visible: boolean) => void;
  setInventoryOpen: (open: boolean) => void;
  loadInventoryData: (inventoryData: ItemSlot[]) => void;
  // Helper selectors
  getIsAnyVisible: () => boolean;
  
  // QuickAccess configuration management
  updateQuickAccessConfig: (config: QuickAccessPreviewConfig) => void;
  
  // Stack validation helper  
  validateStackOperation: (destSlot: ItemSlot | undefined, srcSlot: ItemSlot, quantity: number) => { canStack: boolean; overflow: number; maxStackable: number };
}

export const useInventoryStore = create<InventoryState & InventoryActions>()(
  immer((set, get) => {    // Check if we're in development browser mode (not FiveM NUI)
    const isDevBrowser =  process.env.NODE_ENV === 'development';; // This function only exists in FiveM NUI

    let initialGridItems: ItemSlot[] = [];
    let initialActionSlots: ItemSlot[] = [];
    let initialIsMainVisible = false;
    let initialIsQuickAccessVisible = false;

    // Only use mock data in development browser, not in FiveM NUI
    if (isDevBrowser) {
      initialGridItems = mockGridItems;
      initialActionSlots = mockActionSlots;
      initialIsMainVisible = true;
      initialIsQuickAccessVisible = true; // Also show quick access in browser for dev
    }

    return {
      gridItems: initialGridItems,
      actionSlots: initialActionSlots,
      groundItems: [],
      secondaryInventories: {},
      secondaryPanel: SecondaryPanelType.NONE,
      quickAccessConfig: DEFAULT_QUICKACCESS_CONFIG,
      toasts: [],
      progressBars: [],
      pickupPrompt: null,
      isDragging: false,
      isMainVisible: initialIsMainVisible,
      isQuickAccessVisible: initialIsQuickAccessVisible,
      isSecondaryPanelVisible: true,

      setDragging: (dragging: boolean) => set((state) => { 
        state.isDragging = dragging; 
      }),
      
      setPickupPrompt: (prompt: string | null) => set((state) => {
        state.pickupPrompt = prompt;
      }),      loadInventoryData: (inventoryData: ItemSlot[]) => {
        try {
          console.log('[Store] Loading inventory data:', inventoryData);
          
          const resolvedItems = resolveInventoryFromJSON(inventoryData);
          console.log('[Store] Resolved inventory items:', resolvedItems);
          
          // Separate items by their index ranges
          const mainInventoryItems = resolvedItems.filter(item => item.index < 100);
          const actionSlotItems = resolvedItems.filter(item => item.index >= 100);
          
          console.log('[Store] Main inventory items:', mainInventoryItems);
          console.log('[Store] Action slot items:', actionSlotItems);
          
          // Convert action slot indices to 0-based (100 -> 0, 101 -> 1, etc.)
          const normalizedActionSlots = actionSlotItems.map(item => ({
            ...item,
            index: item.index - 100
          }));          
          set({
            gridItems: mainInventoryItems,
            actionSlots: normalizedActionSlots,
          });
          
          console.log('[Store] Inventory data loaded successfully');
        } catch (error) {
          console.error('[NUI] Error loading inventory data:', error);
        }
      },

      openSecondaryPanel: (type: SecondaryPanelType) => set((state) => {
        state.secondaryPanel = type;
        state.isSecondaryPanelVisible = true;
      }),    
      
      closeSecondaryPanel: () => set((state) => {
        state.secondaryPanel = SecondaryPanelType.NONE;
        state.isSecondaryPanelVisible = false;
      }),
      moveItem: (
        srcPanelType: PanelType,
        srcPanelId: string | undefined,
        srcIndex: number,
        dstPanelType: PanelType,
        dstPanelId: string | undefined,
        dstIndex: number,
        qty?: number
      ) => set((state) => {
        console.log('[Store] Moving item:', {
          src: { panelType: srcPanelType, panelId: srcPanelId, index: srcIndex },
          dst: { panelType: dstPanelType, panelId: dstPanelId, index: dstIndex },
          qty
        });

        // Helper function to get the correct slots array
        const getSlotsArray = (panelType: PanelType, panelId?: string) => {
          switch (panelType) {
            case 'main':
              return state.gridItems;
            case 'quickAccess':
              // QuickAccess is now config-based, not a separate array
              // For drag/drop operations involving quickAccess, redirect to main inventory
              return state.gridItems;
            case 'action':
              return state.actionSlots;
            default:
              if (panelId && state.secondaryInventories[panelId]) {
                return state.secondaryInventories[panelId].items;
              }
              return null;
          }
        };      const srcSlots = getSlotsArray(srcPanelType, srcPanelId);
        const dstSlots = getSlotsArray(dstPanelType, dstPanelId);

        // Validate arrays exist and indices are non-negative
        if (!srcSlots || !dstSlots || srcIndex < 0 || dstIndex < 0) {
          return; // Exit early if invalid
        }

        // Define maximum allowed slots per panel type
        const getMaxSlots = (panelType: PanelType) => {
          switch (panelType) {
            case 'main':
              return 100; // Main inventory slots 0-99
            case 'action':
              return 10;  // Action slots 0-9 (mapped to 100-109 on server)
            default:
              return 50;  // Default for secondary inventories
          }
        };

        // Validate indices are within allowed ranges
        if (srcIndex >= getMaxSlots(srcPanelType) || dstIndex >= getMaxSlots(dstPanelType)) {
          return; // Index out of bounds
        }

        // Ensure slots arrays are large enough (extend with empty slots if needed)
        while (srcSlots.length <= srcIndex) {
          srcSlots.push({
            index: srcSlots.length < 100 ? srcSlots.length : srcSlots.length + 100,
            item: undefined,
            quantity: 0
          });
        }
        while (dstSlots.length <= dstIndex) {
          const actualIndex = dstSlots === state.actionSlots ? dstSlots.length + 100 : dstSlots.length;
          dstSlots.push({
            index: actualIndex,
            item: undefined,
            quantity: 0
          });
        }

        const srcSlot = srcSlots[srcIndex];
        const dstSlot = dstSlots[dstIndex];

        // Validate source slot has an item
        if (!srcSlot?.item) {
          return; // Nothing to move
        }// Helper function to convert panel index to actual inventory index
        const getActualIndex = (panelType: PanelType, slotIndex: number) => {
          if (panelType === 'action') {
            return slotIndex + 100; // Action slots use indices 100+
          }
          return slotIndex; // Main inventory uses indices 0-99
        };

        // Calculate actual indices for server communication
        const srcActualIndex = getActualIndex(srcPanelType, srcIndex);
        const dstActualIndex = getActualIndex(dstPanelType, dstIndex);

        // Handle different move scenarios
        if (dstSlot?.item) {
          // Destination has an item - swap them
          const tempSrc = { ...srcSlot, index: dstActualIndex };
          const tempDst = { ...dstSlot, index: srcActualIndex };

          srcSlots[srcIndex] = tempDst;
          dstSlots[dstIndex] = tempSrc;
        } else {
          // Destination is empty - move item
          dstSlots[dstIndex] = { ...srcSlot, index: dstActualIndex };
          srcSlots[srcIndex] = {
            index: srcActualIndex,
            item: undefined,
            quantity: 0
          };
        }      // Handle quantity if specified (for partial moves)
        if (qty && qty < (srcSlot.quantity ?? 0) && dstSlot?.item === undefined) {
          // Partial move to empty slot
          dstSlots[dstIndex] = {
            index: dstActualIndex,
            item: srcSlot.item,
            quantity: qty
          };
          srcSlots[srcIndex] = {
            ...srcSlot,
            quantity: (srcSlot.quantity ?? 0) - qty
          };
        }
      }),

      dropItem: (slotIndex: number, qty: number) => {
        sendDropItemRequest({ action: 'dropItem', slotIndex, quantity: qty });
      },      
      equipItem: (srcIndex: number) => {
        sendEquipItemRequest({ action: 'equipItem', sourceSlotIndex: srcIndex });
      },

      // Split stack functionality
      splitStack: (slotIndex: number, quantity: number, panelType: PanelType = 'main', panelId?: string) => {
        set((state) => {
          // Use helper function to handle the split operation
          handleSplitStack(state, slotIndex, quantity, panelType, panelId);
        });
      },

      updateGridItems: (items: ItemSlot[]) => {
        set((state) => {
          state.gridItems = items;
        });
      },
      updateActionSlots: (items: ItemSlot[]) => {
        set((state) => {
          state.actionSlots = items;
        });
      },
      updateGroundItems: (items: GroundPanelItem[]) => {
        set((state) => {
          state.groundItems = items;
        });
      },

      addToast: (toast: Omit<Toast, 'id'>) => {
        const id = Date.now().toString();
        set((state) => { 
          state.toasts.push({ ...toast, id }); 
        });
        setTimeout(() => get().removeToast(id), toast.duration || 3000);
      },      
      removeToast: (id: string) => set((state) => { 
        state.toasts = state.toasts.filter((t) => t.id !== id); 
      }),
      
      showToast: (toastType: ToastType, params: ToastParams = {}) => {
        // Use the centralized createToast function from toastUtils.ts
        import('../utils/toastUtils').then(({ createToast }) => {
          createToast(toastType, params);
        });
      },

      addProgressBar: (progressBar: Omit<ProgressBar, 'id'>) => {
        const id = Date.now().toString();
        set((state) => { 
          state.progressBars.push({ ...progressBar, id }); 
        });
      },

      updateProgressBar: (id: string, progress: number) => set((state) => {
        const bar = state.progressBars.find(p => p.id === id);
        if (bar) {
          bar.progress = Math.max(0, Math.min(100, progress));
        }
      }),

      removeProgressBar: (id: string) => set((state) => { 
        state.progressBars = state.progressBars.filter((p) => p.id !== id); 
      }),      
      
      // Helper methods have been removed in favor of the centralized showToast method
      loadSecondaryInventory: (id: string, title: string, rows: number, columns: number, items: ItemSlot[]) => {
        set((state) => { 
          state.secondaryInventories[id] = { id, title, rows, columns, items }; 
        });
      },

      updateSecondaryInventoryItems: (id: string, items: ItemSlot[]) => {
        set((state) => { 
          if (state.secondaryInventories[id]) {
            state.secondaryInventories[id].items = items; 
          }
        });
      },

      closeSecondaryInventory: (id: string) => {
        set((state) => { 
          delete state.secondaryInventories[id]; 
        });
      },
      
      // Granular visibility controls
      setMainVisible: (visible: boolean) => set((state) => { 
        state.isMainVisible = visible; 
      }),

      setQuickAccessVisible: (visible: boolean) => set((state) => { 
        state.isQuickAccessVisible = visible; 
      }),      
      
      setSecondaryPanelVisible: (visible: boolean) => set((state) => { 
        state.isSecondaryPanelVisible = visible; 
      }),

      setAllVisible: (visible: boolean) => set((state) => { 
        state.isMainVisible = visible;
        state.isQuickAccessVisible = visible;
        state.isSecondaryPanelVisible = visible;
      }),

      setInventoryOpen: (open: boolean) => set((state) => {
        if (open) {
          // When opening inventory, show main inventory by default
          state.isMainVisible = true;
          // Keep current state of quick access and secondary panel
        } else {
          // When closing inventory, hide all components
          state.isMainVisible = false;
          state.isQuickAccessVisible = false;
          state.isSecondaryPanelVisible = false;
        }
      }),      
        getIsAnyVisible: () => {
        const state = get();
        return state.isMainVisible || state.isQuickAccessVisible || state.isSecondaryPanelVisible;
      },

      // QuickAccess configuration management
      updateQuickAccessConfig: (config: QuickAccessPreviewConfig) => set((state) => {
        state.quickAccessConfig = config;
      }),

      // Stack validation helper      
      validateStackOperation: (destSlot: ItemSlot | undefined, srcSlot: ItemSlot, quantity: number) => {
        // If destination slot is empty, operation is valid
        if (!destSlot || !destSlot.item) {
          return { canStack: false, overflow: 0, maxStackable: quantity };
        }

        // Check if items can be stacked (same definition ID)
        if (!srcSlot.item || destSlot.item.definitionId !== srcSlot.item.definitionId) {
          return { canStack: false, overflow: 0, maxStackable: 0 };
        }

        const maxStack = destSlot.item.maxStack || 1;
        const currentStack = destSlot.quantity || 0;
        const maxStackable = Math.max(0, maxStack - currentStack);
        const overflow = Math.max(0, quantity - maxStackable);        
        return {
          canStack: maxStackable > 0,
          overflow,
          maxStackable
        };
      }
    };
  })
);

// Helper function to check if a slot is configured for quickaccess preview
export const useQuickAccessHelper = () => {
  const quickAccessConfig = useInventoryStore((state) => state.quickAccessConfig);
  
  const getQuickAccessInfo = (panelType: 'main' | 'action', slotIndex: number) => {
    if (!quickAccessConfig?.visible) return null;
    
    const sourceType = panelType === 'main' ? 'main' : 'action';
    const quickSlot = quickAccessConfig.slots.find(
      slot => slot.enabled && slot.source === sourceType && slot.sourceSlot === slotIndex
    );
    
    return quickSlot ? { keyBinding: quickSlot.keyBinding } : null;
  };
  
  return { getQuickAccessInfo };
};
