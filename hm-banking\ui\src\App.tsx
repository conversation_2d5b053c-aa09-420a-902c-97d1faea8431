import React, { useEffect, memo, Suspense, lazy } from 'react';
import { useBankingStore } from './store/bankingStore';
import AccountSelector from './components/AccountSelector';

// Lazy load components for better performance
const Dashboard = lazy(() => import('./components/Dashboard'));
const TransactionHistory = lazy(() => import('./components/TransactionHistory'));

const App: React.FC = memo(() => {
  const isOpen = useBankingStore((state) => state.isOpen);
  const selectedAccountId = useBankingStore((state) => state.selectedAccountId);
  const isLoading = useBankingStore((state) => state.isLoading);
  const error = useBankingStore((state) => state.error);
  const fetchAccounts = useBankingStore((state) => state.fetchAccounts);
  const clearError = useBankingStore((state) => state.clearError);

  // Initialize app data when opened
  useEffect(() => {
    if (isOpen) {
      fetchAccounts();
    }
  }, [isOpen, fetchAccounts]);

  // Auto-dismiss errors after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  if (!isOpen) {
    return null;
  }
  return (    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-neutral-800 border border-neutral-700/50 border-l-4 border-l-green-400/80 rounded-lg shadow-2xl max-w-4xl w-full max-h-[75vh] overflow-hidden relative">
        {/* Green accent gradient */}
        <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-green-400/10 via-green-500/20 to-green-400/10 opacity-60 rounded-t-lg" />
        
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-700/50 relative z-10">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center border border-green-500/30">
              <i className="fas fa-university text-green-400 text-sm" />
            </div>
            <h1 className="text-xl font-semibold text-neutral-100">HM Banking</h1>
          </div>
          
          <button
            onClick={() => useBankingStore.getState().setOpen(false)}
            className="w-8 h-8 bg-neutral-700 hover:bg-red-500/20 text-neutral-400 hover:text-red-400 rounded-lg flex items-center justify-center transition-all border border-neutral-600/50 hover:border-red-500/30"
          >
            <i className="fas fa-times text-sm" />
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="m-4 bg-red-500/10 border border-red-500/30 border-l-4 border-l-red-400 rounded-lg p-3 flex items-center gap-3">
            <i className="fas fa-exclamation-triangle text-red-400" />
            <span className="text-red-200 text-sm">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300 transition-colors"
            >
              <i className="fas fa-times text-xs" />
            </button>
          </div>
        )}        {/* Loading State - Only show during actual operations */}
        {isLoading && (
          <div className="absolute inset-0 bg-neutral-800/80 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-neutral-700 border border-neutral-600/50 rounded-lg p-6 flex items-center gap-3">
              <div className="w-5 h-5 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin" />
              <span className="text-neutral-200 text-sm">Processing transaction...</span>
            </div>
          </div>
        )}        {/* Main Content */}
        <div className="flex h-[calc(75vh-120px)]">
          {/* Left Panel - Account Selection */}
          <div className="w-80 border-r border-neutral-700/50 flex flex-col">
            <div className="p-4">
              <h2 className="text-neutral-200 font-medium text-sm mb-3">Your Accounts</h2>
              <AccountSelector />
            </div>
          </div>          {/* Right Panel - Main Content */}
          <div className="flex-1 flex flex-col min-h-0">
            {selectedAccountId ? (
              <Suspense fallback={
                <div className="flex-1 flex items-center justify-center">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin" />
                    <span className="text-neutral-200 text-sm">Loading...</span>
                  </div>
                </div>
              }>                {/* Dashboard */}
                <div className="border-b border-neutral-700/50 flex-shrink-0">
                  <Dashboard />
                </div>
                
                {/* Transaction History with integrated "view more" */}
                <div className="flex-1 min-h-0"> {/* Removed overflow-y-auto here */}
                  <TransactionHistory />
                </div>
              </Suspense>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4 border border-green-500/20">
                    <i className="fas fa-credit-card text-green-400 text-xl" />
                  </div>
                  <h3 className="text-neutral-200 font-medium mb-2">Select an Account</h3>
                  <p className="text-neutral-400 text-sm max-w-sm">
                    Choose an account from the left panel to view your balance and transaction history.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>    </div>
  );
});

App.displayName = 'App';

export default App;
