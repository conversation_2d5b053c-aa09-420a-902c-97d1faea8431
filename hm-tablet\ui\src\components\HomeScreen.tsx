import React from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCalculator, 
  faNoteSticky, 
  faAddressBook, 
  faImages, 
  faMap, 
  faGear,
  faSquare
} from '@fortawesome/free-solid-svg-icons';
import { useTabletStore } from '../stores/tabletStore';
import { TabletApp } from '@shared';

const HomeScreen: React.FC = () => {
  const { installedApps, openApp } = useTabletStore();

  const getAppIcon = (appId: string) => {
    switch (appId) {
      case 'calculator': return faCalculator;
      case 'notes': return faNoteSticky;
      case 'contacts': return faAddressBook;
      case 'gallery': return faImages;
      case 'maps': return faMap;
      case 'settings': return faGear;
      default: return faSquare;
    }
  };

  const visibleApps = installedApps.filter(app => app.isVisible);  return (
    <div className="p-4 h-full">      {/* App Grid */}
      <div className="grid grid-cols-10 gap-6 justify-items-start">
        {visibleApps.map((app, index) => (
          <AppIcon 
            key={app.id} 
            app={app} 
            icon={getAppIcon(app.id)}
            delay={index * 0.05}
            onOpen={() => openApp(app.id)}
          />
        ))}
      </div>
    </div>
  );
};

interface AppIconProps {
  app: TabletApp;
  icon: any;
  delay: number;
  onOpen: () => void;
}

const AppIcon: React.FC<AppIconProps> = ({ app, icon, delay, onOpen }) => {
  return (
    <motion.button
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ delay, duration: 0.3, ease: "easeOut" }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onOpen}
      className="flex flex-col items-center gap-2 p-2 rounded-xl hover:bg-tablet-surface/50 transition-all duration-200"
    >
      <div className="app-icon">
        <FontAwesomeIcon icon={icon} className="text-tablet-accent-primary" />
      </div>
      <span className="text-xs text-tablet-text-primary font-medium truncate w-full text-center">
        {app.name}
      </span>
    </motion.button>
  );
};

export default HomeScreen;
