import { create } from 'zustand';
import { Contact, Conversation, ConversationMember, Message } from '../../../../../shared/types';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { isBrowser } from '../../../utils/environment';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { messagesMockData } from '../../../fivem';

interface MessagesState {
  // Core state
  conversations: Conversation[];
  messagesById: Record<number, Message[]>; // Store messages by conversation ID
  loading: boolean;
  activeChat: number | null;

  // Pagination state
  conversationsPage: number;
  hasMoreConversations: boolean;
  messagesPages: Record<number, number>; // conversationId -> page
  hasMoreMessages: Record<number, boolean>; // conversationId -> hasMore

  // Actions
  actions: {
    getConversations: () => Promise<void>;
    loadMoreConversations: () => Promise<void>;
    loadMessages: (conversationId: number | string) => Promise<void>;
    loadMoreMessages: (conversationId: number | string) => Promise<void>;
    sendMessage: (conversationId: number | string, message: Partial<Message>) => Promise<void>;
    createConversation: (contacts: Contact[], initialMessage: Message) => Promise<number | null>;
    updateGroupImage: (conversationId: number | string, imageUrl: string) => Promise<void>;
    updateGroupName: (conversationId: number | string, name: string) => Promise<void>;
    toggleMute: (conversationId: number | string) => Promise<void>;
    addGroupMember: (conversationId: number | string, phoneNumber: string) => Promise<void>;
    removeGroupMember: (conversationId: number | string, phoneNumber: string) => Promise<void>;
  };

  // Handlers
  handlers: {
    onSetConversations: (conversations: Conversation[]) => void;
    onAddConversations: (conversations: Conversation[]) => void;
    onUpdateConversation: (conversation: Conversation) => void;
    onMessageSent: (message: Message) => void;
    onConversationCreated: (conversation: Conversation) => number | string | null;
    onSetMessages: (conversationId: number | string, messages: Message[]) => void;
    onAddMessages: (conversationId: number | string, messages: Message[]) => void;
  };

  // UI methods
  ui: {
    markAsRead: (chatId: number | string) => void;
    setActiveChat: (chatId: number | string | null) => void;
  };
}
export const useMessagesStore = create<MessagesState>((set, get) => ({
  // State
  conversations: [],
  messagesById: {}, // Store messages by conversation ID
  activeChat: null,
  loading: false,

  // Pagination state
  conversationsPage: 0,
  hasMoreConversations: true,
  messagesPages: {},
  hasMoreMessages: {},

  // Actions to fetch data from the server
  actions: {
    getConversations: async () => {
      const state = get();

      // Skip if already loading
      if (state.loading) return;

      set({ loading: true, conversationsPage: 0, hasMoreConversations: true, conversations: [] });

      try {
        const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
        if (!phoneNumber) {
          console.error('[MessagesStore] Cannot get conversations: Player phone number not available');
          set({ loading: false });
          return;
        }

        // Sort mock data by updated_at (newest first)
        const sortedMockData = [...messagesMockData.conversations].sort((a, b) => {
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        });

        // Send request to server
        const response = await clientRequests.send(
          'messages',
          'getConversations',
          {
            phoneNumber,
            limit: 20,
            offset: 0
          },
          sortedMockData.slice(0, 20),
          state.handlers.onSetConversations as (data: unknown) => void
        );

        // Handle response
        if (response.success && 'data' in response) {
          if (!isBrowser) {
            const conversations = response.data as Conversation[];

            get().handlers.onSetConversations(conversations);
          }

          // Update pagination state
          const receivedCount = (response.data as Conversation[]).length;
          set({
            hasMoreConversations: receivedCount === 20,
            conversationsPage: 0
          });
        } else {
          console.error('[MessagesStore] Failed to get initial conversations:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error getting conversations:', error);
      } finally {
        set({ loading: false });
      }
    },

    loadMoreConversations: async () => {
      const state = get();

      // Skip if already loading or no more conversations
      if (state.loading || !state.hasMoreConversations) {

        return;
      }

      const nextPage = state.conversationsPage + 1;
      set({ loading: true });

      try {
        const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
        if (!phoneNumber) {
          console.error('[MessagesStore] Cannot load more conversations: Player phone number not available');
          set({ loading: false });
          return;
        }

        // Calculate the offset based on the current page
        const offset = nextPage * 20;

        // Sort mock data by updated_at (newest first)
        const sortedMockData = [...messagesMockData.conversations].sort((a, b) => {
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        });

        // Send request to server
        const response = await clientRequests.send(
          'messages',
          'getConversations',
          {
            phoneNumber,
            limit: 20,
            offset
          },
          sortedMockData.slice(offset, offset + 20),
          state.handlers.onAddConversations as (data: unknown) => void
        );

        // Handle response
        if (response.success && 'data' in response) {
          if (!isBrowser) {
            get().handlers.onAddConversations(response.data as Conversation[]);
          }

          // Update pagination state
          const receivedConversations = response.data as Conversation[];

          set({
            conversationsPage: nextPage,
            hasMoreConversations: receivedConversations.length === 20,
            loading: false
          });
        } else {
          console.error('[MessagesStore] Failed to load more conversations:', response);
          set({ loading: false });
        }
      } catch (error) {
        console.error('[MessagesStore] Error loading more conversations:', error);
        set({ loading: false });
      }
    },

    loadMessages: async (conversationId: number | string) => {
      const state = get();
      if (state.loading) {

        return Promise.resolve(); // Return a resolved promise for chaining
      }

      set({ loading: true });

      return new Promise<void>((resolve) => {
        try {
          // Reset messages pagination for this conversation
          set({
            messagesPages: { ...state.messagesPages, [conversationId]: 0 },
            hasMoreMessages: { ...state.hasMoreMessages, [conversationId]: true }
          });



          // Find messages for this conversation in the separate messages array
          // Convert both to numbers for comparison to handle string/number type mismatches
          const numericConversationId = typeof conversationId === 'string' ? parseInt(conversationId, 10) : Number(conversationId);
          const mockMessages = messagesMockData.messages.filter(m => Number(m.conversation_id) === numericConversationId) || [];

          console.log(`[MessagesStore] Found ${mockMessages.length} messages for conversation ${conversationId} (numeric: ${numericConversationId}):`, mockMessages);

          // Sort mock messages by timestamp (newest first) for initial load
          mockMessages.sort((a, b) => {
            const timestampA = new Date(a.timestamp).getTime();
            const timestampB = new Date(b.timestamp).getTime();
            return timestampB - timestampA; // Newest first for initial load
          });

          clientRequests.send(
            'messages',
            'getMessages',
            {
              conversationId,
              limit: 20,
              offset: 0
            },
            mockMessages,
            // Create a wrapper function that calls onSetMessages with both parameters
            (data: unknown) => state.handlers.onSetMessages(conversationId, data as Message[])
          ).then(response => {

            if (response.success && 'data' in response) {
              if (!isBrowser) {
                get().handlers.onSetMessages(conversationId, response.data as Message[]);
              }

              // Check if there are more messages
              set({
                hasMoreMessages: {
                  ...state.hasMoreMessages,
                  [conversationId]: (response.data as Message[]).length === 20
                },
                loading: false
              });
            }
            resolve();
          }).catch(error => {
            console.error('[MessagesStore] Error loading messages:', error);
            set({ loading: false });
            resolve();
          });
        } catch (error) {
          console.error('[MessagesStore] Error in loadMessages:', error);
          set({ loading: false });
          resolve();
        }
      });
    },

    loadMoreMessages: async (conversationId: number | string) => {
      const state = get();
      if (state.loading || !state.hasMoreMessages[conversationId as number]) {

        return Promise.resolve(); // Return a resolved promise for chaining
      }

      const currentPage = state.messagesPages[conversationId as number] || 0;
      const nextPage = currentPage + 1;
      set({ loading: true });

      return new Promise<void>((resolve) => {
        try {


          // Find messages for this conversation in the separate messages array with pagination
          // Convert both to numbers for comparison to handle string/number type mismatches
          const numericConversationId = typeof conversationId === 'string' ? parseInt(conversationId, 10) : Number(conversationId);
          const allMessages = messagesMockData.messages.filter(m => Number(m.conversation_id) === numericConversationId) || [];
          const mockMessages = allMessages.slice(nextPage * 20, (nextPage + 1) * 20);

          console.log(`[MessagesStore] Found ${allMessages.length} messages for loadMoreMessages (conversation ${conversationId}, numeric: ${numericConversationId}):`, allMessages);

          // Sort mock messages by timestamp (oldest first) for proper pagination
          mockMessages.sort((a, b) => {
            const timestampA = new Date(a.timestamp).getTime();
            const timestampB = new Date(b.timestamp).getTime();
            return timestampA - timestampB;
          });

          clientRequests.send(
            'messages',
            'getMessages',
            {
              conversationId,
              limit: 20,
              offset: nextPage * 20
            },
            mockMessages,
            (data: unknown) => state.handlers.onAddMessages(conversationId, data as Message[])
          ).then(response => {

            if (response.success && 'data' in response) {
              if (!isBrowser) {
                get().handlers.onAddMessages(conversationId, response.data as Message[]);
              }

              // Update pagination state
              set({
                messagesPages: { ...state.messagesPages, [conversationId]: nextPage },
                hasMoreMessages: {
                  ...state.hasMoreMessages,
                  [conversationId]: (response.data as Message[]).length === 20
                },
                loading: false
              });
            }
            resolve();
          }).catch(error => {
            console.error('[MessagesStore] Error loading more messages:', error);
            set({ loading: false });
            resolve();
          });
        } catch (error) {
          console.error('[MessagesStore] Error in loadMoreMessages:', error);
          set({ loading: false });
          resolve();
        }
      });
    },
    sendMessage: async (conversationId: number | string, message: Partial<Message>) => {
      try {
        // Get the player's phone number
        const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
        if (!phoneNumber) {
          console.error('[MessagesStore] Cannot send message: Player phone number not available');
          return;
        }

        // Convert conversationId to number if it's a string
        const numericConversationId = typeof conversationId === 'string'
          ? parseInt(conversationId, 10)
          : conversationId;

        // Create a proper Message object based on the message type
        // In the shared types, metadata is a string or null, but we need to send an object
        // The server will stringify it before storing in the database

        // Create the base message properties
        const baseMessage = {
          id: 0, // Will be assigned by the server
          conversation_id: numericConversationId,
          sender: phoneNumber,
          message: message.message || '',
          timestamp: new Date().toISOString(),
          is_deleted: 0
        };

        // Create the message object based on the type
        let messageToSend: Record<string, unknown>;

        if (message.type === 'text' || !message.type) {
          // Create a text message
          messageToSend = {
            ...baseMessage,
            type: 'text',
            metadata: JSON.stringify({ type: 'text' })
          };
        } else if (message.type === 'image') {
          // Create an image message
          messageToSend = {
            ...baseMessage,
            type: 'image',
            metadata: JSON.stringify({
              type: 'image',
              url: message.metadata?.url || ''
            })
          };
        } else if (message.type === 'location') {
          // Create a location message
          messageToSend = {
            ...baseMessage,
            type: 'location',
            metadata: JSON.stringify({
              type: 'location',
              x: message.metadata?.x || 0,
              y: message.metadata?.y || 0,
              z: message.metadata?.z || 0
            })
          };
        } else if (message.type === 'contact') {
          // Create a contact message
          messageToSend = {
            ...baseMessage,
            type: 'contact',
            metadata: JSON.stringify({
              type: 'contact',
              contact_id: message.metadata?.contact_id || 0
            })
          };
        } else {
          // Default to text message for unknown types
          messageToSend = {
            ...baseMessage,
            type: 'text',
            metadata: JSON.stringify({ type: 'text' })
          };
        }

        // Send request to server
        const response = await clientRequests.send(
          'messages',
          'sendMessage',
          messageToSend,
          null,
          get().handlers.onMessageSent as (data: unknown) => void
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to send message:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error sending message:', error);
      }
    },

    createConversation: async (contacts: Contact[], initialMessage: Message): Promise<number | null> => {
      try {
        const state = get();
        // Get the player's phone number
        const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
        if (!phoneNumber) {
          console.error('[MessagesStore] Cannot create conversation: Player phone number not available');
          return null;
        }
        const userName = usePhoneStore.getState().userProfile.name || '';
        // const stateid = usePhoneStore.getState().userProfile.stateid || '';
        const type = contacts.length > 1 ? 'group' : 'direct';

        const ConversationMembers: Record<string, ConversationMember> = {
          [phoneNumber]: {
            display_name: userName, // Or get from user profile
            unread_count: 0,
            is_muted: false,
            is_pinned: false,
            is_admin: type === 'group' ? true : false, // Current user is admin
            joined_at: new Date().toISOString(),
            left_at: null
          }
        };
        contacts.forEach((contact: Contact) => {
          ConversationMembers[contact.number] = {
            display_name: contact.name,
            unread_count: 0,
            is_muted: false,
            is_pinned: false,
            is_admin: false,
            joined_at: new Date().toISOString(),
            left_at: null
          };
        });
        const conversation: Conversation = {
          id: 0, // Will be assigned by the server
          name: type === 'group' ? 'Group Chat' : contacts[0].name,
          type: type,
          members: ConversationMembers,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          latest_message: initialMessage,
          // Set default group icon for group conversations, otherwise use contact avatar
          avatar: type === 'group' ? 'group_icon' : contacts[0].avatar || '',
        };
        // Send request to server
        const response = await clientRequests.create(
          'messages',
          'createConversation',
          conversation as unknown as Record<string, unknown>,
          state.conversations as unknown as Record<string, unknown>[],
          state.handlers.onConversationCreated as (data: unknown) => void
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to create conversation:', response);
          return null;
        }

        // Get the conversation ID from the response
        const conversationId = 'data' in response && response.data ? Number(response.data) : null;

        if (conversationId !== null && initialMessage && initialMessage.message) {
          // Send the initial message to this conversation
          await state.actions.sendMessage(conversationId, initialMessage);
        }

        return conversationId;
      } catch (error) {
        console.error('[MessagesStore] Error creating conversation:', error);
        return null;
      }
    },

    updateGroupImage: async (conversationId: number | string, imageUrl: string) => {


      if (!conversationId || !imageUrl) {
        console.error('[MessagesStore] Invalid parameters for updateGroupImage');
        return;
      }

      const state = get();

      // Find the conversation
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) {
        console.error('[MessagesStore] Conversation not found:', conversationId);
        return;
      }

      // Check if it's a group conversation
      if (conversation.type !== 'group') {
        console.error('[MessagesStore] Cannot update image for non-group conversation');
        return;
      }

      // Create updated conversation object
      const updatedConversation = {
        ...conversation,
        avatar: imageUrl
      };

      // Update the UI immediately for a responsive experience
      state.handlers.onUpdateConversation(updatedConversation);

      try {
        // Use clientRequests.update for a more targeted update
        const response = await clientRequests.update(
          'messages',
          'Conversation',
          Number(conversationId), // Convert to number for clientRequests.update
          { avatar: imageUrl },
          // Use the onUpdateConversation handler for browser mode
          () => state.handlers.onUpdateConversation(updatedConversation)
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to update group image:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error updating group image:', error);
      }
    },

    updateGroupName: async (conversationId: number | string, name: string) => {


      if (!conversationId || !name.trim()) {
        console.error('[MessagesStore] Invalid parameters for updateGroupName');
        return;
      }

      const state = get();

      // Find the conversation
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) {
        console.error('[MessagesStore] Conversation not found:', conversationId);
        return;
      }

      // Check if it's a group conversation
      if (conversation.type !== 'group') {
        console.error('[MessagesStore] Cannot update name for non-group conversation');
        return;
      }

      // Create updated conversation object
      const updatedConversation = {
        ...conversation,
        name: name.trim()
      };

      // Update the UI immediately for a responsive experience
      state.handlers.onUpdateConversation(updatedConversation);

      try {
        // Use clientRequests.update for a more targeted update
        const response = await clientRequests.update(
          'messages',
          'Conversation',
          Number(conversationId), // Convert to number for clientRequests.update
          { name: name.trim() },
          // Use the onUpdateConversation handler for browser mode
          () => state.handlers.onUpdateConversation(updatedConversation)
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to update group name:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error updating group name:', error);
      }
    },

    toggleMute: async (conversationId: number | string) => {


      if (!conversationId) {
        console.error('[MessagesStore] Invalid conversationId for toggleMute');
        return;
      }

      const state = get();

      // Find the conversation
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) {
        console.error('[MessagesStore] Conversation not found:', conversationId);
        return;
      }

      // Get the player's phone number
      const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
      if (!phoneNumber) {
        console.error('[MessagesStore] Cannot toggle mute: Player phone number not available');
        return;
      }

      // Get the current mute state from members
      const members = conversation.members || {};
      const currentMember = members[phoneNumber] || {};
      const isMuted = currentMember.is_muted || false;
      const newMutedStatus = !isMuted;



      // Create updated members
      const updatedMembers = { ...members };
      if (updatedMembers[phoneNumber]) {
        updatedMembers[phoneNumber] = {
          ...updatedMembers[phoneNumber],
          is_muted: newMutedStatus
        };
      } else {
        updatedMembers[phoneNumber] = {
          display_name: conversation.name || '',
          unread_count: 0,
          is_muted: newMutedStatus,
          is_pinned: false,
          is_admin: false,
          joined_at: new Date().toISOString(),
          left_at: null
        };
      }

      // Create updated conversation object
      const updatedConversation = {
        ...conversation,
        members: updatedMembers
      };

      // Update the UI immediately for a responsive experience
      state.handlers.onUpdateConversation(updatedConversation);

      try {
        // Use clientRequests.update for a more targeted update
        const response = await clientRequests.update(
          'messages',
          'Conversation',
          Number(conversationId), // Convert to number for clientRequests.update
          { members: updatedMembers },
          // Use the onUpdateConversation handler for browser mode
          () => state.handlers.onUpdateConversation(updatedConversation)
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to update mute status:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error updating mute status:', error);
      }
    },

    addGroupMember: async (conversationId: number | string, phoneNumber: string) => {


      if (!conversationId || !phoneNumber) {
        console.error('[MessagesStore] Invalid parameters for addGroupMember');
        return;
      }

      const state = get();

      // Find the conversation
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) {
        console.error('[MessagesStore] Conversation not found:', conversationId);
        return;
      }

      // Check if it's a group conversation
      if (conversation.type !== 'group') {
        console.error('[MessagesStore] Cannot add member to non-group conversation');
        return;
      }

      // Check if member already exists
      const members = conversation.members || {};
      if (members[phoneNumber] && !members[phoneNumber].left_at) {
        console.error('[MessagesStore] Member already exists in group:', phoneNumber);
        return;
      }

      // Create updated members object
      const updatedMembers = { ...members };
      updatedMembers[phoneNumber] = {
        display_name: '', // Can be updated later
        unread_count: 0,
        is_muted: false,
        is_pinned: false,
        is_admin: false,
        joined_at: new Date().toISOString(),
        left_at: null
      };

      // Create updated conversation object
      const updatedConversation = {
        ...conversation,
        members: updatedMembers
      };

      // Update the UI immediately for a responsive experience
      state.handlers.onUpdateConversation(updatedConversation);

      try {
        // Send request to server
        const response = await clientRequests.send(
          'messages',
          'addGroupMember',
          {
            conversationId,
            phoneNumber
          },
          null
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to add member to group:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error adding member to group:', error);
      }
    },

    removeGroupMember: async (conversationId: number | string, phoneNumber: string) => {


      if (!conversationId || !phoneNumber) {
        console.error('[MessagesStore] Invalid parameters for removeGroupMember');
        return;
      }

      const state = get();

      // Find the conversation
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) {
        console.error('[MessagesStore] Conversation not found:', conversationId);
        return;
      }

      // Check if it's a group conversation
      if (conversation.type !== 'group') {
        console.error('[MessagesStore] Cannot remove member from non-group conversation');
        return;
      }

      // Check if member exists
      const members = conversation.members || {};
      if (!members[phoneNumber] || members[phoneNumber].left_at) {
        console.error('[MessagesStore] Member not found in group:', phoneNumber);
        return;
      }

      // Create updated members object
      const updatedMembers = { ...members };
      if (updatedMembers[phoneNumber]) {
        updatedMembers[phoneNumber] = {
          ...updatedMembers[phoneNumber],
          left_at: new Date().toISOString()
        };
      }

      // Create updated conversation object
      const updatedConversation = {
        ...conversation,
        members: updatedMembers
      };

      // Update the UI immediately for a responsive experience
      state.handlers.onUpdateConversation(updatedConversation);

      try {
        // Use clientRequests.update for a more targeted update
        const response = await clientRequests.update(
          'messages',
          'Conversation',
          Number(conversationId), // Convert to number for clientRequests.update
          { members: updatedMembers },
          // Use the onUpdateConversation handler for browser mode
          () => state.handlers.onUpdateConversation(updatedConversation)
        );

        if (!response.success) {
          console.error('[MessagesStore] Failed to remove member from group:', response);
        }
      } catch (error) {
        console.error('[MessagesStore] Error removing member from group:', error);
      }
    }
  },
  // Handlers for NUI callbacks
  handlers: {
    onSetConversations: (conversations: Conversation[]) => {


      // Validate that conversations is an array
      if (!Array.isArray(conversations)) {
        console.error('[MessagesStore] onSetConversations received non-array data:', conversations);

        // If it's an object, try to wrap it in an array
        if (conversations && typeof conversations === 'object') {


          conversations = [conversations as Conversation];
        } else {
          set({ conversations: [] }); // Set empty array to prevent errors
          return;
        }
      }

      // Get the player's phone number
      const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;

      // Validate each conversation to ensure required properties exist and arrays are properly initialized
      const validatedConversations = conversations.map(conversation => {
        // Log each conversation being processed

        // Ensure members is an object
        const members = conversation.members || {};

        // Get current player's member data if available
        const currentMember = phoneNumber ? members[phoneNumber] : null;
        const memberSettings = currentMember || {
          display_name: conversation.name || '',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: conversation.created_at || new Date().toISOString(),
          left_at: null
        };

        // Ensure other required properties have default values
        const validatedConversation = {
          ...conversation,
          id: conversation.id || 0,
          name: conversation.name || '',
          type: conversation.type || 'direct',
          members: members,
          created_at: conversation.created_at || new Date().toISOString(),
          updated_at: conversation.updated_at || new Date().toISOString(),
          latest_message: conversation.latest_message,

          // UI-derived properties
          displayName: memberSettings.display_name,
          isGroup: conversation.type === 'group',
          unread: memberSettings.unread_count,
          muted: memberSettings.is_muted,
          messages: [] // Initialize empty messages array for UI
        };
        return validatedConversation;
      });

      // Sort ALL conversations by updated_at (newest first) before limiting
      validatedConversations.sort((a, b) => {
        // Ensure we're parsing dates correctly
        const dateA = new Date(a.updated_at).getTime();
        const dateB = new Date(b.updated_at).getTime();

        // Log any invalid dates for debugging
        if (isNaN(dateA)) {
          console.error(`[MessagesStore] Invalid date for conversation ${a.id}: ${a.updated_at}`);
        }
        if (isNaN(dateB)) {
          console.error(`[MessagesStore] Invalid date for conversation ${b.id}: ${b.updated_at}`);
        }

        return dateB - dateA; // Most recent first
      });

      // Limit to 20 conversations AFTER sorting
      const limitedConversations = validatedConversations.length > 20
        ? validatedConversations.slice(0, 20)
        : validatedConversations;


      set({
        conversations: limitedConversations,
        conversationsPage: 0,
        hasMoreConversations: validatedConversations.length > limitedConversations.length
      });
    },

    onAddConversations: (newConversations: Conversation[]) => {
      if (!Array.isArray(newConversations)) {
        console.error('[MessagesStore] Expected array but got:', typeof newConversations);
        return;
      }

      const state = get();

      // Get the player's phone number
      const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;

      // Process each conversation to ensure it has all required fields
      const processedConversations = newConversations.map(conversation => {
        // Ensure members is an object
        const members = conversation.members || {};

        // Get current player's member data if available
        const currentMember = phoneNumber ? members[phoneNumber] : null;
        const memberSettings = currentMember || {
          display_name: conversation.name || '',
          unread_count: 0,
          is_muted: false,
          is_pinned: false,
          is_admin: false,
          joined_at: conversation.created_at || new Date().toISOString(),
          left_at: null
        };

        // Create a properly formatted conversation object
        return {
          ...conversation,
          id: conversation.id || 0,
          name: conversation.name || '',
          type: conversation.type || 'direct',
          members: members,
          created_at: conversation.created_at || new Date().toISOString(),
          updated_at: conversation.updated_at || new Date().toISOString(),
          latest_message: conversation.latest_message,

          // UI-specific properties
          displayName: memberSettings.display_name,
          isGroup: conversation.type === 'group',
          unread: memberSettings.unread_count,
          muted: memberSettings.is_muted,
          messages: [] // Initialize empty messages array for UI
        };
      });

      // Filter out conversations that are already in the state
      const existingIds = new Set(state.conversations.map(c => c.id));
      const newFilteredConversations = processedConversations.filter(c => !existingIds.has(c.id));

      if (newFilteredConversations.length === 0) {
        return;
      }

      // SIMPLIFIED APPROACH: Just append the new conversations to the existing ones
      // and sort the combined list to ensure proper order
      const updatedConversations = [...state.conversations, ...newFilteredConversations];

      // Sort by updated_at (newest first)
      updatedConversations.sort((a, b) => {
        // Ensure we're parsing dates correctly
        const dateA = new Date(a.updated_at).getTime();
        const dateB = new Date(b.updated_at).getTime();

        // Log any invalid dates for debugging
        if (isNaN(dateA)) {
          console.error(`[MessagesStore] Invalid date for conversation ${a.id}: ${a.updated_at}`);
        }
        if (isNaN(dateB)) {
          console.error(`[MessagesStore] Invalid date for conversation ${b.id}: ${b.updated_at}`);
        }

        return dateB - dateA; // Most recent first
      });

      // Update the state with the combined, sorted list
      set({ conversations: updatedConversations });
    },

    onUpdateConversation: (updatedConversation: Conversation) => {
      set(state => {
        // Ensure ID is numeric
        const numericId = Number(updatedConversation.id);
        updatedConversation.id = numericId;

        // Get the player's phone number
        const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;

        // Get current player's member data if available
        const members = updatedConversation.members || {};
        const currentMember = phoneNumber ? members[phoneNumber] : null;

        // Add UI-specific properties
        const conversationWithUIProps = {
          ...updatedConversation,
          displayName: currentMember?.display_name || updatedConversation.name || '',
          isGroup: updatedConversation.type === 'group',
          unread: currentMember?.unread_count || 0,
          muted: currentMember?.is_muted || false
        };

        // Check if the conversation already exists
        const existingConversation = state.conversations.find(c => c.id === numericId);

        let updatedConversations;

        if (existingConversation) {
          // Update existing conversation
          updatedConversations = state.conversations.map(conv =>
            conv.id === numericId ? conversationWithUIProps : conv
          );
        } else {
          // Add new conversation
          updatedConversations = [...state.conversations, conversationWithUIProps];
        }

        return { conversations: updatedConversations };
      });
    },
    onMessageSent: (message: Message) => {
      // Ensure we have a valid conversation_id
      if (!message.conversation_id) {
        console.error('[MessagesStore] Missing conversation_id in message:', message);
        return;
      }

      // Ensure conversation ID is numeric
      const numericId = Number(message.conversation_id);
      set(state => {
        // Get existing messages for this conversation
        const existingMessages = state.messagesById[numericId] || [];

        // Filter out any temporary messages that match this one
        const filteredMessages = existingMessages.filter(msg => {
          // Check if this is a temporary message with the same content
          return !(
            (msg as unknown as Record<string, unknown>).status === 'sending' &&
            (msg.message === message.message)
          );
        });

        // Create a proper Message object
        // Use type assertion to avoid type errors with the union type
        const newMessage = {
          id: Number(message.id) || 0,
          conversation_id: numericId,
          sender: String(message.sender) || '',
          message: String(message.message) || '',
          type: message.type || 'text',
          metadata: message.metadata,
          timestamp: message.timestamp,
          is_deleted: message.is_deleted || 0
        } as Message;

        // Add the new message to the filtered messages
        const updatedMessages = [...filteredMessages, newMessage];

        // Sort messages by timestamp (oldest first) for proper chronological display
        const sortedMessages = [...updatedMessages].sort((a, b) => {
          const timestampA = new Date(a.timestamp).getTime();
          const timestampB = new Date(b.timestamp).getTime();
          return timestampA - timestampB; // Oldest first for chronological display
        });

        // Update the messagesById state with sorted messages
        return {
          messagesById: {
            ...state.messagesById,
            [numericId]: sortedMessages
          }
        };
      });

      // Also update the conversation's latest_message
      set(state => {
        const updatedConversations = state.conversations.map(conv => {
          if (conv.id === numericId) {
            return {
              ...conv,
              latest_message: message,
              updated_at: new Date().toISOString()
            };
          }
          return conv;
        });
        return { conversations: updatedConversations };
      });
    },
    onConversationCreated: (serverConversation: Conversation) => {
      return serverConversation.id;
    },

    onSetMessages: (conversationId: number | string, messages: Message[]) => {
      // Convert conversationId to number if it's a string
      const numericConversationId = typeof conversationId === 'string'
        ? parseInt(conversationId, 10)
        : conversationId;

      console.log(`[MessagesStore] Setting ${messages.length} messages for conversation ${numericConversationId}:`, messages);

      // Sort messages by timestamp (oldest first) for proper chronological display
      const sortedMessages = [...messages].sort((a, b) => {
        const timestampA = new Date(a.timestamp).getTime();
        const timestampB = new Date(b.timestamp).getTime();
        return timestampA - timestampB; // Oldest first for chronological display
      });

      set(state => {
        // Update the messagesById state with sorted messages
        return {
          messagesById: {
            ...state.messagesById,
            [numericConversationId]: sortedMessages
          }
        };
      });
    },

    onAddMessages: (conversationId: number | string, messages: Message[]) => {
      // Convert conversationId to number if it's a string
      const numericConversationId = typeof conversationId === 'string'
        ? parseInt(conversationId, 10)
        : conversationId;

      set(state => {
        // Get existing messages for this conversation
        const existingMessages = state.messagesById[numericConversationId] || [];

        // Create a Set of existing message IDs for quick lookup
        const existingIds = new Set(existingMessages.map(msg => msg.id));

        // Filter out messages that already exist
        const newMessages = messages.filter(msg => !existingIds.has(msg.id));

        if (newMessages.length === 0) {
          return state; // No changes needed
        }
        let updatedMessages = existingMessages;

        if (existingMessages.length > 0 && newMessages.length > 0) {
          const firstExistingTimestamp = new Date(existingMessages[0].timestamp).getTime();
          const firstNewTimestamp = new Date(newMessages[0].timestamp).getTime();

          if (firstNewTimestamp < firstExistingTimestamp) {
            // These are older messages from pagination, add at the beginning
            updatedMessages = [...newMessages, ...existingMessages];
          } else {
            // These are newer messages, add at the end
            updatedMessages = [...existingMessages, ...newMessages];
          }
        } else {
          // If there are no existing messages, just use the new ones
          updatedMessages = newMessages;
        }

        // Sort the updated messages by timestamp (oldest first) for proper chronological display
        const sortedMessages = [...updatedMessages].sort((a, b) => {
          const timestampA = new Date(a.timestamp).getTime();
          const timestampB = new Date(b.timestamp).getTime();
          return timestampA - timestampB; // Oldest first for chronological display
        });

        // Update the messagesById state with sorted messages
        return {
          messagesById: {
            ...state.messagesById,
            [numericConversationId]: sortedMessages
          }
        };
      });
    }
  },
  ui: {
    markAsRead: (chatId: number | string) => {
      set(state => {
        const updatedConversations = state.conversations.map(conv => {
          if (conv.id === chatId && conv.members) {
            // Create a copy of the conversation
            const updatedConv = { ...conv };

            // Get the player's phone number
            const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
            if (!phoneNumber) {
              console.error('[MessagesStore] Cannot mark as read: Player phone number not available');
              return conv;
            }

            // Create a copy of members
            const updatedMembers = { ...updatedConv.members };

            if (updatedMembers[phoneNumber]) {
              // Update the unread_count for the current player
              updatedMembers[phoneNumber] = {
                ...updatedMembers[phoneNumber],
                unread_count: 0
              };

              // Update the members in the conversation
              updatedConv.members = updatedMembers;
            }

            return updatedConv;
          }
          return conv;
        });
        return { conversations: updatedConversations };
      });

      // Send a request to the server to mark as read
      clientRequests.send('messages', 'markAsRead', { conversationId: chatId });
    },
    setActiveChat: (chatId: number | string | null) => {
      // Convert string chatId to number if it's a string
      const numericChatId = typeof chatId === 'string' ? parseInt(chatId, 10) : chatId;
      set({ activeChat: numericChatId });
    }
  }
}));

// Type assertion helper for conversations with id property
export const asConversation = (obj: unknown): Conversation => {
  return obj as Conversation;
};