import React, { useEffect, useState } from 'react';
import { useNavigation } from '../../../navigation/hooks';
import MyContactCard from './MyContactCard';
import { motion  } from 'framer-motion';
import { useSelectionStore } from '../../../common/stores/selectionStore';
import { useContactsStore } from '../stores/contactsStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import MyContactCardSelectable from './MyContactCardSelectable';
import { Contact } from '@shared/types';

interface ContactsTabProps {
  contacts: Contact[];
  searchTerm: string;
  onContactSelect: (contact: Contact) => void;
  onCallClick: (contact: Contact) => void;
  onMessageClick: (contact: Contact) => void;
}

const ContactsTab: React.FC<ContactsTabProps> = ({
  contacts,
  searchTerm: externalSearchTerm,
  onContactSelect,
  onCallClick,
  onMessageClick
}) => {
  // Use internal state for search to make the component self-contained
  const [searchTerm, setSearchTerm] = useState(externalSearchTerm);
  const { switchTab, openView } = useNavigation();
  const { active: selectionActive, mode: selectionMode } = useSelectionStore();
  const {
    selectedContacts,
    ui: { toggleContactSelection, clearSelectedContacts }
  } = useContactsStore();

  // Initialize selection mode when component mounts
  useEffect(() => {
    // If selection mode is active, make sure we have a clean slate
    if (selectionActive) {
      clearSelectedContacts();
    }

    // Clear selected contacts when component unmounts or selection mode ends
    return () => {
      if (!selectionActive) {
        clearSelectedContacts();
      }
    };
  }, [selectionActive, clearSelectedContacts]);

  // Handle selection in single selection mode
  const handleContactSelection = (contactId: number) => {
    if (selectionMode === 'single') {
      // In single selection mode, we clear previous selections and select only the current contact
      clearSelectedContacts();
      toggleContactSelection(contactId);
    } else {
      // In multiple selection mode, we toggle the selection
      toggleContactSelection(contactId);
    }
  };

  // Group contacts by first letter
  const groupedContacts = contacts
    .filter(contact => contact.name.toLowerCase().includes(searchTerm.toLowerCase()))
    .reduce((groups, contact) => {
      const firstLetter = contact.name.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(contact);
      return groups;
    }, {} as Record<string, Contact[]>);

  // Sort groups alphabetically
  const sortedGroups = Object.entries(groupedContacts)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([letter, contacts]) => ({
      letter,
      contacts: contacts.sort((a, b) => a.name.localeCompare(b.name))
    }));

  return (
    <div className="flex-1 flex flex-col overflow-hidden h-full">
      {/* Header with search bar and add button */}
      <div className="px-4 pt-2 pb-3 bg-[#0a0f1a] z-30 border-b border-white/10 shadow-md flex-shrink-0">
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-[#131b2e] text-white rounded-lg py-2 pl-9 pr-3 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
              <i className="fas fa-search text-xs"></i>
            </div>
            {searchTerm && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/80"
                onClick={() => setSearchTerm('')}
              >
                <i className="fas fa-times text-xs"></i>
              </button>
            )}
          </div>
          <button
            onClick={() => openView('add')}
            className="w-9 h-9 rounded-full bg-green-500/20 flex items-center justify-center text-green-500 hover:bg-green-500/30 transition-colors"
          >
            <i className="fas fa-plus text-sm"></i>
          </button>
        </div>
      </div>

      {/* Contacts list with consistent spacing */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-[#0a0f1a] to-[#0a0f1a]/95 flex flex-col min-h-0">
        {/* My Contact Card */}
        {selectionActive ? (
          <MyContactCardSelectable
            onEditProfile={() => switchTab('profile')}
            onSelect={() => {
              // Get the user profile to create a contact object
              const { userProfile } = usePhoneStore.getState();
              const myContact = {
                id: 0,
                number: userProfile.phoneNumber || '',
                name: userProfile.name || 'My Profile',
                favorite: 1,
                avatar: userProfile.imageUrl || null,
                created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
                updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
              };

              // Handle selection of the user's own contact
              handleContactSelection(myContact.id);
            }}
            isSelected={selectedContacts.includes(0)}
          />
        ) : (
          <MyContactCard onEditProfile={() => switchTab('profile')} />
        )}

        {/* Alphabetical sections */}
        {sortedGroups.length > 0 ? (
          <div className="flex-1 pt-1 pb-1">
            {sortedGroups.map(({ letter, contacts }) => (
              <div key={letter} className="mb-1">
                <div className="px-4 py-1 sticky top-0 bg-[#0a0f1a]/90 backdrop-blur-sm z-10 w-full">
                  <p className="text-white/50 text-xs font-medium">{letter}</p>
                </div>
                <div>
                  {contacts.map(contact => (
                    <motion.div
                      key={contact.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      whileTap={{ scale: 0.98 }}
                      className={`mx-4 py-1 px-2.5 border-b border-white/5 flex items-center hover:bg-[#0f1525]/50 transition-colors ${
                        selectionActive && selectedContacts.includes(contact.id!) ? 'bg-blue-500/20' : ''
                      }`}
                      aria-selected={selectionActive && selectedContacts.includes(contact.id!)}
                      onClick={() => {
                        if (selectionActive) {
                          handleContactSelection(contact.id!);
                        } else {
                          onContactSelect(contact);
                        }
                      }}
                    >
                      {/* Selection indicator */}
                      {selectionActive && (
                        <div className="mr-2 flex items-center justify-center">
                          <div
                            className={`w-5 h-5 rounded-full border ${
                              selectedContacts.includes(contact.id!)
                                ? 'bg-blue-500 border-blue-500'
                                : 'border-white/40'
                            } flex items-center justify-center`}
                            onClick={e => {
                              e.stopPropagation();
                              handleContactSelection(contact.id!);
                            }}
                          >
                            {selectedContacts.includes(contact.id!) && (
                              <i className="fas fa-check text-white text-xs"></i>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Contact avatar */}
                      <div className="relative">
                        {contact.avatar ? (
                          <div className="relative">
                            <div
                              className="w-9 h-9 rounded-full bg-cover bg-center"
                              style={{ backgroundImage: `url(${contact.avatar})` }}
                            />
                            {contact.favorite === 1 && (
                              <div className="absolute inset-0 rounded-full bg-yellow-400/10 mix-blend-overlay"></div>
                            )}
                          </div>
                        ) : (
                          <div className="relative">
                            <div className="w-9 h-9 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                              <span className="text-white text-sm font-medium">
                                {contact.name ? contact.name[0].toUpperCase() : '#'}
                              </span>
                            </div>
                            {contact.favorite === 1 && (
                              <div className="absolute inset-0 rounded-full bg-yellow-400/10 mix-blend-overlay"></div>
                            )}
                          </div>
                        )}

                        {/* Favorite indicator */}
                        {contact.favorite === 1 && (
                          <div className="absolute -top-1 -right-1 w-3.5 h-3.5 rounded-full bg-yellow-400 flex items-center justify-center border border-[#0a0f1a]">
                            <i className="fas fa-star text-[7px] text-[#0a0f1a]"></i>
                          </div>
                        )}
                      </div>

                      {/* Contact info */}
                      <div className="ml-2.5 flex-1 min-w-0">
                        <div className="flex justify-between items-center">
                          <div className="text-white font-medium text-sm truncate pr-2">{contact.name}</div>
                        </div>
                        <div className="flex justify-between items-center mt-0.5">
                          <div className="text-xs text-white/50 truncate">{contact.number}</div>

                          {/* Quick action buttons - only show when not in selection mode */}
                          {!selectionActive && (
                            <div className="flex space-x-2">
                              <motion.button
                                whileTap={{ scale: 0.9 }}
                                onClick={e => {
                                  e.stopPropagation();
                                  // Call with the contact directly
                                  onCallClick(contact);
                                }}
                                className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center text-green-500 hover:bg-green-500/30 transition-colors"
                              >
                                <i className="fas fa-phone text-xs"></i>
                              </motion.button>
                              <motion.button
                                whileTap={{ scale: 0.9 }}
                                onClick={e => {
                                  e.stopPropagation();
                                  // Message with the contact directly
                                  onMessageClick(contact);
                                }}
                                className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-500 hover:bg-blue-500/30 transition-colors"
                              >
                                <i className="fas fa-comment text-xs"></i>
                              </motion.button>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex-1 flex flex-col items-center justify-center text-white/60 px-6 py-8"
          >
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#1a2234] to-[#0d1423] flex items-center justify-center mb-4 shadow-inner border border-white/5">
              <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                <i className="fas fa-user-slash text-xl text-white/40"></i>
              </div>
            </div>
            <p className="text-lg font-medium text-center text-white/80">No contacts found</p>
            <p className="text-sm text-white/40 text-center mt-2.5 max-w-xs leading-relaxed">
              {searchTerm
                ? `No contacts matching "${searchTerm}"`
                : "You don't have any contacts yet. Add some to get started."}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ContactsTab;
