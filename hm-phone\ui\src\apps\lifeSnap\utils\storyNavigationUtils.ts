/**
 * Utility functions for story navigation
 */

import { Story } from '../types/lifeSnapTypes';

/**
 * Convert various timestamp formats to a numeric timestamp
 * @param timestamp Timestamp in various formats
 * @returns Numeric timestamp
 */
export const getTimeValue = (timestamp: Date | number | string | undefined): number => {
  if (!timestamp) return 0;
  if (timestamp instanceof Date) return timestamp.getTime();
  if (typeof timestamp === 'number') return timestamp;
  try {
    return new Date(timestamp).getTime();
  } catch {
    console.error('Invalid timestamp format:', timestamp);
    return 0;
  }
};

/**
 * Sort stories by timestamp
 * @param stories Array of stories
 * @returns Sorted array of stories
 */
export const sortStoriesByTimestamp = (stories: Story[]): Story[] => {
  return [...stories].sort((a, b) => {
    const aTime = getTimeValue(a.timestamp);
    const bTime = getTimeValue(b.timestamp);
    return aTime - bTime;
  });
};

/**
 * Filter stories to only include active ones (not expired)
 * @param stories Array of stories
 * @returns Filtered array of stories
 */
export const filterActiveStories = (stories: Story[]): Story[] => {
  return stories.filter(s => s.expiresAt && new Date(s.expiresAt) > new Date());
};

/**
 * Get stories for a specific user
 * @param stories Array of stories
 * @param userId User ID
 * @returns Array of stories for the user
 */
export const getUserStories = (stories: Story[], userId: number): Story[] => {
  return filterActiveStories(stories)
    .filter(s => s.userId === userId)
    .sort((a, b) => {
      const aTime = getTimeValue(a.timestamp);
      const bTime = getTimeValue(b.timestamp);
      return aTime - bTime;
    });
};

/**
 * Group stories by user
 * @param stories Array of stories
 * @returns Record of stories grouped by user ID
 */
export const groupStoriesByUser = (stories: Story[]): Record<string, Story[]> => {
  return filterActiveStories(stories).reduce((acc, story) => {
    const userId = String(story.userId); // Convert to string for consistent indexing
    if (!acc[userId]) acc[userId] = [];
    acc[userId].push(story);
    return acc;
  }, {} as Record<string, Story[]>);
};

/**
 * Find the next story in the same user's stories
 * @param stories Array of stories
 * @param currentStoryId Current story ID
 * @returns Next story or null if not found
 */
export const findNextStoryInSameUser = (stories: Story[], currentStoryId: number): Story | null => {
  const currentStory = stories.find(s => s.id === currentStoryId);
  if (!currentStory) return null;

  const userStories = getUserStories(stories, currentStory.userId as number);
  const currentIndex = userStories.findIndex(s => s.id === currentStoryId);

  if (currentIndex === -1 || currentIndex >= userStories.length - 1) {
    return null;
  }

  return userStories[currentIndex + 1];
};

/**
 * Find the previous story in the same user's stories
 * @param stories Array of stories
 * @param currentStoryId Current story ID
 * @returns Previous story or null if not found
 */
export const findPrevStoryInSameUser = (stories: Story[], currentStoryId: number): Story | null => {
  const currentStory = stories.find(s => s.id === currentStoryId);
  if (!currentStory) return null;

  const userStories = getUserStories(stories, currentStory.userId as number);
  const currentIndex = userStories.findIndex(s => s.id === currentStoryId);

  if (currentIndex <= 0) {
    return null;
  }

  return userStories[currentIndex - 1];
};

/**
 * Find the first story of the next user
 * @param stories Array of stories
 * @param currentStoryId Current story ID
 * @returns First story of the next user or null if not found
 */
export const findFirstStoryOfNextUser = (
  stories: Story[],
  currentStoryId: number
): Story | null => {
  const currentStory = stories.find(s => s.id === currentStoryId);
  if (!currentStory) return null;

  const storiesByUser = groupStoriesByUser(stories);
  const allUsers = Object.entries(storiesByUser);

  const currentUserIndex = allUsers.findIndex(([userId]) => userId === String(currentStory.userId));

  if (currentUserIndex === -1 || currentUserIndex >= allUsers.length - 1) {
    return null;
  }

  const nextUserStories = sortStoriesByTimestamp(allUsers[currentUserIndex + 1][1]);

  if (nextUserStories.length === 0) {
    return null;
  }

  return nextUserStories[0];
};

/**
 * Find the last story of the previous user
 * @param stories Array of stories
 * @param currentStoryId Current story ID
 * @returns Last story of the previous user or null if not found
 */
export const findLastStoryOfPrevUser = (stories: Story[], currentStoryId: number): Story | null => {
  const currentStory = stories.find(s => s.id === currentStoryId);
  if (!currentStory) return null;

  const storiesByUser = groupStoriesByUser(stories);
  const allUsers = Object.entries(storiesByUser);

  const currentUserIndex = allUsers.findIndex(([userId]) => userId === String(currentStory.userId));

  if (currentUserIndex <= 0) {
    return null;
  }

  const prevUserStories = sortStoriesByTimestamp(allUsers[currentUserIndex - 1][1]);

  if (prevUserStories.length === 0) {
    return null;
  }

  return prevUserStories[prevUserStories.length - 1];
};
