import React, { useState, useRef } from 'react';
import { useNavigation } from '../../navigation/hooks';
import MatchesView from './components/MatchesView';
import ChatView from './components/ChatView';
import MyProfileEditor from './components/MyProfileEditor';
import { useNavigationStore } from '../../navigation/navigationStore';
import { LoveLinkProfile } from './types/loveLinkTypes';
import { lovelinkMockData } from '../../fivem';

const LoveLink: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const { currentView } = useNavigationStore.getState();
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};

  const activeMatchId = navigationData.matchId ? Number(navigationData.matchId) : null;

  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const currentProfile = lovelinkMockData.profiles[0] as LoveLinkProfile;

  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [isClick, setIsClick] = useState(false);

  const cardRef = useRef<HTMLDivElement>(null);
  const SWIPE_THRESHOLD_PERCENTAGE = 0.3; // 30% of card width
  const CLICK_THRESHOLD = 10; // 10 pixels for click threshold

  const getSwipeThreshold = () => {
    if (!cardRef.current) return 0;
    return cardRef.current.offsetWidth * SWIPE_THRESHOLD_PERCENTAGE;
  };

  const handleDragStart = (x: number, y: number) => {
    setIsClick(true); // Start assuming it's a click
    setIsDragging(true);
    setDragStart({ x, y });
    // setMoveDistance(0);
  };

  const handleDragMove = (x: number, y: number) => {
    if (!isDragging) return;

    const deltaX = x - dragStart.x;
    const deltaY = y - dragStart.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (distance > CLICK_THRESHOLD) {
      setIsClick(false); // If we move too much, it's not a click
    }

    setDragOffset({ x: deltaX, y: deltaY });
    // setMoveDistance(distance);
  };

  const handleDragEnd = () => {
    if (!isDragging) return;

    const threshold = getSwipeThreshold();
    if (Math.abs(dragOffset.x) > threshold) {
      if (dragOffset.x > 0) {
        handleLike();
      } else {
        handlePass();
      }
    }

    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  };

  // Mouse events
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    handleDragStart(e.clientX, e.clientY);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!isDragging) return;
    handleDragMove(e.clientX, e.clientY);
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isClick) {
      handleProfileClick();
    } else {
      handleDragEnd();
    }
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
    setIsClick(false);
  };

  // Touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleDragStart(touch.clientX, touch.clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleDragMove(touch.clientX, touch.clientY);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (isClick) {
      handleProfileClick();
    } else {
      handleDragEnd();
    }
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
    setIsClick(false);
  };

  // Calculate card rotation and opacity based on drag
  const getCardStyle = () => {
    if (!isDragging) {
      return {
        transform: 'translate(0px, 0px) rotate(0deg)',
        transition: 'transform 0.3s ease'
      };
    }

    const threshold = getSwipeThreshold();
    const rotate = (dragOffset.x / threshold) * 10; // Max 10 degree rotation at threshold

    // Make opacity decrease more gradually (divide by 2 to make it fade out slower)
    // Only start fading after passing 50% of threshold
    const opacity = Math.max(1 - (Math.abs(dragOffset.x) - threshold / 2) / (threshold * 1.5), 0.3);

    return {
      transform: `translate(${dragOffset.x}px, ${dragOffset.y}px) rotate(${rotate}deg)`,
      opacity: opacity,
      transition: isDragging ? 'none' : 'transform 0.3s ease'
    };
  };

  // Action indicators - keep these more responsive
  const getLikeIndicatorOpacity = () => {
    if (!isDragging) return 0;
    const threshold = getSwipeThreshold();
    return Math.max(0, Math.min(dragOffset.x / threshold, 1));
  };

  const getPassIndicatorOpacity = () => {
    if (!isDragging) return 0;
    const threshold = getSwipeThreshold();
    return Math.max(0, Math.min(-dragOffset.x / threshold, 1));
  };

  const handleLike = () => {
    console.log('Handling like');
    if (!currentProfile) return;

    // likeProfile(currentProfile.id);
    setCurrentPhotoIndex(0);
  };

  const handlePass = () => {
    console.log('Handling pass');
    if (!currentProfile) return;

    // passProfile(currentProfile.id);
    setCurrentPhotoIndex(0);
  };

  const handleProfileClick = () => {
    // setShowProfileDetail(true);
  };

  // Remove unused handleNextPhoto function since it's not being used

  // Split the photo navigation into a separate component to prevent event bubbling
  const PhotoNavigation = ({
    currentProfile,
    currentPhotoIndex,
    setCurrentPhotoIndex
  }: {
    currentProfile: LoveLinkProfile;
    currentPhotoIndex: number;
    setCurrentPhotoIndex: (index: number) => void;
  }) => (
    <>
      <button
        onClick={e => {
          e.stopPropagation();
          const newIndex =
            currentPhotoIndex > 0 ? currentPhotoIndex - 1 : currentProfile.photos.length - 1;
          setCurrentPhotoIndex(newIndex);
        }}
        className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-black/30 text-white/80 hover:bg-black/40 hover:text-white flex items-center justify-center transition-all cursor-pointer z-10"
      >
        <i className="fas fa-chevron-left"></i>
      </button>
      <button
        onClick={e => {
          e.stopPropagation();
          const newIndex =
            currentPhotoIndex < currentProfile.photos.length - 1 ? currentPhotoIndex + 1 : 0;
          setCurrentPhotoIndex(newIndex);
        }}
        className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-black/30 text-white/80 hover:bg-black/40 hover:text-white flex items-center justify-center transition-all cursor-pointer z-10"
      >
        <i className="fas fa-chevron-right"></i>
      </button>
    </>
  );

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 pb-3">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <button onClick={goBack} className="text-white/80 hover:text-white cursor-pointer">
          <i className="fas fa-arrow-left"></i>
        </button>
        <div className="flex items-center flex-1 justify-center gap-4">
          <button
            onClick={() => openView('swipe')}
            className={
              currentView === 'swipe' || !currentView || currentView === 'main'
                ? 'text-blue-500'
                : 'text-white/60'
            }
          >
            <i className="fas fa-fire"></i>
          </button>
          <button
            onClick={() => openView('matches')}
            className={currentView === 'matches' ? 'text-blue-500' : 'text-white/60'}
          >
            <i className="fas fa-comments"></i>
          </button>
        </div>
        <button
          onClick={() => openView('myProfile')}
          className={currentView === 'myProfile' ? 'text-blue-500' : 'text-white/60'}
        >
          <i className="fas fa-user"></i>
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {(currentView === 'swipe' || !currentView || currentView === 'main') && (
          <div className="h-full flex flex-col">
            {currentProfile ? (
              <div
                ref={cardRef}
                className="relative flex-1 rounded-xl overflow-hidden cursor-grab active:cursor-grabbing"
                style={getCardStyle()}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                {/* Photos Carousel with Navigation Arrows */}
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: `url(${currentProfile.photos[currentPhotoIndex]})` }}
                >
                  <PhotoNavigation
                    currentProfile={currentProfile}
                    currentPhotoIndex={currentPhotoIndex}
                    setCurrentPhotoIndex={setCurrentPhotoIndex}
                  />
                  {/* Photo Navigation Indicators */}
                  <div className="absolute top-2 left-0 right-0 flex justify-center gap-1">
                    {currentProfile.photos.map((_: string, idx: number) => (
                      <div
                        key={idx}
                        className={`w-1.5 h-1.5 rounded-full ${
                          idx === currentPhotoIndex ? 'bg-white' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                {/* Like/Pass Indicators Overlay */}
                {isDragging && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div
                      className="text-6xl font-bold transform transition-opacity"
                      style={{
                        opacity: getLikeIndicatorOpacity(),
                        color: '#22c55e',
                        textShadow: '0 0 10px rgba(0,0,0,0.5)'
                      }}
                    >
                      LIKE
                    </div>
                    <div
                      className="text-6xl font-bold transform transition-opacity"
                      style={{
                        opacity: getPassIndicatorOpacity(),
                        color: '#ef4444',
                        textShadow: '0 0 10px rgba(0,0,0,0.5)'
                      }}
                    >
                      NOPE
                    </div>
                  </div>
                )}

                {/* Profile Info */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                  <h2 className="text-2xl font-bold text-white">
                    {currentProfile.name}, {currentProfile.age}
                  </h2>
                  <p className="text-white/80">{currentProfile.occupation}</p>
                  <p className="text-white/60 text-sm mt-1">{currentProfile.location}</p>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-white/60 p-4 text-center">
                <i className="fas fa-heart-broken text-3xl mb-2"></i>
                <p>No more profiles to show!</p>
                <p className="text-sm mt-2">Check back later for more matches</p>
              </div>
            )}

            {/* Action Buttons */}
            {currentProfile && (
              <div className="p-4 flex justify-center gap-4">
                <button
                  onClick={handlePass}
                  className="w-14 h-14 rounded-full bg-red-500/20 text-red-500 flex items-center justify-center hover:bg-red-500/30 transition-colors cursor-pointer"
                >
                  <i className="fas fa-times text-2xl"></i>
                </button>
                <button
                  onClick={handleLike}
                  className="w-14 h-14 rounded-full bg-green-500/20 text-green-500 flex items-center justify-center hover:bg-green-500/30 transition-colors cursor-pointer"
                >
                  <i className="fas fa-heart text-2xl"></i>
                </button>
              </div>
            )}
          </div>
        )}

        {currentView === 'matches' && !activeMatchId && <MatchesView />}

        {currentView === 'chat' && activeMatchId && (
          <ChatView
            matchId={activeMatchId}
            onBack={() => {
              openView('matches');
            }}
          />
        )}
        {currentView === 'myProfile' && <MyProfileEditor onBack={() => openView('swipe')} />}
      </div>
    </div>
  );
};

export default LoveLink;
