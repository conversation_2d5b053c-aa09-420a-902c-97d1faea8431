import React from 'react';
import { useNavigation } from '../../navigation/hooks';

const PullIndicator: React.FC = () => {
  const { goHome } = useNavigation();

  return (
    <div className="absolute bottom-0 left-0 right-0 flex justify-center bg-transparent py-3 z-[1000]">
      <div
        className="w-24 h-[7px] bg-white hover:bg-white/80 rounded-full cursor-pointer transition-colors"
        onClick={() => {
          goHome();
        }}
      ></div>
    </div>
  );
};

export default PullIndicator;
