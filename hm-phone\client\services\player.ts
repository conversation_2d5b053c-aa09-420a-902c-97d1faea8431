/**
 * Player Service
 *
 * Handles player-related functionality and state.
 * This service is responsible for:
 * - Checking if player can use phone
 * - Getting player data
 * - Finding nearby players
 */

// Import globals to ensure they're initialized
import '../globals';
import gameService from './game';

/**
 * Check if player is in a vehicle
 * @returns True if player is in a vehicle
 */
export function isPlayerInVehicle(): boolean {
    const playerPed = PlayerPedId();
    return IsPedInAnyVehicle(playerPed, false);
}

/**
 * Check if player is swimming
 * @returns True if player is swimming
 */
export function isPlayerSwimming(): boolean {
    const playerPed = PlayerPedId();
    return IsPedSwimming(playerPed);
}

/**
 * Check if player is climbing
 * @returns True if player is climbing
 */
export function isPlayerClimbing(): boolean {
    const playerPed = PlayerPedId();
    return IsPedClimbing(playerPed);
}

/**
 * Check if player is falling
 * @returns True if player is falling
 */
export function isPlayerFalling(): boolean {
    const playerPed = PlayerPedId();
    return IsPedFalling(playerPed);
}

/**
 * Check if player is ragdolling
 * @returns True if player is ragdolling
 */
export function isPlayerRagdolling(): boolean {
    const playerPed = PlayerPedId();
    return IsPedRagdoll(playerPed);
}

/**
 * Check if player is in cover
 * @returns True if player is in cover
 */
export function isPlayerInCover(): boolean {
    const playerPed = PlayerPedId();
    return IsPedInCover(playerPed, false);
}

/**
 * Check if player can use phone
 * @returns True if player can use phone
 */
export function canUsePhone(): boolean {
    const playerPed = PlayerPedId();

    // Check if player is dead
    if (gameService.isEntityDead(playerPed)) {
        return false;
    }

    // Check if player is in certain states that would prevent phone use
    if (isPlayerSwimming() || isPlayerClimbing() || isPlayerFalling() || isPlayerRagdolling()) {
        return false;
    }

    // Add any other conditions that would prevent phone use

    return true;
}

/**
 * Get player data
 * @returns Player data object
 */
export function getPlayerData(): { phoneNumber: string | null; identifier: string | null; stateid: string | null } {
    return {
        phoneNumber: global.playerPhoneNumber,
        identifier: global.playerIdentifier,
        stateid: global.playerStateId
    };
}

/**
 * Set player phone number
 * @param number Phone number to set
 */
export function setPlayerPhoneNumber(number: string): void {
    global.playerPhoneNumber = number;
}

/**
 * Set player identifier
 * @param identifier Player identifier to set
 */
export function setPlayerIdentifier(identifier: string): void {
    global.playerIdentifier = identifier;
}

/**
 * Set player state ID
 * @param stateid Player state ID to set
 */
export function setPlayerStateId(stateid: string): void {
    global.playerStateId = stateid;
}

/**
 * Check if player is busy
 * @returns True if player is busy
 */
export function isPlayerBusy(): boolean {
    // Check various conditions that would make the player busy
    return (
        gameService.isEntityDead(PlayerPedId()) ||
        isPlayerSwimming() ||
        isPlayerClimbing() ||
        isPlayerFalling() ||
        isPlayerRagdolling() ||
        isPlayerInCover()
    );
}

/**
 * Get closest players
 * @param maxDistance Maximum distance to check
 * @returns Array of player IDs and distances
 */
export function getClosestPlayers(maxDistance = 5.0): { id: number; distance: number }[] {
    const result: { id: number; distance: number }[] = [];
    const playerPed = PlayerPedId();
    const playerCoords = gameService.getPlayerCoords();

    // Get all players
    const players = GetActivePlayers();

    for (let i = 0; i < players.length; i++) {
        const playerId = players[i];
        const targetPed = GetPlayerPed(playerId);

        // Skip self
        if (targetPed === playerPed) {
            continue;
        }

        // Get target coordinates
        const targetCoords = GetEntityCoords(targetPed, false);

        // Calculate distance
        const distance = Math.sqrt(
            Math.pow(playerCoords[0] - targetCoords[0], 2) +
                Math.pow(playerCoords[1] - targetCoords[1], 2) +
                Math.pow(playerCoords[2] - targetCoords[2], 2)
        );

        // Check if within range
        if (distance <= maxDistance) {
            result.push({
                id: playerId,
                distance: distance
            });
        }
    }

    // Sort by distance
    result.sort((a, b) => a.distance - b.distance);

    return result;
}

// Export player service
export const playerService = {
    // Player state functions
    isPlayerInVehicle,
    isPlayerSwimming,
    isPlayerClimbing,
    isPlayerFalling,
    isPlayerRagdolling,
    isPlayerInCover,
    isPlayerBusy,

    // Phone-related functions
    canUsePhone,

    // Player data functions
    getPlayerData,
    setPlayerPhoneNumber,
    setPlayerIdentifier,
    setPlayerStateId,

    // Player interaction functions
    getClosestPlayers
};

export default playerService;
