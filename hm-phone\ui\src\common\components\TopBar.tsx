import React, { useState, useRef } from 'react';
import { usePhoneStore } from '../stores/phoneStateStore';
import { motion, AnimatePresence } from 'framer-motion';
import { useMusicStore } from '../../apps/music/stores/musicStore';
import { useNavigation } from '../../navigation/hooks';
import { useWifiState } from '../../apps/settings/hooks/useWifiState';
import TopBarNotificationSection from '../../notifications/components/TopBarNotificationSection';
import NotificationMusicPlayer from '../../notifications/components/NotificationMusicPlayer';
import ActiveCallIndicator from './ActiveCallIndicator';

const TopBar: React.FC = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const drawerRef = useRef<HTMLDivElement>(null);
  const {
    isAirplaneModeOn,
    isSoundOn,
    actions: { toggleAirplaneMode, toggleSound }
  } = usePhoneStore();
  const { isWifiOn, toggleWifi } = useWifiState();
  const { currentSong } = useMusicStore();
  const { openApp } = useNavigation();

  const currentTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  const handleClick = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const quickActions = [
    {
      icon: 'wifi',
      label: 'WiFi',
      active: isWifiOn,
      onClick: () => toggleWifi()
    },
    {
      icon: 'plane',
      label: 'Airplane',
      active: isAirplaneModeOn,
      onClick: () => toggleAirplaneMode()
    },
    {
      icon: isSoundOn ? 'bell' : 'bell-slash',
      label: 'Sound',
      active: isSoundOn,
      onClick: () => toggleSound()
    },
    {
      icon: 'moon',
      label: 'Dark Mode',
      active: false,
      onClick: () => {} // TODO: Implement dark mode toggle if needed
    }
  ];

  return (
    <div className="relative z-[60]">
      {/* Active Call Indicator - positioned at the same level as topbar */}
      <div className="relative">
        <ActiveCallIndicator />
      </div>

      <div
        className="absolute top-0 left-0 right-0 flex flex-col min-h-[32px] cursor-pointer pointer-events-auto z-[101]"
        onClick={handleClick}
      >
        <div
          className={`flex justify-between items-center px-4 py-2 ${
            isDrawerOpen ? 'bg-black/90' : 'bg-transparent'
          } transition-colors duration-300`}
        >
          <div className="text-white text-xs font-medium">{currentTime}</div>

          <div className="flex items-center gap-2">
            <i className={`fas fa-${isSoundOn ? 'bell' : 'bell-slash'} text-white text-xs`}></i>
            <i className={`fas fa-${isWifiOn ? 'wifi' : 'wifi'} text-${isWifiOn ? 'white' : 'gray-500'} text-xs`}></i>
            {isAirplaneModeOn && <i className="fas fa-plane text-white text-xs"></i>}
            {(!isAirplaneModeOn || (isAirplaneModeOn && isWifiOn)) && (
              <>
                {!isAirplaneModeOn && <i className="fas fa-signal text-white text-xs"></i>}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Notification Drawer */}
      <AnimatePresence>
        {isDrawerOpen && (
          <motion.div
            ref={drawerRef}
            initial={{ y: -20, opacity: 0, scale: 0.98 }}
            animate={{
              y: 0,
              opacity: 1,
              scale: 1,
              transition: {
                type: 'spring',
                damping: 20,
                stiffness: 300,
                mass: 0.8
              }
            }}
            exit={{
              y: -10,
              opacity: 0,
              scale: 0.98,
              transition: {
                type: 'spring',
                damping: 25,
                stiffness: 400,
                duration: 0.2
              }
            }}
            className="fixed top-0 left-0 right-0 z-[100] bg-black/90 overflow-visible pt-[32px] rounded-t-[1.125rem]"
            style={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto', overflowX: 'visible' }}
          >
            {/* Quick Actions Grid */}
            <div className="px-4 pt-4 pb-4">
              {/* Grid layout for Quick Actions and Music Player */}
              <div className="grid grid-cols-4 gap-3">
                {/* Left side - Quick Actions (2x2 grid) */}
                <div className="col-span-2 row-span-2 grid grid-cols-2 gap-2">
                  {quickActions.slice(0, 4).map(action => (
                    <button
                      key={action.icon}
                      onClick={e => {
                        e.stopPropagation();
                        action.onClick();
                      }}
                      className={`flex flex-col items-center justify-center p-1.5 rounded-xl aspect-square overflow-hidden ${
                        action.active ? 'bg-white/20' : 'bg-white/5 hover:bg-white/10'
                      } transition-colors relative z-10`}
                    >
                      <i
                        className={`fas fa-${action.icon} ${
                          action.active ? 'text-blue-400' : 'text-white'
                        } text-lg`}
                      ></i>
                      <span className="text-white/70 font-medium mt-1 max-w-full text-[10px] whitespace-nowrap overflow-visible">
                        {action.label}
                      </span>
                    </button>
                  ))}
                </div>

                {/* Right side - Music Player (2x2) */}
                <div className="col-span-2 row-span-2">
                  {currentSong ? (
                    <NotificationMusicPlayer closeDrawer={() => setIsDrawerOpen(false)} />
                  ) : (
                    <motion.div
                      className="rounded-xl overflow-hidden h-auto aspect-square relative group cursor-pointer"
                      onClick={() => {
                        openApp('music');
                        setIsDrawerOpen(false);
                      }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                      style={{
                        backgroundColor: '#1e1e1e'
                      }}
                    >
                      {/* Gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-pink-900/80 to-purple-900/90"></div>

                      {/* Main content */}
                      <div className="absolute inset-0 flex flex-col justify-between p-3 overflow-hidden">
                        {/* Song info */}
                        <div className="mb-2 text-center">
                          <h3 className="text-white font-medium">Music</h3>
                        </div>

                        {/* Music icon */}
                        <div className="my-auto flex justify-center items-center">
                          <div className="w-16 h-16 rounded-full bg-black/30 flex items-center justify-center">
                            <i className="fas fa-music text-white/30 text-xl"></i>
                          </div>
                        </div>

                        {/* Spacer */}
                        <div></div>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {/* Additional Quick Actions if needed */}
              {quickActions.length > 4 && (
                <div className="mt-4">
                  <div className="mb-1 px-1">
                    <p className="text-white/50 text-xs">More Actions</p>
                  </div>
                  <div className="grid grid-cols-4 gap-3">
                    {quickActions.slice(4).map(action => (
                      <button
                        key={action.icon}
                        onClick={e => {
                          e.stopPropagation();
                          action.onClick();
                        }}
                        className={`flex flex-col items-center justify-center p-1.5 rounded-xl aspect-square overflow-hidden ${
                          action.active ? 'bg-white/20' : 'bg-white/5 hover:bg-white/10'
                        } transition-colors relative z-10`}
                      >
                        <i
                          className={`fas fa-${action.icon} ${
                            action.active ? 'text-blue-400' : 'text-white'
                          } text-lg`}
                        ></i>
                        <span className="text-white/70 font-medium mt-1 max-w-full text-[10px] whitespace-nowrap overflow-visible">
                          {action.label}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Notifications Section */}
            <TopBarNotificationSection closeDrawer={() => setIsDrawerOpen(false)} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TopBar;
