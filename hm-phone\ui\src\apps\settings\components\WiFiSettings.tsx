import React, { useState, useEffect } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { wifiMockData } from '../../../fivem/mockData';
import { motion } from 'framer-motion';
import { useWifiState } from '../hooks/useWifiState';

interface WiFiNetwork {
  id: number;
  ssid: string;
  signal: number; // Signal strength (0-100)
  secured: boolean;
  connected: boolean;
}

interface WiFiSettingsProps {
  onBack: () => void;
}

const WiFiSettings: React.FC<WiFiSettingsProps> = ({ onBack }) => {
  const { isAirplaneModeOn } = usePhoneStore();
  const { isWifiOn, connectedWifi, setConnectedWifi, toggleWifi } = useWifiState();

  const [networks, setNetworks] = useState<WiFiNetwork[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState<WiFiNetwork | null>(null);
  const [password, setPassword] = useState('');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Scan for networks when the component mounts or when WiFi is toggled on
  useEffect(() => {
    if (isWifiOn) {
      scanNetworks();
    } else {
      setNetworks([]);
    }
  }, [isWifiOn]);

  // Initialize networks with the connected network when component mounts
  useEffect(() => {
    if (isWifiOn && connectedWifi && networks.length === 0) {
      // If we have a connected WiFi but no networks yet, create a placeholder
      // This will be replaced when scanNetworks completes
      setNetworks([
        {
          id: 0,
          ssid: connectedWifi,
          signal: 80, // Assume good signal for the connected network
          secured: true, // Assume secured for safety
          connected: true
        }
      ]);
    }

    // If we have a connected network in the mock data but not in the store, update the store
    if (isWifiOn && !connectedWifi) {
      const connectedNetwork = wifiMockData.networks.find(n => n.connected);
      if (connectedNetwork) {
        setConnectedWifi(connectedNetwork.ssid);
      }
    }
  }, [isWifiOn, connectedWifi, networks.length, setConnectedWifi]);

  const scanNetworks = async () => {
    try {
      setIsScanning(true);
      setError(null);

      // Use clientRequests with mock data for browser mode
      const response = await clientRequests.send(
        'settings',
        'scanWifiNetworks',
        {},
        wifiMockData.networks
      );

      if (response.success && 'data' in response) {
        // Get the networks from the response
        const scannedNetworks = response.data as WiFiNetwork[];

        // If we have a connected WiFi, make sure it's marked as connected in the list
        if (connectedWifi) {
          const updatedNetworks = scannedNetworks.map(network => ({
            ...network,
            connected: network.ssid === connectedWifi
          }));
          setNetworks(updatedNetworks);
        } else {
          setNetworks(scannedNetworks);
        }
      } else {
        setError('Failed to scan networks');
      }
    } catch (error) {
      console.error('[WiFiSettings] Error scanning networks:', error);
      setError('Error scanning networks');
    } finally {
      setIsScanning(false);
    }
  };

  const handleNetworkClick = (network: WiFiNetwork) => {
    setSelectedNetwork(network);
    if (network.secured) {
      setShowPasswordModal(true);
    } else {
      connectToNetwork(network.id);
    }
  };

  const connectToNetwork = async (networkId: number, pwd?: string) => {
    try {
      setError(null);

      // Find the network to connect to
      const network = networks.find(n => n.id === networkId);
      if (!network) {
        setError('Network not found');
        return;
      }

      // Use clientRequests with mock data for browser mode
      const response = await clientRequests.send(
        'settings',
        'connectToWifi',
        { networkId, password: pwd },
        { success: true }
      );

      if (response.success) {
        // Find the network we're connecting to
        const network = networks.find(n => n.id === networkId);

        // Update the networks to mark this one as connected
        const updatedNetworks = networks.map(n => ({
          ...n,
          connected: n.id === networkId
        }));

        setNetworks(updatedNetworks);

        // Update the global WiFi state
        if (!isWifiOn) {
          toggleWifi();
        }

        // Update the connected WiFi in the settings store
        if (network) {
          setConnectedWifi(network.ssid);
        }

        // Close the password modal if it's open
        setShowPasswordModal(false);
      } else {
        setError('Failed to connect to network');
      }
    } catch (error) {
      console.error('[WiFiSettings] Error connecting to network:', error);
      setError('Error connecting to network');
    }
  };

  const disconnectFromNetwork = async (networkId: number) => {
    try {
      setError(null);

      // Find the network to disconnect from
      const network = networks.find(n => n.id === networkId);
      if (!network) {
        setError('Network not found');
        return;
      }

      // Use clientRequests with mock data for browser mode
      const response = await clientRequests.send(
        'settings',
        'disconnectFromWifi',
        { networkId },
        { success: true }
      );

      if (response.success) {
        // Find the network we're disconnecting from
        const network = networks.find(n => n.id === networkId);

        // Update the networks to mark this one as disconnected
        const updatedNetworks = networks.map(n => ({
          ...n,
          connected: n.id === networkId ? false : n.connected
        }));

        setNetworks(updatedNetworks);

        // Update the global WiFi state if no networks are connected
        const anyConnected = updatedNetworks.some(n => n.connected);
        if (!anyConnected && isWifiOn) {
          toggleWifi();
        }

        // Clear the connected WiFi in the settings store if this was the connected network
        if (network && network.ssid === connectedWifi) {
          setConnectedWifi(null);
        }
      } else {
        setError('Failed to disconnect from network');
      }
    } catch (error) {
      console.error('[WiFiSettings] Error disconnecting from network:', error);
      setError('Error disconnecting from network');
    }
  };

  const handlePasswordSubmit = () => {
    if (selectedNetwork) {
      connectToNetwork(selectedNetwork.id, password);
    }
  };

  // Function to render signal strength icon based on signal value
  const renderSignalIcon = (signal: number) => {
    if (signal >= 80) return <i className="fas fa-wifi text-green-500"></i>;
    if (signal >= 60) return <i className="fas fa-wifi text-yellow-500"></i>;
    if (signal >= 40) return <i className="fas fa-wifi text-orange-500"></i>;
    return <i className="fas fa-wifi text-red-500"></i>;
  };

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8">
      {/* Header with back button - account for TopBar height */}
      <div className="flex items-center px-4 py-3 border-b border-white/10">
        <button onClick={onBack} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left"></i>
        </button>
        <h1 className="text-white font-medium ml-3">WiFi</h1>
      </div>

      {/* Content with scrolling */}
      <div className="flex-1 overflow-y-auto">
        {/* WiFi Toggle */}
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <i className="fas fa-wifi text-blue-500"></i>
              </div>
              <div>
                <h2 className="text-white font-medium">WiFi</h2>
                <p className="text-white/50 text-sm">
                  {isWifiOn ? 'On' : 'Off'}
                </p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={isWifiOn}
                onChange={toggleWifi}
                disabled={isAirplaneModeOn}
              />
              <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* WiFi Networks Section */}
        {isWifiOn && (
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-white font-medium">Available Networks</h2>
              <button
                onClick={scanNetworks}
                disabled={isScanning}
                className="text-blue-500 hover:text-blue-400 disabled:text-gray-500"
              >
                {isScanning ? (
                  <i className="fas fa-spinner fa-spin"></i>
                ) : (
                  <i className="fas fa-sync-alt"></i>
                )}
              </button>
            </div>

            {error && (
              <div className="bg-red-500/20 text-red-400 p-3 rounded-lg mb-4">
                {error}
              </div>
            )}

            {isScanning ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mb-3">
                  <i className="fas fa-spinner fa-spin text-blue-500"></i>
                </div>
                <p className="text-white/70">Scanning for networks...</p>
              </div>
            ) : networks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="w-12 h-12 rounded-full bg-gray-500/20 flex items-center justify-center mb-3">
                  <i className="fas fa-wifi-slash text-gray-500"></i>
                </div>
                <p className="text-white/70">No networks found</p>
              </div>
            ) : (
              <div className="space-y-2">
                {networks.map(network => (
                  <div
                    key={network.id}
                    className={`p-3 rounded-lg flex items-center justify-between ${
                      network.connected
                        ? 'bg-blue-500/20 border border-blue-500/30'
                        : 'bg-white/5 hover:bg-white/10'
                    } cursor-pointer transition-colors`}
                    onClick={() => network.connected ? disconnectFromNetwork(network.id) : handleNetworkClick(network)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
                        {renderSignalIcon(network.signal)}
                      </div>
                      <div>
                        <h3 className="text-white font-medium">{network.ssid}</h3>
                        <div className="flex items-center gap-2 text-white/50 text-xs">
                          {network.secured && <i className="fas fa-lock"></i>}
                          <span>
                            {network.connected ? 'Connected' : network.secured ? 'Secured' : 'Open'}
                          </span>
                        </div>
                      </div>
                    </div>
                    {network.connected && (
                      <div className="text-blue-500">
                        <i className="fas fa-check-circle"></i>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Password Modal */}
      {showPasswordModal && selectedNetwork && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-[#1a1f2e] rounded-xl p-4 w-[90%] max-w-xs"
          >
            <h2 className="text-white font-medium mb-4">Connect to {selectedNetwork.ssid}</h2>
            <p className="text-white/70 text-sm mb-4">
              This network requires a password.
            </p>
            <input
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              placeholder="Password"
              className="w-full bg-white/10 border border-white/20 rounded-lg p-2 text-white mb-4 focus:outline-none focus:border-blue-500"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPassword('');
                }}
                className="px-4 py-2 text-white/70 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handlePasswordSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                disabled={!password}
              >
                Connect
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default WiFiSettings;
