/**
 * Camera app message handlers
 */
import { useNavigationStore } from '../../../../src/navigation/navigationStore';
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { usePhotosStore } from '../../photos/stores/photosStore';
import { Photo } from '../../photos/stores/photosStore';

// Register handler for photo taken
registerEventHandler('camera', 'photoTaken', data => {
  console.log('[Camera] Photo taken:', data);

  // Validate data
  if (data && typeof data === 'object' && 'id' in data && 'imageUrl' in data) {
    // Update the Photos store with the new photo
    usePhotosStore.getState().handlers.onPhotoSaved(data as Photo);

    // Reset camera view
    useNavigationStore.getState().currentView = 'camera';
  } else {
    console.error('[Camera] Received invalid photo data:', data);
  }
});

// Register handler for errors
registerEventHandler('camera', 'error', data => {
  console.error('[Camera] Error:', data);
});
