// THIS FILE IS AUTO-GENERATED. DO NOT EDIT.
// Generated by scripts/shared/items/generate-weapons.js

import { ItemType } from "../types/items.types"; // Corrected import path
import type { ItemDefinition } from "../types/items.types"; // Corrected import path

export interface WeaponComponent {
  name: string;
  attachBone: string;
  label?: string;
  description?: string;
  variants?: any[];
}

/**
 * Extends ItemDefinition with weapon-specific static properties.
 * This represents the blueprint for a type of weapon.
 */
export interface WeaponDefinition extends ItemDefinition {
  hash: number;
  ammoType: string;
  weaponCategory: string;
  components?: WeaponComponent[];
}

export const weaponDefinitions: Record<string, WeaponDefinition> = {
  WEAPON_KNIFE: {
    id: 'WEAPON_KNIFE',
    name: 'WEAPON_KNIFE',
    hash: 2578778090,
    label: 'Knife',
    description: 'Knife',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Knife.png',
    model: 'prop_knife', // Knife model for ground spawn
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE',
    components: [
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3',
      attachBone: 'gun_root',
      label: 'Eyes',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_01',
      attachBone: 'gun_root',
      label: 'Spatter',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_02',
      attachBone: 'gun_root',
      label: 'Flames',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_03',
      attachBone: 'gun_root',
      label: 'Lightning',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_04',
      attachBone: 'gun_root',
      label: 'Pills',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_05',
      attachBone: 'gun_root',
      label: 'Snakeskin',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_06',
      attachBone: 'gun_root',
      label: 'Lucha Libre',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_07',
      attachBone: 'gun_root',
      label: 'Trippy',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_08',
      attachBone: 'gun_root',
      label: 'Tequilya',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_KNIFE_VARMOD_XM3_09',
      attachBone: 'gun_root',
      label: 'Orang-O-Tang',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_NIGHTSTICK: {
    id: 'WEAPON_NIGHTSTICK',
    name: 'WEAPON_NIGHTSTICK',
    hash: 1737195953,
    label: 'Nightstick',
    description: 'Nightstick',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Nightstick.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_HAMMER: {
    id: 'WEAPON_HAMMER',
    name: 'WEAPON_HAMMER',
    hash: 1317494643,
    label: 'Hammer',
    description: 'Hammer',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Hammer.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_BAT: {
    id: 'WEAPON_BAT',
    name: 'WEAPON_BAT',
    hash: 2508868239,
    label: 'Baseball Bat',
    description: 'Baseball Bat',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Bat.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE',
    components: [
    {
      name: 'COMPONENT_BAT_VARMOD_XM3',
      attachBone: 'gun_root',
      label: 'Blagueurs',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_01',
      attachBone: 'gun_root',
      label: 'Spatter',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_02',
      attachBone: 'gun_root',
      label: 'Bullet Holes',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_03',
      attachBone: 'gun_root',
      label: 'Burger Shot',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_04',
      attachBone: 'gun_root',
      label: 'Cluckin\' Bell',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_05',
      attachBone: 'gun_root',
      label: 'Fatal Incursion',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_06',
      attachBone: 'gun_root',
      label: 'Lucha Libre',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_07',
      attachBone: 'gun_root',
      label: 'Trippy',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_08',
      attachBone: 'gun_root',
      label: 'Tie-Dye',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_BAT_VARMOD_XM3_09',
      attachBone: 'gun_root',
      label: 'Wall',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_GOLFCLUB: {
    id: 'WEAPON_GOLFCLUB',
    name: 'WEAPON_GOLFCLUB',
    hash: 1141786504,
    label: 'Golf Club',
    description: 'Golf Club',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_GClub.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_CROWBAR: {
    id: 'WEAPON_CROWBAR',
    name: 'WEAPON_CROWBAR',
    hash: 2227010557,
    label: 'Crowbar',
    description: 'Crowbar',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Crowbar.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_PISTOL: {
    id: 'WEAPON_PISTOL',
    name: 'WEAPON_PISTOL',
    hash: 453432689,
    label: 'Pistol',
    description: 'Pistol',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_PI_Pistol.png',
    model: 'w_pi_pistol', // Pistol model for ground spawn
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_PISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_PISTOL_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_PISTOL_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_PI_SUPP_02'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_COMBATPISTOL: {
    id: 'WEAPON_COMBATPISTOL',
    name: 'WEAPON_COMBATPISTOL',
    hash: 1593441988,
    label: 'Combat Pistol',
    description: 'Combat Pistol',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_PI_CombatPistol.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_COMBATPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Combat Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Combat Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_COMBATPISTOL_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_COMBATPISTOL_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_PI_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_COMBATPISTOL_VARMOD_XMAS23',
      attachBone: 'gun_root',
      label: 'COMPONENT_COMBATPISTOL_VARMOD_XMAS23',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_APPISTOL: {
    id: 'WEAPON_APPISTOL',
    name: 'WEAPON_APPISTOL',
    hash: 584646201,
    label: 'AP Pistol',
    description: 'AP Pistol',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_PI_APPistol.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_APPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for AP Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_APPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for AP Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_APPISTOL_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Gilded Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_APPISTOL_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_APPISTOL_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_PI_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_APPISTOL_VARMOD_SECURITY',
      attachBone: 'gun_root',
      label: 'Record A Finish',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_PISTOL50: {
    id: 'WEAPON_PISTOL50',
    name: 'WEAPON_PISTOL50',
    hash: 2578377531,
    label: 'Pistol .50',
    description: 'Pistol .50',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_PI_Pistol50.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_PISTOL50_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Pistol .50.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL50_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Pistol .50.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL50_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Platinum Pearl Deluxe Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_PISTOL50_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_PISTOL50_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    }
  ]
  },
  WEAPON_MICROSMG: {
    id: 'WEAPON_MICROSMG',
    name: 'WEAPON_MICROSMG',
    hash: 324215364,
    label: 'Micro SMG',
    description: 'Micro SMG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SB_MicroSMG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_MICROSMG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Micro SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_MICROSMG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Micro SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_MICROSMG_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_MICROSMG_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_MICROSMG_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_SCOPE_MACRO'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    },
    {
      name: 'COMPONENT_MICROSMG_VARMOD_SECURITY',
      attachBone: 'gun_root',
      label: 'Organics Finish',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_MICROSMG_VARMOD_XM3',
      attachBone: 'gun_root',
      label: 'Dildodude Camo',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_MICROSMG_VARMOD_FRN',
      attachBone: 'gun_root',
      label: 'COMPONENT_MICROSMG_VARMOD_FRN',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_SMG: {
    id: 'WEAPON_SMG',
    name: 'WEAPON_SMG',
    hash: 736523883,
    label: 'SMG',
    description: 'SMG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SB_SMG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_SMG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_02',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_SMG_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_SMG_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_SMG_CLIP_03'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_SCOPE_MACRO_02'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_PI_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_ASSAULTSMG: {
    id: 'WEAPON_ASSAULTSMG',
    name: 'WEAPON_ASSAULTSMG',
    hash: 4024951519,
    label: 'Assault SMG',
    description: 'Assault SMG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SB_AssaultSMG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_ASSAULTSMG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Assault SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTSMG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Assault SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_ASSAULTSMG_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_ASSAULTSMG_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_SCOPE_MACRO'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    }
  ]
  },
  WEAPON_ASSAULTRIFLE: {
    id: 'WEAPON_ASSAULTRIFLE',
    name: 'WEAPON_ASSAULTRIFLE',
    hash: 3220176749,
    label: 'Assault Rifle',
    description: 'Assault Rifle',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_AssaultRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_ASSAULTRIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_ASSAULTRIFLE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_ASSAULTRIFLE_CLIP_03'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_SCOPE_MACRO'
        },
        {
          Index: 6,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_CARBINERIFLE: {
    id: 'WEAPON_CARBINERIFLE',
    name: 'WEAPON_CARBINERIFLE',
    hash: 2210333304,
    label: 'Carbine Rifle',
    description: 'Carbine Rifle',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_CarbineRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_CARBINERIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Carbine Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Carbine Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Box Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_RAILCOVER_01',
      attachBone: 'WAPFlshLasr',
      label: 'COMPONENT_AT_RAILCOVER_01',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_CARBINERIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_CARBINERIFLE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_CARBINERIFLE_CLIP_03'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_SCOPE_MEDIUM'
        },
        {
          Index: 6,
          Name: 'COMPONENT_AT_AR_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_CARBINERIFLE_VARMOD_MICH',
      attachBone: 'gun_root',
      label: 'COMPONENT_CARBINERIFLE_VARMOD_MICH',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_ADVANCEDRIFLE: {
    id: 'WEAPON_ADVANCEDRIFLE',
    name: 'WEAPON_ADVANCEDRIFLE',
    hash: 2937143193,
    label: 'Advanced Rifle',
    description: 'Advanced Rifle',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_AdvancedRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_ADVANCEDRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_ADVANCEDRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Gilded Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_ADVANCEDRIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_ADVANCEDRIFLE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_SCOPE_SMALL'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_SUPP'
        }
      ]
    }
  ]
  },
  WEAPON_MG: {
    id: 'WEAPON_MG',
    name: 'WEAPON_MG',
    hash: 2634544996,
    label: 'MG',
    description: 'MG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_MG_MG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MG',
    weaponCategory: 'GROUP_MG',
    components: [
    {
      name: 'COMPONENT_MG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for MG.',
      variants: []
    },
    {
      name: 'COMPONENT_MG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for MG.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL_02',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_MG_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_MG_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_MG_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_SCOPE_SMALL_02'
        }
      ]
    }
  ]
  },
  WEAPON_COMBATMG: {
    id: 'WEAPON_COMBATMG',
    name: 'WEAPON_COMBATMG',
    hash: 2144741730,
    label: 'Combat MG',
    description: 'Combat MG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_MG_CombatMG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MG',
    weaponCategory: 'GROUP_MG',
    components: [
    {
      name: 'COMPONENT_COMBATMG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Combat MG.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Combat MG.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Etched Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_COMBATMG_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_COMBATMG_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_SCOPE_MEDIUM'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_PUMPSHOTGUN: {
    id: 'WEAPON_PUMPSHOTGUN',
    name: 'WEAPON_PUMPSHOTGUN',
    hash: 487013001,
    label: 'Pump Shotgun',
    description: 'Pump Shotgun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_PumpShotgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_PUMPSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 1,
          Name: 'COMPONENT_AT_SR_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_VARMOD_SECURITY',
      attachBone: 'gun_root',
      label: 'Bone Finish',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_VARMOD_XM3',
      attachBone: 'gun_root',
      label: 'Dildodude Camo',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_SAWNOFFSHOTGUN: {
    id: 'WEAPON_SAWNOFFSHOTGUN',
    name: 'WEAPON_SAWNOFFSHOTGUN',
    hash: 2017895192,
    label: 'Sawed-Off Shotgun',
    description: 'Sawed-Off Shotgun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_SAWNOFF.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_SAWNOFFSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Gilded Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_ASSAULTSHOTGUN: {
    id: 'WEAPON_ASSAULTSHOTGUN',
    name: 'WEAPON_ASSAULTSHOTGUN',
    hash: 3800352039,
    label: 'Assault Shotgun',
    description: 'Assault Shotgun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_AssaultShotgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_ASSAULTSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Assault Shotgun.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTSHOTGUN_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Assault Shotgun.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_BULLPUPSHOTGUN: {
    id: 'WEAPON_BULLPUPSHOTGUN',
    name: 'WEAPON_BULLPUPSHOTGUN',
    hash: 2640438543,
    label: 'Bullpup Shotgun',
    description: 'Bullpup Shotgun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_BullpupShotgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_BULLPUPSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_STUNGUN: {
    id: 'WEAPON_STUNGUN',
    name: 'WEAPON_STUNGUN',
    hash: 911657153,
    label: 'Stun Gun',
    description: 'Stun Gun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_PI_StunGun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_STUNGUN',
    weaponCategory: 'GROUP_STUNGUN'
  },
  WEAPON_SNIPERRIFLE: {
    id: 'WEAPON_SNIPERRIFLE',
    name: 'WEAPON_SNIPERRIFLE',
    hash: 100416529,
    label: 'Sniper Rifle',
    description: 'Sniper Rifle',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SR_SniperRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_SNIPERRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_LARGE',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Long-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MAX',
      attachBone: 'WAPScop',
      label: 'Advanced Scope',
      description: 'Maximum zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_SNIPERRIFLE_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Etched Wood Grip Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_SNIPERRIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_AT_SCOPE_LARGE'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_SCOPE_MAX'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    }
  ]
  },
  WEAPON_HEAVYSNIPER: {
    id: 'WEAPON_HEAVYSNIPER',
    name: 'WEAPON_HEAVYSNIPER',
    hash: 205991906,
    label: 'Heavy Sniper',
    description: 'Heavy Sniper',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SR_HeavySniper.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_HEAVYSNIPER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_LARGE',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Long-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MAX',
      attachBone: 'WAPScop',
      label: 'Advanced Scope',
      description: 'Maximum zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_VARMOD_XMAS23',
      attachBone: 'gun_root',
      label: 'COMPONENT_HEAVYSNIPER_VARMOD_XMAS23',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_GRENADELAUNCHER: {
    id: 'WEAPON_GRENADELAUNCHER',
    name: 'WEAPON_GRENADELAUNCHER',
    hash: 2726580491,
    label: 'Grenade Launcher',
    description: 'Grenade Launcher',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_GrenadeLauncher.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_GRENADELAUNCHER',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_GRENADELAUNCHER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    }
  ]
  },
  WEAPON_GRENADELAUNCHER_SMOKE: {
    id: 'WEAPON_GRENADELAUNCHER_SMOKE',
    name: 'WEAPON_GRENADELAUNCHER_SMOKE',
    hash: 1305664598,
    label: 'Tear Gas Launcher',
    description: 'Tear Gas Launcher',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_GrenadeLauncher_smoke.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_GRENADELAUNCHER_SMOKE',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    }
  ]
  },
  WEAPON_RPG: {
    id: 'WEAPON_RPG',
    name: 'WEAPON_RPG',
    hash: 2982836145,
    label: 'RPG',
    description: 'RPG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_RPG.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RPG',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RPG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_RPG_VARMOD_TVR',
      attachBone: 'gun_root',
      label: 'COMPONENT_RPG_VARMOD_TVR',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_PASSENGER_ROCKET: {
    id: 'WEAPON_PASSENGER_ROCKET',
    name: 'WEAPON_PASSENGER_ROCKET',
    hash: 375527679,
    label: 'Invalid',
    description: 'Invalid',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_PassengerRocket.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RPG',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RPG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_AIRSTRIKE_ROCKET: {
    id: 'WEAPON_AIRSTRIKE_ROCKET',
    name: 'WEAPON_AIRSTRIKE_ROCKET',
    hash: 324506233,
    label: 'Invalid',
    description: 'Invalid',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_AirstrikeRocket.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RPG',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RPG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_STINGER: {
    id: 'WEAPON_STINGER',
    name: 'WEAPON_STINGER',
    hash: 1752584910,
    label: 'RPG',
    description: 'RPG',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_Stinger.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_STINGER',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RPG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_MINIGUN: {
    id: 'WEAPON_MINIGUN',
    name: 'WEAPON_MINIGUN',
    hash: 1119849093,
    label: 'Minigun',
    description: 'Minigun',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_MG_Minigun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MINIGUN',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_MINIGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      variants: []
    }
  ]
  },
  WEAPON_GRENADE: {
    id: 'WEAPON_GRENADE',
    name: 'WEAPON_GRENADE',
    hash: 2481070269,
    label: 'Grenade',
    description: 'Grenade',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_GrenadeSmoke.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_GRENADE',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_STICKYBOMB: {
    id: 'WEAPON_STICKYBOMB',
    name: 'WEAPON_STICKYBOMB',
    hash: 741814745,
    label: 'Sticky Bomb',
    description: 'Sticky Bomb',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_C4.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_STICKYBOMB',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_SMOKEGRENADE: {
    id: 'WEAPON_SMOKEGRENADE',
    name: 'WEAPON_SMOKEGRENADE',
    hash: 4256991824,
    label: 'Tear Gas',
    description: 'Tear Gas',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_GrenadeSmoke.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMOKEGRENADE',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_BZGAS: {
    id: 'WEAPON_BZGAS',
    name: 'WEAPON_BZGAS',
    hash: 2694266206,
    label: 'BZ Gas',
    description: 'BZ Gas',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_BZGas.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_BZGAS',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_MOLOTOV: {
    id: 'WEAPON_MOLOTOV',
    name: 'WEAPON_MOLOTOV',
    hash: 615608432,
    label: 'Molotov',
    description: 'Molotov',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_Molotov.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MOLOTOV',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_FIREEXTINGUISHER: {
    id: 'WEAPON_FIREEXTINGUISHER',
    name: 'WEAPON_FIREEXTINGUISHER',
    hash: 101631238,
    label: 'Fire Extinguisher',
    description: 'Fire Extinguisher',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AM_FireExtinguisher.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_FIREEXTINGUISHER',
    weaponCategory: 'GROUP_FIREEXTINGUISHER'
  },
  WEAPON_PETROLCAN: {
    id: 'WEAPON_PETROLCAN',
    name: 'WEAPON_PETROLCAN',
    hash: 883325847,
    label: 'Jerry Can',
    description: 'Jerry Can',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AM_JerryCan.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PETROLCAN',
    weaponCategory: 'GROUP_PETROLCAN'
  },
  WEAPON_BALL: {
    id: 'WEAPON_BALL',
    name: 'WEAPON_BALL',
    hash: 600439132,
    label: 'Ball',
    description: 'Ball',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_Ball.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_BALL',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_FLARE: {
    id: 'WEAPON_FLARE',
    name: 'WEAPON_FLARE',
    hash: 1233104067,
    label: 'Flare',
    description: 'Flare',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_Flare.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_FLARE',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_VEHICLE_ROCKET: {
    id: 'WEAPON_VEHICLE_ROCKET',
    name: 'WEAPON_VEHICLE_ROCKET',
    hash: 3204302209,
    label: 'Invalid',
    description: 'Invalid',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_vehicle_rocket',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RPG',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RPG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_BIRD_CRAP: {
    id: 'WEAPON_BIRD_CRAP',
    name: 'WEAPON_BIRD_CRAP',
    hash: 1834887169,
    label: 'Grenade',
    description: 'Grenade',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_bird_crap',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_BIRD_CRAP',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_BOTTLE: {
    id: 'WEAPON_BOTTLE',
    name: 'WEAPON_BOTTLE',
    hash: 4192643659,
    label: 'Bottle',
    description: 'It\'s not clever and it\'s not pretty but, most of the time, neither is the guy coming at you with a knife. When all else fails, this gets the job done. Part of the Beach Bum Pack.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Bottle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_SNSPISTOL: {
    id: 'WEAPON_SNSPISTOL',
    name: 'WEAPON_SNSPISTOL',
    hash: 3218215474,
    label: 'SNS Pistol',
    description: 'Like condoms or hairspray, this fits in your pocket for a night out in a Vinewood club. It\'s half as accurate as a champagne cork but twice as deadly. Part of the Beach Bum Pack.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_snspistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_SNSPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for SNS Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for SNS Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Etched Wood Grip Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_SNSPISTOL_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_SNSPISTOL_CLIP_02'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_HEAVYPISTOL: {
    id: 'WEAPON_HEAVYPISTOL',
    name: 'WEAPON_HEAVYPISTOL',
    hash: 3523564046,
    label: 'Heavy Pistol',
    description: 'The heavyweight champion of the magazine fed, semi-automatic handgun world. Delivers a serious forearm workout every time. Part of The Business Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_heavypistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_HEAVYPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Heavy Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Heavy Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYPISTOL_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Etched Wood Grip Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_HEAVYPISTOL_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_HEAVYPISTOL_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_PI_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_PI_SUPP'
        }
      ]
    }
  ]
  },
  WEAPON_BULLPUPRIFLE: {
    id: 'WEAPON_BULLPUPRIFLE',
    name: 'WEAPON_BULLPUPRIFLE',
    hash: 2132975508,
    label: 'Bullpup Rifle',
    description: 'The latest Chinese import taking America by storm, this rifle is known for its balanced handling. Lightweight and very controllable in automatic fire. Part of The High Life Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_BullpupRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_BULLPUPRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Bullpup Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Bullpup Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_VARMOD_LOW',
      attachBone: 'gun_root',
      label: 'Gilded Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_BULLPUPRIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_BULLPUPRIFLE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_SCOPE_SMALL'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_AR_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_SPECIALCARBINE: {
    id: 'WEAPON_SPECIALCARBINE',
    name: 'WEAPON_SPECIALCARBINE',
    hash: 3231910285,
    label: 'Special Carbine',
    description: 'Combining accuracy, maneuverability and low recoil, this is an extremely versatile assault rifle for any combat situation. Part of The Business Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_SpecialCarbine.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_SPECIALCARBINE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Special Carbine.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Special Carbine.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER',
      attachBone: 'gun_root',
      label: 'Etched Gun Metal Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_SPECIALCARBINE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_SPECIALCARBINE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_SPECIALCARBINE_CLIP_03'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_SCOPE_MEDIUM'
        },
        {
          Index: 6,
          Name: 'COMPONENT_AT_AR_SUPP_02'
        }
      ]
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_VARMOD_XMAS23',
      attachBone: 'gun_root',
      label: 'COMPONENT_SPECIALCARBINE_VARMOD_XMAS23',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_SNSPISTOL_MK2: {
    id: 'WEAPON_SNSPISTOL_MK2',
    name: 'WEAPON_SNSPISTOL_MK2',
    hash: 2285322324,
    label: 'SNS Pistol Mk II',
    description: 'The ultimate purse-filler: if you want to make Saturday Night really special, this is your ticket.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_snspistol_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_HOLLOWPOINT',
      attachBone: 'WAPClip',
      label: 'Hollow Point Rounds',
      description: 'Increased damage to targets without Body Armor.',
      variants: []
    },
    {
      name: 'COMPONENT_SNSPISTOL_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH_03',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_RAIL_02',
      attachBone: 'WAPScop',
      label: 'Mounted Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP_02',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_COMP_02',
      attachBone: 'WAPSupp_2',
      label: 'Compensator',
      description: 'Reduces recoil for rapid fire.',
      variants: []
    }
  ]
  },
  WEAPON_MARKSMANRIFLE_MK2: {
    id: 'WEAPON_MARKSMANRIFLE_MK2',
    name: 'WEAPON_MARKSMANRIFLE_MK2',
    hash: 1785463520,
    label: 'Marksman Rifle Mk II',
    description: 'Known in military circles as The Dislocator, this mod set will destroy both the target and your shoulder, in that order.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_marksmanrifle_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Large Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Zoom Scope',
      description: 'Long-range fixed zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MRFL_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MRFL_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_PUMPSHOTGUN_MK2: {
    id: 'WEAPON_PUMPSHOTGUN_MK2',
    name: 'WEAPON_PUMPSHOTGUN_MK2',
    hash: 1432025498,
    label: 'Pump Shotgun Mk II',
    description: 'Only one thing pumps more action than a pump action: watch out, the recoil is almost as deadly as the shot.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_pumpshotgun_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_PUMPSHOTGUN_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Shells',
      description: 'Standard shotgun ammunition.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Steel Buckshot Shells',
      description: 'Increased penetration of Body Armor.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_MK2_CLIP_EXPLOSIVE',
      attachBone: 'WAPClip',
      label: 'Explosive Slugs',
      description: 'Projectile which explodes on impact.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_MK2_CLIP_HOLLOWPOINT',
      attachBone: 'WAPClip',
      label: 'Flechette Shells',
      description: 'Increased damage to targets without Body Armor.',
      variants: []
    },
    {
      name: 'COMPONENT_PUMPSHOTGUN_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Dragon\'s Breath Shells',
      description: 'Has a chance to set targets on fire when shot.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_MK2',
      attachBone: 'WAPScop',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL_MK2',
      attachBone: 'WAPScop',
      label: 'Medium Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SR_SUPP_03',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_08',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil when firing.',
      variants: []
    }
  ]
  },
  WEAPON_BULLPUPRIFLE_MK2: {
    id: 'WEAPON_BULLPUPRIFLE_MK2',
    name: 'WEAPON_BULLPUPRIFLE_MK2',
    hash: 2228681469,
    label: 'Bullpup Rifle Mk II',
    description: 'So precise, so exquisite, it\'s not so much a hail of bullets as a symphony.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_bullpuprifle_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_BULLPUPRIFLE_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_02_MK2',
      attachBone: 'WAPScop_2',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL_MK2',
      attachBone: 'WAPScop_2',
      label: 'Medium Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_BP_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_BP_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_SPECIALCARBINE_MK2: {
    id: 'WEAPON_SPECIALCARBINE_MK2',
    name: 'WEAPON_SPECIALCARBINE_MK2',
    hash: 2526821735,
    label: 'Special Carbine Mk II',
    description: 'The jack of all trades just got a serious upgrade: bow to the master.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_specialcarbine_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SPECIALCARBINE_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_MK2',
      attachBone: 'WAPScop_2',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Large Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SC_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SC_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_ACIDPACKAGE: {
    id: 'WEAPON_ACIDPACKAGE',
    name: 'WEAPON_ACIDPACKAGE',
    hash: 4159824478,
    label: 'Acid Package',
    description: 'Acid Package',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_acidpackage',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_ACIDPACKAGE',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_CANDYCANE: {
    id: 'WEAPON_CANDYCANE',
    name: 'WEAPON_CANDYCANE',
    hash: 1703483498,
    label: 'Candy Cane',
    description: 'This year, why not go one step further? Add to the onslaught of music, lights, and merriment by literally beating your peers to death with the festive spirit.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_candycane',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_PISTOLXM3: {
    id: 'WEAPON_PISTOLXM3',
    name: 'WEAPON_PISTOLXM3',
    hash: 465894841,
    label: 'WM 29 Pistol',
    description: 'If you think shooting off without lifting a finger is a problem, there\'s a pill for that. But if you think it\'s a plus, there\'s the semi-automatic WM 29 Pistol.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_pistolxm3',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_PISTOLXM3_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for the WM 29 Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOLXM3_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_RAILGUNXM3: {
    id: 'WEAPON_RAILGUNXM3',
    name: 'WEAPON_RAILGUNXM3',
    hash: 4272043364,
    label: 'Railgun',
    description: 'All you need to know is — magnets, and it does horrible things to the things it\'s pointed at.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_railgunxm3',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RAILGUNXM3',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RAILGUNXM3_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Railgun.',
      variants: []
    }
  ]
  },
  WEAPON_SNOWBALL: {
    id: 'WEAPON_SNOWBALL',
    name: 'WEAPON_SNOWBALL',
    hash: 126349499,
    label: 'Snowball',
    description: 'Snowball',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_Snowball.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNOWBALL',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_DOUBLEACTION: {
    id: 'WEAPON_DOUBLEACTION',
    name: 'WEAPON_DOUBLEACTION',
    hash: 2548703416,
    label: 'Double-Action Revolver',
    description: 'Because sometimes revenge is a dish best served six times, in quick succession, right between the eyes. Part of The Doomsday Heist.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_doubleaction',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_DOUBLEACTION_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard ammo capacity.',
      variants: []
    }
  ]
  },
  WEAPON_HOMINGLAUNCHER: {
    id: 'WEAPON_HOMINGLAUNCHER',
    name: 'WEAPON_HOMINGLAUNCHER',
    hash: 1672152130,
    label: 'Homing Launcher',
    description: 'Infrared guided fire-and-forget missile launcher. For all your moving target needs. Part of the Festive Surprise.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_HomingLauncher.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_HOMINGLAUNCHER',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_HOMINGLAUNCHER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_RAYPISTOL: {
    id: 'WEAPON_RAYPISTOL',
    name: 'WEAPON_RAYPISTOL',
    hash: 2939590305,
    label: 'Up-n-Atomizer',
    description: 'Republican Space Ranger Special, fresh from the galactic war on socialism: no ammo, no mag, just one brutal energy pulse after another.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_raypistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RAYPISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_RAYPISTOL_VARMOD_XMAS18',
      attachBone: 'gun_root',
      label: 'Festive tint',
      description: 'The Festive tint for the Up-n-Atomizer.',
      variants: []
    }
  ]
  },
  WEAPON_RAYCARBINE: {
    id: 'WEAPON_RAYCARBINE',
    name: 'WEAPON_RAYCARBINE',
    hash: 1198256469,
    label: 'Unholy Hellbringer',
    description: 'Republican Space Ranger Special. If you want to turn a little green man into little green goo, this is the only American way to do it.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_raycarbine',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MG',
    weaponCategory: 'GROUP_MG'
  },
  WEAPON_RAYMINIGUN: {
    id: 'WEAPON_RAYMINIGUN',
    name: 'WEAPON_RAYMINIGUN',
    hash: 3056410471,
    label: 'Widowmaker',
    description: 'Republican Space Ranger Special. GO AHEAD, SAY I\'M COMPENSATING FOR SOMETHING. I DARE YOU.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_MG_RayMinigun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MINIGUN',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_MINIGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      variants: []
    }
  ]
  },
  WEAPON_REVOLVER_MK2: {
    id: 'WEAPON_REVOLVER_MK2',
    name: 'WEAPON_REVOLVER_MK2',
    hash: 3415619887,
    label: 'Heavy Revolver Mk II',
    description: 'If you can lift it, this is the closest you\'ll get to shooting someone with a freight train.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_revolver_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_REVOLVER_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Rounds',
      description: 'Standard revolver ammunition.',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass.',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_MK2_CLIP_HOLLOWPOINT',
      attachBone: 'WAPClip',
      label: 'Hollow Point Rounds',
      description: 'Increased damage to targets without Body Armor.',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which set targets on fire when shot.',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_MK2',
      attachBone: 'WAPScop',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_COMP_03',
      attachBone: 'WAPSupp',
      label: 'Compensator',
      description: 'Reduces recoil for rapid fire.',
      variants: []
    }
  ]
  },
  WEAPON_PROXMINE: {
    id: 'WEAPON_PROXMINE',
    name: 'WEAPON_PROXMINE',
    hash: 2874559379,
    label: 'Proximity Mine',
    description: 'Leave a present for your friends with these motion sensor landmines. Short delay after activation. Part of the Festive Surprise.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_proxmine',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PROXMINE',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_GUSENBERG: {
    id: 'WEAPON_GUSENBERG',
    name: 'WEAPON_GUSENBERG',
    hash: 1627465347,
    label: 'Gusenberg Sweeper',
    description: 'Complete your look with a Prohibition gun. Looks great being fired from an Albany Roosevelt or paired with a pinstripe suit. Part of the Valentine\'s Day Massacre Special.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SB_Gusenberg.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MG',
    weaponCategory: 'GROUP_MG',
    components: [
    {
      name: 'COMPONENT_GUSENBERG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Gusenberg Sweeper.',
      variants: []
    },
    {
      name: 'COMPONENT_GUSENBERG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Gusenberg Sweeper.',
      variants: []
    }
  ]
  },
  WEAPON_DAGGER: {
    id: 'WEAPON_DAGGER',
    name: 'WEAPON_DAGGER',
    hash: 2460120199,
    label: 'Antique Cavalry Dagger',
    description: 'You\'ve been rocking the pirate-chic look for a while, but no vicious weapon to complete the look? Get this dagger with guarded hilt. Part of The \"I\'m Not a Hipster\" Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Dagger.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_VINTAGEPISTOL: {
    id: 'WEAPON_VINTAGEPISTOL',
    name: 'WEAPON_VINTAGEPISTOL',
    hash: 137902532,
    label: 'Vintage Pistol',
    description: 'What you really need is a more recognizable gun. Stand out from the crowd at an armed robbery with this engraved pistol. Part of The \"I\'m Not a Hipster\" Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_vintagepistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_VINTAGEPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Vintage Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_VINTAGEPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Vintage Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_FIREWORK: {
    id: 'WEAPON_FIREWORK',
    name: 'WEAPON_FIREWORK',
    hash: 2138347493,
    label: 'Firework Launcher',
    description: 'Put the flair back in flare with this firework launcher, guaranteed to raise some oohs and aahs from the crowd. Part of the Independence Day Special.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_Firework.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_FIREWORK',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_FIREWORK_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_MUSKET: {
    id: 'WEAPON_MUSKET',
    name: 'WEAPON_MUSKET',
    hash: 2828843422,
    label: 'Musket',
    description: 'Armed with nothing but muskets and a superiority complex, the Brits took over half the world. Own the gun that built an empire. Part of the Independence Day Special.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_Musket.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_MUSKET_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_RAILGUN: {
    id: 'WEAPON_RAILGUN',
    name: 'WEAPON_RAILGUN',
    hash: 1834241177,
    label: 'Railgun',
    description: 'All you need to know is - magnets, and it does horrible things to the things it\'s pointed at. Exclusive content for returning players.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_Railgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RAILGUN',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_RAILGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Railgun.',
      variants: []
    }
  ]
  },
  WEAPON_HATCHET: {
    id: 'WEAPON_HATCHET',
    name: 'WEAPON_HATCHET',
    hash: 4191993645,
    label: 'Hatchet',
    description: 'Make kindling... of your pals with this easy to wield, easy to hide hatchet. Exclusive content for returning players.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Hatchet.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_MARKSMANRIFLE: {
    id: 'WEAPON_MARKSMANRIFLE',
    name: 'WEAPON_MARKSMANRIFLE',
    hash: 3342088282,
    label: 'Marksman Rifle',
    description: 'Whether you\'re up close or a disconcertingly long way away, this weapon will get the job done. A multi-range tool for tools. Part of the Last Team Standing Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SR_MarksmanRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_MARKSMANRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Marksman Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Marksman Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Long-range fixed zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_MARKSMANRIFLE_VARMOD_LUXE',
      attachBone: 'gun_root',
      label: 'Yusuf Amir Luxury Finish',
      description: 'Item available to purchase.',
      variants: [
        {
          Index: 0,
          Name: 'COMPONENT_MARKSMANRIFLE_CLIP_01'
        },
        {
          Index: 1,
          Name: 'COMPONENT_MARKSMANRIFLE_CLIP_02'
        },
        {
          Index: 2,
          Name: 'COMPONENT_AT_AR_FLSH'
        },
        {
          Index: 3,
          Name: 'COMPONENT_AT_AR_AFGRIP'
        },
        {
          Index: 4,
          Name: 'COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM'
        },
        {
          Index: 5,
          Name: 'COMPONENT_AT_AR_SUPP'
        }
      ]
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_HEAVYSHOTGUN: {
    id: 'WEAPON_HEAVYSHOTGUN',
    name: 'WEAPON_HEAVYSHOTGUN',
    hash: 984333226,
    label: 'Heavy Shotgun',
    description: 'The weapon to reach for when you absolutely need to make a horrible mess of the room. Best used near easy-wipe surfaces only. Part of the Last Team Standing Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_HeavyShotgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_HEAVYSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Heavy Shotgun.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSHOTGUN_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Heavy Shotgun.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSHOTGUN_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_GARBAGEBAG: {
    id: 'WEAPON_GARBAGEBAG',
    name: 'WEAPON_GARBAGEBAG',
    hash: 3794977420,
    label: 'Knife',
    description: 'Knife',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_garbagebag',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_MILITARYRIFLE: {
    id: 'WEAPON_MILITARYRIFLE',
    name: 'WEAPON_MILITARYRIFLE',
    hash: 2636060646,
    label: 'Military Rifle',
    description: 'This immensely powerful assault rifle was designed for highly qualified, exceptionally skilled soldiers. Yes, you can buy it.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_militaryrifle',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_MILITARYRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_MILITARYRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Assault Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_MILITARYRIFLE_SIGHT_01',
      attachBone: 'WAPScop_2',
      label: 'Iron Sights',
      description: 'Default rail-mounted iron sights.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_GADGETPISTOL: {
    id: 'WEAPON_GADGETPISTOL',
    name: 'WEAPON_GADGETPISTOL',
    hash: 1470379660,
    label: 'Perico Pistol',
    description: 'A deadly shot. Don\'t be precious. You won\'t scuff the titanium nitride finish.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_gadgetpistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_GADGETPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_HANDCUFFS: {
    id: 'WEAPON_HANDCUFFS',
    name: 'WEAPON_HANDCUFFS',
    hash: 3494679629,
    label: 'Knife',
    description: 'Knife',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_handcuffs',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_CERAMICPISTOL: {
    id: 'WEAPON_CERAMICPISTOL',
    name: 'WEAPON_CERAMICPISTOL',
    hash: 727643628,
    label: 'Ceramic Pistol',
    description: 'Not your grandma\'s ceramics. Although this pint-sized pistol is small enough to fit into her purse and won\'t set off a metal detector.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_ceramicpistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_CERAMICPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_CERAMICPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_CERAMICPISTOL_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_HAZARDCAN: {
    id: 'WEAPON_HAZARDCAN',
    name: 'WEAPON_HAZARDCAN',
    hash: 3126027122,
    label: 'Hazardous Jerry Can',
    description: 'Hazardous Jerry Can',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AM_HazardCan.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_HAZARDCAN',
    weaponCategory: 'GROUP_PETROLCAN'
  },
  WEAPON_COMBATSHOTGUN: {
    id: 'WEAPON_COMBATSHOTGUN',
    name: 'WEAPON_COMBATSHOTGUN',
    hash: 94989220,
    label: 'Combat Shotgun',
    description: 'There\'s only one semi-automatic shotgun with a fire rate that sets the LSFD alarm bells ringing, and you\'re looking at it.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_combatshotgun',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_COMBATSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Shells',
      description: 'Standard shotgun ammunition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_NAVYREVOLVER: {
    id: 'WEAPON_NAVYREVOLVER',
    name: 'WEAPON_NAVYREVOLVER',
    hash: 2441047180,
    label: 'Navy Revolver',
    description: 'A true museum piece. You want to know how the West was won - slow reload speeds and a whole heap of bloodshed.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_navyrevolver',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_NAVYREVOLVER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    }
  ]
  },
  WEAPON_FLAREGUN: {
    id: 'WEAPON_FLAREGUN',
    name: 'WEAPON_FLAREGUN',
    hash: 1198879012,
    label: 'Flare Gun',
    description: 'Use to signal distress or drunken excitement. Warning: pointing directly at individuals may cause spontaneous combustion. Part of The Heists Update.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_flaregun',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_FLAREGUN',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_FLAREGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    }
  ]
  },
  WEAPON_MARKSMANPISTOL: {
    id: 'WEAPON_MARKSMANPISTOL',
    name: 'WEAPON_MARKSMANPISTOL',
    hash: **********,
    label: 'Marksman Pistol',
    description: '** PLACEHOLDER MARKSMAN PISTOL DESCRIPTION **',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_marksmanpistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_MARKSMANPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_COMBATPDW: {
    id: 'WEAPON_COMBATPDW',
    name: 'WEAPON_COMBATPDW',
    hash: 171789620,
    label: 'Combat PDW',
    description: '** PLACEHOLDER COMBAT PDW DESCRIPTION **',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_combatpdw',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_COMBATPDW_CLIP_01',
      attachBone: 'WAPClip_2',
      label: 'Default Clip',
      description: 'Standard capacity for Combat PDW.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATPDW_CLIP_02',
      attachBone: 'WAPClip_2',
      label: 'Extended Clip',
      description: 'Extended capacity for Combat PDW.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATPDW_CLIP_03',
      attachBone: 'WAPClip_2',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    }
  ]
  },
  WEAPON_DBSHOTGUN: {
    id: 'WEAPON_DBSHOTGUN',
    name: 'WEAPON_DBSHOTGUN',
    hash: 4019527611,
    label: 'Double Barrel Shotgun',
    description: 'Do one thing, do it well. Who needs a high rate of fire when your first shot turns the other guy into a fine mist? Part of Lowriders: Custom Classics.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_DoubleBarrel.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_DBSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    }
  ]
  },
  WEAPON_MACHETE: {
    id: 'WEAPON_MACHETE',
    name: 'WEAPON_MACHETE',
    hash: 3713923289,
    label: 'Machete',
    description: 'America\'s West African arms trade isn\'t just about giving. Rediscover the simple life with this rusty cleaver. Part of Lowriders.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Machete.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_MACHINEPISTOL: {
    id: 'WEAPON_MACHINEPISTOL',
    name: 'WEAPON_MACHINEPISTOL',
    hash: 3675956304,
    label: 'Machine Pistol',
    description: 'This fully automatic is the snare drum to your twin-engine V8 bass: no drive-by sounds quite right without it. Part of Lowriders.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_machinepistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_MACHINEPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Machine Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_MACHINEPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Machine Pistol.',
      variants: []
    },
    {
      name: 'COMPONENT_MACHINEPISTOL_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_COMPACTRIFLE: {
    id: 'WEAPON_COMPACTRIFLE',
    name: 'WEAPON_COMPACTRIFLE',
    hash: 1649403952,
    label: 'Compact Rifle',
    description: 'Half the size, all the power, double the recoil: there\'s no riskier way to say \"I\'m compensating for something\". Part of Lowriders: Custom Classics.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_CompactRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_COMPACTRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Compact Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_COMPACTRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Compact Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_COMPACTRIFLE_CLIP_03',
      attachBone: 'WAPClip',
      label: 'Drum Magazine',
      description: 'Expanded capacity and slower reload.',
      variants: []
    }
  ]
  },
  WEAPON_FLASHLIGHT: {
    id: 'WEAPON_FLASHLIGHT',
    name: 'WEAPON_FLASHLIGHT',
    hash: 2343591895,
    label: 'Flashlight',
    description: 'Intensify your fear of the dark with this short range, battery-powered light source. Handy for blunt force trauma. Part of The Halloween Surprise.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Torch.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE',
    components: [
    {
      name: 'COMPONENT_FLASHLIGHT_LIGHT',
      attachBone: 'WAPFlsh',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    }
  ]
  },
  WEAPON_SWITCHBLADE: {
    id: 'WEAPON_SWITCHBLADE',
    name: 'WEAPON_SWITCHBLADE',
    hash: 3756226112,
    label: 'Switchblade',
    description: 'From your pocket to hilt-deep in the other guy\'s ribs in under a second: folding knives will never go out of style. Part of Executives and Other Criminals.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Switchblade.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE',
    components: [
    {
      name: 'COMPONENT_SWITCHBLADE_VARMOD_BASE',
      attachBone: 'gun_root',
      label: 'Default Handle',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_SWITCHBLADE_VARMOD_VAR1',
      attachBone: 'gun_root',
      label: 'VIP Variant',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_SWITCHBLADE_VARMOD_VAR2',
      attachBone: 'gun_root',
      label: 'Bodyguard Variant',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_REVOLVER: {
    id: 'WEAPON_REVOLVER',
    name: 'WEAPON_REVOLVER',
    hash: 3249783761,
    label: 'Heavy Revolver',
    description: 'A handgun with enough stopping power to drop a crazed rhino, and heavy enough to beat it to death if you\'re out of ammo. Part of Executives and Other Criminals.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_DA_Revolver.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_REVOLVER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_VARMOD_BOSS',
      attachBone: 'gun_root',
      label: 'VIP Variant',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_REVOLVER_VARMOD_GOON',
      attachBone: 'gun_root',
      label: 'Bodyguard Variant',
      description: 'Item available to purchase.',
      variants: []
    },
    {
      name: 'COMPONENT_GUNRUN_MK2_UPGRADE',
      attachBone: 'gun_gripr',
      label: 'Mk II',
      variants: []
    }
  ]
  },
  WEAPON_BATTLEAXE: {
    id: 'WEAPON_BATTLEAXE',
    name: 'WEAPON_BATTLEAXE',
    hash: 3441901897,
    label: 'Battle Axe',
    description: 'If it\'s good enough for medieval foot soldiers, modern border guards and pushy soccer moms, it\'s good enough for you. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_BattleAxe.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_AUTOSHOTGUN: {
    id: 'WEAPON_AUTOSHOTGUN',
    name: 'WEAPON_AUTOSHOTGUN',
    hash: 317205821,
    label: 'Sweeper Shotgun',
    description: 'How many effective tools for riot control can you tuck into your pants? OK, two. But this is the other one. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_SG_AutoShotgun.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SHOTGUN',
    weaponCategory: 'GROUP_SHOTGUN',
    components: [
    {
      name: 'COMPONENT_AUTOSHOTGUN_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    }
  ]
  },
  WEAPON_MINISMG: {
    id: 'WEAPON_MINISMG',
    name: 'WEAPON_MINISMG',
    hash: 3173288789,
    label: 'Mini SMG',
    description: 'Increasingly popular since the marketing team looked beyond spec ops units and started caring about the little guys in low income areas. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_minismg',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_MINISMG_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    },
    {
      name: 'COMPONENT_MINISMG_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      variants: []
    }
  ]
  },
  WEAPON_POOLCUE: {
    id: 'WEAPON_POOLCUE',
    name: 'WEAPON_POOLCUE',
    hash: 2484171525,
    label: 'Pool Cue',
    description: 'Ah, there\'s no sound as satisfying as the crack of a perfect break, especially when it\'s the other guy\'s spine. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_PoolCue.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_WRENCH: {
    id: 'WEAPON_WRENCH',
    name: 'WEAPON_WRENCH',
    hash: 419712736,
    label: 'Pipe Wrench',
    description: 'Perennial favourite of apocalyptic survivalists and violent fathers the world over, apparently it also doubles as some kind of tool. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_Wrench.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_COMPACTLAUNCHER: {
    id: 'WEAPON_COMPACTLAUNCHER',
    name: 'WEAPON_COMPACTLAUNCHER',
    hash: 125959754,
    label: 'Compact Grenade Launcher',
    description: 'Focus groups using the regular model suggested it was too accurate and found it awkward to use with one hand on the throttle. Easy fix. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_LR_CompactLauncher.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_GRENADELAUNCHER',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_COMPACTLAUNCHER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    }
  ]
  },
  WEAPON_PIPEBOMB: {
    id: 'WEAPON_PIPEBOMB',
    name: 'WEAPON_PIPEBOMB',
    hash: 3125143736,
    label: 'Pipe Bomb',
    description: 'Remember, it doesn\'t count as an IED when you buy it in a store and use it in a first world country. Part of Bikers.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_EX_PipeBomb.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PIPEBOMB',
    weaponCategory: 'GROUP_THROWN'
  },
  WEAPON_COMBATMG_MK2: {
    id: 'WEAPON_COMBATMG_MK2',
    name: 'WEAPON_COMBATMG_MK2',
    hash: 3686625920,
    label: 'Combat MG Mk II',
    description: 'You can never have too much of a good thing: after all, if the first shot counts, then the next hundred or so must count for double.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_combatmg_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_MG',
    weaponCategory: 'GROUP_MG',
    components: [
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_COMBATMG_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL_MK2',
      attachBone: 'WAPScop_2',
      label: 'Medium Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Large Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MG_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MG_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_ASSAULTRIFLE_MK2: {
    id: 'WEAPON_ASSAULTRIFLE_MK2',
    name: 'WEAPON_ASSAULTRIFLE_MK2',
    hash: 961495388,
    label: 'Assault Rifle Mk II',
    description: 'The definitive revision of an all-time classic: all it takes is a little work, and looks can kill after all.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_assaultrifle_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_ASSAULTRIFLE_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_MK2',
      attachBone: 'WAPScop_2',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Large Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_SMG_MK2: {
    id: 'WEAPON_SMG_MK2',
    name: 'WEAPON_SMG_MK2',
    hash: 2024373456,
    label: 'SMG Mk II',
    description: 'Lightweight, compact, with a rate of fire to die very messily for: turn any confined space into a kill box at the click of a well-oiled trigger.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_smg_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_SMG_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_MK2_CLIP_HOLLOWPOINT',
      attachBone: 'WAPClip',
      label: 'Hollow Point Rounds',
      description: 'Increased damage to targets without Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_SMG_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS_SMG',
      attachBone: 'WAPScop',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2',
      attachBone: 'WAPScop',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_SMALL_SMG_MK2',
      attachBone: 'WAPScop',
      label: 'Medium Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SB_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SB_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_CARBINERIFLE_MK2: {
    id: 'WEAPON_CARBINERIFLE_MK2',
    name: 'WEAPON_CARBINERIFLE_MK2',
    hash: 4208062921,
    label: 'Carbine Rifle Mk II',
    description: 'This is bespoke, artisan firepower: you couldn\'t deliver a hail of bullets with more love and care if you inserted them by hand.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AR_CarbineRifleMK2.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_CARBINERIFLE_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SIGHTS',
      attachBone: 'WAPScop_2',
      label: 'Holographic Sight',
      description: 'Accurate sight for close quarters combat.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO_MK2',
      attachBone: 'WAPScop_2',
      label: 'Small Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM_MK2',
      attachBone: 'WAPScop_2',
      label: 'Large Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_01',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_02',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_03',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_04',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_05',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_06',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_07',
      attachBone: 'WAPSupp_2',
      label: 'Muzzle Brake',
      description: 'Reduces recoil during rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP_02',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_CR_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_CR_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_PISTOL_MK2: {
    id: 'WEAPON_PISTOL_MK2',
    name: 'WEAPON_PISTOL_MK2',
    hash: 3219281620,
    label: 'Pistol Mk II',
    description: 'Balance, simplicity, precision: nothing keeps the peace like an extended barrel in the other guy\'s mouth.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_pistol_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_PISTOL',
    weaponCategory: 'GROUP_PISTOL',
    components: [
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_HOLLOWPOINT',
      attachBone: 'WAPClip',
      label: 'Hollow Point Rounds',
      description: 'Increased damage to targets without Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which include a chance to set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_CLIP_TRACER',
      attachBone: 'WAPClip',
      label: 'Tracer Rounds',
      description: 'Bullets with bright visible markers that match the tint of the gun. Standard capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_RAIL',
      attachBone: 'WAPScop',
      label: 'Mounted Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_FLSH_02',
      attachBone: 'WAPFlshLasr',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_PI_COMP',
      attachBone: 'WAPSupp',
      label: 'Compensator',
      description: 'Reduces recoil for rapid fire.',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_VARMOD_XM3_SLIDE',
      attachBone: 'WAPScop_2',
      label: 'COMPONENT_PISTOL_MK2_VARMOD_XM3_SLIDE',
      variants: []
    },
    {
      name: 'COMPONENT_PISTOL_MK2_VARMOD_XM3',
      attachBone: 'gun_root',
      label: 'Season\'s Greetings',
      variants: []
    }
  ]
  },
  WEAPON_HEAVYSNIPER_MK2: {
    id: 'WEAPON_HEAVYSNIPER_MK2',
    name: 'WEAPON_HEAVYSNIPER_MK2',
    hash: 177293209,
    label: 'Heavy Sniper Mk II',
    description: 'Far away, yet always intimate: if you\'re looking for a secure foundation for that long-distance relationship, this is it.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_heavysniper_mk2',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for regular ammo.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_ARMORPIERCING',
      attachBone: 'WAPClip',
      label: 'Armor Piercing Rounds',
      description: 'Increased penetration of Body Armor. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_EXPLOSIVE',
      attachBone: 'WAPClip',
      label: 'Explosive Rounds',
      description: 'Bullets which explode on impact. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_FMJ',
      attachBone: 'WAPClip',
      label: 'Full Metal Jacket Rounds',
      description: 'Increased damage to vehicles. Also penetrates bullet resistant and bulletproof vehicle glass. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYSNIPER_MK2_CLIP_INCENDIARY',
      attachBone: 'WAPClip',
      label: 'Incendiary Rounds',
      description: 'Bullets which set targets on fire when shot. Reduced capacity.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_LARGE_MK2',
      attachBone: 'WAPScop',
      label: 'Zoom Scope',
      description: 'Long-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MAX',
      attachBone: 'WAPScop',
      label: 'Advanced Scope',
      description: 'Maximum zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_NV',
      attachBone: 'WAPScop',
      label: 'Night Vision Scope',
      description: 'Long-range zoom with toggleable night vision.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_THERMAL',
      attachBone: 'WAPScop',
      label: 'Thermal Scope',
      description: 'Long-range zoom with toggleable thermal vision.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SR_SUPP_03',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_08',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil when firing.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_MUZZLE_09',
      attachBone: 'WAPSupp',
      label: 'Muzzle Brake',
      description: 'Reduces recoil when firing.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SR_BARREL_01',
      attachBone: 'WAPBarrel',
      label: 'Default Barrel',
      description: 'Stock barrel attachment.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SR_BARREL_02',
      attachBone: 'WAPBarrel',
      label: 'Heavy Barrel',
      description: 'Increases damage dealt to long-range targets.',
      variants: []
    }
  ]
  },
  WEAPON_STONE_HATCHET: {
    id: 'WEAPON_STONE_HATCHET',
    name: 'WEAPON_STONE_HATCHET',
    hash: 940833800,
    label: 'Stone Hatchet',
    description: 'There\'s retro, there\'s vintage, and there\'s this. After 500 years of technological development and spiritual apocalypse, pre-Colombian chic is back.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_ME_StoneHatchet.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_TACTICALRIFLE: {
    id: 'WEAPON_TACTICALRIFLE',
    name: 'WEAPON_TACTICALRIFLE',
    hash: 3520460075,
    label: 'Service Carbine',
    description: 'This season\'s must-have hardware for law enforcement, military personnel and anyone locked in a fight to the death with either law enforcement or military personnel.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_tacticalrifle',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_TACTICALRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Carbine Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_TACTICALRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Carbine Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH_REH',
      attachBone: 'WAPFlsh_2',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp_2',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip_2',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_PRECISIONRIFLE: {
    id: 'WEAPON_PRECISIONRIFLE',
    name: 'WEAPON_PRECISIONRIFLE',
    hash: 1853742572,
    label: 'Precision Rifle',
    description: 'A rifle for perfectionists. Because why settle for right-between-the-eyes, when you could have right-through-the-superior-frontal-gyrus?',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_precisionrifle',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNIPER',
    weaponCategory: 'GROUP_SNIPER',
    components: [
    {
      name: 'COMPONENT_PRECISIONRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Carbine Rifle.',
      variants: []
    }
  ]
  },
  WEAPON_HEAVYRIFLE: {
    id: 'WEAPON_HEAVYRIFLE',
    name: 'WEAPON_HEAVYRIFLE',
    hash: 3347935668,
    label: 'Heavy Rifle',
    description: 'The no-holds-barred 30-round answer to that eternal question, how do I get this guy off my back?',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_heavyrifle',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_HEAVYRIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Heavy Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYRIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Heavy Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_HEAVYRIFLE_SIGHT_01',
      attachBone: 'WAPScop_3',
      label: 'Iron Sights',
      description: 'Default rail-mounted iron sights.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MEDIUM',
      attachBone: 'WAPScop_3',
      label: 'Scope',
      description: 'Extended-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_FLSH',
      attachBone: 'WAPFlshLasr_3',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_3',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_AFGRIP',
      attachBone: 'WAPGrip_3',
      label: 'Grip',
      description: 'Improves weapon accuracy.',
      variants: []
    }
  ]
  },
  WEAPON_FERTILIZERCAN: {
    id: 'WEAPON_FERTILIZERCAN',
    name: 'WEAPON_FERTILIZERCAN',
    hash: 406929569,
    label: 'Fertilizer Can',
    description: 'Fertilizer Can',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_AM_FertilizerCan.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_FERTILIZERCAN',
    weaponCategory: 'GROUP_PETROLCAN'
  },
  WEAPON_EMPLAUNCHER: {
    id: 'WEAPON_EMPLAUNCHER',
    name: 'WEAPON_EMPLAUNCHER',
    hash: 3676729658,
    label: 'Compact EMP Launcher',
    description: 'Ever seen a confetti cannon? The Compact EMP Launcher is just like that, but instead of paper and happiness, it\'s an electromagnetic pulse, short circuits and shattered dreams.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_emplauncher',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_EMPLAUNCHER',
    weaponCategory: 'GROUP_HEAVY',
    components: [
    {
      name: 'COMPONENT_EMPLAUNCHER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    }
  ]
  },
  WEAPON_STUNGUN_MP: {
    id: 'WEAPON_STUNGUN_MP',
    name: 'WEAPON_STUNGUN_MP',
    hash: 1171102963,
    label: 'Stun Gun',
    description: 'It\'s, like, literally stunning.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_stungun_mp',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_STUNGUN',
    weaponCategory: 'GROUP_STUNGUN',
    components: [
    {
      name: 'COMPONENT_STUNGUN_VARMOD_BAIL',
      attachBone: 'gun_root',
      label: 'COMPONENT_STUNGUN_VARMOD_BAIL',
      description: 'Item available to purchase.',
      variants: []
    }
  ]
  },
  WEAPON_TECPISTOL: {
    id: 'WEAPON_TECPISTOL',
    name: 'WEAPON_TECPISTOL',
    hash: 350597077,
    label: 'Tactical SMG',
    description: 'Ever been on the wrong side of the Micro SMG\'s big bad brother? Trust us, with double the capacity for conflict resolution, you\'d know.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_tecpistol',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SMG',
    weaponCategory: 'GROUP_SMG',
    components: [
    {
      name: 'COMPONENT_TECPISTOL_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Tactical SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_TECPISTOL_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Tactical SMG.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP_02',
      attachBone: 'WAPSupp',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_SCOPE_MACRO',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Standard-range zoom functionality.',
      variants: []
    }
  ]
  },
  WEAPON_SNOWLAUNCHER: {
    id: 'WEAPON_SNOWLAUNCHER',
    name: 'WEAPON_SNOWLAUNCHER',
    hash: 62870901,
    label: 'Snowball Launcher',
    description: 'Snowball Launcher',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_snowlauncher',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_SNOWLAUNCHER',
    weaponCategory: 'GROUP_HEAVY'
  },
  WEAPON_BATTLERIFLE: {
    id: 'WEAPON_BATTLERIFLE',
    name: 'WEAPON_BATTLERIFLE',
    hash: 1924557585,
    label: 'Battle Rifle',
    description: 'Does life feel like a daily battle? Then why not try the Battle Rifle: the fast-acting pick-me-up that flawlessly targets every pain in your ass. Part of The Chop Shop.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'W_Win_BattleRifle.png',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_BATTLERIFLE_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      description: 'Standard capacity for Battle Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_BATTLERIFLE_CLIP_02',
      attachBone: 'WAPClip',
      label: 'Extended Clip',
      description: 'Extended capacity for Battle Rifle.',
      variants: []
    },
    {
      name: 'COMPONENT_AT_AR_SUPP',
      attachBone: 'WAPSupp_3',
      label: 'Suppressor',
      description: 'Reduces noise and muzzle flash.',
      variants: []
    }
  ]
  },
  WEAPON_STUNROD: {
    id: 'WEAPON_STUNROD',
    name: 'WEAPON_STUNROD',
    hash: 3670016037,
    label: 'The Shocker',
    description: 'When blunt force trauma just isn\'t enough, consider diversifying your approach to aggravated assault with a dose of 30,000 volts.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_stunrod',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'NULL',
    weaponCategory: 'GROUP_MELEE'
  },
  WEAPON_STRICKLER: {
    id: 'WEAPON_STRICKLER',
    name: 'WEAPON_STRICKLER',
    hash: 2378080583,
    label: 'El Strickler',
    description: 'Nothing screams \"Class Act\" like gunning down disloyal lackeys with a gold-plated Military Rifle. Part of Agents of Sabotage.',
    type: ItemType.WEAPON,
    weight: 1000,
    stackable: false,
    icon: 'weapon_strickler',
    usable: true,
    destroyable: true,
    maxDurability: 100,
    ammoType: 'AMMO_RIFLE',
    weaponCategory: 'GROUP_RIFLE',
    components: [
    {
      name: 'COMPONENT_STRICKLER_CLIP_01',
      attachBone: 'WAPClip',
      label: 'Default Clip',
      variants: []
    },
    {
      name: 'COMPONENT_STRICKLER_SIGHT',
      attachBone: 'WAPScop_2',
      label: 'Scope',
      description: 'Medium-range zoom functionality.',
      variants: []
    },
    {
      name: 'COMPONENT_STRICKLER_FLSH',
      attachBone: 'WAPFlshLasr_3',
      label: 'Flashlight',
      description: 'Aids low light target acquisition.',
      variants: []
    }
  ]
  }
};

/**
 * Get weapon definition by weapon name (O(1) lookup)
 */
export function getWeaponDefinition(weaponName: string): WeaponDefinition | undefined {
  return weaponDefinitions[weaponName];
}

/**
 * Get weapon definition by weapon hash
 */
export function getItemDefinitionByHash(weaponHash: string | number): WeaponDefinition | undefined {
  const hashNum = typeof weaponHash === 'string' ? parseInt(weaponHash) : weaponHash;
  return Object.values(weaponDefinitions).find(weapon => weapon.hash === hashNum);
}

// Only export for FiveM server environment
if (typeof global !== 'undefined' && global.exports) {
  global.exports('hm-inventory:getWeaponDefinition', getWeaponDefinition);
  global.exports('hm-inventory:getItemDefinitionByHash', getItemDefinitionByHash);
}