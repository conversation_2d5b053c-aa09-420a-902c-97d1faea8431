import React, { useEffect, useState } from 'react';
import { useNavigation } from '../../navigation/hooks';
import { useNavigationStore } from '../../navigation/navigationStore';
import { motion, AnimatePresence } from 'framer-motion';

// Import components
import VehicleCard from './components/VehicleCard';
import VehicleDetail from './components/VehicleDetail';

// Import store
import { useGarageStore } from './stores/garageStore';

// Import styles
import './styles/garage.css';

const Garage: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const { currentView } = useNavigationStore.getState();
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Get the current navigation entry and its data
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};

  // Get vehicle ID from navigation data
  const selectedVehicleId = navigationData.vehicleId ? Number(navigationData.vehicleId) : null;

  // Get data from the store
  const { vehicles, selectVehicle, trackVehicle, toggleEngine, toggleLock, getVehicleById } =
    useGarageStore();

  // Update selected vehicle when navigation changes
  useEffect(() => {
    if (selectedVehicleId) {
      selectVehicle(selectedVehicleId);
    }
  }, [selectedVehicleId, selectVehicle]);

  // Filter vehicles based on active filter and search query
  const filteredVehicles = vehicles.filter(vehicle => {
    // First apply category filter
    if (activeFilter !== 'all' && vehicle.category !== activeFilter) {
      return false;
    }

    // Then apply search filter if there's a query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        vehicle.name.toLowerCase().includes(query) ||
        vehicle.brand?.toLowerCase().includes(query) ||
        vehicle.licensePlate?.toLowerCase().includes(query) ||
        vehicle.plate.toLowerCase().includes(query) ||
        vehicle.location?.toLowerCase().includes(query) ||
        vehicle.garage?.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Get unique categories for filter buttons
  const categories = ['all', ...new Set(vehicles.map(v => v.category).filter(Boolean) as string[])];

  // Background style with gradient and blur
  const backgroundStyle = {
    backgroundImage: 'linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.9))',
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  };

  return (
    <div
      className="h-full w-full flex flex-col pt-8 backdrop-blur-sm"
      style={backgroundStyle}
    >
      {currentView === 'detail' ? (
        // Detail view header - glassmorphic
        <div className="px-4 py-3">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-9 h-9 rounded-full bg-white/10 backdrop-blur-md text-white/80 hover:bg-white/20 transition-colors"
          >
            <i className="fas fa-arrow-left"></i>
          </button>
        </div>
      ) : (
        // Main view header with glassmorphic search
        <div className="px-4 py-3">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-white text-xl font-medium">My Vehicles</h1>
            <div className="text-white/60 text-xs backdrop-blur-md bg-white/5 px-3 py-1 rounded-full">
              {filteredVehicles.length} vehicles
            </div>
          </div>

          {/* Search bar - glassmorphic */}
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="fas fa-search text-white/40 text-sm"></i>
            </div>
            <input
              type="text"
              placeholder="Search vehicles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full backdrop-blur-md bg-white/10 text-white rounded-lg py-2 pl-9 pr-9 text-sm border border-white/20 focus:outline-none focus:border-white/40 transition-colors"
            />
            {searchQuery && (
              <button
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-white/40 hover:text-white/80"
                onClick={() => setSearchQuery('')}
              >
                <i className="fas fa-times text-sm"></i>
              </button>
            )}
          </div>

          {/* Category pills - scrollable container with fixed height */}
          <div className="flex overflow-x-auto pb-2 hide-scrollbar">
            <div className="flex space-x-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveFilter(category)}
                  className={`px-3 py-1.5 text-xs font-medium whitespace-nowrap rounded-full backdrop-blur-md transition-colors ${
                    activeFilter === category
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10'
                  }`}
                >
                  {category === 'all' ? 'All Vehicles' : category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {(currentView === 'main' || !currentView) && (
        <div className="flex-1 overflow-auto px-4 py-3">
          {filteredVehicles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <div className="w-16 h-16 rounded-full backdrop-blur-md bg-white/10 border border-white/20 flex items-center justify-center mb-3">
                <i className="fas fa-car text-2xl text-white/60"></i>
              </div>
              <h3 className="text-lg font-medium text-white mb-1">No vehicles found</h3>
              <p className="text-white/60 text-sm max-w-xs">
                {searchQuery
                  ? "Try a different search term"
                  : activeFilter !== 'all'
                    ? `No ${activeFilter} vehicles available`
                    : "You don't have any vehicles in your garage"}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence>
                {filteredVehicles.map(vehicle => (
                  <motion.div
                    key={vehicle.id}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <VehicleCard
                      vehicle={vehicle}
                      isSelected={selectedVehicleId === vehicle.id}
                      onSelect={id => openView('detail', { vehicleId: id })}
                      onTrack={trackVehicle}
                      onToggleEngine={toggleEngine}
                      onToggleLock={toggleLock}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Bottom padding to ensure last card is fully visible */}
              <div className="h-2"></div>
            </div>
          )}
        </div>
      )}

      {/* Detail View */}
      {currentView === 'detail' && selectedVehicleId && (
        <div className="flex-1 overflow-hidden">
          {(() => {
            const vehicle = getVehicleById(selectedVehicleId);
            if (!vehicle) return (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <div className="w-16 h-16 rounded-full backdrop-blur-md bg-white/10 border border-white/20 flex items-center justify-center mb-3">
                  <i className="fas fa-exclamation-triangle text-2xl text-yellow-400"></i>
                </div>
                <h3 className="text-lg font-medium text-white mb-1">Vehicle not found</h3>
                <p className="text-white/60 text-sm max-w-xs mb-4">This vehicle may no longer be available</p>
                <button
                  onClick={goBack}
                  className="px-5 py-2 backdrop-blur-md bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg border border-white/20 transition-colors"
                >
                  Back to Garage
                </button>
              </div>
            );

            return (
              <VehicleDetail
                vehicle={vehicle}
                onTrack={trackVehicle}
                onToggleEngine={toggleEngine}
              />
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default Garage;
