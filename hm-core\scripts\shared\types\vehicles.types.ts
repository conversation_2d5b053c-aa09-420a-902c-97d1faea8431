/**
 * Core vehicle metadata types for HM Framework
 * Essential parameters only - converted from QBCore vehicles.lua
 */

export enum VehicleCategory {
    COMPACTS = 'compacts',
    SEDANS = 'sedans',
    SUVS = 'suvs',
    COUPES = 'coupes',
    MUSCLE = 'muscle',
    SPORTS_CLASSICS = 'sports_classics',
    SPORTS = 'sports',
    SUPER = 'super',
    MOTORCYCLES = 'motorcycles',
    OFF_ROAD = 'offroad',
    INDUSTRIAL = 'industrial',
    UTILITY = 'utility',
    VANS = 'vans',
    CYCLES = 'cycles',
    BOATS = 'boats',
    HELICOPTERS = 'helicopters',
    PLANES = 'planes',
    SERVICE = 'service',
    EMERGENCY = 'emergency',
    MILITARY = 'military',
    COMMERCIAL = 'commercial',
    TRAINS = 'trains',
}

export enum VehicleType {
    AUTOMOBILE = 'automobile',
    BIKE = 'bike',
    HELI = 'heli',
    PLANE = 'plane',
    BOAT = 'boat',
    TRAIN = 'train',
}

/**
 * Essential vehicle metadata interface
 * Contains only the core parameters needed for basic functionality
 */
export interface VehicleMetadata {
    model: string;
    name: string;
    category: VehicleCategory;
    brand: string;
    type: VehicleType;
}

/**
 * Vehicle database type - maps model name to metadata
 */
export type VehicleDatabase = Record<string, VehicleMetadata>;
