fx_version 'cerulean'
game 'gta5'
name 'hm-polyzones'
description 'PolyZone and grid management system for HM-Framework'
author 'HM-Core'
version '1.0.0'

node_version '22' -- Assuming same node version as hm-admin

dependencies {
    'hm-core' -- Assuming hm-core is a common dependency
}

-- Compiled TypeScript files
shared_script 'build/shared.js'
client_script 'build/client.js'
server_script 'build/server.js' -- Even if minimal, good to have for consistency
