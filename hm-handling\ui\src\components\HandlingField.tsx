import React from 'react';
import { BaseHandlingData, HANDLING_FIELD_LIMITS } from '../../../scripts/shared/types';
import { isValueInBounds, getFieldStep, isStringField, isIntegerField } from '../utils/fieldUtils';

interface HandlingFieldProps {
  field: keyof BaseHandlingData;
  currentValue: number | string;
  stockValue: number | string;
  onValueChange: (field: keyof BaseHandlingData, value: number) => void;
  onReset: (field: keyof BaseHandlingData) => void;
  onStringChange?: (field: keyof BaseHandlingData, value: string) => void;
  onApplyField?: (field: keyof BaseHandlingData) => void;
  isModified?: boolean;
  isApplied?: boolean;
}

const HandlingField: React.FC<HandlingFieldProps> = ({
  field,
  currentValue,
  stockValue,
  onValueChange,
  onReset,
  onStringChange,
  onApplyField,
  isModified = false,
  isApplied = false
}) => {
  const formatFieldName = (fieldName: string): string => {
    // Use the field name directly without formatting
    return fieldName;
  };
  // Get field limits
  const limits = HANDLING_FIELD_LIMITS[field];
  const { min, max } = limits || {};
  const step = getFieldStep(field);
  const isString = isStringField(field);
  const isInteger = isIntegerField(field);
  const isChanged = currentValue !== stockValue || isModified;
  const isValidValue = isString ? 
    (currentValue !== undefined && currentValue !== null) :
    (typeof currentValue === 'number' && !isNaN(currentValue));
  
  // Check if value is within limits (only for numeric fields)
  const isOutOfBounds = !isString && typeof currentValue === 'number' && !isValueInBounds(field, currentValue);
  
  const displayStockValue = isString ? 
    (stockValue?.toString() || 'N/A') :
    (typeof stockValue === 'number' && !isNaN(stockValue) ? 
      (isInteger ? stockValue.toString() : stockValue.toFixed(3)) : 'N/A');
      
  const displayCurrentValue = isString ?
    (currentValue?.toString() || 'N/A') :
    (isValidValue && typeof currentValue === 'number' ? 
      (isInteger ? currentValue.toString() : currentValue.toFixed(3)) : 'N/A');  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (isString) {
      // Handle string fields
      if (onStringChange) {
        onStringChange(field, e.target.value);
      }
    } else {
      const value = isInteger ? parseInt(e.target.value) : parseFloat(e.target.value);
      if (!isNaN(value)) {
        onValueChange(field, value);
      }
    }
  };
  const handleResetClick = () => {
    onReset(field);
  };
  const handleConfirmClick = () => {
    if (onApplyField && isChanged) {
      onApplyField(field);
      console.log(`Applied field ${field}:`, currentValue);
    }
  };  return (
    <div className={`handling-field-compact ${isChanged ? 'changed' : ''} ${isApplied ? 'applied' : ''} ${isOutOfBounds ? 'out-of-bounds' : ''}`}>
      <button
        className={`reset-btn ${isChanged ? 'active' : ''}`}
        onClick={handleResetClick}
        disabled={!isChanged}
        title="Reset to stock value"
      >
        ↻
      </button>
      <span className="field-name">{formatFieldName(field)}</span>
      <span className="stock-value">{displayStockValue}</span>
      <input
        type={isString ? "text" : "number"}
        step={isString ? undefined : step}
        min={isString ? undefined : min}
        max={isString ? undefined : max}
        className="current-value-input"
        value={isString ? (currentValue?.toString() || '') : (isValidValue && typeof currentValue === 'number' ? currentValue.toString() : '')}
        onChange={handleInputChange}
        placeholder={isString ? "Enter text" : "0.000"}
        title={limits && !isString ? `Min: ${min ?? 'none'}, Max: ${max ?? 'none'}, Step: ${step}` : undefined}
      />      <button
        className={`confirm-btn ${isChanged ? 'active' : ''} ${isApplied ? 'applied' : ''}`}
        onClick={handleConfirmClick}
        disabled={!isChanged}
        title={isApplied ? "Already applied" : "Apply this field immediately"}
      >
        {isApplied ? '✓' : '⚡'}
      </button>
    </div>
  );
};

export default HandlingField;
