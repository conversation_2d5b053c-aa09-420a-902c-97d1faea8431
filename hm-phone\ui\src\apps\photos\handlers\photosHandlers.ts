/**
 * Photos app message handlers
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { usePhotosStore } from '../stores/photosStore';
import { Photo } from '../stores/photosStore';

// Register handler for photos data
registerEventHandler('photos', 'photos', data => {
  console.log('[Photos] Received photos data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the photos data
    usePhotosStore.getState().handlers.onSetPhotos(data as Photo[]);
  } else {
    console.error('[Photos] Received invalid photos data (not an array):', data);
  }
});

// Register handler for photo saved
registerEventHandler('photos', 'photoSaved', data => {
  console.log('[Photos] Photo saved:', data);

  // Validate data
  if (data && typeof data === 'object' && 'id' in data && 'imageUrl' in data) {
    // Update the store with the saved photo
    usePhotosStore.getState().handlers.onPhotoSaved(data as Photo);
  } else {
    console.error('[Photos] Received invalid photo data:', data);
  }
});

// Register handler for photo deleted
registerEventHandler('photos', 'photoDeleted', data => {
  console.log('[Photos] Photo deleted:', data);

  // Validate data
  if (data && typeof data === 'number') {
    // Update the store with the deleted photo ID
    usePhotosStore.getState().handlers.onPhotoDeleted(data);
  } else {
    console.error('[Photos] Received invalid photo ID:', data);
  }
});

// Register handler for errors
registerEventHandler('photos', 'error', data => {
  console.error('[Photos] Error:', data);
});
