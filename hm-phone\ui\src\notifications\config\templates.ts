import { NotificationTemplate, NotificationType } from '../types/notificationTypes';

// App IDs for type safety
export const APP_IDS = {
  MESSAGES: 5,
  DIALER: 6,
  LIFESNAP: 10,
  LOVELINK: 13
} as const;

// Template configurations
const templates: Record<NotificationType, NotificationTemplate> = {
  default: {
    layout: 'basic',
    colors: {
      accent: 'bg-gray-600',
      background: 'bg-[#1a1a1a]',
      text: 'text-white'
    }
  },

  message: {
    layout: 'avatar',
    icon: 'fa-message',
    colors: {
      accent: 'bg-blue-500',
      background: 'bg-[#1a1a1a]',
      text: 'text-white'
    },
    actions: {
      primary: {
        label: 'Reply',
        icon: 'fa-reply'
      }
    }
  },

  call: {
    layout: 'avatar',
    icon: 'fa-phone',
    colors: {
      accent: 'bg-green-500',
      background: 'bg-[#1a1a1a]',
      text: 'text-white'
    },
    actions: {
      primary: {
        label: 'Answer',
        icon: 'fa-phone'
      },
      secondary: {
        label: 'Decline',
        icon: 'fa-phone-slash'
      }
    }
  },

  photo: {
    layout: 'media',
    icon: 'fa-camera',
    colors: {
      accent: 'bg-purple-500',
      background: 'bg-[#1a1a1a]',
      text: 'text-white'
    }
  },

  custom: {
    layout: 'basic',
    colors: {
      accent: 'bg-indigo-500',
      background: 'bg-[#1a1a1a]',
      text: 'text-white'
    }
  }
};

export default templates;
