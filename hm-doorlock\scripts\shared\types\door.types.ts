/**
 * HM Door System - Core Types
 */

// ==================== CORE TYPES ====================

/**
 * Unique identifier for doors in the system
 */
export type DoorId = string;

/**
 * Door hash used by FiveM door system
 */
export type DoorHash = string;

/**
 * Job/role identifier
 */
export type JobId = string;

/**
 * Gang identifier
 */
export type GangId = string;

/**
 * Player identifier
 */
export type PlayerId = string;

// ==================== DOOR STATE ====================

/**
 * Current state of a door (matches FiveM door system states)
 */
export enum DoorState {
  UNLOCKED = 0,
  LOCKED = 1,
  FORCE_LOCKED_UNTIL_OUT_OF_AREA = 2,
  FORCE_UNLOCKED_THIS_FRAME = 3,
  FORCE_LOCKED_THIS_FRAME = 4,
  FORCE_OPEN_THIS_FRAME = 5,
  FORCE_CLOSED_THIS_FRAME = 6,
}

/**
 * Door types for system categorization
 */
export enum DoorType {
  SINGLE = "single",
  DOUBLE = "double",
  GATE = "gate",
}

/**
 * Door lock mechanism types
 */
export enum DoorLockType {
  MANUAL = "manual",
  AUTOMATIC = "automatic",
  KEYCARD = "keycard",
  BIOMETRIC = "biometric",
  TIMED = "timed",
  PROXIMITY = "proximity",
}

// ==================== ACCESS CONTROL ====================

/**
 * Access permissions for a door
 */
export interface DoorAccessControl {
  /** Jobs that can access this door */
  allowedJobs?: JobId[];
  /** Gangs that can access this door */
  allowedGangs?: GangId[];
  /** Specific player IDs that can access */
  allowedPlayers?: PlayerId[];
  /** Items required to access door */
  requiredItems?: string[];
  /** Minimum job grade required */
  minimumJobGrade?: number;
  /** Whether emergency services can override */
  emergencyOverride?: boolean;
}

// ==================== DOOR DEFINITION ====================

/**
 * Base door configuration for all door types
 */
export interface BaseDoorDefinition {
  /** Unique identifier for this door */
  id: DoorId;

  /** Unique hash for this door, used by FiveM */
  hash: DoorHash;

  /** Display name for this door */
  name: string;

  /** Default state when server starts */
  defaultState: DoorState;

  /** Type of lock mechanism */
  lockType: DoorLockType;

  /** Access control settings */
  accessControl: DoorAccessControl;
}

/**
 * Single door part configuration
 */
export interface DoorPart {
  /** World coordinates of the door */
  coords: { x: number; y: number; z: number };

  /** Door model hash or string */
  model: string | number;

  /** Optional heading/rotation */
  heading?: number;
}

/**
 * Single door configuration
 */
export interface SingleDoorDefinition extends BaseDoorDefinition {
  type: DoorType.SINGLE;
  door: DoorPart;
}

/**
 * Double door configuration
 */
export interface DoubleDoorDefinition extends BaseDoorDefinition {
  type: DoorType.DOUBLE;
  doors: [DoorPart, DoorPart]; // Exactly 2 doors
}

/**
 * Gate configuration (could be single or multiple parts)
 */
export interface GateDefinition extends BaseDoorDefinition {
  type: DoorType.GATE;
  parts: DoorPart[]; // Multiple parts for complex gates
}

/**
 * Union type for all door definitions
 */
export type DoorDefinition =
  | SingleDoorDefinition
  | DoubleDoorDefinition
  | GateDefinition;

// Copied from hm-target/scripts/shared/types.ts to avoid import issues
export interface TargetOption {
  id: string;
  label: string;
  icon?: string;
  action: string; // This is an event name
  distance?: number;
  job?: string | string[];
  gang?: string | string[];
  stateid?: string | string[];
  item?: string;
  canInteract?: (entity?: number, distance?: number, data?: any) => boolean; // Added data parameter
  showProgress?: boolean;
  progressDuration?: number;
  progressLabel?: string;
  disabled?: boolean;
  data?: Record<string, any>;
}
