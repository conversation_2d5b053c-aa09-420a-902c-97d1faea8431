/**
 * Notes App - Server Side
 *
 * This file handles server-side functionality for the Notes app.
 */

import config from "@shared/config";

/**
 * Initialize the notes app
 */
export function initializeNotesApp(): void {
    registerServerEvents();
    ensureDatabaseTables();
    
    // Listen for player loaded event to ensure notes data is available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Notes] Player ${source} loaded, notes functionality ready`);
    });
}

/**
 * Ensure database tables exist for notes
 */
function ensureDatabaseTables(): void {
        // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[Photos] Auto-create tables is disabled, skipping table creation');
        return;
    }
    try {
        // Create notes table if it doesn't exist
        global.exports.oxmysql.query(`
            CREATE TABLE IF NOT EXISTS phone_notes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_identifier VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                content TEXT,
                category_id INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_player_identifier (player_identifier)
            );
        `);

        // Create note categories table
        global.exports.oxmysql.query(`
            CREATE TABLE IF NOT EXISTS phone_note_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_identifier VARCHAR(50) NOT NULL,
                name VARCHAR(100) NOT NULL,
                color VARCHAR(20) DEFAULT '#3B82F6',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_player_identifier (player_identifier)
            );
        `);

        console.log('[Notes] Database tables ensured');
    } catch (error) {
        console.error('[Notes] Error ensuring database tables:', error);
    }
}

/**
 * Register server events for the notes app
 */
function registerServerEvents(): void {
    // Register event for getting notes
    onNet('hm-phone:getNotes', async () => {
        const source = global.source;
        console.log(`[Notes] Received getNotes event from player ${source}`);
        try {
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Notes] Player ${source} not found`);
                emitNet('hm-phone:notesError', source, 'Player not found');
                return;            }            const stateid = player.stateid;
            const identifier = player.identifier;
            const playerIdentifier = stateid || identifier;

            // Get notes for the player
            const notes = await global.exports.oxmysql.query_async(
                'SELECT * FROM phone_notes WHERE player_identifier = ? ORDER BY updated_at DESC',
                [playerIdentifier]
            );

            // Get categories
            const categories = await global.exports.oxmysql.query_async(
                'SELECT * FROM phone_note_categories WHERE player_identifier = ? ORDER BY name',
                [playerIdentifier]
            );

            emitNet('hm-phone:notes', source, { notes, categories });
            console.log(`[Notes] Sent ${notes.length} notes and ${categories.length} categories to player ${source}`);
        } catch (error) {
            console.error(`[Notes] Error getting notes: ${error}`);
            emitNet('hm-phone:notesError', source, 'Error getting notes');
        }
    });

    // Register event for creating a note
    onNet('hm-phone:createNote', async (noteData: any) => {
        const source = global.source;
        console.log(`[Notes] Received createNote event from player ${source}`);
        try {
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Notes] Player ${source} not found`);
                emitNet('hm-phone:notesError', source, 'Player not found');
                return;
            }

            const stateid = player.stateid;
            const identifier = player.identifier;
            const playerIdentifier = stateid || identifier;

            const { title, content, categoryId } = noteData;

            // Create the note
            const result = await global.exports.oxmysql.query_async(
                'INSERT INTO phone_notes (player_identifier, title, content, category_id) VALUES (?, ?, ?, ?)',
                [playerIdentifier, title, content, categoryId]
            );

            const note = {
                id: result.insertId,
                player_identifier: playerIdentifier,
                title,
                content,
                category_id: categoryId,
                created_at: new Date(),
                updated_at: new Date()
            };

            emitNet('hm-phone:noteCreated', source, note);
            console.log(`[Notes] Note created for player ${source}: ${note.id}`);
        } catch (error) {
            console.error(`[Notes] Error creating note: ${error}`);
            emitNet('hm-phone:notesError', source, 'Error creating note');
        }
    });

    // Register event for updating a note
    onNet('hm-phone:updateNote', async (noteData: any) => {
        const source = global.source;
        console.log(`[Notes] Received updateNote event from player ${source}`);
        try {
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Notes] Player ${source} not found`);
                emitNet('hm-phone:notesError', source, 'Player not found');
                return;
            }

            const stateid = player.stateid;
            const identifier = player.identifier;
            const playerIdentifier = stateid || identifier;

            const { id, title, content, categoryId } = noteData;

            // Update the note
            await global.exports.oxmysql.query_async(
                'UPDATE phone_notes SET title = ?, content = ?, category_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND player_identifier = ?',
                [title, content, categoryId, id, playerIdentifier]
            );

            const updatedNote = {
                id,
                player_identifier: playerIdentifier,
                title,
                content,
                category_id: categoryId,
                updated_at: new Date()
            };

            emitNet('hm-phone:noteUpdated', source, updatedNote);
            console.log(`[Notes] Note updated for player ${source}: ${id}`);
        } catch (error) {
            console.error(`[Notes] Error updating note: ${error}`);
            emitNet('hm-phone:notesError', source, 'Error updating note');
        }
    });

    // Register event for deleting a note
    onNet('hm-phone:deleteNote', async (noteId: number) => {
        const source = global.source;
        console.log(`[Notes] Received deleteNote event from player ${source}`);
        try {
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Notes] Player ${source} not found`);
                emitNet('hm-phone:notesError', source, 'Player not found');
                return;
            }

            const stateid = player.stateid;
            const identifier = player.identifier;
            const playerIdentifier = stateid || identifier;

            // Delete the note
            await global.exports.oxmysql.query_async(
                'DELETE FROM phone_notes WHERE id = ? AND player_identifier = ?',
                [noteId, playerIdentifier]
            );

            emitNet('hm-phone:noteDeleted', source, noteId);
            console.log(`[Notes] Note deleted for player ${source}: ${noteId}`);
        } catch (error) {
            console.error(`[Notes] Error deleting note: ${error}`);
            emitNet('hm-phone:notesError', source, 'Error deleting note');
        }
    });

    // Register event for creating a category
    onNet('hm-phone:createNoteCategory', async (categoryData: any) => {
        const source = global.source;
        console.log(`[Notes] Received createNoteCategory event from player ${source}`);
        try {
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Notes] Player ${source} not found`);
                emitNet('hm-phone:notesError', source, 'Player not found');
                return;
            }

            const stateid = player.stateid;
            const identifier = player.identifier;
            const playerIdentifier = stateid || identifier;

            const { name, color } = categoryData;

            // Create the category
            const result = await global.exports.oxmysql.query_async(
                'INSERT INTO phone_note_categories (player_identifier, name, color) VALUES (?, ?, ?)',
                [playerIdentifier, name, color]
            );

            const category = {
                id: result.insertId,
                player_identifier: playerIdentifier,
                name,
                color,
                created_at: new Date()
            };

            emitNet('hm-phone:noteCategoryCreated', source, category);
            console.log(`[Notes] Category created for player ${source}: ${category.id}`);
        } catch (error) {
            console.error(`[Notes] Error creating category: ${error}`);
            emitNet('hm-phone:notesError', source, 'Error creating category');
        }
    });
}
