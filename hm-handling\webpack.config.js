const path = require('path');

module.exports = {
    entry: {
        client: './scripts/client/main.ts',
        server: './scripts/server/main.ts',
        shared: './scripts/shared/main.ts'
    },
    output: {
        path: path.resolve(__dirname, 'build'),
        filename: '[name].js',
        libraryTarget: 'this'
    },
    resolve: {
        extensions: ['.ts', '.js'],
        alias: {
            '@shared': path.resolve(__dirname, 'scripts/shared'),
            '@client': path.resolve(__dirname, 'scripts/client'),
            '@server': path.resolve(__dirname, 'scripts/server'),
            '@core': path.resolve(__dirname, '../hm-core/scripts')
        }
    },
    module: {
        rules: [
            {
                test: /\.ts$/,
                use: 'ts-loader',
                exclude: /node_modules/
            }
        ]
    },
    target: 'node',
    mode: 'production',
    optimization: {
        minimize: true
    }
};
