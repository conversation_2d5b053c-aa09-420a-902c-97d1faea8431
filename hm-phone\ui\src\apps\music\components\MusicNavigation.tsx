import React from 'react';

const MusicNavigation: React.FC = () => {
  return (
    <div className="bg-black/90 backdrop-blur-md h-full flex flex-col border-t-0">
      {/* Navigation content */}
      <div className="flex justify-around items-center w-full px-2 h-12">
        <button className="flex flex-col items-center cursor-pointer">
          <i className="fas fa-home text-base text-pink-500"></i>
          <span className="text-xs text-pink-500">Home</span>
        </button>
        <button className="flex flex-col items-center cursor-pointer">
          <i className="fas fa-search text-base"></i>
          <span className="text-xs">Explore</span>
        </button>
        <button className="flex flex-col items-center cursor-pointer">
          <i className="fas fa-book text-base"></i>
          <span className="text-xs">Library</span>
        </button>
      </div>

      {/* Padding for PullIndicator */}
      <div className="h-[24px]"></div>
    </div>
  );
};

export default MusicNavigation;
