/**
 * Webhook Service
 *
 * This service handles sending data to webhooks.
 * It supports multiple webhook providers and is designed to be extensible.
 */

import config from '@shared/config';
import axios from 'axios';
import * as crypto from 'crypto';

// Extract webhook config for easier access
const { webhook: webhookConfig } = config;

/**
 * Custom FormData implementation for Node.js
 * This is needed because FormData is not available in Node.js by default
 */
class CustomFormData {
    private boundary: string;
    private parts: { name: string; value: any; filename?: string }[];

    constructor() {
        this.boundary = `----WebKitFormBoundary${crypto.randomBytes(16).toString('hex')}`;
        this.parts = [];
    }

    append(name: string, value: any, filename?: string): void {
        this.parts.push({ name, value, filename });
    }

    getHeaders(): Record<string, string> {
        return {
            'Content-Type': `multipart/form-data; boundary=${this.boundary}`
        };
    }

    getBuffer(): Buffer {
        const buffers: Buffer[] = [];

        // Add each part to the form data
        this.parts.forEach(part => {
            // Start boundary
            buffers.push(Buffer.from(`--${this.boundary}\r\n`));

            // Content disposition
            let header = `Content-Disposition: form-data; name="${part.name}"`;
            if (part.filename) {
                header += `; filename="${part.filename}"`;
            }
            buffers.push(Buffer.from(`${header}\r\n`));

            // Content type if it's a file
            if (part.filename) {
                buffers.push(Buffer.from('Content-Type: application/octet-stream\r\n'));
            }

            // Empty line before content
            buffers.push(Buffer.from('\r\n'));

            // Content
            if (Buffer.isBuffer(part.value)) {
                buffers.push(part.value);
            } else {
                buffers.push(Buffer.from(String(part.value)));
            }

            // End of part
            buffers.push(Buffer.from('\r\n'));
        });

        // End boundary
        buffers.push(Buffer.from(`--${this.boundary}--\r\n`));

        // Combine all buffers
        return Buffer.concat(buffers);
    }
}

/**
 * Interface for webhook providers
 */
interface WebhookProvider {
    /**
     * Send data to the webhook
     * @param data The data to send
     * @param options Additional options
     * @returns Promise with the response
     */
    send(data: any, options?: any): Promise<any>;
}

/**
 * Discord webhook provider
 */
class DiscordWebhookProvider implements WebhookProvider {
    /**
     * Send data to a Discord webhook
     * @param data The data to send
     * @param options Additional options
     * @returns Promise with the response
     */
    async send(data: any, options?: any): Promise<any> {
        const { url, username, avatarUrl, content, embeds, files } = data;

        if (!url) {
            throw new Error('Discord webhook URL is required');
        }

        // Create form data for file uploads
        const formData = new CustomFormData();

        // Add webhook payload
        const payload: any = {};

        if (username) {
            payload.username = username;
        } else if (webhookConfig.discord.username) {
            payload.username = webhookConfig.discord.username;
        }

        if (avatarUrl) {
            payload.avatar_url = avatarUrl;
        } else if (webhookConfig.discord.avatarUrl) {
            payload.avatar_url = webhookConfig.discord.avatarUrl;
        }

        if (content) {
            payload.content = content;
        }

        if (embeds) {
            payload.embeds = embeds;
        }

        formData.append('payload_json', JSON.stringify(payload));

        // Add files if any
        if (files && Array.isArray(files)) {
            files.forEach((file, index) => {
                formData.append(`file${index}`, file.data, file.name);
            });
        }

        try {
            // Send the webhook request
            const response = await axios.post(url, formData.getBuffer(), {
                headers: {
                    ...formData.getHeaders()
                }
            });

            return response.data;
        } catch (error) {
            console.error('[Webhook] Discord webhook error:', error);
            throw error;
        }
    }
}

/**
 * Main webhook service
 */
class WebhookService {
    private providers: Record<string, WebhookProvider> = {};

    constructor() {
        // Register default providers
        this.registerProvider('discord', new DiscordWebhookProvider());
    }

    /**
     * Register a webhook provider
     * @param name Provider name
     * @param provider Provider implementation
     */
    registerProvider(name: string, provider: WebhookProvider): void {
        this.providers[name] = provider;
    }

    /**
     * Get a webhook provider
     * @param name Provider name
     * @returns The provider or undefined if not found
     */
    getProvider(name: string): WebhookProvider | undefined {
        return this.providers[name];
    }

    /**
     * Send data to a webhook using the specified provider
     * @param providerName Provider name
     * @param data Data to send
     * @param options Additional options
     * @returns Promise with the response
     */
    async send(providerName: string, data: any, options?: any): Promise<any> {
        const provider = this.getProvider(providerName);

        if (!provider) {
            throw new Error(`Webhook provider '${providerName}' not found`);
        }

        return provider.send(data, options);
    }

    /**
     * Send data to a webhook using the default provider
     * @param data Data to send
     * @param options Additional options
     * @returns Promise with the response
     */
    async sendWithDefaultProvider(data: any, options?: any): Promise<any> {
        const defaultProvider = webhookConfig.general.defaultProvider;
        return this.send(defaultProvider, data, options);
    }

    /**
     * Send a photo to a webhook
     * @param base64Data Base64-encoded image data
     * @param metadata Additional metadata
     * @returns Promise with the response including the image URL
     */
    async sendPhoto(base64Data: string, metadata: any = {}): Promise<string> {
        if (!webhookConfig.general.enabled) {
            throw new Error('Webhooks are disabled in the configuration');
        }

        // Extract the base64 data (remove the data:image/jpeg;base64, part)
        const base64Image = base64Data.replace(/^data:image\/\w+;base64,/, '');

        // Convert base64 to buffer
        const buffer = Buffer.from(base64Image, 'base64');

        // Generate a filename
        const timestamp = Date.now();
        const filename = `photo_${timestamp}.jpg`;

        // Create embeds if metadata is included
        const embeds = [];

        if (webhookConfig.discord.includeMetadata && metadata) {
            const embed: any = {
                title: 'Photo Information',
                color: 0x3498db, // Blue color
                fields: [],
                timestamp: new Date().toISOString()
            };

            // Add player information if available
            if (metadata.playerName) {
                embed.fields.push({
                    name: 'Player',
                    value: metadata.playerName,
                    inline: true
                });
            }

            // Add camera mode if available
            if (metadata.mode) {
                embed.fields.push({
                    name: 'Mode',
                    value: metadata.mode === 'selfie' ? 'Selfie' : 'Photo',
                    inline: true
                });
            }

            // Add flash information if available
            if (metadata.flash !== undefined) {
                embed.fields.push({
                    name: 'Flash',
                    value: metadata.flash ? 'On' : 'Off',
                    inline: true
                });
            }

            // Add location information if available
            if (metadata.location) {
                embed.fields.push({
                    name: 'Location',
                    value: metadata.location.name || 'Unknown',
                    inline: true
                });
            }

            embeds.push(embed);
        }

        // Prepare the webhook data
        const webhookData = {
            url: webhookConfig.discord.photoWebhookUrl,
            username: webhookConfig.discord.username,
            avatarUrl: webhookConfig.discord.avatarUrl,
            content: 'New photo uploaded',
            embeds: embeds.length > 0 ? embeds : undefined,
            files: [
                {
                    name: filename,
                    data: buffer
                }
            ]
        };

        try {
            // Send the webhook
            const response = await this.send('discord', webhookData);

            // Discord returns attachments array with URLs
            if (response && response.attachments && response.attachments.length > 0) {
                return response.attachments[0].url;
            }

            // If we can't get the URL from the response, throw an error
            throw new Error('Failed to get image URL from Discord response');
        } catch (error) {
            console.error('[Webhook] Error sending photo:', error);
            throw error;
        }
    }

    /**
     * Send data to a webhook using the specified provider
     * @param type 'photo' | 'video'
     * @param data The data to send
     * @param metadata Additional metadata (e.g., source, timestamp)
     * @returns Promise with the response
     */
    async sendMediaToWebhook(type: 'photo' | 'video', data: any, metadata: any): Promise<any> {
        if (!webhookConfig.general.enabled) {
            console.log('[Webhook] Webhook service is disabled in config.');
            return { success: false, message: 'Webhook service disabled' };
        }

        const providerName = webhookConfig.general.defaultProvider;
        const provider = this.getProvider(providerName);

        if (!provider) {
            throw new Error(`Webhook provider '${providerName}' not found`);
        }

        const webhookUrl = webhookConfig.discord.photoWebhookUrl; // Assuming Discord for now
        if (!webhookUrl) {
            throw new Error('Discord webhook URL is not configured');
        }

        // Prepare data for Discord
        const discordData: any = {
            url: webhookUrl,
            username: webhookConfig.discord.username,
            avatarUrl: webhookConfig.discord.avatarUrl,
            content: `New ${type} received!`, // Simple content
            embeds: [],
            files: []
        };

        // Add metadata to embed
        const embed: any = {
            title: `${type.charAt(0).toUpperCase() + type.slice(1)} Details`,
            color: type === 'photo' ? 0x00ff00 : 0x0000ff, // Green for photo, Blue for video
            fields: [],
            timestamp: new Date().toISOString()
        };

        if (metadata) {
            if (metadata.source) {
                embed.fields.push({ name: 'Source Player ID', value: String(metadata.source), inline: true });
            }
            if (metadata.mode) {
                embed.fields.push({ name: 'Camera Mode', value: metadata.mode, inline: true });
            }
            if (metadata.flash !== undefined) {
                embed.fields.push({ name: 'Flash Used', value: metadata.flash ? 'Yes' : 'No', inline: true });
            }
            if (metadata.filter) {
                embed.fields.push({ name: 'Filter Used', value: metadata.filter, inline: true });
            }
            // Add more metadata fields as needed
        }
        discordData.embeds.push(embed);

        // Prepare file data
        if (type === 'photo') {
            // Assuming data is a base64 string for photos
            if (typeof data === 'string' && data.startsWith('data:image/')) {
                const base64Data = data.split(',')[1];
                const buffer = Buffer.from(base64Data, 'base64');
                discordData.files.push({ name: 'photo.png', data: buffer });
            } else {
                console.error('[Webhook] Invalid photo data format. Expected base64 string.');
                throw new Error('Invalid photo data format');
            }
        } else if (type === 'video') {
            // Assuming data is a Blob or Buffer for videos
            // Note: Need to handle potential Blob conversion if coming directly from client
            let buffer: Buffer;
            if (Buffer.isBuffer(data)) {
                buffer = data;
            } else if (data instanceof Blob) {
                // If it's a Blob, we need to convert it to a Buffer
                // This requires reading the Blob's ArrayBuffer
                // This part might need adjustment depending on how Blob is passed
                console.warn('[Webhook] Received Blob data, attempting conversion to Buffer. This might be inefficient.');
                const arrayBuffer = await (data as any).arrayBuffer(); // Assuming Blob has arrayBuffer method
                buffer = Buffer.from(arrayBuffer);
            } else if (typeof data === 'object' && data.type === 'Buffer' && Array.isArray(data.data)) {
                 // Handle case where Blob might be serialized as a plain object
                 console.log('[Webhook] Received serialized Buffer object, converting back to Buffer.');
                 buffer = Buffer.from(data.data);
            } else {
                console.error('[Webhook] Invalid video data format. Expected Buffer or Blob.', typeof data, data);
                throw new Error('Invalid video data format');
            }
            discordData.files.push({ name: 'video.webm', data: buffer });
        }

        // Send using the provider
        return provider.send(discordData);
    }
}

// Create and export the webhook service instance
export const webhookService = new WebhookService();
