import { create } from 'zustand';

type SelectionMode = 'single' | 'multiple';
type SelectionPurpose = 'newMessage' | 'addToGroup' | 'share';

// Define a generic type for selected items
type SelectedItem = Record<string, unknown>;

interface SelectionConfig {
  // Selection behavior
  mode: SelectionMode;
  maxSelection?: number;
  initialSelected?: SelectedItem[];

  // Selection purpose
  purpose: SelectionPurpose;
  targetApp: string;

  // Navigation
  returnPath: string;

  // Callback
  callback: (selected: SelectedItem[]) => void;
}

interface SelectionState {
  active: boolean;
  mode: SelectionMode;
  purpose: SelectionPurpose | null;
  maxSelection?: number;
  returnPath: string;
  initialSelected?: SelectedItem[];
  callback?: (selected: SelectedItem[]) => void;

  startSelection: (config: SelectionConfig) => void;

  endSelection: (selected: SelectedItem[]) => void;
  cancelSelection: () => void;
}

export const useSelectionStore = create<SelectionState>((set, get) => ({
  active: false,
  mode: 'single',
  purpose: null,
  returnPath: '',
  initialSelected: [],

  startSelection: config => {
    console.log('[SelectionStore] Starting selection mode:', {
      mode: config.mode,
      purpose: config.purpose,
      returnPath: config.returnPath,
      maxSelection: config.maxSelection,
      initialSelectedCount: config.initialSelected?.length || 0
    });

    set({
      active: true,
      mode: config.mode,
      purpose: config.purpose,
      returnPath: config.returnPath,
      maxSelection: config.maxSelection,
      initialSelected: config.initialSelected,
      callback: config.callback
    });
  },

  endSelection: selected => {
    const { callback, returnPath } = get();

    console.log(
      '[SelectionStore] Ending selection with',
      selected?.length || 0,
      'items selected, returnPath:',
      returnPath
    );
    console.log('[SelectionStore] Selected items:', selected);

    if (!selected || !Array.isArray(selected)) {
      console.error('[SelectionStore] Invalid selected items:', selected);
      return;
    }

    if (callback) {
      console.log('[SelectionStore] Executing callback with', selected.length, 'items');
      console.log(
        '[SelectionStore] Callback function:',
        callback.toString().substring(0, 100) + '...'
      );

      try {
        // Make a copy of the selected items to avoid reference issues
        const selectedCopy = [...selected];
        callback(selectedCopy);
        console.log('[SelectionStore] Callback executed successfully');
      } catch (error) {
        console.error('[SelectionStore] Error executing callback:', error);
      }
    } else {
      console.log('[SelectionStore] No callback registered');
    }

    // Reset the selection store state
    set({
      active: false,
      purpose: null,
      callback: undefined
    });

    // Note: Navigation is now handled explicitly in the Photos component
    // using goBackWithResult from navigationStore
  },

  cancelSelection: () => {
    console.log('[SelectionStore] Cancelling selection');

    set({
      active: false,
      purpose: null,
      callback: undefined
    });

    // Note: Navigation is now handled explicitly in the Photos component
    // using goBackWithResult from navigationStore
  }
}));
