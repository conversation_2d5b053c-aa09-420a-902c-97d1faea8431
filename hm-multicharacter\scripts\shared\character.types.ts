/**
 * Character-related type definitions
 */

// Character data interface
export interface CharacterData {
    id: number;
    license: string; // Player identifier (license)
    stateid: string; // Unique citizen identifier
    first_name: string;
    last_name: string;
    birthdate: string;
    gender: 'male' | 'female' | 'other'; // Gender
    created_at: Date;
    updated_at: Date;

    // Stats
    level: number;
    cash: number;
    bank: number;
    job: string;
    jobGrade: number;

    // Appearance data
    appearance: CharacterAppearance;

    // Additional data
    metadata: Record<string, any>;
}

// Character appearance interface
export interface CharacterAppearance {
    model: string | number;
    components: ClothingComponent[];
    props: ClothingProp[];
    headBlend: HeadBlend;
    faceFeatures: FaceFeatures;
    headOverlays: HeadOverlays;
    hair: Hair;
    eyeColor: number;
    tattoos: Tatto<PERSON>[];
}

// Clothing component interface
export interface ClothingComponent {
    component_id: number;
    drawable: number;
    texture: number;
}

// Clothing prop interface
export interface ClothingProp {
    prop_id: number;
    drawable: number;
    texture: number;
}

// Head blend interface
export interface HeadBlend {
    shapeFirst: number;
    shapeSecond: number;
    shapeThird: number;
    skinFirst: number;
    skinSecond: number;
    skinThird: number;
    shapeMix: number;
    skinMix: number;
    thirdMix: number;
}

// Face features interface
export interface FaceFeatures {
    noseWidth: number;
    nosePeakHeight: number;
    nosePeakLength: number;
    noseBoneHeight: number;
    nosePeakLowering: number;
    noseBoneTwist: number;
    eyeBrowHeight: number;
    eyeBrowForward: number;
    cheekBoneHeight: number;
    cheekBoneWidth: number;
    cheeksWidth: number;
    eyesOpening: number;
    lipsThickness: number;
    jawBoneWidth: number;
    jawBoneBackLength: number;
    chinBoneLowering: number;
    chinBoneLength: number;
    chinBoneWidth: number;
    chinDimple: number;
    neckThickness: number;
}

// Head overlays interface
export interface HeadOverlays {
    blemishes: HeadOverlay;
    beard: HeadOverlay;
    eyebrows: HeadOverlay;
    ageing: HeadOverlay;
    makeUp: HeadOverlay;
    blush: HeadOverlay;
    complexion: HeadOverlay;
    sunDamage: HeadOverlay;
    lipstick: HeadOverlay;
    moleAndFreckles: HeadOverlay;
    chestHair: HeadOverlay;
    bodyBlemishes: HeadOverlay;
}

// Head overlay interface
export interface HeadOverlay {
    index: number;
    opacity: number;
    color: number;
    secondColor?: number;
}

// Hair interface
export interface Hair {
    style: number;
    color: number;
    highlight: number;
}

// Tattoo interface
export interface Tattoo {
    collection: string;
    overlay: string;
}

// Character creation data
export interface CharacterCreationData {
    id?: number; // Optional ID for existing characters
    stateid: string; // Unique state identifier
    license: string; // Player license identifier
    first_name: string;
    last_name: string;
    birthdate: Date;
    gender: 'male' | 'female' | 'other';
    state: 'alive' | 'dead' | 'unconscious';
    money: {
        //give defalt values for money
        cash: 500,
        bank: 5000,
        crypto: 0,
    };
    position: { x: number; y: number; z: number; heading?: number }; // Character position in the world
    appearance: CharacterAppearance;
    tattoos?: Tattoo[]; // Optional tattoos
    metadata?: Record<string, any>; // Additional metadata (e.g., custom data)
    created_at?: Date; // Character creation date
    updated_at?: Date; // Last update date
}

// Character selection data (simplified for UI)
export interface CharacterSelectionData {
    id: number;
    stateid: string;
    license: string;
    first_name: string;
    last_name: string;
    birthdate: Date;
    gender: 'male' | 'female' | 'other';
    state: 'alive' | 'dead' | 'unconscious';
    money: {
        cash: number;
        bank: number;
        crypto?: number;
    };
    position: { x: number; y: number; z: number; heading?: number }; // Character position in the world
    appearance?: CharacterAppearance; // Optional appearance data for UI display
    metadata?: Record<string, any>; // Additional metadata for UI display
    created_at?: Date; // Character creation date for UI display
    updated_at?: Date; // Last update date for UI display
}