import React, { useState } from 'react';
import StoryRing from './StoryRing';
import { getTimeAgo } from '../../../utils/timeUtils';
import { useLifeSnapStore } from '../stores/lifeSnapStore';

interface PostDetailProps {
  postId: number;
  onBack: () => void;
}

const PostDetail: React.FC<PostDetailProps> = ({ postId, onBack }) => {
  const [newComment, setNewComment] = useState('');
  const [showLikeAnimation, setShowLikeAnimation] = useState(false);
  const { posts, profiles } = useLifeSnapStore();

  const post = posts.find(p => p.id === postId);
  if (!post) return null;

  // Use embedded profile information if available, otherwise fall back to profiles array
  const hasEmbeddedProfile = post.username && post.profilePicture;
  const profileId = post.userId; // Use userId (identifier) directly
  const username = post.username;
  const fullName = post.fullName;
  const profilePicture = post.profilePicture;
  const isVerified = post.isVerified;

  // If we don't have embedded profile info, try to find it in the profiles array
  // Note: With the new structure, profiles should have identifier field matching post.userId
  const fallbackProfile = !hasEmbeddedProfile
    ? profiles.find(
        p =>
          (typeof p.id === 'string' && typeof post.userId === 'string' && p.id === post.userId) ||
          (typeof p.id === 'number' && typeof post.userId === 'number' && p.id === post.userId)
      )
    : null;

  // If we have neither embedded profile info nor a matching profile, we can't display the post
  if (!hasEmbeddedProfile && !fallbackProfile) return null;

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      // Call the NUI callback to add a comment
      fetch('https://hm-phone/lifeSnap:addComment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId: postId,
          text: newComment.trim()
        })
      });
      setNewComment('');
    }
  };

  const handleDoubleTap = () => {
    // Call the NUI callback to like the post
    fetch('https://hm-phone/lifeSnap:likePost', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        postId: post.id
      })
    });
    setShowLikeAnimation(true);
    setTimeout(() => setShowLikeAnimation(false), 1000);
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center gap-2 p-4 border-b border-white/10">
        <button onClick={onBack} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left"></i>
        </button>
        <StoryRing userId={profileId} size="sm" onClick={() => {}}>
          <img
            src={profilePicture || fallbackProfile?.profilePicture}
            alt={username || fallbackProfile?.username}
            className="w-8 h-8 rounded-full object-cover border-2 border-black"
          />
        </StoryRing>
        <div className="ml-3 flex-1">
          <div className="flex items-center gap-1">
            <span className="font-bold text-sm text-white">
              {fullName || fallbackProfile?.fullName}
            </span>
            {(isVerified || fallbackProfile?.isVerified) && (
              <i className="fas fa-check-circle text-blue-500 text-xs"></i>
            )}
          </div>
          <div className="text-xs text-white/60">@{username || fallbackProfile?.username}</div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Post Image */}
        <div className="aspect-square relative" onDoubleClick={handleDoubleTap}>
          <img src={post.imageUrl} alt="Post content" className="w-full h-full object-cover" />
          {showLikeAnimation && (
            <div className="absolute inset-0 flex items-center justify-center">
              <i className="fas fa-heart text-red-500 text-7xl animate-pulse"></i>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="p-3">
          <div className="flex items-center gap-4 mb-2">
            <div className="flex items-center gap-1">
              <button
                onClick={() => {
                  fetch('https://hm-phone/lifeSnap:likePost', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                      postId: post.id
                    })
                  });
                }}
                className={`text-lg ${post.likes.includes(1) ? 'text-red-500' : 'text-white'}`}
              >
                <i className={`${post.likes.includes(1) ? 'fas' : 'far'} fa-heart`}></i>
              </button>
              <span className="text-xs text-white/90">{post.likes.length}</span>
            </div>
            <div className="flex items-center gap-1">
              <button className="text-lg text-white">
                <i className="far fa-comment"></i>
              </button>
              <span className="text-xs text-white/90">{post.comments.length}</span>
            </div>
            {/* Bookmark button removed */}
          </div>

          {/* Likes */}
          <div className="text-sm font-semibold text-white mb-2">{post.likes.length} likes</div>

          {/* Caption */}
          <div className="mb-2 text-sm text-white/90">
            <span className="font-bold">{username || fallbackProfile?.username}</span>
            <span className="ml-2">{post.caption}</span>
          </div>

          {/* Location */}
          {post.location && (
            <div className="mb-2 text-xs text-white/70 flex items-center">
              <i className="fas fa-map-marker-alt mr-1"></i>
              <span>{post.location}</span>
            </div>
          )}

          {/* Timestamp */}
          <div className="mb-4 text-xs text-white/50">
            {getTimeAgo(
              post.timestamp instanceof Date
                ? post.timestamp.getTime()
                : typeof post.timestamp === 'number'
                ? post.timestamp
                : new Date(post.timestamp).getTime()
            )}
          </div>

          {/* Comment Input */}
          <div className="border border-white/10 rounded-lg mb-4 bg-[#1a1f2c]">
            <div className="flex items-center gap-2 p-2">
              <div className="w-7 h-7 rounded-full overflow-hidden">
                <img
                  src={profilePicture || fallbackProfile?.profilePicture}
                  alt={username || fallbackProfile?.username}
                  className="w-full h-full object-cover"
                />
              </div>
              <input
                type="text"
                value={newComment}
                onChange={e => setNewComment(e.target.value)}
                placeholder={`Add a comment as @${username || fallbackProfile?.username}...`}
                className="flex-1 bg-transparent text-white text-sm focus:outline-none"
                onKeyDown={e => e.key === 'Enter' && handleSubmitComment()}
              />
              <button
                onClick={handleSubmitComment}
                className={`text-sm font-semibold ${
                  newComment.trim() ? 'text-blue-500' : 'text-blue-500/50'
                }`}
                disabled={!newComment.trim()}
              >
                Post
              </button>
            </div>
          </div>

          {/* Comments */}
          <div className="space-y-4">
            {[...post.comments]
              .sort((a, b) => {
                const aTime =
                  a.timestamp instanceof Date
                    ? a.timestamp.getTime()
                    : typeof a.timestamp === 'number'
                    ? a.timestamp
                    : new Date(a.timestamp).getTime();
                const bTime =
                  b.timestamp instanceof Date
                    ? b.timestamp.getTime()
                    : typeof b.timestamp === 'number'
                    ? b.timestamp
                    : new Date(b.timestamp).getTime();
                return bTime - aTime; // Newest first
              })
              .map(comment => {
                const commentUser = profiles.find(p => p.id === comment.userId);
                const commentTime =
                  comment.timestamp instanceof Date
                    ? comment.timestamp.getTime()
                    : typeof comment.timestamp === 'number'
                    ? comment.timestamp
                    : new Date(comment.timestamp).getTime();
                return (
                  <div key={comment.id} className="flex gap-2">
                    <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0 mt-1">
                      <img
                        src={commentUser?.profilePicture}
                        alt={commentUser?.username}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-xs text-white">
                          {commentUser?.username}
                        </span>
                        <span className="text-[10px] text-white/40">{getTimeAgo(commentTime)}</span>
                      </div>
                      <span className="text-[11px] text-white/90 block mt-0.5">{comment.text}</span>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostDetail;
