{"root": true, "env": {"browser": true, "es2022": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react-hooks"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unsafe-function-type": "off", "no-case-declarations": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}