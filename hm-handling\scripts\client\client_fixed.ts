import { VehicleInfo, BaseHandlingData, HandlingProfile } from '../shared/types';

// Simple state management (instead of Zustand for now)
interface HandlingState {
  isUIOpen: boolean;
  vehicleInfo: VehicleInfo | null;
  originalHandling: Partial<BaseHandlingData>;
  currentHandling: Partial<BaseHandlingData>;
  profiles: HandlingProfile[];
  hasUnsavedChanges: boolean;
}

let handlingState: HandlingState = {
  isUIOpen: false,
  vehicleInfo: null,
  originalHandling: {},
  currentHandling: {},
  profiles: [],
  hasUnsavedChanges: false
};

// UI State - consolidated for better performance
let isUIOpen = false;
let currentVehicle: number | null = null;

// Performance optimization: Cache for vehicle handling data
const vehicleHandlingCache = new Map<number, {
  data: Partial<BaseHandlingData>;
  timestamp: number;
  vehicleInfo: VehicleInfo;
}>();

const CACHE_DURATION = 5000; // 5 seconds cache

// Performance optimization: Debouncing for real-time updates
let debounceTimers = new Map<string, NodeJS.Timeout>();
const DEBOUNCE_DELAY = 150; // milliseconds

// Debounced field update function
function debouncedFieldUpdate(field: string, value: any, callback: () => void): void {
  // Clear existing timer for this field
  const existingTimer = debounceTimers.get(field);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }
  
  // Set new timer
  const timer = setTimeout(() => {
    callback();
    debounceTimers.delete(field);
  }, DEBOUNCE_DELAY);
  
  debounceTimers.set(field, timer);
}

// Performance optimization: Batch handling field updates
interface PendingUpdate {
  field: string;
  value: any;
  timestamp: number;
}

let pendingUpdates = new Map<string, PendingUpdate>();
let batchUpdateTimer: NodeJS.Timeout | null = null;
const BATCH_UPDATE_DELAY = 200; // milliseconds

function batchFieldUpdate(field: string, value: any): void {
  // Add to pending updates
  pendingUpdates.set(field, {
    field,
    value,
    timestamp: GetGameTimer()
  });
  
  // Schedule batch update if not already scheduled
  if (!batchUpdateTimer) {
    batchUpdateTimer = setTimeout(() => {
      processBatchUpdates();
    }, BATCH_UPDATE_DELAY);
  }
}

function processBatchUpdates(): void {
  if (!currentVehicle || pendingUpdates.size === 0) {
    clearBatchUpdate();
    return;
  }
  
  const updates = Array.from(pendingUpdates.values());
  pendingUpdates.clear();
    // Apply all updates at once
  for (const update of updates) {
    // TODO: Implement field-specific update logic
    console.log(`Applying batched update: ${update.field} = ${update.value}`);
  }
  
  clearBatchUpdate();
}

function clearBatchUpdate(): void {
  if (batchUpdateTimer) {
    clearTimeout(batchUpdateTimer);
    batchUpdateTimer = null;
  }
}

// Efficient change detection without JSON.stringify
function hasHandlingChanges(current: Partial<BaseHandlingData>, original: Partial<BaseHandlingData>): boolean {
  const currentKeys = Object.keys(current);
  const originalKeys = Object.keys(original);
  
  if (currentKeys.length !== originalKeys.length) return true;
  
  for (const key of currentKeys) {
    const currentValue = current[key as keyof BaseHandlingData];
    const originalValue = original[key as keyof BaseHandlingData];
    
    // Handle Vector3 objects
    if (typeof currentValue === 'object' && currentValue !== null && 'x' in currentValue) {
      const currentVec = currentValue as { x: number; y: number; z: number };
      const originalVec = originalValue as { x: number; y: number; z: number };
      if (currentVec.x !== originalVec.x || currentVec.y !== originalVec.y || currentVec.z !== originalVec.z) {
        return true;
      }
    } else if (currentValue !== originalValue) {
      return true;
    }
  }
  
  return false;
}

// State management functions
function updateState(updates: Partial<HandlingState>): void {
  handlingState = { ...handlingState, ...updates };
  
  // Efficient change detection
  if (updates.currentHandling || updates.originalHandling) {
    handlingState.hasUnsavedChanges = hasHandlingChanges(
      handlingState.currentHandling, 
      handlingState.originalHandling
    );
  }
}

// Initialize when client starts
setImmediate(() => {
  console.log('Handling editor client initialized');
});

// F7 Keybind to toggle UI
RegisterCommand('toggle_handling', () => {
  toggleHandlingUI();
}, false);

RegisterKeyMapping('toggle_handling', 'Toggle Handling Editor', 'keyboard', 'F7');

// Main UI toggle function
function toggleHandlingUI(): void {
  if (isUIOpen) {
    closeHandlingUI();
  } else {
    openHandlingUI();
  }
}

// Unit conversion functions
function toUIValue(field: string, gameValue: number): number {
  switch(field) {
    case 'fSteeringLock':
      return gameValue * (180 / Math.PI); // radians → degrees
    case 'fInitialDriveMaxFlatVel':
      return gameValue * 3.6; // m/s → km/h
    default:
      return gameValue;
  }
}

function toGameValue(field: string, uiValue: number): number {
  switch(field) {
    case 'fSteeringLock':
      return uiValue * (Math.PI / 180); // degrees → radians
    case 'fInitialDriveMaxFlatVel':
      return uiValue / 3.6; // km/h → m/s
    default:
      return uiValue;
  }
}

// Open handling UI
async function openHandlingUI(): Promise<void> {
  const playerPed = PlayerPedId();
  
  // Debug logging
  console.log('[hm-handling] Opening handling UI...');
  console.log('[hm-handling] Player ped:', playerPed);
  
  // First check if player is in any vehicle at all
  if (!IsPedInAnyVehicle(playerPed, false)) {
    console.log('[hm-handling] Player is not in any vehicle');
    
    // Clear cache and reset state when no vehicle
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no vehicle message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat of a vehicle to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  
  const vehicle = GetVehiclePedIsIn(playerPed, false);
  console.log('[hm-handling] Vehicle handle:', vehicle);
  
  // Double-check vehicle exists
  if (vehicle === 0 || !DoesEntityExist(vehicle)) {
    console.log('[hm-handling] Vehicle does not exist or invalid handle');
    
    // Clear cache and reset state when no valid vehicle
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no vehicle message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat of a vehicle to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  // Verify player is in driver seat (seat -1)
  // Check if the player ped is the driver by getting who's in the driver seat
  const driverPed = GetPedInVehicleSeat(vehicle, -1);
  console.log('[hm-handling] Driver ped:', driverPed, 'Player ped:', playerPed);
  
  if (driverPed !== playerPed) {
    console.log('[hm-handling] Player is not in driver seat');
    
    // Clear cache and reset state when not in driver seat
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no driver seat message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  // Vehicle changed - invalidate old cache and update reference
  if (currentVehicle && currentVehicle !== vehicle) {
    console.log('[hm-handling] Vehicle changed from', currentVehicle, 'to', vehicle);
    vehicleHandlingCache.delete(currentVehicle);
  }
  currentVehicle = vehicle;
  console.log('[hm-handling] Current vehicle set to:', currentVehicle);

  // Get vehicle info and handling data
  console.log('[hm-handling] Getting vehicle info and handling data...');
  const vehicleInfo = await getVehicleInfo(currentVehicle);
  const handlingData = await getVehicleHandling(currentVehicle);
  
  console.log('[hm-handling] Vehicle info:', vehicleInfo);
  console.log('[hm-handling] Handling data available:', !!handlingData);
  
  if (!vehicleInfo || !handlingData) {
    console.log('[hm-handling] Failed to get vehicle info or handling data');
    
    // Send UI with error message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'Failed to get vehicle information. Please try again.',
        profiles: handlingState.profiles
      }
    });
      SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  
  console.log('[hm-handling] Successfully retrieved vehicle data, opening UI');

  // Update state
  updateState({
    isUIOpen: true,
    vehicleInfo,
    originalHandling: { ...handlingData },
    currentHandling: { ...handlingData }
  });

  // Send data to UI
  SendNUIMessage({
    type: 'setVisible',
    visible: true,
    data: {
      vehicle: vehicleInfo,
      handling: handlingData,
      profiles: handlingState.profiles
    }
  });

  SetNuiFocus(true, true);
  isUIOpen = true;
  console.log('[hm-handling] UI opened successfully');
}

// Close handling UI
function closeHandlingUI(): void {
  console.log('[hm-handling] Closing handling UI...');
  
  SendNUIMessage({
    type: 'setVisible',
    visible: false
  });

  SetNuiFocus(false, false);
  updateState({ isUIOpen: false });
  isUIOpen = false;
  
  console.log('[hm-handling] UI closed successfully');
}

// Get vehicle information
async function getVehicleInfo(vehicle: number): Promise<VehicleInfo | null> {
  if (!DoesEntityExist(vehicle)) return null;

  const model = GetEntityModel(vehicle);
  const modelName = GetDisplayNameFromVehicleModel(model);
  const displayName = GetLabelText(modelName);
  const vehicleClass = GetVehicleClass(vehicle);
  const className = GetLabelText(`VEH_CLASS_${vehicleClass}`);  const playerPed = PlayerPedId();
  const isInVehicle = GetVehiclePedIsIn(playerPed, false) === vehicle;
  const driverPed = GetPedInVehicleSeat(vehicle, -1);
  const isDriverSeat = isInVehicle && driverPed === playerPed;

  return {
    model: modelName.toLowerCase(),
    displayName: displayName !== 'NULL' ? displayName : modelName,
    hash: model,
    vehicleClass,
    className: className !== 'NULL' ? className : `Class ${vehicleClass}`,
    isInVehicle,
    isDriverSeat,
    vehicleHandle: vehicle
  };
}

// Get vehicle handling data
// Performance-optimized vehicle handling retrieval with caching
async function getVehicleHandling(vehicle: number): Promise<Partial<BaseHandlingData> | null> {
  if (!DoesEntityExist(vehicle)) return null;

  const now = GetGameTimer();
  const cached = vehicleHandlingCache.get(vehicle);
  
  // Return cached data if still valid
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  // Batch retrieve handling values from the vehicle for better performance
  const handlingData: Partial<BaseHandlingData> = {
    fMass: toUIValue('fMass', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fMass')),
    fInitialDragCoeff: toUIValue('fInitialDragCoeff', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDragCoeff')),
    fPercentSubmerged: toUIValue('fPercentSubmerged', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fPercentSubmerged')),
    
    // Centre of mass offset
    vecCentreOfMassOffset: {
      x: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_x'),
      y: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_y'),
      z: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_z')
    },

    // Inertia multiplier
    vecInertiaMultiplier: {
      x: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_x'),
      y: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_y'),
      z: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_z')
    },

    // Drive properties
    fDriveBiasFront: toUIValue('fDriveBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDriveBiasFront')),
    nInitialDriveGears: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'nInitialDriveGears'),
    fInitialDriveForce: toUIValue('fInitialDriveForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDriveForce')),
    fDriveInertia: toUIValue('fDriveInertia', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDriveInertia')),
    fClutchChangeRateScaleUpShift: toUIValue('fClutchChangeRateScaleUpShift', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fClutchChangeRateScaleUpShift')),
    fClutchChangeRateScaleDownShift: toUIValue('fClutchChangeRateScaleDownShift', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fClutchChangeRateScaleDownShift')),
    fInitialDriveMaxFlatVel: toUIValue('fInitialDriveMaxFlatVel', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDriveMaxFlatVel')),

    // Brake properties
    fBrakeForce: toUIValue('fBrakeForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce')),
    fBrakeBiasFront: toUIValue('fBrakeBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeBiasFront')),
    fHandBrakeForce: toUIValue('fHandBrakeForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fHandBrakeForce')),

    // Steering
    fSteeringLock: toUIValue('fSteeringLock', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSteeringLock')),

    // Traction
    fTractionCurveMax: toUIValue('fTractionCurveMax', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveMax')),
    fTractionCurveMin: toUIValue('fTractionCurveMin', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveMin')),
    fTractionCurveLateral: toUIValue('fTractionCurveLateral', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveLateral')),
    fTractionSpringDeltaMax: toUIValue('fTractionSpringDeltaMax', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionSpringDeltaMax')),
    fLowSpeedTractionLossMult: toUIValue('fLowSpeedTractionLossMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fLowSpeedTractionLossMult')),
    fCamberStiffnesss: toUIValue('fCamberStiffnesss', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCamberStiffnesss')),
    fTractionBiasFront: toUIValue('fTractionBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionBiasFront')),
    fTractionLossMult: toUIValue('fTractionLossMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionLossMult')),

    // Suspension
    fSuspensionForce: toUIValue('fSuspensionForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionForce')),
    fSuspensionCompDamp: toUIValue('fSuspensionCompDamp', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionCompDamp')),
    fSuspensionReboundDamp: toUIValue('fSuspensionReboundDamp', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionReboundDamp')),
    fSuspensionUpperLimit: toUIValue('fSuspensionUpperLimit', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionUpperLimit')),
    fSuspensionLowerLimit: toUIValue('fSuspensionLowerLimit', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionLowerLimit')),
    fSuspensionRaise: toUIValue('fSuspensionRaise', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionRaise')),
    fSuspensionBiasFront: toUIValue('fSuspensionBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionBiasFront')),
    fAntiRollBarForce: toUIValue('fAntiRollBarForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fAntiRollBarForce')),
    fAntiRollBarBiasFront: toUIValue('fAntiRollBarBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fAntiRollBarBiasFront')),
    fRollCentreHeightFront: toUIValue('fRollCentreHeightFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fRollCentreHeightFront')),
    fRollCentreHeightRear: toUIValue('fRollCentreHeightRear', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fRollCentreHeightRear')),

    // Damage
    fCollisionDamageMult: toUIValue('fCollisionDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult')),
    fWeaponDamageMult: toUIValue('fWeaponDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fWeaponDamageMult')),
    fDeformationDamageMult: toUIValue('fDeformationDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult')),
    fEngineDamageMult: toUIValue('fEngineDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult')),

    // Fuel and oil
    fPetrolTankVolume: toUIValue('fPetrolTankVolume', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fPetrolTankVolume')),
    fOilVolume: toUIValue('fOilVolume', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fOilVolume')),

    // Misc
    fSeatOffsetDistX: toUIValue('fSeatOffsetDistX', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistX')),
    fSeatOffsetDistY: toUIValue('fSeatOffsetDistY', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistY')),
    fSeatOffsetDistZ: toUIValue('fSeatOffsetDistZ', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistZ')),
    nMonetaryValue: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'nMonetaryValue')
  };

  // Cache the result with vehicle info
  const vehicleInfo = await getVehicleInfo(vehicle);
  if (vehicleInfo) {
    vehicleHandlingCache.set(vehicle, {
      data: handlingData,
      timestamp: now,
      vehicleInfo
    });
  }

  return handlingData;
}

// Apply handling changes to vehicle
function applyHandlingToVehicle(vehicle: number, handlingData: Partial<BaseHandlingData>): boolean {
  if (!DoesEntityExist(vehicle)) return false;

  try {
    // Apply each handling value
    Object.entries(handlingData).forEach(([field, value]) => {
      if (value === undefined || value === null) return;

      if (field === 'vecCentreOfMassOffset' && typeof value === 'object' && 'x' in value) {
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_x', value.x);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_y', value.y);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_z', value.z);
      } else if (field === 'vecInertiaMultiplier' && typeof value === 'object' && 'x' in value) {
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_x', value.x);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_y', value.y);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_z', value.z);
      } else if (field === 'nInitialDriveGears' || field === 'nMonetaryValue') {
        SetVehicleHandlingInt(vehicle, 'CHandlingData', field, value as number);
      } else if (typeof value === 'number') {
        // Convert UI value back to game units before setting
        const gameValue = toGameValue(field, value);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', field, gameValue);
      }
    });

    return true;
  } catch (error) {
    console.error('Error applying handling to vehicle:', error);
    return false;
  }
}

// Apply single field to vehicle
function applySingleField(field: string, value: number | string | object): void {
  if (!currentVehicle) return;

  const handlingData: Partial<BaseHandlingData> = {
    [field]: value
  };

  const success = applyHandlingToVehicle(currentVehicle, handlingData);
  
  if (success) {
    // Update current state with the new value
    updateState({
      currentHandling: {
        ...handlingState.currentHandling,
        [field]: value
      }
    });

    // Send success feedback to UI
    SendNUIMessage({
      type: 'fieldApplied',
      field,
      success: true
    });
  } else {
    // Send error feedback to UI
    SendNUIMessage({
      type: 'fieldApplied',
      field,
      success: false
    });
  }
}

// NUI Callbacks
RegisterNuiCallback('closeUI', (data: unknown, cb: (response: unknown) => void) => {
  closeHandlingUI();
  cb({ ok: true });
});

RegisterNuiCallback('applyHandling', (data: { handlingData: Partial<BaseHandlingData> }, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  const success = applyHandlingToVehicle(currentVehicle, data.handlingData);
  
  if (success) {
    updateState({ currentHandling: data.handlingData });
    cb({ success: true });
  } else {
    cb({ success: false, error: 'Failed to apply handling changes' });
  }
});

RegisterNuiCallback('refreshVehicle', async (data: unknown, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  // Clear cache to force refresh
  vehicleHandlingCache.delete(currentVehicle);

  const vehicleInfo = await getVehicleInfo(currentVehicle);
  const handlingData = await getVehicleHandling(currentVehicle);

  if (vehicleInfo && handlingData) {
    updateState({
      vehicleInfo,
      originalHandling: { ...handlingData },
      currentHandling: { ...handlingData }
    });

    cb({ success: true, data: { vehicle: vehicleInfo, handling: handlingData } });
  } else {
    cb({ success: false, error: 'Failed to refresh vehicle data' });
  }
});

RegisterNuiCallback('applyField', (data: { field: string; value: number | string | object }, cb: (response: unknown) => void) => {
  applySingleField(data.field, data.value);
  cb({ ok: true });
});

RegisterNuiCallback('applyAllChanges', (data: { handlingData: Partial<BaseHandlingData> }, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  const success = applyHandlingToVehicle(currentVehicle, data.handlingData);
  
  if (success) {
    updateState({ currentHandling: data.handlingData });
    cb({ success: true });
  } else {
    cb({ success: false, error: 'Failed to apply handling changes' });
  }
});

RegisterNuiCallback('saveProfile', (data: { profile: Omit<HandlingProfile, 'id' | 'createdAt'> }, cb: (response: unknown) => void) => {
  // TODO: Implement profile saving logic
  cb({ success: true });
});

RegisterNuiCallback('loadProfile', (data: { profileId: string }, cb: (response: unknown) => void) => {
  // TODO: Implement profile loading logic
  cb({ success: true });
});

// Reset vehicle handling to default (if needed)
function resetVehicleHandling(vehicle: number): boolean {
  if (!DoesEntityExist(vehicle)) return false;

  try {
    // This would require storing original values or reloading from handling.meta
    // For now, we'll just return success
    return true;
  } catch (error) {
    console.error('Error resetting vehicle handling:', error);
    return false;
  }
}

RegisterNuiCallback('deleteProfile', (data: { profileId: string }, cb: (response: unknown) => void) => {
  // TODO: Implement profile deletion logic
  cb({ success: true });
});

// Add debug logging for development
if (GetConvarInt('hm_handling_debug', 0) === 1) {
  console.log('[hm-handling] Debug mode enabled');
  
  // Add debug command to print current vehicle handling
  RegisterCommand('hm_debug_handling', async () => {
    if (currentVehicle) {
      const handling = await getVehicleHandling(currentVehicle);
      console.log('Current vehicle handling:', JSON.stringify(handling, null, 2));
    } else {
      console.log('No current vehicle');
    }
  }, false);
}
