import React from 'react';
import { useNavigation } from '../../../navigation/hooks';
import SongCard from './SongCard';
import { useMusicStore } from '../stores/musicStore';

const Profile: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const { songs, getUserProfileArtist, getUserProfileAlbums, getUserProfileSongs } =
    useMusicStore();

  // No need to load data here as it's already loaded in PhoneProvider

  // Get the user profile artist and related data
  const userProfile = getUserProfileArtist();
  const userAlbums = getUserProfileAlbums();
  const userSongs = getUserProfileSongs();

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white pt-[32px]">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-2">
        <button onClick={goBack} className="text-white cursor-pointer">
          <i className="fas fa-arrow-left text-xl"></i>
        </button>
        <div className="text-center">
          <h1 className="text-lg font-medium">Profile</h1>
        </div>
        <button className="text-white cursor-pointer">
          <i className="fas fa-cog"></i>
        </button>
      </div>

      {/* Profile Info - Compact Version */}
      <div className="px-4 py-3 flex items-center border-b border-gray-800">
        <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-pink-500 mr-4">
          <img
            src={userProfile?.imageUrl || 'https://picsum.photos/200/200?random=50'}
            alt={userProfile?.name || 'User'}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1">
          <h2 className="text-lg font-bold">{userProfile?.name || 'User'}</h2>
          <p className="text-sm text-gray-400 line-clamp-1">{userProfile?.bio || 'Music lover'}</p>
          <div className="flex mt-1">
            <div className="flex items-center mr-4">
              <span className="text-sm font-bold mr-1">{userAlbums.length}</span>
              <span className="text-xs text-gray-400">Albums</span>
            </div>
            <div className="flex items-center mr-4">
              <span className="text-sm font-bold mr-1">{userSongs.length}</span>
              <span className="text-xs text-gray-400">Songs</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-bold mr-1">{userProfile?.followers || 0}</span>
              <span className="text-xs text-gray-400">Followers</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto px-4 pt-4 pb-4">
        {userProfile?.isActive ? (
          <>
            {/* Albums */}
            <div className="mb-4">
              <h2 className="text-base font-semibold mb-2">Albums</h2>

              <div className="flex overflow-x-auto space-x-4 pb-2">
                {userAlbums.length > 0 ? (
                  userAlbums.map(album => (
                    <div
                      key={album.id}
                      className="flex-shrink-0 w-28 cursor-pointer"
                      onClick={() => openView('album', { albumId: album.id })}
                    >
                      <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
                        <img
                          src={album.imageUrl}
                          alt={album.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <h3 className="text-sm font-medium text-white truncate">{album.title}</h3>
                      <p className="text-xs text-gray-400 truncate">
                        {new Date().getFullYear()} •{' '}
                        {songs.filter(s => s.albumId === album.id).length} songs
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="w-full text-center text-gray-400 py-4">
                    <p>No albums yet</p>
                  </div>
                )}
              </div>
            </div>

            {/* Songs */}
            <div className="mb-4">
              <h2 className="text-base font-semibold mb-2">Songs</h2>

              <div className="space-y-2">
                {userSongs.length > 0 ? (
                  userSongs.map((song, index) => (
                    <SongCard
                      key={song.id}
                      song={{
                        id: song.id,
                        title: song.title,
                        artist: song.artist,
                        imageUrl: song.imageUrl,
                        duration: `${Math.floor(song.duration / 60)}:${(song.duration % 60)
                          .toString()
                          .padStart(2, '0')}`,
                        plays: `${Math.floor(Math.random() * 900) + 100}K`
                      }}
                      index={index}
                      showIndex={true}
                      showDuration={true}
                      showPlays={true}
                      compact={true}
                    />
                  ))
                ) : (
                  <div className="w-full text-center text-gray-400 py-4">
                    <p>No songs yet</p>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-gray-400">
            <p>This user hasn't published any music yet.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
