/**
 * Utility functions for the Garage app
 */

/**
 * Get the appropriate icon for a vehicle category
 */
export const getCategoryIcon = (category: string): string => {
  switch (category?.toLowerCase()) {
    case 'super':
      return 'fa-rocket';
    case 'sports':
      return 'fa-car-side';
    case 'motorcycles':
      return 'fa-motorcycle';
    case 'suvs':
      return 'fa-truck';
    case 'muscle':
      return 'fa-bolt';
    case 'sedans':
      return 'fa-car';
    case 'compacts':
      return 'fa-car-side';
    case 'coupes':
      return 'fa-car-side';
    case 'vans':
      return 'fa-shuttle-van';
    case 'offroad':
      return 'fa-truck-monster';
    case 'emergency':
      return 'fa-ambulance';
    case 'service':
      return 'fa-truck-pickup';
    case 'industrial':
      return 'fa-industry';
    case 'utility':
      return 'fa-toolbox';
    case 'commercial':
      return 'fa-truck-moving';
    case 'boats':
      return 'fa-ship';
    case 'helicopters':
      return 'fa-helicopter';
    case 'planes':
      return 'fa-plane';
    case 'military':
      return 'fa-fighter-jet';
    case 'trains':
      return 'fa-train';
    default:
      return 'fa-car';
  }
};

/**
 * Get the appropriate icon for a vehicle type
 */
export const getVehicleTypeIcon = (type: string): string => {
  switch (type?.toLowerCase()) {
    case 'automobile':
      return 'fa-car';
    case 'motorcycle':
      return 'fa-motorcycle';
    case 'boat':
      return 'fa-ship';
    case 'aircraft':
      return 'fa-plane';
    case 'helicopter':
      return 'fa-helicopter';
    case 'bicycle':
      return 'fa-bicycle';
    case 'train':
      return 'fa-train';
    case 'submarine':
      return 'fa-water';
    case 'amphibious':
      return 'fa-water';
    default:
      return 'fa-car';
  }
};

/**
 * Get the appropriate color class for a vehicle status
 */
export const getStatusColorClass = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'charging':
      return 'bg-blue-500/20 text-blue-400';
    case 'ready':
      return 'bg-green-500/20 text-green-400';
    case 'impounded':
      return 'bg-red-500/20 text-red-400';
    case 'out':
      return 'bg-yellow-500/20 text-yellow-400';
    case 'in':
      return 'bg-green-500/20 text-green-400';
    case 'parked':
      return 'bg-green-500/20 text-green-400';
    default:
      return 'bg-white/10 text-white/60';
  }
};

/**
 * Get a human-readable color name from a color ID
 */
export const getColorName = (colorId: string | number): string => {
  const colorMap: Record<string | number, string> = {
    0: 'Black',
    1: 'Graphite',
    2: 'Black Steel',
    3: 'Dark Silver',
    4: 'Silver',
    5: 'Blue Silver',
    6: 'Steel Gray',
    7: 'Shadow Silver',
    8: 'Stone Silver',
    9: 'Midnight Silver',
    10: 'Gun Metal',
    11: 'Anthracite',
    12: 'Matte Black',
    13: 'Matte Gray',
    14: 'Light Gray',
    15: 'Chrome',
    16: 'Pure White',
    17: 'Frost White',
    18: 'Cream',
    19: 'Ice White',
    20: 'Worn White',
    21: 'Worn Black',
    22: 'Worn Graphite',
    23: 'Worn Silver',
    24: 'Worn Blue Silver',
    25: 'Worn Shadow Silver',
    26: 'Red',
    27: 'Torino Red',
    28: 'Formula Red',
    29: 'Blaze Red',
    30: 'Grace Red',
    31: 'Garnet Red',
    32: 'Sunset Red',
    33: 'Cabernet Red',
    34: 'Candy Red',
    35: 'Sunrise Orange',
    36: 'Gold',
    37: 'Orange',
    38: 'Red Orange',
    39: 'Light Orange',
    40: 'Yellow',
    41: 'Race Yellow',
    42: 'Dew Yellow',
    43: 'Dark Green',
    44: 'Racing Green',
    45: 'Sea Green',
    46: 'Olive Green',
    47: 'Bright Green',
    48: 'Gasoline Green',
    49: 'Lime Green',
    50: 'Dark Blue',
    51: 'Galaxy Blue',
    52: 'Dark Blue',
    53: 'Saxon Blue',
    54: 'Blue',
    55: 'Mariner Blue',
    56: 'Harbor Blue',
    57: 'Diamond Blue',
    58: 'Surf Blue',
    59: 'Nautical Blue',
    60: 'Ultra Blue',
    61: 'Schafter Purple',
    62: 'Spinnaker Purple',
    63: 'Midnight Purple',
    64: 'Bright Purple',
    65: 'Cream',
    66: 'Frost White',
    67: 'Chocolate Brown',
    68: 'Bison Brown',
    69: 'Creek Brown',
    70: 'Feltzer Brown',
    71: 'Maple Brown',
    72: 'Beechwood Brown',
    73: 'Sienna Brown',
    74: 'Saddle Brown',
    75: 'Moss Brown',
    76: 'Woodbeech Brown',
    77: 'Straw Brown',
    78: 'Sandy Brown',
    79: 'Bleached Brown',
    80: 'Schafter Purple',
    81: 'Spinnaker Purple',
    82: 'Midnight Purple',
    83: 'Bright Purple',
    84: 'Cream',
    85: 'Frost White',
    86: 'Hot Pink',
    87: 'Salmon Pink',
    88: 'Pfister Pink',
    89: 'Bright Orange',
    90: 'Green',
    91: 'Fluorescent Blue',
    92: 'Midnight Blue',
    93: 'Midnight Purple',
    94: 'Wine Red',
    95: 'Hunter Green',
    96: 'Bright Purple',
    97: 'Midnight Purple',
    98: 'Carbon Black',
    99: 'Matte Purple',
    100: 'Matte Dark Purple',
    101: 'Metallic Lava Red',
    102: 'Olive Green',
    103: 'Matte Olive Drab',
    104: 'Dark Earth',
    105: 'Desert Tan',
    106: 'Matte Foliage Green',
    107: 'DEFAULT ALLOY',
    108: 'Epsilon Blue',
    109: 'Pure Gold',
    110: 'Brushed Gold',
    111: 'Police Blue',
    112: 'Matte White',
    113: 'Matte Red',
    114: 'Matte Dark Red',
    115: 'Matte Orange',
    116: 'Matte Yellow',
    117: 'Matte Lime Green',
    118: 'Matte Green',
    119: 'Matte Forest Green',
    120: 'Matte Foliage Green',
    121: 'Matte Olive Drab',
    122: 'Matte Dark Earth',
    123: 'Matte Desert Tan',
    124: 'Matte Desert Tan',
    125: 'Matte Foliage Green',
    126: 'DEFAULT ALLOY COLOR',
    127: 'Epsilon Blue',
    128: 'Pure Gold',
    129: 'Brushed Gold',
    130: 'Chrome',
    131: 'Brushed Steel',
    132: 'Brushed Black Steel',
    133: 'Brushed Aluminum',
    134: 'Chrome',
    135: 'Lime Green',
    136: 'Champagne',
    137: 'Feltzer Brown',
    138: 'Dark Brown',
    139: 'Brushed Bronze',
    140: 'Metallic Black',
    141: 'Metallic Graphite Black',
    142: 'Metallic Black Steel',
    143: 'Metallic Dark Silver',
    144: 'Metallic Silver',
    145: 'Metallic Blue Silver',
    146: 'Metallic Steel Gray',
    147: 'Metallic Shadow Silver',
    148: 'Metallic Stone Silver',
    149: 'Metallic Midnight Silver',
    150: 'Metallic Gun Metal',
    151: 'Metallic Anthracite Gray',
    152: 'Matte Black',
    153: 'Matte Gray',
    154: 'Matte Light Gray',
    155: 'Util Black',
    156: 'Util Black Poly',
    157: 'Util Dark Silver',
    158: 'Util Silver',
    159: 'Util Gun Metal',
    160: 'Util Shadow Silver',
    161: 'Worn Black',
    162: 'Worn Graphite',
    163: 'Worn Silver Gray',
    164: 'Worn Silver',
    165: 'Worn Blue Silver',
    166: 'Worn Shadow Silver',
    167: 'Metallic Red',
    168: 'Metallic Torino Red',
    169: 'Metallic Formula Red',
    170: 'Metallic Blaze Red',
    171: 'Metallic Graceful Red',
    172: 'Metallic Garnet Red',
    173: 'Metallic Desert Red',
    174: 'Metallic Cabernet Red',
    175: 'Metallic Candy Red',
    176: 'Metallic Sunrise Orange',
    177: 'Metallic Classic Gold',
    178: 'Metallic Orange',
    179: 'Matte Red',
    180: 'Matte Dark Red',
    181: 'Matte Orange',
    182: 'Matte Yellow',
    183: 'Util Red',
    184: 'Util Bright Red',
    185: 'Util Garnet Red',
    186: 'Worn Red',
    187: 'Worn Golden Red',
    188: 'Worn Dark Red',
    189: 'Metallic Dark Green',
    190: 'Metallic Racing Green',
    191: 'Metallic Sea Green',
    192: 'Metallic Olive Green',
    193: 'Metallic Green',
    194: 'Metallic Gasoline Blue Green',
    195: 'Matte Lime Green',
    196: 'Util Dark Green',
    197: 'Util Green',
    198: 'Worn Dark Green',
    199: 'Worn Green',
    200: 'Worn Sea Wash',
    201: 'Metallic Midnight Blue',
    202: 'Metallic Dark Blue',
    203: 'Metallic Saxony Blue',
    204: 'Metallic Blue',
    205: 'Metallic Mariner Blue',
    206: 'Metallic Harbor Blue',
    207: 'Metallic Diamond Blue',
    208: 'Metallic Surf Blue',
    209: 'Metallic Nautical Blue',
    210: 'Metallic Bright Blue',
    211: 'Metallic Purple Blue',
    212: 'Metallic Spinnaker Blue',
    213: 'Metallic Ultra Blue',
    214: 'Metallic Bright Blue',
    215: 'Util Dark Blue',
    216: 'Util Midnight Blue',
    217: 'Util Blue',
    218: 'Util Sea Foam Blue',
    219: 'Util Lightning Blue',
    220: 'Util Maui Blue Poly',
    221: 'Util Bright Blue',
    222: 'Matte Dark Blue',
    223: 'Matte Blue',
    224: 'Matte Midnight Blue',
    225: 'Worn Dark Blue',
    226: 'Worn Blue',
    227: 'Worn Light Blue',
    228: 'Metallic Taxi Yellow',
    229: 'Metallic Race Yellow',
    230: 'Metallic Bronze',
    231: 'Metallic Yellow Bird',
    232: 'Metallic Lime',
    233: 'Metallic Champagne',
    234: 'Metallic Pueblo Beige',
    235: 'Metallic Dark Ivory',
    236: 'Metallic Choco Brown',
    237: 'Metallic Golden Brown',
    238: 'Metallic Light Brown',
    239: 'Metallic Straw Beige',
    240: 'Metallic Moss Brown',
    241: 'Metallic Biston Brown',
    242: 'Metallic Beechwood',
    243: 'Metallic Dark Beechwood',
    244: 'Metallic Choco Orange',
    245: 'Metallic Beach Sand',
    246: 'Metallic Sun Bleeched Sand',
    247: 'Metallic Cream',
    248: 'Util Brown',
    249: 'Util Medium Brown',
    250: 'Util Light Brown',
    251: 'Metallic White',
    252: 'Metallic Frost White',
    253: 'Worn Honey Beige',
    254: 'Worn Brown',
    255: 'Worn Dark Brown',
    256: 'Worn Straw Beige',
    257: 'Brushed Steel',
    258: 'Brushed Black Steel',
    259: 'Brushed Aluminum',
    260: 'Chrome',
    261: 'Worn Off White',
    262: 'Util Off White',
    263: 'Worn Orange',
    264: 'Worn Light Orange',
    265: 'Metallic Securicor Green',
    266: 'Worn Taxi Yellow',
    267: 'Police Car Blue',
    268: 'Matte Green',
    269: 'Matte Brown',
    270: 'Worn Orange',
    271: 'Matte White',
    272: 'Worn White',
    273: 'Worn Olive Army Green',
    274: 'Pure White',
    275: 'Hot Pink',
    276: 'Salmon Pink',
    277: 'Metallic Vermillion Pink',
    278: 'Orange',
    279: 'Green',
    280: 'Blue',
    281: 'Mettalic Black Blue',
    282: 'Metallic Black Purple',
    283: 'Metallic Black Red',
    284: 'Hunter Green',
    285: 'Metallic Purple',
    286: 'Metaillic V Dark Blue',
    287: 'MODSHOP BLACK1',
    288: 'Matte Purple',
    289: 'Matte Dark Purple',
    290: 'Metallic Lava Red',
    291: 'Matte Forest Green',
    292: 'Matte Olive Drab',
    293: 'Matte Desert Brown',
    294: 'Matte Desert Tan',
    295: 'Matte Foilage Green',
    296: 'DEFAULT ALLOY COLOR',
    297: 'Epsilon Blue',
    298: 'Unknown',
  };

  return colorMap[colorId] || `Color ${colorId}`;
};
