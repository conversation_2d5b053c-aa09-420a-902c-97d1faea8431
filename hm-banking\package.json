{"name": "hm-banking", "version": "1.0.0", "description": "Banking resource for FiveM", "main": "index.js", "type": "module", "engines": {"node": "22.16.0"}, "scripts": {"watch:all": "concurrently -n \"GAME,UI\" -c \"blue,green\" \"npm run watch:game\" \"npm run watch:ui\"", "build:all": "vite build && cd ui && npm run build", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\"", "watch:game": "vite build --watch", "watch:ui": "cd ui && npm run dev"}, "keywords": ["fivem", "banking", "typescript"], "author": "HM", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "concurrently": "latest", "eslint": "latest", "eslint-config-prettier": "latest", "eslint-plugin-prettier": "latest", "prettier": "latest", "typescript": "latest", "vite": "latest"}, "dependencies": {"axios": "^1.9.0"}}