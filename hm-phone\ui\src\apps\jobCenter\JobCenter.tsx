import React, { useState, useEffect } from 'react';
import { useJobCenterStore } from './stores/jobCenterStore';
import { JobGroup } from './types/jobCenterTypes';
import { useNavigation } from '../../navigation/hooks';
import { motion, AnimatePresence } from 'framer-motion';
import JobList from './components/JobList';
import JobGroupList from './components/JobGroupList';
import GroupDetails from './components/GroupDetails';
import MyGroups from './components/MyGroups';

const JobCenter: React.FC = () => {
  const { goBack } = useNavigation();
  const {
    filteredJobs,
    selectedJob,
    selectedGroup,
    selectJob,
    selectGroup,
    setSearchTerm,
    jobs,
    groups,
    createGroup,
    leaveGroup,
    startJob,
    completeJob,
    getMyGroups,
    getGroupsForJob,
    updateGroupSettings
  } = useJobCenterStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [showMyGroups, setShowMyGroups] = useState(false);
  const myGroups = getMyGroups();

  // Calculate active group counts for each job
  const activeGroupCounts = jobs.reduce((counts, job) => {
    counts[job.id] = groups.filter(g => g.jobId === job.id && g.status === 'RECRUITING').length;
    return counts;
  }, {} as Record<number, number>);

  // Apply search term debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchTerm(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, setSearchTerm]);

  const handleSelectJob = (jobId: number) => {
    selectJob(jobId);
    selectGroup(null);
  };

  const handleSelectGroup = (groupId: number) => {
    selectGroup(groupId);
    selectJob(null);
  };

  const handleBackToList = () => {
    selectJob(null);
    selectGroup(null);
  };

  const handleCreateGroup = (
    jobId: number,
    name: string,
    isPrivate: boolean,
    password?: string
  ) => {
    createGroup(jobId, name, isPrivate, password);
  };

  const handleLeaveGroup = (groupId: number, memberId?: string) => {
    leaveGroup(groupId, memberId);
    if (!memberId) {
      selectGroup(null);
    }
  };

  const handleStartJob = (groupId: number) => {
    startJob(groupId);
  };

  const handleCompleteJob = (groupId: number, success: boolean) => {
    completeJob(groupId, success);
  };

  const handleUpdateGroupSettings = (groupId: number, updates: Partial<JobGroup>) => {
    updateGroupSettings(groupId, updates);
  };

  const isInAnyGroup = (): boolean => {
    return myGroups.some(group => group.status === 'RECRUITING' || group.status === 'IN_PROGRESS');
  };

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 pb-3">
      <AnimatePresence mode="wait">
        {selectedJob ? (
          <JobGroupList
            key="job-groups"
            job={selectedJob}
            groups={getGroupsForJob(selectedJob.id)}
            onBack={handleBackToList}
            onCreateGroup={handleCreateGroup}
            onJoinGroup={() => {}}
            onViewGroup={handleSelectGroup}
            isInAnyGroup={isInAnyGroup()}
          />
        ) : selectedGroup ? (
          <GroupDetails
            key="group-details"
            group={selectedGroup}
            job={jobs.find(j => j.id === selectedGroup.jobId)!}
            onBack={handleBackToList}
            onLeaveGroup={handleLeaveGroup}
            onStartJob={handleStartJob}
            onCompleteJob={handleCompleteJob}
            onUpdateSettings={handleUpdateGroupSettings}
          />
        ) : showMyGroups ? (
          <MyGroups
            key="my-groups"
            groups={myGroups}
            jobs={jobs}
            onBack={() => setShowMyGroups(false)}
            onViewGroup={handleSelectGroup}
          />
        ) : (
          <motion.div
            key="job-list-container"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col h-full"
          >
            {/* Header */}
            <div className="flex items-center gap-2 px-3 py-2 bg-[#0a0f1a] border-b border-white/10">
              <button onClick={goBack} className="text-white/80 hover:text-white">
                <i className="fas fa-arrow-left text-lg"></i>
              </button>
              <div className="flex-1">
                <input
                  type="search"
                  placeholder="Search jobs..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 bg-white/10 rounded-full text-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-teal-500/50"
                />
              </div>
              <button
                onClick={() => setShowMyGroups(true)}
                className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors relative"
              >
                <i className="fas fa-users text-white/80"></i>
                {myGroups.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-4 h-4 bg-teal-500 rounded-full text-white text-xs flex items-center justify-center">
                    {myGroups.length}
                  </span>
                )}
              </button>
            </div>

            {/* Job list */}
            <div className="flex-1 overflow-hidden">
              <JobList
                jobs={filteredJobs}
                onSelectJob={handleSelectJob}
                activeGroupCounts={activeGroupCounts}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default JobCenter;
