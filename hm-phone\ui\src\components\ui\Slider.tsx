import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface SliderProps {
  min: number;
  max: number;
  step?: number;
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
  className?: string;
}

export const Slider: React.FC<SliderProps> = ({
  min,
  max,
  step = 1,
  value,
  onChange,
  disabled = false,
  className = ''
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const trackRef = useRef<HTMLDivElement>(null);

  // Calculate percentage for styling
  const percentage = ((value - min) / (max - min)) * 100;

  // Handle click on track
  const handleTrackClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return;
    
    const track = trackRef.current;
    if (!track) return;
    
    const rect = track.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = x / rect.width;
    
    let newValue = min + percentage * (max - min);
    
    // Apply step
    if (step) {
      newValue = Math.round(newValue / step) * step;
    }
    
    // Clamp value
    newValue = Math.max(min, Math.min(max, newValue));
    
    onChange(newValue);
  };

  // Handle drag start
  const handleDragStart = () => {
    if (disabled) return;
    setIsDragging(true);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false);
  };

  // Handle drag
  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      const track = trackRef.current;
      if (!track) return;
      
      const rect = track.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      
      let newValue = min + percentage * (max - min);
      
      // Apply step
      if (step) {
        newValue = Math.round(newValue / step) * step;
      }
      
      // Clamp value
      newValue = Math.max(min, Math.min(max, newValue));
      
      onChange(newValue);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleMouseMove as any);
    document.addEventListener('touchend', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleMouseMove as any);
      document.removeEventListener('touchend', handleMouseUp);
    };
  }, [isDragging, min, max, step, onChange]);

  return (
    <div 
      className={`relative h-6 flex items-center ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      onClick={handleTrackClick}
    >
      {/* Track background */}
      <div 
        ref={trackRef}
        className="absolute h-2 w-full rounded-full bg-gray-700"
      >
        {/* Filled track */}
        <div 
          className="absolute h-full rounded-full bg-blue-600"
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {/* Thumb */}
      <motion.div
        className="absolute w-5 h-5 rounded-full bg-white shadow-md"
        style={{ left: `calc(${percentage}% - 10px)` }}
        onMouseDown={handleDragStart}
        onTouchStart={handleDragStart}
        onMouseUp={handleDragEnd}
        onTouchEnd={handleDragEnd}
        animate={{ scale: isDragging ? 1.2 : 1 }}
        whileTap={{ scale: 1.2 }}
        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
      />
    </div>
  );
};