// This will be imported in the Inventory Store
import { ItemSlot } from '@shared/inventory.types';
import { PanelType, SecondaryPanelType, InventoryState } from '../stores/inventoryStore';
import { SplitStackRequest } from '@shared';
import { sendSplitStackRequest } from '../hooks/useDndKitSlot';

/**
 * Split a stack of items into two stacks
 * @param state The current inventory state object
 * @param slotIndex Index of slot containing item to split
 * @param quantity Amount to split from the stack
 * @param panelType Type of panel where the item is located
 * @param panelId Optional ID for secondary panels
 */
export function handleSplitStack(
  state: InventoryState, 
  slotIndex: number,
  quantity: number,
  panelType: PanelType = 'main',
  panelId?: string
): void {
  // Determine which array to use based on panel type
  const getSlots = (panel: PanelType, instId?: string): ItemSlot[] => {
    if (panel === 'main') return state.gridItems;
    if (panel === 'action') return state.actionSlots;
    // Secondary panels
    if (
      panel === SecondaryPanelType.CONTAINER || 
      panel === SecondaryPanelType.GROUND || 
      panel === SecondaryPanelType.SHOP || 
      panel === SecondaryPanelType.STASH
    ) {
      if (instId && state.secondaryInventories[instId]) {
        return state.secondaryInventories[instId].items;
      }
    }
    return [];
  };
  // Get correct array and source slot
  const slotsArray = getSlots(panelType, panelId);
  const srcSlot = slotsArray[slotIndex];

  // Additional validation for slot types
  if (panelType === 'action') {
    // Special slots can't have items split from them
    state.toasts.push({
      id: `split-error-${Date.now()}`,
      message: 'Cannot split items from special slots',
      type: 'error',
      duration: 3000
    });
    return;
  }

  if (!srcSlot?.item || !srcSlot.item.stackable || (srcSlot.quantity || 0) <= 1 || quantity >= (srcSlot.quantity || 0)) {
    // Cannot split if:
    // - Item doesn't exist
    // - Item isn't stackable
    // - Quantity is 1 or less
    // - Requested split amount is greater than or equal to current quantity
    state.toasts.push({
      id: `split-error-${Date.now()}`,
      message: 'Cannot split stack (invalid operation)',
      type: 'error',
      duration: 3000
    });
    return;
  }
  
  // If item has special properties, validate those as well
  if (srcSlot.item.type === 'weapon' && srcSlot.item.durability !== undefined) {
    // For weapons, ensure we're copying durability correctly
    console.log(`Splitting weapon with durability: ${srcSlot.item.durability}`);
  }

  // Find the first empty slot
  const emptySlotIndex = slotsArray.findIndex(slot => !slot.item);
  
  if (emptySlotIndex === -1) {
    // No empty slot available
    state.toasts.push({
      id: `no-empty-slot-${Date.now()}`,
      message: 'No empty slot available to split stack',
      type: 'error',
      duration: 3000
    });
    return;
  }

  // Create a clone of the item for the new stack
  // This is important because each item instance should have a unique instanceId
  const newItem = {
    ...srcSlot.item,
    instanceId: `inst-${srcSlot.item.definitionId}-${Date.now()}-${Math.floor(Math.random() * 10000)}`
  };

  // Update quantities
  slotsArray[emptySlotIndex].item = newItem;
  slotsArray[emptySlotIndex].quantity = quantity;
  srcSlot.quantity = (srcSlot.quantity || 0) - quantity;

  // Show success toast
  state.toasts.push({
    id: `split-success-${Date.now()}`,
    message: `Split ${quantity} ${srcSlot.item.label}`,
    type: 'success',
    duration: 3000,
    itemIcon: srcSlot.item.icon,
    quantity: quantity
  });
  // Send command to server for any panel type to ensure data consistency
  const data: SplitStackRequest = { 
    action: 'splitStack', 
    slotIndex, 
    quantity,
    panelType: panelType as string,
    panelId
  };
  
  // Send the request to the server
  try {
    sendSplitStackRequest(data)
      .then(response => {
        if (!response.ok) {
          // If server rejects the operation, show error and revert changes
          console.error(`Server rejected split stack operation: ${response.status}`);
          state.toasts.push({
            id: `split-error-server-${Date.now()}`,
            message: 'Server rejected split stack operation',
            type: 'error',
            duration: 3000
          });
          
          // Revert changes
          slotsArray[emptySlotIndex].item = undefined;
          slotsArray[emptySlotIndex].quantity = undefined;
          srcSlot.quantity = (srcSlot.quantity || 0) + quantity;
        }
      })
      .catch(err => {
        console.error('Error sending split stack request:', err);
        // Show error toast
        state.toasts.push({
          id: `split-error-network-${Date.now()}`,
          message: 'Network error when splitting stack',
          type: 'error',
          duration: 3000
        });
        
        // Revert changes on error
        slotsArray[emptySlotIndex].item = undefined;
        slotsArray[emptySlotIndex].quantity = undefined;
        srcSlot.quantity = (srcSlot.quantity || 0) + quantity;
      });
  } catch (error) {
    console.error('Exception in split stack operation:', error);
    // Show error toast
    state.toasts.push({
      id: `split-error-exception-${Date.now()}`,
      message: 'Error when splitting stack',
      type: 'error',
      duration: 3000
    });
    
    // Revert changes on error
    slotsArray[emptySlotIndex].item = undefined;
    slotsArray[emptySlotIndex].quantity = undefined;
    srcSlot.quantity = (srcSlot.quantity || 0) + quantity;
  }
}
