/**
 * Types for the Garage app
 */

export interface Vehicle {
  id: number;
  name: string;
  model: string;
  plate: string;
  garage: string;
  state: string;
  fuel: number;
  engine: number;
  body: number;
  image?: string;
  brand?: string;
  imageUrl?: string;
  status?: string;
  category?: string;
  location?: string;
  licensePlate?: string;
  type?: string;

  // Financial properties
  balance?: number;
  paymentamount?: number;
  paymentsleft?: number;
  financetime?: number;

  // Additional properties
  job?: string;
  owner?: string;
  identifier?: string;
}

export interface GarageState {
  vehicles: Vehicle[];
  selectedVehicleId: number | null;
  loading: boolean;
  error: string | null;
}

export interface GarageStore extends GarageState {
  // Actions
  setVehicles: (vehicles: Vehicle[]) => void;
  selectVehicle: (id: number | null) => void;
  trackVehicle: (id: number) => void;
  toggleEngine: (id: number) => void;
  toggleLock: (id: number) => void;

  // Getters
  getVehicleById: (id: number) => Vehicle | undefined;
  getVehiclesByGarage: (garage: string) => Vehicle[];
}
