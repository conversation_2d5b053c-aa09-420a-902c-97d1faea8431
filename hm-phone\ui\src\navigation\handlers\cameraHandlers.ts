// Camera event handlers

import { registerEventHandler } from '../../fivem';

// Register handler for zoom changes
registerEventHandler('camera', 'zoomChanged', data => {
  console.log('[Camera] Zoom level changed:', data);
  // No need to update store here as we're handling this in the component
});

// Register handler for flash changes
registerEventHandler('camera', 'flashChanged', data => {
  console.log('[Camera] Flash state changed:', data);
  // No need to update store here as we're handling this in the component
});

// Register handler for selfie mode changes
registerEventHandler('camera', 'selfieModeChanged', data => {
  console.log('[Camera] Selfie mode changed:', data);
  // No need to update store here as we're handling this in the component
});

// Register handler for photo taken
registerEventHandler('camera', 'photoTaken', data => {
  console.log('[Camera] Photo taken:', data);
});
