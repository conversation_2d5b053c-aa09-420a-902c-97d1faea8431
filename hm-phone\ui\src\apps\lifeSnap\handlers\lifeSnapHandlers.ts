/**
 * LifeSnap app message handlers
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import { Post, Profile, Story } from '../types/lifeSnapTypes';

// Register handler for posts data
registerEventHandler('lifeSnap', 'posts', data => {
  console.log('[LifeSnap] Received posts data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the posts data
    useLifeSnapStore.getState().handlers.onSetPosts(data as Post[]);
  } else {
    console.error('[LifeSnap] Received invalid posts data (not an array):', data);
  }
});

// Register handler for stories data
registerEventHandler('lifeSnap', 'stories', data => {
  console.log('[LifeSnap] Received stories data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the stories data
    useLifeSnapStore.getState().handlers.onSetStories(data as Story[]);
  } else {
    console.error('[LifeSnap] Received invalid stories data (not an array):', data);
  }
});

// Register handler for profiles data
registerEventHandler('lifeSnap', 'profiles', data => {
  console.log('[LifeSnap] Received profiles data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the profiles data
    useLifeSnapStore.getState().handlers.onSetProfiles(data as Profile[]);
  } else {
    console.error('[LifeSnap] Received invalid profiles data (not an array):', data);
  }
});
