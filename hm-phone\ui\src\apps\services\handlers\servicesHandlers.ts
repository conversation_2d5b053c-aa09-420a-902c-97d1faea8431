/**
 * Services app message handlers
 *
 * These handlers receive events from the FiveM client and update the Services store.
 *
 * Flow:
 * 1. Client sends Services data using SendNUIMessage({app: 'services', type: 'services', data: [...]})
 * 2. clientEventReceiver dispatches the event to the registered handler
 * 3. <PERSON><PERSON> validates the data and updates the Services store
 * 4. UI components re-render with the new data
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useServicesStore } from '../stores/servicesStore';
import { Service } from '../types/servicesTypes';

// Register handler for Services data
registerEventHandler('services', 'setServices', data => {
  console.log('[Services] Received services data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the services data
    useServicesStore.getState().handlers.onSetServices(data as Service[]);
  } else {
    console.error('[Services] Received invalid services data (not an array):', data);
  }
});
