import React, { useState, useEffect } from 'react';
import { sendNuiMessage, NuiActions, onNuiEvent } from '../utils/nui';
import { useAdmin } from '../stores/adminStore';

// Player interface
interface Player {
  id: number;
  name: string;
  ping: number;
  health: number;
  armor: number;
  position: { x: number; y: number; z: number; };
  identifiers: { license: string; discord?: string; steam?: string; ip?: string; };
  vehicle?: number;
}

// Common hook for player operations
const usePlayerOperation = () => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  useEffect(() => {
    sendNuiMessage(NuiActions.GET_PLAYER_LIST, {});
    const playerListCleanup = onNuiEvent('playerList', (response: { data: Player[] }) => {
      const players = response.data || [];
      setPlayers(players);
    });
    const operationResultCleanup = onNuiEvent('operationResult', (result: { success: boolean; message: string }) => {
      setMessage(result.message);
      setIsLoading(false);
      setTimeout(() => setMessage(''), 3000);
    });
    return () => { playerListCleanup(); operationResultCleanup(); };
  }, []);

  return { players, message, isLoading, setMessage, setIsLoading };
};

// Give Item Feature - Compact
export const GiveItemFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const { items } = useAdmin();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [selectedItemName, setSelectedItemName] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);

  const handleGiveItem = () => {
    if (!selectedPlayerId || !selectedItemName || quantity < 1) { 
      setMessage('Please select a player, item, and valid quantity'); 
      return; 
    }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.GIVE_ITEM, { targetId: selectedPlayerId, itemName: selectedItemName, quantity: quantity });
    setSelectedItemName('');
    setQuantity(1);
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select 
          value={selectedPlayerId} 
          onChange={(e) => setSelectedPlayerId(Number(e.target.value))} 
          className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          <option value={0}>Select Player...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
      </div>
      <div className="flex gap-1">
        <select 
          value={selectedItemName} 
          onChange={(e) => setSelectedItemName(e.target.value)} 
          className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          <option value="">Select Item...</option>
          {items.map(item => (
            <option key={item.name} value={item.name}>
              {item.label} ({item.name})
            </option>
          ))}
        </select>
        <input 
          type="number" 
          value={quantity} 
          onChange={(e) => setQuantity(Math.max(1, Number(e.target.value)))} 
          min="1" 
          max="999" 
          placeholder="Qty" 
          className="w-12 shrink-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500" 
        />
      </div>
      <button 
        onClick={handleGiveItem} 
        disabled={isLoading || !selectedPlayerId || !selectedItemName} 
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-neutral-600 text-white px-2 py-1 rounded text-xs transition-colors flex items-center justify-center gap-1"
      >
        <i className="fas fa-gift text-xs"></i>Give Item
      </button>
      {message && (
        <div className={`p-2 rounded text-sm ${message.includes('Success') || message.includes('successfully') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
          {message}
        </div>
      )}
    </div>
  );
};

// Give Weapon Feature - Compact
export const GiveWeaponFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const { weapons } = useAdmin();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [selectedWeaponName, setSelectedWeaponName] = useState<string>('');
  const [ammo, setAmmo] = useState<number>(250);

  const handleGiveWeapon = () => {
    if (!selectedPlayerId || !selectedWeaponName || ammo < 0) { 
      setMessage('Please select a player, weapon, and valid ammo amount'); 
      return; 
    }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.GIVE_WEAPON, { 
      targetId: selectedPlayerId, 
      weaponName: selectedWeaponName, 
      ammo: ammo 
    });
    setSelectedWeaponName('');
    setAmmo(250);
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select value={selectedPlayerId} onChange={(e) => setSelectedPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-orange-500">
          <option value={0}>Select Player...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <select value={selectedWeaponName} onChange={(e) => setSelectedWeaponName(e.target.value)} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-orange-500">
          <option value="">Select Weapon...</option>
          {weapons.map(weapon => <option key={weapon.name} value={weapon.name}>{weapon.label} ({weapon.name})</option>)}
        </select>
      </div>
      <div className="flex gap-1">
        <input type="number" value={ammo} onChange={(e) => setAmmo(Math.max(0, Number(e.target.value)))} min="0" max="9999" placeholder="Ammo" className="w-16 shrink-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-orange-500" />
        <button onClick={handleGiveWeapon} disabled={isLoading || !selectedPlayerId || !selectedWeaponName} className="flex-1 min-w-0 bg-orange-600 hover:bg-orange-700 disabled:bg-neutral-600 text-white px-2 py-1 rounded text-xs transition-colors flex items-center justify-center gap-1">
          <i className="fas fa-crosshairs text-xs"></i>Give Weapon
        </button>
      </div>
      {message && <div className={`p-2 rounded text-sm ${message.includes('Success') || message.includes('successfully') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>{message}</div>}
    </div>
  );
};

// Teleport Player Feature - Compact
export const TeleportPlayerFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [targetPlayerId, setTargetPlayerId] = useState<number>(0);
  const [coordX, setCoordX] = useState<number>(0);
  const [coordY, setCoordY] = useState<number>(0);
  const [coordZ, setCoordZ] = useState<number>(0);
  const teleportToPlayer = () => {
    if (!selectedPlayerId || !targetPlayerId || selectedPlayerId === targetPlayerId) { 
      setMessage('Please select different players'); return; 
    }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.TELEPORT_PLAYER_TO_PLAYER, { targetId: selectedPlayerId, destinationPlayerId: targetPlayerId });
  };
  const teleportToCoords = () => {
    if (!selectedPlayerId) { setMessage('Please select a player'); return; }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.TELEPORT_PLAYER_TO_COORDS, { targetId: selectedPlayerId, x: coordX, y: coordY, z: coordZ });
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select value={selectedPlayerId} onChange={(e) => setSelectedPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-purple-500">
          <option value={0}>From...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <select value={targetPlayerId} onChange={(e) => setTargetPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-purple-500">
          <option value={0}>To...</option>
          {players.filter(p => p.id !== selectedPlayerId).map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <button onClick={teleportToPlayer} disabled={isLoading || !selectedPlayerId || !targetPlayerId} className="px-1 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-neutral-600 text-white rounded text-xs transition-colors shrink-0 w-6">
          <i className="fas fa-exchange-alt text-xs"></i>
        </button>
      </div>
      <div className="flex gap-1">        <input type="number" value={coordX} onChange={(e) => setCoordX(Number(e.target.value))} placeholder="X" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-purple-500" />
        <input type="number" value={coordY} onChange={(e) => setCoordY(Number(e.target.value))} placeholder="Y" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-purple-500" />
        <input type="number" value={coordZ} onChange={(e) => setCoordZ(Number(e.target.value))} placeholder="Z" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-purple-500" />
        <button onClick={teleportToCoords} disabled={isLoading || !selectedPlayerId} className="px-1 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-neutral-600 text-white rounded text-xs transition-colors shrink-0 w-6">
          <i className="fas fa-map-marker-alt text-xs"></i>
        </button>
      </div>
      {message && <div className={`p-2 rounded text-sm ${message.includes('successfully') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>{message}</div>}
    </div>
  );
};

// Kick Player Feature - Compact
export const KickPlayerFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const kickPlayer = () => {
    if (!selectedPlayerId) { setMessage('Please select a player'); return; }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.KICK_PLAYER, { targetId: selectedPlayerId, reason: reason || 'No reason provided' });
    setReason('');
  };  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select value={selectedPlayerId} onChange={(e) => setSelectedPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-yellow-500">
          <option value={0}>Select...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <input type="text" value={reason} onChange={(e) => setReason(e.target.value)} placeholder="Reason" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-yellow-500" />
        <button onClick={kickPlayer} disabled={isLoading || !selectedPlayerId} className="px-2 py-1 bg-yellow-600 hover:bg-yellow-700 disabled:bg-neutral-600 text-white rounded text-xs transition-colors shrink-0">
          <i className="fas fa-sign-out-alt text-xs"></i>
        </button>
      </div>
      {message && <div className={`p-2 rounded text-sm ${message.includes('successfully') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>{message}</div>}
    </div>
  );
};

// Ban Player Feature - Compact
export const BanPlayerFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [banDuration, setBanDuration] = useState<number>(0);
  const banPlayer = () => {
    if (!selectedPlayerId) { setMessage('Please select a player'); return; }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.BAN_PLAYER, { targetId: selectedPlayerId, reason: reason || 'No reason provided', duration: banDuration });
    setReason('');
  };  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select value={selectedPlayerId} onChange={(e) => setSelectedPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-red-500">
          <option value={0}>Select...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <input type="text" value={reason} onChange={(e) => setReason(e.target.value)} placeholder="Reason" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-red-500" />
      </div>
      <div className="flex gap-1">
        <input type="number" value={banDuration} onChange={(e) => setBanDuration(Number(e.target.value))} min="0" placeholder="Days" className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-red-500" />
        <button onClick={banPlayer} disabled={isLoading || !selectedPlayerId} className="px-2 py-1 bg-red-600 hover:bg-red-700 disabled:bg-neutral-600 text-white rounded text-xs transition-colors shrink-0">
          <i className="fas fa-ban text-xs"></i>
        </button>
      </div>
      {message && <div className={`p-2 rounded text-sm ${message.includes('successfully') || message.includes('banned') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>{message}</div>}
      <div className="bg-red-900/30 border border-red-700 rounded p-2">
        <div className="flex items-start gap-2">
          <i className="fas fa-exclamation-triangle text-red-500 text-sm mt-0.5"></i>
          <div><h4 className="font-medium text-red-500 text-sm">Warning</h4><p className="text-xs text-neutral-300">Use responsibly. Actions are logged.</p></div>
        </div>
      </div>
    </div>
  );
};

// Player Info Feature - Compact
export const PlayerInfoFeature: React.FC = () => {
  const { players, isLoading, setIsLoading } = usePlayerOperation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [playerInfo, setPlayerInfo] = useState<any>(null);

  useEffect(() => {
    const playerInfoCleanup = onNuiEvent('playerInfo', (data: any) => {
      setPlayerInfo(data);
      setIsLoading(false);
    });
    return () => { playerInfoCleanup(); };
  }, []);

  const getPlayerInfo = () => {
    if (!selectedPlayerId) return;
    setIsLoading(true);
    sendNuiMessage(NuiActions.GET_PLAYER_INFO, { playerId: selectedPlayerId });
  };
  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select value={selectedPlayerId} onChange={(e) => setSelectedPlayerId(Number(e.target.value))} className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-indigo-500">
          <option value={0}>Select...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <button onClick={getPlayerInfo} disabled={isLoading || !selectedPlayerId} className="px-2 py-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-neutral-600 text-white rounded text-xs transition-colors shrink-0">
          <i className="fas fa-info-circle text-xs"></i>
        </button>
      </div>
      {playerInfo && (
        <div className="bg-neutral-700 rounded p-2 text-xs space-y-1">
          <div className="flex justify-between"><span className="text-neutral-400">Name:</span><span>{playerInfo.name}</span></div>
          <div className="flex justify-between"><span className="text-neutral-400">ID:</span><span>{playerInfo.id}</span></div>
          <div className="flex justify-between"><span className="text-neutral-400">Ping:</span><span>{playerInfo.ping}ms</span></div>
          <div className="flex justify-between"><span className="text-neutral-400">Health:</span><span>{playerInfo.health}/100</span></div>
          <div className="flex justify-between"><span className="text-neutral-400">Armor:</span><span>{playerInfo.armor}/100</span></div>
          {playerInfo.identifiers && (
            <div className="border-t border-neutral-600 pt-1 mt-1">
              <div className="text-neutral-400 text-xs mb-1">Identifiers:</div>
              {Object.entries(playerInfo.identifiers).map(([key, value]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="text-neutral-400">{key}:</span>
                  <span className="font-mono break-all">{value as string}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Heal Player Feature - Compact
export const HealPlayerFeature: React.FC = () => {
  const { players, message, isLoading, setMessage, setIsLoading } = usePlayerOperation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<number>(0);
  const [healAmount, setHealAmount] = useState<number>(100);

  const healPlayer = () => {
    if (!selectedPlayerId) { 
      setMessage('Please select a player'); 
      return; 
    }
    setIsLoading(true);
    setMessage('');
    sendNuiMessage(NuiActions.HEAL_PLAYER, { 
      targetId: selectedPlayerId, 
      health: healAmount 
    });
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <select 
          value={selectedPlayerId} 
          onChange={(e) => setSelectedPlayerId(Number(e.target.value))} 
          className="flex-1 min-w-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
        >
          <option value={0}>Select Player...</option>
          {players.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
        </select>
        <input 
          type="number" 
          value={healAmount} 
          onChange={(e) => setHealAmount(Math.max(1, Math.min(200, Number(e.target.value))))} 
          min="1" 
          max="200" 
          placeholder="HP" 
          className="w-12 shrink-0 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-1 focus:ring-green-500" 
        />
      </div>
      <button 
        onClick={healPlayer} 
        disabled={isLoading || !selectedPlayerId} 
        className="w-full bg-green-600 hover:bg-green-700 disabled:bg-neutral-600 text-white px-2 py-1 rounded text-xs transition-colors flex items-center justify-center gap-1"
      >
        <i className="fas fa-heart text-xs"></i>Heal Player
      </button>
      {message && (
        <div className={`p-2 rounded text-sm ${message.includes('Success') || message.includes('successfully') ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
          {message}
        </div>
      )}
    </div>
  );
};
