import { create } from 'zustand';
import { isBrowser } from '../../../utils/environment';
import { clientRequests } from '../../../fivem';

// Mock data for browser testing
const photosMockData = {
  photos: [
    {
      id: 1,
      identifier: 'FXG8T3OE', // Player's identifier
      imageUrl: 'https://picsum.photos/800/1000?random=1',
      timestamp: new Date().getTime(),
      metadata: {
        mode: 'photo',
        flash: false
      }
    },
    {
      id: 2,
      identifier: 'FXG8T3OE', // Player's identifier
      imageUrl: 'https://picsum.photos/800/1000?random=2',
      timestamp: new Date().getTime() - 3600000, // 1 hour ago
      metadata: {
        mode: 'selfie',
        flash: true
      }
    },
    {
      id: 3,
      identifier: 'FXG8T3OE', // Player's identifier
      imageUrl: 'https://picsum.photos/800/1000?random=3',
      timestamp: new Date().getTime() - 86400000, // 1 day ago
      metadata: {
        mode: 'photo',
        flash: false
      }
    }
  ]
};

export interface Photo {
  id: number;
  identifier?: string; // Player's identifier
  imageUrl: string;
  timestamp: Date | number;
  location?: {
    name: string;
    coordinates: {
      x: number;
      y: number;
      z: number;
    };
  };
  metadata?: {
    mode?: 'photo' | 'selfie';
    flash?: boolean;
  };
}

interface PhotosState {
  photos: Photo[];
  loading: boolean;
  initialized: boolean;

  // Actions
  actions: {
    getPhotos: () => Promise<void>;
    deletePhoto: (photoId: number) => Promise<void>;
  };

  // Handlers
  handlers: {
    onSetPhotos: (photos: Photo[]) => void;
    onPhotoSaved: (photo: Photo) => void;
    onPhotoDeleted: (photoId: number) => void;
    onClearFullResolutionPhotos: () => void; // New handler for cleanup
  };
}

export const usePhotosStore = create<PhotosState>((set, get) => ({
  photos: [],
  loading: false,
  initialized: false,

  // Actions
  actions: {
    getPhotos: async () => {
      const state = get();

      // If photos are already initialized, don't fetch them again
      if (state.initialized && state.photos.length > 0) {
        console.log('[PhotosStore] Photos already initialized, skipping request');
        return;
      }

      if (state.loading) {
        console.log('[PhotosStore] Photos already loading, skipping request');
        return;
      }

      set({ loading: true });

      try {
        const response = await clientRequests.send(
          'photos',
          'getAllPhotos',
          {}, // No data needed
          photosMockData.photos,
          state.handlers.onSetPhotos as (data: unknown) => void
        );

        console.log('[PhotosStore] Photos request completed');

        // The actual photos will be sent via the photos event handler
        // We don't need to do anything here except in browser mode
        if (isBrowser && response.success && 'data' in response) {
          get().handlers.onSetPhotos(response.data as Photo[]);
        }

        // Mark photos as initialized
        set({ initialized: true });
      } catch (error) {
        console.error('[PhotosStore] Error loading photos:', error);
      } finally {
        set({ loading: false });
      }
    },

    deletePhoto: async (photoId: number) => {
      console.log('[PhotosStore] Deleting photo:', photoId);

      try {
        const response = await clientRequests.send(
          'photos',
          'deletePhoto',
          { id: photoId },
          photoId,
          get().handlers.onPhotoDeleted as (data: unknown) => void
        );

        console.log('[PhotosStore] Photo delete response:', response);

        // The actual deletion confirmation will come via the photoDeleted event handler
        // We don't need to do anything here except in browser mode
        if (isBrowser && response.success) {
          get().handlers.onPhotoDeleted(photoId);
        }
      } catch (error) {
        console.error('[PhotosStore] Error deleting photo:', error);
      }
    }
  },

  // Handlers
  handlers: {
    onSetPhotos: (photos: Photo[]) => {
      console.log('[PhotosStore] Setting photos:', photos.length);

      // Sort photos by timestamp (newest first)
      const sortedPhotos = [...photos].sort((a, b) => {
        const timestampA = a.timestamp instanceof Date ? a.timestamp.getTime() : a.timestamp;
        const timestampB = b.timestamp instanceof Date ? b.timestamp.getTime() : b.timestamp;
        return timestampB - timestampA;
      });

      // Set photos and mark as initialized
      set({ photos: sortedPhotos, initialized: true });
    },

    onPhotoSaved: (photo: Photo) => {
      console.log('[PhotosStore] Photo saved:', photo);

      set(state => {
        // Check if the photo already exists
        const existingIndex = state.photos.findIndex(p => p.id === photo.id);

        if (existingIndex >= 0) {
          // Update existing photo
          const updatedPhotos = [...state.photos];
          updatedPhotos[existingIndex] = photo;
          return { photos: updatedPhotos };
        } else {
          // Add new photo to the beginning
          return { photos: [photo, ...state.photos] };
        }
      });
    },

    onPhotoDeleted: (photoId: number) => {
      console.log('[PhotosStore] Photo deleted:', photoId);

      set(state => ({
        photos: state.photos.filter(p => p.id !== photoId)
      }));
    },

    onClearFullResolutionPhotos: () => {
      // console.log('[PhotosStore] Clearing full-resolution photos to save memory');

      const { photos } = get();

      // Replace full-resolution image URLs with thumbnails
      // This keeps the photo metadata but reduces memory usage
      const thumbnailPhotos = photos.map(photo => ({
        ...photo,
        // Keep all metadata
        id: photo.id,
        identifier: photo.identifier,
        timestamp: photo.timestamp,
        location: photo.location,
        metadata: photo.metadata,
        // Replace full-resolution image URL with a placeholder
        // In a real implementation, you would use actual thumbnail URLs
        imageUrl: photo.imageUrl.replace(/\.(jpg|jpeg|png|gif|webp)/, '_thumb.$1')
      }));

      // Update the store with thumbnail versions
      set({ photos: thumbnailPhotos });

      console.log('[PhotosStore] Cleared full-resolution photos, using thumbnails instead');
    }
  }
}));
