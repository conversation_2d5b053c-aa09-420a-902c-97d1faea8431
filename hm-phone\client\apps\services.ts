/**
 * Services App - Client Side
 *
 * This file handles client-side functionality for the Services app.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the services app
 */
export function initializeServicesApp(): void {
    console.log('[Services] Initializing client-side services app');

    // Register client events
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();
}

/**
 * Register client events for the services app
 */
function registerClientEvents(): void {
    // Register event for services data
    onNet('hm-phone:services', (servicesData: any) => {
        console.log('[Services] Services data received:', servicesData.length);

        // Send the services data to the UI
        sendToUI('setServices', servicesData);
    });

    // Register event for services error
    onNet('hm-phone:servicesError', (errorMessage: string) => {
        console.error('[Services] Services error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });

    // Register event for service request result
    onNet('hm-phone:serviceRequestResult', (result: any) => {
        console.log('[Services] Service request result:', result);

        // Send the result to the UI
        sendToUI('serviceRequestResult', result);
    });

    // Register event for service request error
    onNet('hm-phone:serviceRequestError', (errorMessage: string) => {
        console.error('[Services] Service request error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });

    // Register event for service request
    onNet('hm-phone:serviceRequest', (requestData: any) => {
        console.log('[Services] Service request received:', requestData);

        // Show a notification for the service request
        showServiceRequestNotification(requestData);
    });

    // Register event for adding a service blip
    onNet('hm-phone:addServiceBlip', (blipData: any) => {
        console.log('[Services] Adding service blip:', blipData);

        // Add a blip on the map for the service request
        addServiceBlip(blipData);
    });
}

/**
 * Register NUI handlers for the services app
 */
function registerNUIHandlers(): void {
    // Register handler for getting services
    registerAppHandler('services', 'getServices', async () => {
        console.log('[Services] Received getServices request from UI');

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Services] Getting services for player: ${stateid || identifier}`);

            // Request services from the server
            emitNet('hm-phone:getServices');

            // Return success to the UI
            // The actual services will be sent via the hm-phone:services event
            return { success: true };
        } catch (error) {
            console.error('[Services] Error getting services:', error);
            return { success: false, error: 'Failed to get services' };
        }
    });

    // Register handler for requesting a service
    registerAppHandler('services', 'requestService', async (data: any) => {
        console.log('[Services] Received requestService request from UI:', data);

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Services] Requesting service for player: ${stateid || identifier}`);

            // Extract the service ID, message, and location
            const { serviceId, message } = data;

            // Get the player's current location
            const location = await getPlayerLocation();

            // Send the request to the server
            emitNet('hm-phone:requestService', serviceId, message, location);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Services] Error requesting service:', error);
            return { success: false, error: 'Failed to request service' };
        }
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'services',
        type,
        data
    });
}

/**
 * Get the player's current location
 * @returns Player location
 */
async function getPlayerLocation(): Promise<any> {
    // Get the player's current coordinates
    const playerPed = PlayerPedId();
    const coords = GetEntityCoords(playerPed, false);

    // Get the street name
    const [streetHash, crossingHash] = GetStreetNameAtCoord(coords[0], coords[1], coords[2]);
    const streetName = GetStreetNameFromHashKey(streetHash);
    const crossingName = GetStreetNameFromHashKey(crossingHash);

    // Get the zone name
    const zoneHash = GetNameOfZone(coords[0], coords[1], coords[2]);
    const zoneName = GetLabelText(zoneHash);

    // Return the location data
    return {
        x: coords[0],
        y: coords[1],
        z: coords[2],
        streetName,
        crossingName,
        zoneName,
        name: streetName + (crossingName ? ` & ${crossingName}` : '') + (zoneName ? `, ${zoneName}` : '')
    };
}

/**
 * Show a notification for a service request
 * @param requestData Service request data
 */
function showServiceRequestNotification(requestData: any): void {
    // Extract the request data
    const { service, requester, message } = requestData;

    // Show a notification
    const notificationTitle = `${service} Request`;
    const notificationMessage = `${requester} is requesting ${service} services.${
        message ? `\nMessage: ${message}` : ''
    }`;

    // Use the native notification system
    SetNotificationTextEntry('STRING');
    AddTextComponentString(notificationMessage);
    SetNotificationMessage('CHAR_DEFAULT', 'CHAR_DEFAULT', true, 4, notificationTitle, 'New Request');
    DrawNotification(false, true);

    // Play a sound
    PlaySound(-1, 'Event_Message_Purple', 'GTAO_FM_Events_Soundset', false, 0, true);
}

/**
 * Add a blip on the map for a service request
 * @param blipData Service blip data
 */
function addServiceBlip(blipData: any): void {
    // Extract the blip data
    const { service, requester, location } = blipData;

    // Create a blip at the location
    const blip = AddBlipForCoord(location.x, location.y, location.z);

    // Set the blip properties
    SetBlipSprite(blip, 280); // Use an appropriate sprite
    SetBlipDisplay(blip, 4);
    SetBlipScale(blip, 1.0);
    SetBlipColour(blip, 2); // Red
    SetBlipAsShortRange(blip, false);

    // Add a label to the blip
    BeginTextCommandSetBlipName('STRING');
    AddTextComponentString(`${service} Request: ${requester}`);
    EndTextCommandSetBlipName(blip);

    // Remove the blip after a delay
    setTimeout(() => {
        RemoveBlip(blip);
    }, 60000); // Remove after 1 minute
}
