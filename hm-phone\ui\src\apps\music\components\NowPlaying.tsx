import React, { useEffect, useState } from 'react';
import { useNavigation } from '../../../navigation/hooks';
import { useMusicStore } from '../stores/musicStore';

const NowPlaying: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    togglePlay,
    nextSong,
    previousSong,
    toggleRepeat,
    toggleShuffle,
    isRepeatEnabled,
    isShuffleEnabled,
    seekTo,
    setCurrentArtistId
  } = useMusicStore();

  // Force re-render every second to update progress bar
  const [, setForceUpdate] = useState(0);

  useEffect(() => {
    if (isPlaying) {
      const forceUpdateTimer = setInterval(() => {
        // This forces a re-render which will update the progress bar
        setForceUpdate(prev => prev + 1);
      }, 1000);

      return () => clearInterval(forceUpdateTimer);
    }
  }, [isPlaying]);

  // Format time as mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white pt-[32px]">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-2">
        <button onClick={goBack} className="text-white cursor-pointer">
          <i className="fas fa-chevron-down text-xl"></i>
        </button>
        <div className="text-center">
          <h1 className="text-lg font-medium">Play Now</h1>
        </div>
        <button className="text-white cursor-pointer">
          <i className="fas fa-ellipsis-v"></i>
        </button>
      </div>

      {/* Album Art */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="relative w-full max-w-[200px] aspect-square rounded-full overflow-hidden border-4 border-pink-500/30 shadow-lg shadow-pink-500/20">
          <img
            src={currentSong?.imageUrl || 'https://picsum.photos/400/400?random=1'}
            alt={currentSong?.title || ''}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Song Info */}
      <div className="px-6 mb-3">
        <h2 className="text-xl font-bold text-center mb-1">
          "{currentSong?.title || 'No song playing'}"
        </h2>
        <p
          className="text-gray-400 text-center cursor-pointer hover:text-pink-500"
          onClick={() => {
            if (currentSong?.artistId) {
              setCurrentArtistId(currentSong.artistId);
              openView('artist', { artistId: currentSong.artistId });
            }
          }}
        >
          {currentSong?.artist?.name || 'Unknown Artist'}
        </p>
      </div>

      {/* Progress Bar */}
      <div className="px-6 mb-3">
        <div
          className="h-1 bg-gray-700 rounded-full cursor-pointer"
          onClick={e => {
            // Calculate position click percentage
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = x / rect.width;
            const newTime = Math.floor(percentage * (duration || 0));
            seekTo(newTime);
          }}
        >
          <div
            className="h-full bg-pink-500 rounded-full relative"
            style={{ width: `${((currentTime || 0) / (duration || 1)) * 100}%` }}
          >
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-pink-500 border-2 border-white rounded-full"></div>
          </div>
        </div>
        <div className="flex justify-between mt-1 text-xs text-gray-400">
          <span>{formatTime(currentTime || 0)}</span>
          <span>{formatTime(duration || 0)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="px-6 mb-5">
        <div className="flex justify-between items-center">
          <button
            className={`text-white p-1 cursor-pointer ${isShuffleEnabled ? 'text-pink-500' : ''}`}
            onClick={toggleShuffle}
          >
            <i className="fas fa-random"></i>
          </button>

          <button className="text-white p-1 cursor-pointer" onClick={previousSong}>
            <i className="fas fa-step-backward text-2xl"></i>
          </button>

          <button
            className="bg-pink-500 rounded-full w-14 h-14 flex items-center justify-center cursor-pointer"
            onClick={togglePlay}
          >
            <i className={`fas ${isPlaying ? 'fa-pause' : 'fa-play'} text-white text-xl`}></i>
          </button>

          <button className="text-white p-1 cursor-pointer" onClick={nextSong}>
            <i className="fas fa-step-forward text-2xl"></i>
          </button>

          <button
            className={`text-white p-1 cursor-pointer ${isRepeatEnabled ? 'text-pink-500' : ''}`}
            onClick={toggleRepeat}
          >
            <i className="fas fa-redo"></i>
          </button>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="px-6 mb-4 flex justify-between">
        <button className="text-white cursor-pointer">
          <i className="far fa-heart text-xl"></i>
        </button>

        <button className="text-white cursor-pointer">
          <i className="fas fa-arrow-down text-xl"></i>
        </button>

        <button className="text-white cursor-pointer">
          <i className="fas fa-share-alt text-xl"></i>
        </button>

        <button className="text-white cursor-pointer">
          <i className="fas fa-list text-xl"></i>
        </button>
      </div>
    </div>
  );
};

export default NowPlaying;
