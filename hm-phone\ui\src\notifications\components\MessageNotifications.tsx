import React, { useState } from 'react';
import { Notification } from '../types/notificationTypes';

interface MessageNotificationsProps {
  notifications: Notification[];
  onNotificationClick?: (notification: Notification) => void;
}

export const MessageNotifications: React.FC<MessageNotificationsProps> = ({
  notifications,
  onNotificationClick
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  // Group messages by sender
  const groupedBySender = notifications.reduce<Record<string, Notification[]>>(
    (acc, notification) => {
      const sender = notification.metadata?.sender || 'Unknown';
      return {
        ...acc,
        [sender as string]: [...(acc[sender as string] || []), notification]
      };
    },
    {}
  );

  const handleGroupExpand = (sender: string) => {
    setExpandedGroups(prev => {
      const next = new Set(prev);
      if (next.has(sender)) {
        next.delete(sender);
      } else {
        next.add(sender);
      }
      return next;
    });
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="message-notifications">
      {Object.entries(groupedBySender).map(([sender, messages]) => {
        const isExpanded = expandedGroups.has(sender);
        const latestMessage = messages[0];

        return (
          <div key={sender} className="message-group bg-gray-800 rounded-lg mb-2">
            {/* Group Header - Always visible */}
            <div
              onClick={() => handleGroupExpand(sender)}
              className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-white">{sender}</span>
                <span className="text-gray-400 text-sm">
                  {messages.length > 1 ? `${messages.length} messages` : '1 message'}
                </span>
              </div>
              <div className="text-gray-400 text-sm">{formatTime(latestMessage.timestamp)}</div>
            </div>

            {/* Expanded Messages */}
            {isExpanded && (
              <div className="px-3 pb-2">
                {messages.map(notification => (
                  <div
                    key={notification.id}
                    onClick={() => onNotificationClick?.(notification)}
                    className="message-item py-2 px-3 hover:bg-gray-700 rounded cursor-pointer"
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-white">{notification.message}</span>
                      <span className="text-gray-400 text-xs">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
