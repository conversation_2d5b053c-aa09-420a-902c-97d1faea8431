// onNet hm-core:playerLoaded set the positions
onNet('hmcore:playerLoaded', () => {
    console.log('hmcore:playerLoaded event triggered, setting HUD component positions.');
    // Set the position of all HUD components to hide them
    HideHud();
});

// Function to set the position of a all hud components
function HideHud() {
    SetHudComponentPosition(1, -5.0, -5.0);   // Wanted Stars
    SetHudComponentPosition(2, -5.0, -5.0);   // Weapon Icon
    SetHudComponentPosition(3, -5.0, -5.0);   // Cash
    SetHudComponentPosition(4, -5.0, -5.0);   // MP Cash
    SetHudComponentPosition(5, -5.0, -5.0);   // MP Message
    SetHudComponentPosition(6, -5.0, -5.0);   // Vehicle Name
    SetHudComponentPosition(7, -5.0, -5.0);   // Area Name
    SetHudComponentPosition(8, -5.0, -5.0);   // Vehicle Class
    SetHudComponentPosition(9, -5.0, -5.0);   // Street Name
    SetHudComponentPosition(10, -5.0, -5.0);  // Help Text
    SetHudComponentPosition(11, -5.0, -5.0);  // Floating Help Text 1
    SetHudComponentPosition(12, -5.0, -5.0);  // Floating Help Text 2
    SetHudComponentPosition(13, -5.0, -5.0);  // Cash Change
    // SetHudComponentPosition(14, -5.0, -5.0);  // Reticle (commented out in original)
    SetHudComponentPosition(15, -5.0, -5.0);  // Subtitle Text
    // SetHudComponentPosition(16, -5.0, -5.0);  // Radio Stations (commented out in original)
    SetHudComponentPosition(17, -5.0, -5.0);  // Saving Game
    SetHudComponentPosition(18, -5.0, -5.0);  // Game Stream
    SetHudComponentPosition(19, -5.0, -5.0);  // Weapon Wheel
    SetHudComponentPosition(20, -5.0, -5.0);  // Weapon Wheel Stats
    SetHudComponentPosition(21, -5.0, -5.0);  // HUD Components
    SetHudComponentPosition(22, -5.0, -5.0);  // HUD Weapons
}

on('onResourceStart', (resourceName: string) => {
    if (resourceName === GetCurrentResourceName()) {
        console.log('Resource started, hiding HUD components.');
        HideHud();
    }
});