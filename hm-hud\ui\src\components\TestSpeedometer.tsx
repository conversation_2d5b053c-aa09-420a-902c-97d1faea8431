import React from 'react';
import { useHudStore } from '../store/hudStore';

interface TestSpeedometerProps {
  speed?: number;
  fuel?: number;
  engineHealth?: number;
  bodyHealth?: number;
  gear?: number;
  nitrous?: number;
  seatbelt?: boolean;
}

const TestSpeedometer: React.FC<TestSpeedometerProps> = ({ 
  speed: propSpeed, 
  fuel: propFuel,
  engineHealth: propEngineHealth,
  gear: propGear,
  nitrous: propNitrous,
  seatbelt: propSeatbelt,
}) => {
  const { hudData } = useHudStore();
  const pathRef = React.useRef<SVGPathElement>(null);
  const [pathLength, setPathLength] = React.useState(0);
  
  // Use props if provided, otherwise fall back to store data with safe defaults
  const speed = Math.max(0, propSpeed ?? hudData.vehicle.speed ?? 0);
  const fuel = Math.max(0, Math.min(100, propFuel ?? hudData.vehicle.fuel ?? 100));
  const engineHealth = Math.max(0, Math.min(100, propEngineHealth ?? (hudData.vehicle.engineHealth ? hudData.vehicle.engineHealth / 10 : 100)));
  const gear = propGear ?? hudData.vehicle.gear ?? 1;
  const nitrous = Math.max(0, Math.min(100, propNitrous ?? hudData.vehicle.nitrous ?? 0));
  const seatbelt = propSeatbelt ?? hudData.vehicle.seatbelt ?? false;
  const rpmRatio = Math.max(0, Math.min(1, hudData.vehicle.rpmRatio ?? 0)); // RPM ratio (0-1)

  // Speed is already in MPH, so we'll use it directly
  const speedMph = Math.round(speed);
  
  // Calculate needle angle based on RPM ratio instead of speed
  const needleAngle = -135 + (rpmRatio * 270); // 270 degree arc from -135 to +135
  
  // Calculate needle position based on RPM ratio to match arc exactly
  const needleAngleRad = (() => {
    if (pathRef.current) {
      // Use the RPM ratio as the percentage along the arc
      const point = pathRef.current.getPointAtLength(pathLength * rpmRatio);
      // Calculate angle from center to this point
      const dx = point.x - 150;
      const dy = point.y - 150;
      return Math.atan2(dy, dx);
    } else {
      // Fallback calculation using same geometry as main arc
      const startAngleActual = Math.atan2(218.9 - 150, 82.1 - 150);
      const totalArcAngle = (3 * Math.PI) / 2;
      return startAngleActual + (rpmRatio * totalArcAngle);
    }
  })();
  
  // Calculate outer arc path for 0-3 range (30% of the full arc)
  const outerArcPercentage = 0.3; // 30% of the arc (from 0 to 3)
  
  // Calculate dash array for RPM-controlled arc length
  React.useEffect(() => {
    if (pathRef.current) {
      const length = pathRef.current.getTotalLength();
      setPathLength(length);
    }
  }, []);
  
  const visibleLength = pathLength * rpmRatio;
  const hiddenLength = pathLength - visibleLength;
  
  // For browser testing, always show the speedometer
  const isBrowser = typeof window !== 'undefined' && !(window as unknown as { invokeNative?: unknown }).invokeNative;
  
  // Don't render if not in a vehicle or engine is off (except in browser)
  if (!isBrowser && (!hudData.vehicle.isInVehicle || !hudData.vehicle.showSpeedometer)) {
    return null;
  }

  return (
    <div className="relative">
      {/* Step 2: Circular arc with tick marks at each number */}
      <div className="hud-speedometer relative">
        
        {/* Circular arc track using SVG */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 300 300">
          {/* Small tick marks between major marks - UNDER the main arc */}
          {Array.from({ length: 40 }, (_, i) => i + 1).filter(i => i % 4 !== 0).map((tickIndex) => {
            // Use the same arc geometry as the main arc for small ticks
            let arcX = 0, arcY = 0;
            
            if (pathRef.current) {
              const pathLength = pathRef.current.getTotalLength();
              const t = tickIndex / 40; // Parameter from 0 to 1 (40 small divisions)
              const point = pathRef.current.getPointAtLength(t * pathLength);
              arcX = point.x;
              arcY = point.y;
            } else {
              // Fallback calculation using same geometry as main arc
              const centerX = 150;
              const centerY = 150;
              const arcRadius = 105; // Same as main arc
              const startAngleActual = Math.atan2(218.9 - centerY, 82.1 - centerX);
              const totalArcAngle = (3 * Math.PI) / 2;
              const t = tickIndex / 40;
              const currentAngle = startAngleActual + (t * totalArcAngle);
              arcX = centerX + Math.cos(currentAngle) * arcRadius;
              arcY = centerY + Math.sin(currentAngle) * arcRadius;
            }
            
            // Calculate the radial direction from center to this point
            const centerX = 150;
            const centerY = 150;
            const dx = arcX - centerX;
            const dy = arcY - centerY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const unitX = dx / distance;
            const unitY = dy / distance;
            
            // Small tick mark centered on arc (extends both inward and outward)
            const smallTickLength = 4;
            const smallTickHalfLength = smallTickLength / 2;
            const tickStartX = arcX - (unitX * smallTickHalfLength);
            const tickStartY = arcY - (unitY * smallTickHalfLength);
            const tickEndX = arcX + (unitX * smallTickHalfLength);
            const tickEndY = arcY + (unitY * smallTickHalfLength);
            
            return (
              <line
                key={`small-${tickIndex}`}
                x1={tickStartX}
                y1={tickStartY}
                x2={tickEndX}
                y2={tickEndY}
                stroke="rgba(156, 163, 175, 0.5)"
                strokeWidth="2"
                strokeLinecap="round"
              />
            );
          })}

          {/* Main speedometer arc (270 degrees) - solid white with glow */}
          <path
            ref={pathRef}
            id="speedometer-arc"
            d="M 82.1 218.9 A 105 105 0 1 1 217.9 218.9"
            fill="none"
            stroke="rgb(229, 231, 235)"
            strokeWidth="6"
            strokeDasharray={pathLength > 0 ? `${visibleLength} ${hiddenLength}` : undefined}
            strokeDashoffset="0"
            filter="drop-shadow(0 0 8px rgba(229, 231, 235, 0.6))"
          />
          
          {/* Tick marks using exact path positions */}
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((number) => {
            // Use the actual SVG path to get exact positions
            let arcX = 0, arcY = 0;
            
            if (pathRef.current) {
              const pathLength = pathRef.current.getTotalLength();
              const t = number / 10; // Parameter from 0 to 1
              const point = pathRef.current.getPointAtLength(t * pathLength);
              arcX = point.x;
              arcY = point.y;
            } else {
              // Fallback calculation if ref not available yet
              const centerX = 150;
              const centerY = 150;
              const arcRadius = 105; // Updated to match new arc radius
              const startAngleActual = Math.atan2(218.9 - centerY, 82.1 - centerX);
              const totalArcAngle = (3 * Math.PI) / 2;
              const t = number / 10;
              const currentAngle = startAngleActual + (t * totalArcAngle);
              arcX = centerX + Math.cos(currentAngle) * arcRadius;
              arcY = centerY + Math.sin(currentAngle) * arcRadius;
            }
            
            // Calculate the radial direction from center to this point
            const centerX = 150;
            const centerY = 150;
            const dx = arcX - centerX;
            const dy = arcY - centerY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const unitX = dx / distance;
            const unitY = dy / distance;
            
            // Big tick mark centered on arc (extends both inward and outward)
            const tickLength = 8;
            const tickHalfLength = tickLength / 2;
            const tickStartX = arcX - (unitX * tickHalfLength);
            const tickStartY = arcY - (unitY * tickHalfLength);
            const tickEndX = arcX + (unitX * tickHalfLength);
            const tickEndY = arcY + (unitY * tickHalfLength);
            
            // Number label position (farther from the arc - outward)
            const numberDistance = 18;
            const numberX = arcX - (unitX * numberDistance);
            const numberY = arcY - (unitY * numberDistance);
            
            return (
              <g key={number}>
                {/* Tick mark line - solid white with subtle shadow */}
                <line
                  x1={tickStartX}
                  y1={tickStartY}
                  x2={tickEndX}
                  y2={tickEndY}
                  stroke="rgb(229, 231, 235)"
                  strokeWidth="4"
                  strokeLinecap="round"
                  filter="drop-shadow(0 0 3px rgba(0, 0, 0, 0.5))"
                />
                
                {/* Number label */}
                <text
                  x={numberX}
                  y={numberY}
                  fill="white"
                  fontSize="14"
                  fontWeight="900"
                  fontFamily="'Nunito', 'Quicksand', 'Varela Round', 'Rubik', 'Open Sans', sans-serif"
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {number}
                </text>
              </g>
            );
          })}
          
          {/* Outer arc that goes from 0 to 3 (30% of the main arc) - LARGER radius, positioned outside */}
          {(() => {
            // Calculate start point (0 position) at radius 125
            let startX = 69.1, startY = 245.3; // fallback
            let endX = 43.9, endY = 84.0; // fallback
            let iconX = 69.1, iconY = 245.3; // fallback for engine icon
            
            if (pathRef.current) {
              const pathLength = pathRef.current.getTotalLength();
              const offsetDistance = 15; // Increased offset from main arc
              
              // Get start point (0 position)
              const startPoint = pathRef.current.getPointAtLength(0);
              const startDx = startPoint.x - 150;
              const startDy = startPoint.y - 150;
              const startDistance = Math.sqrt(startDx * startDx + startDy * startDy);
              const startUnitX = startDx / startDistance;
              const startUnitY = startDy / startDistance;
              startX = startPoint.x + startUnitX * offsetDistance;
              startY = startPoint.y + startUnitY * offsetDistance;
              
              // Get end point (3 position - 30% along the path)
              const endPoint = pathRef.current.getPointAtLength(pathLength * 0.3);
              const endDx = endPoint.x - 150;
              const endDy = endPoint.y - 150;
              const endDistance = Math.sqrt(endDx * endDx + endDy * endDy);
              const endUnitX = endDx / endDistance;
              const endUnitY = endDy / endDistance;
              endX = endPoint.x + endUnitX * offsetDistance;
              endY = endPoint.y + endUnitY * offsetDistance;
              
              // Calculate engine icon position (offset backward from start)
              // We want to position it at -2% from the start, which means we need to 
              // calculate a position that's 2% of the arc length before the start point
              const backwardOffsetDistance = pathLength * 0.02; // 2% of arc length
              
              // Get a point slightly forward from start to calculate the direction
              const forwardPoint = pathRef.current.getPointAtLength(pathLength * 0.01);
              const iconStartPoint = pathRef.current.getPointAtLength(0);
              
              // Calculate the direction vector from forward point to start point (backward direction)
              const directionX = iconStartPoint.x - forwardPoint.x;
              const directionY = iconStartPoint.y - forwardPoint.y;
              const directionLength = Math.sqrt(directionX * directionX + directionY * directionY);
              const unitDirectionX = directionX / directionLength;
              const unitDirectionY = directionY / directionLength;
              
              // Position the icon backward from the start point along the arc direction
              const iconArcX = iconStartPoint.x + unitDirectionX * backwardOffsetDistance;
              const iconArcY = iconStartPoint.y + unitDirectionY * backwardOffsetDistance;
              
              // Now offset outward from this arc position
              const iconDx = iconArcX - 150;
              const iconDy = iconArcY - 150;
              const iconDistance = Math.sqrt(iconDx * iconDx + iconDy * iconDy);
              const iconUnitX = iconDx / iconDistance;
              const iconUnitY = iconDy / iconDistance;
              iconX = iconArcX + iconUnitX * (offsetDistance + 8); // Slightly further out
              iconY = iconArcY + iconUnitY * (offsetDistance + 8);
            }
            
            return (
              <g>
                {/* Outer arc - white to match main arc */}
                <path
                  id="outer-arc"
                  d={`M ${startX.toFixed(1)} ${startY.toFixed(1)} A 115 115 0 0 1 ${endX.toFixed(1)} ${endY.toFixed(1)}`}
                  fill="none"
                  stroke="rgb(229, 231, 235)"
                  strokeWidth="6"
                  filter="drop-shadow(0 0 8px rgba(229, 231, 235, 0.6))"
                />
                
                {/* Engine icon offset backward from the start of the arc */}
                <g transform={`translate(${iconX}, ${iconY})`}>
                  {/* Placeholder for positioning - we'll use HTML icon instead */}
                </g>
              </g>
            );
          })()}
          
          {/* Speedometer needle */}
          <g>
            {/* Define gradient for needle - faded at base, solid at tip */}
            <defs>
              <linearGradient id="needleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="rgba(239, 68, 68, 0.2)" />
                <stop offset="30%" stopColor="rgba(239, 68, 68, 0.5)" />
                <stop offset="100%" stopColor="rgba(239, 68, 68, 0.9)" />
              </linearGradient>
            </defs>
            
            {/* Needle line - faded at base, solid at tip */}
            <line
              x1={150}
              y1={150}
              x2={150 + Math.cos(needleAngleRad) * 115}
              y2={150 + Math.sin(needleAngleRad) * 115}
              stroke="url(#needleGradient)"
              strokeWidth="2"
              strokeLinecap="round"
              filter="drop-shadow(0 0 3px rgba(239, 68, 68, 0.5))"
            />
          </g>
        </svg>
        
        {/* FontAwesome wrench icon positioned outside SVG */}
        {(() => {
          // Calculate the same position as the SVG icon
          let iconX = 69.1, iconY = 245.3; // fallback for engine icon
          
          if (pathRef.current) {
            const pathLength = pathRef.current.getTotalLength();
            const offsetDistance = 15; // Same offset as outer arc
            
            // Calculate engine icon position (offset backward from start)
            const backwardOffsetDistance = pathLength * 0.01; // 1% of arc length
            
            // Get a point slightly forward from start to calculate the direction
            const forwardPoint = pathRef.current.getPointAtLength(pathLength * 0.01);
            const iconStartPoint = pathRef.current.getPointAtLength(0);
            
            // Calculate the direction vector from forward point to start point (backward direction)
            const directionX = iconStartPoint.x - forwardPoint.x;
            const directionY = iconStartPoint.y - forwardPoint.y;
            const directionLength = Math.sqrt(directionX * directionX + directionY * directionY);
            const unitDirectionX = directionX / directionLength;
            const unitDirectionY = directionY / directionLength;
            
            // Position the icon backward from the start point along the arc direction
            const iconArcX = iconStartPoint.x + unitDirectionX * backwardOffsetDistance;
            const iconArcY = iconStartPoint.y + unitDirectionY * backwardOffsetDistance;
            
            // Now offset outward from this arc position
            const iconDx = iconArcX - 150;
            const iconDy = iconArcY - 150;
            const iconDistance = Math.sqrt(iconDx * iconDx + iconDy * iconDy);
            const iconUnitX = iconDx / iconDistance;
            const iconUnitY = iconDy / iconDistance;
            iconX = iconArcX + iconUnitX * (offsetDistance + 8); // Slightly further out
            iconY = iconArcY + iconUnitY * (offsetDistance + 8);
          }
          
          // Convert SVG coordinates to CSS coordinates (relative to the SVG container)
          const containerSize = 384; // w-96 = 384px
          const viewBoxSize = 300;
          const scale = containerSize / viewBoxSize;
          const cssX = iconX * scale;
          const cssY = iconY * scale;
          
          return (
            <i 
              className="fas fa-wrench absolute text-white text-sm drop-shadow-lg"
              style={{
                left: `${cssX}px`,
                top: `${cssY}px`,
                transform: 'translate(-50%, -50%)',
                filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.4))'
              }}
            />
          );
        })()}
        
        {/* Central speed display */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-center">
            <div className="hud-text-3xl text-white leading-none" style={{ fontFamily: '"Nunito", "Quicksand", "Varela Round", "Rubik", "Open Sans", sans-serif', fontWeight: '900' }}>
              {Math.round(speed)}
            </div>
            <div className="hud-text-base text-gray-300 mt-0.5" style={{ fontFamily: '"Nunito", "Quicksand", "Varela Round", "Rubik", "Open Sans", sans-serif', fontWeight: '700', letterSpacing: '0.1em' }}>
              MPH
            </div>
            {/* Gear display - simple number only, positioned lower and bigger */}
            <div className="text-white hud-text-4xl font-bold mt-2">
              {gear === 0 ? 'R' : gear}
            </div>
          </div>
        </div>
        
        {/* Nitrous bar - aligned with main arc start and end */}
        {(() => {
          // Calculate the nitrous bar position to align with arc start and end
          let barLeft = 82.1; // fallback to arc start X
          let barRight = 217.9; // fallback to arc end X
          let barY = 218.9; // same height as arc endpoints
          
          if (pathRef.current) {
            // Get start and end points of the main arc
            const pathLength = pathRef.current.getTotalLength();
            const startPoint = pathRef.current.getPointAtLength(0);
            const endPoint = pathRef.current.getPointAtLength(pathLength);
            
            barLeft = startPoint.x;
            barRight = endPoint.x;
            barY = startPoint.y; // Use the same Y position as the arc endpoints
          }
          
          // Convert SVG coordinates to CSS coordinates
          const containerSize = 384; // w-96 = 384px
          const viewBoxSize = 300;
          const scale = containerSize / viewBoxSize;
          const cssLeft = barLeft * scale;
          const cssRight = barRight * scale;
          const cssY = barY * scale;
          const barWidth = cssRight - cssLeft;
          
          // Add more padding to make the bar smaller
          const padding = 30; // 30px padding on each side (increased from 20px)
          
          return (
            <div 
              className="absolute"
              style={{
                left: `${cssLeft + padding}px`,
                top: `${cssY}px`,
                width: `${barWidth - (padding * 2)}px`
              }}
            >
              <div className="relative h-2">
                {/* Background track */}
                <div className="absolute inset-0 bg-gray-700 bg-opacity-50 rounded-full backdrop-blur-sm"></div>
                
                {/* Nitrous fill - blue with reduced glow */}
                <div 
                  className="absolute inset-0 bg-blue-500 rounded-full transition-all duration-300 ease-out"
                  style={{
                    width: `${nitrous}%`,
                    boxShadow: '0 0 4px rgba(59, 130, 246, 0.6)',
                    filter: 'drop-shadow(0 0 2px rgba(59, 130, 246, 0.4))'
                  }}
                ></div>
                
                {/* Nitrous bottle icon centered under the bar */}
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                  <i 
                    className="fas fa-fire-extinguisher text-blue-400 text-sm"
                    style={{
                      filter: 'drop-shadow(0 0 2px rgba(59, 130, 246, 0.3))'
                    }}
                  />
                </div>
              </div>
            </div>
          );
        })()}
      </div>
    </div>
  );
};

export default TestSpeedometer;
