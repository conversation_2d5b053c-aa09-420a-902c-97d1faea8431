/**
 * NUI utility functions for communicating with the FiveM client
 */

import { isBrowser } from './environment';

/**
 * Interface for NUI message data
 */
interface NuiMessageData {
  type: string;
  [key: string]: any;
}

/**
 * Send a message to the FiveM client
 * @param event Event name
 * @param data Event data
 */
export const sendNuiMessage = (event: string, data: any = {}): void => {
  if (isBrowser()) {
    console.log('NUI Message (Browser):', { event, data });
    return;
  }

  fetch(`https://${(window as any).GetParentResourceName()}/${event}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: JSON.stringify(data),
  })
    .then(response => response.json())
    .then(response => {
      if (response.error) {
        console.error('NUI Error:', response.error);
      }
    })
    .catch(error => {
      console.error('NUI Error:', error);
    });
};

/**
 * Register a callback for all NUI messages
 * @param callback Callback function
 * @returns Cleanup function to remove the event listener
 */
export const registerNuiListener = (callback: (type: string, data: any) => void): () => void => {
  const eventListener = (event: MessageEvent<NuiMessageData>) => {    // Check if event.data exists and has the expected structure
    if (!event.data || typeof event.data !== 'object') {
      console.error('UI: Invalid message format received:', event);
      return;
    }
    const { type, ...data } = event.data;
    if (!type) {
      console.error('UI: Message received without type:', event.data);
      return;
    }
    callback(type, data);
  };

  window.addEventListener('message', eventListener);

  // Return a cleanup function
  return () => {
    console.log('UI: Removing NUI listener');
    window.removeEventListener('message', eventListener);
  };
};

/**
 * Send a ready message to the FiveM client
 */
export const sendReadyMessage = (): void => {
  sendNuiMessage('hm-multicharacter:nui:ready');
};

/**
 * Close the NUI interface
 */
export const closeNui = (): void => {
  sendNuiMessage('hm-multicharacter:nui:close');
};

/**
 * Select a character
 * @param characterId Character ID
 */
export const selectCharacter = (characterId: number): void => {
  sendNuiMessage('hm-multicharacter:nui:selectCharacter', { characterId });
};

/**
 * Create a character
 * @param characterData Character data
 */
export const createCharacter = (characterData: any): void => {
  sendNuiMessage('hm-multicharacter:nui:createCharacter', { characterData });
};

/**
 * Delete a character
 * @param characterId Character ID
 */
export const deleteCharacter = (characterId: number): void => {
  sendNuiMessage('hm-multicharacter:nui:deleteCharacter', { characterId });
};