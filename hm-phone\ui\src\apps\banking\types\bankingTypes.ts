export interface Transaction {
  id: number;
  merchantName: string;
  amount: number;
  timestamp: string;
  type: 'debit' | 'credit';
  icon?: string;
  description?: string;
  accountId: number;
}

export interface Account {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit';
  number: string;
  balance: number;
  currency: string;
  color: string;
  icon: string;
}

export interface BankingState {
  accounts: Account[];
  selectedAccountId: number;
  transactions: Transaction[];
  isLoading: boolean;

  // Actions
  setSelectedAccount: (accountId: number) => void;
  addTransaction: (transaction: Transaction) => void;
  transferBetweenAccounts: (fromId: number, toId: number, amount: number) => void;
  getAccountBalance: (accountId: number) => number;
  getAccountTransactions: (accountId: number) => Transaction[];
}
