/**
 * HM-Admin Shared Commands
 * This file contains command definitions shared between client and server
 */

// Admin command categories
export enum CommandCategory {
  PLAYER = 'player',
  VEHICLE = 'vehicle',
  WORLD = 'world',
  SERVER = 'server',
  UTILITY = 'utility',
  FUN = 'fun',
  DEVELOPMENT = 'development',
}

// Admin command definitions
export interface AdminCommandDefinition {
  name: string;
  description: string;
  category: CommandCategory;
  usage: string;
  requiresTarget?: boolean;
  aliases?: string[];
}

// Define all admin commands
export const AdminCommands: { [key: string]: AdminCommandDefinition } = {
  // Player commands
  KILL_PLAYER: {
    name: 'kill',
    description: 'Kill a player',
    category: CommandCategory.PLAYER,
    usage: '/kill [id]',
    requiresTarget: true,
  },
  REVIVE_PLAYER: {
    name: 'revive',
    description: 'Revive a player',
    category: CommandCategory.PLAYER,
    usage: '/revive [id]',
    requiresTarget: true,
  },
  TELEPORT_TO_PLAYER: {
    name: 'goto',
    description: 'Teleport to a player',
    category: CommandCategory.PLAYER,
    usage: '/goto [id]',
    requiresTarget: true,
  },
  TELEPORT_PLAYER_TO_ME: {
    name: 'bring',
    description: 'Teleport a player to you',
    category: CommandCategory.PLAYER,
    usage: '/bring [id]',
    requiresTarget: true,
  },
  TELEPORT_TO_COORDS: {
    name: 'tpcoords',
    description: 'Teleport to coordinates',
    category: CommandCategory.PLAYER,
    usage: '/tpcoords [x] [y] [z]',
  },
  TELEPORT_TO_MARKER: {
    name: 'tpm',
    description: 'Teleport to waypoint marker',
    category: CommandCategory.PLAYER,
    usage: '/tpm',
  },
  SPECTATE_PLAYER: {
    name: 'spectate',
    description: 'Spectate a player',
    category: CommandCategory.PLAYER,
    usage: '/spectate [id]',
    requiresTarget: true,
  },
  FREEZE_PLAYER: {
    name: 'freeze',
    description: 'Freeze a player',
    category: CommandCategory.PLAYER,
    usage: '/freeze [id]',
    requiresTarget: true,
  },
  UNFREEZE_PLAYER: {
    name: 'unfreeze',
    description: 'Unfreeze a player',
    category: CommandCategory.PLAYER,
    usage: '/unfreeze [id]',
    requiresTarget: true,
    aliases: ['thaw'],
  },
  KICK_PLAYER: {
    name: 'kick',
    description: 'Kick a player from the server',
    category: CommandCategory.PLAYER,
    usage: '/kick [id] [reason]',
    requiresTarget: true,
  },
  BAN_PLAYER: {
    name: 'ban',
    description: 'Ban a player from the server',
    category: CommandCategory.PLAYER,
    usage: '/ban [id] [duration] [reason]',
    requiresTarget: true,
  },
  UNBAN_PLAYER: {
    name: 'unban',
    description: 'Unban a player from the server',
    category: CommandCategory.PLAYER,
    usage: '/unban [identifier]',
  },
  WARN_PLAYER: {
    name: 'warn',
    description: 'Warn a player',
    category: CommandCategory.PLAYER,
    usage: '/warn [id] [reason]',
    requiresTarget: true,
  },

  // Vehicle commands
  SPAWN_VEHICLE: {
    name: 'car',
    description: 'Spawn a vehicle',
    category: CommandCategory.VEHICLE,
    usage: '/car [model]',
    aliases: ['vehicle'],
  },
  DELETE_VEHICLE: {
    name: 'dv',
    description: 'Delete a vehicle',
    category: CommandCategory.VEHICLE,
    usage: '/dv',
    aliases: ['deletevehicle'],
  },
  REPAIR_VEHICLE: {
    name: 'fix',
    description: 'Repair a vehicle',
    category: CommandCategory.VEHICLE,
    usage: '/fix',
    aliases: ['repair'],
  },
  CLEAN_VEHICLE: {
    name: 'clean',
    description: 'Clean a vehicle',
    category: CommandCategory.VEHICLE,
    usage: '/clean',
    aliases: ['wash'],
  },
  BURST_TIRE: {
    name: 'bursttire',
    description: 'Burst a specific tire on a vehicle',
    category: CommandCategory.FUN,
    usage: '/bursttire [id] [tire index 0-5]',
    requiresTarget: true,
  },

  // Server commands
  ANNOUNCE: {
    name: 'announce',
    description: 'Make a server-wide announcement',
    category: CommandCategory.SERVER,
    usage: '/announce [message]',
  },
  WEATHER: {
    name: 'weather',
    description: 'Change the weather',
    category: CommandCategory.SERVER,
    usage: '/weather [type]',
  },
  TIME: {
    name: 'time',
    description: 'Change the time',
    category: CommandCategory.SERVER,
    usage: '/time [hour] [minute]',
  },

  // Utility commands
  NOCLIP: {
    name: 'noclip',
    description: 'Toggle noclip mode',
    category: CommandCategory.UTILITY,
    usage: '/noclip',
  },
  GOD_MODE: {
    name: 'god',
    description: 'Toggle god mode',
    category: CommandCategory.UTILITY,
    usage: '/god',
    aliases: ['godmode'],
  },
  INVISIBLE: {
    name: 'invisible',
    description: 'Toggle invisibility',
    category: CommandCategory.UTILITY,
    usage: '/invisible',
    aliases: ['invis'],
  },
  CLEAR_AREA: {
    name: 'cleararea',
    description: 'Clear all entities in an area',
    category: CommandCategory.UTILITY,
    usage: '/cleararea [radius]',
  },

  // Fun commands
  RAGDOLL_PLAYER: {
    name: 'ragdoll',
    description: 'Make a player ragdoll',
    category: CommandCategory.FUN,
    usage: '/ragdoll [id] [duration]',
    requiresTarget: true,
  },
  SET_ON_FIRE: {
    name: 'fire',
    description: 'Set a player on fire',
    category: CommandCategory.FUN,
    usage: '/fire [id] [duration]',
    requiresTarget: true,
  },
  LAUNCH_PLAYER: {
    name: 'launch',
    description: 'Launch a player into the air',
    category: CommandCategory.FUN,
    usage: '/launch [id] [force]',
    requiresTarget: true,
  },

  // Development commands
  COORDS: {
    name: 'coords',
    description: 'Get your current coordinates',
    category: CommandCategory.DEVELOPMENT,
    usage: '/coords',
  },
  HEADING: {
    name: 'heading',
    description: 'Get your current heading',
    category: CommandCategory.DEVELOPMENT,
    usage: '/heading',
  },
  ENTITY_INFO: {
    name: 'entinfo',
    description: 'Get information about the entity you are looking at',
    category: CommandCategory.DEVELOPMENT,
    usage: '/entinfo',
  },
};

// Get command by name
export function getCommandByName(name: string): AdminCommandDefinition | undefined {
  return Object.values(AdminCommands).find(cmd =>
    cmd.name === name || (cmd.aliases && cmd.aliases.includes(name))
  );
}

// Get commands by category
export function getCommandsByCategory(category: CommandCategory): AdminCommandDefinition[] {
  return Object.values(AdminCommands).filter(cmd => cmd.category === category);
}

// Commands module initialization is handled in shared/main.ts