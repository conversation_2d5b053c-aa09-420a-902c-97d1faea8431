import React, { useState } from 'react';
import { useNavigation } from '../../../navigation/hooks';
import { useBankingStore } from '../stores/bankingStore';

interface TransferViewProps {
  mode: 'send' | 'request' | 'transfer';
}

const TransferView: React.FC<TransferViewProps> = ({ mode }) => {
  const { openView } = useNavigation();
  const { accounts, selectedAccountId } = useBankingStore();
  const [amount, setAmount] = useState('');
  const [recipient, setRecipient] = useState('');
  const [note, setNote] = useState('');

  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);

  const handleSubmit = () => {
    // Here you would implement the actual transfer logic
    console.log(`${mode} ${amount} to ${recipient} with note: ${note}`);

    // Return to main view after submission
    openView('main');
  };

  const getTitle = () => {
    switch (mode) {
      case 'send':
        return 'Send Money';
      case 'request':
        return 'Request Money';
      case 'transfer':
        return 'Transfer Between Accounts';
      default:
        return 'Money Transfer';
    }
  };

  return (
    <div className="flex-1 overflow-auto flex flex-col">
      {/* Header */}
      <div className="flex items-center px-4 py-3 border-b border-white/10">
        <button onClick={() => openView('main')} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left text-lg"></i>
        </button>
        <h1 className="text-white font-medium ml-3">{getTitle()}</h1>
      </div>

      {/* Form */}
      <div className="p-4 space-y-4">
        {/* From Account */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-white/60 text-xs mb-1">From Account</div>
          <div className="flex items-center justify-between">
            <div className="text-white">{selectedAccount?.name}</div>
            <div className="text-white font-medium">
              {selectedAccount?.balance.toLocaleString('en-US', {
                style: 'currency',
                currency: selectedAccount?.currency || 'USD'
              })}
            </div>
          </div>
        </div>

        {/* To Account/Recipient */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-white/60 text-xs mb-1">
            {mode === 'transfer' ? 'To Account' : 'Recipient'}
          </div>
          <input
            type="text"
            value={recipient}
            onChange={e => setRecipient(e.target.value)}
            placeholder={mode === 'transfer' ? 'Select account...' : 'Enter recipient...'}
            className="w-full bg-transparent text-white border-b border-white/20 pb-1 focus:outline-none focus:border-white/40"
          />
        </div>

        {/* Amount */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-white/60 text-xs mb-1">Amount</div>
          <div className="flex items-center">
            <span className="text-white mr-2">$</span>
            <input
              type="text"
              value={amount}
              onChange={e => {
                // Only allow numbers and decimal point
                const value = e.target.value.replace(/[^0-9.]/g, '');
                setAmount(value);
              }}
              placeholder="0.00"
              className="w-full bg-transparent text-white text-xl border-b border-white/20 pb-1 focus:outline-none focus:border-white/40"
            />
          </div>
        </div>

        {/* Note */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-white/60 text-xs mb-1">Note (Optional)</div>
          <input
            type="text"
            value={note}
            onChange={e => setNote(e.target.value)}
            placeholder="What's this for?"
            className="w-full bg-transparent text-white border-b border-white/20 pb-1 focus:outline-none focus:border-white/40"
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="mt-auto p-4">
        <button
          onClick={handleSubmit}
          disabled={!amount || !recipient}
          className={`w-full py-3 rounded-lg font-medium ${
            amount && recipient
              ? 'bg-blue-500 text-white hover:bg-blue-600'
              : 'bg-white/10 text-white/50 cursor-not-allowed'
          }`}
        >
          {mode === 'send' ? 'Send Money' : mode === 'request' ? 'Request Money' : 'Transfer Funds'}
        </button>
      </div>
    </div>
  );
};

export default TransferView;
