{"name": "hm-framework", "version": "1.0.0", "description": "HM Framework - Complete FiveM resource collection", "main": "build-all.js", "engines": {"node": "22.16.0"}, "scripts": {"build": "node build-all.js", "build:parallel": "node build-all.js --parallel", "build:verbose": "node build-all.js --verbose", "build:fast": "node build-all.js --parallel --skip-install", "build:help": "node build-all.js --help", "install:all": "node install-all.js", "clean": "node clean-all.js"}, "keywords": ["fivem", "framework", "typescript", "lua", "gaming"], "author": "HM Framework Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}