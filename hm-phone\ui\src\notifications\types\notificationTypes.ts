export interface NotificationAction {
  label: string;
  onPress: () => void;
  icon?: string;
  primary?: boolean;
  dismissOnClick?: boolean;
}

export interface NotificationMetadata {
  navigateTo?: string;
  openApp?: boolean;
  deepLink?: string;
  conversationId?: string | number;
  timestamp?: number;
  avatar?: string;
  [key: string]: string | number | boolean | Record<string, unknown> | undefined;
}

export interface Notification {
  id: number;
  appId: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'message';
  timestamp: number;
  actions?: NotificationAction[];
  metadata?: NotificationMetadata;
  read: boolean;
  duration?: number | null; // null means persistent
  persistent?: boolean;
  movedToTopBar?: boolean;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  groupId?: string;
  image?: string;
  silent?: boolean;
  expandable?: boolean;
  count?: number;
  category?: 'message' | 'alert' | 'update' | 'promotion' | 'system';
  // Added for Android-style notification grouping
  isGroupSummary?: boolean;
  // App icon for the notification
  icon?: string;
  // Deep linking to specific app views
  deepLink?: {
    app: string;
    view: string;
    params?: Record<string, any>;
  };
  interaction?: {
    quickReply?: string[];
    inputs?: {
      type: 'text' | 'number' | 'select';
      placeholder?: string;
      options?: string[];
    }[];
  };
  childNotifications?: Notification[];
}

export interface NotificationSettings {
  // Per app settings
  [appId: number]: {
    enabled: boolean;
    sound: boolean;
    priority: 'urgent' | 'high' | 'normal' | 'low';
    quietHours: {
      enabled: boolean;
      start: string; // HH:mm
      end: string; // HH:mm
    };
    categories: {
      [category: string]: boolean;
    };
    grouping: {
      enabled: boolean;
      maxGroupSize: number;
      collapseThreshold: number; // Number of notifications before collapsing into a group
    };
    retention: {
      duration: number; // in hours
      maxCount: number;
    };
  };
}

export interface GlobalNotificationSettings {
  doNotDisturb: boolean;
  doNotDisturbSchedule: {
    enabled: boolean;
    start: string;
    end: string;
  };
  batteryOptimization: boolean;
  defaultSound: string;
  showPreview: boolean;
  screenWakeup: boolean;
  groupSimilarNotifications: boolean;
  notificationSoundVolume: number; // 0-100
}

export type NotificationType = 'default' | 'message' | 'call' | 'photo' | 'custom';

export interface NotificationTemplate {
  layout: 'basic' | 'avatar' | 'media' | 'action';
  icon?: string;
  colors: {
    accent: string;
    background: string;
    text: string;
  };
  actions?: {
    primary?: {
      label: string;
      icon?: string;
    };
    secondary?: {
      label: string;
      icon?: string;
    };
  };
}

export interface NotificationData {
  id: number;
  appId: number;
  title: string;
  message: string;
  timestamp: number;
  type: 'message' | 'info' | 'success' | 'warning' | 'error' | 'default';
  read: boolean;
  movedToTopBar: boolean;
  count?: number;
  expanded?: boolean;
  // App icon for the notification
  icon?: string;
  // Added for Android-style notification grouping
  isGroupSummary?: boolean;
  groupId?: string;
  // Auto-dismiss settings
  duration?: number;
  persistent?: boolean;
  // Deep linking to specific app views
  deepLink?: {
    app: string;
    view: string;
    params?: Record<string, any>;
  };
  metadata?: {
    sender?: string;
    senderPhone?: string;
    conversationId?: number | string;
    messageType?: string;
    avatar?: string;
    isOnline?: boolean;
    media?: string;
    isGroup?: boolean;
    [key: string]: unknown;
  };
  actions?: {
    primary?: () => void;
    secondary?: () => void;
  };
}
