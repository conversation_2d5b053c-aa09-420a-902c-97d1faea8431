/**
 * Utility functions for handling weapon mod icons
 */

/**
 * Get the correct icon path for a weapon mod
 * @param iconPath - The icon path from the server
 * @returns The correct path to use in the UI
 */
export const getModIconPath = (iconPath: string | undefined): string => {
  if (!iconPath) {
    return ''; // Will fallback to default icon
  }
  
  // If it's a FontAwesome icon, return as-is
  if (iconPath.startsWith('fa-')) {
    return iconPath;
  }
  
  // If it's already a full URL or absolute path, return as-is
  if (iconPath.startsWith('http') || iconPath.startsWith('/')) {
    return iconPath;
  }
  
  // For PNG files, we need to reference them from the local public directory
  // The inventory icons should be copied to the weapons UI public directory
  // or served from a shared asset directory
  if (iconPath.endsWith('.png') || iconPath.endsWith('.jpg') || iconPath.endsWith('.jpeg')) {
    // For now, try the local assets first, then fallback to NUI callback
    return `./${iconPath}`;
  }
  
  // For any other case, return as-is
  return iconPath;
};

/**
 * Check if an icon is a FontAwesome icon
 * @param iconPath - The icon path
 * @returns True if it's a FontAwesome icon
 */
export const isFontAwesomeIcon = (iconPath: string | undefined): boolean => {
  return Boolean(iconPath && iconPath.startsWith('fa-'));
};
