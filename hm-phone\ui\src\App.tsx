import React from 'react';
import { useModalStore } from './common/stores/modalStore';
import ContactShareModal from './apps/contacts/components/ContactShareModal';

// Import the AppRoutes component
import AppRoutes from './apps/AppRoutes';

// Import the TypeScriptTest component

const App: React.FC = () => {
  const { isOpen, modalType, modalProps } = useModalStore();

  // Render modals based on modalType
  const renderModal = () => {
    if (!isOpen) return null;

    switch (modalType) {
      case 'contactShareRequest':
        return <ContactShareModal {...modalProps} />;
      default:
        return null;
    }
  };

  return (
    <>
      <AppRoutes />
      {renderModal()}
    </>
  );
};

export default App;
