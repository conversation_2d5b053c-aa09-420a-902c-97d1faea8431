import React from 'react';
import { motion } from 'framer-motion';
import { NotificationData } from '../types/notificationTypes';
import { useNavigationStore } from '../../navigation/navigationStore';
import { getAppIconAndColor, formatTimeAgo } from '../utils/notificationUtils';

interface NotificationRendererProps {
  notification: NotificationData;
  onClose: (notification: NotificationData) => void;
  onClick?: () => void;
  className?: string;
  isChild?: boolean;
  isTopBar?: boolean;
  isSummary?: boolean;
  isExpandable?: boolean;
  isExpanded?: boolean;
}

// Using formatTimeAgo from notificationUtils.ts

// Helper functions to get app icon and color
const getAppIcon = (appId: number): string => {
  // Use custom icon if provided in notification, otherwise get from app config
  const { icon } = getAppIconAndColor(appId);
  return icon;
};

const getAppIconColor = (appId: number): string => {
  // Get color from app config and convert to CSS color
  const { color } = getAppIconAndColor(appId);

  // Map color names to CSS colors
  const colorMap: Record<string, string> = {
    'green': '#10B981',
    'purple': '#8B5CF6',
    'gray': '#6B7280',
    'orange': '#F97316',
    'indigo': '#6366F1',
    'cyan': '#06B6D4',
    'yellow': '#FBBF24',
    'red': '#EF4444',
    'blue': '#3B82F6',
    'pink': '#EC4899',
    'teal': '#14B8A6'
  };

  return colorMap[color] || '#6B7280'; // Default to gray if color not found
};

export const NotificationRenderer: React.FC<NotificationRendererProps> = ({
  notification,
  onClick,
  onClose,
  className = '',
  isChild = false,
  isTopBar = false,
  isSummary = false,
  isExpandable = false,
  isExpanded = false
}) => {
  const isMessage = notification.type === 'message';
  const { openAppView } = useNavigationStore();

  // Handle notification click with deep linking
  const handleNotificationClick = () => {
    // If there's a custom click handler, use it
    if (onClick) {
      onClick();
      return;
    }

    // If there's a deep link, navigate to the specified app and view
    if (notification.deepLink) {
      const { app, view, params } = notification.deepLink;
      openAppView(app, view, params || {});

      // Mark notification as read
      if (onClose) {
        onClose(notification);
      }
    }
  };

  const containerClasses = isTopBar ? 'bg-transparent' : 'bg-[#2A2A2A] rounded-xl';

  return (
    <motion.div
      className={`${className} ${isChild ? 'pl-12' : ''}`}
      onClick={handleNotificationClick}
      initial={isTopBar ? {} : { opacity: 0, y: -10 }}
      animate={isTopBar ? {} : { opacity: 1, y: 0 }}
      exit={isTopBar ? {} : { opacity: 0, y: -10 }}
    >
      <div
        className={`flex items-start p-3 ${containerClasses} ${(onClick || notification.deepLink) ? 'cursor-pointer' : ''}`}
      >
        {/* Avatar/Icon Section */}
        {isMessage ? (
          <div
            className={`relative ${
              isTopBar ? 'w-8 h-8' : 'w-10 h-10'
            } rounded-full mr-3 overflow-hidden`}
          >
            {notification.metadata?.avatar ? (
              <img
                src={notification.metadata.avatar}
                alt=""
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-blue-500 flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {notification.metadata?.sender?.[0] || '?'}
                </span>
              </div>
            )}
            {notification.metadata?.isOnline && !isTopBar && (
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-blue-500 rounded-full border-2 border-[#2A2A2A]" />
            )}
          </div>
        ) : (
          <div
            className={`${
              isTopBar ? 'w-8 h-8' : 'w-10 h-10'
            } rounded-full flex items-center justify-center mr-3`}
            style={{
              backgroundColor: getAppIconColor(notification.appId)
            }}
          >
            <i
              className={`fas fa-${getAppIcon(notification.appId)} ${
                isTopBar ? 'text-sm' : ''
              } text-white/80`}
            />
          </div>
        )}

        {/* Content Section */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-0.5">
            <span
              className={`text-white font-medium ${
                isTopBar ? 'text-xs' : 'text-sm'
              } truncate flex items-center`}
            >
              {/* For message notifications in topbar, don't show sender name to avoid redundancy */}
              {isMessage && isTopBar && !isSummary
                ? ''
                : isMessage
                ? notification.metadata?.sender
                : notification.title}

              {/* Show count badge for summary notifications */}
              {isSummary && notification.count && notification.count > 1 && (
                <span className="ml-1.5 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                  {notification.count}
                </span>
              )}
            </span>
            <div className="flex items-center space-x-2">
              <span className="text-white/50 text-xs">{formatTimeAgo(notification.timestamp)}</span>

              {/* Expandable indicator */}
              {isExpandable && (
                <button
                  className="text-white/50 hover:text-white"
                  onClick={e => {
                    e.stopPropagation();
                    onClick?.();
                  }}
                >
                  <i className={`fas fa-chevron-${isExpanded ? 'up' : 'down'} text-xs`} />
                </button>
              )}

              {/* Close button */}
              {onClose && (
                <button
                  onClick={e => {
                    e.stopPropagation();
                    onClose(notification);
                  }}
                  className="text-white/40 hover:text-white/80"
                >
                  <i className="fas fa-times text-xs"></i>
                </button>
              )}
            </div>
          </div>
          <p className={`text-white/80 ${isTopBar ? 'text-xs' : 'text-sm'} truncate`}>
            {notification.message}
          </p>
          {notification.metadata?.media && !isTopBar && (
            <div className="mt-2 rounded-lg overflow-hidden w-16 h-16">
              <img
                src={notification.metadata.media}
                alt=""
                className="w-full h-full object-cover"
              />
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default NotificationRenderer;
