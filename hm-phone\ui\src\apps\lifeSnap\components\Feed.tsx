import React, { useState, useEffect, useRef } from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import StoryRing from './StoryRing';
import StoryViewer from './StoryViewer';
import PostDetail from './PostDetail';
import { getTimeAgo } from '../../../utils/timeUtils';

interface FeedProps {
  onPostDetailView: (isDetail: boolean, postId?: number) => void;
  isDetailView?: boolean;
  postId?: number;
}

const Feed: React.FC<FeedProps> = ({ onPostDetailView, isDetailView = false, postId }) => {
  const store = useLifeSnapStore();
  const { posts, profiles, stories, loading } = store;
  const [selectedStoryId, setSelectedStoryId] = useState<number | null>(null);
  const [selectedPostId, setSelectedPostId] = useState<number | null>(null);
  const [expandedCaptions, setExpandedCaptions] = useState<number[]>([]);

  // Use a ref to track the previous value of selectedPostId
  const prevSelectedPostIdRef = useRef<number | null>(null);

  // Initialize selectedPostId from props if in detail view
  useEffect(() => {
    if (isDetailView && postId && !selectedPostId) {
      setSelectedPostId(postId);
    }
  }, [isDetailView, postId, selectedPostId]);

  // Update parent component when post detail view changes, but only when the value actually changes
  useEffect(() => {
    // Only call onPostDetailView when selectedPostId actually changes
    if (prevSelectedPostIdRef.current !== selectedPostId) {
      onPostDetailView(selectedPostId !== null, selectedPostId || undefined);
      prevSelectedPostIdRef.current = selectedPostId;
    }
  }, [selectedPostId, onPostDetailView]);

  const handleStoryClick = (userId: string | number) => {
    if (!stories || stories.length === 0) return;

    // Safely get timestamp values with fallbacks
    const getTimeValue = (timestamp: Date | number | string | undefined) => {
      if (!timestamp) return 0;
      if (timestamp instanceof Date) return timestamp.getTime();
      if (typeof timestamp === 'number') return timestamp;
      try {
        return new Date(timestamp).getTime();
      } catch {
        console.error('Invalid timestamp format:', timestamp);
        return 0;
      }
    };

    const userStories = stories
      .filter(
        story =>
          story.userId === userId && story.expiresAt && new Date(story.expiresAt) > new Date()
      )
      .sort((a, b) => {
        const aTime = getTimeValue(a.timestamp);
        const bTime = getTimeValue(b.timestamp);
        return aTime - bTime;
      });

    if (userStories.length > 0) {
      setSelectedStoryId(userStories[0].id);
    }
  };

  const handleStoryNavigation = (direction: 'next' | 'previous') => {
    if (!selectedStoryId || !stories || stories.length === 0) return;

    const currentStory = stories.find(s => s.id === selectedStoryId);
    if (!currentStory) return;

    // Safely get timestamp values with fallbacks
    const getTimeValue = (timestamp: Date | number | string | undefined) => {
      if (!timestamp) return 0;
      if (timestamp instanceof Date) return timestamp.getTime();
      if (typeof timestamp === 'number') return timestamp;
      try {
        return new Date(timestamp).getTime();
      } catch {
        console.error('Invalid timestamp format:', timestamp);
        return 0;
      }
    };

    const userStories = stories
      .filter(
        story =>
          story.userId === currentStory.userId &&
          story.expiresAt &&
          new Date(story.expiresAt) > new Date()
      )
      .sort((a, b) => {
        const aTime = getTimeValue(a.timestamp);
        const bTime = getTimeValue(b.timestamp);
        return aTime - bTime;
      });

    if (userStories.length === 0) {
      setSelectedStoryId(null);
      return;
    }

    const currentIndex = userStories.findIndex(s => s.id === selectedStoryId);
    if (currentIndex === -1) {
      setSelectedStoryId(null);
      return;
    }

    if (direction === 'next') {
      if (currentIndex < userStories.length - 1) {
        setSelectedStoryId(userStories[currentIndex + 1].id);
      } else {
        setSelectedStoryId(null);
      }
    } else {
      if (currentIndex > 0) {
        setSelectedStoryId(userStories[currentIndex - 1].id);
      }
    }
  };

  // Remove unused code

  const toggleCaptionExpansion = (postId: number) => {
    setExpandedCaptions(prev =>
      prev.includes(postId) ? prev.filter(id => id !== postId) : [...prev, postId]
    );
  };

  // Show post detail view if a post is selected
  if (selectedPostId) {
    return (
      <>
        <PostDetail
          postId={selectedPostId}
          onBack={() => {
            setSelectedPostId(null);
            onPostDetailView(false);
          }}
        />
      </>
    );
  }

  // Show loading indicator when posts are being loaded
  if (loading) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center py-8">
        <div className="text-white text-lg">Loading posts...</div>
      </div>
    );
  }

  // Show empty state if no posts are available
  if (posts.length === 0) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center py-8">
        <i className="fas fa-camera text-3xl text-white/60 mb-2"></i>
        <div className="text-white/60 text-lg">No posts yet</div>
      </div>
    );
  }

  // Get current user ID from the store
  const { currentProfile } = store;
  const currentUserId = currentProfile?.id;

  return (
    <div className="flex flex-col gap-2">
      {posts.map(post => {
        // Use embedded profile information if available, otherwise fall back to profiles array
        const hasEmbeddedProfile = post.username && post.profilePicture;
        const profileId = post.userId; // Use userId (identifier) directly
        const username = post.username;
        const fullName = post.fullName;
        const profilePicture = post.profilePicture;
        const isVerified = post.isVerified;

        // Simplified fallback profile lookup
        const fallbackProfile = !hasEmbeddedProfile
          ? profiles.find(p => p.id === post.userId)
          : null;

        // If we have neither embedded profile info nor a matching profile, skip this post
        if (!hasEmbeddedProfile && !fallbackProfile) return null;

        return (
          <div key={post.id} className="border-b border-white/10">
            <div className="flex items-center gap-2 p-2">
              <StoryRing userId={profileId} size="sm" onClick={() => handleStoryClick(profileId)}>
                <img
                  src={profilePicture || fallbackProfile?.profilePicture}
                  alt={username || fallbackProfile?.username}
                  className="w-8 h-8 rounded-full object-cover border-2 border-black"
                />
              </StoryRing>
              <div className="ml-3 flex-1">
                <div className="flex items-center gap-1">
                  <span className="font-bold text-sm text-white">
                    {fullName || fallbackProfile?.fullName}
                  </span>
                  {(isVerified || fallbackProfile?.isVerified) && (
                    <i className="fas fa-check-circle text-blue-500 text-xs"></i>
                  )}
                </div>
                <div className="text-xs text-white/60">
                  @{username || fallbackProfile?.username}
                </div>
              </div>
              <button className="text-white/60">
                <i className="fas fa-ellipsis-h"></i>
              </button>
            </div>

            {/* Post Image */}
            <div
              className="aspect-square cursor-pointer"
              onClick={() => {}}
              onDoubleClick={() => {}}
            >
              <img src={post.imageUrl} alt="Post content" className="w-full h-full object-cover" />
            </div>

            {/* Post Actions and Text Content */}
            <div className="px-3">
              {/* Action Buttons */}
              <div className="flex items-center gap-4 mb-0.5">
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => {}}
                    className={`text-lg ${
                      currentUserId && post.likes.some((id: number) => id === currentUserId)
                        ? 'text-red-500'
                        : 'text-white'
                    }`}
                  >
                    <i
                      className={`${
                        currentUserId && post.likes.some((id: number) => id === currentUserId)
                          ? 'fas'
                          : 'far'
                      } fa-heart`}
                    ></i>
                  </button>
                  <span className="text-xs text-white/90">{post.likes.length}</span>
                </div>
                <div
                  className="flex items-center gap-1 cursor-pointer"
                  onClick={() => setSelectedPostId(post.id)}
                >
                  <i className="far fa-comment text-lg text-white/90"></i>
                  <span className="text-xs text-white/90">{post.comments.length}</span>
                </div>
                {/* Bookmark button - removed since functionality is not implemented */}
              </div>

              {/* Caption with truncate */}
              <div className="text-xs text-white/90 overflow-hidden">
                <div className={`${!expandedCaptions.includes(post.id) ? 'line-clamp-1' : ''}`}>
                  <span className="font-bold">{username || fallbackProfile?.username}</span>
                  <span className="ml-1">{post.caption}</span>
                </div>
                {post.caption.length > 50 && (
                  <button
                    className="text-white/60 text-xs mt-0.5"
                    onClick={() => toggleCaptionExpansion(post.id)}
                  >
                    {expandedCaptions.includes(post.id) ? 'Show less' : 'See more'}
                  </button>
                )}
              </div>

              {/* Timestamp */}
              <div className="mt-1.5 mb-1.5 text-[10px] text-white/40">
                {getTimeAgo(
                  post.timestamp instanceof Date
                    ? post.timestamp.getTime()
                    : typeof post.timestamp === 'number'
                    ? post.timestamp
                    : new Date(post.timestamp).getTime()
                )}
              </div>
            </div>
          </div>
        );
      })}
      {selectedStoryId !== null && (
        <StoryViewer
          storyId={selectedStoryId}
          onClose={() => setSelectedStoryId(null)}
          onNext={() => handleStoryNavigation('next')}
          onPrevious={() => handleStoryNavigation('previous')}
        />
      )}
    </div>
  );
};

export default Feed;
