import React, { useState } from 'react';
import { HandlingProfile, BaseHandlingData } from '../../../scripts/shared/types';

interface ProfileManagerProps {
  profiles: HandlingProfile[];
  currentHandling: Partial<BaseHandlingData>;
  vehicleModel?: string;
  onSaveProfile: (profile: Omit<HandlingProfile, 'id' | 'createdAt'>) => void;
  onLoadProfile: (profileId: string) => void;
  onDeleteProfile: (profileId: string) => void;
  onSearchProfiles: (searchTerm: string) => void;
}

const ProfileManager: React.FC<ProfileManagerProps> = ({
  profiles,
  currentHandling,
  vehicleModel,
  onSaveProfile,
  onLoadProfile,
  onDeleteProfile,
  onSearchProfiles
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');  
  const [newProfile, setNewProfile] = useState({
    name: '',
    description: '',
    isPublic: false,
    vehicleSpecific: false
  });

  const handleSearchChange = (term: string) => {
    setSearchTerm(term);
    onSearchProfiles(term);
  };

  const handleSaveProfile = () => {
    if (!newProfile.name.trim()) return;

    const profile: Omit<HandlingProfile, 'id' | 'createdAt'> = {
      name: newProfile.name.trim(),
      description: newProfile.description.trim() || undefined,
      vehicleModel: newProfile.vehicleSpecific ? vehicleModel : undefined,
      handlingData: currentHandling,
      createdBy: 'current-player', // This would come from player data
      isPublic: newProfile.isPublic,
      tags: []
    };

    onSaveProfile(profile);
    setIsCreating(false);
    setNewProfile({
      name: '',
      description: '',
      isPublic: false,
      vehicleSpecific: false
    });
  };

  const filteredProfiles = profiles.filter(profile => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      profile.name.toLowerCase().includes(searchLower) ||
      profile.description?.toLowerCase().includes(searchLower) ||
      profile.vehicleModel?.toLowerCase().includes(searchLower)
    );
  });

  return (
    <div className="profile-manager">
      <div className="profile-header">
        <div className="profile-search">
          <input
            type="text"
            placeholder="Search profiles..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="profile-search-input"
          />
        </div>
        <button
          className="create-profile-btn"
          onClick={() => setIsCreating(true)}
          disabled={Object.keys(currentHandling).length === 0}
          title="Create new profile from current settings"
        >
          + New Profile
        </button>
      </div>

      {isCreating && (
        <div className="profile-creator">
          <div className="profile-creator-header">
            <h3>Create New Profile</h3>
            <button className="close-btn" onClick={() => setIsCreating(false)}>✕</button>
          </div>
          
          <div className="profile-form">
            <div className="form-row">
              <label htmlFor="profile-name">Profile Name*</label>
              <input
                id="profile-name"
                type="text"
                value={newProfile.name}
                onChange={(e) => setNewProfile(prev => ({ ...prev, name: e.target.value }))}
                placeholder="My Custom Setup"
                maxLength={50}
              />
            </div>
            
            <div className="form-row">
              <label htmlFor="profile-description">Description</label>
              <textarea
                id="profile-description"
                value={newProfile.description}
                onChange={(e) => setNewProfile(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description..."
                maxLength={200}                rows={3}
              />
            </div>
            
            <div className="form-checkboxes">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={newProfile.vehicleSpecific}
                  onChange={(e) => setNewProfile(prev => ({ ...prev, vehicleSpecific: e.target.checked }))}
                />
                Vehicle-specific ({vehicleModel || 'Unknown'})
              </label>
              
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={newProfile.isPublic}
                  onChange={(e) => setNewProfile(prev => ({ ...prev, isPublic: e.target.checked }))}
                />
                Make public
              </label>
            </div>
            
            <div className="form-actions">
              <button className="cancel-btn" onClick={() => setIsCreating(false)}>
                Cancel
              </button>
              <button 
                className="save-btn" 
                onClick={handleSaveProfile}
                disabled={!newProfile.name.trim()}
              >
                Save Profile
              </button>
            </div>
          </div>
        </div>
      )}      <div className="profile-list">
        {filteredProfiles.map(profile => {
          return (
            <div key={profile.id} className="profile-item">
              <div className="profile-info">
                <div className="profile-main">
                  <span className="profile-icon">🔧</span>
                  <div className="profile-details">
                    <div className="profile-name">{profile.name}</div>
                    {profile.description && (
                      <div className="profile-description">{profile.description}</div>
                    )}
                    <div className="profile-meta">
                      {profile.vehicleModel && (
                        <span className="profile-vehicle">{profile.vehicleModel}</span>
                      )}
                      {profile.vehicleModel && (
                        <span className="profile-separator"> • </span>
                      )}
                      <span className="profile-created">{new Date(profile.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
                
                <div className="profile-actions">
                  <button
                    className="load-btn"
                    onClick={() => onLoadProfile(profile.id)}
                    title="Load this profile"
                  >
                    Load
                  </button>
                  <button
                    className="delete-btn"
                    onClick={() => onDeleteProfile(profile.id)}
                    title="Delete this profile"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          );
        })}
        
        {filteredProfiles.length === 0 && (
          <div className="no-profiles">
            {searchTerm ? 'No profiles found matching your search' : 'No profiles saved yet'}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileManager;
