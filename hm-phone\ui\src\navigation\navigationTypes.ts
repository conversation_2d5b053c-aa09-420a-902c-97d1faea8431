// navigation/types.ts

export interface NavigationHistoryEntry {
  app: string;
  view: string;
  tab?: string;
  data?: Record<string, unknown>;
  mode?: string;
  returnTo?: string;
  callbackId?: string;
}

export interface NavigationOptions {
  mode?: string;
  clearHistory?: boolean;
  replace?: boolean;
  tab?: string;
}

export interface NavigationStateData {
  currentApp: string | null;
  currentView: string;
  currentTab?: string;
  history: NavigationHistoryEntry[];
}

export interface NavigationState extends NavigationStateData {
  // Navigation methods
  openApp: (app: string, data?: Record<string, unknown>, options?: NavigationOptions) => boolean;
  openView: (view: string, data?: Record<string, unknown>, options?: NavigationOptions) => boolean;
  openAppView: (
    app: string,
    view: string,
    data?: Record<string, unknown>,
    options?: NavigationOptions
  ) => boolean;
  switchTab: (tab: string, data?: Record<string, unknown>) => boolean;
  goBack: () => void;
  goHome: () => void;
  openAppAndReturn: (
    fromApp: string,
    toApp: string,
    data?: Record<string, unknown>,
    mode?: string,
    onReturn?: (result: unknown) => void
  ) => void;
  goBackWithResult: (result?: unknown) => void;
  openDeepLink: (link: string) => boolean;

  // Persistence methods
  saveNavigationState: () => NavigationStateData;
  restoreNavigationState: (state: NavigationStateData) => void;
}

export interface CallbackRegistry {
  [key: string]: (result: unknown) => void;
}
