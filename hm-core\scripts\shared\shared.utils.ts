export class Logger {
    private static formatMessage(level: string, message: string): string {
        const timestamp = new Date().toISOString();
        return `[${timestamp}] [${level}] ${message}`;
    }

    static info(message: string): void {
        console.log(this.formatMessage('INFO', message));
    }

    static warn(message: string): void {
        console.warn(this.formatMessage('WARN', message));
    }

    static error(message: string): void {
        console.error(this.formatMessage('ERROR', message));
    }

    static debug(message: string): void {
        if (GetConvar('debug_mode', 'false') === 'true') {
            console.log(this.formatMessage('DEBUG', message));
        }
    }
}

export function generateId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

export function sanitizeString(input: string): string {
    return input.replace(/[<>"'&]/g, '').trim();
}

export function isValidName(name: string): boolean {
    return /^[a-zA-Z\s]{2,50}$/.test(name);
}
