import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  plugins: [],
  build: {
    outDir: 'build',
    emptyOutDir: true,
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      input: {
        client: resolve(__dirname, 'scripts/client/main.ts'),
        server: resolve(__dirname, 'scripts/server/main.ts'),
        shared: resolve(__dirname, 'scripts/shared/main.ts'),
      },
      output: {
        entryFileNames: '[name].js',
        format: 'esm', // Changed to esm
      },
    },
  },
  resolve: {
    alias: {
      '@shared': resolve(__dirname, './scripts/shared/types'),
    },
  },
});
