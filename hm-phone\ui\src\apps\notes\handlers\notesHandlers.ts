/**
 * Notes app message handlers
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useNotesStore } from '../stores/notesStore';

// Register handler for notes data
registerEventHandler('notes', 'notes', data => {
  console.log('[Notes] Received notes data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the notes data
    useNotesStore.getState().setNotes(data);
  } else {
    console.error('[Notes] Received invalid notes data (not an array):', data);
  }
});
