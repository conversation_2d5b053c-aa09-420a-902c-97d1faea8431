import React, { useState } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import NearbyPlayersPanel from '../../../common/components/NearbyPlayersPanel';

const ProfileTab: React.FC = () => {
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [showAirdrop, setShowAirdrop] = useState(false);

  const { userProfile } = usePhoneStore();

  const handlePlayerClick = (playerId: number) => {
    console.log('Player clicked:', playerId);
    // Add your player click handling logic here
    // For example: openView('player', { id: playerId });
  };

  const handleEditName = () => {
    setEditedName(userProfile.name);
    setIsEditingName(true);
  };

  const handleSaveName = () => {
    // Handle saving name logic here
    setIsEditingName(false);
  };

  const handleCopyNumber = () => {
    navigator.clipboard.writeText(userProfile.phoneNumber || '');
  };

  const handleShareNumber = () => {
    setShowAirdrop(true);
  };

  return (
    <div className="h-full flex flex-col bg-gray-900 text-white relative">
      {' '}
      {/* Added relative positioning */}
      <div className="p-4 flex flex-col items-center">
        {/* Profile Image */}
        <div className="relative w-24 h-24 rounded-full bg-white/20 overflow-hidden mb-4">
          {userProfile.imageUrl ? (
            <img
              src={userProfile.imageUrl}
              alt={userProfile.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <i className="fas fa-user text-4xl text-white/40"></i>
            </div>
          )}
        </div>

        {/* Name */}
        {isEditingName ? (
          <div className="flex flex-col items-center gap-2">
            <input
              type="text"
              value={editedName}
              onChange={e => setEditedName(e.target.value)}
              className="bg-white/10 px-3 py-2 rounded text-center"
              autoFocus
            />
            <div className="flex gap-2">
              <button
                onClick={() => setIsEditingName(false)}
                className="px-3 py-1 bg-white/10 rounded text-sm"
              >
                Cancel
              </button>
              <button onClick={handleSaveName} className="px-3 py-1 bg-blue-500 rounded text-sm">
                Save
              </button>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold">{userProfile.name}</h2>
            <button onClick={handleEditName} className="text-white/60 hover:text-white">
              <i className="fas fa-pencil-alt text-sm"></i>
            </button>
          </div>
        )}

        {/* Phone Number */}
        <div className="flex items-center gap-2 mt-2">
          <p className="text-white/60 text-sm">{userProfile.phoneNumber}</p>
          <button onClick={handleCopyNumber} className="text-white/60 hover:text-white">
            <i className="fas fa-copy text-sm"></i>
          </button>
        </div>

        {/* Share Number Button - Icon Only */}
        <button
          onClick={handleShareNumber}
          className="mt-4 w-12 h-12 flex items-center justify-center
                     bg-gradient-to-r from-blue-500/20 to-blue-500/20
                     hover:from-blue-500/30 hover:to-blue-500/30
                     border border-blue-500/20 rounded-xl"
        >
          <i className="fas fa-qrcode text-blue-400 text-2xl"></i>
        </button>
      </div>
      {/* Nearby Players Panel */}
      <NearbyPlayersPanel
        show={showAirdrop}
        onClose={() => setShowAirdrop(false)}
        title="Share My Number"
        onPlayerSelect={handlePlayerClick}
        contentAbove={
          <div className="bg-white/5 rounded-lg p-3 mb-2 flex items-center">
            {userProfile.imageUrl ? (
              <div
                className="w-10 h-10 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${userProfile.imageUrl})` }}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-lg font-medium">
                  {userProfile.name ? userProfile.name[0].toUpperCase() : '#'}
                </span>
              </div>
            )}
            <div className="ml-3">
              <div className="text-white font-medium">{userProfile.name}</div>
              <div className="text-white/60 text-sm">{userProfile.phoneNumber}</div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ProfileTab;
