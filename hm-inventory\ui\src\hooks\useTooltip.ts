import { useState, useCallback, useRef, useEffect } from 'react';

interface TooltipPosition {
  x: number;
  y: number;
}

interface UseTooltipReturn {
  isVisible: boolean;
  position: TooltipPosition;
  isDetailedView: boolean; // New property for detailed view
  showTooltip: (event: React.MouseEvent) => void;
  hideTooltip: () => void;
  forceHide: () => void;
  mouseEnterProps: {
    onMouseEnter: (event: React.MouseEvent) => void;
    onMouseLeave: () => void;
    onMouseMove: (event: React.MouseEvent) => void;
    onContextMenu: () => void;
  };
}

export const useTooltip = (delay: number = 300): UseTooltipReturn => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDetailedView, setIsDetailedView] = useState(false);
  const [position, setPosition] = useState<TooltipPosition>({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isHoveringRef = useRef(false);
  const showTooltip = useCallback((event: React.MouseEvent) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Calculate position to avoid going off-screen
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let x = event.clientX;
    let y = event.clientY;

    // Adjust position if tooltip would go off-screen
    // Assuming tooltip width of ~300px and height of ~200px
    const tooltipWidth = 300;
    const tooltipHeight = 200;
    
    if (x + tooltipWidth > viewportWidth) {
      x = event.clientX - tooltipWidth - 20;
    }
    
    if (y + tooltipHeight > viewportHeight) {
      y = event.clientY - tooltipHeight - 20;
    }

    setPosition({ x, y });
    
    // Show tooltip after delay
    timeoutRef.current = setTimeout(() => {
      if (isHoveringRef.current) {
        setIsVisible(true);
      }
    }, delay);
  }, [delay]);
  const hideTooltip = useCallback(() => {
    isHoveringRef.current = false;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsVisible(false);
  }, []);

  const forceHide = useCallback(() => {
    isHoveringRef.current = false;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsVisible(false);
  }, []);

  const handleContextMenu = useCallback(() => {
    // Immediately hide tooltip when right-clicking
    forceHide();
  }, [forceHide]);
  const handleMouseEnter = useCallback((event: React.MouseEvent) => {
    isHoveringRef.current = true;
    setIsDetailedView(event.altKey); // Check if Alt key is pressed
    showTooltip(event);
  }, [showTooltip]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (isHoveringRef.current) {
      // Update detailed view state based on Alt key
      setIsDetailedView(event.altKey);
      
      // Update position on mouse move for better following
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      let x = event.clientX;
      let y = event.clientY;

      const tooltipWidth = 300;
      const tooltipHeight = 200;
      
      if (x + tooltipWidth > viewportWidth) {
        x = event.clientX - tooltipWidth - 20;
      }
      
      if (y + tooltipHeight > viewportHeight) {
        y = event.clientY - tooltipHeight - 20;
      }

      setPosition({ x, y });
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    hideTooltip();
  }, [hideTooltip]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);  return {
    isVisible,
    position,
    isDetailedView,
    showTooltip,
    hideTooltip,
    forceHide,
    mouseEnterProps: {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onMouseMove: handleMouseMove,
      onContextMenu: handleContextMenu,
    },
  };
};
