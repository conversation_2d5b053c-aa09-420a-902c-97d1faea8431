import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigation } from '../../navigation/hooks';
import PhotoPreview from './components/PhotoPreview';
import { usePhotosStore } from '../photos/stores/photosStore';
import { clientRequests } from '../../fivem/clientRequestSender';
import { isBrowser } from '../../utils/environment';
import { useNavigationStore } from '../../navigation/navigationStore';
import { GameView } from '../../lib/GameView';
import useContainerDimensions from './hooks/useContainerDimensions';
import { motion } from 'framer-motion';
import config from '@shared/config';

const Camera: React.FC = () => {
  const { openApp, goBackWithResult } = useNavigation();
  const { history } = useNavigationStore();
  const [currentView, setCurrentView] = useState<'photo' | 'video' | 'preview'>('photo');
  const [currentPhoto, setCurrentPhoto] = useState<string | null>(null);
  const [isSelfieMode, setIsSelfieMode] = useState(config.camera.defaultSettings.currentMode === 'selfie');
  const [flashEnabled, setFlashEnabled] = useState(config.camera.defaultSettings.flashEnabled !== undefined ?
    config.camera.defaultSettings.flashEnabled : false);

  // Video recording state
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const recordingControllerRef = useRef<any>(null);

  // GameView refs
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameViewRef = useRef<GameView | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const dimensions = useContainerDimensions(containerRef as React.RefObject<HTMLDivElement>);

  // Get max recording duration from config
  const MAX_RECORDING_DURATION = config.camera.video?.maxRecordingDuration || 15; // Default to 15 seconds if not specified

  // Get the most recent photo from the Photos store
  const { photos } = usePhotosStore();
  const recentPhoto = photos.length > 0 ? photos[0].imageUrl : null;

  // Mock latest video thumbnail (replace with actual data when available)
  const latestVideo = 'https://picsum.photos/800/1000?random=42';

  // Handle camera animations when component mounts or selfie mode changes
  useEffect(() => {
    // Start camera animations when component mounts or selfie mode changes
    if (!isBrowser) {
      console.log('[Camera] Sending openCamera request with settings:', {
        currentMode: isSelfieMode ? 'selfie' : 'photo',
        flashEnabled: flashEnabled
      });
      clientRequests.send('camera', 'openCamera', {
        settings: {
          currentMode: isSelfieMode ? 'selfie' : 'photo',
          flashEnabled: flashEnabled
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSelfieMode]); // Only depend on selfie mode changes to prevent flash toggle loop

  // Initialize GameView on mount with dimensions
  useEffect(() => {
    if (canvasRef.current && dimensions.width > 0 && dimensions.height > 0) {
      // Always clean up previous instance first
      if (gameViewRef.current) {
        gameViewRef.current.stop();
        gameViewRef.current = null;
      }

      console.log('[Camera] Initializing GameView');

      try {
        // Set canvas dimensions
        if (canvasRef.current) {
          canvasRef.current.width = dimensions.width;
          canvasRef.current.height = dimensions.height;
        }

        // In browser mode, we don't need to draw anything on the canvas
        // as we'll use the gameview.png at the root level
        if (isBrowser) {
          console.log('[Camera] Browser mode: using gameview.png at root level');
          return; // Skip GameView initialization in browser mode
        }

        // Create GameView with cropping and camera controls options
        // Use different cropping based on current mode
        const cropHorizontal = currentView === 'photo' ? 0.20 : 0.15;

        const gv = new GameView({
          cropVertical: 0.10,   // 5% crop from both top and bottom
          cropHorizontal: cropHorizontal,  // Different crop for photo/video
          enableCameraControls: true // Allow player to look around
        });

        // Initialize the GameView
        const gameViewInstance = gv.createGameView(canvasRef.current);

        // Resize if needed
        gameViewInstance?.resize(dimensions.width, dimensions.height);

        gameViewRef.current = gv;

        console.log('[Camera] GameView initialized with cropping and camera controls');
      } catch (error) {
        console.error('[Camera] Error initializing GameView:', error);
      }
    }
  }, [dimensions, currentView]);

  // Add cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      // Clean up GameView
      if (gameViewRef.current) {
        console.log('[Camera] Stopping GameView on unmount');
        gameViewRef.current.stop();
        gameViewRef.current = null;
      }

      // Clean up recording timer if active
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      // Stop camera animations when component unmounts
      if (!isBrowser) {
        clientRequests.send('camera', 'closeCamera', {});
      }
    };
  }, []); // Only run once on mount

  // Take a photo using the GameView
  const takePhoto = useCallback(() => {
    try {
      // Get the canvas
      const canvas = canvasRef.current;
      if (!canvas) {
        console.error('[Camera] Canvas not available');
        return;
      }

      // In browser mode, we'll use a mock photo
      if (isBrowser) {
        // Generate a random placeholder image URL
        const mockPhotoId = Math.floor(Math.random() * 1000);
        const mockPhotoUrl = `https://picsum.photos/800/1000?random=${mockPhotoId}`;

        console.log('[Camera] Browser mode: using mock photo');

        // Set the mock photo and switch to preview mode
        setCurrentPhoto(mockPhotoUrl);
        setCurrentView('preview');
        return;
      }

      // For FiveM, use the GameView to capture a photo
      if (!gameViewRef.current) {
        console.error('[Camera] GameView not available');
        return;
      }

      // Capture the current frame as a data URL
      const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      console.log('[Camera] Photo captured');

      // Set the current photo and switch to preview mode
      setCurrentPhoto(dataUrl);
      setCurrentView('preview');

      // Enable NUI focus for preview mode
      clientRequests.send('camera', 'togglePreviewMode', { enabled: true });
    } catch (error) {
      console.error('[Camera] Error taking photo:', error);
    }
  }, [gameViewRef, canvasRef]);

    // Stop recording video
  const stopRecording = useCallback(async () => {
    console.log('[Camera] Stopping recording');

    try {
      // Stop the recording timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      // In browser mode, we'll simulate stopping recording
      if (isBrowser) {
        console.log('[Camera] Browser mode: simulating video stop');

        // Reset recording state
        setIsRecording(false);
        setRecordingTime(0);

        // Redraw the canvas with the normal view
        if (canvasRef.current) {
          const ctx = canvasRef.current.getContext('2d');
          if (ctx) {
            // Draw a gradient background
            const gradient = ctx.createLinearGradient(0, 0, 0, dimensions.height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(1, '#16213e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, dimensions.width, dimensions.height);

            // Draw a video icon in the center
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.font = '48px FontAwesome';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('\uf03d', dimensions.width / 2, dimensions.height / 2);

            // Draw text
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.font = '16px Arial';
            ctx.fillText('Video Preview', dimensions.width / 2, dimensions.height / 2 + 60);
          }
        }

        return;
      }

      // For FiveM, use the GameView to stop recording
      if (!isRecording || !recordingControllerRef.current) return;

      // Stop the recording
      recordingControllerRef.current.stop();

      // Get the video blob
      const blob = recordingControllerRef.current.getBlob();

      // Reset recording state
      setIsRecording(false);
      setRecordingTime(0);
      recordingControllerRef.current = null;

      console.log('[Camera] Recording stopped, processing video');

      // Process the video
      await processVideo(blob);
    } catch (error) {
      console.error('[Camera] Error stopping recording:', error);

      // Reset recording state even if there's an error
      setIsRecording(false);
      setRecordingTime(0);
      recordingControllerRef.current = null;
    }
  }, [isRecording, recordingControllerRef, dimensions, canvasRef]);

  // Start recording video
  const startRecording = useCallback(() => {
    console.log('[Camera] Starting recording');

    try {
      // In browser mode, we'll simulate recording
      if (isBrowser) {
        console.log('[Camera] Browser mode: simulating video recording');

        // Update recording state
        setIsRecording(true);
        setRecordingTime(0);

        // Start a timer to update the recording time
        recordingTimerRef.current = setInterval(() => {
          setRecordingTime(prev => {
            const newTime = prev + 1;

            // Stop recording if we reach the maximum duration
            if (newTime >= MAX_RECORDING_DURATION) {
              stopRecording();
              return MAX_RECORDING_DURATION;
            }

            return newTime;
          });
        }, 1000);

        // Redraw the canvas with a recording indicator
        if (canvasRef.current) {
          const ctx = canvasRef.current.getContext('2d');
          if (ctx) {
            // Draw a gradient background
            const gradient = ctx.createLinearGradient(0, 0, 0, dimensions.height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(1, '#16213e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, dimensions.width, dimensions.height);

            // Draw a video icon in the center
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.font = '48px FontAwesome';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('\uf03d', dimensions.width / 2, dimensions.height / 2);

            // Draw text
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.font = '16px Arial';
            ctx.fillText('Recording Video...', dimensions.width / 2, dimensions.height / 2 + 60);
          }
        }

        return;
      }

      // For FiveM, use the GameView to record
      if (isRecording || !gameViewRef.current) return;

      // Start recording using the GameView instance
      const controller = gameViewRef.current.startRecording({
        mimeType: 'video/webm;codecs=vp9',
        videoBitsPerSecond: 5000000 // 5 Mbps
      });

      if (!controller) {
        console.error('[Camera] Failed to start recording');
        return;
      }

      // Store the recording controller
      recordingControllerRef.current = controller;

      // Update recording state
      setIsRecording(true);
      setRecordingTime(0);

      // Start a timer to update the recording time
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;

          // Stop recording if we reach the maximum duration
          if (newTime >= MAX_RECORDING_DURATION) {
            stopRecording();
            return MAX_RECORDING_DURATION;
          }

          return newTime;
        });
      }, 1000);

      console.log('[Camera] Recording started');
    } catch (error) {
      console.error('[Camera] Error starting recording:', error);
    }
  }, [isRecording, MAX_RECORDING_DURATION, stopRecording, dimensions.height, dimensions.width]);



  // Process the recorded video
  const processVideo = async (blob?: Blob) => {
    // In browser mode, we'll use a mock video
    if (isBrowser) {
      console.log('[Camera] Browser mode: using mock video');
      return;
    }

    if (!blob) {
      console.warn('[Camera] No video blob to process');
      return;
    }

    console.log('[Camera] Processing video blob:', blob);

    try {
      // Convert the blob to a base64 string
      const reader = new FileReader();
      reader.readAsDataURL(blob);

      reader.onloadend = () => {
        const base64String = reader.result as string;

        console.log('[Camera] Video converted to base64, sending to client');

        // Send to client script via NUI callback (FiveM)
        if (typeof fetch !== 'undefined') {
          fetch('https://hm-phone/videoTaken', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ video: base64String })
          });
          console.log('[Camera] Sent videoTaken event to client script via fetch');
        } else if (window && typeof window.postMessage === 'function') {
          window.postMessage({ app: 'camera', type: 'videoTaken', data: { video: base64String } }, '*');
          console.log('[Camera] Sent videoTaken event to client script via postMessage');
        }
      };
    } catch (err) {
      console.error('[Camera] Error sending videoTaken event:', err);
    }
  };

  // Handle saving a photo
  const handleSavePhoto = async () => {
    if (currentPhoto) {
      try {
        // const response = await cameraActions.takePhoto(currentPhoto);
        // console.log('[Camera] Photo saved successfully:', response);
        const lastEntry = history[history.length - 1];
        const hasReturnPath = lastEntry && lastEntry.data && lastEntry.data._returnTo;

        // Disable preview mode NUI focus
        if (!isBrowser) {
          clientRequests.send('camera', 'togglePreviewMode', { enabled: false });
        }

        if (hasReturnPath) {
          console.log('[Camera] Returning photo data:', currentPhoto?.substring(0, 30) + '...');
          goBackWithResult([currentPhoto]);
        } else {
          // Just go back to photo mode
          setCurrentView('photo');
        }
      } catch (error) {
        console.error('[Camera] Error saving photo:', error);
        // Show an error message to the user
        alert('Failed to save photo. Please try again.');
        // Stay in preview mode so user can retry
      }
    } else {
      // Disable preview mode NUI focus
      if (!isBrowser) {
        clientRequests.send('camera', 'togglePreviewMode', { enabled: false });
      }

      // No photo, just go back to photo mode
      setCurrentView('photo');
    }
  };

  // Handle discarding a photo
  const handleDiscardPhoto = () => {
    // Disable preview mode NUI focus
    if (!isBrowser) {
      clientRequests.send('camera', 'togglePreviewMode', { enabled: false });
    }

    setCurrentPhoto(null);
    setCurrentView('photo');
  };

  // Handle opening the Photos app
  const handleOpenPhotos = () => {
    openApp('photos');
  };

  // Handle going back to camera
  const handleBackToCamera = () => {
    // Disable preview mode NUI focus
    if (!isBrowser) {
      clientRequests.send('camera', 'togglePreviewMode', { enabled: false });
    }

    // First set the view
    setCurrentView('photo');

    // Force a re-measurement of the container when returning to photo view
    // Use a slightly longer timeout to ensure the view has fully rendered
    setTimeout(() => {}, 100); // 100ms delay to ensure DOM is updated
  };

  // Handle switching to video mode
  const handleSwitchToVideo = () => {
    setCurrentView('video');
  };

  // Handle toggling selfie mode
  // const handleToggleSelfieMode = () => {
  //   const newSelfieMode = !isSelfieMode;
  //   setIsSelfieMode(newSelfieMode);

  //   // Update animation if not in browser mode
  //   if (!isBrowser) {
  //     clientRequests.send('camera', 'openCamera', {
  //       settings: {
  //         currentMode: newSelfieMode ? 'selfie' : 'photo',
  //         flashEnabled: flashEnabled
  //       }
  //     });
  //   }
  // };

  // Handle toggling flash
  // const handleToggleFlash = async () => {
  //   // Toggle flash directly through the client
  //   if (!isBrowser) {
  //     try {
  //       const response = await clientRequests.send('camera', 'toggleFlash', {});
  //       if (
  //         response.success &&
  //         'flashEnabled' in response &&
  //         typeof response.flashEnabled === 'boolean'
  //       ) {
  //         // Update UI state based on server response
  //         setFlashEnabled(response.flashEnabled);
  //       } else {
  //         // Fallback to toggling locally if server doesn't respond with state
  //         setFlashEnabled(!flashEnabled);
  //       }
  //     } catch (error) {
  //       console.error('[Camera] Error toggling flash:', error);
  //       // Fallback to toggling locally
  //       setFlashEnabled(!flashEnabled);
  //     }
  //   } else {
  //     // In browser mode, just toggle the state locally
  //     setFlashEnabled(!flashEnabled);
  //   }
  // };

  // Register event handlers for camera controls
  useEffect(() => {
    if (isBrowser) return; // Only run in FiveM environment

    // Define interface for message data
    interface CameraMessageData {
      mode?: 'photo' | 'video';
      flashEnabled?: boolean;
      selfieMode?: boolean;
      zoomLevel?: number;
    }

    // Handler for capturing photo
    const handleCapturePhoto = () => {
      console.log('[Camera] Received capturePhoto event');
      if (currentView === 'photo') {
        takePhoto();
      }
    };

    // Handler for starting/stopping recording
    const handleToggleRecording = () => {
      console.log('[Camera] Received toggleRecording event');
      if (currentView === 'video') {
        if (isRecording) {
          stopRecording();
        } else {
          startRecording();
        }
      }
    };

    // Handler for setting mode
    const handleSetMode = (data: CameraMessageData) => {
      console.log('[Camera] Received setMode event:', data);
      if (data && data.mode) {
        const newMode = data.mode === 'photo' ? 'photo' : 'video';
        setCurrentView(newMode);
      }
    };

    // Handler for setting flash
    const handleSetFlash = (data: CameraMessageData) => {
      console.log('[Camera] Received setFlash event:', data);
      if (data && typeof data.flashEnabled === 'boolean') {
        setFlashEnabled(data.flashEnabled);
      }
    };

    // Handler for setting selfie mode
    const handleSetSelfieMode = (data: CameraMessageData) => {
      console.log('[Camera] Received setSelfieMode event:', data);
      if (data && typeof data.selfieMode === 'boolean') {
        setIsSelfieMode(data.selfieMode);
      }
    };

    // Handler for zoom changes
    const handleZoomChanged = (data: CameraMessageData) => {
      // We don't need to handle zoom changes in the UI anymore
      // as we're using native GTA UI for this
      console.log('[Camera] Zoom level changed:', data);
    };

    // Register event listener
    const handleMessage = (event: MessageEvent) => {
      const data = event.data;

      if (data && data.app === 'camera') {
        switch (data.type) {
          case 'capturePhoto':
            handleCapturePhoto();
            break;
          case 'toggleRecording':
            handleToggleRecording();
            break;
          case 'setMode':
            handleSetMode(data.data || {});
            break;
          case 'setFlash':
            handleSetFlash(data.data || {});
            break;
          case 'setSelfieMode':
            handleSetSelfieMode(data.data || {});
            break;
          case 'zoomChanged':
            handleZoomChanged(data.data || {});
            break;
        }
      }
    };

    // Add event listener
    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [currentView, isRecording, takePhoto, startRecording, stopRecording]); // Dependencies for the handlers

  return (
    <>
      {/* Phone UI */}
      <div className="h-full w-full flex flex-col overflow-hidden relative z-10">
        {currentView === 'preview' ? (
          currentPhoto && (
            <PhotoPreview
              photoUrl={currentPhoto}
              onSave={handleSavePhoto}
              onDiscard={handleDiscardPhoto}
              onBack={handleBackToCamera}
            />
          )
        ) : (
          <div className="flex flex-col h-full">
            {/* Main content area with proper spacing for TopBar and PullIndicator */}
            <div className="flex flex-col h-full">
              {/* Top spacing for status bar - 32px (8rem) */}
              <div className="h-8"></div>

              {/* Main camera view container */}
              <div className="relative flex-1 flex flex-col">
                {/* Canvas container */}
                <div ref={containerRef} className="absolute inset-0" style={{ zIndex: 1 }}>
                  {/* Fallback content - only shown in FiveM, not in browser */}
                  {!isBrowser && (
                    <div className="absolute inset-0 bg-gradient-to-b from-zinc-900 to-black flex items-center justify-center pointer-events-none z-0 opacity-50">
                      <div className="text-center text-white/50">
                        <i className={`fas ${currentView === 'photo' ? 'fa-camera' : 'fa-video'} text-5xl mb-3`}></i>
                        <p className="text-sm">{currentView === 'photo' ? 'Camera' : 'Video'} Preview</p>
                      </div>
                    </div>
                  )}

                  {/* Canvas for GameView - only used in FiveM, not in browser */}
                  {!isBrowser && (
                    <canvas
                      ref={canvasRef}
                      className="absolute inset-0 w-full h-full"
                      style={{
                        backgroundColor: 'transparent',
                        imageRendering: 'auto',
                        objectFit: 'cover',
                        pointerEvents: 'none',
                        display: 'block',
                        zIndex: 1
                      }}
                    />
                  )}

                  {/* Flash Overlay */}
                  {flashEnabled && (
                    <div className="absolute inset-0 bg-white/30 pointer-events-none" style={{ zIndex: 2 }}></div>
                  )}
                </div>

                {/* Top controls bar */}
                <div className="relative z-10 px-4 py-2 flex justify-between items-center bg-gradient-to-b from-black/80 to-transparent">
                  {/* Left side - Mode selector */}
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={currentView === 'video' ? handleBackToCamera : undefined}
                      className={`${
                        currentView === 'photo' ? 'text-white' : 'text-white/50'
                      }`}
                    >
                      <span className="text-xs font-medium">PHOTO</span>
                    </button>
                    <button
                      onClick={currentView === 'photo' ? handleSwitchToVideo : undefined}
                      className={`${
                        currentView === 'video' ? 'text-white' : 'text-white/50'
                      }`}
                    >
                      <span className="text-xs font-medium">VIDEO</span>
                    </button>
                  </div>

                  {/* Recording indicator - only shown when recording */}
                  {currentView === 'video' && isRecording && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-red-500 animate-pulse"></div>
                      <span className="text-white text-xs font-medium">
                        {Math.floor(recordingTime / 60)
                          .toString()
                          .padStart(2, '0')}
                        :
                        {(recordingTime % 60).toString().padStart(2, '0')}
                      </span>
                    </div>
                  )}

                  {/* Right side - Empty space for balance */}
                  <div className="w-8"></div>
                </div>

                {/* Spacer to push content to bottom */}
                <div className="flex-1"></div>

                {/* Bottom controls bar */}
                <div className="relative z-10 px-4 py-3 bg-gradient-to-t from-black/80 to-transparent">
                  <div className="flex items-center justify-between">
                    {/* Gallery Button */}
                    <motion.button
                      onClick={handleOpenPhotos}
                      className="w-10 h-10 flex items-center justify-center"
                      whileTap={{ scale: 0.95 }}
                    >
                      {currentView === 'photo' ? (
                        recentPhoto ? (
                          <div className="w-8 h-8 relative rounded-full overflow-hidden">
                            <img src={recentPhoto} alt="Recent" className="w-full h-full object-cover" />
                            <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                              <i className="fas fa-images text-white text-xs"></i>
                            </div>
                          </div>
                        ) : (
                          <i className="fas fa-images text-white text-sm"></i>
                        )
                      ) : (
                        <div className="w-8 h-8 relative rounded-full overflow-hidden">
                          <img src={latestVideo} alt="Latest Video" className="w-full h-full object-cover" />
                          <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                            <i className="fas fa-play text-white text-xs"></i>
                          </div>
                        </div>
                      )}
                    </motion.button>

                    {/* Capture/Record Button */}
                    {currentView === 'photo' ? (
                      <motion.button
                        className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center"
                        onClick={takePhoto}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div className="w-10 h-10 rounded-full bg-white"></div>
                      </motion.button>
                    ) : (
                      <motion.button
                        className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          isRecording
                            ? 'border-2 border-red-500'
                            : 'border-2 border-white'
                        }`}
                        onClick={isRecording ? stopRecording : startRecording}
                        whileTap={{ scale: 0.95 }}
                      >
                        <div
                          className={`${
                            isRecording
                              ? 'w-5 h-5 rounded-sm bg-red-500'
                              : 'w-10 h-10 rounded-full bg-red-500'
                          }`}
                        ></div>
                      </motion.button>
                    )}

                    {/* Empty space for balance */}
                    <div className="w-10 h-10"></div>
                  </div>
                </div>
              </div>

              {/* Bottom spacing for PullIndicator - 40px (10rem) */}
              <div className="h-10"></div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Camera;
