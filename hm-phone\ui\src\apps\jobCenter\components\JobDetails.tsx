import React, { useState } from 'react';
import { Job, JobGroup } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';
import CreateGroupForm from './CreateGroupForm';
import GroupList from './GroupList';

interface JobDetailsProps {
  job: Job;
  groups: JobGroup[];
  onBack: () => void;
  onCreateGroup: (
    jobId: number,
    name: string,
    isPrivate: boolean,
    password?: string,
    notes?: string,
    meetupLocation?: string
  ) => void;
  onJoinGroup: (groupId: number, password?: string) => void;
  onViewGroup: (groupId: number) => void;
  isInAnyGroup: boolean;
}

const JobDetails: React.FC<JobDetailsProps> = ({
  job,
  groups,
  onBack,
  onCreateGroup,
  onJoinGroup,
  onViewGroup,
  isInAnyGroup
}) => {
  const [showCreateGroupForm, setShowCreateGroupForm] = useState(false);
  const [view, setView] = useState<'details' | 'groups'>('details');

  const formatPayout = (job: Job): string => {
    const { amount, type } = job.payout;

    switch (type) {
      case 'PER_PERSON':
        return `$${amount} per person`;
      case 'SPLIT':
        return `$${amount} split between all members`;
      case 'FIXED':
        return `$${amount} fixed payout`;
      default:
        return `$${amount}`;
    }
  };

  // Use a static object instead of a function that returns dynamic class names
  const difficultyColorMap: Record<string, string> = {
    EASY: 'text-green-400',
    MEDIUM: 'text-yellow-400',
    HARD: 'text-orange-400',
    EXPERT: 'text-red-400'
  };

  const handleCreateGroup = (
    name: string,
    isPrivate: boolean,
    password?: string,
    notes?: string,
    meetupLocation?: string
  ) => {
    onCreateGroup(job.id, name, isPrivate, password, notes, meetupLocation);
    setShowCreateGroupForm(false);
  };

  if (showCreateGroupForm) {
    return (
      <CreateGroupForm
        job={job}
        onSubmit={handleCreateGroup}
        onCancel={() => setShowCreateGroupForm(false)}
      />
    );
  }

  if (view === 'groups') {
    return (
      <GroupList
        groups={groups}
        job={job}
        onBack={() => setView('details')}
        onJoinGroup={onJoinGroup}
        onViewGroup={onViewGroup}
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header with image */}
      <div className="relative">
        {job.imageUrl ? (
          <div className="h-40 overflow-hidden">
            <img src={job.imageUrl} alt={job.title} className="w-full h-full object-cover" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
          </div>
        ) : (
          <div className="h-20 bg-gradient-to-r from-blue-500/30 to-purple-500/30"></div>
        )}

        <button
          onClick={onBack}
          className="absolute top-3 left-3 w-8 h-8 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
      </div>

      {/* Job details */}
      <div className={`flex-1 overflow-y-auto p-4 ${job.imageUrl ? 'mt-[-20px]' : ''}`}>
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-white text-xl font-medium">{job.title}</h2>
              <p className="text-white/70">{job.company}</p>
            </div>

            <span
              className={`${
                difficultyColorMap[job.difficulty] || 'text-white/70'
              } text-xs px-2 py-1 rounded-full bg-black/30`}
            >
              {job.difficulty}
            </span>
          </div>

          <div className="mt-4 grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-map-marker-alt text-white/50"></i>
              <span>{job.location}</span>
            </div>

            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-money-bill-wave text-white/50"></i>
              <span>{formatPayout(job)}</span>
            </div>

            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-users text-white/50"></i>
              <span>
                {job.minPlayers || 1}-{job.maxPlayers || '∞'} players
              </span>
            </div>

            <div className="flex items-center gap-2 text-white/70">
              <i className="fas fa-clock text-white/50"></i>
              <span>{job.duration}</span>
            </div>

            {job.isRecurring && (
              <div className="flex items-center gap-2 text-white/70">
                <i className="fas fa-sync-alt text-white/50"></i>
                <span>Recurring job</span>
              </div>
            )}

            {job.cooldown && (
              <div className="flex items-center gap-2 text-white/70">
                <i className="fas fa-hourglass-half text-white/50"></i>
                <span>{job.cooldown} min cooldown</span>
              </div>
            )}
          </div>

          <div className="mt-5">
            <h3 className="text-white font-medium mb-2">Description</h3>
            <p className="text-white/70 text-sm whitespace-pre-line">{job.description}</p>
          </div>

          {job.requirements && job.requirements.length > 0 && (
            <div className="mt-5">
              <h3 className="text-white font-medium mb-2">Requirements</h3>
              <ul className="text-white/70 text-sm space-y-1">
                {job.requirements.map((req: string, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <i className="fas fa-check-circle text-green-400 mt-1"></i>
                    <span>{req}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="mt-5">
            <h3 className="text-white font-medium mb-2">Active Groups</h3>
            <p className="text-white/70 text-sm">
              {groups.length === 0
                ? 'No active groups for this job. Create one to get started!'
                : `${groups.length} group${
                    groups.length !== 1 ? 's' : ''
                  } currently recruiting for this job.`}
            </p>
            <button
              onClick={() => setView('groups')}
              className="mt-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white/80 hover:text-white rounded-lg text-sm transition-colors"
            >
              <i className="fas fa-users mr-2"></i>
              View Groups
            </button>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="p-4 border-t border-white/10">
        {isInAnyGroup ? (
          <button
            disabled
            className="w-full py-3 rounded-lg bg-gray-500/20 text-gray-400 font-medium"
          >
            <i className="fas fa-info-circle mr-2"></i>
            Already in a group
          </button>
        ) : (
          <button
            onClick={() => setShowCreateGroupForm(true)}
            className="w-full py-3 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors"
          >
            <i className="fas fa-plus mr-2"></i>
            Create Group
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default JobDetails;
