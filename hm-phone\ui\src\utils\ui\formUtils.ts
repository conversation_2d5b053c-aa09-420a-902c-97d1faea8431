/**
 * Form Utilities
 *
 * This module provides utilities for form handling, validation, and input formatting.
 */

/**
 * Validate an email address
 * @param email - The email address to validate
 * @returns True if valid, false otherwise
 */
export function validateEmail(email: string): boolean {
  const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email);
}

/**
 * Validate a phone number
 * @param phone - The phone number to validate
 * @returns True if valid, false otherwise
 */
export function validatePhone(phone: string): boolean {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');
  // Check if the cleaned phone number has a valid length
  return cleaned.length >= 7 && cleaned.length <= 15;
}

/**
 * Validate a URL
 * @param url - The URL to validate
 * @returns True if valid, false otherwise
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate form input based on type
 * @param value - The value to validate
 * @param type - The type of validation to perform
 * @returns Validation result object
 */
export function validateInput(
  value: string,
  type: 'email' | 'phone' | 'url' | 'required' | 'minLength' | 'maxLength' | 'custom',
  options?: {
    minLength?: number;
    maxLength?: number;
    customValidator?: (value: string) => boolean;
    errorMessage?: string;
  }
): { isValid: boolean; errorMessage: string } {
  // Default error messages
  const defaultErrorMessages = {
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    url: 'Please enter a valid URL',
    required: 'This field is required',
    minLength: `Must be at least ${options?.minLength || 1} characters`,
    maxLength: `Must be no more than ${options?.maxLength || 100} characters`,
    custom: options?.errorMessage || 'Invalid input'
  };

  // Validation logic
  switch (type) {
    case 'email':
      return {
        isValid: validateEmail(value),
        errorMessage: defaultErrorMessages.email
      };
    case 'phone':
      return {
        isValid: validatePhone(value),
        errorMessage: defaultErrorMessages.phone
      };
    case 'url':
      return {
        isValid: validateUrl(value),
        errorMessage: defaultErrorMessages.url
      };
    case 'required':
      return {
        isValid: value.trim().length > 0,
        errorMessage: defaultErrorMessages.required
      };
    case 'minLength':
      return {
        isValid: value.length >= (options?.minLength || 1),
        errorMessage: defaultErrorMessages.minLength
      };
    case 'maxLength':
      return {
        isValid: value.length <= (options?.maxLength || 100),
        errorMessage: defaultErrorMessages.maxLength
      };
    case 'custom':
      return {
        isValid: options?.customValidator ? options.customValidator(value) : true,
        errorMessage: defaultErrorMessages.custom
      };
    default:
      return { isValid: true, errorMessage: '' };
  }
}

/**
 * Format input value based on type
 * @param value - The value to format
 * @param type - The type of formatting to apply
 * @returns Formatted value
 */
export function formatInputValue(
  value: string,
  type: 'phone' | 'currency' | 'uppercase' | 'lowercase' | 'capitalize' | 'trim'
): string {
  switch (type) {
    case 'phone': {
      // Format phone number as XXX-XXX-XXXX
      const cleaned = value.replace(/\D/g, '');
      if (cleaned.length <= 3) {
        return cleaned;
      } else if (cleaned.length <= 6) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
      } else {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      }
    }
    case 'currency': {
      // Format as currency (remove non-numeric, add decimal)
      const numericValue = value.replace(/[^0-9.]/g, '');
      // Ensure only one decimal point
      const parts = numericValue.split('.');
      if (parts.length > 2) {
        return `${parts[0]}.${parts.slice(1).join('')}`;
      }
      return numericValue;
    }
    case 'uppercase':
      return value.toUpperCase();
    case 'lowercase':
      return value.toLowerCase();
    case 'capitalize':
      return value
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    case 'trim':
      return value.trim();
    default:
      return value;
  }
}

/**
 * Create a form state handler with validation
 * @param initialValues - Initial form values
 * @param validators - Validation functions for each field
 * @returns Form state handler
 */
export function createFormState<T extends Record<string, unknown>>(
  initialValues: T,
  validators?: Partial<
    Record<keyof T, (value: unknown) => { isValid: boolean; errorMessage: string }>
  >
) {
  return {
    values: { ...initialValues },
    errors: {} as Record<keyof T, string>,
    touched: {} as Record<keyof T, boolean>,

    handleChange: function (field: keyof T, value: unknown) {
      this.values[field] = value as T[keyof T];
      this.touched[field] = true;

      // Validate if validator exists
      if (validators && validators[field]) {
        const validation = validators[field]!(value);
        this.errors[field] = validation.isValid ? '' : validation.errorMessage;
      }

      return this;
    },

    isValid: function () {
      // Check if all validators pass
      if (!validators) return true;

      let isValid = true;
      for (const field in validators) {
        if (Object.prototype.hasOwnProperty.call(validators, field)) {
          const validation = validators[field]!(this.values[field]);
          if (!validation.isValid) {
            isValid = false;
            this.errors[field] = validation.errorMessage;
          }
        }
      }

      return isValid;
    },

    reset: function () {
      this.values = { ...initialValues };
      this.errors = {} as Record<keyof T, string>;
      this.touched = {} as Record<keyof T, boolean>;

      return this;
    }
  };
}
