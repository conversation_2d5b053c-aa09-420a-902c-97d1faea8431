import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { ItemSlot } from '@shared/inventory.types';
import { CraftingRecipe, CraftingQueueItem, CraftingStationData } from '@shared/crafting.types';
import { getItemDefinition } from '../../../scripts/shared/items/items';

export interface CraftingState {
  stationInventory: ItemSlot[];
  craftingQueue: CraftingQueueItem[];
  selectedRecipe: CraftingRecipe | null;
  searchTerm: string;
  currentStation: CraftingStationData | null;
  availableRecipes: CraftingRecipe[];
  isProcessingQueue: boolean;
}

export interface CraftingActions {
  // Station inventory management
  updateStationInventory: (items: ItemSlot[]) => void;
  addItemToStation: (item: ItemSlot) => void;
  addItemToSlot: (item: ItemSlot, destIndex: number) => void;
  removeItemFromStation: (index: number) => void;
  moveItem: (fromIndex: number, toIndex: number) => void;
  
  // Queue management
  addToQueue: (recipe: CraftingRecipe, quantity?: number) => void;
  removeFromQueue: (queueItemId: string) => void;
  clearQueue: () => void;
  updateQueueProgress: () => void;
  
  // Recipe selection and search
  setSelectedRecipe: (recipe: CraftingRecipe | null) => void;
  setSearchTerm: (term: string) => void;
  
  // Station management
  setCurrentStation: (station: CraftingStationData | null) => void;
  updateAvailableRecipes: (recipes: CraftingRecipe[]) => void;
  
  // Crafting operations
  canCraftRecipe: (recipe: CraftingRecipe, playerInventory: ItemSlot[]) => boolean;
  getRequiredQuantity: (itemId: string, recipe: CraftingRecipe) => number;
  getAvailableQuantity: (itemId: string, playerInventory: ItemSlot[]) => number;
  
  // Queue processing control
  startQueueProcessing: () => void;
  stopQueueProcessing: () => void;
  
  // Initialization
  initializeCrafting: () => void;
  resetCraftingState: () => void;
}

// Sample crafting station inventory data
const createSampleStationInventory = (): ItemSlot[] => {
  const createItemSlot = (itemId: string, quantity: number, index: number): ItemSlot | null => {
    const definition = getItemDefinition(itemId);
    if (!definition) return null;
    
    return {
      index,
      item: {
        instanceId: `crafting_${itemId}_${index}`,
        id: itemId,
        name: definition.name,
        definitionId: itemId,
        label: definition.label,
        type: definition.type,
        icon: definition.icon,
        stackable: definition.stackable,
        maxStack: definition.maxStack,
        weight: definition.weight,
        description: definition.description,
        quantity
      },
      quantity
    };
  };

  const slots: ItemSlot[] = [];
  
  // Create sample inventory items
  const sampleItems = [
    { itemId: 'bread', quantity: 25 },
    { itemId: 'metal_fragment', quantity: 10 },
    { itemId: 'wood_plank', quantity: 50 }
  ];

  sampleItems.forEach((item, index) => {
    const slot = createItemSlot(item.itemId, item.quantity, index);
    if (slot) slots.push(slot);
  });

  return slots;
};

export const useCraftingStore = create<CraftingState & CraftingActions>()(
  immer((set, get) => ({
    // Initial state
    stationInventory: [],
    craftingQueue: [],
    selectedRecipe: null,
    searchTerm: '',
    currentStation: null,
    availableRecipes: [],
    isProcessingQueue: false,    // Station inventory management
    updateStationInventory: (items) => 
      set((state) => {
        state.stationInventory = items;
      }),    addItemToStation: (item) =>
      set((state) => {
        // Check if there's already an item at the destination index
        const existingItemAtDestination = state.stationInventory.find(slot => slot.index === item.index);
        
        if (existingItemAtDestination) {
          // If item at destination is the same type and stackable, stack them
          if (existingItemAtDestination.item?.definitionId === item.item?.definitionId && item.item?.stackable) {
            existingItemAtDestination.quantity = 
              (existingItemAtDestination.quantity || 0) + (item.quantity || 1);
          } else {
            // Find an empty slot or push to end if destination is occupied by different item
            const emptySlotIndex = Math.max(...state.stationInventory.map(slot => slot.index), -1) + 1;
            state.stationInventory.push({ ...item, index: emptySlotIndex });
          }
        } else {
          // Destination slot is empty, place item there
          state.stationInventory.push({ ...item, index: item.index });
        }      }),

    addItemToSlot: (item, destIndex) =>
      set((state) => {
        // Check if there's already an item at the destination index
        const existingItemAtDestination = state.stationInventory.find(slot => slot.index === destIndex);
        
        if (existingItemAtDestination) {
          // If item at destination is the same type and stackable, stack them
          if (existingItemAtDestination.item?.definitionId === item.item?.definitionId && item.item?.stackable) {
            existingItemAtDestination.quantity = 
              (existingItemAtDestination.quantity || 0) + (item.quantity || 1);
          } else {
            // Different item types - this should trigger a swap operation instead
            // For now, we'll reject the operation (this should be handled by swapping logic)
            console.warn('Attempting to place different item type in occupied slot');
          }
        } else {
          // Destination slot is empty, place item there
          state.stationInventory.push({ ...item, index: destIndex });
        }
      }),

    removeItemFromStation: (index) =>
      set((state) => {
        state.stationInventory = state.stationInventory.filter(item => item.index !== index);
      }),

    // Reorder items within crafting station
    moveItem: (fromIndex: number, toIndex: number) =>
      set((state) => {
        const items = [...state.stationInventory];
        const fromItem = items.find(item => item.index === fromIndex);
        const toItem = items.find(item => item.index === toIndex);
        if (fromItem && toItem) {
          fromItem.index = toIndex;
          toItem.index = fromIndex;
        } else if (fromItem && !toItem) {
          // Move to empty slot
          fromItem.index = toIndex;
        }
        state.stationInventory = items;
      }),

    // Queue management
    addToQueue: (recipe, quantity = 1) =>
      set((state) => {
        const queueItem: CraftingQueueItem = {
          id: `craft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          recipe,
          quantity,
          startTime: Date.now(),
          duration: recipe.craftingTime * 1000,
          progress: 0
        };
        state.craftingQueue.push(queueItem);
      }),

    removeFromQueue: (queueItemId) =>
      set((state) => {
        state.craftingQueue = state.craftingQueue.filter(item => item.id !== queueItemId);
      }),

    clearQueue: () =>
      set((state) => {
        state.craftingQueue = [];
      }),

    updateQueueProgress: () =>
      set((state) => {
        state.craftingQueue = state.craftingQueue
          .map(item => {
            const elapsed = Date.now() - item.startTime;
            const progress = Math.min(elapsed / item.duration, 1);
            return { ...item, progress };
          })
          .filter(item => item.progress < 1); // Remove completed items
      }),

    // Recipe and search management
    setSelectedRecipe: (recipe) =>
      set((state) => {
        state.selectedRecipe = recipe;
      }),

    setSearchTerm: (term) =>
      set((state) => {
        state.searchTerm = term;
      }),

    // Station management
    setCurrentStation: (station) =>
      set((state) => {
        state.currentStation = station;
        if (station) {
          state.stationInventory = station.inventory || [];
          state.craftingQueue = station.currentQueue || [];
        }
      }),

    updateAvailableRecipes: (recipes) =>
      set((state) => {
        state.availableRecipes = recipes;
      }),    // Crafting logic
    canCraftRecipe: (recipe, playerInventory) => {
      const state = get();
      return recipe.ingredients.every(ingredient => {
        const availableQuantity = state.getAvailableQuantity(ingredient.itemId, playerInventory);
        return availableQuantity >= ingredient.quantity;
      });
    },

    getRequiredQuantity: (itemId, recipe) => {
      const ingredient = recipe.ingredients.find(ing => ing.itemId === itemId);
      return ingredient ? ingredient.quantity : 0;
    },

    getAvailableQuantity: (itemId, playerInventory) => {
      const state = get();
      
      // Check player inventory
      const playerQuantity = playerInventory
        .filter(slot => slot.item?.definitionId === itemId)
        .reduce((total, slot) => total + (slot.quantity || 0), 0);
      
      // Check station inventory
      const stationQuantity = state.stationInventory
        .filter(slot => slot.item?.definitionId === itemId)
        .reduce((total, slot) => total + (slot.quantity || 0), 0);
      
      return playerQuantity + stationQuantity;
    },

    // Queue processing control
    startQueueProcessing: () =>
      set((state) => {
        state.isProcessingQueue = true;
      }),

    stopQueueProcessing: () =>
      set((state) => {
        state.isProcessingQueue = false;
      }),

    // Initialization and reset
    initializeCrafting: () =>
      set((state) => {
        state.stationInventory = createSampleStationInventory();
        state.craftingQueue = [];
        state.selectedRecipe = null;
        state.searchTerm = '';
        state.isProcessingQueue = false;
      }),

    resetCraftingState: () =>
      set((state) => {
        state.stationInventory = [];
        state.craftingQueue = [];
        state.selectedRecipe = null;
        state.searchTerm = '';
        state.currentStation = null;
        state.availableRecipes = [];
        state.isProcessingQueue = false;
      }),
  }))
);
