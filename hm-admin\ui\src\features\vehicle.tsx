import { useEffect, useMemo, useState } from 'react';
import { sendNuiMessage, onNuiEvent, NuiActions, NuiMessageTypes } from '../utils/nui';
import { isBrowser } from '../utils/environment';
import { useAdmin } from '../stores/adminStore';

// Vehicle spawn feature component
export const SpawnVehicleFeature: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVehicle, setSelectedVehicle] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  // Use the admin store
  const { vehicles, isLoading, fetchVehicles } = useAdmin();
  
  // Convert vehicles array to strings for easier filtering
  const vehicleList = useMemo(() => {
    return vehicles.map(vehicle => 
      typeof vehicle === 'string' ? vehicle : vehicle.model || vehicle.name || vehicle.toString()
    );
  }, [vehicles]);
  
  // Fetch vehicle list on mount if not already loaded
  useEffect(() => {
    if (vehicles.length === 0) {
      fetchVehicles();
    }
  }, [vehicles, fetchVehicles]);

  const filteredVehicles = vehicleList.filter(vehicle =>
    vehicle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.vehicle-dropdown')) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleVehicleSelect = (vehicle: string) => {
    setSelectedVehicle(vehicle);
    setSearchTerm(vehicle);
    setIsDropdownOpen(false);
  };

  const handleSpawn = () => {
    if (selectedVehicle) {
      console.log('Spawning vehicle:', selectedVehicle);
      // Send NUI message to spawn vehicle using new NUI system (now starts ghost placement)
      sendNuiMessage(NuiActions.SPAWN_VEHICLE, { model: selectedVehicle });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setSelectedVehicle(value);
    setIsDropdownOpen(true);
  };
  return (
    <div className="space-y-2">
      
      {/* Inline container for dropdown and buttons */}
      <div className="flex gap-1">
        <div className="relative vehicle-dropdown flex-1">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={handleInputChange}
              onFocus={() => setIsDropdownOpen(true)}
              placeholder="Search or select vehicle..."
              className="w-full px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-200"
            >
              <i className={`fas fa-chevron-${isDropdownOpen ? 'up' : 'down'} text-xs`} />
            </button>
          </div>
            {/* Dropdown with proper positioning and overflow handling */}
          {isDropdownOpen && (
            <div className="fixed z-[9999] bg-neutral-800 border border-neutral-600 rounded shadow-lg max-h-32 overflow-y-auto"
                 style={{
                   top: (document.querySelector('.vehicle-dropdown input') as HTMLElement)?.getBoundingClientRect()?.bottom + 2 || 0,
                   left: (document.querySelector('.vehicle-dropdown') as HTMLElement)?.getBoundingClientRect()?.left || 0,
                   width: (document.querySelector('.vehicle-dropdown') as HTMLElement)?.getBoundingClientRect()?.width || 'auto'
                 }}>              {isLoading.vehicles ? (
                <div className="px-2 py-1 text-xs text-neutral-400 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-neutral-400"></div>
                  Loading vehicles...
                </div>
              ) : (                <>
                  {filteredVehicles.map((vehicle) => (
                    <button
                      key={vehicle}
                      onClick={() => handleVehicleSelect(vehicle)}
                      className="w-full px-2 py-1 text-left text-xs text-neutral-200 hover:bg-neutral-700 transition-colors"
                    >
                      {vehicle}
                    </button>
                  ))}
                  {filteredVehicles.length === 0 && !isLoading.vehicles && (
                    <div className="px-2 py-1 text-xs text-neutral-400">No vehicles found</div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
        
        <button
          onClick={handleSpawn}
          disabled={!selectedVehicle}
          className="px-2 py-1 bg-green-600 hover:bg-green-500 disabled:bg-neutral-600 disabled:cursor-not-allowed text-white rounded transition-colors text-xs font-medium whitespace-nowrap"
          title="Spawn vehicle with ghost placement - move mouse to position, A/D to rotate, ENTER to confirm, ESC to cancel"
        >
          Spawn
        </button>
      </div>
    </div>
  );
};

// Vehicle repair feature component
export const RepairVehicleFeature: React.FC = () => {
  const handleRepair = () => {
    console.log('Repairing vehicle');
    // Send NUI message to repair vehicle using new NUI system
    sendNuiMessage(NuiActions.REPAIR_VEHICLE);
  };

  return (
    <div>
      <button
        onClick={handleRepair}
        className="w-full px-2 py-1 bg-blue-600 hover:bg-blue-500 text-white rounded text-xs font-medium"
      >
        Repair Vehicle
      </button>
    </div>
  );
};

// Vehicle delete feature component
export const DeleteVehicleFeature: React.FC = () => {
  const handleDelete = () => {
    console.log('Deleting vehicle');
    // Send NUI message to delete vehicle using new NUI system
    sendNuiMessage(NuiActions.DELETE_VEHICLE);
  };

  return (
    <div>
      <button
        onClick={handleDelete}
        className="w-full px-2 py-1 bg-red-600 hover:bg-red-500 text-white rounded text-xs font-medium"
      >
        Delete Vehicle
      </button>
    </div>
  );
};

// Vehicle color feature component
export const VehicleColorFeature: React.FC = () => {
  const [colorType, setColorType] = useState<'primary' | 'secondary'>('primary');
  const [colors, setColors] = useState<Array<{ID: string, Description: string, Hex: string, RGB: string, Category: string}>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedColorId, setSelectedColorId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  // Function to process colors and extract categories
  const processColors = (colorsList: Array<{ID: string, Description: string, Hex: string, RGB: string}>) => {
    // Process and categorize the colors based on official FiveM docs
    const categorizedColors = colorsList.map(color => {
      const id = parseInt(color.ID);
      let category = 'Normal'; // Default category for unnamed/normal colors
      
      // Metallic colors
      if ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 
           38, 49, 50, 51, 52, 53, 54, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 
           74, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 
           106, 107, 111, 112, 135, 136, 137, 138, 141, 142, 143, 145, 147, 150].includes(id)) {
        category = 'Metallic';
      } 
      // Matte colors
      else if ([12, 13, 14, 39, 40, 41, 42, 55, 82, 83, 84, 128, 131, 148, 149, 151, 152, 154, 155].includes(id)) {
        category = 'Matte';
      } 
      // Metals
      else if ([117, 118, 119, 158, 159].includes(id)) {
        category = 'Metals';
      }
      
      return { ...color, Category: category };
    });
    
    // Extract unique categories and sort them in the official order
    const categoryOrder = ['All', 'Metallic', 'Matte', 'Metals', 'Normal'];
    const uniqueCategories = ['All', ...new Set(categorizedColors.map(c => c.Category))];
    
    // Sort categories according to the predefined order
    const sortedCategories = uniqueCategories.sort((a, b) => {
      return categoryOrder.indexOf(a) - categoryOrder.indexOf(b);
    });
    
    setCategories(sortedCategories);
    
    return categorizedColors;
  };

  // Fetch colors on component mount
  useEffect(() => {
    const fetchColors = async () => {
      try {
        setIsLoading(true);
        
        // In a real browser environment, we'd fetch from an endpoint
        // In FiveM NUI, we need to access the data differently
        if (isBrowser()) {
          // Load all 160 colors from the complete vehicle colors dataset
          const allColors = [
            {ID: "0", Description: "Metallic Black", Hex: "#0d1116", RGB: "13, 17, 22"},
            {ID: "1", Description: "Metallic Graphite Black", Hex: "#1c1d21", RGB: "28, 29, 33"},
            {ID: "2", Description: "Metallic Black Steal", Hex: "#32383d", RGB: "50, 56, 61"},
            {ID: "3", Description: "Metallic Dark Silver", Hex: "#454b4f", RGB: "69, 75, 79"},
            {ID: "4", Description: "Metallic Silver", Hex: "#999da0", RGB: "153, 157, 160"},
            {ID: "5", Description: "Metallic Blue Silver", Hex: "#c2c4c6", RGB: "194, 196, 198"},
            {ID: "6", Description: "Metallic Steel Gray", Hex: "#979a97", RGB: "151, 154, 151"},
            {ID: "7", Description: "Metallic Shadow Silver", Hex: "#637380", RGB: "99, 115, 128"},
            {ID: "8", Description: "Metallic Stone Silver", Hex: "#63625c", RGB: "99, 98, 92"},
            {ID: "9", Description: "Metallic Midnight Silver", Hex: "#3c3f47", RGB: "60, 63, 71"},
            {ID: "10", Description: "Metallic Gun Metal", Hex: "#444e54", RGB: "68, 78, 84"},
            {ID: "11", Description: "Metallic Anthracite Grey", Hex: "#1d2129", RGB: "29, 33, 41"},
            {ID: "12", Description: "Matte Black", Hex: "#13181f", RGB: "19, 24, 31"},
            {ID: "13", Description: "Matte Gray", Hex: "#26282a", RGB: "38, 40, 42"},
            {ID: "14", Description: "Matte Light Grey", Hex: "#515554", RGB: "81, 85, 84"},
            {ID: "15", Description: "Util Black", Hex: "#151921", RGB: "21, 25, 33"},
            {ID: "16", Description: "Util Black Poly", Hex: "#1e2429", RGB: "30, 36, 41"},
            {ID: "17", Description: "Util Dark silver", Hex: "#333a3c", RGB: "51, 58, 60"},
            {ID: "18", Description: "Util Silver", Hex: "#8c9095", RGB: "140, 144, 149"},
            {ID: "19", Description: "Util Gun Metal", Hex: "#39434d", RGB: "57, 67, 77"},
            {ID: "20", Description: "Util Shadow Silver", Hex: "#506272", RGB: "80, 98, 114"},
            {ID: "21", Description: "Worn Black", Hex: "#1e232f", RGB: "30, 35, 47"},
            {ID: "22", Description: "Worn Graphite", Hex: "#363a3f", RGB: "54, 58, 63"},
            {ID: "23", Description: "Worn Silver Grey", Hex: "#a0a199", RGB: "160, 161, 153"},
            {ID: "24", Description: "Worn Silver", Hex: "#d3d3d3", RGB: "211, 211, 211"},
            {ID: "25", Description: "Worn Blue Silver", Hex: "#b7bfca", RGB: "183, 191, 202"},
            {ID: "26", Description: "Worn Shadow Silver", Hex: "#778794", RGB: "119, 135, 148"},
            {ID: "27", Description: "Metallic Red", Hex: "#c00e1a", RGB: "192, 14, 26"},
            {ID: "28", Description: "Metallic Torino Red", Hex: "#da1918", RGB: "218, 25, 24"},
            {ID: "29", Description: "Metallic Formula Red", Hex: "#b6111b", RGB: "182, 17, 27"},
            {ID: "30", Description: "Metallic Blaze Red", Hex: "#a51e23", RGB: "165, 30, 35"},
            {ID: "31", Description: "Metallic Graceful Red", Hex: "#7b1a22", RGB: "123, 26, 34"},
            {ID: "32", Description: "Metallic Garnet Red", Hex: "#8e1b1f", RGB: "142, 27, 31"},
            {ID: "33", Description: "Metallic Desert Red", Hex: "#6f1818", RGB: "111, 24, 24"},
            {ID: "34", Description: "Metallic Cabernet Red", Hex: "#49111d", RGB: "73, 17, 29"},
            {ID: "35", Description: "Metallic Candy Red", Hex: "#b60f25", RGB: "182, 15, 37"},
            {ID: "36", Description: "Metallic Sunrise Orange", Hex: "#d44a17", RGB: "212, 74, 23"},
            {ID: "37", Description: "Metallic Classic Gold", Hex: "#c2944f", RGB: "194, 148, 79"},
            {ID: "38", Description: "Metallic Orange", Hex: "#f78616", RGB: "247, 134, 22"},
            {ID: "39", Description: "Matte Red", Hex: "#cf1f21", RGB: "207, 31, 33"},
            {ID: "40", Description: "Matte Dark Red", Hex: "#732021", RGB: "115, 32, 33"},
            {ID: "41", Description: "Matte Orange", Hex: "#f27d20", RGB: "242, 125, 32"},
            {ID: "42", Description: "Matte Yellow", Hex: "#ffc91f", RGB: "255, 201, 31"},
            {ID: "43", Description: "Util Red", Hex: "#9c1016", RGB: "156, 16, 22"},
            {ID: "44", Description: "Util Bright Red", Hex: "#de0f18", RGB: "222, 15, 24"},
            {ID: "45", Description: "Util Garnet Red", Hex: "#8f1e17", RGB: "143, 30, 23"},
            {ID: "46", Description: "Worn Red", Hex: "#a94744", RGB: "169, 71, 68"},
            {ID: "47", Description: "Worn Golden Red", Hex: "#b16c51", RGB: "177, 108, 81"},
            {ID: "48", Description: "Worn Dark Red", Hex: "#371c25", RGB: "55, 28, 37"},
            {ID: "49", Description: "Metallic Dark Green", Hex: "#132428", RGB: "19, 36, 40"},
            {ID: "50", Description: "Metallic Racing Green", Hex: "#122e2b", RGB: "18, 46, 43"},
            {ID: "51", Description: "Metallic Sea Green", Hex: "#12383c", RGB: "18, 56, 60"},
            {ID: "52", Description: "Metallic Olive Green", Hex: "#31423f", RGB: "49, 66, 63"},
            {ID: "53", Description: "Metallic Green", Hex: "#155c2d", RGB: "21, 92, 45"},
            {ID: "54", Description: "Metallic Gasoline Blue Green", Hex: "#1b6770", RGB: "27, 103, 112"},
            {ID: "55", Description: "Matte Lime Green", Hex: "#66b81f", RGB: "102, 184, 31"},
            {ID: "56", Description: "Util Dark Green", Hex: "#22383e", RGB: "34, 56, 62"},
            {ID: "57", Description: "Util Green", Hex: "#1d5a3f", RGB: "29, 90, 63"},
            {ID: "58", Description: "Worn Dark Green", Hex: "#2d423f", RGB: "45, 66, 63"},
            {ID: "59", Description: "Worn Green", Hex: "#45594b", RGB: "69, 89, 75"},
            {ID: "60", Description: "Worn Sea Wash", Hex: "#65867f", RGB: "101, 134, 127"},
            {ID: "61", Description: "Metallic Midnight Blue", Hex: "#222e46", RGB: "34, 46, 70"},
            {ID: "62", Description: "Metallic Dark Blue", Hex: "#233155", RGB: "35, 49, 85"},
            {ID: "63", Description: "Metallic Saxony Blue", Hex: "#304c7e", RGB: "48, 76, 126"},
            {ID: "64", Description: "Metallic Blue", Hex: "#47578f", RGB: "71, 87, 143"},
            {ID: "65", Description: "Metallic Mariner Blue", Hex: "#637ba7", RGB: "99, 123, 167"},
            {ID: "66", Description: "Metallic Harbor Blue", Hex: "#394762", RGB: "57, 71, 98"},
            {ID: "67", Description: "Metallic Diamond Blue", Hex: "#d6e7f1", RGB: "214, 231, 241"},
            {ID: "68", Description: "Metallic Surf Blue", Hex: "#76afbe", RGB: "118, 175, 190"},
            {ID: "69", Description: "Metallic Nautical Blue", Hex: "#345e72", RGB: "52, 94, 114"},
            {ID: "70", Description: "Metallic Bright Blue", Hex: "#0b9cf1", RGB: "11, 156, 241"},
            {ID: "71", Description: "Metallic Purple Blue", Hex: "#2f2d52", RGB: "47, 45, 82"},
            {ID: "72", Description: "Metallic Spinnaker Blue", Hex: "#282c4d", RGB: "40, 44, 77"},
            {ID: "73", Description: "Metallic Ultra Blue", Hex: "#2354a1", RGB: "35, 84, 161"},
            {ID: "74", Description: "Metallic Bright Blue", Hex: "#6ea3c6", RGB: "110, 163, 198"},
            {ID: "75", Description: "Util Dark Blue", Hex: "#112552", RGB: "17, 37, 82"},
            {ID: "76", Description: "Util Midnight Blue", Hex: "#1b203e", RGB: "27, 32, 62"},
            {ID: "77", Description: "Util Blue", Hex: "#275190", RGB: "39, 81, 144"},
            {ID: "78", Description: "Util Sea Foam Blue", Hex: "#608592", RGB: "96, 133, 146"},
            {ID: "79", Description: "Util Lightning blue", Hex: "#2446a8", RGB: "36, 70, 168"},
            {ID: "80", Description: "Util Maui Blue Poly", Hex: "#4271e1", RGB: "66, 113, 225"},
            {ID: "81", Description: "Util Bright Blue", Hex: "#3b39e0", RGB: "59, 57, 224"},
            {ID: "82", Description: "Matte Dark Blue", Hex: "#1f2852", RGB: "31, 40, 82"},
            {ID: "83", Description: "Matte Blue", Hex: "#253aa7", RGB: "37, 58, 167"},
            {ID: "84", Description: "Matte Midnight Blue", Hex: "#1c3551", RGB: "28, 53, 81"},
            {ID: "85", Description: "Worn Dark blue", Hex: "#4c5f81", RGB: "76, 95, 129"},
            {ID: "86", Description: "Worn Blue", Hex: "#58688e", RGB: "88, 104, 142"},
            {ID: "87", Description: "Worn Light blue", Hex: "#74b5d8", RGB: "116, 181, 216"},
            {ID: "88", Description: "Metallic Taxi Yellow", Hex: "#ffcf20", RGB: "255, 207, 32"},
            {ID: "89", Description: "Metallic Race Yellow", Hex: "#fbe212", RGB: "251, 226, 18"},
            {ID: "90", Description: "Metallic Bronze", Hex: "#916532", RGB: "145, 101, 50"},
            {ID: "91", Description: "Metallic Yellow Bird", Hex: "#e0e13d", RGB: "224, 225, 61"},
            {ID: "92", Description: "Metallic Lime", Hex: "#98d223", RGB: "152, 210, 35"},
            {ID: "93", Description: "Metallic Champagne", Hex: "#9b8c78", RGB: "155, 140, 120"},
            {ID: "94", Description: "Metallic Pueblo Beige", Hex: "#503218", RGB: "80, 50, 24"},
            {ID: "95", Description: "Metallic Dark Ivory", Hex: "#473f2b", RGB: "71, 63, 43"},
            {ID: "96", Description: "Metallic Choco Brown", Hex: "#221b19", RGB: "34, 27, 25"},
            {ID: "97", Description: "Metallic Golden Brown", Hex: "#653f23", RGB: "101, 63, 35"},
            {ID: "98", Description: "Metallic Light Brown", Hex: "#775c3e", RGB: "119, 92, 62"},
            {ID: "99", Description: "Metallic Straw Beige", Hex: "#ac9975", RGB: "172, 153, 117"},
            {ID: "100", Description: "Metallic Moss Brown", Hex: "#6c6b4b", RGB: "108, 107, 75"},
            {ID: "101", Description: "Metallic Biston Brown", Hex: "#402e2b", RGB: "64, 46, 43"},
            {ID: "102", Description: "Metallic Beechwood", Hex: "#a4965f", RGB: "164, 150, 95"},
            {ID: "103", Description: "Metallic Dark Beechwood", Hex: "#46231a", RGB: "70, 35, 26"},
            {ID: "104", Description: "Metallic Choco Orange", Hex: "#752b19", RGB: "117, 43, 25"},
            {ID: "105", Description: "Metallic Beach Sand", Hex: "#bfae7b", RGB: "191, 174, 123"},
            {ID: "106", Description: "Metallic Sun Bleeched Sand", Hex: "#dfd5b2", RGB: "223, 213, 178"},
            {ID: "107", Description: "Metallic Cream", Hex: "#f7edd5", RGB: "247, 237, 213"},
            {ID: "108", Description: "Util Brown", Hex: "#3a2a1b", RGB: "58, 42, 27"},
            {ID: "109", Description: "Util Medium Brown", Hex: "#785f33", RGB: "120, 95, 51"},
            {ID: "110", Description: "Util Light Brown", Hex: "#b5a079", RGB: "181, 160, 121"},
            {ID: "111", Description: "Metallic White", Hex: "#fffff6", RGB: "255, 255, 246"},
            {ID: "112", Description: "Metallic Frost White", Hex: "#eaeaea", RGB: "234, 234, 234"},
            {ID: "113", Description: "Worn Honey Beige", Hex: "#b0ab94", RGB: "176, 171, 148"},
            {ID: "114", Description: "Worn Brown", Hex: "#453831", RGB: "69, 56, 49"},
            {ID: "115", Description: "Worn Dark Brown", Hex: "#2a282b", RGB: "42, 40, 43"},
            {ID: "116", Description: "Worn straw beige", Hex: "#726c57", RGB: "114, 108, 87"},
            {ID: "117", Description: "Brushed Steel", Hex: "#6a747c", RGB: "106, 116, 124"},
            {ID: "118", Description: "Brushed Black steel", Hex: "#354158", RGB: "53, 65, 88"},
            {ID: "119", Description: "Brushed Aluminium", Hex: "#9ba0a8", RGB: "155, 160, 168"},
            {ID: "120", Description: "Chrome", Hex: "#5870a1", RGB: "88, 112, 161"},
            {ID: "121", Description: "Worn Off White", Hex: "#eae6de", RGB: "234, 230, 222"},
            {ID: "122", Description: "Util Off White", Hex: "#dfddd0", RGB: "223, 221, 208"},
            {ID: "123", Description: "Worn Orange", Hex: "#f2ad2e", RGB: "242, 173, 46"},
            {ID: "124", Description: "Worn Light Orange", Hex: "#f9a458", RGB: "249, 164, 88"},
            {ID: "125", Description: "Metallic Securicor Green", Hex: "#83c566", RGB: "131, 197, 102"},
            {ID: "126", Description: "Worn Taxi Yellow", Hex: "#f1cc40", RGB: "241, 204, 64"},
            {ID: "127", Description: "police car blue", Hex: "#4cc3da", RGB: "76, 195, 218"},
            {ID: "128", Description: "Matte Green", Hex: "#4e6443", RGB: "78, 100, 67"},
            {ID: "129", Description: "Matte Brown", Hex: "#bcac8f", RGB: "188, 172, 143"},
            {ID: "130", Description: "Worn Orange", Hex: "#f8b658", RGB: "248, 182, 88"},
            {ID: "131", Description: "Matte White", Hex: "#fcf9f1", RGB: "252, 249, 241"},
            {ID: "132", Description: "Worn White", Hex: "#fffffb", RGB: "255, 255, 251"},
            {ID: "133", Description: "Worn Olive Army Green", Hex: "#81844c", RGB: "129, 132, 76"},
            {ID: "134", Description: "Pure White", Hex: "#ffffff", RGB: "255, 255, 255"},
            {ID: "135", Description: "Hot Pink", Hex: "#f21f99", RGB: "242, 31, 153"},
            {ID: "136", Description: "Salmon pink", Hex: "#fdd6cd", RGB: "253, 214, 205"},
            {ID: "137", Description: "Metallic Vermillion Pink", Hex: "#df5891", RGB: "223, 88, 145"},
            {ID: "138", Description: "Orange", Hex: "#f6ae20", RGB: "246, 174, 32"},
            {ID: "139", Description: "Green", Hex: "#b0ee6e", RGB: "176, 238, 110"},
            {ID: "140", Description: "Blue", Hex: "#08e9fa", RGB: "8, 233, 250"},
            {ID: "141", Description: "Mettalic Black Blue", Hex: "#0a0c17", RGB: "10, 12, 23"},
            {ID: "142", Description: "Metallic Black Purple", Hex: "#0c0d18", RGB: "12, 13, 24"},
            {ID: "143", Description: "Metallic Black Red", Hex: "#0e0d14", RGB: "14, 13, 20"},
            {ID: "144", Description: "hunter green", Hex: "#9f9e8a", RGB: "159, 158, 138"},
            {ID: "145", Description: "Metallic Purple", Hex: "#621276", RGB: "98, 18, 118"},
            {ID: "146", Description: "Metaillic V Dark Blue", Hex: "#0b1421", RGB: "11, 20, 33"},
            {ID: "147", Description: "MODSHOP BLACK1", Hex: "#11141a", RGB: "17, 20, 26"},
            {ID: "148", Description: "Matte Purple", Hex: "#6b1f7b", RGB: "107, 31, 123"},
            {ID: "149", Description: "Matte Dark Purple", Hex: "#1e1d22", RGB: "30, 29, 34"},
            {ID: "150", Description: "Metallic Lava Red", Hex: "#bc1917", RGB: "188, 25, 23"},
            {ID: "151", Description: "Matte Forest Green", Hex: "#2d362a", RGB: "45, 54, 42"},
            {ID: "152", Description: "Matte Olive Drab", Hex: "#696748", RGB: "105, 103, 72"},
            {ID: "153", Description: "Matte Desert Brown", Hex: "#7a6c55", RGB: "122, 108, 85"},
            {ID: "154", Description: "Matte Desert Tan", Hex: "#c3b492", RGB: "195, 180, 146"},
            {ID: "155", Description: "Matte Foilage Green", Hex: "#5a6352", RGB: "90, 99, 82"},
            {ID: "156", Description: "DEFAULT ALLOY COLOR", Hex: "#81827f", RGB: "129, 130, 127"},
            {ID: "157", Description: "Epsilon Blue", Hex: "#afd6e4", RGB: "175, 214, 228"},
            {ID: "158", Description: "Pure Gold", Hex: "#7a6440", RGB: "122, 100, 64"},
            {ID: "159", Description: "Brushed Gold", Hex: "#7f6a48", RGB: "127, 106, 72"}
          ];
          
          // Process the colors to add categories
          const categorizedColors = processColors(allColors);
          setColors(categorizedColors);
        } else {
          // In FiveM, request colors from client script
          sendNuiMessage(NuiActions.GET_VEHICLE_COLORS);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching vehicle colors:', error);
        setIsLoading(false);
      }
    };

    fetchColors();
    
    // Set up listener for vehicle colors from client
    const unsubscribe = onNuiEvent(NuiMessageTypes.VEHICLE_COLORS, (data: { colors: any[] }) => {
      if (data && data.colors) {
        const processedColors = data.colors.map(color => ({
          ID: color.ID,
          Description: color.Description,
          Hex: color["Hex (Web RGB)"],
          RGB: color.RGB
        }));
        
        // Process the colors to add categories
        const categorizedColors = processColors(processedColors);
        setColors(categorizedColors);
      }
    });
    
    return () => unsubscribe();
  }, []);

  const handleColorSelect = (colorId: string) => {
    setSelectedColorId(colorId);
    
    // Apply the color to the vehicle
    sendNuiMessage(NuiActions.SET_VEHICLE_COLOR, { 
      colorType, 
      colorIndex: parseInt(colorId) 
    });
  };

  // Get filtered colors based on selected category
  const filteredColors = useMemo(() => {
    return selectedCategory === 'All' 
      ? colors 
      : colors.filter(color => color.Category === selectedCategory);
  }, [colors, selectedCategory]);  return (
    <div className="space-y-2">
      {/* Color Type Selection */}
      <div className="flex gap-1">
        <button
          onClick={() => setColorType('primary')}
          className={`flex-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
            colorType === 'primary' 
              ? 'bg-blue-600 text-white' 
              : 'bg-neutral-700 text-neutral-300 hover:bg-neutral-600'
          }`}
        >
          Primary
        </button>
        <button
          onClick={() => setColorType('secondary')}
          className={`flex-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
            colorType === 'secondary' 
              ? 'bg-blue-600 text-white' 
              : 'bg-neutral-700 text-neutral-300 hover:bg-neutral-600'
          }`}
        >
          Secondary
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-neutral-100"></div>
        </div>
      ) : (
        <>
          {/* Category Tabs */}
          <div className="flex flex-wrap gap-0.5 max-w-full overflow-x-auto pb-1">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-1.5 py-0.5 text-[10px] font-medium rounded transition-colors whitespace-nowrap ${
                  selectedCategory === category 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-neutral-700 text-neutral-300 hover:bg-neutral-600'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
          
          {/* Color Grid */}
          <div className="grid grid-cols-8 gap-1 max-h-32 overflow-y-auto p-1 bg-neutral-900/50 rounded">
            {filteredColors.map(color => (
              <button
                key={color.ID}
                onClick={() => handleColorSelect(color.ID)}
                className={`w-6 h-6 rounded-sm hover:scale-110 transition-transform relative border border-neutral-600/30 ${
                  selectedColorId === color.ID ? 'ring-1 ring-white scale-110 z-10' : ''
                }`}
                style={{ backgroundColor: color.Hex }}
                title={`${color.Description} (ID: ${color.ID})`}
              >
                {selectedColorId === color.ID && (
                  <span className="absolute inset-0 flex items-center justify-center">
                    <i className="fas fa-check text-[10px] drop-shadow-[0_0_2px_rgba(0,0,0,0.9)] text-white"></i>
                  </span>
                )}
              </button>
            ))}
          </div>
          
          {/* Selected Color Info */}
          {selectedColorId && (
            <div className="text-[10px] text-neutral-300 flex items-center gap-1">
              <div 
                className="w-3 h-3 rounded border border-neutral-600" 
                style={{ backgroundColor: colors.find(c => c.ID === selectedColorId)?.Hex || 'transparent' }}
              ></div>
              <span>
                {colors.find(c => c.ID === selectedColorId)?.Description} (ID: {selectedColorId})
              </span>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Set Vehicle Plate feature component
export const SetVehiclePlateFeature: React.FC = () => {
  const [plateText, setPlateText] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  const handlePlateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    // Limit to 8 characters as per FiveM native function requirement
    if (value.length <= 8) {
      setPlateText(value);
    }
  };

  const handleSetPlate = () => {
    if (plateText.trim()) {
      setIsValidating(true);
      console.log('Setting vehicle plate:', plateText.trim());
      // Send NUI message to set vehicle plate using new NUI system
      sendNuiMessage(NuiActions.SET_VEHICLE_PLATE, { plateText: plateText.trim() });
      
      // Reset validation state after a short delay
      setTimeout(() => {
        setIsValidating(false);
      }, 1000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && plateText.trim()) {
      handleSetPlate();
    }
  };

  const isValid = plateText.trim().length > 0;
  const charactersRemaining = 8 - plateText.length;

  return (
    <div className="space-y-2">
      <div className="flex gap-1">
        <div className="flex-1 relative">
          <input
            type="text"
            value={plateText}
            onChange={handlePlateChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter plate text..."
            className="w-full px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-neutral-100 text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 uppercase font-mono"
            disabled={isValidating}
          />
          {plateText && (
            <div className="absolute right-1 top-1/2 transform -translate-y-1/2 text-xs text-neutral-400">
              {charactersRemaining}
            </div>
          )}
        </div>
        
        <button
          onClick={handleSetPlate}
          disabled={!isValid || isValidating}
          className="px-2 py-1 bg-purple-600 hover:bg-purple-500 disabled:bg-neutral-600 disabled:cursor-not-allowed text-white rounded transition-colors text-xs font-medium whitespace-nowrap"
          title="Set license plate text on current vehicle"
        >
          {isValidating ? <i className="fas fa-spinner fa-spin" /> : 'Set Plate'}
        </button>
      </div>
      
      {plateText && (
        <div className="text-xs text-neutral-400">
          Preview: <span className="font-mono bg-neutral-800 px-1 rounded">{plateText || 'XXXXXXXX'}</span>
          {charactersRemaining < 8 && (
            <span className="ml-2">({charactersRemaining} chars left)</span>
          )}
        </div>
      )}
    </div>
  );
};
