// Tablet App Types
export interface TabletApp {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: 'productivity' | 'entertainment' | 'utility' | 'communication' | 'system';
  isInstalled: boolean;
  isVisible: boolean;
  version: string;
  size: number; // in MB
}

// Tablet Configuration
export interface TabletConfig {
  // Display settings
  brightness: number; // 0-100
  volume: number; // 0-100
  theme: 'dark' | 'light';
  
  // System settings
  autoLock: boolean;
  lockTimeout: number; // in minutes
  
  // App settings
  installedApps: TabletApp[];
  homeScreenApps: string[]; // App IDs for home screen
  recentApps: string[]; // App IDs for recent apps
}

// App State Management
export interface AppState {
  currentApp: string | null;
  isTabletVisible: boolean;
  isLocked: boolean;
  backgroundApps: string[];
}

// Notification System
export interface Notification {
  id: string;
  appId: string;
  title: string;
  message: string;
  timestamp: number;
  isRead: boolean;
  priority: 'low' | 'normal' | 'high';
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  action: string;
}

// Built-in Apps Data Types
export interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: number;
  updatedAt: number;
  tags: string[];
}

export interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  email?: string;
  avatar?: string;
  isFavorite: boolean;
}

export interface CalculatorHistory {
  id: string;
  expression: string;
  result: string;
  timestamp: number;
}

// Events for NUI Communication
export type TabletEvents = {
  // Tablet Control
  'tablet:show': void;
  'tablet:hide': void;
  'tablet:toggle': void;
  'tablet:lock': void;
  'tablet:unlock': void;
  
  // App Management
  'app:open': { appId: string };
  'app:close': { appId: string };
  'app:install': { appId: string };
  'app:uninstall': { appId: string };
  
  // Notifications
  'notification:send': Notification;
  'notification:dismiss': { id: string };
  'notification:clear': void;
  
  // Settings
  'settings:update': Partial<TabletConfig>;
  'settings:get': void;
};

export type TabletCallbacks = {
  'tablet:ready': void;
  'app:launched': { appId: string };
  'settings:updated': TabletConfig;
};
