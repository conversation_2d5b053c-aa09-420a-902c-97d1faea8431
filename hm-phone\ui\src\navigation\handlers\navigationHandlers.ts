/**
 * Navigation message handlers
 */
import { registerEventHandler } from '../../fivem/clientEventReceiver';
import { useNavigationStore } from '../navigationStore';
import { NavigationStateData } from '../navigationTypes';

// Define the expected deep link data structure
interface DeepLinkData {
  link: string;
}

// Register handler for deep link
registerEventHandler('navigation', 'deepLink', (data: DeepLinkData | unknown) => {
  if (
    typeof data === 'object' &&
    data !== null &&
    'link' in data &&
    typeof data.link === 'string'
  ) {
    useNavigationStore.getState().openDeepLink(data.link);
  } else {
    console.error('[Navigation] Received invalid deepLink data:', data);
  }
});

// Register handler for restoring navigation state
registerEventHandler('navigation', 'restoreNavigationState', (data: unknown) => {
  if (typeof data === 'object' && data !== null) {
    try {
      const stateData = data as NavigationStateData;
      if (typeof stateData.currentView !== 'string') {
        console.error('[Navigation] Invalid navigation state data:', data);
        return;
      }
      useNavigationStore.getState().restoreNavigationState(stateData);
    } catch (error) {
      console.error('[Navigation] Error restoring navigation state:', error);
    }
  } else {
    console.error('[Navigation] Received invalid navigation state data:', data);
  }
});
