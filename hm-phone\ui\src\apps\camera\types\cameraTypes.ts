/**
 * Type definitions for the Camera app
 */

// Import Photo type from photos store
import { Photo } from '../../photos/stores/photosStore';

export interface CameraSettings {
  flashEnabled: boolean;
  gridEnabled: boolean;
  timerEnabled: boolean;
  timerDuration: number; // in seconds
  currentMode: 'photo' | 'selfie';
}

export interface CameraStore {
  // Settings state
  settings: CameraSettings;

  // UI state
  currentView: 'camera' | 'preview';
  previewPhoto: Photo | null;

  // Actions
  actions: {
    takePhoto: (imageData: string) => Promise<any>; // Returns the response from clientRequests.create
    updateSettings: (settings: Partial<CameraSettings>) => void;
  };

  // UI actions
  ui: {
    setCurrentView: (view: 'camera' | 'preview') => void;
    setPreviewPhoto: (photo: Photo | null) => void;
  };
}
