import Contacts from '../../apps/contacts/Contacts';
import Photos from '../../apps/photos/Photos';
import Settings from '../../apps/settings/Settings';
import AppStore from '../../apps/appStore/AppStore';
import Messages from '../../apps/messages/Messages';
import YellowPages from '../../apps/yellowPages/YellowPages';
import DarkMarket from '../../apps/darkMarket/DarkMarket';
import Garage from '../../apps/garage/Garage';
import LifeSnap from '../../apps/lifeSnap/LifeSnap';
import BankingApp from '../../apps/banking/BankingApp';
import Music from '../../apps/music/Music';
import LoveLink from '../../apps/lovelink/LoveLink';
import Notes from '../../apps/notes/Notes';
import JobCenter from '../../apps/jobCenter/JobCenter';
import Camera from '../../apps/camera/Camera';
import Services from '../../apps/services/Services';
import { App } from '../types/appTypes';

const APPS = {
  // System Apps (1-6)
  CONTACTS: { id: 1, path: 'contacts', component: Contacts },
  PHOTOS: { id: 2, path: 'photos', component: Photos },
  SETTINGS: { id: 3, path: 'settings', component: Settings },
  APP_STORE: { id: 4, path: 'app-store', component: AppStore },
  MESSAGES: { id: 5, path: 'messages', component: Messages },
  CAMERA: { id: 15, path: 'camera', component: Camera },

  // Store Apps (6-16)
  YELLOW_PAGES: { id: 6, path: 'yellow-pages', component: YellowPages },
  DARK_MARKET: { id: 7, path: 'dark-market', component: DarkMarket },
  GARAGE: { id: 8, path: 'garage', component: Garage },
  LIFE_SNAP: { id: 9, path: 'life-snap', component: LifeSnap },
  BANKING: { id: 10, path: 'banking', component: BankingApp },
  MUSIC: { id: 11, path: 'music', component: Music },
  LOVE_LINK: { id: 12, path: 'love-link', component: LoveLink },
  NOTES: { id: 13, path: 'notes', component: Notes },
  JOB_CENTER: { id: 14, path: 'job-center', component: JobCenter },
  SERVICES: { id: 16, path: 'services', component: Services }
} as const;

export const appConfigurations: App[] = [
  // System Apps
  {
    ...APPS.CONTACTS,
    name: 'Contacts',
    icon: 'user',
    iconColor: 'green',
    description: 'Manage your contacts and address book',
    colors: {
      bg: 'bg-gradient-to-br from-green-500/60 to-green-600/60',
      text: 'text-green-500'
    },
    settings: { sortBy: 'lastName' },
    views: {
      main: { name: 'Contact List', tabs: ['dialer', 'calls', 'contacts', 'favorites', 'profile'] },
      detail: { name: 'Contact Details' },
      add: { name: 'Add Contact' },
      edit: { name: 'Edit Contact' },
      share: { name: 'Share Contact' },
      call: { name: 'Call' }
    },
    type: 'system'
  },
  {
    ...APPS.PHOTOS,
    name: 'Photos',
    icon: 'image',
    iconColor: 'purple',
    description: 'View and manage your photo gallery',
    colors: {
      bg: 'bg-gradient-to-br from-purple-500/60 to-purple-600/60',
      text: 'text-purple-500'
    },
    settings: { displayMode: 'grid' },
    views: {
      main: { name: 'Gallery' },
      detail: { name: 'Photo Detail' }
    },
    type: 'system'
  },
  {
    ...APPS.SETTINGS,
    name: 'Settings',
    icon: 'cogs',
    iconColor: 'gray',
    description: 'Configure your phone settings',
    colors: {
      bg: 'bg-gradient-to-br from-gray-500/60 to-gray-600/60',
      text: 'text-gray-500'
    },
    settings: {},
    views: {
      main: { name: 'Settings Menu' },
      notifications: { name: 'Notification Settings' },
      profile: { name: 'Edit Profile' },
      wifi: { name: 'WiFi Settings' },
      sounds: { name: 'Sound Settings' }
    },
    type: 'system'
  },
  {
    ...APPS.APP_STORE,
    name: 'AppStore',
    icon: 'store',
    iconColor: 'orange',
    description: 'Download and manage your applications',
    colors: {
      bg: 'bg-gradient-to-br from-orange-500/60 to-orange-600/60',
      text: 'text-orange-500'
    },
    settings: {},
    views: {
      main: { name: 'App List' },
      detail: { name: 'App Details' }
    },
    type: 'system'
  },
  {
    ...APPS.MESSAGES,
    name: 'Messages',
    icon: 'envelope',
    iconColor: 'indigo',
    description: 'Send and receive text messages',
    colors: {
      bg: 'bg-gradient-to-br from-indigo-500/60 to-indigo-600/60',
      text: 'text-indigo-500'
    },
    settings: { notificationsEnabled: true, sound: true },
    views: {
      main: { name: 'Conversations', tabs: ['chats', 'profile'] },
      conversation: { name: 'Conversation' },
      info: { name: 'Group Info' },
      newConversation: { name: 'New Conversation' }
    },
    type: 'system'
  },
  {
    ...APPS.CAMERA,
    name: 'Camera',
    icon: 'camera',
    iconColor: 'cyan',
    description: 'Take photos and videos',
    colors: {
      bg: 'bg-gradient-to-br from-cyan-500/60 to-cyan-600/60',
      text: 'text-cyan-500'
    },
    settings: {},
    views: {
      main: { name: 'Camera View' },
      preview: { name: 'Photo Preview' },
      gallery: { name: 'Gallery' }
    },
    type: 'system'
  },

  // Store Apps
  {
    ...APPS.YELLOW_PAGES,
    name: 'Yellow Pages',
    icon: 'book',
    iconColor: 'yellow',
    description: 'Find local businesses and services',
    colors: {
      bg: 'bg-gradient-to-br from-yellow-500/60 to-yellow-600/60',
      text: 'text-yellow-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Listings' },
      form: { name: 'Create Ad' }
    },
    type: 'store'
  },
  {
    ...APPS.DARK_MARKET,
    name: 'Dark Market',
    icon: 'mask',
    iconColor: 'red',
    description: 'Access the underground marketplace',
    colors: {
      bg: 'bg-gradient-to-br from-red-500/60 to-red-600/60',
      text: 'text-red-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Listings' },
      detail: { name: 'Listing Details' }
    },
    type: 'store'
  },
  {
    ...APPS.GARAGE,
    name: 'Garage',
    icon: 'car',
    iconColor: 'blue',
    description: 'Manage your vehicles',
    colors: {
      bg: 'bg-gradient-to-br from-blue-500/60 to-blue-600/60',
      text: 'text-blue-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Vehicle List' },
      detail: { name: 'Vehicle Details' }
    },
    type: 'store'
  },
  {
    ...APPS.LIFE_SNAP,
    name: 'LifeSnap',
    icon: 'camera',
    iconColor: 'yellow',
    description: 'Share your life with friends and family',
    colors: {
      bg: 'bg-gradient-to-br from-yellow-500/60 to-yellow-600/60',
      text: 'text-yellow-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Feed' },
      create: { name: 'Create Post' },
      profile: { name: 'Profile' },
      story: { name: 'Story Viewer' },
      post: { name: 'Post Detail' }
    },
    type: 'store'
  },
  {
    ...APPS.BANKING,
    name: 'Banking',
    icon: 'money-bill-wave',
    iconColor: 'green',
    description: 'Manage your finances and transactions',
    colors: {
      bg: 'bg-gradient-to-br from-green-500/60 to-green-600/60',
      text: 'text-green-500'
    },
    settings: { installed: false, notificationsEnabled: true, sound: true },
    views: {
      main: { name: 'Accounts' },
      transactions: { name: 'Transactions' },
      transfer: { name: 'Transfer Money' },
      transaction: { name: 'Transaction Details' }
    },
    type: 'store'
  },
  {
    ...APPS.MUSIC,
    name: 'Music',
    icon: 'music',
    iconColor: 'purple',
    description: 'Enhanced music player experience',
    colors: {
      bg: 'bg-gradient-to-br from-purple-500/60 to-purple-600/60',
      text: 'text-purple-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Song List' },
      nowPlaying: { name: 'Now Playing' },
      search: { name: 'Search' },
      playlist: { name: 'Playlists' },
      artist: { name: 'Artist Profile' },
      profile: { name: 'User Profile' },
      album: { name: 'Album Details' }
    },
    type: 'store'
  },
  {
    ...APPS.LOVE_LINK,
    name: 'LoveLink',
    icon: 'heart',
    iconColor: 'pink',
    description: 'Find your perfect match',
    colors: {
      bg: 'bg-gradient-to-br from-pink-500/60 to-pink-600/60',
      text: 'text-pink-500'
    },
    settings: { installed: false },
    views: {
      swipe: { name: 'Swipe Profiles' },
      matches: { name: 'Matches' },
      chat: { name: 'Chat' },
      myProfile: { name: 'My Profile' }
    },
    type: 'store'
  },
  {
    ...APPS.NOTES,
    name: 'Notes',
    icon: 'sticky-note',
    iconColor: 'orange',
    description: 'Create and manage notes',
    colors: {
      bg: 'bg-gradient-to-br from-orange-500/60 to-orange-600/60',
      text: 'text-orange-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Notes List' },
      editor: { name: 'Note Editor' }
    },
    type: 'store'
  },
  {
    ...APPS.JOB_CENTER,
    name: 'Job Center',
    icon: 'briefcase',
    iconColor: 'teal',
    description: 'Find jobs and career opportunities',
    colors: {
      bg: 'bg-gradient-to-br from-teal-500/60 to-teal-600/60',
      text: 'text-teal-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Job List' },
      detail: { name: 'Job Details' },
      group: { name: 'Group Details' }
    },
    type: 'store'
  },
  {
    ...APPS.SERVICES,
    name: 'Services',
    icon: 'concierge-bell',
    iconColor: 'blue',
    description: 'Find and contact local services',
    colors: {
      bg: 'bg-gradient-to-br from-blue-500/60 to-blue-600/60',
      text: 'text-blue-500'
    },
    settings: { installed: false },
    views: {
      main: { name: 'Services List' }
    },
    type: 'store'
  }
];

export default appConfigurations;
