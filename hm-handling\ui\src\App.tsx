import React, { useEffect } from 'react';
import './App.css';
import HandlingPanel from './components/HandlingPanel';
import { useHandlingStore } from './store/handlingStore';
import { BaseHandlingData, Vector3, HandlingProfile, HANDLING_FIELD_LIMITS } from '../../scripts/shared/types';

// Utility function to clamp values based on field limits
const clampValue = (field: keyof BaseHandlingData, value: number): number => {
  const limits = HANDLING_FIELD_LIMITS[field];
  if (!limits) return value;
  
  const { min, max } = limits;
  if (min !== undefined && value < min) return min;
  if (max !== undefined && value > max) return max;
  return value;
};

// Utility function to clamp Vector3 values
const clampVector3 = (field: string, value: Vector3): Vector3 => {
  const limits = HANDLING_FIELD_LIMITS[field as keyof typeof HANDLING_FIELD_LIMITS];
  if (!limits) return value;
  
  const { min, max } = limits;
  return {
    x: min !== undefined && value.x < min ? min : max !== undefined && value.x > max ? max : value.x,
    y: min !== undefined && value.y < min ? min : max !== undefined && value.y > max ? max : value.y,
    z: min !== undefined && value.z < min ? min : max !== undefined && value.z > max ? max : value.z
  };
};

const App: React.FC = () => {  // Get state and actions from Zustand store
  const {
    isVisible,
    isApplying,
    showSaveModal,
    currentVehicle,
    originalHandling,
    currentHandling,
    hasUnsavedChanges,
    modifiedFields,
    appliedFields,
    profiles,
    saveProfileData,
    noVehicleMessage,
    isDevelopmentMode,
    setVisible,
    setApplying,
    setShowSaveModal,
    updateHandlingField,
    updateVector3Field,
    resetField,
    resetAllFields,
    loadProfile,
    setSaveProfileData,
    resetSaveProfileData,    addProfile,
    initializeFromData,
    setNoVehicleMessage,
    applyField,
    enableDevelopmentMode
  } = useHandlingStore();

  // Enable development mode for testing (remove in production)
  useEffect(() => {
    // Auto-enable development mode in dev environment
    if (process.env.NODE_ENV === 'development' && !isDevelopmentMode) {
      enableDevelopmentMode();
    }
  }, [isDevelopmentMode, enableDevelopmentMode]);

  // Listen for NUI messages from client
  useEffect(() => {
    // Skip NUI message handling in development mode
    if (isDevelopmentMode) return;    const handleMessage = (event: MessageEvent) => {
      const { type, visible, data } = event.data;
      
      if (type === 'setVisible') {
        setVisible(visible);
        if (visible) {
          if (data && data.vehicle && data.vehicle.isDriverSeat) {
            // Player is in driver seat, initialize with vehicle data
            initializeFromData(data);
          } else {
            // Player is not in driver seat or no vehicle
            const message = data?.noVehicleMessage || 'You must be in the driver seat of a vehicle to use the handling editor.';
            setNoVehicleMessage(message);
          }
        }
      }
    };    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [setVisible, initializeFromData, setNoVehicleMessage, isDevelopmentMode]);
  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {      // F7 key to close UI
      if (event.key === 'F7' && isVisible) {
        event.preventDefault();
        // Close UI functionality
        if (isDevelopmentMode) {
          setVisible(false);
        } else {
          fetch(`https://hm-handling/closeUI`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          });
        }
        return;
      }
      
      // Ctrl+Shift+H to toggle UI visibility in development mode
      if (event.ctrlKey && event.shiftKey && event.key === 'H' && isDevelopmentMode) {
        setVisible(!isVisible);
      }
    };    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, setVisible, isDevelopmentMode]);

  // Handle field changes with clamping
  const handleValueChange = (field: keyof BaseHandlingData, value: number) => {
    const clampedValue = clampValue(field, value);
    updateHandlingField(field, clampedValue);
  };

  const handleStringChange = (field: keyof BaseHandlingData, value: string) => {
    updateHandlingField(field, value);
  };

  const handleVector3Change = (field: string, value: Vector3) => {
    const clampedValue = clampVector3(field, value);
    updateVector3Field(field, clampedValue);
  };

  // Reset handlers
  const handleReset = (field: keyof BaseHandlingData) => {
    resetField(field);
  };

  const handleVector3Reset = (field: string) => {
    resetField(field as keyof BaseHandlingData);
  };
  // Apply changes to vehicle
  const handleApplyChanges = async () => {
    if (!currentVehicle || !hasUnsavedChanges) return;

    setApplying(true);
    
    try {      // Send only modified fields to client
      const changedFields: Partial<BaseHandlingData> = {};
      modifiedFields.forEach(field => {
        const fieldKey = field as keyof BaseHandlingData;
        const value = currentHandling[fieldKey];
        if (value !== undefined) {
          (changedFields as any)[fieldKey] = value;
        }
      });
      
      console.log('Applying only changed fields:', Object.keys(changedFields));
      
      // Send apply command to client (skip in development mode)
      if (!isDevelopmentMode) {
        await fetch(`https://hm-handling/applyAllChanges`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ handlingData: changedFields })
        });
      }
      
      console.log('Applied handling changes to vehicle');
      
    } catch (error) {
      console.error('Failed to apply handling changes:', error);
    } finally {
      setApplying(false);
    }
  };

  // Refresh vehicle info
  const handleRefreshVehicle = () => {
    if (isDevelopmentMode) return;
    
    fetch(`https://hm-handling/refreshVehicle`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
  };

  // Profile management
  const handleSaveNewProfile = () => {
    setShowSaveModal(true);
  };

  const handleSaveProfile = () => {
    if (!currentVehicle || !saveProfileData.name.trim()) return;
    
    const profile: HandlingProfile = {
      id: Date.now().toString(),
      name: saveProfileData.name.trim(),
      description: saveProfileData.description.trim() || undefined,
      vehicleModel: saveProfileData.vehicleSpecific ? currentVehicle.model : undefined,
      handlingData: { ...currentHandling },
      createdBy: 'current-player',
      createdAt: new Date(),
      isPublic: false,
      tags: []
    };
    
    addProfile(profile);
    
    // Send to client for persistence (skip in development mode)
    if (!isDevelopmentMode) {
      fetch(`https://hm-handling/saveProfile`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ profile })
      });
    }
    
    setShowSaveModal(false);
    resetSaveProfileData();
  };

  const handleCancelSave = () => {
    setShowSaveModal(false);
    resetSaveProfileData();
  };

  const handleLoadProfile = (profileId: string) => {
    loadProfile(profileId);
  };

  const handleCloseUI = () => {
    if (isDevelopmentMode) {
      setVisible(false);
      return;
    }
    
    fetch(`https://hm-handling/closeUI`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="handling-menu">
      {/* Header */}
      <div className="handling-menu-header">
        <h2>Vehicle Handling</h2>
        <div className="handling-menu-controls">
          {isDevelopmentMode && (
            <span className="dev-mode-indicator" title="Development Mode">DEV</span>
          )}          <button 
            className="reset-all-btn" 
            onClick={resetAllFields}
            disabled={!hasUnsavedChanges || !currentVehicle}
            title="Reset all to original"
          >
            Reset All
          </button>
          <button 
            className={`apply-btn ${hasUnsavedChanges ? 'has-changes' : ''}`}
            onClick={handleApplyChanges}
            disabled={!hasUnsavedChanges || isApplying || !currentVehicle}
            title="Apply changes to vehicle"
          >
            {isApplying ? 'Applying...' : 'Apply'}
          </button>
          <button className="close-btn" onClick={handleCloseUI} title="Close menu">✕</button>
        </div>
      </div>

      {/* Vehicle Info Line with Profile Dropdown */}
      {currentVehicle && (
        <div className="vehicle-info-line">
          <div className="vehicle-info-content">
            <span className="vehicle-name">{currentVehicle.displayName}</span>
            <span className="vehicle-model">({currentVehicle.model})</span>
            <span className="vehicle-class">{currentVehicle.className}</span>
          </div>
          <div className="vehicle-info-controls">
            <select 
              className="profile-dropdown"
              value=""
              onChange={(e) => {
                if (e.target.value === 'save-new') {
                  handleSaveNewProfile();
                } else if (e.target.value) {
                  handleLoadProfile(e.target.value);
                }
              }}
            >
              <option value="">Profiles</option>
              <option value="save-new">💾 Save Current</option>
              {profiles.length > 0 && <option disabled>──────────</option>}
              {profiles.map((profile: HandlingProfile) => (
                <option key={profile.id} value={profile.id}>
                  {profile.name} {profile.vehicleModel ? `(${profile.vehicleModel})` : ''}
                </option>
              ))}
            </select>
            <button 
              className="refresh-btn"
              onClick={handleRefreshVehicle}
              title="Refresh vehicle info"
              disabled={isDevelopmentMode}
            >
              🔄
            </button>
          </div>
        </div>      )}

      {/* No Vehicle Message */}
      {noVehicleMessage && (
        <div className="no-vehicle-message">
          <div className="no-vehicle-icon">🚗</div>
          <div className="no-vehicle-text">
            <h3>No Vehicle Available</h3>
            <p>{noVehicleMessage}</p>
          </div>
        </div>
      )}

      {/* Unsaved Changes Indicator */}
      {hasUnsavedChanges && (
        <div className="unsaved-changes-bar">
          <span className="changes-icon">●</span>
          <span>You have unsaved changes</span>
        </div>
      )}

      {/* Save Profile Modal */}
      {showSaveModal && (
        <div className="modal-overlay">
          <div className="save-profile-modal">
            <div className="modal-header">
              <h3>Save Profile</h3>
              <button className="close-btn" onClick={handleCancelSave}>✕</button>
            </div>
            
            <div className="modal-content">
              <div className="form-row">
                <label htmlFor="profile-name">Profile Name*</label>
                <input
                  id="profile-name"
                  type="text"
                  value={saveProfileData.name}
                  onChange={(e) => setSaveProfileData({ name: e.target.value })}
                  placeholder="My Custom Setup"
                  maxLength={50}
                  autoFocus
                />
              </div>
              
              <div className="form-row">
                <label htmlFor="profile-description">Description</label>
                <textarea
                  id="profile-description"
                  value={saveProfileData.description}
                  onChange={(e) => setSaveProfileData({ description: e.target.value })}
                  placeholder="Optional description..."
                  maxLength={200}
                  rows={3}
                />
              </div>
              
              <div className="form-checkbox">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={saveProfileData.vehicleSpecific}
                    onChange={(e) => setSaveProfileData({ vehicleSpecific: e.target.checked })}
                  />
                  Vehicle-specific ({currentVehicle?.model || 'Unknown'})
                </label>
              </div>
            </div>
            
            <div className="modal-actions">
              <button className="cancel-btn" onClick={handleCancelSave}>
                Cancel
              </button>
              <button 
                className="save-btn" 
                onClick={handleSaveProfile}
                disabled={!saveProfileData.name.trim()}
              >
                Save Profile
              </button>
            </div>
          </div>
        </div>
      )}      {/* Main Handling Panel */}
      {currentVehicle && (
        <div className="handling-content">          <HandlingPanel
            currentHandling={currentHandling}
            stockHandling={originalHandling}
            onValueChange={handleValueChange}
            onReset={handleReset}
            onVector3Change={handleVector3Change}
            onVector3Reset={handleVector3Reset}
            onStringChange={handleStringChange}
            onApplyField={applyField}
            modifiedFields={modifiedFields}
            appliedFields={appliedFields}
          />
        </div>
      )}
    </div>
  );
};

export default App;