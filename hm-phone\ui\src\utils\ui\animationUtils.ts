/**
 * Animation Utilities
 *
 * This module provides utilities for common animations using Framer Motion.
 */

/**
 * Fade transition animation
 * @param duration - Animation duration in seconds
 * @returns Framer Motion animation properties
 */
export const fadeTransition = (duration: number = 0.3) => ({
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { duration }
});

/**
 * Slide transition animation
 * @param direction - Direction to slide from
 * @param duration - Animation duration in seconds
 * @returns Framer Motion animation properties
 */
export const slideTransition = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  duration: number = 0.3
) => {
  const directionMap = {
    up: { y: 20 },
    down: { y: -20 },
    left: { x: 20 },
    right: { x: -20 }
  };

  return {
    initial: { opacity: 0, ...directionMap[direction] },
    animate: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, ...directionMap[direction] },
    transition: { duration }
  };
};

/**
 * Scale transition animation
 * @param initialScale - Initial scale value
 * @param duration - Animation duration in seconds
 * @returns Framer Motion animation properties
 */
export const scaleTransition = (initialScale: number = 0.95, duration: number = 0.3) => ({
  initial: { opacity: 0, scale: initialScale },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: initialScale },
  transition: { duration }
});

/**
 * Staggered children animation
 * @param staggerDuration - Delay between each child animation
 * @returns Framer Motion animation properties for parent and child
 */
export const staggeredAnimation = (staggerDuration: number = 0.05) => ({
  parent: {
    initial: { opacity: 1 },
    animate: { opacity: 1, transition: { staggerChildren: staggerDuration } },
    exit: { opacity: 1 }
  },
  child: {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 10 }
  }
});

/**
 * Pulse animation for buttons or notifications
 * @returns Framer Motion animation properties
 */
export const pulseAnimation = () => ({
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      repeatType: 'loop' as const
    }
  }
});

/**
 * Bounce animation
 * @returns Framer Motion animation properties
 */
export const bounceAnimation = () => ({
  animate: {
    y: [0, -10, 0],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      repeatType: 'loop' as const
    }
  }
});

/**
 * Tab indicator animation
 * @returns Framer Motion animation properties for tab indicator
 */
export const tabIndicatorAnimation = () => ({
  layoutId: 'tab-indicator',
  initial: false,
  transition: { type: 'spring', stiffness: 300, damping: 30 }
});

/**
 * Button tap animation
 * @param scale - Scale value when tapped
 * @returns Framer Motion animation properties for button tap
 */
export const buttonTapAnimation = (scale: number = 0.95) => ({
  whileTap: { scale }
});

/**
 * List item animation
 * @returns Framer Motion animation properties for list items
 */
export const listItemAnimation = () => ({
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 10 },
  transition: { duration: 0.2 }
});
