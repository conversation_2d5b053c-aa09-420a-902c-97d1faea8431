import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clientRequests } from '../../fivem';
import { useContactsStore } from '../../apps/contacts/stores/contactsStore';

// Define nearby player type
export interface NearbyPlayer {
  id: number;
  name: string;
  distance: number;
  avatar?: string;
}

// Define props for the component
export interface NearbyPlayersPanelProps {
  show: boolean;
  onClose: () => void;
  title: string;
  onPlayerSelect: (playerId: number) => void;
  getPlayerStatus?: (playerId: number) => {
    status: string | null;
    text: string;
    color: string;
    icon?: string;
  };
  contentAbove?: React.ReactNode;
}

const NearbyPlayersPanel: React.FC<NearbyPlayersPanelProps> = ({
  show,
  onClose,
  title,
  onPlayerSelect,
  getPlayerStatus,
  contentAbove
}) => {
  // For horizontal scrolling
  const scrollableRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // State for nearby players
  const [nearbyPlayers, setNearbyPlayers] = useState<NearbyPlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Get contacts to check if we have a saved contact for a player
  const { contacts } = useContactsStore();

  // Function to refresh nearby players
  const handleRefresh = async () => {
    console.log('Refreshing nearby players...');
    setRefreshing(true);

    try {
      // Get nearby players with refresh=true
      const response = await clientRequests.send('phone', 'getNearbyPlayers', { refresh: true }, [
        // Mock data for browser testing
        // { id: 1, name: 'John Doe', distance: 3.2, avatar: 'https://i.pravatar.cc/150?img=1' },
        // { id: 2, name: 'Jane Smith', distance: 5.7, avatar: 'https://i.pravatar.cc/150?img=2' },
        // { id: 3, name: 'Bob Johnson', distance: 8.3, avatar: 'https://i.pravatar.cc/150?img=3' }
      ]);

      console.log('Refresh response received:', response);

      // Handle the response
      if (response && typeof response === 'object') {
        // If it's a direct array
        if (Array.isArray(response)) {
          setNearbyPlayers(response);
          console.log(`Found ${response.length} nearby players`);
        }
        // If it has a data property that is an array (standard format)
        else if ('data' in response && response.data && Array.isArray(response.data)) {
          setNearbyPlayers(response.data);
          console.log(`Found ${response.data.length} nearby players`);
        }
        // Otherwise set empty array
        else {
          console.warn('Unexpected response format:', response);
          setNearbyPlayers([]);
        }
      } else {
        console.warn('Invalid response:', response);
        setNearbyPlayers([]);
      }
    } catch (error) {
      console.error('Error refreshing nearby players:', error);
      setNearbyPlayers([]);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  // Fetch nearby players when panel is shown
  useEffect(() => {
    if (show) {
      console.log('NearbyPlayersPanel shown, initializing...');
      setLoading(true);

      // Initial fetch with refresh=true to perform the search
      const fetchNearbyPlayers = async () => {
        try {
          const response = await clientRequests.send(
            'phone',
            'getNearbyPlayers',
            { refresh: true },
            [
              // Mock data for browser testing
              { id: 1, name: 'John Doe', distance: 3.2, avatar: 'https://i.pravatar.cc/150?img=1' },
              {
                id: 2,
                name: 'Jane Smith',
                distance: 5.7,
                avatar: 'https://i.pravatar.cc/150?img=2'
              },
              {
                id: 3,
                name: 'Bob Johnson',
                distance: 8.3,
                avatar: 'https://i.pravatar.cc/150?img=3'
              }
            ]
          );

          console.log('Initial nearby players response:', response);

          // Handle the response
          if (response && typeof response === 'object') {
            // If it's a direct array
            if (Array.isArray(response)) {
              setNearbyPlayers(response);
              console.log(`Initial fetch: ${response.length} nearby players`);
            }
            // If it has a data property that is an array (standard format)
            else if ('data' in response && response.data && Array.isArray(response.data)) {
              setNearbyPlayers(response.data);
              console.log(`Initial fetch: ${response.data.length} nearby players`);
            }
            // Otherwise set empty array
            else {
              console.warn('Unexpected response format:', response);
              setNearbyPlayers([]);
            }
          } else {
            console.warn('Invalid response:', response);
            setNearbyPlayers([]);
          }
        } catch (error) {
          console.error('Error fetching nearby players:', error);
          setNearbyPlayers([]);
        } finally {
          setLoading(false);
        }
      };

      fetchNearbyPlayers();
    }
  }, [show]);

  // Horizontal scrolling handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollableRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollableRef.current.offsetLeft);
    setScrollLeft(scrollableRef.current.scrollLeft);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollableRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollableRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollableRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <AnimatePresence>
      {show && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="absolute bottom-0 left-0 right-0 bg-[#0a0f1a] rounded-t-2xl p-3 z-50"
          >
            <div className="flex flex-col gap-2">
              <div className="flex justify-between items-center">
                <h3 className="text-base font-semibold text-white">{title}</h3>
                <button onClick={onClose} className="text-white/60 hover:text-white">
                  <i className="fas fa-times"></i>
                </button>
              </div>

              {/* Optional content above the nearby players section */}
              {contentAbove}

              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm font-medium text-white/80">Nearby Players</h3>
                  <button
                    onClick={handleRefresh}
                    disabled={loading || refreshing}
                    className="text-white/60 hover:text-white disabled:text-white/30 transition-colors"
                    title="Refresh nearby players"
                  >
                    <i className={`fas fa-sync-alt ${refreshing ? 'animate-spin' : ''}`}></i>
                  </button>
                </div>
                {!loading && nearbyPlayers.length > 0 && (
                  <span className="text-xs text-white/50">
                    {nearbyPlayers.length} {nearbyPlayers.length === 1 ? 'player' : 'players'}{' '}
                    nearby
                  </span>
                )}
              </div>

              <div
                ref={scrollableRef}
                className="flex gap-4 overflow-x-auto pb-2 px-1 cursor-grab active:cursor-grabbing min-h-[100px]"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
              >
                {loading ? (
                  <div className="flex-1 flex flex-col items-center justify-center py-6">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white/60"></div>
                    <p className="text-white/60 text-xs mt-2">
                      {refreshing ? 'Refreshing nearby players...' : 'Finding nearby players...'}
                    </p>
                  </div>
                ) : nearbyPlayers.length > 0 ? (
                  nearbyPlayers.map(player => {
                    // Get player status if the function is provided
                    const statusInfo = getPlayerStatus ? getPlayerStatus(player.id) : null;

                    return (
                      <div
                        key={player.id}
                        className="flex flex-col items-center w-16 select-none transition-transform hover:scale-105"
                        onClick={() => {
                          // Only allow clicking if status is not 'accepted' or 'error'
                          if (
                            statusInfo?.status &&
                            ['accepted', 'error'].includes(statusInfo.status)
                          ) {
                            return;
                          }
                          onPlayerSelect(player.id);
                        }}
                      >
                        <div className="w-16 h-16 rounded-full bg-white/10 overflow-hidden mb-2 relative">
                          {player.avatar ? (
                            <img
                              src={player.avatar}
                              alt={player.name}
                              className="w-full h-full object-cover"
                              draggable={false}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-600 to-purple-700">
                              <span className="text-white text-xl font-medium">
                                {player.name ? player.name[0].toUpperCase() : '#'}
                              </span>
                            </div>
                          )}

                          {/* Status indicator */}
                          {statusInfo?.status && (
                            <div className="absolute top-0 right-0 w-6 h-6 bg-black/60 rounded-full flex items-center justify-center">
                              <i
                                className={`fas ${statusInfo.icon || 'fa-info'} ${
                                  statusInfo.color
                                } text-sm`}
                              ></i>
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-xs text-white/80 w-full text-center truncate px-1">
                            {/* Show contact name if we have it saved, otherwise show player name */}
                            {(() => {
                              // Check if we have this player saved as a contact
                              const savedContact = contacts.find(
                                contact =>
                                  contact.number === player.id.toString() ||
                                  contact.number === `${player.id}`
                              );

                              if (savedContact) {
                                return savedContact.name;
                              } else {
                                return player.name || `ID: ${player.id}`;
                              }
                            })()}
                          </span>
                          {statusInfo?.status && (
                            <span className={`text-xs font-medium ${statusInfo.color}`}>
                              {statusInfo.text}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center py-6">
                    <p className="text-white/60 text-sm">No players nearby</p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default NearbyPlayersPanel;
