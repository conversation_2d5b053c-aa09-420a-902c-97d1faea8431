/**
 * HM Target - Shared Types
 *
 * TypeScript type definitions for the targeting system.
 */

export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

export interface Vector2 {
  x: number;
  y: number;
}

export interface TargetOption {
  id: string;
  label: string;
  icon?: string;
  action: string; // This is an event name
  distance?: number;
  job?: string | string[];
  gang?: string | string[];
  stateid?: string | string[];
  item?: string;
  canInteract?: (entity?: number, distance?: number, data?: any) => boolean; // data is option.data
  showProgress?: boolean;
  progressDuration?: number;
  progressLabel?: string;
  disabled?: boolean;
  data?: Record<string, any>;
}

export interface TargetZone {
  id: string;
  type: "box" | "circle" | "sphere" | "polygon";
  position: Vector3;
  size?: Vector3; // For box zones
  radius?: number; // For circle and sphere zones
  points?: Vector3[]; // For polygon zones
  rotation?: Vector3; // For box zones
  height?: number; // For polygon zones
  options: TargetOption[];
  minZ?: number;
  maxZ?: number;
  debug?: boolean;
  data?: Record<string, any>;
}

export interface TargetEntity {
  id: string;
  entity: number;
  options: TargetOption[];
  distance?: number;
  data?: Record<string, any>;
}

export interface TargetModel {
  id: string;
  models: string[] | number[];
  options: TargetOption[];
  distance?: number;
  data?: Record<string, any>;
}

export interface RaycastResult {
  hit: boolean;
  entity?: number;
  position?: Vector3;
  normal?: Vector3;
  distance?: number;
}

export interface TargetState {
  active: boolean;
  currentTarget?: {
    entity?: number;
    modelHash?: number; // Ensure this is present
    zone?: TargetZone;
    position: Vector3;
    options: TargetOption[];
    distance: number;
    type?: "entity" | "zone" | "model"; // Ensure this is present
  };
  showingOptions: boolean;
  selectedOption?: TargetOption;
  progress?: {
    active: boolean;
    label: string;
    duration: number;
    startTime: number;
  };
}

export interface NUIMessage {
  action: string;
  data?: any;
}

export interface TargetAPIExports {
  // Zone management
  addBoxZone: (
    id: string,
    position: Vector3,
    size: Vector3,
    options: TargetOption[],
    config?: Partial<TargetZone>
  ) => boolean;
  addCircleZone: (
    id: string,
    position: Vector3,
    radius: number,
    options: TargetOption[],
    config?: Partial<TargetZone>
  ) => boolean;
  addPolyZone: (
    id: string,
    points: Vector3[],
    options: TargetOption[],
    config?: Partial<TargetZone>
  ) => boolean;

  // Entity management
  addTargetEntity: (
    entity: number,
    options: TargetOption[],
    config?: Partial<TargetEntity>
  ) => boolean;
  removeTargetEntity: (entity: number) => boolean;

  // Model management
  addTargetModel: (
    models: string[] | number[],
    options: TargetOption[],
    config?: Partial<TargetModel>
  ) => boolean;
  removeTargetModel: (models: string[] | number[]) => boolean;

  // General management
  removeTarget: (id: string) => boolean;
  removeAllTargets: () => void;

  // State management
  getTargetState: () => TargetState;
  isTargetActive: () => boolean;

  // Utility functions
  getCurrentTarget: () => TargetState["currentTarget"] | null;
  getDistance: (entity: number) => number;
}
