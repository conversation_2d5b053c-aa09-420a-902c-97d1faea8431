/**
 * Store for the Garage app
 */
import { create } from 'zustand';
import { GarageStore } from '../types/garageTypes';
import { garageMockData } from '../../../fivem/mockData';

export const useGarageStore = create<GarageStore>((set, get) => ({
  // State
  vehicles: garageMockData.vehicles,
  selectedVehicleId: null,
  loading: false,
  error: null,

  // Actions
  setVehicles: vehicles => set({ vehicles }),

  selectVehicle: id => set({ selectedVehicleId: id }),

  trackVehicle: id => {
    // In a real implementation, this would send a message to the server
    console.log(`Tracking vehicle with ID: ${id}`);
  },

  toggleEngine: id => {
    // In a real implementation, this would send a message to the server
    console.log(`Toggling engine for vehicle with ID: ${id}`);
  },

  toggleLock: id => {
    // In a real implementation, this would send a message to the server
    console.log(`Toggling lock for vehicle with ID: ${id}`);
  },

  // Getters
  getVehicleById: id => {
    return get().vehicles.find(vehicle => vehicle.id === id);
  },

  getVehiclesByGarage: garage => {
    return get().vehicles.filter(vehicle => vehicle.garage === garage);
  }
}));
