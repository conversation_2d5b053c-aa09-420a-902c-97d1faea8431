import React, { memo } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface ScanningIndicatorProps {
  isScanning: boolean;
  scanTarget: any | null;
}

const ScanningIndicator: React.FC<ScanningIndicatorProps> = memo(({ isScanning, scanTarget }) => {
  return (
    <AnimatePresence>
      {isScanning && (
        <motion.div
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.2 }}
        >
          {/* Scanning UI */}
          <div className="bg-neutral-900/90 border border-green-500/30 rounded-lg p-4 backdrop-blur-sm shadow-xl">
            <div className="flex items-center gap-3">
              {/* Scanning spinner */}
              <div className="relative">
                <div className="w-6 h-6 border-2 border-green-500/30 border-t-green-400 rounded-full animate-spin"></div>
                <div className="absolute inset-0 w-6 h-6 border border-green-500/20 rounded-full animate-pulse"></div>
              </div>
              
              {/* Scanning text */}
              <div className="text-neutral-200">
                <div className="text-sm font-medium">
                  {scanTarget ? 'Target Detected' : 'Scanning...'}
                </div>
                <div className="text-xs text-neutral-400">
                  Hold <span className="text-green-400 font-mono">I</span> to scan for targets
                </div>
              </div>
            </div>
            
            {/* Target preview */}
            {scanTarget && (
              <motion.div
                className="mt-3 pt-3 border-t border-neutral-700/50"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <div className="text-xs text-neutral-300">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-green-400 font-medium">
                      {scanTarget.entityType?.toUpperCase() || 'ENTITY'}
                    </span>
                    <span className="text-neutral-500">
                      ID: {scanTarget.entity}
                    </span>
                  </div>
                  <div className="text-neutral-400">
                    Distance: {scanTarget.distance?.toFixed(1)}m
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Crosshair enhancement during scanning */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
            <motion.div
              className="w-8 h-8 border border-green-400/40 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                rotate: 360,
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            >
              <div className="w-full h-full border border-green-400/20 rounded-full animate-ping"></div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});

ScanningIndicator.displayName = "ScanningIndicator";

export default ScanningIndicator;
