import React from 'react';
import { useHudStore } from '../store/hudStore';

const StreetNames: React.FC = () => {
  const { hudData } = useHudStore();
  const { location } = hudData;

  // Don't render if no location data
  if (!location?.streetName) {
    return null;
  }
  return (
    <div className="flex justify-center">
      <div className="bg-gradient-to-r from-neutral-900/70 via-neutral-800/80 to-neutral-900/70 border border-emerald-500/30 rounded-full px-4 py-1.5 shadow-lg">
        <div className="flex items-center space-x-2">
          {/* Location indicator */}
          <div className="flex items-center space-x-1.5">
            <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full" />
            <div className="w-0.5 h-3 bg-emerald-500/30" />
          </div>

          {/* Street name */}
          <span className="text-emerald-400 font-bold text-xs tracking-wide">
            {location.streetName}
          </span>

          {/* Crossing street separator and name */}
          {location.crossingName && location.crossingName !== location.streetName && (
            <>
              <span className="text-neutral-500 text-xs">×</span>
              <span className="text-neutral-300 font-medium text-xs">
                {location.crossingName}
              </span>
            </>
          )}

          {/* Zone separator and name */}
          {location.zoneName && (
            <>
              <div className="w-0.5 h-3 bg-neutral-600/50" />
              <span className="text-neutral-400 text-xs font-medium tracking-wider uppercase">
                {location.zoneName}
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default StreetNames;
