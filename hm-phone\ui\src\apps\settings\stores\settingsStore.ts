import { create } from 'zustand';
import { PhoneSettings } from '../types/settingsTypes';
import { settingsMockData } from '../../../fivem/mockData';
import { clientRequests } from '../../../fivem/clientRequestSender';
import config from '@shared/config';

interface SettingsState {
  phoneSettings: PhoneSettings;
  isLoading: boolean;
  error: string | null;
  lastSettingsUpdate: number; // Timestamp of last settings update
  connectedWifi: string | null; // Store the SSID of the connected WiFi network

  // Actions
  getSettings: () => Promise<void>;
  setPhoneSettings: (settings: PhoneSettings) => void;
  updateSetting: <K extends keyof PhoneSettings>(key: K, value: PhoneSettings[K]) => void;
  setConnectedWifi: (ssid: string | null) => void;
}

// Use default settings from shared config, with fallback to mock data
const defaultPhoneSettings: PhoneSettings = {
  // Start with mock data as base
  ...settingsMockData.phoneSettings,

  // Override with shared config where available
  wallpaper: String(config.ui.defaultSettings.wallpaper || settingsMockData.phoneSettings.wallpaper),
  ringtone: config.ui.defaultSettings.ringtone || settingsMockData.phoneSettings.ringtone,
  notificationSound: config.ui.defaultSettings.notificationSound || settingsMockData.phoneSettings.notificationSound,
  theme: (config.ui.defaultSettings.theme || settingsMockData.phoneSettings.theme) as 'light' | 'dark' | 'system',
  fontSize: (config.ui.defaultSettings.fontSize || settingsMockData.phoneSettings.fontSize) as 'small' | 'medium' | 'large',
  language: config.ui.defaultSettings.language || settingsMockData.phoneSettings.language,
  doNotDisturb: config.ui.defaultSettings.doNotDisturb !== undefined ? config.ui.defaultSettings.doNotDisturb : settingsMockData.phoneSettings.doNotDisturb,
  airplaneMode: config.ui.defaultSettings.airplaneMode !== undefined ? config.ui.defaultSettings.airplaneMode : settingsMockData.phoneSettings.airplaneMode,
  showNotificationsOnLockScreen: false,
  vibrate: false,
  volume: 0,
  autoLock: 0,
  lockScreenType: 'pattern'
};

export const useSettingsStore = create<SettingsState>((set, get) => ({
  phoneSettings: defaultPhoneSettings,
  isLoading: false,
  error: null,
  lastSettingsUpdate: 0,
  connectedWifi: null,

  // Actions
  getSettings: async () => {
    try {
      // Check if we already have settings loaded - use permanent caching
      // since settings like stateid and phone don't change unless explicitly modified
      const state = get();

      if (state.lastSettingsUpdate > 0) {
        // Settings already loaded, use cached data
        return;
      }

      set({ isLoading: true });

      // Use clientRequests with mock data for browser mode
      const response = await clientRequests.send('settings', 'getPhoneSettings', {}, settingsMockData.phoneSettings);

      // Update the timestamp when settings are loaded
      if (response.success) {
        set({ lastSettingsUpdate: Date.now() });
      }
    } catch (error) {
      console.error('[SettingsStore] Error requesting settings:', error);
      set({ error: 'Failed to load settings' });
    } finally {
      // Reset loading state after a short delay to ensure the UI has time to process
      setTimeout(() => set({ isLoading: false }), 500);
    }
  },

  setPhoneSettings: (settings: PhoneSettings) => {
    set({
      phoneSettings: settings,
      lastSettingsUpdate: Date.now() // Update timestamp when settings are set
    });
  },

  updateSetting: <K extends keyof PhoneSettings>(key: K, value: PhoneSettings[K]) => {
    set(state => ({
      phoneSettings: {
        ...state.phoneSettings,
        [key]: value
      },
      lastSettingsUpdate: Date.now() // Update timestamp when a setting is updated
    }));

    // TODO: Send updated setting to server
  },

  setConnectedWifi: (ssid: string | null) => {
    set({ connectedWifi: ssid });
  }
}));
