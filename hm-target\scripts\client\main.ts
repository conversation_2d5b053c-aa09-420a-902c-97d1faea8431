/// <reference types="@citizenfx/client" />

import { TargetAPI } from "./TargetAPI";
import { Target } from "./Target";
import {
  toggleTargetDebugMode,
  resetDebugModeToConfig,
  clearAllVisualizations,
  getDebugStats,
} from "./debug";

// Initialize the targeting system
const targetAPI = new TargetAPI();
const targetSystem = Target.getInstance();

// Setup resource events
on("onResourceStart", (resourceName: string) => {
  if (GetCurrentResourceName() === resourceName) {
    console.log("[hm-target] Resource started");
    // Initialize any default global targeting here if needed
  }
});

on("onResourceStop", (resourceName: string) => {
  if (GetCurrentResourceName() === resourceName) {
    targetSystem.hide();
    console.log("[hm-target] Resource stopped");
  }
});

// Debug mode commands
RegisterCommand(
  "target_debug",
  () => {
    const newState = toggleTargetDebugMode();
    console.log(`[hm-target] Debug mode ${newState ? "enabled" : "disabled"}`);
  },
  false
);

RegisterCommand(
  "target_debug_clear",
  () => {
    clearAllVisualizations();
    console.log("[hm-target] All debug visualizations cleared");
  },
  false
);

RegisterCommand(
  "target_debug_stats",
  () => {
    const stats = getDebugStats();
    console.log("[hm-target] Debug Statistics:");
    console.log(`- Debug Mode: ${stats.debugMode}`);
    console.log(`- Total Visualizations: ${stats.visualizationCount}`);
    console.log(`- Zone Visualizations: ${stats.zoneVisualizations}`);
    console.log(`- Zones Rendered Last Frame: ${stats.zonesRendered}`);
    console.log(`- Entities Debugged: ${stats.entitiesDebugged}`);
    console.log(`- Raycasts Drawn: ${stats.raycastsDrawn}`);
    console.log(`- Frame Time: ${stats.frameTime}ms`);
  },
  false
);

RegisterCommand(
  "target_debug_reset",
  () => {
    resetDebugModeToConfig();
    console.log("[hm-target] Debug mode reset to config default");
  },
  false
);

// Development/testing commands
RegisterCommand(
  "target_add_test_zone",
  () => {
    const playerPos = GetEntityCoords(PlayerPedId(), false);
    const testId = `test_zone_${Date.now()}`;

    targetAPI.addSphereZone(
      testId,
      { x: playerPos[0], y: playerPos[1], z: playerPos[2] },
      2.0,
      [
        {
          id: "test_interaction",
          icon: "fas fa-hand-paper",
          label: "Test Interaction",
          action: "hm-target:test:interaction",
        },
      ]
    );

    console.log(`Added test zone at your current position: ${testId}`);
    console.log(
      `Coordinates: x: ${playerPos[0].toFixed(2)}, y: ${playerPos[1].toFixed(
        2
      )}, z: ${playerPos[2].toFixed(2)}`
    );
  },
  false
);

export {};
