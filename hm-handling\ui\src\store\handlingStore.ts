import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  BaseHandlingData, 
  VehicleInfo, 
  HandlingProfile, 
  Vector3,
  AIHandlingType,
  FieldState,
  FieldStateInfo,
  FlagCategoryState
} from '../../../scripts/shared/types';

interface SaveProfileData {
  name: string;
  description: string;
  vehicleSpecific: boolean;
}

interface HandlingStore {
  // Core state
  isVisible: boolean;
  isApplying: boolean;
  showSaveModal: boolean;
  currentVehicle: VehicleInfo | null;
  
  // Enhanced immutable state management
  readonly stockHandling: Readonly<Partial<BaseHandlingData>>;  // Never changes
  currentHandling: Partial<BaseHandlingData>;                   // Working values
  appliedHandling: Partial<BaseHandlingData>;                   // Last applied values
  
  // Enhanced field state tracking
  fieldStates: Record<string, FieldStateInfo>;
  flagStates: Record<string, FlagCategoryState>;
  
  // Legacy compatibility
  originalHandling: Partial<BaseHandlingData>;
  hasUnsavedChanges: boolean;
  noVehicleMessage: string | null;
  modifiedFields: string[];
  appliedFields: string[];
  
  // Session management
  sessionId: string;
  lastSyncTime: number;
  
  // Profile management
  profiles: HandlingProfile[];
  saveProfileData: SaveProfileData;
    // Development mode
  isDevelopmentMode: boolean;

  // Enhanced Actions
  setVisible: (visible: boolean) => void;
  setApplying: (applying: boolean) => void;
  setShowSaveModal: (show: boolean) => void;
  updateHandlingField: (field: keyof BaseHandlingData, value: any) => void;
  updateVector3Field: (field: string, value: Vector3) => void;
  resetField: (field: keyof BaseHandlingData) => void;
  resetAllFields: () => void;
  applyField: (field: keyof BaseHandlingData) => void;
  applyAllFields: () => void;
  
  // Enhanced field state management
  getFieldState: (field: keyof BaseHandlingData) => FieldState;
  updateFieldState: (field: string, newState: Partial<FieldStateInfo>) => void;
  
  // Flag management per category
  updateFlagCategory: (category: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags', flags: string) => void;
  toggleFlag: (category: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags', flagValue: number) => void;
  
  // Session persistence
  saveToLocalStorage: () => void;
  loadFromLocalStorage: () => void;
  clearSession: () => void;
  
  // Legacy profile management
  loadProfile: (profileId: string) => void;
  setSaveProfileData: (data: Partial<SaveProfileData>) => void;
  resetSaveProfileData: () => void;
  addProfile: (profile: HandlingProfile) => void;
  deleteProfile: (profileId: string) => void;
  initializeFromData: (data: { 
    vehicle: VehicleInfo; 
    handling: Partial<BaseHandlingData>;    originalHandling?: Partial<BaseHandlingData>;
    modifiedFields?: string[];
    appliedFields?: string[];
    profiles?: HandlingProfile[] 
  }) => void;
  setNoVehicleMessage: (message: string | null) => void;
  enableDevelopmentMode: () => void;
}

// Enhanced field state utilities
const createFieldState = (
  originalValue: any, 
  currentValue: any, 
  appliedValue?: any
): FieldStateInfo => {
  const now = Date.now();
  let state = FieldState.UNCHANGED;
  
  if (currentValue !== originalValue) {
    if (appliedValue !== undefined) {
      if (currentValue === appliedValue) {
        state = FieldState.APPLIED;
      } else {
        state = FieldState.APPLIED_MODIFIED;
      }
    } else {
      state = FieldState.MODIFIED;
    }
  }
  
  return {
    state,
    originalValue,
    currentValue,
    appliedValue,
    lastModified: now,
    isValidValue: currentValue !== null && currentValue !== undefined,
    isInBounds: true // TODO: Add bounds checking
  };
};

const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Local storage utilities
const STORAGE_KEY = 'hm-handling-session';

const saveStateToStorage = (state: Partial<HandlingStore>) => {
  try {
    const sessionData = {
      currentHandling: state.currentHandling,
      appliedHandling: state.appliedHandling,
      fieldStates: state.fieldStates,
      flagStates: state.flagStates,
      sessionId: state.sessionId,
      lastSyncTime: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(sessionData));
  } catch (error) {
    console.warn('[hm-handling] Failed to save state to localStorage:', error);
  }
};

const loadStateFromStorage = (): Partial<HandlingStore> | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn('[hm-handling] Failed to load state from localStorage:', error);
  }
  return null;
};
const mockVehicleInfo: VehicleInfo = {
  model: 'adder',
  displayName: 'Truffade Adder',
  hash: 0x86618EDA,
  vehicleClass: 7,
  className: 'Super',
  isInVehicle: true,
  isDriverSeat: true,
  vehicleHandle: 1234
};

const mockOriginalHandling: Partial<BaseHandlingData> = {
  fMass: 1500.0,
  fInitialDragCoeff: 10.0,
  fPercentSubmerged: 85.0,
  vecCentreOfMassOffset: { x: 0.0, y: 0.0, z: 0.0 },
  vecInertiaMultiplier: { x: 1.0, y: 1.0, z: 1.0 },
  fDriveBiasFront: 0.0,
  nInitialDriveGears: 6,
  fInitialDriveForce: 0.3,
  fDriveInertia: 1.0,
  fClutchChangeRateScaleUpShift: 0.5,
  fClutchChangeRateScaleDownShift: 0.5,
  fInitialDriveMaxFlatVel: 0.8,
  fBrakeForce: 1.0,
  fBrakeBiasFront: 0.5,
  fHandBrakeForce: 0.8,
  fSteeringLock: 35.0,
  fTractionCurveMax: 2.5,
  fTractionCurveMin: 2.0,
  fTractionCurveLateral: 22.5,
  fTractionSpringDeltaMax: 0.15,
  fLowSpeedTractionLossMult: 1.3,
  fCamberStiffnesss: 0.0,
  fTractionBiasFront: 0.485,
  fTractionLossMult: 1.0,
  fSuspensionForce: 2.5,
  fSuspensionCompDamp: 1.2,
  fSuspensionReboundDamp: 2.1,
  fSuspensionUpperLimit: 0.1,
  fSuspensionLowerLimit: -0.15,
  fSuspensionRaise: 0.0,
  fSuspensionBiasFront: 0.5,
  fAntiRollBarForce: 1.0,
  fAntiRollBarBiasFront: 0.6,
  fRollCentreHeightFront: 0.34,
  fRollCentreHeightRear: 0.34,
  fCollisionDamageMult: 0.7,
  fWeaponDamageMult: 1.0,
  fDeformationDamageMult: 0.8,
  fEngineDamageMult: 1.5,
  fPetrolTankVolume: 65.0,
  fOilVolume: 5.0,
  fPetrolConsumptionRate: 0.5,
  fSeatOffsetDistX: 0.0,
  fSeatOffsetDistY: 0.0,
  fSeatOffsetDistZ: 0.0,
  nMonetaryValue: 1000000,
  strModelFlags: '0',
  strHandlingFlags: '0',
  strDamageFlags: '0',
  AIHandling: AIHandlingType.AVERAGE,
  fWeaponDamageScaledToVehHealthMult: 0.5,
  fDownforceModifier: 0.0,
  fPopUpLightRotation: 0.0,
  fRocketBoostCapacity: 1.25,
  fBoostMaxSpeed: 70.0
};

const mockProfiles: HandlingProfile[] = [
  {
    id: '1',
    name: 'Drift Setup',
    vehicleModel: 'adder',
    handlingData: { ...mockOriginalHandling, fTractionCurveMax: 1.8, fTractionCurveMin: 1.5 },
    createdBy: 'test-user',
    createdAt: new Date(),
    isPublic: false,
    tags: []
  },
  {
    id: '2', 
    name: 'Racing Config',
    handlingData: { ...mockOriginalHandling, fInitialDriveForce: 0.4, fBrakeForce: 1.2 },
    createdBy: 'test-user',
    createdAt: new Date(),
    isPublic: false,
    tags: []
  }
];

export const useHandlingStore = create<HandlingStore>()(
  devtools(
    (set, get) => {
      // Load persisted state from localStorage
      const persistedState = loadStateFromStorage();
      
      return {
        // Initial state
        isVisible: false,
        isApplying: false,
        showSaveModal: false,
        currentVehicle: null,
        
        // Enhanced immutable state management
        stockHandling: persistedState?.stockHandling || {},
        currentHandling: persistedState?.currentHandling || {},
        appliedHandling: persistedState?.appliedHandling || {},
        
        // Enhanced field state tracking
        fieldStates: persistedState?.fieldStates || {},
        flagStates: persistedState?.flagStates || {},
        
        // Legacy compatibility
        originalHandling: {},
        hasUnsavedChanges: false,
        noVehicleMessage: null,
        modifiedFields: [],
        appliedFields: [],
        
        // Session management
        sessionId: persistedState?.sessionId || generateSessionId(),
        lastSyncTime: persistedState?.lastSyncTime || Date.now(),
        
        // Profile management
        profiles: [],
        saveProfileData: {
          name: '',
          description: '',
          vehicleSpecific: true
        },
        isDevelopmentMode: false,

        // Basic actions
        setVisible: (visible: boolean) => set({ isVisible: visible }),
        setApplying: (applying: boolean) => set({ isApplying: applying }),
        setShowSaveModal: (show: boolean) => set({ showSaveModal: show }),

        // Enhanced handling field updates
        updateHandlingField: (field: keyof BaseHandlingData, value: any) => {
          const state = get();
          const newHandling = { ...state.currentHandling, [field]: value };
          
          // Create or update field state
          const fieldState = createFieldState(
            state.stockHandling[field],
            value,
            state.appliedHandling[field]
          );
          
          const newFieldStates = { ...state.fieldStates, [field]: fieldState };
          const hasChanges = JSON.stringify(newHandling) !== JSON.stringify(state.stockHandling);
          
          // Update legacy arrays for compatibility
          const originalValue = state.stockHandling[field];
          const isModified = value !== originalValue;
          const newModifiedFields = isModified 
            ? [...state.modifiedFields.filter(f => f !== field), field]
            : state.modifiedFields.filter(f => f !== field);
          
          const newState = { 
            currentHandling: newHandling,
            fieldStates: newFieldStates,
            hasUnsavedChanges: hasChanges,
            modifiedFields: newModifiedFields,
            lastSyncTime: Date.now()
          };
          
          set(newState);
          
          // Auto-save to localStorage
          saveStateToStorage({ ...state, ...newState });
        },

        // Enhanced field state management
        getFieldState: (field: keyof BaseHandlingData): FieldState => {
          const state = get();
          return state.fieldStates[field]?.state || FieldState.UNCHANGED;
        },

        updateFieldState: (field: string, newState: Partial<FieldStateInfo>) => {
          const state = get();
          const currentFieldState = state.fieldStates[field];
          if (currentFieldState) {
            const updatedFieldState = { ...currentFieldState, ...newState };
            set({ 
              fieldStates: { ...state.fieldStates, [field]: updatedFieldState }
            });
          }
        },

        // Enhanced apply functionality
        applyField: (field: keyof BaseHandlingData) => {
          const state = get();
          const currentValue = state.currentHandling[field];
          
          if (currentValue !== undefined) {
            // Update applied handling
            const newAppliedHandling = { ...state.appliedHandling, [field]: currentValue };
            
            // Update field state
            const fieldState = createFieldState(
              state.stockHandling[field],
              currentValue,
              currentValue  // Now applied
            );
            fieldState.state = FieldState.APPLIED;
            fieldState.lastApplied = Date.now();
            
            // Update legacy arrays
            const newAppliedFields = [...state.appliedFields.filter(f => f !== field), field];
            
            const newState = {
              appliedHandling: newAppliedHandling,
              fieldStates: { ...state.fieldStates, [field]: fieldState },
              appliedFields: newAppliedFields,
              lastSyncTime: Date.now()
            };
            
            set(newState);
            
            // Save to localStorage
            saveStateToStorage({ ...state, ...newState });
            
            // Notify game client
            fetch(`https://hm-handling/applyField`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ field, value: currentValue })
            });
          }
        },

        applyAllFields: () => {
          const state = get();
          const fieldsToApply = state.modifiedFields;
          
          fieldsToApply.forEach(field => {
            get().applyField(field as keyof BaseHandlingData);
          });
        },

        // Flag management per category
        updateFlagCategory: (category: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags', flags: string) => {
          const state = get();
          
          // Update the handling field
          get().updateHandlingField(category, flags);
          
          // Update flag state
          const flagState: FlagCategoryState = {
            categoryId: category,
            originalFlags: state.stockHandling[category] as string || '0',
            currentFlags: flags,
            appliedFlags: state.appliedHandling[category] as string || '0',
            enabledFlags: [], // TODO: Parse flags
            disabledFlags: [], // TODO: Parse flags
            lastModified: Date.now()
          };
          
          set({
            flagStates: { ...state.flagStates, [category]: flagState }
          });
        },

        toggleFlag: (category: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags', flagValue: number) => {
          const state = get();
          const currentFlags = parseInt(state.currentHandling[category] as string || '0');
          const newFlags = (currentFlags ^ flagValue).toString();
          
          get().updateFlagCategory(category, newFlags);
        },

        // Session persistence
        saveToLocalStorage: () => {
          const state = get();
          saveStateToStorage(state);
        },

        loadFromLocalStorage: () => {
          const persistedState = loadStateFromStorage();
          if (persistedState) {
            set(persistedState);
          }
        },

        clearSession: () => {
          localStorage.removeItem(STORAGE_KEY);
          set({
            sessionId: generateSessionId(),
            currentHandling: {},
            appliedHandling: {},
            fieldStates: {},
            flagStates: {},
            lastSyncTime: Date.now()          });
        },

      // Development mode
      enableDevelopmentMode: () => {
        set({
          isDevelopmentMode: true,
          isVisible: true,
          currentVehicle: mockVehicleInfo,
          originalHandling: mockOriginalHandling,
          currentHandling: { ...mockOriginalHandling },
          hasUnsavedChanges: false,
          noVehicleMessage: null,          profiles: mockProfiles
        });
      }
    }
    },
    {
      name: 'handling-store'
    }
  )
);
