import React, { useState } from 'react';
import { useDialerStore } from '../stores/dialerStore';
import { useContactsStore } from '../stores/contactsStore';
import { useNavigation } from '../../../navigation/hooks';
import { motion } from 'framer-motion';

const DialerTab: React.FC = () => {
  const [number, setNumber] = useState('');
  const { actions } = useDialerStore();
  const { contacts } = useContactsStore();
  const { openView } = useNavigation();

  // Handle key press
  const handleKeyPress = (key: string) => {
    if (number.length < 15) {
      // Limit to 15 digits
      setNumber(prev => prev + key);
    }
  };

  // Handle backspace press
  const handleBackspacePress = () => {
    setNumber(prev => prev.slice(0, -1));
  };

  // Handle long backspace press (clear all)
  const handleClearAll = () => {
    setNumber('');
  };

  // Handle call
  const handleCall = () => {
    if (!number) return;

    // Find contact info if available
    const contact = contacts?.find(c => c.number === number);

    // Make the call
    actions.makeCall(number, contact?.name, contact?.avatar || '');
  };

  // Handle add contact
  const handleAddContact = () => {
    if (!number) return;
    openView('add', { phone: number });
  };

  // Find matching contact
  const matchingContact = number ? contacts?.find(c => c.number === number) : null;

  // Define keypad letters for each number
  const keypadLetters: Record<string, string> = {
    '1': '',
    '2': 'ABC',
    '3': 'DEF',
    '4': 'GHI',
    '5': 'JKL',
    '6': 'MNO',
    '7': 'PQRS',
    '8': 'TUV',
    '9': 'WXYZ',
    '*': '',
    '0': '+',
    '#': ''
  };

  return (
    <div className="flex-1 flex flex-col bg-[#0a0f1a] overflow-hidden h-full">
      {/* Number display */}
      <div className="w-full px-6 pt-4 pb-2">
        <div className="relative w-full flex flex-col items-center">
          <div className="w-full text-center text-3xl font-medium text-white tracking-wider min-h-[40px] flex items-center justify-center">
            {number || <span className="text-white/30">Enter number</span>}
          </div>

          {matchingContact && (
            <div className="text-green-400 text-sm mt-1">{matchingContact.name}</div>
          )}

          {/* Backspace button - positioned absolutely */}
          {number && (
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={handleBackspacePress}
              onDoubleClick={handleClearAll}
              className="absolute right-1 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/10 flex items-center justify-center"
            >
              <i className="fas fa-backspace text-white/70"></i>
            </motion.button>
          )}
        </div>
      </div>

      {/* Spacer to push content down */}
      <div className="flex-grow"></div>

      {/* Dialer pad - centered with proper spacing */}
      <div className="w-full max-w-[280px] mx-auto grid grid-cols-3 gap-4 px-4 py-2">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'].map(key => (
          <motion.button
            key={key}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleKeyPress(key.toString())}
            className="flex flex-col items-center justify-center"
          >
            <div className="w-16 h-16 rounded-full bg-white/10 flex flex-col items-center justify-center">
              <span className="text-xl font-medium text-white">{key}</span>
              <span className="text-[10px] text-white/60 mt-0.5">
                {keypadLetters[key.toString()]}
              </span>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Spacer to push content up */}
      <div className="flex-grow"></div>

      {/* Bottom actions - centered layout */}
      <div className="w-full flex justify-center py-4 space-x-4">
        {/* Add contact button */}
        {number && (
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={handleAddContact}
            className="flex items-center bg-white/10 rounded-full px-4 py-2"
          >
            <i className="fas fa-user-plus text-white/70 mr-2"></i>
            <span className="text-white/70 text-sm">Add Contact</span>
          </motion.button>
        )}

        {/* Call button */}
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleCall}
          disabled={!number}
          className={`w-14 h-14 rounded-full flex items-center justify-center ${
            number ? 'bg-green-500' : 'bg-white/10'
          }`}
        >
          <i className="fas fa-phone text-white text-lg"></i>
        </motion.button>
      </div>
    </div>
  );
};

export default DialerTab;
