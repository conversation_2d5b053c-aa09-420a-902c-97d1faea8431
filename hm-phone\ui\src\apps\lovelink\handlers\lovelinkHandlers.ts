/**
 * LoveLink app message handlers
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';

// Register handler for profiles data
registerEventHandler('lovelink', 'profiles', data => {
  console.log('[LoveLink] Received profiles data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the profiles data
    // useLoveLinkStore.getState().handlers.onSetProfiles(data);
  } else {
    console.error('[LoveLink] Received invalid profiles data (not an array):', data);
  }
});

// Register handler for matches data
registerEventHandler('lovelink', 'matches', data => {
  console.log('[LoveLink] Received matches data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the matches data
    // useLoveLinkStore.getState().handlers.onSetMatches(data);
  } else {
    console.error('[LoveLink] Received invalid matches data (not an array):', data);
  }
});
