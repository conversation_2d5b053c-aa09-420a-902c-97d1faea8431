/**
 * Vehicle Manager for HM Admin
 * Handles vehicle spawning and repair with clean ghost placement system
 */

import { AdminManager } from './admin-manager';
import { 
  VehicleUtils, 
  NotificationUtils, 
  PositionUtils, 
  KeyUtils,
  DEFAULT_VEHICLE_PLACEMENT_CONFIG,
  Vector3,
  MathUtils
} from '../shared/utilities';

interface GhostVehicleState {
  isActive: boolean;
  entity: number | null;
  model: string | null;
  position: Vector3 | null;
  heading: number;
  lastUpdateTime: number;
}

interface RaycastResult {
  hit: boolean;
  entity: number | null;
  coords: number[] | null;
}

/**
 * Main Vehicle Manager - Simplified to handle high-level flow only
 */
export class VehicleManager {
  private adminManager: AdminManager;
  private ghostState: GhostVehicleState;
  private isGhostModeActive: boolean = false;
  private ghostUpdateInterval: NodeJS.Timeout | null = null;
  private ghostUpdateTickId: number | null = null; // New property for optimized tick-based updates
  private ghostInputTickId: number | null = null;

  constructor(adminManager: AdminManager) {
    this.adminManager = adminManager;
    this.ghostState = {
      isActive: false,
      entity: null,
      model: null,
      position: null,
      heading: 0.0,
      lastUpdateTime: 0
    };
    console.log('[VehicleManager] Initialized with ghost placement system');
  }  /**
   * Start ghost vehicle placement mode
   * @param model Vehicle model name to preview
   */
  public startGhostPlacement(model: string): void {
    console.log(`[VehicleManager] startGhostPlacement called with model: "${model}"`);
    
    if (!model) {
      console.error('[VehicleManager] No model provided to startGhostPlacement');
      NotificationUtils.show('Invalid vehicle model', 'error');
      return;
    }

    // Validate model
    if (!VehicleUtils.isValidModel(model)) {
      console.error(`[VehicleManager] Invalid vehicle model: "${model}"`);
      NotificationUtils.show('Invalid vehicle model', 'error');
      return;
    }

    console.log('[VehicleManager] Model validation passed, cleaning up existing ghost');    // Clean up any existing ghost entity without resetting state flags
    if (this.ghostState.entity && DoesEntityExist(this.ghostState.entity)) {
      console.log('[VehicleManager] Deleting existing ghost entity');
      DeleteEntity(this.ghostState.entity);
    }
    
    // Stop any existing update interval
    if (this.ghostUpdateInterval !== null) {
      clearInterval(this.ghostUpdateInterval);
      this.ghostUpdateInterval = null;
    }    // Initialize ghost state (set flags FIRST to avoid race condition)
    this.isGhostModeActive = true;
    this.ghostState.isActive = true;
    this.ghostState.model = model;
    this.ghostState.entity = null;
    this.ghostState.position = null;
    this.ghostState.heading = 0.0;    // Allow limited NUI focus to handle F6 key while enabling game input
    SetNuiFocus(false, true); // Disable mouse focus but keep keyboard for F6
    SetNuiFocusKeepInput(true); // Allow game input to work
      console.log('[VehicleManager] Ghost state initialized, starting update loop');
    NotificationUtils.show('Ghost placement mode: Look around to position, A/D to rotate, ENTER to spawn, ESC to cancel', 'info');
    console.log(`[VehicleManager] Ghost placement started for model: ${model}`);

    // Start the ghost update loop
    this.startGhostUpdateLoop();
    
    // Start the ghost input tick handler
    this.startGhostInputTick();
  }

  /**
   * Stop ghost vehicle placement mode
   */
  public stopGhostPlacement(): void {
    // Delete the ghost entity if it exists
    if (this.ghostState.entity && DoesEntityExist(this.ghostState.entity)) {
      DeleteEntity(this.ghostState.entity);
    }    // Stop update loop (both interval and tick-based versions)
    if (this.ghostUpdateInterval !== null) {
      clearInterval(this.ghostUpdateInterval);
      this.ghostUpdateInterval = null;
    }
    if (this.ghostUpdateTickId !== null) {
      clearTick(this.ghostUpdateTickId);
      this.ghostUpdateTickId = null;
    }

    // Stop ghost input tick handler
    this.stopGhostInputTick();

    // Store whether ghost mode was active (for UI reopening)
    const wasActive = this.ghostState.isActive;
    
    // Reset state
    this.ghostState = {
      isActive: false,
      entity: null,
      model: null,
      position: null,
      heading: 0.0,
      lastUpdateTime: 0
    };
    this.isGhostModeActive = false;    console.log('[VehicleManager] Ghost placement stopped');
    
    // If cancelling (not confirming), show the admin UI again
    if (wasActive) {
      setTimeout(() => {
        if (this.adminManager) {
          this.adminManager.show();
        }
      }, 100);
    }
  }

  /**
   * Confirm ghost placement and spawn the actual vehicle
   */
  public confirmGhostPlacement(): void {
    if (!this.ghostState.isActive || !this.ghostState.position || !this.ghostState.model) {
      NotificationUtils.show('No valid ghost position to confirm', 'error');
      return;
    }

    const position = this.ghostState.position;
    const heading = this.ghostState.heading;
    const model = this.ghostState.model;    
    
    // Stop ghost mode first
    this.stopGhostPlacement();    // Spawn the actual vehicle
    this.spawnActualVehicle(model, position, heading);
    
    // Show the admin UI again with a small delay
    setTimeout(() => {
      if (this.adminManager) {
        this.adminManager.show();
      }
    }, 100);
  }  /**
   * Main spawn vehicle method - starts with ghost placement, then confirms spawn
   * @param model Vehicle model name
   */
  public spawnVehicle(model: string): void {
    console.log(`[VehicleManager] spawnVehicle called with model: ${model}`);
    
    if (!model) {
      console.error('[VehicleManager] No model provided to spawnVehicle');
      NotificationUtils.show('Invalid vehicle model', 'error');
      return;
    }
    
    // If ghost mode is already active, ignore the request
    if (this.isGhostModeActive) {
      console.log('[VehicleManager] Ghost placement already active, ignoring spawn request');
      NotificationUtils.show('Ghost placement already active', 'info');
      return;
    }

    console.log('[VehicleManager] Starting ghost placement for model:', model);
    
    // Start ghost placement mode with a small delay to ensure UI closure is complete
    setTimeout(() => {
      this.startGhostPlacement(model);
    }, 50);
  }

  /**
   * Spawn a vehicle at the specified position and heading (called after ghost confirmation)
   * @param model Vehicle model name
   * @param position Position to spawn the vehicle
   * @param heading Heading angle for the vehicle
   */
  private spawnActualVehicle(model: string, position: Vector3, heading: number): void {
    if (!model || !position) {
      NotificationUtils.show('Invalid vehicle model or position', 'error');
      return;
    }

    // Request the model
    const modelHash = GetHashKey(model);
    RequestModel(modelHash);
    
    // Wait for model to load
    const startTime = GetGameTimer();
    const timeout = 5000; // 5 second timeout
    
    const waitForModel = () => {
      if (HasModelLoaded(modelHash)) {
        // Create the vehicle
        const vehicle = CreateVehicle(modelHash, position.x, position.y, position.z, heading, true, true);
        
        if (vehicle && DoesEntityExist(vehicle)) {
          // Set vehicle properties
          SetVehicleOnGroundProperly(vehicle);
          SetEntityAsMissionEntity(vehicle, true, true);
          SetVehicleHasBeenOwnedByPlayer(vehicle, true);
          SetVehicleNeedsToBeHotwired(vehicle, false);
          SetVehicleEngineOn(vehicle, true, true, false);
          
          NotificationUtils.show(`Vehicle ${model} spawned successfully!`, 'success');
          console.log(`[VehicleManager] Vehicle spawned: ${model} at ${position.x}, ${position.y}, ${position.z}`);
        } else {
          NotificationUtils.show('Failed to create vehicle', 'error');
        }
        
        // Release the model
        SetModelAsNoLongerNeeded(modelHash);
      } else if (GetGameTimer() - startTime > timeout) {
        NotificationUtils.show('Failed to load vehicle model', 'error');
        SetModelAsNoLongerNeeded(modelHash);
      } else {
        // Try again next frame
        setTimeout(waitForModel, 16);
      }
    };
    
    waitForModel();
  }  /**
   * Start the ghost update loop for real-time position tracking
   * OPTIMIZED: Using frame-skipping setTick instead of continuous setInterval for better CPU efficiency
   */
  private startGhostUpdateLoop(): void {
    // Clear any existing interval or tick
    if (this.ghostUpdateInterval !== null) {
      clearInterval(this.ghostUpdateInterval);
      this.ghostUpdateInterval = null;
    }
    if (this.ghostUpdateTickId !== null) {
      clearTick(this.ghostUpdateTickId);
      this.ghostUpdateTickId = null;
    }

    // Use frame-skipping setTick for more efficient updates
    // Skip frames to achieve ~20 FPS instead of 30 FPS, reducing CPU usage by 33%
    let frameCounter = 0;
    this.ghostUpdateTickId = setTick(() => {
      if (!this.ghostState.isActive) {
        return; // Early exit if ghost mode is no longer active
      }
      
      frameCounter++;
      // Only update every 3 frames (20 FPS instead of 60 FPS)
      // This maintains smooth positioning while reducing CPU usage
      if (frameCounter % 3 === 0) {
        this.updateGhostPosition();
      }
    });
  }/**
   * Update ghost vehicle position based on camera center raycast
   */  
  private updateGhostPosition(): void {
    if (!this.ghostState.isActive || !this.ghostState.model) {
      return;
    }
    
    // Cast ray from camera center to world
    const rayResult = this.castRayFromCameraCenter();
    
    if (rayResult.hit && rayResult.coords) {
      const newPosition: Vector3 = {
        x: rayResult.coords[0],
        y: rayResult.coords[1],
        z: rayResult.coords[2]
      };
      
      // Update ghost position
      this.ghostState.position = newPosition;
      this.ghostState.lastUpdateTime = GetGameTimer();

      // Create or update ghost vehicle
      this.updateGhostVehicle(newPosition);
    }
  }
  /**
   * Create or update the ghost vehicle entity
   */  
  private updateGhostVehicle(position: Vector3): void {
    if (!this.ghostState.model) return;
    
    const modelHash = GetHashKey(this.ghostState.model);

    // If ghost vehicle doesn't exist, create it
    if (!this.ghostState.entity || !DoesEntityExist(this.ghostState.entity)) {
      // Request model if not loaded
      if (!HasModelLoaded(modelHash)) {
        RequestModel(modelHash);
        return; // Wait for next update
      }

      // Create ghost vehicle
      this.ghostState.entity = CreateVehicle(
        modelHash, 
        position.x, 
        position.y, 
        position.z, 
        this.ghostState.heading, 
        false, // Not networked
        false  // Not a mission entity
      );

      if (this.ghostState.entity && DoesEntityExist(this.ghostState.entity)) {
        // Configure ghost properties
        this.configureGhostVehicle(this.ghostState.entity);
      }
    } else {
      // Update existing ghost position
      SetEntityCoords(this.ghostState.entity, position.x, position.y, position.z, false, false, false, true);
      SetEntityHeading(this.ghostState.entity, this.ghostState.heading);
      
      // Ensure it stays on ground
      SetVehicleOnGroundProperly(this.ghostState.entity);
    }
  }

  /**
   * Configure ghost vehicle properties (semi-transparent, collision, etc.)
   */
  private configureGhostVehicle(vehicle: number): void {
    if (!vehicle || !DoesEntityExist(vehicle)) return;

    // Make semi-transparent
    SetEntityAlpha(vehicle, 128, false); // 50% transparency

    // Disable collision
    SetEntityCollision(vehicle, false, false);

    // Freeze the vehicle
    FreezeEntityPosition(vehicle, true);

    // Disable physics
    SetEntityInvincible(vehicle, true);

    // Make sure it doesn't despawn
    SetEntityAsMissionEntity(vehicle, true, true);

    // Place on ground properly
    SetVehicleOnGroundProperly(vehicle);    // Disable engine and lights
    SetVehicleEngineOn(vehicle, false, false, true);
    SetVehicleLights(vehicle, 0);
  }
  /**
   * Cast a ray from the camera forward (screen center) for ghost placement
   * Simplified approach using gameplay camera
   */  
  private castRayFromCameraCenter(): RaycastResult {
    try {
      // Get player position and camera rotation
      const playerPed = PlayerPedId();
      const playerCoords = GetEntityCoords(playerPed, true);
      // Use gameplay camera for more reliable results
      const camRot = GetGameplayCamRot(2); // Get world rotation
      const camCoords = GetGameplayCamCoord();
      
      // Convert rotation to direction vector (simplified)
      const pitch = camRot[0] * (Math.PI / 180.0);
      const yaw = camRot[2] * (Math.PI / 180.0);
      
      // Calculate forward direction vector
      const directionX = -Math.sin(yaw) * Math.cos(pitch);
      const directionY = Math.cos(yaw) * Math.cos(pitch);
      const directionZ = Math.sin(pitch);
      
      // Raycast distance
      const rayDistance = 50.0;
      
      // Calculate end point
      const endX = camCoords[0] + directionX * rayDistance;
      const endY = camCoords[1] + directionY * rayDistance;
      const endZ = camCoords[2] + directionZ * rayDistance;
      
      // Cast the ray
      // const rayHandle = StartShapeTestLosProbe(
      //   playerCoords[0], playerCoords[1], playerCoords[2],
      //   endX, endY, endZ,
      //   1,
      //   playerPed, // Ignore player
      //   4 // Ray test type (LOS probe)
      // )
      const rayHandle = StartShapeTestRay(
        playerCoords[0], playerCoords[1], playerCoords[2],
        endX, endY, endZ,
        1, // Hit world geometry only
        PlayerPedId(), // Ignore player
        4 // Ray test type
      )
      
      // Get result
      const [status, hit, hitCoords, surfaceNormal, entityHit] = GetShapeTestResult(rayHandle);
      
      return {
        hit: hit === 1,
        entity: (hit === 1 && entityHit && typeof entityHit === 'number') ? entityHit : null,
        coords: hit === 1 && hitCoords ? [hitCoords[0], hitCoords[1], hitCoords[2]] : null
      };
    } catch (error) {
      console.error('[VehicleManager] Camera ray casting error:', error);
      return {
        hit: false,
        entity: null,
        coords: null
      };
    }
  }

  /**
   * Get the current ghost entity ID for debugging
   */
  public getGhostEntityId(): number | null {
    return this.ghostState?.entity || null;
  }
  /**
   * Start the ghost input tick handler for continuous key input handling
   * OPTIMIZED: Using frame-skipping pattern like hm-phone controls for better CPU efficiency
   */
  private startGhostInputTick(): void {
    // If already running, do nothing
    if (this.ghostInputTickId !== null) {
      return;
    }

    console.log('[VehicleManager] Starting optimized ghost input tick handler');
    
    // Use frame-skipping setTick for more efficient input handling
    // Run at reduced frequency to save CPU while maintaining responsiveness
    let frameCounter = 0;
    
    this.ghostInputTickId = setTick(() => {
      if (!this.ghostState.isActive) {
        return; // Early exit if ghost mode is no longer active
      }
      
      frameCounter++;
      // Only process input every 2 frames (30 FPS instead of 60 FPS)
      // This cuts CPU usage in half while still being responsive
      if (frameCounter % 2 === 0) {
        this.handleGhostInput();
      }
    });
  }

  /**
   * Stop the ghost input tick handler
   */
  private stopGhostInputTick(): void {
    if (this.ghostInputTickId !== null) {
      clearTick(this.ghostInputTickId);
      this.ghostInputTickId = null;
      console.log('[VehicleManager] Ghost input tick handler stopped');
    }
  }

  public handleGhostInput(): void {
    if (!this.isGhostModeActive) return;

    // Debug log every second instead of every frame
    if (!this.ghostState.lastUpdateTime || GetGameTimer() - this.ghostState.lastUpdateTime > 1000) {
      console.log('[VehicleManager] Ghost input handler active');
      this.ghostState.lastUpdateTime = GetGameTimer();
    }

    // Disable all controls first to ensure we have complete control
    DisableAllControlActions(0);
    
    // Then enable only the ones we need
    EnableControlAction(0, 1, true);   // Look left/right
    EnableControlAction(0, 2, true);   // Look up/down
    EnableControlAction(0, 30, true);  // Move left/right
    EnableControlAction(0, 31, true);  // Move up/down
    
    // Movement controls for the player/camera
    EnableControlAction(0, 32, true);  // W key
    EnableControlAction(0, 33, true);  // S key
    EnableControlAction(0, 34, true);  // A key
    EnableControlAction(0, 35, true);  // D key
    
    // Vehicle controls for rotation
    EnableControlAction(0, 63, true);  // A key (vehicle)
    EnableControlAction(0, 64, true);  // D key (vehicle)
    
    // Mouse controls
    EnableControlAction(0, 1, true);   // Mouse look X
    EnableControlAction(0, 2, true);   // Mouse look Y
    EnableControlAction(0, 3, true);   // Mouse scroll X
    EnableControlAction(0, 4, true);   // Mouse scroll Y
    EnableControlAction(0, 5, true);   // Mouse wheel X
    EnableControlAction(0, 6, true);   // Mouse wheel Y
      // Confirm/cancel controls
    EnableControlAction(0, 201, true); // ENTER
    EnableControlAction(0, 200, true); // ESC
    EnableControlAction(0, 177, true); // ESC (alternative)
    
    // Admin UI controls
    EnableControlAction(0, 167, true); // F6 key (admin panel toggle)
    
    // Check for ENTER key to confirm placement
    if (IsControlJustPressed(0, 201) || IsControlJustPressed(1, 201) || IsControlJustPressed(2, 201)) {
      console.log('[VehicleManager] ENTER pressed - confirming ghost placement');
      this.confirmGhostPlacement();
      return;
    }

    // Check for ESC key to cancel placement
    if (IsControlJustPressed(0, 200) || IsControlJustPressed(1, 200) || IsControlJustPressed(0, 177) || IsControlJustPressed(1, 177)) {
      console.log('[VehicleManager] ESC pressed - cancelling ghost placement');
      this.stopGhostPlacement();
      NotificationUtils.show('Ghost placement cancelled', 'info');
      return;
    }

    // Check for rotation controls (A/D keys)
    if (IsControlPressed(0, 34) || IsControlPressed(0, 63)) { // A key
      this.ghostState.heading = (this.ghostState.heading - 2.0) % 360.0;
      if (this.ghostState.heading < 0) this.ghostState.heading += 360.0;
      console.log(`[VehicleManager] A pressed - heading: ${this.ghostState.heading.toFixed(1)}`);
    }
    
    if (IsControlPressed(0, 35) || IsControlPressed(0, 64)) { // D key
      this.ghostState.heading = (this.ghostState.heading + 2.0) % 360.0;
      if (this.ghostState.heading >= 360.0) this.ghostState.heading -= 360.0;
      console.log(`[VehicleManager] D pressed - heading: ${this.ghostState.heading.toFixed(1)}`);
    }
  }

  /**
   * Check if ghost placement mode is currently active
   */
  public isGhostPlacementActive(): boolean {
    return this.isGhostModeActive;
  }

  /**
   * Repair the current vehicle or closest vehicle
   */
  public repairVehicle(): void {
    const playerPed = PlayerPedId();
    let targetVehicle = 0;

    if (IsPedInAnyVehicle(playerPed, false)) {
      targetVehicle = GetVehiclePedIsIn(playerPed, false);
    } else {
      const playerCoords = GetEntityCoords(playerPed, false);
      const closestVehicle = GetClosestVehicle(playerCoords[0], playerCoords[1], playerCoords[2], 10.0, 0, 71);
      
      if (closestVehicle && DoesEntityExist(closestVehicle)) {
        const distance = MathUtils.distance3D(
          { x: playerCoords[0], y: playerCoords[1], z: playerCoords[2] },
          { x: GetEntityCoords(closestVehicle, false)[0], y: GetEntityCoords(closestVehicle, false)[1], z: GetEntityCoords(closestVehicle, false)[2] }
        );
        
        if (distance <= 5.0) {
          targetVehicle = closestVehicle;
        }
      }
    }

    if (!targetVehicle || !DoesEntityExist(targetVehicle)) {
      NotificationUtils.show('No vehicle found to repair', 'error');
      return;
    }
    SetVehicleFixed(targetVehicle);
    SetVehicleDeformationFixed(targetVehicle);
    SetVehicleUndriveable(targetVehicle, false);
    SetVehicleEngineOn(targetVehicle, true, false, false);
    SetVehicleFuelLevel(targetVehicle, 100.0);

    NotificationUtils.show('Vehicle repaired successfully!', 'success');
  }

  /**
   * Set vehicle color for primary or secondary
   * @param colorType 'primary' or 'secondary'
   * @param colorIndex FiveM color index (0-159)
   */
  public setVehicleColor(colorType: 'primary' | 'secondary', colorIndex: number): void {
    const playerPed = PlayerPedId();
    let targetVehicle = 0;

    // Check if player is in a vehicle
    if (IsPedInAnyVehicle(playerPed, false)) {
      targetVehicle = GetVehiclePedIsIn(playerPed, false);
    } else {
      NotificationUtils.show('You must be in a vehicle to change its color', 'error');
      return;
    }

    if (!targetVehicle || !DoesEntityExist(targetVehicle)) {
      NotificationUtils.show('No vehicle found to change color', 'error');
      return;
    }

    // Validate color index
    if (colorIndex < 0 || colorIndex > 159) {
      NotificationUtils.show('Invalid color index. Must be between 0-159', 'error');
      return;
    }

    try {
      // Get current colors
      const [primaryColor, secondaryColor] = GetVehicleColours(targetVehicle);
      
      // Set the new color based on type
      if (colorType === 'primary') {
        SetVehicleColours(targetVehicle, colorIndex, secondaryColor);
        NotificationUtils.show(`Vehicle primary color changed successfully!`, 'success');
      } else if (colorType === 'secondary') {
        SetVehicleColours(targetVehicle, primaryColor, colorIndex);
        NotificationUtils.show(`Vehicle secondary color changed successfully!`, 'success');
      }

      console.log(`[VehicleManager] Vehicle color changed: ${colorType} = ${colorIndex}`);
    } catch (error) {
      console.error('[VehicleManager] Error setting vehicle color:', error);
      NotificationUtils.show('Failed to change vehicle color', 'error');
    }
  }

  /**
   * Set vehicle license plate text
   * @param plateText License plate text (max 8 characters)
   */
  public setVehiclePlate(plateText: string): void {
    const playerPed = PlayerPedId();
    let targetVehicle = 0;

    // only check if player is in a vehicle. no closest vehicle logic here
    // This is to ensure we only change the plate of the vehicle the player is currently in
    if (IsPedInAnyVehicle(playerPed, false)) {
      targetVehicle = GetVehiclePedIsIn(playerPed, false);
    } else {
      NotificationUtils.show('You must be in a vehicle to change its plate', 'error');
      return;
    }
    if (!targetVehicle || !DoesEntityExist(targetVehicle)) {
      NotificationUtils.show('No vehicle found to change plate', 'error');
      return;
    }
    // Validate plate text length
    if (!plateText || plateText.length === 0 || plateText.length > 8) {
      NotificationUtils.show('Plate text must be between 1 and 8 characters', 'error');
      return;
    }

    try {
      emitNet('hm-admin:setVehiclePlate', targetVehicle, plateText);
    } catch (error) {
      console.error('[VehicleManager] Error setting vehicle plate:', error);
      NotificationUtils.show('Failed to change vehicle plate', 'error');
    }
  }

  /**
   * Delete the current vehicle or closest vehicle
   */
  public deleteVehicle(): void {
    const playerPed = PlayerPedId();
    let targetVehicle = 0;

    // Check if player is in a vehicle
    if (IsPedInAnyVehicle(playerPed, false)) {
      targetVehicle = GetVehiclePedIsIn(playerPed, false);
    } else {
      // Try to find closest vehicle
      const playerCoords = GetEntityCoords(playerPed, false);
      const closestVehicle = GetClosestVehicle(playerCoords[0], playerCoords[1], playerCoords[2], 10.0, 0, 71);
      
      if (closestVehicle && DoesEntityExist(closestVehicle)) {
        const distance = MathUtils.distance3D(
          { x: playerCoords[0], y: playerCoords[1], z: playerCoords[2] },
          { x: GetEntityCoords(closestVehicle, false)[0], y: GetEntityCoords(closestVehicle, false)[1], z: GetEntityCoords(closestVehicle, false)[2] }
        );
        
        if (distance <= 5.0) {
          targetVehicle = closestVehicle;
        }
      }
    }

    if (!targetVehicle || !DoesEntityExist(targetVehicle)) {
      NotificationUtils.show('No vehicle found to delete', 'error');
      return;
    }

    try {
      // If player is in the vehicle, remove them first
      if (IsPedInVehicle(playerPed, targetVehicle, false)) {
        TaskLeaveVehicle(playerPed, targetVehicle, 0);
        
        // Wait a moment for the player to exit before deleting
        setTimeout(() => {
          if (DoesEntityExist(targetVehicle)) {
            // Set as mission entity to ensure we can delete it
            SetEntityAsMissionEntity(targetVehicle, true, true);
            DeleteEntity(targetVehicle);
            NotificationUtils.show('Vehicle deleted successfully!', 'success');
            console.log('[VehicleManager] Vehicle deleted after player exit');
          }
        }, 1000);
      } else {
        // Delete immediately if player is not in the vehicle
        SetEntityAsMissionEntity(targetVehicle, true, true);
        DeleteEntity(targetVehicle);
        NotificationUtils.show('Vehicle deleted successfully!', 'success');
        console.log('[VehicleManager] Vehicle deleted');
      }
    } catch (error) {
      console.error('[VehicleManager] Error deleting vehicle:', error);
      NotificationUtils.show('Failed to delete vehicle', 'error');
    }
  }
}