// Service for sending NUI messages to the FiveM client script

import * as SharedTypes from "@shared";

/**
 * Sends a message to the NUI callback using dynamic resource name.
 * @param event The event name.
 * @param data The event data.
 */
async function sendNuiMessage<T = unknown>(event: string, data: T): Promise<Response> {
  try {
    // Get the resource name dynamically to ensure correct URL
    const resourceName = (window as Window & { GetParentResourceName?: () => string }).GetParentResourceName?.() || 'hm-inventory';
    const url = `https://${resourceName}/${event}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      console.error(`[ClientRequestSender] NUI Error: ${response.status} ${response.statusText} for event ${event}`);
    }
    
    console.log('[ClientRequestSender] Response received:', {
      event,
      url,
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });
    
    return response;
  } catch (error) {
    console.error(`[ClientRequestSender] Error sending NUI message ${event}:`, error);
    console.log(`[ClientRequestSender] Data sent: ${JSON.stringify(data)}`);
    // Return a synthetic error response or rethrow, depending on desired error handling
    // For now, rethrowing to make it clear the call failed.
    throw error;
  }
}

// Specific request functions
export const sendMoveItemRequest = (data: SharedTypes.MoveItemRequest) => {
  return sendNuiMessage('MoveItemRequest', data);
};

export const sendDropItemRequest = (data: SharedTypes.DropItemRequest) => {
  return sendNuiMessage('DropItemRequest', data);
};

export const sendEquipItemRequest = (data: SharedTypes.EquipItemRequest) => {
  return sendNuiMessage('EquipItemRequest', data);
};

export const sendPickupItemRequest = (data: SharedTypes.PickupItemRequest) => {
  return sendNuiMessage('PickupItemRequest', data);
};

export const sendTransferItemRequest = (data: SharedTypes.TransferItemRequest) => {
  return sendNuiMessage('TransferItemRequest', data);
};

import { SplitStackRequest } from '@shared';

/**
 * Send a request to split a stack of items into two stacks
 * @param data The split stack request data
 * @returns Promise with the response from the server
 */
export const sendSplitStackRequest = (data: SplitStackRequest) => {
  console.log(`Sending split stack request: ${JSON.stringify(data)}`);
  return sendNuiMessage('SplitStackRequest', data);
};

export const sendUseItemRequest = (data: SharedTypes.UseItemRequest) => {
  return sendNuiMessage('UseItemRequest', data);
};

// Example of a request that might expect a response from the client script
// export async function someRequestWithResponse(data: any): Promise<any> {
//   const response = await sendNuiMessage('SomeClientEventWithResponse', data);
//   return response.json(); // Assuming the client script sends back JSON
// }
