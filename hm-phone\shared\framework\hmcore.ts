/**
 * HM Core Framework Adapter
 *
 * This file implements the IFramework interface for HM Core.
 */

import { IFramework, FrameworkPlayer, FrameworkItem } from './types';

export class HMCoreFramework implements IFramework {
    private playerManager: any;
    name = 'hm-core';
    isLoaded = false;

    constructor() {
        try {
            console.log('[Framework] Attempting to initialize HM Core framework...');
            
            // Check if we're on server or client side
            const isServer = IsDuplicityVersion();
            console.log(`[Framework] Running on ${isServer ? 'server' : 'client'} side`);
            
            // Use appropriate exports for client or server side
            if (isServer) {
                this.playerManager = global.exports['hm-core'].getPlayerManager();
                console.log('[Framework] Server-side: Attempting to get PlayerManager from hm-core');
            } else {
                // On client side, try to get client-side player manager
                this.playerManager = global.exports['hm-core'].getPlayerManager();
                console.log('[Framework] Client-side: Attempting to get PlayerManager from hm-core');
            }
            
            this.isLoaded = !!this.playerManager;
            if (this.isLoaded) {
                console.log('[Framework] HM Core PlayerManager detected and loaded successfully');
            } else {
                console.error('[Framework] Failed to load HM Core PlayerManager - exports may not be available yet');
            }
        } catch (error) {
            console.error('[Framework] Error initializing HM Core framework:', error);
            this.isLoaded = false;
        }
    }

    /**
     * Get the raw HM Core object
     */
    getCore() {
        return this.playerManager;
    }

    /**
     * Get player data by server ID
     */
    getPlayer(source: number): FrameworkPlayer | null {
        if (!this.playerManager) return null;
        
        try {
            const player = this.playerManager.getPlayer(source);
            if (!player) return null;

            // Get active character data
            const character = this.playerManager.getActiveCharacter(source);
            
            return {
                source,
                identifier: player.license,
                stateid: character?.stateid,
                firstName: character?.first_name,
                lastName: character?.last_name,
                name: character ? `${character.first_name} ${character.last_name}` : player.name,
                phoneNumber: character?.phoneNumber || character?.phone, // Check both fields
                job: character?.job,
                gang: character?.gang,
                money: character?.money,
                metadata: character?.metadata
            } as FrameworkPlayer;
        } catch (error) {
            console.error('[Framework] Error getting HM Core player:', error);
            return null;
        }
    }

    /**
     * Get player by identifier
     */
    getPlayerByIdentifier(identifier: string): FrameworkPlayer | null {
        if (!this.playerManager) return null;
        return this.playerManager.getPlayerByIdentifier(identifier) as FrameworkPlayer;
    }

    /**
     * Get player by phone number
     * Note: HM Core might not have a direct method for this
     */
    getPlayerByPhoneNumber(phoneNumber: string): FrameworkPlayer | null {
        if (!this.playerManager) return null;
        return this.playerManager.getPlayerByPhoneNumber(phoneNumber) as FrameworkPlayer;
    }

    /**
     * Get all players
     */
    private getAllPlayers(): FrameworkPlayer[] {
        return this.playerManager.getAllPlayers() as FrameworkPlayer[];
    }

    /**
     * Get player identifier
     */
    getPlayerIdentifier(source: number): string | null {
        return this.playerManager?.getPlayerIdentifier(source) || null;
    }

    /**
     * Get player phone number
     */
    getPlayerPhoneNumber(source: number): string | null {
        return this.playerManager?.getPlayerPhoneNumber(source) || null;
    }

    /**
     * Check if player has permission
     */
    hasPermission(source: number, permission: string): boolean {
        return this.playerManager?.hasPermission(source, permission) || false;
    }

    /**
     * Add money to player
     */
    addMoney(source: number, amount: number, type = 'cash'): boolean {
        return this.playerManager?.addMoney(source, amount, type) || false;
    }

    /**
     * Remove money from player
     */
    removeMoney(source: number, amount: number, type = 'cash'): boolean {
        return this.playerManager?.removeMoney(source, amount, type) || false;
    }

    /**
     * Get player money
     */
    getMoney(source: number, type = 'cash'): number {
        return this.playerManager?.getMoney(source, type) || 0;
    }

    /**
     * Get inventory item
     */
    getInventoryItem(source: number, item: string): FrameworkItem | null {
        return this.playerManager?.getInventoryItem(source, item) || null;
    }

    /**
     * Add inventory item
     */
    addInventoryItem(source: number, item: string, amount: number, metadata: any = {}): boolean {
        return this.playerManager?.addInventoryItem(source, item, amount, metadata) || false;
    }

    /**
     * Remove inventory item
     */
    removeInventoryItem(source: number, item: string, amount: number, metadata: any = {}): boolean {
        return this.playerManager?.removeInventoryItem(source, item, amount, metadata) || false;
    }

    /**
     * Send notification to player
     */
    notify(source: number, message: string, type = 'info', duration = 5000): void {
        this.playerManager?.notify(source, message, type, duration);
    }
}
