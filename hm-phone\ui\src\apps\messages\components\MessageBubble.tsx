import React, { useState, useRef } from 'react';
import { Message } from '@shared/types';
import { ImageMessage } from './messageTypes/ImageMessage';
import { LocationMessage } from './messageTypes/LocationMessage';
import ContactMessage from './messageTypes/ContactMessage';
import { TextMessage } from './messageTypes/TextMessage';
import MessageAvatar from './MessageAvatar';

// Define UI-specific properties that are not part of the Message type
interface UIProperties {
  showAvatar?: boolean;
  avatar?: string | null;
  displayName?: string;
  isEdited?: boolean;
  isDeleted?: boolean;
  status?: string;
  isContact?: boolean;
}

interface MessageBubbleProps {
  message: Message & UIProperties;
  onDelete: (id: number) => void;
  onEdit: (id: number, content: string) => void;
  onReply: (message: Message & UIProperties) => void;
  isMine: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isMine }) => {
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const editInputRef = useRef<HTMLInputElement>(null);
  const [editedContent, setEditedContent] = useState('');
  // We're not using editing functionality in this version

  const renderMainContent = () => {
    if (!message.type) return null;

    switch (message.type) {
      case 'text': {
        // Use more specific type for TextMessage
        return (
          <TextMessage
            message={(message as unknown) as {
              type: 'text';
              message: string;
              isEdited?: boolean;
              timestamp?: string;
              metadata?: Record<string, unknown>;
            }}
            isEditing={false}
            editedContent={editedContent}
            editInputRef={editInputRef as React.RefObject<HTMLInputElement>}
            setEditedContent={setEditedContent}
            handleEditSubmit={() => {}}
          />
        );
      }
      case 'image': {
        // Use more specific type for ImageMessage
        return (
          <ImageMessage
            message={(message as unknown) as {
              type: 'image';
              message?: string;
              timestamp?: string;
              metadata?: {
                mediaUrls?: string[];
                thumbnailUrls?: string[];
                hasText?: boolean;
                timestamp?: number;
                [key: string]: unknown;
              };
            }}
            showImageModal={showImageModal}
            selectedImageIndex={selectedImageIndex}
            setSelectedImageIndex={setSelectedImageIndex}
            setShowImageModal={setShowImageModal}
          />
        );
      }
      case 'location': {
        // Use more specific type for LocationMessage
        return (
          <LocationMessage 
            message={(message as unknown) as {
              type: 'location';
              message?: string;
              timestamp?: string;
              content?: {
                x: number;
                y: number;
                z: number;
              };
              metadata?: {
                location: {
                  name: string;
                  address: string;
                  coordinates?: {
                    lat: number;
                    lng: number;
                  };
                };
                timestamp?: number;
              };
            }} 
          />
        );
      }
      case 'contact': {
        // Use more specific type for ContactMessage
        return (
          <ContactMessage 
            message={(message as unknown) as {
              type: 'contact';
              message?: string;
              timestamp?: string;
              content?: {
                name: string;
                number: string;
              };
              metadata?: {
                contact: {
                  id?: number;
                  identifier?: string;
                  stateid?: string;
                  owner_number?: string;
                  number: string;
                  name: string;
                  favorite?: number;
                  avatar?: string | null;
                  created_at?: string;
                  updated_at?: string;
                };
                timestamp?: number;
              };
            }} 
          />
        );
      }
      default:
        return null;
    }
  };

  return (
    <div
      className={`message-bubble group flex items-start gap-2 ${
        isMine ? 'justify-end' : 'justify-start'
      } relative mb-2`}
    >
      {/* Message Menu */}
      {!isMine && message.showAvatar && (
        <MessageAvatar
          showAvatar={message.showAvatar}
          avatar={message.avatar}
          sender={message.sender}
          displayName={message.displayName}
          isContact={message.isContact}
        />
      )}

      <div
        className={`w-fit min-w-0 max-w-[85%] flex-shrink ${
          !message.showAvatar && !isMine ? 'ml-10' : ''
        }`}
      >
        <div
          className={`rounded-2xl overflow-hidden ${
            message.isDeleted
              ? 'bg-zinc-800/40 text-white/40'
              : isMine
              ? 'bg-gradient-to-r from-sky-400/10 via-sky-400/15 to-sky-400/20 backdrop-blur-sm border border-white/[0.08] shadow-lg text-white'
              : 'bg-gradient-to-r from-zinc-800/60 via-zinc-800/70 to-zinc-800/80 backdrop-blur-sm border border-white/[0.08] shadow-lg text-white'
          }`}
        >
          {renderMainContent()}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
