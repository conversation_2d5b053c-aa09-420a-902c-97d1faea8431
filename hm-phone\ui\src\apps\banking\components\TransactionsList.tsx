import React from 'react';
import { useBankingStore } from '../stores/bankingStore';
import TransactionItem from './TransactionItem';
import { motion } from 'framer-motion';

const TransactionsList: React.FC = () => {
  const { selectedAccountId, getAccountTransactions } = useBankingStore();
  const accountTransactions = getAccountTransactions(selectedAccountId);

  return (
    <div className="px-4 space-y-2 pb-4">
      {accountTransactions.length > 0 ? (
        accountTransactions.map((transaction, index) => (
          <motion.div
            key={transaction.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <TransactionItem transaction={transaction} />
          </motion.div>
        ))
      ) : (
        <div className="text-center py-8 text-white/30">No transactions yet</div>
      )}
    </div>
  );
};

export default TransactionsList;
