class SharedCore {
    private static instance: SharedCore;
    private config: FrameworkConfig | undefined;

    private constructor() {
        this.initialize();
    }

    public static getInstance(): SharedCore {
        if (!SharedCore.instance) {
            SharedCore.instance = new SharedCore();
        }
        return SharedCore.instance;
    }

    private initialize(): void {
        this.config = this.loadConfig();
        this.setupSharedUtilities();
        this.validateEnvironment();
    }

    private loadConfig(): FrameworkConfig {
        // Default configuration
        const defaultConfig: FrameworkConfig = {
            framework: {
                name: 'Custom FiveM Framework',
                version: '1.0.0',
                author: 'ItsHicham',
                debug: GetConvar('debug_mode', 'false') === 'true',
            },
            player: {
                maxCharacters: parseInt(GetConvar('max_characters', '3')),
                autoSave: parseInt(GetConvar('auto_save_interval', '30')) * 1000,
                spawnProtection: parseInt(GetConvar('spawn_protection', '10')) * 1000,
                defaultMoney: {
                    cash: parseInt(GetConvar('default_cash', '5000')),
                    bank: parseInt(GetConvar('default_bank', '10000')),
                },
            },
            database: {
                connectionTimeout: parseInt(GetConvar('db_timeout', '10')) * 1000,
                maxRetries: parseInt(GetConvar('db_max_retries', '3')),
            },
            server: {
                maxPlayers: GetConvarInt('sv_maxclients', 32),
                serverName: GetConvar('sv_hostname', 'FiveM Server'),
                enablePvp: GetConvar('enable_pvp', 'true') === 'true',
            },
        };

        // Try to load custom config if it exists
        try {
            const customConfigPath = GetResourcePath(GetCurrentResourceName()) + '/config.json';
            if (customConfigPath) {
                // In a real implementation, you'd load from file
                console.info('Using default configuration');
            }
        } catch (error) {
            console.warn('Custom config not found, using defaults');
        }

        return defaultConfig;
    }

    private setupSharedUtilities(): void {
        // Setup global utilities that both client and server can use

        // Validation utilities
        (global as any).isValidEmail = (email: string): boolean => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        };

        (global as any).isValidPhoneNumber = (phone: string): boolean => {
            const phoneRegex = /^\+?[\d\s\-()]{10,}$/;
            return phoneRegex.test(phone);
        };

        (global as any).formatMoney = (amount: number): string => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
            }).format(amount);
        };

        (global as any).formatDate = (date: Date): string => {
            return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
            }).format(date);
        };

        // Distance calculation
        (global as any).getDistance = (
            pos1: { x: number; y: number; z: number },
            pos2: { x: number; y: number; z: number }
        ): number => {
            const dx = pos1.x - pos2.x;
            const dy = pos1.y - pos2.y;
            const dz = pos1.z - pos2.z;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        };
    }

    private validateEnvironment(): void {
        const requiredResources = ['oxmysql'];

        for (const resource of requiredResources) {
            if (GetResourceState(resource) !== 'started') {
                console.error(`Required resource '${resource}' is not running!`);
                throw new Error(`Missing required resource: ${resource}`);
            }
        }

        // Validate convars
        const requiredConvars = ['mysql_connection_string'];
        for (const convar of requiredConvars) {
            if (!GetConvar(convar, '')) {
                console.warn(`Convar '${convar}' is not set`);
            }
        }
    }

    public getConfig(): FrameworkConfig {
        return this.config!;
    }

    public updateConfig(newConfig: Partial<FrameworkConfig>): void {
        this.config = {
            ...this.config!,
            ...newConfig,
            framework: { ...this.config!.framework, ...newConfig.framework },
            player: { ...this.config!.player, ...newConfig.player },
            database: { ...this.config!.database, ...newConfig.database },
            server: { ...this.config!.server, ...newConfig.server },
        };
        console.info('Configuration updated');
    }
}

// Configuration interface
interface FrameworkConfig {
    framework: {
        name: string;
        version: string;
        author: string;
        debug: boolean;
    };
    player: {
        maxCharacters: number;
        autoSave: number;
        spawnProtection: number;
        defaultMoney: {
            cash: number;
            bank: number;
        };
    };
    database: {
        connectionTimeout: number;
        maxRetries: number;
    };
    server: {
        maxPlayers: number;
        serverName: string;
        enablePvp: boolean;
    };
}

// Initialize shared core
const sharedCore = SharedCore.getInstance();

// Export for other resources
if (IsDuplicityVersion()) {
    // Server-side export
    global.exports('getSharedCore', () => sharedCore);
    global.exports('getConfig', () => sharedCore.getConfig());
} else {
    // Client-side export
    global.exports('getSharedCore', () => sharedCore);
    global.exports('getConfig', () => sharedCore.getConfig());
}

// Global access
(global as any).SharedCore = sharedCore;
(global as any).FrameworkConfig = sharedCore.getConfig();
