import { CharacterData, PlayerData, PlayerEvents } from '@shared';

export class LocalPlayer {
    private static instance: LocalPlayer;
    private playerData: PlayerData | null = null;
    private characters: CharacterData[] = [];
    private activeCharacter: CharacterData | null = null;
    private multicharacterUI = false;

    private constructor() {
        this.registerEvents();
    }

    public static getInstance(): LocalPlayer {
        if (!LocalPlayer.instance) {
            LocalPlayer.instance = new LocalPlayer();
        }
        return LocalPlayer.instance;
    }

    private registerEvents(): void {
        // Register event listeners here
        onNet(PlayerEvents.PLAYER_LOADED, (playerData: PlayerData) => {
            this.playerData = playerData;
            console.log(`Player loaded: ${playerData.name} (${playerData.license})`);
        });

        onNet(PlayerEvents.CHARACTER_LIST, (characters: CharacterData[]) => {
            this.characters = characters;
            console.log(`Character list received: ${characters.map(c => c.first_name).join(', ')}`);
            this.showMulticharacterUI();
        });

        onNet(PlayerEvents.CHARACTER_LOADED, (character: CharacterData) => {
            this.activeCharacter = character;
            this.hideMulticharacterUI();
            this.spawnCharacter(character);
            console.log(`Character loaded: ${character.first_name} ${character.last_name}`);
        });

        // Listen for character loaded from multicharacter system
        onNet('hm-core:playerLoaded', (data: { characterId: number; stateid: string; characterData: any }) => {
            // Convert the character data to match our CharacterData interface
            const characterData = {
                id: data.characterId,
                identifier: data.characterData.identifier || '',
                stateid: data.stateid,
                first_name: data.characterData.first_name,
                last_name: data.characterData.last_name,
                birthdate: new Date(data.characterData.birthdate),
                gender: data.characterData.gender,
                state: data.characterData.state || 'alive',
                money: data.characterData.money || { cash: 500, bank: 5000, crypto: 0 },
                position: data.characterData.position || { x: 0, y: 0, z: 100, heading: 0 },
                appearance: data.characterData.appearance || {},
                tattoos: data.characterData.tattoos || [],
                metadata: data.characterData.metadata || {},
                created_at: new Date(data.characterData.created_at || Date.now()),
                updated_at: new Date(data.characterData.updated_at || Date.now()),
            } as CharacterData;

            this.activeCharacter = characterData;
            console.log(`[hm-core] Character loaded from multicharacter: ${characterData.first_name} ${characterData.last_name} (stateid: ${characterData.stateid})`);
            console.log(`[hm-core] Character position: ${characterData.position.x}, ${characterData.position.y}, ${characterData.position.z}`);
        });

        setInterval(() => {
            if (this.activeCharacter) {
                this.savePosition();
                console.log(`[hm-core] Auto-saving position for ${this.activeCharacter.first_name}`);
            }
        }, 60000); // Save position every 60 seconds
    }

    private showMulticharacterUI(): void {
        this.multicharacterUI = true;
        SetNuiFocus(true, true);

        const ped = PlayerPedId();
        FreezeEntityPosition(ped, true);
        SetEntityVisible(ped, false, false);

        SendNuiMessage(
            JSON.stringify({
                action: 'showMulticharacterUI',
                characters: this.characters,
                maxCharacters: 5,
            })
        );
    }

    private hideMulticharacterUI(): void {
        this.multicharacterUI = false;
        SetNuiFocus(false, false);

        SendNuiMessage(
            JSON.stringify({
                action: 'hideMulticharacterUI',
            })
        );
    }

    private spawnCharacter(character: CharacterData): void {
        const ped = PlayerPedId();

        SetEntityCoords(
            ped,
            character.position.x,
            character.position.y,
            character.position.z,
            false,
            false,
            false,
            true
        );
        SetEntityHeading(ped, character.position.heading || 0);

        FreezeEntityPosition(ped, false);
        SetEntityVisible(ped, true, false);

        // NetworkSetEntityOnlyExistsForParticipants(ped, false);

        console.log(
            `Character spawned at position: ${character.position.x}, ${character.position.y}, ${character.position.z}`
        );
    }

    private savePosition(): void {
        if (!this.activeCharacter) return;

        const ped = PlayerPedId();
        const coords = GetEntityCoords(ped, true);
        const heading = GetEntityHeading(ped);

        this.activeCharacter.position = {
            x: coords[0],
            y: coords[1],
            z: coords[2],
            heading: heading,
        };

        // Emit event to server to save character position
        emitNet(PlayerEvents.CHARACTER_UPDATE, {
            id: this.activeCharacter.id,
            position: this.activeCharacter.position,
        });

        console.log(`[hm-core] Position saved for ${this.activeCharacter.first_name}: x=${coords[0].toFixed(2)}, y=${coords[1].toFixed(2)}, z=${coords[2].toFixed(2)}, heading=${heading.toFixed(2)}`);
    }

    public selectCharacter(characterid: number): void {
        emitNet(PlayerEvents.SELECT_CHARACTER, characterid);
        console.log(`Character selection requested: ${characterid}`);
    }

    public createCharacter(characterData: Partial<CharacterData>): void {
        emitNet(PlayerEvents.CREATE_CHARACTER, characterData);
        console.log(
            `Character creation requested: ${characterData.first_name} ${characterData.last_name}`
        );
    }

    public getActiveCharacter(): CharacterData | null {
        return this.activeCharacter;
    }

    public getPlayerData(): PlayerData | null {
        return this.playerData;
    }

    public getCharacters(): CharacterData[] {
        return this.characters;
    }
}

RegisterNuiCallbackType('selectCharacter');
on('__cfx_nui:selectCharacter', (data: { characterid: number }, cb: (response: string) => void) => {
    LocalPlayer.getInstance().selectCharacter(data.characterid);
    cb('ok');
});

RegisterNuiCallbackType('createCharacter');
on('__cfx_nui:createCharacter', (data: Partial<CharacterData>, cb: (response: string) => void) => {
    LocalPlayer.getInstance().createCharacter(data);
    cb('ok');
});

// Initialize the player client
LocalPlayer.getInstance();
// Export the LocalPlayer instance for use in other modules
export const localPlayer = LocalPlayer.getInstance();
