import { CraftingRecipe } from '../types/crafting.types';
import { getItemDefinition } from '../items/items';

/**
 * Gets the display icon for a recipe.
 * Uses the result item's icon (recipe.id is the item ID it produces).
 * 
 * @param recipe The recipe to get the icon for
 * @returns The icon string to use, or undefined if no icon is available
 */
export function getRecipeDisplayIcon(recipe: CraftingRecipe): string | undefined {
    // Get icon from the item definition using recipe.id
    const resultItem = getItemDefinition(recipe.id);
    return resultItem?.icon;
}
