import '@citizenfx/server';
import { Database } from './database';
import { CharacterData, PlayerData, PlayerEvents, Vector4 } from '@shared';

export class PlayerManager {
    private static instance: PlayerManager;
    private players: Map<string, PlayerData> = new Map(); // license -> PlayerData
    private playerCharacters: Map<string, CharacterData[]> = new Map(); // license -> CharacterData[]
    private activeCharacters: Map<number, CharacterData> = new Map(); // serverId -> CharacterData
    private database: Database;

    private constructor() {
        this.database = Database.getInstance();
        this.registerEvents();
    }

    public static getInstance(): PlayerManager {
        if (!PlayerManager.instance) {
            PlayerManager.instance = new PlayerManager();
        }
        return PlayerManager.instance;
    }

    private registerEvents(): void {
        // Player connecting
        on('playerConnecting', async (name: string, setKickReason: string, deferrals: any) => {
            deferrals.defer();
            deferrals.update('Checking your information...');
            const source = global.source;

            const license = this.getPlayerLicense(source);
            if (!license) {
                deferrals.done('Failed to get your license. Please restart FiveM.');
                return;
            }
            if (!name || name.trim() === '') {
                deferrals.done('Invalid player name. Please use a valid name.');
                return;
            }
            deferrals.update(`Welcome ${name.trim()}! Checking your profile...`);
            console.log(`Player connecting: ${name} (${license})`);

            try {
                let playerData = await this.database.getPlayerByLicense(license);

                if (!playerData) {
                    deferrals.update('Creating your profile...');
                    const identifiers = this.getPlayerIdentifiers(source);

                    playerData = await this.database.createPlayer({
                        license: license,
                        name: name.trim(),
                        discord: identifiers.discord ? identifiers.discord : '',
                        steam: identifiers.steam ? identifiers.steam : '',
                    });

                    if (!playerData) {
                        deferrals.done('Failed to create your profile. Please try again.');
                        return;
                    }

                    console.log(`New player connected: ${name} (${license})`);
                } else {
                    console.log(`Player reconnected: ${name} (${license})`);
                }

                this.players.set(license, playerData);
                deferrals.done();
            } catch (error) {
                console.error(`Error during player connection: ${error}`);
                deferrals.done('An error occurred. Please try again.');
            }
        });

        // Player joined
        on('playerJoined', async () => {
            const license = this.getPlayerLicense(source);
            if (!license) return;

            const playerData = this.players.get(license);
            if (!playerData) return;

            await this.database.updatePlayer(license, true);

            // Load characters
            const characters = await this.database.getCharactersByPlayerId(playerData.id);
            this.playerCharacters.set(license, characters);

            // Emit to client
            emitNet(PlayerEvents.PLAYER_LOADED, source, playerData);
            emitNet(PlayerEvents.CHARACTER_LIST, source, characters);
        });

        // Player dropped
        on('playerDropped', async () => {
            const license = this.getPlayerLicense(source);
            if (!license) return;

            // Save active character position if exists
            const activeCharacter = this.getActiveCharacter(source);
            if (activeCharacter) {
                const ped = GetPlayerPed(source);
                const coords = GetEntityCoords(ped);
                const heading = GetEntityHeading(ped);

                await this.database.updateCharacterPosition(license, activeCharacter.stateid, {
                    x: coords[0],
                    y: coords[1],
                    z: coords[2],
                    heading: heading || 0,
                });
            }

            await this.database.updatePlayer(license, false);

            this.players.delete(license);
            this.playerCharacters.delete(license);
            this.activeCharacters.delete(source);

            console.log(`Player disconnected: ${license}`);
        });

        // Character selection
        onNet(PlayerEvents.SELECT_CHARACTER, async (characterId: number) => {
            const license = this.getPlayerLicense(source);
            if (!license) return;

            const characters = this.playerCharacters.get(license);
            if (!characters) return;

            const character = characters.find(c => c.id === characterId);
            if (!character) return;

            this.activeCharacters.set(source, character);
            emitNet(PlayerEvents.CHARACTER_LOADED, source, character);

            console.log(
                `Player ${license} selected character: ${character.first_name} ${character.last_name}`
            );
        });

        // Character creation
        onNet(PlayerEvents.CREATE_CHARACTER, async (characterData: Partial<CharacterData>) => {
            const license = this.getPlayerLicense(source);
            if (!license) return;

            const playerData = this.players.get(license);
            if (!playerData) return;

            const characters = this.playerCharacters.get(license) || [];
            if (characters.length >= 5) {
                // Max 5 characters
                emitNet('core:notify', source, 'error', 'Maximum character limit reached');
                return;
            }

            characterData.identifier = playerData.license;
            const newCharacter = await this.database.createCharacter(characterData);

            if (newCharacter) {
                characters.push(newCharacter);
                this.playerCharacters.set(license, characters);
                emitNet(PlayerEvents.CHARACTER_LIST, source, characters);
                console.info(
                    `Character created: ${newCharacter.first_name} ${newCharacter.last_name} for ${license}`
                );
            }
        });

        // Character update (position saving)
        onNet(PlayerEvents.CHARACTER_UPDATE, async (updateData: { id: number; position: Vector4 }) => {
            const license = this.getPlayerLicense(source);
            if (!license) return;

            const activeCharacter = this.activeCharacters.get(source);
            if (!activeCharacter) return;

            // Verify the character ID matches the active character
            if (activeCharacter.id !== updateData.id) return;

            // Update the character position in the database
            await this.database.updateCharacterPosition(license, activeCharacter.stateid, updateData.position);

            // Update the local character data
            activeCharacter.position = updateData.position;
            
            console.log(`Position updated for character ${activeCharacter.first_name} ${activeCharacter.last_name}: ${updateData.position.x}, ${updateData.position.y}, ${updateData.position.z}`);
        });
    }

    private getPlayerLicense(source: number): string | null {
        const identifiers = getPlayerIdentifiers(source);
        const license = identifiers.find((id: string) => id.startsWith('license:'));
        return license ? license.replace('license:', '') : null;
    }

    private getPlayerIdentifiers(source: number): { discord?: string; steam?: string } {
        const identifiers = getPlayerIdentifiers(source);
        const discord = identifiers
            .find((id: string) => id.startsWith('discord:'))
            ?.replace('discord:', '');
        const steam = identifiers
            .find((id: string) => id.startsWith('steam:'))
            ?.replace('steam:', '');
        return { discord, steam };
    }

    public getPlayer(license: string): PlayerData | undefined {
        return this.players.get(license);
    }

    public getActiveCharacter(source: number): CharacterData | undefined {
        return this.activeCharacters.get(source);
    }

    public getPlayerCharacters(license: string): CharacterData[] {
        return this.playerCharacters.get(license) || [];
    }

    public setActiveCharacter(source: number, characterData: CharacterData): void {
        this.activeCharacters.set(source, characterData);
        console.log(
            `Active character set for player ${source}: ${characterData.first_name} ${characterData.last_name} (stateid: ${characterData.stateid})`
        );
    }
}

// Initialize the player manager
PlayerManager.getInstance();
