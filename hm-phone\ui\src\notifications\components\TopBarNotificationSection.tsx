import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NotificationData } from '../types/notificationTypes';
import { useNotificationStore } from '../stores/notificationStore';
import MessageListView from './MessageListView';
import appConfigurations from '../../common/stores/appConfigurations';
import { useNavigationStore } from '../../navigation/navigationStore';
import { usePhoneStore, PhoneStateEnum } from '../../common/stores/phoneStateStore';

interface TopBarNotificationSectionProps {
  onClose?: () => void;
  closeDrawer?: () => void;
}

// Helper function to format timestamp as relative time
const formatTimeAgo = (timestamp: number): string => {
  const diff = Date.now() - timestamp;
  const minutes = Math.floor(diff / 60000);

  if (minutes === 0) return 'Now';
  if (minutes < 60) return `${minutes}m`;
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h`;
  return `${Math.floor(hours / 24)}d`;
};

// Helper function to get app name from app ID using the app configurations
const getAppName = (appId: number): string => {
  const app = appConfigurations.find(app => app.id === appId);
  return app?.name || `App ${appId}`;
};

// Helper function to get app icon from app ID using the app configurations
const getAppIcon = (appId: number): string => {
  const app = appConfigurations.find(app => app.id === appId);
  return app?.icon || 'bell';
};

// Helper function to get app color from app ID using the app configurations
const getAppColor = (appId: number): string => {
  const app = appConfigurations.find(app => app.id === appId);
  if (app?.colors?.bg) {
    return app.colors.bg;
  }

  // Default colors based on app ID if not found in configurations
  const defaultColors: Record<number, string> = {
    5: 'bg-indigo-500', // Messages
    6: 'bg-green-500', // Dialer
    10: 'bg-yellow-500', // LifeSnap
    11: 'bg-green-500', // Banking
    12: 'bg-purple-500', // Music
    13: 'bg-pink-500', // LoveLink
    14: 'bg-orange-500' // Notes
  };

  return defaultColors[appId] || 'bg-gray-700';
};

// Define the conversation group type
interface ConversationGroup {
  notifications: NotificationData[];
  count: number;
  sender?: string;
  avatar?: string;
  isGroup?: boolean;
  latestMessage?: string;
  latestTimestamp: number;
}

// Helper function to generate a preview of conversations for Messages app
const generateConversationPreviews = (
  conversationGroups: Record<string, ConversationGroup> | undefined
): React.ReactNode => {
  if (!conversationGroups || Object.keys(conversationGroups).length === 0) {
    return <span>No new messages</span>;
  }

  // Get up to 3 conversations to preview
  const conversations = Object.values(conversationGroups).slice(0, 3);

  return (
    <div className="flex flex-col space-y-0.5">
      {conversations.map((convo: ConversationGroup, index: number) => (
        <div key={index} className="truncate text-xs">
          <span className="font-medium text-white">{convo.sender || 'Unknown'}: </span>
          <span className="text-white/80">{convo.latestMessage}</span>
        </div>
      ))}
      {Object.keys(conversationGroups).length > 3 && (
        <div className="text-[10px] text-white/60 truncate">
          +{Object.keys(conversationGroups).length - 3} more conversations
        </div>
      )}
    </div>
  );
};

export const TopBarNotificationSection: React.FC<TopBarNotificationSectionProps> = ({ closeDrawer }) => {
  const {
    notifications,
    removeNotification,
    isGroupExpanded,
    toggleGroupExpansion,
    clearNotifications
  } = useNotificationStore();

  // Add navigation hooks for deep linking
  const { openAppView } = useNavigationStore();
  const phoneStore = usePhoneStore();

  // Force component to re-render when notifications change
  const [, setForceUpdate] = useState(0);

  // We're using inline handlers for deep linking in the JSX

  // Watch for changes in notifications and force a re-render
  React.useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [notifications]);

  // Group notifications by app (Android 16 style)
  const groupedNotifications = React.useMemo(() => {
    // First, filter to only get notifications that should be in the topbar
    const topBarNotifs = Object.entries(notifications)
      .flatMap(([appId, appNotifications]) =>
        appNotifications
          .filter(n => n.movedToTopBar)
          .map(
            n =>
              ({
                ...n,
                appId: parseInt(appId),
                movedToTopBar: true, // Ensure movedToTopBar is always boolean
                type: n.type || 'default' // Ensure type is always defined
              } as NotificationData)
          )
      )
      .sort((a, b) => b.timestamp - a.timestamp); // Sort by timestamp (newest first)

    // Group by app first
    const appGroups: Record<number, NotificationData[]> = {};

    topBarNotifs.forEach(notification => {
      if (!appGroups[notification.appId]) {
        appGroups[notification.appId] = [];
      }
      appGroups[notification.appId].push(notification);
    });

    // Process each app's notifications
    const processedGroups: Record<
      string,
      {
        notifications: NotificationData[];
        count: number;
        appId: number;
        appName: string;
        appIcon: string;
        groupType: 'message' | 'app';
        sender?: string;
        avatar?: string;
        isGroup?: boolean;
        latestMessage?: string;
        latestTimestamp: number;
        // For Messages app - store conversation groups for expanded view
        conversationGroups?: Record<
          string,
          {
            notifications: NotificationData[];
            count: number;
            sender?: string;
            avatar?: string;
            isGroup?: boolean;
            latestMessage?: string;
            latestTimestamp: number;
          }
        >;
      }
    > = {};

    // Process each app's notifications
    Object.entries(appGroups).forEach(([appIdStr, appNotifications]) => {
      const appId = parseInt(appIdStr);
      const groupKey = `app-${appId}`;
      const latestNotification = appNotifications[0]; // Already sorted by timestamp

      // For Messages app, prepare conversation groups for expanded view
      if (appId === 5) {
        // Messages app
        // Group messages by conversation (for expanded view)
        const conversationGroups: Record<
          string,
          {
            notifications: NotificationData[];
            count: number;
            sender?: string;
            avatar?: string;
            isGroup?: boolean;
            latestMessage?: string;
            latestTimestamp: number;
          }
        > = {};

        appNotifications.forEach(notification => {
          // Skip summary notifications
          if (notification.isGroupSummary) return;

          // Determine conversation key
          let conversationKey = '';
          if (notification.metadata?.conversationId) {
            conversationKey = `conversation-${notification.metadata.conversationId}`;
          } else if (notification.metadata?.sender) {
            conversationKey = `sender-${notification.metadata.sender}`;
          } else {
            conversationKey = 'unknown-conversation';
          }

          // Create conversation group if it doesn't exist
          if (!conversationGroups[conversationKey]) {
            conversationGroups[conversationKey] = {
              notifications: [],
              count: 0,
              sender: notification.metadata?.sender,
              avatar: notification.metadata?.avatar,
              isGroup: notification.metadata?.isGroup,
              latestMessage: notification.message,
              latestTimestamp: notification.timestamp
            };
          }

          // Add notification to conversation group
          conversationGroups[conversationKey].notifications.push(notification);
          conversationGroups[conversationKey].count++;

          // Update latest message if this is newer
          if (notification.timestamp > conversationGroups[conversationKey].latestTimestamp) {
            conversationGroups[conversationKey].latestMessage = notification.message;
            conversationGroups[conversationKey].latestTimestamp = notification.timestamp;
          }
        });

        // Create the Messages app group with conversation groups
        processedGroups[groupKey] = {
          notifications: appNotifications,
          count: appNotifications.length,
          appId,
          appName: getAppName(appId),
          appIcon: getAppIcon(appId),
          groupType: 'message',
          latestMessage: latestNotification.message,
          latestTimestamp: latestNotification.timestamp,
          conversationGroups // Store conversation groups for expanded view
        };
      } else {
        // For other apps - group all notifications by app
        processedGroups[groupKey] = {
          notifications: appNotifications,
          count: appNotifications.length,
          appId,
          appName: getAppName(appId),
          appIcon: getAppIcon(appId),
          groupType: 'app',
          latestMessage: latestNotification.message,
          latestTimestamp: latestNotification.timestamp
        };
      }
    });

    return processedGroups;
  }, [notifications]);

  // Track expanded conversation groups for Messages app
  const [expandedConversations, setExpandedConversations] = useState<Set<string>>(new Set());

  // Toggle conversation expansion
  const toggleConversationExpansion = (conversationKey: string) => {
    setExpandedConversations((prev: Set<string>) => {
      const newSet = new Set(prev);
      if (newSet.has(conversationKey)) {
        newSet.delete(conversationKey);
      } else {
        newSet.add(conversationKey);
      }
      return newSet;
    });
  };

  // Check if a conversation is expanded
  const isConversationExpanded = (conversationKey: string) => {
    return expandedConversations.has(conversationKey);
  };

  return (
    <div className="px-4 pb-4 relative">
      {/* Notifications header with Clear All button */}
      {Object.keys(groupedNotifications).length > 0 && (
        <div className="flex justify-between items-center mb-3 pt-2">
          <h2 className="text-white text-lg font-medium">Notifications</h2>
          <button
            onClick={() => clearNotifications()}
            className="text-white/60 hover:text-white text-sm px-2 py-1 bg-white/10 hover:bg-white/15 rounded transition-colors"
          >
            Clear All
          </button>
        </div>
      )}

      {/* Notification list with app-specific grouping */}
      <div className="space-y-1">
        {Object.entries(groupedNotifications).map(([groupKey, group]) => {
          const {
            notifications,
            count,
            appId,
            appName,
            appIcon,
            latestMessage,
            latestTimestamp,
            conversationGroups
          } = group;
          const hasMultiple = count > 1;
          const isExpanded = isGroupExpanded(groupKey);
          const isMessagesApp = appId === 5; // Messages app

          return (
            <motion.div
              key={groupKey}
              className="bg-[#1E1E1E] rounded-lg overflow-hidden mb-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              {/* App header - always clickable */}
              <div
                className={`flex ${isExpanded ? 'items-center' : 'items-start'} justify-between ${
                  isExpanded ? 'py-2 px-3' : 'p-3'
                } cursor-pointer`}
                onClick={() => toggleGroupExpansion(groupKey)}
              >
                <div className="flex min-w-0 flex-1 pr-2 items-center">
                  {/* App icon with count badge */}
                  <div className="relative mr-3 flex-shrink-0">
                    <div
                      className={`${
                        isExpanded ? 'w-8 h-8' : 'w-10 h-10'
                      } rounded-full flex items-center justify-center ${getAppColor(appId)}`}
                    >
                      <i
                        className={`fas fa-${appIcon} text-white ${isExpanded ? 'text-sm' : ''}`}
                      ></i>
                    </div>
                    {hasMultiple && (
                      <div
                        className={`absolute -top-1 -right-1 bg-[#E2D0F9] text-[#1E1E1E] text-xs font-medium rounded-full ${
                          isExpanded ? 'h-4 w-4 text-[10px]' : 'h-5 w-5'
                        } flex items-center justify-center`}
                      >
                        {count}
                      </div>
                    )}
                  </div>

                  {/* App info and notification preview */}
                  <div className="flex flex-col min-w-0 flex-1 justify-center">
                    <div className="flex items-center">
                      <div
                        className={`text-white ${
                          isExpanded ? 'text-xs' : 'text-sm'
                        } font-medium whitespace-nowrap`}
                      >
                        {appName}
                      </div>
                      <div
                        className={`text-white/50 ${
                          isExpanded ? 'text-[10px]' : 'text-xs'
                        } ml-2 whitespace-nowrap`}
                      >
                        • {formatTimeAgo(latestTimestamp)}
                      </div>
                    </div>
                    {!isExpanded && (
                      <div className="text-white/80 min-h-[1.25rem]">
                        {isMessagesApp && hasMultiple ? (
                          generateConversationPreviews(conversationGroups)
                        ) : isMessagesApp && !hasMultiple && notifications[0]?.metadata?.sender ? (
                          <div className="truncate text-xs">
                            <span className="font-medium">
                              {notifications[0]?.metadata?.sender}:{' '}
                            </span>
                            {latestMessage}
                          </div>
                        ) : (
                          <div className="truncate text-xs">{latestMessage}</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Expand/collapse indicator and clear button */}
                <div
                  className={`flex ${isExpanded ? 'items-center' : 'items-start'} flex-shrink-0 ${
                    isExpanded ? '' : 'mt-1'
                  }`}
                >
                  <div className="text-white/60 mr-1">
                    <i
                      className={`fas fa-chevron-${isExpanded ? 'up' : 'down'} ${
                        isExpanded ? 'text-[10px]' : 'text-xs'
                      }`}
                    ></i>
                  </div>
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      // Remove each notification one by one
                      notifications.forEach(n => {
                        removeNotification(n.appId, n.id);
                      });
                      // Force re-render after all notifications are removed
                      setTimeout(() => setForceUpdate(prev => prev + 1), 0);
                    }}
                    className="text-white/40 hover:text-white/80 ml-2"
                  >
                    <i className={`fas fa-times ${isExpanded ? 'text-[10px]' : 'text-xs'}`}></i>
                  </button>
                </div>
              </div>

              {/* Expanded content - different for Messages app vs other apps */}
              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    className="border-t border-white/10"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {isMessagesApp && conversationGroups ? (
                      // Messages app - show conversations
                      <div className="overflow-hidden rounded-lg">
                        {Object.entries(conversationGroups).map(
                          ([conversationKey, conversation], index, array) => {
                            const {
                              notifications: conversationNotifs,
                              count: conversationCount,
                              sender,
                              avatar,
                              isGroup,
                              latestMessage: conversationLatestMessage,
                              latestTimestamp: conversationLatestTimestamp
                            } = conversation;
                            const isConversationOpen = isConversationExpanded(conversationKey);
                            const isFirst = index === 0;
                            const isLast = index === array.length - 1;

                            return (
                              <div
                                key={conversationKey}
                                className={`bg-[#2A2A2A] ${isFirst ? 'rounded-t-lg' : ''} ${
                                  isLast && Object.keys(conversationGroups).length > 1 ? '' : ''
                                } ${index > 0 ? 'border-t border-white/10' : ''}`}
                              >
                                {/* Conversation header */}
                                <div
                                  className={`flex items-center justify-between py-2 px-3 cursor-pointer`}
                                  onClick={(e) => {
                                    // If the expand button is clicked, toggle expansion
                                    if ((e.target as HTMLElement).closest('.expand-toggle') ||
                                        (e.target as HTMLElement).closest('.dismiss-button')) {
                                      return; // Let the button handlers handle it
                                    }

                                    // Otherwise, navigate to the conversation
                                    const conversationId = conversationNotifs[0]?.metadata?.conversationId;
                                    if (conversationId) {
                                      // Close the drawer if provided
                                      if (closeDrawer) closeDrawer();

                                      // Dismiss all notifications for this conversation
                                      conversationNotifs.forEach(n => {
                                        removeNotification(n.appId, n.id);
                                      });

                                      // Force re-render after all notifications are removed
                                      setTimeout(() => setForceUpdate(prev => prev + 1), 0);

                                      // Make sure the phone is visible
                                      phoneStore.actions.setPhoneState(PhoneStateEnum.OPEN);

                                      // Navigate to the conversation
                                      openAppView('messages', 'conversation', { id: conversationId });
                                    } else if (conversationCount > 1) {
                                      // If no conversation ID but multiple messages, toggle expansion
                                      toggleConversationExpansion(conversationKey);
                                    }
                                  }}
                                >
                                  <div className="flex items-center min-w-0 flex-1 pr-2 justify-center">
                                    {/* Avatar with count badge */}
                                    <div className="relative mr-3 flex-shrink-0">
                                      <div className="w-8 h-8 rounded-full overflow-hidden">
                                        {avatar ? (
                                          <img
                                            src={avatar}
                                            alt=""
                                            className="w-full h-full object-cover"
                                          />
                                        ) : (
                                          <div className="w-full h-full bg-indigo-500 flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {sender?.[0] || '?'}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                      {conversationCount > 1 && (
                                        <div className="absolute -top-1 -right-1 bg-[#E2D0F9] text-[#1E1E1E] text-xs font-medium rounded-full h-4 w-4 flex items-center justify-center text-[10px]">
                                          {conversationCount}
                                        </div>
                                      )}
                                    </div>

                                    {/* Sender info and message preview */}
                                    <div className="flex flex-col min-w-0 flex-1">
                                      <div className="flex items-center min-w-0">
                                        <div className="text-white text-sm font-medium truncate flex-shrink-1 min-w-0">
                                          {isGroup ? (
                                            <span className="flex items-center gap-1">
                                              <i className="fas fa-users text-xs mr-1 flex-shrink-0"></i>
                                              <span className="truncate">
                                                {sender || 'Group Chat'}
                                              </span>
                                            </span>
                                          ) : (
                                            <span className="truncate">
                                              {sender || 'Conversation'}
                                            </span>
                                          )}
                                        </div>
                                        <div className="text-white/50 text-xs ml-2 whitespace-nowrap flex-shrink-0">
                                          • {formatTimeAgo(conversationLatestTimestamp)}
                                        </div>
                                      </div>
                                      <div className="text-white/80 text-xs truncate">
                                        {conversationLatestMessage}
                                      </div>
                                    </div>
                                  </div>

                                  {/* Expand/collapse indicator and clear button */}
                                  <div className="flex items-center flex-shrink-0">
                                    {conversationCount > 1 && (
                                      <div
                                        className="text-white/60 mr-1 expand-toggle"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toggleConversationExpansion(conversationKey);
                                        }}
                                      >
                                        <i
                                          className={`fas fa-chevron-${
                                            isConversationOpen ? 'up' : 'down'
                                          } text-xs`}
                                        ></i>
                                      </div>
                                    )}
                                    <button
                                      onClick={e => {
                                        e.stopPropagation();
                                        // Remove each notification one by one
                                        conversationNotifs.forEach(n => {
                                          removeNotification(n.appId, n.id);
                                        });
                                        // Force re-render after all notifications are removed
                                        setTimeout(() => setForceUpdate(prev => prev + 1), 0);
                                      }}
                                      className="text-white/40 hover:text-white/80 ml-2 dismiss-button"
                                    >
                                      <i className="fas fa-times text-xs"></i>
                                    </button>
                                  </div>
                                </div>

                                {/* Expanded messages for this conversation - directly under the header */}
                                <AnimatePresence>
                                  {isConversationOpen && conversationCount > 1 && (
                                    <motion.div
                                      initial={{ opacity: 0, height: 0 }}
                                      animate={{ opacity: 1, height: 'auto' }}
                                      exit={{ opacity: 0, height: 0 }}
                                      transition={{ duration: 0.2 }}
                                    >
                                      <MessageListView
                                        notifications={conversationNotifs}
                                        formatTimeAgo={formatTimeAgo}
                                        closeDrawer={closeDrawer}
                                        removeNotification={removeNotification}
                                      />
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </div>
                            );
                          }
                        )}

                        {/* Bottom rounded corner div */}
                        <div className="bg-[#2A2A2A] h-1 rounded-b-lg"></div>
                      </div>
                    ) : (
                      // Other apps - show individual notifications
                      <div className="overflow-hidden rounded-lg">
                        {notifications.map((notification, index, array) => {
                          const isFirst = index === 0;
                          const isLast = index === array.length - 1;

                          return (
                            <div
                              key={notification.id}
                              className={`flex justify-between items-start py-3 px-3 bg-[#2A2A2A] ${
                                isFirst ? 'rounded-t-lg' : ''
                              } ${isLast && notifications.length > 1 ? '' : ''} ${
                                index > 0 ? 'border-t border-white/10' : ''
                              }`}
                            >
                              <div className="text-white/90 text-sm flex-1 min-w-0 pr-2 break-words">
                                {notification.message}
                              </div>
                              <div className="flex items-center flex-shrink-0">
                                <div className="text-white/40 text-xs mr-2 whitespace-nowrap">
                                  {formatTimeAgo(notification.timestamp)}
                                </div>
                                <button
                                  onClick={e => {
                                    e.stopPropagation();
                                    removeNotification(notification.appId, notification.id);
                                    // Force re-render after notification is removed
                                    setTimeout(() => setForceUpdate(prev => prev + 1), 0);
                                  }}
                                  className="text-white/40 hover:text-white/80"
                                >
                                  <i className="fas fa-times text-xs"></i>
                                </button>
                              </div>
                            </div>
                          );
                        })}

                        {/* Bottom rounded corner div */}
                        <div className="bg-[#2A2A2A] h-1 rounded-b-lg"></div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </div>

      {Object.keys(groupedNotifications).length === 0 && (
        <div className="text-center text-white/40 py-4">No notifications</div>
      )}
    </div>
  );
};

export default TopBarNotificationSection;
