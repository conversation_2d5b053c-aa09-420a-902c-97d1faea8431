/**
 * HM Target - Debug Utilities
 *
 * Debug visualization utilities for the target system
 * Provides zone visualization and debug rendering capabilities
 */

import { Vector3, TargetZone } from "../shared/types";
import { targetConfig } from "../shared/config";

// ===== DEBUG MODE =====

// Runtime debug state that can override config
let runtimeDebugOverride: boolean | null = null;

// Debug visualization storage
interface DebugVisualization {
  id: string;
  type: "line" | "sphere" | "box" | "circle" | "polygon" | "text";
  data: any;
  timestamp: number;
  zoneId?: string;
  persistent?: boolean; // If true, doesn't auto-expire
}

let debugVisualizations: DebugVisualization[] = [];
let debugVisualizationId = 0;

// Performance tracking
interface DebugStats {
  zonesRendered: number;
  entitiesDebugged: number;
  raycastsDrawn: number;
  frameTime: number;
  lastUpdate: number;
}

const debugStats: DebugStats = {
  zonesRendered: 0,
  entitiesDebugged: 0,
  raycastsDrawn: 0,
  frameTime: 0,
  lastUpdate: 0,
};

/**
 * Get current debug mode state - checks both config and runtime override
 */
export function isTargetDebugMode(): boolean {
  return runtimeDebugOverride !== null
    ? runtimeDebugOverride
    : targetConfig.debug.enabled;
}

/**
 * Set runtime debug mode override
 */
export function setTargetDebugMode(enabled: boolean): void {
  runtimeDebugOverride = enabled;
  console.log(`[hm-target] Debug mode ${enabled ? "enabled" : "disabled"}`);

  if (!enabled) {
    // Clear non-persistent debug visualizations when disabling debug mode
    debugVisualizations = debugVisualizations.filter((viz) => viz.persistent);
  } else {
    // Re-visualize all existing zones when enabling debug mode
    console.log(
      "[hm-target] Debug mode enabled, visualizations will appear when targeting is active"
    );
  }
}

/**
 * Toggle debug mode
 */
export function toggleTargetDebugMode(): boolean {
  const newState = !isTargetDebugMode();
  setTargetDebugMode(newState);
  return newState;
}

/**
 * Check if specific debug feature is enabled
 */
export function isDebugFeatureEnabled(
  feature: "showZones" | "showEntities" | "showRaycast" | "showStats"
): boolean {
  return isTargetDebugMode() && targetConfig.debug[feature];
}

/**
 * Reset runtime override to use config default
 */
export function resetDebugModeToConfig(): void {
  runtimeDebugOverride = null;
  console.log(
    `[hm-target] Debug mode reset to config default: ${targetConfig.debug.enabled}`
  );
}

/**
 * Update debug statistics
 */
export function updateDebugStats(stats: Partial<DebugStats>): void {
  Object.assign(debugStats, stats, { lastUpdate: GetGameTimer() });
}

/**
 * Get current debug statistics
 */
export function getDebugStats(): DebugStats & {
  debugMode: boolean;
  visualizationCount: number;
  zoneVisualizations: number;
} {
  const zoneCount = debugVisualizations.filter((viz) => viz.zoneId).length;

  return {
    ...debugStats,
    debugMode: isTargetDebugMode(),
    visualizationCount: debugVisualizations.length,
    zoneVisualizations: zoneCount,
  };
}

/**
 * Add debug line visualization
 */
function addDebugLine(
  start: Vector3,
  end: Vector3,
  color: { r: number; g: number; b: number; a: number } = {
    r: 255,
    g: 0,
    b: 0,
    a: 255,
  },
  zoneId?: string,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showRaycast")) return;

  const id = `target_line_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "line",
    data: { start, end, color },
    timestamp: GetGameTimer(),
    zoneId,
    persistent,
  });
}

/**
 * Add debug sphere visualization
 */
function addDebugSphere(
  position: Vector3,
  radius = 0.1,
  color: { r: number; g: number; b: number; a: number } = {
    r: 0,
    g: 255,
    b: 0,
    a: 255,
  },
  zoneId?: string,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showZones")) return;

  const id = `target_sphere_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "sphere",
    data: { position, radius, color },
    timestamp: GetGameTimer(),
    zoneId,
    persistent,
  });
}

/**
 * Add debug box visualization
 */
function addDebugBox(
  center: Vector3,
  size: Vector3,
  rotation: Vector3 = { x: 0, y: 0, z: 0 },
  color: { r: number; g: number; b: number; a: number } = {
    r: 0,
    g: 0,
    b: 255,
    a: 100,
  },
  zoneId?: string,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showZones")) return;

  const id = `target_box_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "box",
    data: { center, size, rotation, color },
    timestamp: GetGameTimer(),
    zoneId,
    persistent,
  });
}

/**
 * Add debug circle visualization
 */
function addDebugCircle(
  center: Vector3,
  radius: number,
  color: { r: number; g: number; b: number; a: number } = {
    r: 255,
    g: 255,
    b: 0,
    a: 150,
  },
  zoneId?: string,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showZones")) return;

  const id = `target_circle_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "circle",
    data: { center, radius, color },
    timestamp: GetGameTimer(),
    zoneId,
    persistent,
  });
}

/**
 * Add debug polygon visualization
 */
function addDebugPolygon(
  points: Vector3[],
  height = 2.0,
  color: { r: number; g: number; b: number; a: number } = {
    r: 255,
    g: 0,
    b: 255,
    a: 120,
  },
  zoneId?: string,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showZones")) return;

  const id = `target_polygon_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "polygon",
    data: { points, height, color },
    timestamp: GetGameTimer(),
    zoneId,
    persistent,
  });
}

/**
 * Visualize a target zone based on its type
 */
export function visualizeTargetZone(zone: TargetZone): void {
  if (!isDebugFeatureEnabled("showZones")) return;

  // Clear existing visualizations for this zone
  debugVisualizations = debugVisualizations.filter(
    (viz) => viz.zoneId !== zone.id
  );

  const baseColor = { r: 0, g: 200, b: 255, a: 150 }; // Cyan color for target zones

  switch (zone.type) {
    case "box":
      if (zone.size) {
        addDebugBox(
          zone.position,
          zone.size,
          zone.rotation || { x: 0, y: 0, z: 0 },
          baseColor,
          zone.id,
          true // persistent
        );
      }
      break;

    case "circle":
      if (zone.radius) {
        addDebugCircle(zone.position, zone.radius, baseColor, zone.id, true);
      }
      break;

    case "sphere":
      if (zone.radius) {
        addDebugSphere(zone.position, zone.radius, baseColor, zone.id, true);
      }
      break;

    case "polygon":
      if (zone.points && zone.points.length > 2) {
        addDebugPolygon(
          zone.points,
          zone.height || 2.0,
          baseColor,
          zone.id,
          true
        );
      }
      break;

    default:
      console.warn(`[hm-target] Unknown zone type: ${zone.type}`);
      break;
  }

  // Add a center marker for all zones
  addDebugSphere(
    zone.position,
    0.1,
    { r: 255, g: 255, b: 255, a: 255 }, // White center dot
    zone.id,
    true // persistent
  );
}

/**
 * Remove debug visualization for a specific zone
 */
export function removeZoneVisualization(zoneId: string): void {
  debugVisualizations = debugVisualizations.filter(
    (viz) => viz.zoneId !== zoneId
  );
}

/**
 * Clear all debug visualizations
 */
export function clearAllVisualizations(): void {
  debugVisualizations = [];
}

/**
 * Render all debug visualizations (call this in a render loop)
 */
export function renderTargetDebugVisualizations(): void {
  if (!isTargetDebugMode() || debugVisualizations.length === 0) return;

  const currentTime = GetGameTimer();
  const maxAge = 30000; // 30 seconds for zone visualizations (longer than admin)

  // Remove old visualizations (but keep persistent ones and zone visualizations longer)
  debugVisualizations = debugVisualizations.filter((viz) => {
    if (viz.persistent) return true; // Always keep persistent visualizations
    
    const age = currentTime - viz.timestamp;
    // Keep zone visualizations longer, other debug items shorter
    const maxLifetime = viz.zoneId ? maxAge : 5000;
    return age < maxLifetime;
  });

  // Track stats
  let zonesRendered = 0;
  let raycastsDrawn = 0;

  // Render active visualizations
  for (const viz of debugVisualizations) {
    try {
      if (viz.type === "line") {
        const { start, end, color } = viz.data;
        DrawLine(
          start.x,
          start.y,
          start.z,
          end.x,
          end.y,
          end.z,
          color.r,
          color.g,
          color.b,
          color.a
        );
        raycastsDrawn++;
      } else if (viz.type === "sphere") {
        const { position, radius, color } = viz.data;
        DrawSphere(
          position.x,
          position.y,
          position.z,
          radius,
          color.r,
          color.g,
          color.b,
          color.a
        );
        if (viz.zoneId) zonesRendered++;
      } else if (viz.type === "box") {
        const { center, size, rotation, color } = viz.data;
        renderBoxZone(center, size, rotation, color);
        zonesRendered++;
      } else if (viz.type === "circle") {
        const { center, radius, color } = viz.data;
        renderCircleZone(center, radius, color);
        zonesRendered++;
      } else if (viz.type === "polygon") {
        const { points, height, color } = viz.data;
        renderPolygonZone(points, height, color);
        zonesRendered++;
      } else if (viz.type === "text") {
        const { position, text, color, scale } = viz.data;
        const onScreen = GetScreenCoordFromWorldCoord(
          position.x,
          position.y,
          position.z
        );

        if (onScreen[0]) {
          SetTextScale(scale || 0.35, scale || 0.35);
          SetTextFont(4);
          SetTextProportional(true);
          SetTextColour(color.r, color.g, color.b, color.a);
          SetTextEntry("STRING");
          SetTextCentre(true);
          AddTextComponentString(text);
          DrawText(onScreen[1], onScreen[2]);
        }
      }
    } catch (error) {
      console.error(`[hm-target] Error rendering debug visualization:`, error);
    }
  }

  // Update stats
  updateDebugStats({
    zonesRendered,
    raycastsDrawn,
    frameTime: GetGameTimer() - currentTime,
  });
}

/**
 * Render a box zone using lines
 */
function renderBoxZone(
  center: Vector3,
  size: Vector3,
  rotation: Vector3,
  color: { r: number; g: number; b: number; a: number }
): void {
  const halfSize = {
    x: size.x / 2,
    y: size.y / 2,
    z: size.z / 2,
  };

  // Calculate the 8 corners of the box
  const corners = [
    {
      x: center.x - halfSize.x,
      y: center.y - halfSize.y,
      z: center.z - halfSize.z,
    },
    {
      x: center.x + halfSize.x,
      y: center.y - halfSize.y,
      z: center.z - halfSize.z,
    },
    {
      x: center.x + halfSize.x,
      y: center.y + halfSize.y,
      z: center.z - halfSize.z,
    },
    {
      x: center.x - halfSize.x,
      y: center.y + halfSize.y,
      z: center.z - halfSize.z,
    },
    {
      x: center.x - halfSize.x,
      y: center.y - halfSize.y,
      z: center.z + halfSize.z,
    },
    {
      x: center.x + halfSize.x,
      y: center.y - halfSize.y,
      z: center.z + halfSize.z,
    },
    {
      x: center.x + halfSize.x,
      y: center.y + halfSize.y,
      z: center.z + halfSize.z,
    },
    {
      x: center.x - halfSize.x,
      y: center.y + halfSize.y,
      z: center.z + halfSize.z,
    },
  ];

  // Draw the bottom face
  DrawLine(
    corners[0].x,
    corners[0].y,
    corners[0].z,
    corners[1].x,
    corners[1].y,
    corners[1].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[1].x,
    corners[1].y,
    corners[1].z,
    corners[2].x,
    corners[2].y,
    corners[2].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[2].x,
    corners[2].y,
    corners[2].z,
    corners[3].x,
    corners[3].y,
    corners[3].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[3].x,
    corners[3].y,
    corners[3].z,
    corners[0].x,
    corners[0].y,
    corners[0].z,
    color.r,
    color.g,
    color.b,
    color.a
  );

  // Draw the top face
  DrawLine(
    corners[4].x,
    corners[4].y,
    corners[4].z,
    corners[5].x,
    corners[5].y,
    corners[5].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[5].x,
    corners[5].y,
    corners[5].z,
    corners[6].x,
    corners[6].y,
    corners[6].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[6].x,
    corners[6].y,
    corners[6].z,
    corners[7].x,
    corners[7].y,
    corners[7].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[7].x,
    corners[7].y,
    corners[7].z,
    corners[4].x,
    corners[4].y,
    corners[4].z,
    color.r,
    color.g,
    color.b,
    color.a
  );

  // Draw the vertical edges
  DrawLine(
    corners[0].x,
    corners[0].y,
    corners[0].z,
    corners[4].x,
    corners[4].y,
    corners[4].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[1].x,
    corners[1].y,
    corners[1].z,
    corners[5].x,
    corners[5].y,
    corners[5].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[2].x,
    corners[2].y,
    corners[2].z,
    corners[6].x,
    corners[6].y,
    corners[6].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
  DrawLine(
    corners[3].x,
    corners[3].y,
    corners[3].z,
    corners[7].x,
    corners[7].y,
    corners[7].z,
    color.r,
    color.g,
    color.b,
    color.a
  );
}

/**
 * Render a circle zone using multiple lines
 */
function renderCircleZone(
  center: Vector3,
  radius: number,
  color: { r: number; g: number; b: number; a: number }
): void {
  const segments = 32;
  const angleStep = (Math.PI * 2) / segments;

  for (let i = 0; i < segments; i++) {
    const angle1 = i * angleStep;
    const angle2 = ((i + 1) % segments) * angleStep;

    const x1 = center.x + Math.cos(angle1) * radius;
    const y1 = center.y + Math.sin(angle1) * radius;
    const x2 = center.x + Math.cos(angle2) * radius;
    const y2 = center.y + Math.sin(angle2) * radius;

    // Draw circle at ground level
    DrawLine(
      x1,
      y1,
      center.z,
      x2,
      y2,
      center.z,
      color.r,
      color.g,
      color.b,
      color.a
    );

    // Draw circle at top level (2 units higher)
    DrawLine(
      x1,
      y1,
      center.z + 2.0,
      x2,
      y2,
      center.z + 2.0,
      color.r,
      color.g,
      color.b,
      color.a
    );

    // Draw vertical lines every 8th segment
    if (i % 8 === 0) {
      DrawLine(
        x1,
        y1,
        center.z,
        x1,
        y1,
        center.z + 2.0,
        color.r,
        color.g,
        color.b,
        color.a
      );
    }
  }
}

/**
 * Render a polygon zone using lines
 */
function renderPolygonZone(
  points: Vector3[],
  height: number,
  color: { r: number; g: number; b: number; a: number }
): void {
  if (points.length < 3) return;

  // Draw the bottom edges
  for (let i = 0; i < points.length; i++) {
    const current = points[i];
    const next = points[(i + 1) % points.length];

    DrawLine(
      current.x,
      current.y,
      current.z,
      next.x,
      next.y,
      next.z,
      color.r,
      color.g,
      color.b,
      color.a
    );
  }

  // Draw the top edges
  for (let i = 0; i < points.length; i++) {
    const current = points[i];
    const next = points[(i + 1) % points.length];

    DrawLine(
      current.x,
      current.y,
      current.z + height,
      next.x,
      next.y,
      next.z + height,
      color.r,
      color.g,
      color.b,
      color.a
    );
  }

  // Draw vertical edges
  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    DrawLine(
      point.x,
      point.y,
      point.z,
      point.x,
      point.y,
      point.z + height,
      color.r,
      color.g,
      color.b,
      color.a
    );
  }
}

/**
 * Add raycast debug visualization
 */
export function addRaycastDebug(
  start: Vector3,
  end: Vector3,
  hit: boolean,
  hitPoint?: Vector3
): void {
  if (!isDebugFeatureEnabled("showRaycast")) return;

  // Draw the ray line
  const rayColor = hit
    ? { r: 0, g: 255, b: 0, a: 255 }
    : { r: 255, g: 0, b: 0, a: 255 };
  addDebugLine(start, hitPoint || end, rayColor);

  // Draw hit point if we hit something
  if (hit && hitPoint) {
    addDebugSphere(hitPoint, 0.05, { r: 255, g: 255, b: 0, a: 255 });
  }
}

/**
 * Add debug text visualization
 */
export function addDebugText(
  position: Vector3,
  text: string,
  color: { r: number; g: number; b: number; a: number } = {
    r: 255,
    g: 255,
    b: 255,
    a: 255,
  },
  scale = 0.35,
  persistent = false
): void {
  if (!isDebugFeatureEnabled("showEntities")) return;

  const id = `target_text_${debugVisualizationId++}`;
  debugVisualizations.push({
    id,
    type: "text",
    data: { position, text, color, scale },
    timestamp: GetGameTimer(),
    persistent,
  });
}

/**
 * Add entity debug visualization (outline + text)
 */
export function addEntityDebug(
  entity: number,
  entityType: string,
  color: number[] = [255, 255, 255]
): void {
  if (!isDebugFeatureEnabled("showEntities")) return;
  
  // Set entity outline
  SetEntityDrawOutline(entity, true);
  SetEntityDrawOutlineColor(color[0], color[1], color[2], 255);
  SetEntityDrawOutlineShader(1);

  // Add text label
  const coords = GetEntityCoords(entity, true);
  addDebugText(
    { x: coords[0], y: coords[1], z: coords[2] + 1.0 },
    `${entityType} (${entity})`,
    { r: color[0], g: color[1], b: color[2], a: 255 }
  );
}

/**
 * Clear entity debug outline
 */
export function clearEntityDebug(entity: number): void {
  if (DoesEntityExist(entity)) {
    SetEntityDrawOutline(entity, false);
  }
}
