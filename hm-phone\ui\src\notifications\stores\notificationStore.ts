import { create } from 'zustand';
import { Notification } from '../types/notificationTypes';
import { processNotifications } from '../utils/notificationGroupingUtils';
import {
  addNotification,
  removeNotification,
  clearNotifications,
  markAsRead,
  moveToTopBar,
  toggleExpansion,
  getUnreadCountByApp
} from '../utils/notificationActionUtils';
import { getMessageNotifications, getOtherNotifications } from '../utils/notificationFilterUtils';
import { playNotificationSound, preloadNotificationSounds } from '../sounds/notificationSoundSystem';
import { createMockNotifications } from '../../common/components/topbar/mockData';

interface NotificationState {
  // State
  notifications: Record<number, Notification[]>;
  expandedGroups: Set<string>;
  notificationSettings: {
    groupSimilarNotifications: boolean;
    collapseThreshold: number;
    doNotDisturb: boolean;
    defaultSound: string;
    notificationSoundVolume: number;
    ringtone: string;
    appSettings: Record<number, {
      enabled: boolean;
      sound: boolean;
      customSound?: string;
      priority: 'urgent' | 'high' | 'normal' | 'low';
      grouping: {
        enabled: boolean;
        collapseThreshold: number;
      };
    }>;
  };

  // Actions
  addNotification: (notification: Notification) => void;
  removeNotification: (appId: number, notificationId: number) => void;
  clearNotifications: (appId?: number) => void;
  markAsRead: (appId: number, notificationId: number) => void;
  moveToTopBar: (appId: number, notificationId: number) => void;
  onSetNotifications: (notifications: Notification[]) => void;

  // Getters
  getMessageNotifications: () => Notification[];
  getOtherNotifications: () => Notification[];
  getUnreadCountByApp: () => Record<number, number>;

  // Group expansion
  toggleExpansion: (appId: number, notificationId: number) => void;
  toggleGroupExpansion: (groupId: string) => void;
  isGroupExpanded: (groupId: string) => boolean;

  // Settings
  updateNotificationSettings: (settings: Partial<NotificationState['notificationSettings']>) => void;
  updateAppNotificationSettings: (appId: number, settings: Partial<NotificationState['notificationSettings']['appSettings'][number]>) => void;
  toggleDoNotDisturb: () => void;
}

// Default notification settings
const DEFAULT_NOTIFICATION_SETTINGS = {
  groupSimilarNotifications: true,
  collapseThreshold: 3,
  doNotDisturb: false,
  defaultSound: '/sounds/notification-default.mp3',
  notificationSoundVolume: 80,
  ringtone: '/sounds/notification-call.mp3',
  appSettings: {}
};

// Initialize notification sound system
preloadNotificationSounds();

// Check if we're in browser mode
const isBrowser = typeof window !== 'undefined' && !(window as any).invokeNative;

// Create store with initial state
export const useNotificationStore = create<NotificationState>((set, get) => {
  // Initialize with mock notifications in browser mode
  if (isBrowser) {
    setTimeout(() => {
      const mockNotifications = createMockNotifications();
      mockNotifications.forEach(notification => {
        // Add each notification with a slight delay to simulate real-time notifications
        setTimeout(() => {
          get().addNotification(notification);
        }, Math.random() * 2000); // Random delay up to 2 seconds
      });
    }, 1000); // Wait 1 second after initialization
  }

  return {
    // State
    notifications: {},
    expandedGroups: new Set<string>(),
    notificationSettings: DEFAULT_NOTIFICATION_SETTINGS,

  // Filter functions
  getMessageNotifications: () => {
    return getMessageNotifications(get().notifications);
  },

  getOtherNotifications: () => {
    return getOtherNotifications(get().notifications);
  },

  // Action functions
  addNotification: (notification: Notification) => {
    // Play notification sound if appropriate
    playNotificationSound(notification, {
      defaultSound: get().notificationSettings.defaultSound,
      notificationSoundVolume: get().notificationSettings.notificationSoundVolume,
      doNotDisturb: get().notificationSettings.doNotDisturb,
      // Convert app settings to the format expected by playNotificationSound
      appSettings: Object.entries(get().notificationSettings.appSettings).reduce((acc, [appId, settings]) => {
        acc[Number(appId)] = {
          enabled: settings.enabled,
          sound: settings.sound ? get().notificationSettings.defaultSound : undefined
        };
        return acc;
      }, {} as Record<number, { sound?: string; enabled?: boolean }>)
    });

    set(state => {
      // Add notification to state
      const updatedNotifications = addNotification(state.notifications, notification);

      // Process notifications with current settings to handle grouping
      return {
        notifications: processNotifications(
          Object.values(updatedNotifications).flat(),
          {
            groupSimilarNotifications: state.notificationSettings.groupSimilarNotifications,
            collapseThreshold: state.notificationSettings.collapseThreshold,
            appSettings: state.notificationSettings.appSettings
          }
        )
      };
    });
  },

  removeNotification: (appId: number, notificationId: number) => {
    set(state => ({
      notifications: removeNotification(state.notifications, appId, notificationId)
    }));
  },

  clearNotifications: (appId?: number) => {
    set(state => ({
      notifications: clearNotifications(state.notifications, appId)
    }));
  },

  markAsRead: (appId: number, notificationId: number) => {
    set(state => ({
      notifications: markAsRead(state.notifications, appId, notificationId)
    }));
  },

  moveToTopBar: (appId: number, notificationId: number) => {
    set(state => ({
      notifications: moveToTopBar(state.notifications, appId, notificationId)
    }));
  },

  // Process and set notifications
  onSetNotifications: (notifications: Notification[]) => {
    set(state => ({
      notifications: processNotifications(
        notifications,
        {
          groupSimilarNotifications: state.notificationSettings.groupSimilarNotifications,
          collapseThreshold: state.notificationSettings.collapseThreshold,
          appSettings: state.notificationSettings.appSettings
        }
      )
    }));
  },

  // Toggle notification expansion
  toggleExpansion: (appId: number, notificationId: number) => {
    set(state => ({
      notifications: toggleExpansion(state.notifications, appId, notificationId)
    }));
  },

  // Toggle group expansion
  toggleGroupExpansion: (groupId: string) => {
    set(state => {
      const expandedGroups = new Set(state.expandedGroups);
      if (expandedGroups.has(groupId)) {
        expandedGroups.delete(groupId);
      } else {
        expandedGroups.add(groupId);
      }
      return { expandedGroups };
    });
  },

  // Check if a group is expanded
  isGroupExpanded: (groupId: string) => {
    return get().expandedGroups.has(groupId);
  },

  // Get unread notification count by app
  getUnreadCountByApp: () => {
    return getUnreadCountByApp(get().notifications);
  },

  // Update notification settings
  updateNotificationSettings: (settings) => {
    set(state => ({
      notificationSettings: {
        ...state.notificationSettings,
        ...settings
      }
    }));

    // Reprocess notifications with new settings
    const allNotifications = Object.values(get().notifications).flat();
    set(state => ({
      notifications: processNotifications(
        allNotifications,
        {
          groupSimilarNotifications: state.notificationSettings.groupSimilarNotifications,
          collapseThreshold: state.notificationSettings.collapseThreshold,
          appSettings: state.notificationSettings.appSettings
        }
      )
    }));
  },

  // Update app-specific notification settings
  updateAppNotificationSettings: (appId, settings) => {
    set(state => {
      const currentAppSettings = state.notificationSettings.appSettings[appId] || {
        enabled: true,
        sound: true,
        priority: 'normal' as const,
        grouping: {
          enabled: true,
          collapseThreshold: state.notificationSettings.collapseThreshold
        }
      };

      return {
        notificationSettings: {
          ...state.notificationSettings,
          appSettings: {
            ...state.notificationSettings.appSettings,
            [appId]: {
              ...currentAppSettings,
              ...settings
            }
          }
        }
      };
    });

    // Reprocess notifications with new settings
    const allNotifications = Object.values(get().notifications).flat();
    set(state => ({
      notifications: processNotifications(
        allNotifications,
        {
          groupSimilarNotifications: state.notificationSettings.groupSimilarNotifications,
          collapseThreshold: state.notificationSettings.collapseThreshold,
          appSettings: state.notificationSettings.appSettings
        }
      )
    }));
  },

  // Toggle Do Not Disturb mode
  toggleDoNotDisturb: () => {
    set(state => ({
      notificationSettings: {
        ...state.notificationSettings,
        doNotDisturb: !state.notificationSettings.doNotDisturb
      }
    }));
  }
  };
});
