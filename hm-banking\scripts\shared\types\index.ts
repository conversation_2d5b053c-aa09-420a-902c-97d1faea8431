export interface BankAccount {
  accountId: string; // Unique identifier for the bank account
  accountNumber: string; // Player-facing account number, can be same as accountId or formatted
  balance: number;
  characterId: string; // Unique identifier for the character (e.g., stateId from your framework)
  playerId?: string; // Optional: Unique identifier for the player (if you link characters to a master player account)
  accountType: 'personal' | 'savings' | 'business'; // Initial types, can be expanded
  isDefault?: boolean; // If a character has multiple accounts, which one is primary
  // Future considerations for business accounts:
  // jointAccountHolders?: { characterId: string, role: string }[]; // e.g., owner, manager, employee
  // permissions?: AccountPermissionEntry[]; // Or a more detailed access control list
}

export type TransactionType =
  | 'deposit'
  | 'withdrawal'
  | 'transfer_in' // Funds received
  | 'transfer_out' // Funds sent
  | 'fee' // Bank fees
  | 'interest_payment' // For savings accounts, etc.
  | 'salary' // Direct deposit from employer
  | 'business_payout' // Payouts from business activities
  | 'invoice_payment' // Paying an invoice
  | 'account_creation' // Initial transaction if any
  | 'system_adjustment'; // For admin corrections

export interface Transaction {
  transactionId: string; // Unique ID for the transaction
  accountId: string; // The account this transaction belongs to
  type: TransactionType;
  amount: number; // Positive for credits (deposit, transfer_in), negative for debits (withdrawal, transfer_out, fee)
  timestamp: number; // Unix timestamp (e.g., Date.now())
  description?: string; // e.g., "Transfer to Jane Doe (CID: 123)", "ATM Withdrawal at Pillbox", "Monthly Salary"
  counterpartyCharacterId?: string; // Character ID of the other party in a transfer
  counterpartyAccountId?: string; // Account ID of the other party in a transfer
  counterpartyName?: string; // Name of the other party (for display purposes in transaction history)
  newBalance: number; // The balance of `accountId` after this transaction occurred
}

// For the Zustand store state (UI focused)
export interface BankingUIState {
  isOpen: boolean; // Is the banking UI currently visible?
  isLoading: boolean; // For indicating global loading (initial account fetch)
  isLoadingTransactions: boolean; // For indicating transaction loading
  accounts: BankAccount[]; // All bank accounts owned by the current character
  selectedAccountId: string | null; // The ID of the currently viewed/active account
  transactionsByAccount: Record<string, Transaction[]>; // Cached transactions by account ID
  transactions: Transaction[]; // Transactions for the selectedAccountId (computed)
  error: string | null; // For displaying any errors to the user in the UI
  currentView: 'dashboard' | 'transactions'; // Current view state

  // Form states for UI interactions - typically strings for input fields
  depositAmount: string;
  withdrawAmount: string;
  transferTargetAccountNumber: string; // For initiating a transfer
  transferAmount: string;
  transferDescription: string;
}

// Actions for the Zustand store (these are conceptual and will be implemented within the store)
export interface BankingUIActions {
  // UI visibility and basic data loading
  setOpen: (isOpen: boolean) => void;
  fetchAccounts: () => Promise<void>; // Fetches accounts for the current character
  selectAccount: (accountId: string) => void; // Sets selectedAccountId and fetches its transactions
  fetchTransactions: (accountId: string) => Promise<void>; // Fetches transactions for a given account

  // View management
  setCurrentView: (view: 'dashboard' | 'transactions') => void;

  // Form input handlers
  setDepositAmount: (amount: string) => void;
  setWithdrawAmount: (amount: string) => void;
  setTransferTargetAccountNumber: (accountNumber: string) => void;
  setTransferAmount: (amount: string) => void;
  setTransferDescription: (description: string) => void;

  // Core banking operations that will interact with the backend (NUI messages)
  submitDeposit: () => Promise<void>;
  submitWithdrawal: () => Promise<void>;
  submitTransfer: () => Promise<void>;

  // Utility
  resetForms: () => void; // Clears form input fields
  clearError: () => void;

  // Future actions for business account management:
  // createBusinessAccount: (accountName: string, initialDeposit: number) => Promise<void>;
  // manageAccountAccess: (accountId: string, targetCharacterId: string, permissions: string[]) => Promise<void>;
  // viewAccountAccessList: (accountId: string) => Promise<void>;
}

// Future: Define AccountPermissionEntry and specific Permission types when implementing business features
// export interface AccountPermissionEntry {
//   characterId: string;
//   role?: string; // e.g., 'owner', 'manager', 'accountant'
//   permissions: PermissionType[];
// }
// export type PermissionType =
//   | 'view_balance'
//   | 'view_transactions'
//   | 'deposit'
//   | 'withdraw_limited' // e.g., with daily/transaction limits
//   | 'withdraw_full'
//   | 'initiate_transfer'
//   | 'manage_users' // Add/remove users, set permissions
//   | 'close_account';
