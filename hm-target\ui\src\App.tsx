import React, { useState, useEffect, useCallback, useMemo } from "react";
import TargetOverlay from "./components/TargetOverlay";
import TargetOptions from "./components/TargetOptions";
import ProgressBar from "./components/ProgressBar";
import { TargetState, NUIMessage } from "@shared/types";

const App: React.FC = () => {
  // Development mode for browser testing
  const isDevelopment = !window.parent || window.parent === window;

  const [targetState, setTargetState] = useState<TargetState>({
    active: isDevelopment,
    showingOptions: true, // Set to true to show options from center
    currentTarget: isDevelopment
      ? {
          position: { x: 960, y: 540, z: 0 },
          options: [
            {
              id: "test-interact",
              label: "Interact",
              icon: "fas fa-hand",
              action: "test_interact",
              canInteract: () => true,
            },
            {
              id: "test-examine",
              label: "Examine",
              icon: "fas fa-eye",
              action: "test_examine",
              canInteract: () => true,
            },
            {
              id: "test-use",
              label: "Use Item",
              icon: "fas fa-tools",
              action: "test_use",
              canInteract: () => true,
            },
          ],
          distance: 2.5,
          entity: 1234, // Mock entity ID for development
          modelHash: 5678, // Mock model hash for development
          type: "entity" as const, // Mock type for development
        }
      : undefined,
  });

  useEffect(() => {
    // Listen for NUI messages from the client
    const handleNuiMessage = (event: MessageEvent<NUIMessage>) => {
      const { action, data } = event.data;

      switch (action) {
        case "showTarget":
          setTargetState((prev: TargetState) => ({
            ...prev,
            active: true,
            currentTarget: data.target,
            showingOptions: false,
          }));
          break;

        case "hideTarget":
          setTargetState((prev: TargetState) => {
            const newState = {
              ...prev,
              active: false,
              currentTarget: undefined,
              showingOptions: false,
            };
            return newState;
          });
          break;

        case "showOptions":
          setTargetState((prev: TargetState) => ({
            ...prev,
            showingOptions: true,
          }));
          break;

        case "hideOptions":
          setTargetState((prev: TargetState) => ({
            ...prev,
            showingOptions: false,
            selectedOption: undefined,
          }));
          break;

        case "showProgress":
          setTargetState((prev: TargetState) => ({
            ...prev,
            progress: {
              active: true,
              label: data.label,
              duration: data.duration,
              startTime: Date.now(),
            },
          }));
          break;

        case "hideProgress":
          setTargetState((prev: TargetState) => ({
            ...prev,
            progress: undefined,
          }));
          break;

        case "updateTarget":
          setTargetState((prev: TargetState) => ({
            ...prev,
            currentTarget: data.target,
          }));
          break;

        default:
          break;
      }
    };

    window.addEventListener("message", handleNuiMessage);

    return () => {
      window.removeEventListener("message", handleNuiMessage);
    };
  }, []);

  const handleOptionSelect = useCallback((option: any, index: number) => {
    console.log(`[UI] Option clicked: ${option.label} (index: ${index})`, option);
    
    // Send option selection back to client with index
    console.log(`[UI] Sending NUI callback to: hm-target/selectOption with index: ${index}`);
    fetch(`https://hm-target/selectOption`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ index }),
    }).then(() => {
      console.log(`[UI] ✅ NUI callback sent successfully for index: ${index}`);
    }).catch((error) => {
      console.error(`[UI] ❌ NUI callback failed:`, error);
    });
  }, []);

  const handleProgressCancel = useCallback(() => {
    // Send progress cancellation back to client
    fetch(`https://hm-target/cancelProgress`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    }).catch(() => {
      // Ignore fetch errors in NUI context
    });
  }, []);

  // Memoize the components to prevent unnecessary re-renders
  const overlayComponent = useMemo(() => {
    if (!targetState.active || !targetState.currentTarget) return null;
    return <TargetOverlay target={targetState.currentTarget} />;
  }, [targetState.active, targetState.currentTarget]);

  const optionsComponent = useMemo(() => {
    if (!targetState.showingOptions || !targetState.currentTarget) return null;
    return (
      <TargetOptions
        target={targetState.currentTarget}
        onOptionSelect={handleOptionSelect}
        onClose={() => {
          console.log("[UI] Target options closed");
          // For close, we can send a special index like -1 or just hide the options
          setTargetState(prev => ({ ...prev, showingOptions: false }));
        }}
      />
    );
  }, [
    targetState.showingOptions,
    targetState.currentTarget,
    handleOptionSelect,
  ]);

  const progressComponent = useMemo(() => {
    if (!targetState.progress?.active) return null;
    return (
      <ProgressBar
        label={targetState.progress.label}
        duration={targetState.progress.duration}
        startTime={targetState.progress.startTime}
        onCancel={handleProgressCancel}
      />
    );
  }, [targetState.progress, handleProgressCancel]);

  return (
    <div className="w-full h-full">
      {/* Target Indicator Overlay */}
      {overlayComponent}

      {/* Target Options Menu */}
      {optionsComponent}

      {/* Progress Bar */}
      {progressComponent}
    </div>
  );
};

export default App;
