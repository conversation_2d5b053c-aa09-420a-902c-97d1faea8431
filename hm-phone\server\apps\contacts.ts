/**
 * Contacts App - Server Side
 *
 * This file handles server-side functionality for the Contacts app.
 */

import { Contact, ContactShareRequest, Call } from '@shared/types';

/**
 * Initialize the contacts app
 */
export function initializeContactsApp(): void {
    registerServerEvents();
    
    // Listen for player loaded event to ensure contacts are available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Contacts] Player ${source} loaded, ensuring contacts data is available`);
        
        // Initialize player data if needed
        await ensurePlayerContactsData(source);
    });
}

/**
 * Ensure player has contacts data initialized
 */
async function ensurePlayerContactsData(source: number): Promise<void> {
    try {
        const player = global.getServerPlayerData(source);
        if (!player) return;

        const identifier = player.stateid || player.identifier;
        if (!identifier) return;

        // Check if player has any contacts, if not, they're properly initialized
        const contacts = await getContacts(player.identifier, player.stateid);
        console.log(`[Contacts] Player ${source} has ${contacts.length} contacts`);
    } catch (error) {
        console.error(`[Contacts] Error ensuring player contacts data:`, error);
    }
}

/**
 * Register server events for the contacts app
 */
function registerServerEvents(): void {
    // Register event for getting contacts
    onNet('hm-phone:getContacts', async () => {
        const source = global.source;

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }
            // Get contacts for the player
            const contacts = await getContacts(identifier, stateid);

            // Send the contacts back to the client
            emitNet('hm-phone:setContacts', source, contacts);
        } catch (error) {
            console.error(`[Contacts] Error getting contacts: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error getting contacts');
        }
    });
    // Register event for adding a contact
    onNet('hm-phone:addContact', async (contact: Contact) => {
        const source = global.source;
        console.log(`[Contacts] Received addContact event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }
            // Add the contact to the database
            await addContact(identifier, stateid, contact);

            // Send the result back to the client
            emitNet('hm-phone:contactAdded', source, contact);
        } catch (error) {
            console.error(`[Contacts] Error adding contact: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error adding contact');
        }
    });
    // Register event for updating a contact
    onNet('hm-phone:updateContact', async (contact: Contact) => {
        const source = global.source;
        console.log(`[Contacts] Received updateContact event from player ${source}`);
        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Update the contact in the database
            await updateContact(identifier, stateid, contact);

            // Send the result back to the client
            emitNet('hm-phone:contactUpdated', source, contact);
        } catch (error) {
            console.error(`[Contacts] Error updating contact: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error updating contact');
        }
    });
    // Register event for deleting a contact
    onNet('hm-phone:deleteContact', async (contact: Contact) => {
        const source = global.source;
        console.log(`[Contacts] Received deleteContact event from player ${source} for contact ${contact.id}`);
        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Delete the contact from the database
            await deleteContact(identifier, stateid, contact);

            // Send the result back to the client
            emitNet('hm-phone:contactDeleted', source, contact);
        } catch (error) {
            console.error(`[Contacts] Error deleting contact: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error deleting contact');
        }
    });
    // Register event for toggling favorite status of a contact
    onNet('hm-phone:toggleFavorite', async (contact: Contact) => {
        const source = global.source;
        console.log(`[Contacts] Received toggleFavorite event from player ${source} for contact ${contact.id}`);
        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Toggle the favorite status of the contact in the database
            await toggleFavorite(identifier, stateid, contact);

            // Send the result back to the client
            emitNet('hm-phone:favoriteToggled', source, contact);
        } catch (error) {
            console.error(`[Contacts] Error toggling favorite status: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error toggling favorite status');
        }
    });
    // Register event for sharing a contact
    onNet('hm-phone:shareContact', async (contact: Contact, targetPlayerId: number) => {
        const source = global.source;
        console.log(
            `[Contacts] Received shareContact event from player ${source} for contact ${contact.id} to player ${targetPlayerId}`
        );
        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Get the contact from the database
            const contactData = await getContact(identifier, stateid, contact);

            // Get the target player's identifier
            const targetPlayer = global.getServerPlayerData(targetPlayerId);
            if (!targetPlayer) {
                console.error(`[Contacts] Player ${targetPlayerId} not found`);
                emitNet('hm-phone:contactsError', source, 'Target player not found');
                return;
            }

            // Use stateid if available, fall back to identifier for target player
            const targetStateid = targetPlayer.stateid;
            const targetIdentifier = targetPlayer.identifier;

            if (!targetStateid && !targetIdentifier) {
                console.error(`[Contacts] Target player ${targetPlayerId} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Target player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const targetPlayerIdentifier = targetStateid || targetIdentifier;

            // Share the contact with the target player
            await shareContact(identifier, stateid, targetPlayerIdentifier, contact);

            // Send the result back to the client
            emitNet('hm-phone:contactShared', source, contact, targetPlayerId);
        } catch (error) {
            console.error(`[Contacts] Error sharing contact: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error sharing contact');
        }
    });
    // Register event for accepting a contact share request
    onNet('hm-phone:acceptContactShare', async (request: ContactShareRequest) => {
        const source = global.source;
        console.log(`[Contacts] Received acceptContactShare event from player ${source}`);
        try {
            // Get the requesting player's identifier
            const requestingPlayer = global.getServerPlayerData(source);
            if (!requestingPlayer) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Requesting player not found');
                return;
            }
            const requestingIdentifier = requestingPlayer.identifier;

            // Get the target player's identifier
            const targetPlayer = global.getServerPlayerData(source);
            if (!targetPlayer) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Target player not found');
                return;
            }
            const targetIdentifier = targetPlayer.identifier;
            // Send the result back to the client
            emitNet('hm-phone:contactShareAccepted', source, request.contactId);
        } catch (error) {
            console.error(`[Contacts] Error accepting contact share: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error accepting contact share');
        }
    });
    // Register event for declining a contact share request
    onNet('hm-phone:declineContactShare', async (request: ContactShareRequest) => {
        const source = global.source;
        console.log(`[Contacts] Received declineContactShare event from player ${source}`);
        try {
            // Get the requesting player's identifier
            const requestingPlayer = global.getServerPlayerData(source);
            if (!requestingPlayer) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Requesting player not found');
                return;
            }
            const requestingIdentifier = requestingPlayer.identifier;
            // Get the target player's identifier
            const targetPlayer = global.getServerPlayerData(source);
            if (!targetPlayer) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Target player not found');
                return;
            }
            const targetIdentifier = targetPlayer.identifier;
            emitNet('hm-phone:contactShareDeclined', source, request.contactId);
        } catch (error) {
            console.error(`[Contacts] Error declining contact share: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error declining contact share');
        }
    });

    // Register event for getting call history
    onNet('hm-phone:getCallHistory', async () => {
        const source = global.source;
        console.log(`[Contacts] Received getCallHistory event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Contacts] Player ${source} not found`);
                emitNet('hm-phone:contactsError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Get the player's phone number
            const [playerData] = await globalThis.exports.oxmysql.query_async(
                'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
                [identifier, stateid]
            );

            if (!playerData || !playerData.phone_number) {
                console.error(
                    `[Contacts] Could not find phone number for player with identifier: ${stateid || identifier}`
                );
                emitNet('hm-phone:contactsError', source, 'Player phone number not found');
                return;
            }

            const ownerNumber = playerData.phone_number;

            // Get call history for the player
            const callHistory = await getCallHistory(ownerNumber);

            // Send the call history back to the client
            emitNet('hm-phone:callHistory', source, callHistory);
        } catch (error) {
            console.error(`[Contacts] Error getting call history: ${error}`);
            emitNet('hm-phone:contactsError', source, 'Error getting call history');
        }
    });

    // Register event for adding a call record
    onNet(
        'hm-phone:addCallRecord',
        async (data: { contactNumber: string; status: 'missed' | 'answered' | 'outgoing'; duration: number }) => {
            const source = global.source;
            console.log(`[Contacts] Received addCallRecord event from player ${source}`);

            try {
                // Get player data
                const player = global.getServerPlayerData(source);
                if (!player) {
                    console.error(`[Contacts] Player ${source} not found`);
                    emitNet('hm-phone:contactsError', source, 'Player not found');
                    return;
                }

                // Use stateid if available, fall back to identifier
                const stateid = player.stateid;
                const identifier = player.identifier;

                if (!stateid && !identifier) {
                    console.error(`[Contacts] Player ${source} has no stateid or identifier`);
                    emitNet('hm-phone:contactsError', source, 'Player has no stateid or identifier');
                    return;
                }

                // Get the player's phone number
                const [playerData] = await globalThis.exports.oxmysql.query_async(
                    'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
                    [identifier, stateid]
                );

                if (!playerData || !playerData.phone_number) {
                    console.error(
                        `[Contacts] Could not find phone number for player with identifier: ${stateid || identifier}`
                    );
                    emitNet('hm-phone:contactsError', source, 'Player phone number not found');
                    return;
                }

                const ownerNumber = playerData.phone_number;

                // Extract call data
                const { contactNumber, status, duration } = data;

                // Add the call record to the database
                await addCallRecord(ownerNumber, contactNumber, status, duration);

                // Send success response to the client
                emitNet('hm-phone:callRecordAdded', source);
            } catch (error) {
                console.error(`[Contacts] Error adding call record: ${error}`);
                emitNet('hm-phone:contactsError', source, 'Error adding call record');
            }
        }
    );
}

global.exports = global.exports || {};
// Export relevant contacts functions if needed

/**
 * Get contacts for a player
 * @param {string} identifier - Player identifier
 * @returns {Promise<Contact[]>} - Array of contacts
 */
async function getContacts(identifier: string, stateid: string): Promise<Contact[]> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            return [];
        }

        const ownerNumber = playerData.phone_number;

        // Query the database for contacts associated with the player's phone number
        console.log(`[Contacts] Getting contacts for player with phone number: ${ownerNumber}`);
        const contacts = await globalThis.exports.oxmysql.query_async(
            'SELECT * FROM phone_contacts WHERE identifier = ? AND stateid = ?',
            [identifier, stateid]
        );

        // Return the contacts directly without transformation
        return contacts as Contact[];
    } catch (error) {
        console.error(`[Contacts] Error getting contacts from database: ${error}`);
        throw error;
    }
}

/**
 * Get a contact for a player
 * @param {string} identifier - Player identifier
 * @param {number} contact - the contact to get
 * @returns {Promise<Contact>}
 */
async function getContact(identifier: string, stateid: string, contact: Contact): Promise<Contact> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            throw new Error('Player phone number not found');
        }

        const ownerNumber = playerData.phone_number;

        // Query the database for the contact
        const [contactData] = await exports.oxmysql.query_async(
            'SELECT * FROM phone_contacts WHERE identifier = ? AND stateid = ?  AND number = ?',
            [identifier, stateid, contact.number]
        );

        if (!contactData) {
            throw new Error(`Contact with ID ${contact.number} not found for player with phone ${ownerNumber}`);
        }

        // Return the contact directly without transformation
        return contactData as Contact;
    } catch (error) {
        console.error(`[Contacts] Error getting contact from database: ${error}`);
        throw error;
    }
}

/**
 * Add a contact for a player
 * @param {string} identifier - Player identifier
 * @param {Contact} contact - Contact to add
 * @returns {Promise<number>} - ID of the newly added contact
 */
async function addContact(identifier: string, stateid: string, contact: Contact): Promise<number> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            throw new Error('Player phone number not found');
        }
        // Insert the contact into the database
        const insertId = await exports.oxmysql.insert_async(
            'INSERT INTO phone_contacts (identifier, stateid, name, number, avatar, favorite) VALUES (?, ?, ?, ?, ?, ?)',
            [
                identifier,
                stateid,
                contact.name,
                contact.number,
                contact.avatar || null,
                contact.favorite || 0
            ]
        );

        return insertId;
    } catch (error) {
        console.error(`[Contacts] Error adding contact to database: ${error}`);
        throw error;
    }
}

/**
 * Update a contact for a player
 * @param {string} identifier - Player identifier
 * @param {Contact} contact - Contact to update
 * @returns {Promise<void>}
 */
async function updateContact(identifier: string, stateid: string, contact: Contact): Promise<void> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM players WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            throw new Error('Player phone number not found');
        }

        const ownerNumber = playerData.phone_number;

        // Update the contact in the database
        await exports.oxmysql.update_async(
            'UPDATE phone_contacts SET number = ?, name = ?, favorite = ?, avatar = ? WHERE identifier = ? AND stateid = ? AND number = ?',
            [
                contact.number,
                contact.name,
                contact.favorite || 0,
                contact.avatar || null,
                identifier,
                stateid,
                contact.number
            ]
        );
    } catch (error) {
        console.error(`[Contacts] Error updating contact in database: ${error}`);
        throw error;
    }
}

/**
 * Delete a contact for a player
 * @param {string} identifier - Player identifier
 * @param {number} contactId - ID of the contact to delete
 * @returns {Promise<void>}
 */
async function deleteContact(identifier: string, stateid: string, contact: Contact): Promise<void> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            throw new Error('Player phone number not found');
        }

        // Delete the contact from the database
        await exports.oxmysql.delete_async('DELETE FROM phone_contacts WHERE identifier = ? AND stateid = ? AND number = ?', [
            identifier,
            stateid,
            contact.number
        ]);
    } catch (error) {
        console.error(`[Contacts] Error deleting contact from database: ${error}`);
        throw error;
    }
}

/**
 * Toggle favorite status of a contact for a player
 * @param {string} identifier - Player identifier
 * @param {number} contact - Contact to toggle
 * @returns {Promise<void>}
 */
async function toggleFavorite(identifier: string, stateid: string, contact: Contact): Promise<void> {
    try {
        // Get the player's phone number
        const [playerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [identifier, stateid]
        );

        if (!playerData || !playerData.phone_number) {
            console.error(`[Contacts] Could not find phone number for player with identifier: ${identifier}`);
            throw new Error('Player phone number not found');
        }

        const ownerNumber = playerData.phone_number;

        // Get the current favorite status of the contact
        const [favoriteData] = await exports.oxmysql.query_async(
            'SELECT favorite FROM phone_contacts WHERE identifier = ? AND stateid = ? AND number = ?',
            [ownerNumber, stateid, contact.number]
        );

        if (!favoriteData) {
            throw new Error(`Contact with number ${contact.number} not found for player with phone ${ownerNumber}`);
        }

        // Update the favorite status in the database
        await exports.oxmysql.update_async(
            'UPDATE phone_contacts SET favorite = ? WHERE identifier = ? AND stateid = ? AND number = ?',
            [contact.favorite ? 0 : 1, ownerNumber, stateid, contact.number]
        );
    } catch (error) {
        console.error(`[Contacts] Error toggling favorite status: ${error}`);
        throw error;
    }
}

/**
 * Share a contact with another player
 * @param {string} identifier - Player identifier
 * @param {string} targetIdentifier - Target player identifier
 * @param {Contact} contact - Contact to share
 * @returns {Promise<void>}
 */
async function shareContact(identifier: string, targetIdentifier: string, targetStateid: string, contact: Contact): Promise<void> {
    try {
        // Get the target player's phone number
        const [targetPlayerData] = await globalThis.exports.oxmysql.query_async(
            'SELECT phone_number FROM characters WHERE identifier = ? AND stateid = ? LIMIT 1',
            [targetIdentifier, targetStateid]
        );

        if (!targetPlayerData || !targetPlayerData.phone_number) {
            console.error(
                `[Contacts] Could not find phone number for target player with identifier: ${targetIdentifier}`
            );
            throw new Error('Target player phone number not found');
        }
        // Create a new contact for the target player
        const sharedContact: Contact = {
            identifier:  targetIdentifier,
            stateid: targetStateid,
            number: contact.number,
            name: contact.name,
            favorite: 0,
            is_blocked: 0,
            avatar: contact.avatar,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // Insert the shared contact into the database
        await exports.oxmysql.insert_async(
            'INSERT INTO phone_contacts (identifier, stateid, number, name, favorite, avatar) VALUES (?, ?, ?, ?, ?, ?)',
            [
                targetIdentifier,
                targetStateid,
                sharedContact.number,
                sharedContact.name,
                sharedContact.favorite,
                sharedContact.avatar,
            ]
        );
    } catch (error) {
        console.error(`[Contacts] Error sharing contact: ${error}`);
        throw error;
    }
}

/**
 * Get call history for a player
 * @param {string} ownerNumber - Player's phone number
 * @returns {Promise<Call[]>} - Array of call records
 */
async function getCallHistory(ownerNumber: string): Promise<Call[]> {
    try {
        // Query the database for call records associated with the player's phone number
        const callHistory = await global.exports.oxmysql.query_async(
            'SELECT * FROM phone_calls WHERE owner_number = ? ORDER BY timestamp DESC LIMIT 50',
            [ownerNumber]
        );

        // Check if call history is null or not an array
        if (!callHistory || !Array.isArray(callHistory)) {
            console.log(`[Contacts] No call history found or invalid data returned for owner number ${ownerNumber}`);
            return []; // Return empty array if no call history found
        }

        // Return the call history directly without transformation
        return callHistory as Call[];
    } catch (error) {
        console.error(`[Contacts] Error getting call history from database: ${error}`);
        throw error;
    }
}

/**
 * Add a call record to the database
 * @param {string} ownerNumber - Player's phone number
 * @param {string} contactNumber - Contact's phone number
 * @param {string} status - Call status (missed, answered, outgoing)
 * @param {number} duration - Call duration in seconds
 * @returns {Promise<number>} - ID of the newly added call record
 */
async function addCallRecord(
    ownerNumber: string,
    contactNumber: string,
    status: 'missed' | 'answered' | 'outgoing',
    duration: number
): Promise<number> {
    try {
        // Insert the call record into the database
        const timestamp = Math.floor(Date.now() / 1000); // Current timestamp in seconds
        const insertId = await exports.oxmysql.insert_async(
            'INSERT INTO phone_calls (owner_number, contact_number, timestamp, duration, status) VALUES (?, ?, ?, ?, ?)',
            [ownerNumber, contactNumber, timestamp, duration, status]
        );

        return insertId;
    } catch (error) {
        console.error(`[Contacts] Error adding call record to database: ${error}`);
        throw error;
    }
}
