import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface ProgressBarProps {
  label: string;
  duration: number;
  startTime: number;
  onCancel: () => void;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  duration,
  startTime,
  onCancel,
}) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / duration) * 100, 100);
      setProgress(newProgress);

      if (newProgress < 100) {
        requestAnimationFrame(updateProgress);
      }
    };

    updateProgress();
  }, [startTime, duration]);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onCancel();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [onCancel]);

  return (
    <div className="progress-overlay">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
        className="text-center"
      >
        {/* Icon */}
        <div className="mb-4">
          <div className="w-12 h-12 mx-auto bg-target-primary rounded-full flex items-center justify-center">
            <i className="fas fa-clock text-white text-lg" />
          </div>
        </div>

        {/* Label */}
        <h3 className="text-white font-medium text-lg mb-6">{label}</h3>

        {/* Progress Bar */}
        <div className="relative">
          <div className="w-full h-3 bg-neutral-700 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-target-success to-target-accent"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ ease: "linear" }}
            />
          </div>

          {/* Progress Text */}
          <div className="mt-3 flex justify-between items-center text-sm">
            <span className="text-neutral-400">{Math.round(progress)}%</span>
            <span className="text-neutral-400">
              {Math.max(
                0,
                Math.ceil((duration - (Date.now() - startTime)) / 1000)
              )}
              s
            </span>
          </div>
        </div>

        {/* Cancel Button */}
        <button
          onClick={onCancel}
          className="mt-6 px-6 py-2 bg-target-error hover:bg-red-600 text-white rounded-lg transition-colors duration-150 flex items-center gap-2 mx-auto"
        >
          <i className="fas fa-times text-sm" />
          Cancel
        </button>

        {/* Instructions */}
        <p className="mt-4 text-neutral-400 text-xs">Press ESC to cancel</p>
      </motion.div>
    </div>
  );
};

export default ProgressBar;
