/**
 * Yellow Pages app message handlers
 *
 * These handlers receive events from the FiveM client and update the Yellow Pages store.
 *
 * Flow:
 * 1. Client sends Yellow Pages data using SendNUIMessage({app: 'yellowPages', type: 'ads', data: [...]})
 * 2. clientEventReceiver dispatches the event to the registered handler
 * 3. <PERSON><PERSON> validates the data and updates the Yellow Pages store
 * 4. UI components re-render with the new data
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useYellowPagesStore } from '../stores/yellowPagesStore';
import { Ad } from '../types/yellowPagesTypes';

// Register handler for Yellow Pages ads data
registerEventHandler('yellowPages', 'ads', data => {
  console.log('[YellowPages] Received ads data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the ads data
    useYellowPagesStore.getState().handlers.onSetAds(data as Ad[]);
  } else {
    console.error('[YellowPages] Received invalid ads data (not an array):', data);
  }
});

// Register handler for ad created confirmation
registerEventHandler('yellowPages', 'adCreated', data => {
  console.log('[YellowPages] Ad created confirmation:', data);

  if (
    data &&
    typeof data === 'object' &&
    'success' in data &&
    'ad' in data &&
    data.success &&
    data.ad
  ) {
    useYellowPagesStore.getState().handlers.onAddAd(data.ad as Ad);
  } else {
    console.error('[YellowPages] Ad creation failed:', data);
  }
});

// Register handler for ad updated confirmation
registerEventHandler('yellowPages', 'adUpdated', data => {
  console.log('[YellowPages] Ad updated confirmation:', data);

  if (
    data &&
    typeof data === 'object' &&
    'success' in data &&
    'ad' in data &&
    data.success &&
    data.ad
  ) {
    useYellowPagesStore.getState().handlers.onUpdateAd(data.ad as Ad);
  } else {
    console.error('[YellowPages] Ad update failed:', data);
  }
});

// Register handler for ad deleted confirmation
registerEventHandler('yellowPages', 'adDeleted', data => {
  console.log('[YellowPages] Ad deleted confirmation:', data);

  if (
    data &&
    typeof data === 'object' &&
    'success' in data &&
    'id' in data &&
    data.success &&
    data.id
  ) {
    useYellowPagesStore.getState().handlers.onDeleteAd(data.id as number);
  } else {
    console.error('[YellowPages] Ad deletion failed:', data);
  }
});
