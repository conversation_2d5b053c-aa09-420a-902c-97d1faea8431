import React from 'react';
import { CharacterSelectionData } from '../../../scripts/shared/character.types';

export type Character = CharacterSelectionData;

interface CharacterCardProps {
  character: Character;
  spawnPointIndex: number;
  isActive?: boolean;
  onSelect: (stateid: string) => void;
  onDelete: (stateid: string) => void;
  onHover: (stateid: string, spawnPointIndex: number) => void;
  onHoverEnd: () => void;
}

export const CharacterCard: React.FC<CharacterCardProps> = ({ character, spawnPointIndex, isActive = false, onSelect, onDelete, onHover, onHoverEnd }) => (
  <div 
    className={`transition-all duration-300 ease-out ${isActive ? 'scale-[1.02]' : ''}`}
    onMouseEnter={() => onHover(character.stateid, spawnPointIndex)}
    onMouseLeave={() => onHoverEnd()}
  >
    <div className={`bg-[#111827]/40 rounded-2xl overflow-hidden shadow-lg ${isActive ? 'ring-2 ring-blue-500' : ''}`}>
      <div className="p-5">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-bold text-white">{character.first_name} {character.last_name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-400">                
                Last played: {new Date(character.updated_at!).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                })} at {new Date(character.updated_at!).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })} UTC
              </span>
            </div>
          </div>          <button            onClick={e => { e.stopPropagation(); onDelete(character.stateid); }}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-[#1f2937]/50 text-gray-400 hover:text-red-400 transition-all"
          >
            <i className="fas fa-trash-alt text-xs"></i>
          </button>
        </div>        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-[#1f2937]/50 rounded-xl p-3 text-center">
            <div className="text-lg font-bold text-white">${character.money.cash || 0}</div>
            <div className="text-xs text-gray-400">Cash</div>
          </div>
        </div>        <button
          onClick={() => onSelect(character.stateid)}
          className={`w-full py-3 rounded-xl text-white text-sm font-medium transition-all duration-300 ${isActive ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-md' : 'bg-[#1f2937]/50 hover:bg-[#2d3748]/60'}`}
        >
          <div className="flex items-center justify-center gap-2">
            <i className={`fas ${isActive ? 'fa-user-check' : 'fa-user'} text-xs`}></i>
            <span>SELECT</span>
          </div>
        </button>
      </div>
    </div>
  </div>
);
