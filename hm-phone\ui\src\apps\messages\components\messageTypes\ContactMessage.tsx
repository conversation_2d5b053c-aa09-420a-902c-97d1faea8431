import React, { useMemo } from 'react';
import { ContactMessageType } from '../../../../../../shared/types';
import { useContactsStore } from '../../../contacts/stores/contactsStore';
import { useNavigation } from '../../../../navigation/hooks';
import { usePhoneStore } from '../../../../common/stores/phoneStateStore';

// Extended ContactMessageType with UI-specific properties
interface UIContactMessage extends Omit<ContactMessageType, 'content'> {
  message?: string;
  timestamp?: string;
  content?: {
    name: string;
    number: string;
  };
  metadata?: {
    contact: {
      id?: number;
      identifier?: string;
      stateid?: string;
      owner_number?: string;
      number: string;
      name: string;
      favorite?: number;
      avatar?: string | null;
      created_at?: string;
      updated_at?: string;
    };
    timestamp?: number;
  };
}

interface ContactMessageProps {
  message: UIContactMessage;
}

export const ContactMessage: React.FC<ContactMessageProps> = ({ message }) => {
  // Extract contact data from metadata
  const contact = message.metadata?.contact || {
    name: 'Unknown Contact',
    number: 'Unknown'
  };

  // Create content object if it doesn't exist
  const content = message.content || {
    name: contact.name,
    number: contact.number
  };
  const { contacts } = useContactsStore();
  const { openAppView } = useNavigation();
  const { userProfile } = usePhoneStore();

  // Check if this is the user's own contact
  const isOwnContact = useMemo(() => {
    return userProfile && userProfile.phoneNumber === content.number;
  }, [userProfile, content.number]);

  // Check if the contact already exists in the user's contacts list
  const contactExists = useMemo(() => {
    // If this is the user's own contact, consider it as existing
    if (isOwnContact) return true;

    // If the contact has an ID, check if a contact with that ID exists
    if (contact.id) {
      return contacts.some(c => c.id === contact.id);
    }

    // Otherwise, check if a contact with the same phone number exists
    return contacts.some(c => c.number === content.number);
  }, [contacts, contact, content.number, isOwnContact]);

  const handleContactClick = () => {
    // Don't do anything if it's the user's own contact
    if (isOwnContact) return;

    if (contactExists) {
      // If the contact exists, open the contact detail view
      const existingContact = contacts.find(
        c => (contact.id && c.id === contact.id) || (!contact.id && c.number === content.number)
      );

      if (existingContact) {
        openAppView('contacts', 'detail', { id: existingContact.id });
      }
    } else {
      // If the contact doesn't exist, open the add contact view with pre-filled data
      openAppView('contacts', 'add', {
        name: content.name,
        phone: content.number
      });
    }
  };

  return (
    <div className="w-fit min-w-0 max-w-full">
      <div className="rounded-xl overflow-hidden bg-white/5">
        <div className="p-1.5 flex items-center gap-3">
          <div className="w-8 h-8 rounded-full overflow-hidden">
            {contact.avatar ? (
              <img
                src={contact.avatar}
                alt={content.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-blue-600/20 flex items-center justify-center">
                <span className="text-white/80 text-sm font-medium">
                  {content.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-white/90 truncate">{content.name}</div>
            <div className="text-xs font-medium text-white/60 truncate">{content.number}</div>
          </div>
          {/* Only show the button if it's not the user's own contact */}
          {!isOwnContact && (
            <button
              onClick={handleContactClick}
              className="flex-shrink-0 w-7 h-7 rounded-lg bg-white/10 hover:bg-white/20 transition-colors flex items-center justify-center"
              title={contactExists ? 'View Contact' : 'Add Contact'}
            >
              {contactExists ? (
                <i className="fas fa-address-card text-xs text-white/80"></i>
              ) : (
                <i className="fas fa-user-plus text-xs text-white/80"></i>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactMessage;
