/**
 * HM Target - Shared Configuration
 *
 * This file contains the main configuration for the targeting system.
 */

export const generalConfig = {
  resourceName: "hm-target",
  debug: true,
  framework: {
    forceFramework: "hm-core",
    debug: true,
  },
};

export const targetConfig = {
  maxRange: 2.0, // Maximum interaction range in meters
  raycastDistance: 3.0, // Optimized raycast distance (was 3.5, now closer to maxRange)
  updateFrequency: 50, // Much more responsive update frequency (~20 FPS, was 1000ms/1 FPS)
  defaultInteractionKey: "LMENU", // Default interaction key (Left ALT)

  // Debug settings - centralized debug configuration
  debug: {
    enabled: false, // Master debug toggle
    showZones: true, // Show zone visualizations
    showEntities: true, // Show entity outlines and info
    showRaycast: true, // Show raycast lines and hit points
    showStats: true, // Show performance statistics on screen
    persistentVisualizations: false, // Keep visualizations when targeting is inactive
  },

  // Performance optimization settings
  frameSkipCount: 1, // Reduced frame skipping (was 2, now 1 for smoother experience)
  raycastOptimization: true, // Enable raycast optimizations
  spatialCulling: true, // Enable spatial culling for entities/zones

  // Raycast caching settings (new)
  raycastCaching: {
    enabled: true, // Enable raycast result caching
    maxCacheAge: 100, // Maximum age of cached raycast results in ms
    cameraMovementThreshold: 0.1, // Camera position threshold for cache invalidation
    cameraRotationThreshold: 1.0, // Camera rotation threshold for cache invalidation
    entityCacheSize: 50, // Maximum number of entities to cache type/existence for
    entityCacheAge: 1000, // Maximum age of entity cache entries in ms
  },

  // Zone optimization settings
  zoneOptimization: {
    enabled: true, // Enable zone optimizations
    spatialGridSize: 50.0, // Size of spatial grid cells in meters
    maxZoneChecksPerFrame: 10, // Maximum zones to check per frame
    distanceCulling: 20.0, // Distance beyond which zones are ignored
    playerMovementThreshold: 0.5, // Minimum movement to recheck zones
  },

  // Visual settings
  visual: {
    indicatorColor: [16, 185, 129], // RGB for target indicator
    successColor: [34, 197, 94], // RGB for success state
    warningColor: [245, 158, 11], // RGB for warning state
    errorColor: [239, 68, 68], // RGB for error state
    animationDuration: 200, // Animation duration in ms
    fadeDistance: 2.0, // Distance where targets start to fade
  },

  // Performance settings (updated for raycast optimization)
  performance: {
    maxActiveTargets: 15, // Reduced from 25 for better performance
    spatialGridSize: 15.0, // Spatial grid size for zone optimization
    cullingDistance: 8.0, // Reduced culling distance (was 10.0)
    enableSpatialOptimization: true, // Enable spatial indexing
    frameSkippingEnabled: true, // Enable frame skipping for non-critical operations
    batchEntityChecks: true, // Batch entity checks to reduce per-frame overhead
    asyncOperations: true, // Enable async operations where possible
  },
};

export const uiConfig = {
  animations: {
    enabled: true,
    optionSlideDelay: 50, // Delay between option animations
    progressBarSpeed: 1000, // Progress bar animation speed
    fadeInDuration: 200, // Fade in duration
    fadeOutDuration: 150, // Fade out duration
  },

  styling: {
    borderRadius: 12, // Border radius for UI elements
    backdropBlur: 8, // Backdrop blur amount
    shadowIntensity: 0.4, // Shadow intensity
    optionHeight: 44, // Height of option buttons
    maxOptionsVisible: 8, // Maximum options visible without scrolling
  },
};

export default {
  general: generalConfig,
  target: targetConfig,
  ui: uiConfig,
};
