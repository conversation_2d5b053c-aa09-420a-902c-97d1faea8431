import { LocalPlayer } from './player';

class hmCore {
    private static instance: hmCore;
    private localPlayer: LocalPlayer | undefined;
    private isInitialized = false;

    private constructor() {
        this.initialize();
    }

    public static getInstance(): hmCore {
        if (!hmCore.instance) {
            hmCore.instance = new hmCore();
        }
        return hmCore.instance;
    }

    private async initialize(): Promise<void> {
        try {
            console.info('=== CLIENT CORE INITIALIZATION ===');
            console.info(`Client time: ${new Date().toISOString()}`);

            // Wait for game to be fully loaded
            await this.waitForGameLoad();

            // Initialize player client
            this.localPlayer = LocalPlayer.getInstance();
            console.info('✓ Player Client initialized');

            // Setup client events
            this.setupClientEvents();

            // Setup NUI handlers
            this.setupNUIHandlers();

            // Register client exports
            this.registerClientExports();

            this.isInitialized = true;
            console.info('=== CLIENT CORE READY ===');

            // Notify server that client is ready
            emitNet('client:ready');
        } catch (error) {
            console.error(`Failed to initialize client core: ${error}`);
        }
    }

    private async waitForGameLoad(): Promise<void> {
        return new Promise(resolve => {
            const checkGameLoad = setInterval(() => {
                if (IsPlayerSwitchInProgress() === false && NetworkIsPlayerActive(PlayerId())) {
                    clearInterval(checkGameLoad);
                    console.info('✓ Game fully loaded');
                    resolve();
                }
            }, 1000);
        });
    }

    private setupClientEvents(): void {
        // Resource management
        on('onResourceStop', (resourceName: string) => {
            if (resourceName === GetCurrentResourceName()) {
                console.info('=== CLIENT CORE SHUTDOWN ===');
                this.cleanup();
            }
        });

        // Network events
        onNet('core:reload', () => {
            console.info('Core reload requested');
            // Implement client-side reload logic
        });

        console.info('✓ Client events registered');
    }

    private registerClientExports(): void {
        // Export LocalPlayer functions for other client-side resources
        global.exports('getLocalPlayer', () => this.localPlayer);
        
        // Export specific functions that other resources need
        global.exports('getActiveCharacter', () => {
            return this.localPlayer?.getActiveCharacter() || null;
        });

        global.exports('getPlayerData', () => {
            return this.localPlayer?.getPlayerData() || null;
        });

        global.exports('getCharacters', () => {
            return this.localPlayer?.getCharacters() || [];
        });

        console.info('✓ Client exports registered');
    }

    private setupNUIHandlers(): void {
        // Focus management
        let nuiFocused = false;

        RegisterNuiCallbackType('focusLost');
        on('__cfx_nui:focusLost', (data: any, cb: (response: string) => void) => {
            if (nuiFocused) {
                SetNuiFocus(false, false);
                nuiFocused = false;
            }
            cb('ok');
        });

        // Error handling for NUI
        RegisterNuiCallbackType('error');
        on('__cfx_nui:error', (data: { message: string }, cb: (response: string) => void) => {
            console.error(`NUI Error: ${data.message}`);
            cb('ok');
        });

        console.info('✓ NUI handlers registered');
    }

    private cleanup(): void {
        console.info('Performing client cleanup...');

        console.info('Client cleanup completed');
    }

    public isReady(): boolean {
        return this.isInitialized;
    }

    public getLocalPlayer(): LocalPlayer {
        return this.localPlayer!;
    }
}

// Initialize client core
const clientCore = hmCore.getInstance();

// Global access
(global as any).hmCore = clientCore;
