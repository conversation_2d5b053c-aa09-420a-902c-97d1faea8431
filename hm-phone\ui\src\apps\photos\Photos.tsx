import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ImageModal from '../../common/components/ImageModal';
import { useSelectionStore } from '../../common/stores/selectionStore';
import { useNavigation } from '../../navigation/hooks';
import { usePhotosStore } from './stores/photosStore';

const Photos: React.FC = () => {
  // Get all navigation functions at the top level
  const { goBackWithResult, openApp } = useNavigation();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const { active, maxSelection, endSelection, cancelSelection } = useSelectionStore();
  const [selectedTab, setSelectedTab] = useState<'all' | 'albums'>('all');

  // Initialize selectedImages with the initialSelected from selectionStore
  const [selectedImages, setSelectedImages] = useState<string[]>(() => {
    const state = useSelectionStore.getState();
    const initialSelected = state.initialSelected || [];
    // If initialSelected is an array of objects (e.g., [{ id: string }]), map to string[]
    if (
      initialSelected.length > 0 &&
      typeof initialSelected[0] === 'object' &&
      initialSelected[0] !== null
    ) {
      // Try to extract 'id' or 'imageUrl' property
      if ('id' in initialSelected[0]) {
        return initialSelected.map((item: any) => item.id);
      } else if ('imageUrl' in initialSelected[0]) {
        return initialSelected.map((item: any) => item.imageUrl);
      }
    }
    // Otherwise, assume it's already string[]
    return initialSelected;
  });

  // Get photos from the store
  const { photos, actions, loading } = usePhotosStore();

  // Group photos by date
  const photoGroups = React.useMemo(() => {
    const groups: Record<string, { date: string; label: string; photos: string[] }> = {};

    // Process each photo
    photos.forEach(photo => {
      // Convert timestamp to date string
      const timestamp =
        photo.timestamp instanceof Date ? photo.timestamp : new Date(photo.timestamp);
      const dateStr = timestamp.toLocaleDateString();

      // Determine label
      let label = dateStr;
      const today = new Date().toLocaleDateString();
      const yesterday = new Date(Date.now() - 86400000).toLocaleDateString();

      if (dateStr === today) {
        label = 'Today';
      } else if (dateStr === yesterday) {
        label = 'Yesterday';
      }

      // Create group if it doesn't exist
      if (!groups[dateStr]) {
        groups[dateStr] = {
          date: dateStr,
          label,
          photos: []
        };
      }

      // Add photo to group
      groups[dateStr].photos.push(photo.imageUrl);
    });

    // Convert to array and sort by date (newest first)
    return Object.values(groups).sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
  }, [photos]);

  const handleImageClick = (photo: string) => {
    if (active) {
      if (selectedImages.includes(photo)) {
        console.log('[Photos] Removing photo from selection:', photo.substring(0, 30) + '...');
        setSelectedImages(prev => prev.filter(p => p !== photo));
      } else if (selectedImages.length < (maxSelection || 6)) {
        console.log('[Photos] Adding photo to selection:', photo.substring(0, 30) + '...');
        setSelectedImages(prev => [...prev, photo]);
      } else {
        console.log('[Photos] Maximum selection limit reached:', maxSelection || 6);
      }
    } else {
      setSelectedImage(photo);
    }
  };

  // Handle deleting a photo
  const handleDeletePhoto = (photoUrl: string) => {
    // Find the photo ID from the URL
    const photo = photos.find(p => p.imageUrl === photoUrl);
    if (photo) {
      actions.deletePhoto(photo.id);
      // Close the modal if the deleted photo was selected
      if (selectedImage === photoUrl) {
        setSelectedImage(null);
      }
    }
  };

  const handleCancel = () => {
    console.log('[Photos] Cancelling selection');
    cancelSelection();
    goBackWithResult([]);
    console.log('[Photos] Cancelled selection, navigating back');
  };

  const handleConfirmSelection = () => {
    console.log('[Photos] Confirming selection of', selectedImages.length, 'photos');

    if (selectedImages.length === 0) {
      console.log('[Photos] No photos selected, cancelling');
      handleCancel();
      return;
    }

    const photosCopy = [...selectedImages];
    console.log('[Photos] Calling endSelection with', photosCopy.length, 'photos');
    // If endSelection expects objects, map string[] to { id: string }
    if (typeof selectedImages[0] === 'string') {
      endSelection(selectedImages.map(id => ({ id })));
    } else {
      endSelection(selectedImages.map(id => ({ id })));
    }

    setTimeout(() => {
      console.log('[Photos] Calling goBackWithResult with', photosCopy.length, 'photos');
      goBackWithResult(photosCopy);
      console.log('[Photos] Confirmed selection of', photosCopy.length, 'photos, navigating back');
    }, 100);
  };

  return (
    <div className="h-full w-full flex flex-col bg-black pt-8 pb-3">
      {/* Header */}
      {active ? (
        <div className="flex justify-between items-center px-4 py-2 border-b border-white/10">
          <button onClick={handleCancel} className="text-white/80 hover:text-white">
            Cancel
          </button>
          <span className="text-white/40 text-sm">
            {selectedImages.length} / {maxSelection || 6}
          </span>
          <button
            onClick={handleConfirmSelection}
            disabled={selectedImages.length === 0}
            className={`text-blue-500 hover:text-blue-400 ${
              selectedImages.length === 0 ? 'opacity-50' : ''
            }`}
          >
            Done
          </button>
        </div>
      ) : (
        <>
          <div className="px-4 py-2 flex justify-between items-center">
            <div className="text-white font-medium text-lg">Photos</div>
            <button className="w-10 h-10 flex items-center justify-center text-white/70">
              <i className="fas fa-search"></i>
            </button>
          </div>

          {/* Tabs */}
          <div className="px-4 border-b border-white/10">
            <div className="flex space-x-6">
              <button
                onClick={() => setSelectedTab('all')}
                className={`py-2 px-1 text-sm font-medium relative ${
                  selectedTab === 'all' ? 'text-white' : 'text-white/50'
                }`}
              >
                All Photos
                {selectedTab === 'all' && (
                  <motion.div
                    layoutId="tab-indicator"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"
                    initial={false}
                  />
                )}
              </button>
              <button
                onClick={() => setSelectedTab('albums')}
                className={`py-2 px-1 text-sm font-medium relative ${
                  selectedTab === 'albums' ? 'text-white' : 'text-white/50'
                }`}
              >
                Albums
                {selectedTab === 'albums' && (
                  <motion.div
                    layoutId="tab-indicator"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"
                    initial={false}
                  />
                )}
              </button>
            </div>
          </div>
        </>
      )}

      {/* Gallery Content */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-white/50">Loading photos...</div>
          </div>
        ) : photoGroups.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-white/50">No photos yet</div>
          </div>
        ) : (
          photoGroups.map(group => (
            <div key={group.date} className="mb-4">
              <div className="px-4 py-2 sticky top-0 bg-black/80 backdrop-blur-sm z-10">
                <h3 className="text-white/80 text-sm font-medium">{group.label}</h3>
              </div>
              <div className="grid grid-cols-3 gap-1 px-1">
                {group.photos.map((photo, index) => (
                  <motion.div
                    key={`${group.date}-${index}`}
                    whileTap={{ scale: 0.95 }}
                    className="aspect-[9/19.5] relative"
                    onClick={() => handleImageClick(photo)}
                  >
                    <img
                      src={photo}
                      alt={`Photo ${index}`}
                      className="w-full h-full object-cover"
                      style={{
                        // Apply 5% cropping from top and bottom to match camera view
                        objectPosition: 'center center',
                        clipPath: 'inset(5% 0 5% 0)'
                      }}
                    />
                    {active && selectedImages.includes(photo) && (
                      <div className="absolute inset-0 bg-blue-500/30 flex items-center justify-center">
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                          <span className="text-white text-sm">
                            {selectedImages.indexOf(photo) + 1}
                          </span>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>

      {!active && (
        <>
          {/* Floating Action Button - Camera */}
          <div className="absolute bottom-6 right-6">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg"
              onClick={() => openApp('camera')}
            >
              <i className="fas fa-camera text-lg"></i>
            </motion.button>
          </div>

          {/* Image Modal */}
          <ImageModal
            isVisible={selectedImage !== null}
            onClose={() => setSelectedImage(null)}
            imageUrl={selectedImage || ''}
            onDelete={selectedImage ? () => handleDeletePhoto(selectedImage) : undefined}
          />
        </>
      )}
    </div>
  );
};

export default Photos;
