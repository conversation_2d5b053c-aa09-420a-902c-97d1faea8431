import { create } from 'zustand';
import { sendNuiMessage, NuiActions, onNuiEvent } from '../utils/nui';

// Type definitions
export interface ItemDefinition {
  name: string;
  label: string;
}

export interface WeaponDefinition {
  name: string;
  label: string;
}

export interface VehicleDefinition {
  model: string;
  name?: string;
  make?: string;
  class?: string;
}

// Store state interface
interface AdminStoreState {
  // Data collections
  items: ItemDefinition[];
  weapons: WeaponDefinition[];
  vehicles: VehicleDefinition[];
  
  // Status flags
  isInitialized: boolean;
  isLoading: {
    items: boolean;
    weapons: boolean;
    vehicles: boolean;
  };

  // Actions
  fetchItems: () => void;
  fetchWeapons: () => void;
  fetchVehicles: () => void;
  fetchAllData: () => void;
  
  // Setters
  setItems: (items: ItemDefinition[]) => void;
  setWeapons: (weapons: WeaponDefinition[]) => void;
  setVehicles: (vehicles: VehicleDefinition[]) => void;
  
  // Clean up
  reset: () => void;
  cleanup: () => void;
}

// Create the store
export const useAdminStore = create<AdminStoreState>((set, get) => ({
  // Initial state
  items: [],
  weapons: [],
  vehicles: [],
  
  isInitialized: false,
  isLoading: {
    items: false,
    weapons: false,
    vehicles: false
  },

  // Actions
  fetchItems: () => {
    set(state => ({ 
      isLoading: { ...state.isLoading, items: true } 
    }));
    
    sendNuiMessage(NuiActions.GET_ITEM_LIST, {});
    console.log('[AdminStore] Fetching items');
  },
  
  fetchWeapons: () => {
    set(state => ({ 
      isLoading: { ...state.isLoading, weapons: true } 
    }));
    
    sendNuiMessage(NuiActions.GET_WEAPON_LIST, {});
    console.log('[AdminStore] Fetching weapons');
  },
  
  fetchVehicles: () => {
    set(state => ({ 
      isLoading: { ...state.isLoading, vehicles: true } 
    }));
    
    sendNuiMessage(NuiActions.GET_VEHICLE_LIST, {});
    console.log('[AdminStore] Fetching vehicles');
  },
  
  fetchAllData: () => {
    const { fetchItems, fetchWeapons, fetchVehicles } = get();
    
    fetchItems();
    fetchWeapons();
    fetchVehicles();
    
    set({ isInitialized: true });
    console.log('[AdminStore] Fetching all data');
  },
  
  // Setters
  setItems: (items: ItemDefinition[]) => {
    set({ 
      items, 
      isLoading: { ...get().isLoading, items: false } 
    });
  },
  
  setWeapons: (weapons: WeaponDefinition[]) => {
    set({ 
      weapons, 
      isLoading: { ...get().isLoading, weapons: false } 
    });
  },
  
  setVehicles: (vehicles: VehicleDefinition[]) => {
    set({ 
      vehicles, 
      isLoading: { ...get().isLoading, vehicles: false } 
    });
  },
  
  // Clean up
  reset: () => {
    set({
      items: [],
      weapons: [],
      vehicles: [],
      isInitialized: false,
      isLoading: {
        items: false,
        weapons: false,
        vehicles: false
      }
    });
    console.log('[AdminStore] Store reset');
  },
  
  // Clean up resources when UI closes
  cleanup: () => {
    console.log('[AdminStore] Cleaning up resources');
    set({
      items: [],
      weapons: [],
      vehicles: [],
      isInitialized: false,
      isLoading: {
        items: false,
        weapons: false,
        vehicles: false
      }
    });
  }
}));

// Initialize event handlers for NUI responses
export const initializeAdminStore = (): (() => void) => {
  console.log('[AdminStore] Initializing store and event listeners');
  
  // This is just setting up event listeners, NOT triggering data fetching
  // Data fetching will only happen when UI becomes visible
  
  // Set up event handlers
  const itemListCleanup = onNuiEvent('itemList', (response: { data: ItemDefinition[] }) => {
    useAdminStore.getState().setItems(response.data || []);
  });
  
  const weaponListCleanup = onNuiEvent('weaponList', (response: { data: WeaponDefinition[] }) => {
    useAdminStore.getState().setWeapons(response.data || []);
  });
  
  const vehicleListCleanup = onNuiEvent('vehicleList', (response: { data: VehicleDefinition[] | string[] }) => {
    // Handle different response formats
    const vehicles = Array.isArray(response.data) 
      ? response.data.map(vehicle => {
          if (typeof vehicle === 'string') {
            return { model: vehicle };
          }
          return vehicle;
        })
      : [];
      
    useAdminStore.getState().setVehicles(vehicles);
  });
  
  // Return cleanup function
  return () => {
    itemListCleanup();
    weaponListCleanup();
    vehicleListCleanup();
  };
};

// Export a hook for using the admin store
export const useAdmin = () => {
  const store = useAdminStore();
  
  // Don't automatically fetch data
  // Data will be fetched when UI becomes visible
  // This prevents data loading when resource starts
  
  return store;
};
