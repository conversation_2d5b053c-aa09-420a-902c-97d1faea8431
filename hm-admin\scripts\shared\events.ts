/**
 * HM-Admin Shared Events
 * This file contains event definitions shared between client and server
 * All events follow the standardized naming convention: resourceName:target:action
 */

/**
 * Admin events
 * All events follow the standardized naming convention
 */
export const AdminEvents = {
  // Client to server events
  /**
   * Request player list from server
   * @event
   * @param {void} No payload
   */
  REQUEST_PLAYER_LIST: 'hm-admin:server:requestPlayerList',

  /**
   * Execute admin command on server
   * @event
   * @param {AdminCommandRequest} payload - The command request
   */
  EXECUTE_COMMAND: 'hm-admin:server:executeCommand',

  /**
   * Request admin level from server
   * @event
   * @param {void} No payload
   */
  REQUEST_ADMIN_LEVEL: 'hm-admin:server:requestAdminLevel',

  /**
   * Request vehicle list from server
   * @event
   * @param {void} No payload
   */
  REQUEST_VEHICLE_LIST: 'hm-admin:server:requestVehicleList',

  /**
   * Request weather types from server
   * @event
   * @param {void} No payload
   */
  REQUEST_WEATHER_TYPES: 'hm-admin:server:requestWeatherTypes',

  /**
   * Request ban list from server
   * @event
   * @param {void} No payload
   */
  REQUEST_BAN_LIST: 'hm-admin:server:requestBanList',

  /**
   * Request item list from server
   * @event
   * @param {void} No payload
   */
  REQUEST_ITEM_LIST: 'hm-admin:server:requestItemList',

  /**
   * Give item to player
   * @event
   * @param {GiveItemRequest} payload - The give item request
   */
  GIVE_ITEM: 'hm-admin:server:giveItem',

  /**
   * Teleport player to another player
   * @event
   * @param {TeleportPlayerRequest} payload - The teleport request
   */
  TELEPORT_PLAYER_TO_PLAYER: 'hm-admin:server:teleportPlayerToPlayer',

  /**
   * Teleport player to coordinates
   * @event
   * @param {TeleportCoordsRequest} payload - The teleport request
   */
  TELEPORT_PLAYER_TO_COORDS: 'hm-admin:server:teleportPlayerToCoords',

  /**
   * Kick player from server
   * @event
   * @param {KickPlayerRequest} payload - The kick request
   */
  KICK_PLAYER: 'hm-admin:server:kickPlayer',

  /**
   * Ban player from server
   * @event
   * @param {BanPlayerRequest} payload - The ban request
   */
  BAN_PLAYER: 'hm-admin:server:banPlayer',

  // Server to client events
  /**
   * Update player list on client
   * @event
   * @param {AdminPlayerInfo[]} payload - The player list
   */
  PLAYER_LIST_UPDATE: 'hm-admin:client:playerListUpdate',

  /**
   * Send command result to client
   * @event
   * @param {AdminCommandResult} payload - The command result
   */
  COMMAND_RESULT: 'hm-admin:client:commandResult',

  /**
   * Update admin level on client
   * @event
   * @param {AdminPermissionLevel} payload - The admin level
   */
  ADMIN_LEVEL_UPDATE: 'hm-admin:client:adminLevelUpdate',

  /**
   * Update vehicle list on client
   * @event
   * @param {VehicleInfo[]} payload - The vehicle list
   */
  VEHICLE_LIST_UPDATE: 'hm-admin:client:vehicleListUpdate',

  /**
   * Update weather types on client
   * @event
   * @param {string[]} payload - The weather types
   */
  WEATHER_TYPES_UPDATE: 'hm-admin:client:weatherTypesUpdate',

  /**
   * Update ban list on client
   * @event
   * @param {BanInfo[]} payload - The ban list
   */
  BAN_LIST_UPDATE: 'hm-admin:client:banListUpdate',

  /**
   * Send item list to client
   * @event
   * @param {ItemDefinition[]} payload - The item list
   */
  SEND_ITEM_LIST: 'hm-admin:client:sendItemList',

  /**
   * Send operation result to client
   * @event
   * @param {OperationResult} payload - The operation result
   */
  SEND_OPERATION_RESULT: 'hm-admin:client:sendOperationResult',

  // Shared events - updating to follow convention
  /**
   * Send admin notification
   * @event
   * @param {string} payload - The notification message
   */
  ADMIN_NOTIFICATION: 'hm-admin:shared:notification',

  /**
   * Player ragdolled event
   * @event
   * @param {number} payload - The player ID
   */
  PLAYER_RAGDOLLED: 'hm-admin:player:ragdolled',

  /**
   * Player set on fire event
   * @event
   * @param {number} payload - The player ID
   */
  PLAYER_SET_ON_FIRE: 'hm-admin:player:setOnFire',

  /**
   * Player launched event
   * @event
   * @param {number} payload - The player ID
   */
  PLAYER_LAUNCHED: 'hm-admin:player:launched',

  /**
   * Vehicle tire burst event
   * @event
   * @param {number} payload - The vehicle entity ID
   */
  VEHICLE_TIRE_BURST: 'hm-admin:vehicle:tireBurst',
} as const;

// Player info for admin menu
export interface AdminPlayerInfo {
  id: number;
  name: string;
  identifiers: {
    license: string;
    discord?: string;
    steam?: string;
    ip?: string;
    live?: string;
    xbl?: string;
    fivem?: string;
  };
  ping: number;
  health: number;
  armor: number;
  position: {
    x: number;
    y: number;
    z: number;
  };
  vehicle?: number;
}

// Command execution request
export interface AdminCommandRequest {
  command: string;
  targetId?: number;
  args?: string[];
}

// Command execution result
export interface AdminCommandResult {
  success: boolean;
  message: string;
  command: string;
}

// Ban info
export interface BanInfo {
  id: number;
  identifier: string;
  reason: string;
  bannedBy: string;
  bannedAt: string;
  expiresAt?: string;
}

// Vehicle info
export interface VehicleInfo {
  model: string;
  name: string;
  class: string;
  category?: string;
}

// Item definition
export interface ItemDefinition {
  name: string;
  label: string;
  type: string;
  category?: string;
  description?: string;
  weight?: number;
  useable?: boolean;
  unique?: boolean;
}

// Player management request interfaces
export interface GiveItemRequest {
  targetId: number;
  itemName: string;
  quantity: number;
}

export interface TeleportPlayerRequest {
  targetId: number;
  destinationPlayerId: number;
}

export interface TeleportCoordsRequest {
  targetId: number;
  x: number;
  y: number;
  z: number;
}

export interface KickPlayerRequest {
  targetId: number;
  reason: string;
}

export interface BanPlayerRequest {
  targetId: number;
  reason: string;
  duration?: number; // in hours, undefined for permanent
}

// Operation result
export interface OperationResult {
  success: boolean;
  message: string;
  data?: any;
}

// Events module initialization is handled in shared/main.ts