import React from 'react';
import { InventoryItem } from '../../../../scripts/shared/types/inventory.types';
import { generateContextMenuOptions, ContextMenuOption } from '../../config/contextMenuConfig';

interface ContextMenuProps {
  item: InventoryItem;
  quantity: number;
  position: { x: number; y: number };
  onAction: (action: string) => void;
  onClose: () => void;
  contextMenuRef: React.RefObject<HTMLDivElement | null>;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  item,
  quantity,
  position,
  onAction,
  contextMenuRef
}) => {
  const options = generateContextMenuOptions(item, quantity);

  const handleOptionClick = (option: ContextMenuOption) => {
    if (option.enabled && !option.enabled(item, quantity)) {
      return; // Option is disabled
    }
    onAction(option.action);
  };

  const renderOption = (option: ContextMenuOption, index: number) => {
    const isEnabled = !option.enabled || option.enabled(item, quantity);
    const label = typeof option.label === 'function' 
      ? option.label(item) 
      : option.label;

    return (
      <React.Fragment key={option.id}>
        <div
          className={`
            px-2 py-1 rounded cursor-pointer transition-colors duration-150 flex items-center gap-2
            ${isEnabled 
              ? `hover:bg-neutral-700 ${option.className || 'text-neutral-200'}` 
              : 'text-neutral-500 cursor-not-allowed'
            }
          `}
          onClick={() => isEnabled && handleOptionClick(option)}
        >
          {option.icon && (
            <i className={`fas ${option.icon} w-4 text-center`} />
          )}
          <span>{label}</span>
        </div>
        {option.dividerAfter && index < options.length - 1 && (
          <div className="border-t border-neutral-600 my-1" />
        )}
      </React.Fragment>
    );
  };

  // Calculate position to avoid going off-screen
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const menuWidth = 180; // Estimated menu width
  const menuHeight = options.length * 32 + 40; // Estimated menu height
  
  let adjustedX = position.x;
  let adjustedY = position.y;
  
  if (adjustedX + menuWidth > viewportWidth) {
    adjustedX = position.x - menuWidth;
  }
  
  if (adjustedY + menuHeight > viewportHeight) {
    adjustedY = position.y - menuHeight;
  }

  return (
    <div
      ref={contextMenuRef}
      className="fixed bg-neutral-800 border border-neutral-700 rounded-md shadow-lg p-2 z-50 text-sm max-w-[220px]"
      style={{ 
        top: Math.max(10, adjustedY), 
        left: Math.max(10, adjustedX) 
      }}
    >
      
      {/* Context menu options */}
      {options.map((option, index) => renderOption(option, index))}
      
      {/* If no options available */}
      {options.length === 0 && (
        <div className="text-neutral-500 px-2 py-1 text-center italic">
          No actions available
        </div>
      )}
    </div>
  );
};

export default ContextMenu;
