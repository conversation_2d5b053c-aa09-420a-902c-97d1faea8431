import React from 'react';

interface ContactsBottomNavProps {
  activeTab: string;
  switchTab: (tab: string) => void;
  className?: string;
}

const ContactsBottomNav: React.FC<ContactsBottomNavProps> = ({ activeTab, switchTab, className = '' }) => {
  // Get colors from the app configuration
  const colors = {
    text: {
      primary: 'text-green-500',
      tertiary: 'text-white/60'
    },
    border: {
      primary: 'border-white/10'
    }
  };

  return (
    <div
      className={`flex justify-around items-center py-3 px-1 bg-[#0a0f1a] border-t ${colors.border.primary} ${className}`}
    >
      <button
        onClick={() => switchTab('dialer')}
        className={`flex flex-col items-center justify-center w-full cursor-pointer ${
          activeTab === 'dialer' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-phone text-base mb-1"></i>
        <span className="text-xs">Dialer</span>
        {activeTab === 'dialer' && (
          <div className={`h-0.5 w-8 ${colors.text.primary} mt-1.5 rounded-full`}></div>
        )}
      </button>
      <button
        onClick={() => switchTab('calls')}
        className={`flex flex-col items-center justify-center w-full cursor-pointer ${
          activeTab === 'calls' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-history text-base mb-1"></i>
        <span className="text-xs">Recents</span>
        {activeTab === 'calls' && (
          <div className={`h-0.5 w-8 ${colors.text.primary} mt-1.5 rounded-full`}></div>
        )}
      </button>
      <button
        onClick={() => switchTab('contacts')}
        className={`flex flex-col items-center justify-center w-full cursor-pointer ${
          activeTab === 'contacts' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-address-book text-base mb-1"></i>
        <span className="text-xs">Contacts</span>
        {activeTab === 'contacts' && (
          <div className={`h-0.5 w-8 ${colors.text.primary} mt-1.5 rounded-full`}></div>
        )}
      </button>
      <button
        onClick={() => switchTab('favorites')}
        className={`flex flex-col items-center justify-center w-full cursor-pointer ${
          activeTab === 'favorites' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-star text-base mb-1"></i>
        <span className="text-xs">Favorites</span>
        {activeTab === 'favorites' && (
          <div className={`h-0.5 w-8 ${colors.text.primary} mt-1.5 rounded-full`}></div>
        )}
      </button>
      <button
        onClick={() => switchTab('profile')}
        className={`flex flex-col items-center justify-center w-full cursor-pointer ${
          activeTab === 'profile' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-user text-base mb-1"></i>
        <span className="text-xs">Profile</span>
        {activeTab === 'profile' && (
          <div className={`h-0.5 w-8 ${colors.text.primary} mt-1.5 rounded-full`}></div>
        )}
      </button>
    </div>
  );
};

export default ContactsBottomNav;
