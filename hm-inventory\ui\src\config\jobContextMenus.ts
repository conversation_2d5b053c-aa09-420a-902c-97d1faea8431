import { ItemType } from '../../../scripts/shared/types/items.types';
import { ContextMenuConfig } from './contextMenuConfig';

/**
 * Example custom context menu configurations for different scenarios
 */

/**
 * Role-based context menu for Police Officer
 */
export const POLICE_CONTEXT_MENU_CONFIG: Partial<ContextMenuConfig> = {
  itemSpecific: {
    'handcuffs': [
      {
        id: 'arrest',
        label: 'Arrest Suspect',
        action: 'arrestSuspect',
        className: 'text-blue-400'
      },
      {
        id: 'uncuff',
        label: 'Remove Handcuffs',
        action: 'removeHandcuffs',
        className: 'text-green-400'
      }
    ],
    'evidence_bag': [
      {
        id: 'collect_evidence',
        label: 'Collect Evidence',
        action: 'collectEvidence',
        className: 'text-yellow-400'
      }
    ],
    'police_radio': [
      {
        id: 'call_backup',
        label: 'Call Backup',
        action: 'callBackup',
        className: 'text-red-400'
      },
      {
        id: 'report_status',
        label: 'Report Status',
        action: 'reportStatus',
        className: 'text-blue-400'
      }
    ]
  },

  conditional: [
    // Weapons have impound option for police
    {
      condition: (item) => item.type === ItemType.WEAPON,
      options: [
        {
          id: 'impound',
          label: 'Impound Weapon',
          action: 'impoundWeapon',
          className: 'text-orange-400'
        }
      ]
    }
  ]
};

/**
 * Medic/Doctor context menu configuration
 */
export const MEDIC_CONTEXT_MENU_CONFIG: Partial<ContextMenuConfig> = {
  itemSpecific: {
    'defibrillator': [
      {
        id: 'revive',
        label: 'Revive Patient',
        action: 'revivePatient',
        className: 'text-red-400'
      }
    ],
    'medical_kit': [
      {
        id: 'treat_wounds',
        label: 'Treat Wounds',
        action: 'treatWounds',
        className: 'text-green-400'
      },
      {
        id: 'stabilize',
        label: 'Stabilize Patient',
        action: 'stabilizePatient',
        className: 'text-blue-400'
      }
    ],
    'morphine': [
      {
        id: 'inject',
        label: 'Inject Morphine',
        action: 'injectMorphine',
        className: 'text-purple-400'
      }
    ]
  },

  conditional: [
    {
      condition: (item) => item.type === ItemType.CONSUMABLE && item.metadata?.medical === true,
      options: [
        {
          id: 'administer',
          label: 'Administer to Patient',
          action: 'administerToPatient',
          className: 'text-green-400'
        }
      ]
    }
  ]
};

/**
 * Mechanic context menu configuration
 */
export const MECHANIC_CONTEXT_MENU_CONFIG: Partial<ContextMenuConfig> = {
  itemSpecific: {
    'wrench': [
      {
        id: 'repair_vehicle',
        label: 'Repair Vehicle',
        action: 'repairVehicle',
        className: 'text-orange-400'
      }
    ],
    'tire': [
      {
        id: 'change_tire',
        label: 'Change Tire',
        action: 'changeTire',
        className: 'text-gray-400'
      }
    ],
    'car_engine': [
      {
        id: 'install_engine',
        label: 'Install Engine',
        action: 'installEngine',
        className: 'text-red-400'
      },
      {
        id: 'tune_engine',
        label: 'Tune Engine',
        action: 'tuneEngine',
        className: 'text-blue-400'
      }
    ],
    'lockpick': [
      {
        id: 'unlock_vehicle',
        label: 'Unlock Vehicle',
        action: 'unlockVehicle',
        className: 'text-yellow-400'
      }
    ]
  },

  conditional: [
    // Vehicle parts can be installed
    {
      condition: (item) => item.metadata?.vehiclePart === true,
      options: [
        {
          id: 'install_part',
          label: 'Install Part',
          action: 'installVehiclePart',
          className: 'text-green-400'
        }
      ]
    }
  ]
};

/**
 * Criminal/Gang member context menu configuration
 */
export const CRIMINAL_CONTEXT_MENU_CONFIG: Partial<ContextMenuConfig> = {
  itemSpecific: {
    'lockpick': [
      {
        id: 'pick_lock',
        label: 'Pick Lock',
        action: 'pickLock',
        className: 'text-red-400'
      }
    ],
    'drugs': [
      {
        id: 'sell_drugs',
        label: 'Sell Drugs',
        action: 'sellDrugs',
        className: 'text-green-400'
      },
      {
        id: 'package_drugs',
        label: 'Package for Sale',
        action: 'packageDrugs',
        className: 'text-purple-400'
      }
    ],
    'crowbar': [
      {
        id: 'break_in',
        label: 'Break In',
        action: 'breakIn',
        className: 'text-red-400'
      }
    ]
  },

  conditional: [
    // Stolen items have fence option
    {
      condition: (item) => item.metadata?.stolen === true,
      options: [
        {
          id: 'fence_item',
          label: 'Fence Item',
          action: 'fenceItem',
          className: 'text-yellow-400'
        }
      ]
    }
  ]
};

/**
 * Chef/Restaurant context menu configuration
 */
export const CHEF_CONTEXT_MENU_CONFIG: Partial<ContextMenuConfig> = {
  itemSpecific: {
    'chef_knife': [
      {
        id: 'chop',
        label: 'Chop Ingredients',
        action: 'chopIngredients',
        className: 'text-blue-400'
      }
    ],
    'raw_meat': [
      {
        id: 'grill',
        label: 'Grill Meat',
        action: 'grillMeat',
        className: 'text-red-400'
      }
    ]
  },

  conditional: [
    {
      condition: (item) => item.type === ItemType.CONSUMABLE && item.metadata?.raw === true,
      options: [
        {
          id: 'cook',
          label: 'Cook',
          action: 'cookFood',
          className: 'text-orange-400'
        }
      ]
    },
    {
      condition: (item) => item.type === ItemType.CONSUMABLE && item.metadata?.cooked === true,
      options: [
        {
          id: 'serve',
          label: 'Serve to Customer',
          action: 'serveFood',
          className: 'text-green-400'
        }
      ]
    }
  ]
};

/**
 * Get context menu config based on player's job/role
 */
export function getJobSpecificContextMenu(jobName: string): Partial<ContextMenuConfig> | undefined {
  switch (jobName.toLowerCase()) {
    case 'police':
    case 'cop':
      return POLICE_CONTEXT_MENU_CONFIG;
    
    case 'medic':
    case 'doctor':
    case 'ems':
      return MEDIC_CONTEXT_MENU_CONFIG;
    
    case 'mechanic':
      return MECHANIC_CONTEXT_MENU_CONFIG;
    
    case 'criminal':
    case 'gang':
      return CRIMINAL_CONTEXT_MENU_CONFIG;
    
    case 'chef':
    case 'cook':
      return CHEF_CONTEXT_MENU_CONFIG;
    
    default:
      return undefined;
  }
}
