/**
 * Custom hook for post navigation
 */

import { useCallback } from 'react';
import { useNavigation } from '../../../navigation/hooks';

interface UsePostNavigationReturn {
  handlePostDetailView: (isDetail: boolean, postId?: number) => void;
}

export const usePostNavigation = (): UsePostNavigationReturn => {
  const { openView } = useNavigation();

  /**
   * Handle navigation to post detail view
   */
  const handlePostDetailView = useCallback(
    (isDetail: boolean, postId?: number) => {
      if (isDetail && postId) {
        openView('post', { postId }, { replace: true });
      } else {
        openView('main', {}, { replace: true });
      }
    },
    [openView]
  );

  return {
    handlePostDetailView
  };
};

export default usePostNavigation;
