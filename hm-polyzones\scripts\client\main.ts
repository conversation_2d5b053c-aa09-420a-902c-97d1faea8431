import { PolyZoneManager } from "./polyzone-manager";


const polyzoneManager = new PolyZoneManager();

global.exports('createBoxZone', polyzoneManager.createBoxZone.bind(polyzoneManager));
global.exports('createCircleZone', polyzoneManager.createCircleZone.bind(polyzoneManager));
global.exports('createPolygonZone', polyzoneManager.createPolygonZone.bind(polyzoneManager));
global.exports('toggleDebug', polyzoneManager.toggleDebug.bind(polyzoneManager));

RegisterCommand("hm-polyzones:debug", () => {
    polyzoneManager.toggleDebug();
}, false); // false: allow anyone to use it, or set to true to restrict

console.log('[hm-polyzones] Client module loaded. Use command /hm-polyzones:debug to toggle debug view.');
