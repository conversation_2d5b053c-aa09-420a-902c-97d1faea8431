import React, { useState } from 'react';

const CreatePost: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const [caption, setCaption] = useState('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [location, setLocation] = useState('');

  const handlePost = () => {
    if (!selectedImage) return;

    // createPost({
    //   userId: 1, // Should come from currentProfile
    //   imageUrl: selectedImage,
    //   caption,
    //   location,
    //   taggedUsers,
    //   saved: false
    // });

    onClose();
  };

  return (
    <div className="h-full bg-gray-900 px-4">
      <div className="pt-4">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-white/10">
          <button onClick={onClose} className="text-white/80 hover:text-white">
            <i className="fas fa-arrow-left"></i>
          </button>
          <span className="font-semibold text-white">New Post</span>
          <button
            onClick={handlePost}
            disabled={!selectedImage}
            className="text-blue-500 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Post
          </button>
        </div>

        {/* Image Selection */}
        <div className="flex-1 bg-black/20 flex items-center justify-center p-4">
          {selectedImage ? (
            <div className="relative w-full h-full">
              <img
                src={selectedImage}
                className="w-full h-full object-contain rounded-lg"
                alt="selected"
              />
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-3 right-3 bg-black/50 rounded-full p-2.5 text-white/80 hover:text-white"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          ) : (
            <div className="text-center space-y-3">
              <button className="bg-white/10 rounded-full p-5 mb-2 hover:bg-white/20 transition-colors">
                <i className="fas fa-camera text-2xl text-white/60"></i>
              </button>
              <div className="text-white/60 text-sm">Click to select an image</div>
            </div>
          )}
        </div>

        {/* Post Details */}
        <div className="p-5 space-y-5 bg-black/20">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 rounded-full bg-white/10 flex-shrink-0"></div>
            <textarea
              value={caption}
              onChange={e => setCaption(e.target.value)}
              placeholder="Write a caption..."
              className="flex-1 bg-transparent text-white placeholder-white/60 resize-none focus:outline-none text-sm min-h-[60px]"
              rows={3}
            />
          </div>

          <div className="flex items-center gap-4 border-t border-white/10 pt-4">
            <i className="fas fa-map-marker-alt text-white/60 w-8 text-center"></i>
            <input
              type="text"
              value={location}
              onChange={e => setLocation(e.target.value)}
              placeholder="Add location"
              className="flex-1 bg-transparent text-white placeholder-white/60 focus:outline-none text-sm"
            />
          </div>

          <div className="flex items-center gap-4 border-t border-white/10 pt-4">
            <i className="fas fa-user-tag text-white/60 w-8 text-center"></i>
            <button className="text-blue-500 text-sm hover:text-blue-400">Tag people</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePost;
