/**
 * HM-Phone Shared Events
 * This file contains event definitions shared between client and server
 * All events follow the standardized naming convention: resourceName:target:action
 */

/**
 * Phone events
 * All events follow the standardized naming convention
 */
export const PhoneEvents = {
    // Client to server events
    /**
     * Request phone data from server
     * @event
     * @param {void} No payload
     */
    REQUEST_PHONE_DATA: 'hm-phone:server:requestPhoneData',

    /**
     * Create a new conversation
     * @event
     * @param {CreateConversationPayload} payload - The conversation data
     */
    CREATE_CONVERSATION: 'hm-phone:server:createConversation',

    /**
     * Send a message
     * @event
     * @param {SendMessagePayload} payload - The message data
     */
    SEND_MESSAGE: 'hm-phone:server:sendMessage',

    /**
     * Create a new contact
     * @event
     * @param {CreateContactPayload} payload - The contact data
     */
    CREATE_CONTACT: 'hm-phone:server:createContact',

    /**
     * Update a contact
     * @event
     * @param {UpdateContactPayload} payload - The contact data
     */
    UPDATE_CONTACT: 'hm-phone:server:updateContact',

    /**
     * Delete a contact
     * @event
     * @param {DeleteContactPayload} payload - The contact ID
     */
    DELETE_CONTACT: 'hm-phone:server:deleteContact',

    /**
     * Create a new ad
     * @event
     * @param {CreateAdPayload} payload - The ad data
     */
    CREATE_AD: 'hm-phone:server:createAd',

    /**
     * Save a photo
     * @event
     * @param {SavePhotoPayload} payload - The photo data
     */
    SAVE_PHOTO: 'hm-phone:server:savePhoto',

    /**
     * Request service
     * @event
     * @param {RequestServicePayload} payload - The service request data
     */
    REQUEST_SERVICE: 'hm-phone:server:requestService',

    // Server to client events
    /**
     * Send phone data to client
     * @event
     * @param {PhoneDataPayload} payload - The phone data
     */
    PHONE_DATA: 'hm-phone:client:phoneData',

    /**
     * Send contacts data to client
     * @event
     * @param {ContactsDataPayload} payload - The contacts data
     */
    CONTACTS_DATA: 'hm-phone:client:contactsData',

    /**
     * Send messages data to client
     * @event
     * @param {MessagesDataPayload} payload - The messages data
     */
    MESSAGES_DATA: 'hm-phone:client:messagesData',

    /**
     * Send new message to client
     * @event
     * @param {NewMessagePayload} payload - The new message data
     */
    NEW_MESSAGE: 'hm-phone:client:newMessage',

    /**
     * Send notification to client
     * @event
     * @param {NotificationPayload} payload - The notification data
     */
    NOTIFICATION: 'hm-phone:client:notification',

    /**
     * Send photo saved confirmation to client
     * @event
     * @param {PhotoSavedPayload} payload - The photo data
     */
    PHOTO_SAVED: 'hm-phone:client:photoSaved',

    /**
     * Send photo save error to client
     * @event
     * @param {string} payload - The error message
     */
    PHOTO_SAVE_ERROR: 'hm-phone:client:photoSaveError',

    /**
     * Send service request response to client
     * @event
     * @param {ServiceResponsePayload} payload - The service response data
     */
    SERVICE_RESPONSE: 'hm-phone:client:serviceResponse',

    // NUI events
    /**
     * NUI is ready
     * @event
     * @param {void} No payload
     */
    NUI_READY: 'hm-phone:nui:ready',

    /**
     * Set phone state
     * @event
     * @param {SetPhoneStatePayload} payload - The phone state data
     */
    NUI_SET_STATE: 'hm-phone:nui:setState',

    /**
     * Update contacts in NUI
     * @event
     * @param {ContactsDataPayload} payload - The contacts data
     */
    NUI_UPDATE_CONTACTS: 'hm-phone:nui:updateContacts',

    /**
     * Update messages in NUI
     * @event
     * @param {MessagesDataPayload} payload - The messages data
     */
    NUI_UPDATE_MESSAGES: 'hm-phone:nui:updateMessages',

    /**
     * Update photos in NUI
     * @event
     * @param {PhotosDataPayload} payload - The photos data
     */
    NUI_UPDATE_PHOTOS: 'hm-phone:nui:updatePhotos',

    /**
     * Show notification in NUI
     * @event
     * @param {NotificationPayload} payload - The notification data
     */
    NUI_SHOW_NOTIFICATION: 'hm-phone:nui:showNotification'
} as const;

// Event payload types
export interface PhoneDataPayload {
    phoneNumber: string;
    contacts: any[];
    messages: any[];
    photos: any[];
    settings: any;
}

export interface ContactsDataPayload {
    contacts: any[];
}

export interface MessagesDataPayload {
    conversations: any[];
    messages: any[];
}

export interface NewMessagePayload {
    conversationId: number;
    message: any;
}

export interface CreateConversationPayload {
    name?: string;
    type: 'direct' | 'group';
    members: Record<string, any>;
}

export interface SendMessagePayload {
    conversationId: number;
    content: string;
    attachments?: any[];
}

export interface CreateContactPayload {
    name: string;
    phoneNumber: string;
    avatar?: string;
}

export interface UpdateContactPayload {
    id: number;
    name?: string;
    phoneNumber?: string;
    avatar?: string;
}

export interface DeleteContactPayload {
    id: number;
}

export interface CreateAdPayload {
    title: string;
    content: string;
    category: string;
    phoneNumber: string;
    image?: string;
}

export interface SavePhotoPayload {
    imageData: string;
    metadata?: Record<string, any>;
}

export interface PhotoSavedPayload {
    id: number;
    url: string;
    timestamp: number;
    metadata?: Record<string, any>;
}

export interface RequestServicePayload {
    serviceId: string;
    message: string;
    location?: {
        x: number;
        y: number;
        z: number;
    };
}

export interface ServiceResponsePayload {
    serviceId: string;
    success: boolean;
    message: string;
}

export interface NotificationPayload {
    title: string;
    message: string;
    type: 'success' | 'error' | 'info' | 'warning';
    duration?: number;
    sound?: boolean;
}

export interface SetPhoneStatePayload {
    state: number; // 0 = closed, 1 = peek, 2 = open
}

export interface PhotosDataPayload {
    photos: any[];
}
