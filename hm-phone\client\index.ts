/**
 * Phone Client - Main Entry Point
 *
 * This file serves as the entry point for the client-side phone functionality.
 * It imports and initializes the core phone functionality when the resource starts.
 */

/// <reference types="@citizenfx/client" />

// Import framework detection and globals
import { initializeHandlers } from './handlers';
import config from '@shared/config';

// Import services
import { initializeCameraControlsService } from './services/cameraControls';

// Import core modules at the module level for better tree shaking
import { initializePhone, cleanupPhone, togglePhone } from './phone';

// Core events will be used directly as strings since CoreEvents doesn't exist yet

// Phone initialization guard
let phoneInitDone = false;
/**
 * Initialize phone handlers and UI, only once after character is loaded
 */
async function initPhone(): Promise<void> {
    if (phoneInitDone) return;
    phoneInitDone = true;
    console.log('[Phone] Initializing phone post-character');
    // Initialize event handlers and core phone UI
    initializeHandlers();
    initializePhone();
    initializeCameraControlsService();
}

/**
 * Initialize player data globals
 * @returns Promise that resolves to true if initialization was successful, false otherwise
 */
async function initializePlayerDataGlobals(): Promise<boolean> {
    try {
        const { updatePlayerDataGlobals } = await import('@shared/framework/globals');
        updatePlayerDataGlobals();

        // Set player data globals
        const playerData = global.getPlayerData();
        if (playerData) {
            const { setPlayerPhoneNumber, setPlayerIdentifier, setPlayerStateId } = await import('./services/player');
            setPlayerPhoneNumber(playerData.phoneNumber || '');
            setPlayerIdentifier(playerData.identifier || '');
            setPlayerStateId(playerData.stateid || '');
            return true;
        }
        return false;
    } catch (error) {
        console.error('[Phone] Error initializing player data:', error);
        return false;
    }
}

// Register phone command at the module level
RegisterCommand(
    'phone',
    async () => {
        await togglePhone();
    },
    false
);

// Register resource start/stop events at the module level
on('onClientResourceStart', async (resourceName: string) => {
    if (GetCurrentResourceName() !== resourceName) return;
    
    console.log(`[Phone] Resource ${resourceName} starting...`);
    console.log(`[Phone] Using hm-core framework directly`);

    // Phone UI will initialize once character is loaded via core event
    console.log('[Phone] Waiting for character to load before initializing phone...');
});

// Listen for framework player loaded event to initialize player data
// This ensures we only load player data when the framework is fully ready
onNet('QBCore:Client:OnPlayerLoaded', async () => {
    const success = await initializePlayerDataGlobals();
    if (success) {
        // Register app handlers but don't load data yet
        const { initializeAppHandlers } = await import('./handlers');
        initializeAppHandlers();
        // Load settings data now that player is loaded
        const { loadSettingsData } = await import('./apps/settings');
        loadSettingsData();
        // Now initialize phone UI and handlers
        await initPhone();
    }
});

// Listen for QBox framework player loaded events
// QBox might use different event names depending on the version
// Try both the standard QBX event and the legacy QB event for QBox
onNet('QBX:Client:OnPlayerLoaded', async () => {
    const success = await initializePlayerDataGlobals();
    if (success) {
        const { initializeAppHandlers } = await import('./handlers');
        initializeAppHandlers();

        const { loadSettingsData } = await import('./apps/settings');
        loadSettingsData();
        await initPhone();
    }
    console.log('[Phone] Player data initialized on QBox player load (QBX event)');
});

// Alternative QBox event name (some versions might use this)
onNet('QBox:Client:OnPlayerLoaded', async () => {
    const success = await initializePlayerDataGlobals();
    if (success) {
        const { initializeAppHandlers } = await import('./handlers');
        initializeAppHandlers();

        const { loadSettingsData } = await import('./apps/settings');
        loadSettingsData();
        await initPhone();
    }
    console.log('[Phone] Player data initialized on QBox player load');
});

// // Listen for HM Core client ready event
// onNet('hm-core:client:ready', async () => {
//     console.log('[Phone] HM Core client ready, initializing player data');
//     const success = await initializePlayerDataGlobals();
//     if (success) {
//         const { initializeAppHandlers } = await import('./handlers');
//         initializeAppHandlers();

//         const { loadSettingsData } = await import('./apps/settings');
//         loadSettingsData();
//         await initPhone();
//     }
//     console.log('[Phone] Player data initialized on HM Core client ready');
// });

// Listen for HM Core player loaded event
onNet('hm-core:playerLoaded', async () => {
    const success = await initializePlayerDataGlobals();
    if (success) {
        const { initializeAppHandlers } = await import('./handlers');
        initializeAppHandlers();

        const { loadSettingsData } = await import('./apps/settings');
        loadSettingsData();
        await initPhone();
    }
    console.log('[Phone] Player data initialized on HM Core player load');
});

// Listen for HM Core player loaded event (backward compatibility)
// Remove this block in future major update when breaking changes are acceptable
onNet('hm-core:player:loaded', async () => {
    const success = await initializePlayerDataGlobals();
    if (success) {
        const { initializeAppHandlers } = await import('./handlers');
        initializeAppHandlers();

        const { loadSettingsData } = await import('./apps/settings');
        loadSettingsData();
        await initPhone();
    }
    console.log('[Phone] Player data initialized on HM Core player load');
});

on('onClientResourceStop', async (resourceName: string) => {
    if (GetCurrentResourceName() !== resourceName) return;
    const { cleanupCameraControlsResources } = await import('./services/cameraControls');
    cleanupCameraControlsResources();

    // Clean up phone
    cleanupPhone();
});
