/**
 * Client Event Receiver
 *
 * This module receives events from the FiveM client and dispatches them to registered handlers.
 *
 * How it works:
 * 1. The FiveM client sends events using SendNUIMessage({app, type, data})
 * 2. This module listens for those events and dispatches them to registered handlers
 * 3. App-specific handlers update their stores with the received data
 *
 * Example flow:
 * Client Lua → SendNUIMessage → window.addEventListener → clientEventReceiver → App Store
 */

// Type for event handlers
type EventHandler = (data: unknown) => void;

// Store for event handlers
const handlers: Record<string, Record<string, EventHandler>> = {};

/**
 * Register a handler for a specific app and event type
 *
 * @param app The app name (e.g., 'contacts')
 * @param eventType The event type (e.g., 'contactsData')
 * @param handler The function to call when an event is received
 */
export function registerEventHandler(app: string, eventType: string, handler: EventHandler) {
  // console.log(`[ClientEventReceiver] Registering handler for ${app}:${eventType}`);
  if (!handlers[app]) {
    handlers[app] = {};
  }
  handlers[app][eventType] = handler;
}

/**
 * Unregister a handler for a specific app and event type
 *
 * @param app The app name
 * @param eventType The event type
 */
export function unregisterEventHandler(app: string, eventType: string) {
  if (handlers[app] && handlers[app][eventType]) {
    delete handlers[app][eventType];
    // console.log(`[ClientEventReceiver] Unregistered handler for ${app}:${eventType}`);
  }
}

/**
 * Initialize the event receiver
 * Should be called once at application startup
 */
export function initializeEventReceiver() {
  console.log('[ClientEventReceiver] Initializing event receiver');

  window.addEventListener('message', event => {
    // Skip if event.data is not an object
    if (!event.data || typeof event.data !== 'object') return;

    const { app, type, data } = event.data;

    // Handle using the app+type format
    if (app && type && typeof app === 'string' && typeof type === 'string') {
      if (handlers[app] && handlers[app][type]) {
        console.log(`[ClientEventReceiver] Handling event: ${app}:${type}`);
        handlers[app][type](data);
        return;
      } else {
        console.log(`[ClientEventReceiver] No handler registered for ${app}:${type}`);
      }
    }
  });
}
