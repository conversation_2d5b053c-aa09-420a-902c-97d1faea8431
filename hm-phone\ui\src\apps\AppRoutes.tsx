import React, { lazy, Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';
import { NavigationProvider } from '../navigation/provider';

// Separate the imports to enable better HMR
const HomeScreen = lazy(() => import('../common/components/HomeScreen'));
const PhoneLayoutComponent = lazy(() => import('../layouts/PhoneLayoutComponent'));
const LoadingSpinner = lazy(() => import('../common/components/LoadingSpinner'));

// Lazy load all apps with explicit webpackChunkName comments
const Contacts = lazy(() => import(/* webpackChunkName: "contacts" */ './contacts/Contacts'));
const Messages = lazy(() => import(/* webpackChunkName: "messages" */ './messages/Messages'));
const Settings = lazy(() => import('./settings/Settings'));
const Photos = lazy(() => import('./photos/Photos'));
const AppStore = lazy(() => import('./appStore/AppStore'));
const YellowPages = lazy(() => import('./yellowPages/YellowPages'));
const DarkMarket = lazy(() => import('./darkMarket/DarkMarket'));
const Garage = lazy(() => import('./garage/Garage'));
const LifeSnap = lazy(() => import('./lifeSnap/LifeSnap'));
const BankingApp = lazy(() => import('./banking/BankingApp'));
const Music = lazy(() => import('./music/Music'));
const LoveLink = lazy(() => import('./lovelink/LoveLink'));
const Notes = lazy(() => import('./notes/Notes'));
const JobCenter = lazy(() => import('./jobCenter/JobCenter'));
const Camera = lazy(() => import('./camera/Camera'));
const Services = lazy(() => import('./services/Services'));

const AppContent: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`AppContent: Current path: ${location.pathname}`);
    }
  }, [location.pathname]);

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route index element={<HomeScreen />} />
        <Route path="contacts/*" element={<Contacts />} />
        <Route path="messages/*" element={<Messages />} />
        <Route path="settings/*" element={<Settings />} />
        <Route path="photos/*" element={<Photos />} />
        <Route path="app-store/*" element={<AppStore />} />
        <Route path="yellow-pages/*" element={<YellowPages />} />
        <Route path="dark-market/*" element={<DarkMarket />} />
        <Route path="garage/*" element={<Garage />} />
        <Route path="life-snap/*" element={<LifeSnap />} />
        <Route path="banking/*" element={<BankingApp />} />
        <Route path="music/*" element={<Music />} />
        <Route path="love-link/*" element={<LoveLink />} />
        <Route path="notes/*" element={<Notes />} />
        <Route path="job-center/*" element={<JobCenter />} />
        <Route path="camera/*" element={<Camera />} />
        <Route path="services/*" element={<Services />} />
        <Route path="*" element={<HomeScreen />} />
      </Routes>
    </Suspense>
  );
};

const AppRoutes: React.FC = () => {
  return (
    <Router>
      <NavigationProvider>
        <Routes>
          <Route path="*" element={<PhoneLayoutComponent />}>
            <Route path="*" element={<AppContent />} />
          </Route>
        </Routes>
      </NavigationProvider>
    </Router>
  );
};

export default AppRoutes;
