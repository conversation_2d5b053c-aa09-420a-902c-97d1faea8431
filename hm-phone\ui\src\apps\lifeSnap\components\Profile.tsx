import React, { useState } from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import StoryRing from './StoryRing';
import StoryViewer from './StoryViewer';
import { getTimeAgo } from '../../../utils/timeUtils';

const Profile: React.FC = () => {
  const { currentProfile, posts, stories } = useLifeSnapStore();
  const [selectedStoryId, setSelectedStoryId] = useState<number | null>(null);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedBio, setEditedBio] = useState('');

  if (!currentProfile) return null;

  const userStories = stories
    .filter(story => story.userId === currentProfile.id && new Date(story.expiresAt) > new Date())
    .sort((a, b) => {
      const aTime =
        a.timestamp instanceof Date
          ? a.timestamp.getTime()
          : typeof a.timestamp === 'number'
          ? a.timestamp
          : new Date(a.timestamp).getTime();
      const bTime =
        b.timestamp instanceof Date
          ? b.timestamp.getTime()
          : typeof b.timestamp === 'number'
          ? b.timestamp
          : new Date(b.timestamp).getTime();
      return aTime - bTime;
    });

  const handleStoryClick = () => {
    if (userStories.length > 0) {
      const storyId = userStories[0].id;
      setSelectedStoryId(storyId);
      // actions.viewStory(storyId);
      console.log('Viewing story:', storyId); // Add debug log
    } else {
      console.log('No stories available'); // Add debug log
    }
  };

  const handleStoryNavigation = (direction: 'next' | 'previous') => {
    if (!selectedStoryId) return;

    const currentIndex = userStories.findIndex(s => s.id === selectedStoryId);

    if (direction === 'next') {
      if (currentIndex < userStories.length - 1) {
        const nextStoryId = userStories[currentIndex + 1].id;
        setSelectedStoryId(nextStoryId);
        // actions.viewStory(nextStoryId);
      } else {
        setSelectedStoryId(null);
      }
    } else {
      if (currentIndex > 0) {
        const prevStoryId = userStories[currentIndex - 1].id;
        setSelectedStoryId(prevStoryId);
        // actions.viewStory(prevStoryId);
      }
    }
  };

  const userPosts = posts.filter(post => post.userId === currentProfile.id);

  // Initialize editedBio when entering edit mode
  const handleEditProfile = () => {
    setEditedBio(currentProfile.bio);
    setIsEditingProfile(true);
  };

  // Save profile changes
  const handleSaveProfile = () => {
    // actions.updateProfile({
    //   bio: editedBio
    // });
    setIsEditingProfile(false);
  };

  return (
    <>
      <div className="flex flex-col text-white h-full overflow-y-auto">
        {/* Profile Header */}
        <div className="p-4">
          <div className="flex items-center gap-4">
            <StoryRing userId={currentProfile.id} size="lg" onClick={handleStoryClick}>
              <img
                src={currentProfile.profilePicture}
                alt={currentProfile.username}
                className="w-20 h-20 rounded-full object-cover border-2 border-black"
              />
            </StoryRing>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold">{currentProfile.fullName}</h2>
                {currentProfile.isVerified && (
                  <i className="fas fa-check-circle text-blue-500 text-sm"></i>
                )}
              </div>
              <p className="text-white/60 text-sm">@{currentProfile.username}</p>
              {currentProfile.businessCategory && (
                <span className="text-sm text-white/80">{currentProfile.businessCategory}</span>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="flex justify-around mt-4">
            <div className="text-center">
              <div className="font-semibold">{userPosts.length}</div>
              <div className="text-sm text-white/60">Posts</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{currentProfile.followers.length}</div>
              <div className="text-sm text-white/60">Followers</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{currentProfile.following.length}</div>
              <div className="text-sm text-white/60">Following</div>
            </div>
          </div>

          {/* Bio */}
          <div className="mt-4">
            {isEditingProfile ? (
              <div className="flex flex-col gap-2">
                <textarea
                  value={editedBio}
                  onChange={e => setEditedBio(e.target.value)}
                  className="w-full p-2 bg-[#1a1f2c] border border-white/10 rounded text-sm text-white"
                  rows={4}
                  maxLength={150}
                />
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/60">{editedBio.length}/150</span>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setIsEditingProfile(false)}
                      className="px-3 py-1 bg-white/10 rounded text-sm"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveProfile}
                      className="px-3 py-1 bg-blue-500 rounded text-sm"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <p className="whitespace-pre-line text-sm">{currentProfile.bio}</p>
                <button
                  onClick={handleEditProfile}
                  className="self-start px-3 py-1 bg-white/10 rounded text-xs mt-2"
                >
                  Edit Profile
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Posts Header */}
        <div className="flex border-t border-white/10 mt-2">
          <div className="flex-1 py-2 flex justify-center items-center border-b-2 border-white">
            <i className="fas fa-th text-sm"></i>
          </div>
        </div>

        {/* Posts Grid */}
        <div className="grid grid-cols-3 gap-1">
          {userPosts.length > 0 ? (
            userPosts.map(post => {
              const postDate =
                post.timestamp instanceof Date
                  ? post.timestamp.getTime()
                  : typeof post.timestamp === 'number'
                  ? post.timestamp
                  : new Date(post.timestamp).getTime();

              return (
                <div key={post.id} className="aspect-square relative group">
                  <img
                    src={post.imageUrl}
                    alt={post.caption}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-4">
                    <div className="flex items-center gap-1">
                      <i className="fas fa-heart text-white"></i>
                      <span className="text-white text-sm">{post.likes.length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <i className="fas fa-comment text-white"></i>
                      <span className="text-white text-sm">{post.comments.length}</span>
                    </div>
                  </div>
                  <div className="absolute bottom-1 right-1 text-[10px] text-white/70 bg-black/30 px-1 rounded">
                    {getTimeAgo(postDate)}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-3 py-10 flex flex-col items-center justify-center text-white/60">
              <i className="fas fa-camera text-3xl mb-2"></i>
              <p className="text-sm">No posts yet</p>
            </div>
          )}
        </div>
      </div>

      {selectedStoryId !== null && (
        <StoryViewer
          storyId={selectedStoryId}
          onClose={() => setSelectedStoryId(null)}
          onNext={() => handleStoryNavigation('next')}
          onPrevious={() => handleStoryNavigation('previous')}
        />
      )}
    </>
  );
};

export default Profile;
