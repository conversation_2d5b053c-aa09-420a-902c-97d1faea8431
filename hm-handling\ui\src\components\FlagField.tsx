import React, { useState, useEffect } from 'react';
import { BaseHandlingData, ModelFlags, HandlingFlags, DamageFlags } from '../../../scripts/shared/types';

interface FlagFieldProps {
  field: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags';
  currentValue: string;
  stockValue: string;
  onValueChange: (field: keyof BaseHandlingData, value: string) => void;
  onReset: (field: keyof BaseHandlingData) => void;
}

type FlagEnumType = typeof ModelFlags | typeof HandlingFlags | typeof DamageFlags;

const FlagField: React.FC<FlagFieldProps> = ({
  field,
  currentValue,
  stockValue,
  onValueChange,
  onReset
}) => {
  const [selectedFlags, setSelectedFlags] = useState<number[]>([]);

  // Get the appropriate enum based on field type
  const getFlagEnum = (): FlagEnumType => {
    switch (field) {
      case 'strModelFlags':
        return ModelFlags;
      case 'strHandlingFlags':
        return HandlingFlags;
      case 'strDamageFlags':
        return DamageFlags;
      default:
        return ModelFlags;
    }
  };

  const flagEnum = getFlagEnum();
  const flagEntries = Object.entries(flagEnum).filter(([key, value]) => typeof value === 'number') as [string, number][];

  // Convert current value to selected flags
  useEffect(() => {
    const numValue = parseInt(currentValue || '0');
    const flags: number[] = [];
    
    flagEntries.forEach(([, flagValue]) => {
      if ((numValue & flagValue) === flagValue) {
        flags.push(flagValue);
      }
    });
    
    setSelectedFlags(flags);
  }, [currentValue, flagEntries]);

  // Calculate final value from selected flags
  const calculateFinalValue = (flags: number[]): number => {
    return flags.reduce((sum, flag) => sum | flag, 0);
  };

  // Handle flag toggle
  const handleFlagToggle = (flagValue: number) => {
    let newFlags: number[];
    
    if (selectedFlags.includes(flagValue)) {
      newFlags = selectedFlags.filter(flag => flag !== flagValue);
    } else {
      newFlags = [...selectedFlags, flagValue];
    }
    
    setSelectedFlags(newFlags);
    const finalValue = calculateFinalValue(newFlags);
    onValueChange(field, finalValue.toString());
  };
  // Keep flag names exactly as provided
  const formatFlagName = (flagName: string): string => {
    return flagName;
  };

  const handleReset = () => {
    onReset(field);
  };

  const finalValue = calculateFinalValue(selectedFlags);

  return (
    <div className="flag-field">
      <div className="flag-header">
        <button
          className="reset-btn"
          onClick={handleReset}
          title="Reset to stock value"
        >
          ↻
        </button>
        <span className="field-name">{field}</span>
        <span className="stock-value">Stock: {stockValue}</span>
        <span className="current-value">Current: {finalValue}</span>
        <button
          className="confirm-btn"
          onClick={() => console.log(`Confirmed ${field}: ${finalValue}`)}
          title="Confirm changes"
        >
          ✓
        </button>
      </div>
        <div className="flag-checkboxes">
        {flagEntries.map(([flagName, flagValue]) => {
          const isChecked = selectedFlags.includes(flagValue);
          return (
            <label 
              key={flagName} 
              className={`flag-checkbox-label ${isChecked ? 'checked' : ''}`}
            >
              <input
                type="checkbox"
                checked={isChecked}
                onChange={() => handleFlagToggle(flagValue)}
                className="flag-checkbox"
              />
              <span className="flag-name">{formatFlagName(flagName)}</span>
              <span className="flag-value">0x{flagValue.toString(16).toUpperCase().padStart(8, '0')}</span>
            </label>
          );
        })}
      </div>
    </div>
  );
};

export default FlagField;
