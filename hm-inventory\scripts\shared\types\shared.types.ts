import { PanelType } from '../../../ui/src/stores/inventoryStore';
import { InventoryItem } from './inventory.types';

/**
 * Types and constants for drag-and-drop operations
 */
export const DraggableItemType = {
    INVENTORY_ITEM: 'inventoryItem'
} as const;
export type DraggableItemType = (typeof DraggableItemType)[keyof typeof DraggableItemType];
export { DraggableItemType as DraggableItemTypeValue };

export interface DraggedItemPayload {
    inventoryItem: InventoryItem;
    sourceSlotIndex: number;
    quantity: number;
    // Panel context for drag origin
    sourcePanelType?: 'main' | 'quickAccess' | 'action' | 'secondary';
    sourceInstanceId?: string;
}

// NUI Message Types
export interface NuiRequestBase {
    action: string;
}

export interface MoveItemRequest extends NuiRequestBase {
    action: 'moveItem';
    sourceIndex: number;
    sourcePanelType: PanelType;
    sourcePanelId?: string;
    destinationIndex: number;
    destinationPanelType: PanelType;
    destinationPanelId?: string;
    quantity?: number;
}

export interface DropItemRequest extends NuiRequestBase {
    action: 'dropItem';
    slotIndex: number;
    quantity: number;
}

export interface EquipItemRequest extends NuiRequestBase {
    action: 'equipItem';
    sourceSlotIndex: number;
}

export interface PickupItemRequest extends NuiRequestBase {
    action: 'pickupItem';
    slotIndex: number;
    quantity: number;
}

export interface TransferItemRequest extends NuiRequestBase {
    action: 'transferItem';
    sourcePanel: string;
    sourceInstanceId?: string;
    sourceIndex: number;
    destPanel: string;
    destInstanceId?: string;
    destIndex: number;
    quantity?: number;
}

export interface UseItemRequest extends NuiRequestBase {
    action: 'useItem';
    slotIndex: number;
}

export type NuiRequest = MoveItemRequest | DropItemRequest | EquipItemRequest | PickupItemRequest | TransferItemRequest | UseItemRequest;
