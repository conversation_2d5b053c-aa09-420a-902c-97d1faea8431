fx_version 'cerulean'
game 'gta5'
name 'hm-multicharacter'
description 'HM Multicharacter System'
author 'HM-Core'
version '1.0.0'

node_version '22'

dependencies {
    'hm-core',
    'oxmysql'
}

-- Ensure proper loading order
provide 'multicharacter'

dependency 'hm-core'

-- Compiled TypeScript files
shared_script 'build/shared.js'
client_script 'build/client.js'
server_script 'build/server.js'

-- UI
ui_page 'ui/dist/index.html'

-- UI Files
files {
    'ui/dist/index.html',
    'ui/dist/**/*'
}