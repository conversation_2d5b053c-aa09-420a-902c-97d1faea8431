{"name": "hm-events", "version": "1.0.0", "description": "Game event logging for HM Framework", "main": "index.js", "engines": {"node": "22.16.0"}, "scripts": {"watch:all": "concurrently -n \"GAME,UI\" -c \"blue,green\" \"npm run watch:game\" \"npm run watch:ui\"", "build:all": "node build.js --production && cd ui && npm run build", "watch:game": "node build.js --watch", "watch:ui": "cd ui && npm run dev", "build:game": "node build.js --production", "build:ui": "cd ui && npm run build", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\""}, "keywords": ["fivem", "hm-framework", "events"], "author": "HM Framework", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "esbuild": "^0.25.5", "esbuild-node-externals": "^1.4.1", "typescript": "^5.8.3"}}