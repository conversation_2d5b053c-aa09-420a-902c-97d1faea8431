{"name": "hm-weapons", "version": "1.0.0", "description": "Weapons resource for FiveM", "main": "index.js", "engines": {"node": "22.16.0"}, "scripts": {"watch:all": "concurrently -n \"GAME,UI\" -c \"blue,green\" \"npm run watch:game\" \"npm run watch:ui\"", "build:all": "node build.js && cd ui && npm run build", "build": "node build.js", "build:prod": "node build.js --production", "watch": "node build.js --watch", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\"", "watch:game": "node build.js --watch", "watch:ui": "cd ui && npm run dev"}, "keywords": ["fivem", "weapons", "typescript"], "author": "HM", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "concurrently": "latest", "esbuild": "latest", "eslint": "latest", "eslint-config-prettier": "latest", "eslint-plugin-prettier": "latest", "prettier": "latest", "typescript": "latest", "vite": "latest"}, "dependencies": {"axios": "^1.9.0"}}