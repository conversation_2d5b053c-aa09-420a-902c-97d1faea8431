import React, { useState } from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import { 
  sortInventoryItems, 
  calculateInventoryStats, 
  SortOptions,
  autoOptimizeStacks,
  analyzeStackOptimization,
  getStackHealthAnalysis
} from '../../utils/inventoryUtils';
import { ToastType } from '@/utils/toastUtils';

interface InventoryToolbarProps {
  onOpenQuickAccessConfig?: () => void;
}

const InventoryToolbar: React.FC<InventoryToolbarProps> = ({ onOpenQuickAccessConfig }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [sortMethod, setSortMethod] = useState<SortOptions['method']>('type');
  const [sortDirection, setSortDirection] = useState<SortOptions['direction']>('asc');
  
  const gridItems = useInventoryStore(state => state.gridItems);
  const updateGridItems = useInventoryStore(state => state.updateGridItems);
  
  const stats = calculateInventoryStats(gridItems);
  
  const handleSort = () => {
    const sortOptions: SortOptions = {
      method: sortMethod,
      direction: sortDirection,
      groupStackable: true,
    };
    
    const sortedItems = sortInventoryItems(gridItems, sortOptions);
    updateGridItems(sortedItems);
    
    // Show feedback toast
    useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
      itemName: `Inventory sorted by ${sortMethod}`,
    });
  };
  
  const handleQuickSort = (method: SortOptions['method']) => {
    const sortOptions: SortOptions = {
      method,
      direction: 'asc',
      groupStackable: true,
    };
    
    const sortedItems = sortInventoryItems(gridItems, sortOptions);
    updateGridItems(sortedItems);
    
    useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
      itemName: `Quick sorted by ${method}`,
    });
  };
  
  const handleConsolidate = () => {
    const sortOptions: SortOptions = {
      method: 'type',
      direction: 'asc',
      groupStackable: true,
    };
    
    const consolidatedItems = sortInventoryItems(gridItems, sortOptions);
    updateGridItems(consolidatedItems);
    
    useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
      itemName: 'Items consolidated',
    });
  };
  const handleSmartOptimize = () => {
    try {
      const optimizedItems = autoOptimizeStacks(gridItems);
      updateGridItems(optimizedItems);
      
      const analysis = getStackHealthAnalysis(optimizedItems);
      useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
        itemName: `Optimized ${analysis.inefficientStacks} stacks, saved ${analysis.potentialSpaceSaved} slots`,
      });
    } catch (error) {
      console.error('Failed to optimize stacks:', error);
      useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
        itemName: 'Failed to optimize inventory',
      });
    }
  };
  const handleAnalyzeStacks = () => {
    const suggestions = analyzeStackOptimization(gridItems);
    
    if (suggestions.length > 0) {
      useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
        itemName: `Found ${suggestions.length} optimization opportunities`,
      });
    } else {
      useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
        itemName: 'Inventory is already optimized',
      });
    }
  };
  
  return (
    <div className="flex items-center gap-2 mb-3">
      {/* Compact Stats Display */}
      <div className="flex items-center gap-1 text-xs text-gray-400">
        <span>{stats.usedSlots}/{gridItems.length}</span>
        <i className="fas fa-boxes text-blue-400/60" />
      </div>
      
      {/* Quick Action Buttons */}
      <div className="flex items-center gap-1">        <button
          onClick={() => handleQuickSort('type')}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-neutral-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Sort by type"
        >
          <i className="fas fa-layer-group text-[10px]" />
        </button>
        
        <button
          onClick={() => handleQuickSort('name')}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-neutral-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Sort alphabetically"
        >
          <i className="fas fa-sort-alpha-down text-[10px]" />
        </button>
          <button
          onClick={handleConsolidate}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-neutral-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Consolidate stacks"
        >
          <i className="fas fa-compress-arrows-alt text-[10px]" />
        </button>
        
        <button
          onClick={handleSmartOptimize}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-green-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Smart optimize stacks"
        >
          <i className="fas fa-magic text-[10px]" />
        </button>        <button
          onClick={handleAnalyzeStacks}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-blue-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Analyze stack efficiency"
        >
          <i className="fas fa-chart-bar text-[10px]" />
        </button>
        
        {/* QuickAccess Config Button */}
        <button
          onClick={onOpenQuickAccessConfig}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700/80 hover:bg-green-600 border border-neutral-600/50 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
          title="Configure QuickAccess hotkeys"
        >
          <i className="fas fa-keyboard text-[10px]" />
        </button>
          {/* Expand Button */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          tabIndex={-1}
          className={`w-6 h-6 border border-neutral-600/50 rounded text-xs transition-all flex items-center justify-center ${
            isExpanded 
              ? 'bg-blue-600 text-white' 
              : 'bg-neutral-700/80 hover:bg-neutral-600 text-gray-300 hover:text-white'
          }`}
          title="More options"
        >
          <i className={`fas ${isExpanded ? 'fa-times' : 'fa-cog'} text-[10px]`} />
        </button>

        {/* Help Button */}
        <button
          onClick={() => setShowHelp(!showHelp)}
          tabIndex={-1}
          className={`w-6 h-6 border border-neutral-600/50 rounded text-xs transition-all flex items-center justify-center relative ${
            showHelp 
              ? 'bg-purple-600 text-white'
              : 'bg-neutral-700/80 hover:bg-purple-600 text-gray-300 hover:text-white'
          }`}
          title="Keyboard shortcuts"
        >
          <i className="fas fa-question text-[10px]" />
          
          {/* Help Tooltip */}
          {showHelp && (
            <div className="absolute top-8 right-0 bg-neutral-900 border border-neutral-600 rounded-lg p-3 text-xs text-white z-50 min-w-[200px]">
              <div className="font-semibold mb-2">Keyboard Shortcuts:</div>
              <div className="space-y-1">
                <div><span className="text-blue-400">Ctrl+S</span> - Sort by type</div>
                <div><span className="text-blue-400">Ctrl+O</span> - Optimize stacks</div>
                <div><span className="text-blue-400">Alt+S</span> - Sort alphabetically</div>
                <div><span className="text-blue-400">Alt+Q</span> - Sort by quantity</div>
              </div>
            </div>
          )}
        </button>
      </div>
      
      {/* Expanded Options */}
      {isExpanded && (
        <div className="flex items-center gap-2 ml-2 pl-2 border-l border-neutral-600/50">
          {/* Sort Method */}
          <select
            value={sortMethod}
            onChange={(e) => setSortMethod(e.target.value as SortOptions['method'])}
            className="px-2 py-1 bg-neutral-700 border border-neutral-600 rounded text-xs text-white focus:border-blue-500 focus:outline-none"
          >
            <option value="type">Type</option>
            <option value="name">Name</option>
            <option value="quantity">Quantity</option>
            <option value="weight">Weight</option>
            <option value="rarity">Rarity</option>
          </select>
          
          {/* Sort Direction */}
          <button
            onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
            className="w-6 h-6 bg-neutral-700 hover:bg-neutral-600 border border-neutral-600 rounded text-xs text-gray-300 hover:text-white transition-colors flex items-center justify-center"
            title={`Sort ${sortDirection === 'asc' ? 'descending' : 'ascending'}`}
          >
            <i className={`fas fa-sort-amount-${sortDirection === 'asc' ? 'down' : 'up'} text-[10px]`} />
          </button>
          
          {/* Apply Sort */}
          <button
            onClick={handleSort}
            className="px-2 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded transition-colors"
          >
            Sort
          </button>
          
          {/* Extended Stats */}
          <div className="flex items-center gap-2 text-xs text-gray-400 ml-2">
            <span title="Total weight">
              {(stats.totalWeight / 1000).toFixed(1)}kg
            </span>
            <span title="Stackable items">
              <i className="fas fa-layer-group" /> {stats.stackableItems}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryToolbar;
