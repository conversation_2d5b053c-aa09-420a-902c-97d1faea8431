/**
 * Client Request Sender
 *
 * This module sends requests to the FiveM client and handles responses.
 *
 * How it works:
 * 1. UI components call methods from this module to send requests to the client
 * 2. In browser mode, it returns mock data for testing
 * 3. In FiveM mode, it sends the request to the client and returns the response
 *
 * Example flow:
 * UI Component → clientRequests.send → Client <PERSON>a <PERSON> → Response
 */
import { isBrowser } from '../utils/environment';

/**
 * Client Request Sender API
 */
export const clientRequests = {
  /**
   * Send a request to the client
   *
   * @param app The app name (e.g., 'contacts', 'messages')
   * @param action The action to perform (e.g., 'getContacts', 'sendMessage')
   * @param data Optional data to send with the request
   * @param mockData Optional mock data for browser testing
   * @param storeMethod Optional method to call on the response data (for browser testing)
   * @returns A promise that resolves with the response
   */
  async send(
    app: string,
    action: string,
    data: Record<string, unknown> = {},
    mockData: unknown = null,
    storeMethod?: (data: unknown) => void
  ) {
    // console.log(`[ClientRequests] Sending ${app}:${action} request:`, JSON.stringify(data));
    // console.log(`[ClientRequests] Resource name:`, (window as any).GetParentResourceName?.());

    // In browser mode, return mock data if provided
    if (isBrowser) {
      await new Promise(resolve => setTimeout(resolve, 300));

      if (mockData !== null) {
        const response = { success: true, data: mockData };
        // console.log(`[ClientRequests] Returning mock data for ${app}:${action}:`, JSON.stringify(response, null, 2));

        // If a store method is provided, call it with the mockData
        if (storeMethod && typeof storeMethod === 'function') {
          storeMethod(mockData);
        }

        return response;
      }

      // Otherwise, return an empty success response
      // console.log(`[ClientRequests] No mock data provided for ${app}:${action}, returning empty response`);
      return { success: true, data: {} };
    }

    // Get the resource name
    const resourceName = (
      window as Window & { GetParentResourceName?: () => string }
    ).GetParentResourceName?.();

    try {
      // Send the request to the client
      const response = await fetch(`https://${resourceName}/nuiEvent`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ app, action, data })
      });

      // Parse the response
      const text = await response.text();
      let result = { success: false };

      if (text && text !== 'OK' && text.length > 0) {
        try {
          result = JSON.parse(text);
        } catch (error) {
          console.error(`[ClientRequests] Error parsing response:`, error);
        }
      }

      // console.log(`[ClientRequests] ${app}:${action} response:`, result);
      return result;
    } catch (error) {
      console.error(`[ClientRequests] Error in ${app}:${action} request:`, error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Helper for get operations
   *
   * @param app The app name
   * @param resource The resource name (singular or plural)
   * @param id Optional ID for getting a specific item
   * @param mockData Optional mock data for browser testing
   */
  async get(
    app: string,
    resource: string,
    id: number | null = null,
    mockData: unknown = null,
    storeMethod?: (data: unknown) => void
  ) {
    const action = id ? `get${resource}` : `getAll${resource}`;
    const data = id ? { id } : {};
    return this.send(app, action, data, mockData, storeMethod);
  },

  /**
   * Helper for create operations
   *
   * @param app The app name
   * @param resource The resource name (singular)
   * @param data The data to create
   * @param mockData Optional mock data for browser testing
   */
  async create(
    app: string,
    resource: string,
    data: Record<string, unknown>,
    currentState: Record<string, unknown>[] = [],
    storeMethod?: (data: Record<string, unknown>) => void
  ) {
    if (isBrowser) {
      // Create new item with generated ID based on current state
      const newItem = {
        ...data,
        id: Math.max(...currentState.map(item => (item.id as number) ?? 0), 0) + 1
      };

      if (storeMethod && typeof storeMethod === 'function') {
        storeMethod(newItem);
      }

      return { success: true, data: newItem };
    }

    return this.send(app, `create${resource}`, data);
  },

  /**
   * Helper for update operations
   *
   * @param app The app name
   * @param resource The resource name (singular)
   * @param id The ID of the item to update
   * @param data The data to update
   * @param mockData Optional mock data for browser testing
   */
  async update(
    app: string,
    resource: string,
    id: number,
    data: Record<string, unknown>,
    storeMethod?: (data: Record<string, unknown>) => void
  ) {
    if (isBrowser) {
      if (storeMethod && typeof storeMethod === 'function') {
        storeMethod(data);
      }
      return { success: true };
    }

    return this.send(app, `update${resource}`, { id, ...data });
  },

  /**
   * Helper for delete operations
   *
   * @param app The app name
   * @param resource The resource name (singular)
   * @param id The ID of the item to delete
   * @param mockData Optional mock data for browser testing
   */
  async delete(app: string, resource: string, id: number, storeMethod?: (id: number) => void) {
    if (isBrowser) {
      if (storeMethod && typeof storeMethod === 'function') {
        storeMethod(id);
      }
      return { success: true };
    }

    return this.send(app, `delete${resource}`, { id });
  }
};
