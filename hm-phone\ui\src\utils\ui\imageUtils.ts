/**
 * Image Utilities
 *
 * This module provides utilities for image handling, processing, and display.
 */

/**
 * Load an image with proper error handling
 * @param url - The URL of the image to load
 * @returns A promise that resolves with the loaded image or rejects with an error
 */
export function loadImage(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = error => reject(error);
    img.src = url;
  });
}

/**
 * Get image dimensions
 * @param url - The URL of the image
 * @returns A promise that resolves with the image dimensions
 */
export function getImageDimensions(url: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve({ width: img.width, height: img.height });
    img.onerror = error => reject(error);
    img.src = url;
  });
}

/**
 * Resize an image to a maximum width and height
 * @param imageUrl - The URL of the image to resize
 * @param maxWidth - Maximum width
 * @param maxHeight - Maximum height
 * @param quality - JPEG quality (0-1)
 * @returns A promise that resolves with the resized image data URL
 */
export async function resizeImage(
  imageUrl: string,
  maxWidth: number = 1024,
  maxHeight: number = 1024,
  quality: number = 0.8
): Promise<string> {
  try {
    // Load the image
    const img = await loadImage(imageUrl);

    // Calculate new dimensions while maintaining aspect ratio
    let width = img.width;
    let height = img.height;

    if (width > maxWidth) {
      height = (height * maxWidth) / width;
      width = maxWidth;
    }

    if (height > maxHeight) {
      width = (width * maxHeight) / height;
      height = maxHeight;
    }

    // Create a canvas and resize the image
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    ctx.drawImage(img, 0, 0, width, height);

    // Convert to data URL
    return canvas.toDataURL('image/jpeg', quality);
  } catch (error) {
    console.error('[ImageUtils] Error resizing image:', error);
    throw error;
  }
}

/**
 * Convert a data URL to a Blob
 * @param dataUrl - The data URL to convert
 * @returns A Blob object
 */
export function dataUrlToBlob(dataUrl: string): Blob {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new Blob([u8arr], { type: mime });
}

/**
 * Calculate the file size of a data URL
 * @param dataUrl - The data URL
 * @returns Size in bytes
 */
export function getDataUrlFileSize(dataUrl: string): number {
  const base64 = dataUrl.split(',')[1];
  const stringLength = base64.length;
  const sizeInBytes = 4 * Math.ceil(stringLength / 3) * 0.75;
  return Math.round(sizeInBytes);
}

/**
 * Apply a filter to an image
 * @param imageUrl - The URL of the image
 * @param filter - The filter to apply
 * @returns A promise that resolves with the filtered image data URL
 */
export async function applyImageFilter(
  imageUrl: string,
  filter: 'grayscale' | 'sepia' | 'blur' | 'brightness' | 'contrast'
): Promise<string> {
  try {
    // Load the image
    const img = await loadImage(imageUrl);

    // Create a canvas
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    // Draw the image
    ctx.drawImage(img, 0, 0);

    // Apply filter
    switch (filter) {
      case 'grayscale': {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
          const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
          data[i] = avg;
          data[i + 1] = avg;
          data[i + 2] = avg;
        }
        ctx.putImageData(imageData, 0, 0);
        break;
      }
      case 'sepia':
        ctx.filter = 'sepia(100%)';
        ctx.drawImage(img, 0, 0);
        break;
      case 'blur':
        ctx.filter = 'blur(5px)';
        ctx.drawImage(img, 0, 0);
        break;
      case 'brightness':
        ctx.filter = 'brightness(150%)';
        ctx.drawImage(img, 0, 0);
        break;
      case 'contrast':
        ctx.filter = 'contrast(150%)';
        ctx.drawImage(img, 0, 0);
        break;
    }

    // Reset filter
    ctx.filter = 'none';

    // Convert to data URL
    return canvas.toDataURL('image/jpeg', 0.9);
  } catch (error) {
    console.error('[ImageUtils] Error applying filter:', error);
    throw error;
  }
}

/**
 * Create a thumbnail from an image
 * @param imageUrl - The URL of the image
 * @param size - Thumbnail size
 * @returns A promise that resolves with the thumbnail data URL
 */
export async function createThumbnail(imageUrl: string, size: number = 100): Promise<string> {
  return resizeImage(imageUrl, size, size, 0.7);
}
