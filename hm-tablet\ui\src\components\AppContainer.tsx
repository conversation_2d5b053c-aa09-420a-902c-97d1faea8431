import React from 'react';
import { useTabletStore } from '../stores/tabletStore';

// App Components
import SettingsApp from './apps/SettingsApp';
import RacingApp from './apps/RacingApp';

interface AppContainerProps {
  appId: string;
}

const AppContainer: React.FC<AppContainerProps> = ({ appId }) => {
  const { installedApps, goHome } = useTabletStore();
  
  const app = installedApps.find(a => a.id === appId);
  
  if (!app) {
    return (
      <div className="app-container flex items-center justify-center">
        <div className="text-center">
          <p className="text-tablet-text-secondary mb-4">App not found</p>
          <button 
            onClick={goHome}
            className="btn-primary"
          >
            Go Home
          </button>
        </div>
      </div>
    );
  }  const renderApp = () => {
    switch (appId) {
      case 'racing': return <RacingApp />;
      case 'settings': return <SettingsApp />;
      default: 
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-tablet-text-secondary mb-4">
                {app.name} is coming soon!
              </p>
              <p className="text-tablet-text-muted text-sm">
                This app is currently under development.
              </p>
            </div>
          </div>
        );
    }
  };
  return (
    <div className="app-container">
      {/* App Content */}
      <div className="flex-1 overflow-hidden">
        {renderApp()}
      </div>
    </div>
  );
};

export default AppContainer;
