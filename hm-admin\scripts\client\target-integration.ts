/**
 * HM Admin Target Integration
 * Bridges the targeting system with the admin InfoPanel
 * Provides entity type detection and admin action hooks
 */

import { TargetFlags } from "@shared/types";


// Interface for target information
interface TargetInfo {
  entity?: number;
  modelHash?: number;
  zone?: any;
  position: { x: number; y: number; z: number };
  options: any[];
  distance: number;
  type?: "entity" | "zone" | "model";
  entityType?: "player" | "vehicle" | "ped" | "object";
}

export class TargetIntegration {
  private static instance: TargetIntegration | null = null;
  private isActive = false;
  private lastTargetedEntity = 0;
  private infoPanelVisible = false;  private isScanning = false;
  private scanInterval: number | null = null;
  private currentScanTarget: TargetInfo | null = null;
  private scanStartTime = 0;

  private constructor() {
    this.setupEventHandlers();
    this.setupKeyBindings();
  }

  public static getInstance(): TargetIntegration {
    if (!TargetIntegration.instance) {
      TargetIntegration.instance = new TargetIntegration();
    }
    return TargetIntegration.instance;
  }
  /**
   * Setup key bindings for admin targeting - using hold I to scan pattern
   */
  private setupKeyBindings(): void {
    // Register hold-to-scan commands using +/- pattern like hm-target
    RegisterCommand('+hm-admin-scan', () => {
      this.startScanning();
    }, false);

    RegisterCommand('-hm-admin-scan', () => {
      this.stopScanning();
    }, false);

    // Bind the I key to the scan commands
    RegisterKeyMapping(
      '+hm-admin-scan',
      'Hold to Scan for Admin Target',
      'keyboard',
      'I'
    );
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Listen for NUI callbacks from InfoPanel
    RegisterNuiCallbackType('hm-admin:toggleInfoPanel');
    on('__cfx_nui:hm-admin:toggleInfoPanel', (data: any, cb: Function) => {
      this.toggleInfoPanel();
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:teleportToCoords');
    on('__cfx_nui:hm-admin:teleportToCoords', (data: { x: number; y: number; z: number }, cb: Function) => {
      this.teleportToCoords(data.x, data.y, data.z);
      cb('ok');
    });

    RegisterNuiCallbackType('hm-admin:freezeEntity');
    on('__cfx_nui:hm-admin:freezeEntity', (data: { entity: number }, cb: Function) => {
      this.freezeEntity(data.entity);
      cb('ok');
    });

    // Listen for server events
    onNet('hm-admin:client:sendOperationResult', (result: any) => {
      if (result.success) {
        this.showNotification(result.message, 'success');
      } else {
        this.showNotification(result.message, 'error');
      }    });
  }

  /**
   * Start the scanning process when I key is pressed down
   */
  private startScanning(): void {
    if (this.isScanning) return;
    
    this.isScanning = true;
    this.scanStartTime = Date.now();
    this.currentScanTarget = null;
    
    // Send NUI message to show scanning UI
    SendNUIMessage({
      type: 'startScanning',
      data: {}
    });
    
    console.log('[Admin-Target] Started scanning...');
    
    // Start continuous raycast loop at 30fps (every ~33ms)
    this.scanInterval = setInterval(() => {
      this.performScan();
    }, 33) as any;
  }

  /**
   * Stop the scanning process when I key is released
   */
  private stopScanning(): void {
    if (!this.isScanning) return;
    
    this.isScanning = false;
    
    // Clear the scanning interval
    if (this.scanInterval !== null) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }
    
    const scanDuration = Date.now() - this.scanStartTime;
    console.log(`[Admin-Target] Stopped scanning after ${scanDuration}ms`);
    
    // Send NUI message to hide scanning UI
    SendNUIMessage({
      type: 'stopScanning',
      data: {}
    });
    // If we found a target during scanning, show the InfoPanel
    if (this.currentScanTarget) {
      this.showInfoPanelWithData(this.currentScanTarget);
      this.currentScanTarget = null;
    } else {
      this.showNotification('No target found while scanning', 'info');
    }
  }

  /**
   * Perform a single scan raycast and update target preview
   */
  private performScan(): void {
    const targetInfo = this.getCurrentTargetInfo();
    
    if (targetInfo && targetInfo.entity !== this.lastTargetedEntity) {
      this.currentScanTarget = targetInfo;
      this.lastTargetedEntity = targetInfo.entity || 0; // Ensure a number is assigned
      
      // Send target preview to UI
      SendNUIMessage({
        type: 'updateScanTarget',
        data: { target: targetInfo }
      });
      
      // Optional: Add entity highlighting here
      this.highlightEntity(targetInfo.entity);
    } else if (!targetInfo && this.currentScanTarget) {
      // Lost target
      this.currentScanTarget = null;
      this.lastTargetedEntity = 0;
      
      SendNUIMessage({
        type: 'clearScanTarget',
        data: {}
      });
      
      this.clearEntityHighlight();
    }
  }

  /**
   * Highlight the currently scanned entity (optional visual feedback)
   */
  private highlightEntity(entity?: number): void {
    if (!entity || !DoesEntityExist(entity)) return;
    
    // You can add entity highlighting here if desired
    // For example, using SetEntityAlpha or drawing markers
    // This is optional and can be implemented based on your preferences
  }

  /**
   * Clear entity highlighting
   */
  private clearEntityHighlight(): void {
    // Clear any entity highlighting
    // Implementation depends on the highlighting method used
  }

  /**
   * Toggle the InfoPanel with current target information
   */
  private toggleInfoPanel(): void {
    if (this.infoPanelVisible) {
      this.hideInfoPanel();
    } else {
      this.showInfoPanel();
    }
  }

  /**
   * Show InfoPanel with current target information
   */
  private showInfoPanel(): void {
    const targetInfo = this.getCurrentTargetInfo();
    
    if (targetInfo) {
      this.infoPanelVisible = true;
      SendNUIMessage({
        type: 'showInfoPanel',
        data: { target: targetInfo }
      });
      console.log('[Admin-Target] InfoPanel shown with target:', targetInfo);
    } else {
      this.showNotification('No target found under crosshair', 'error');
    }
  }
  /**
   * Hide the InfoPanel
   */
  private hideInfoPanel(): void {
    this.infoPanelVisible = false;
    SendNUIMessage({
      type: 'hideInfoPanel',
      data: {}
    });
    console.log('[Admin-Target] InfoPanel hidden');
  }

  /**
   * Show InfoPanel with provided target data (used after scanning)
   */
  private showInfoPanelWithData(targetInfo: TargetInfo): void {
    this.infoPanelVisible = true;
    SendNUIMessage({
      type: 'showInfoPanel',
      data: { target: targetInfo }
    });
    console.log('[Admin-Target] InfoPanel shown with scanned target:', targetInfo);
  }

  /**
   * Get current target information from crosshair
   */
  private getCurrentTargetInfo(): TargetInfo | null {
    const playerPed = PlayerPedId();
    const playerCoords = GetEntityCoords(playerPed, true);
    
    // Perform raycast from camera forward
    const camCoords = GetGameplayCamCoord();
    const camRot = GetGameplayCamRot(2);
    
    // Calculate direction vector
    const distance = 10.0;
    const direction = this.rotationToDirection(camRot);
    const destination = [
      camCoords[0] + direction.x * distance,
      camCoords[1] + direction.y * distance,
      camCoords[2] + direction.z * distance
    ];

    // Perform raycast
    // NEED TO TRY : StartShapeTestSurroundingCoords
    // START_SHAPE_TEST_MOUSE_CURSOR_LOS_PROBE(TargetFlags, entity, TargetOptions)
    const raycastHandle = StartShapeTestRay(
      camCoords[0], camCoords[1], camCoords[2],
      destination[0], destination[1], destination[2],
      TargetFlags.Vehicle | TargetFlags.Ped | TargetFlags.Object, // Vehicles, Peds, Objects
      playerPed,
      4
    );

    const [status, hit, endCoords, surfaceNormal, entityHit] = GetShapeTestResult(raycastHandle);

    if (hit && entityHit && entityHit !== 0 && DoesEntityExist(entityHit)) {
      const position = {
        x: endCoords[0],
        y: endCoords[1],
        z: endCoords[2]
      };

      const distanceToTarget = this.getDistance3D(
        { x: playerCoords[0], y: playerCoords[1], z: playerCoords[2] },
        position
      );

      // Determine entity type
      const entityType = this.determineEntityType(entityHit);
      const modelHash = GetEntityModel(entityHit);

      return {
        entity: entityHit,
        modelHash: modelHash,
        position: position,
        options: [], // Admin actions are handled in InfoPanel
        distance: distanceToTarget,
        type: "entity",
        entityType: entityType
      };
    }

    return null;
  }

  /**
   * Determine the entity type for admin actions
   */
  private determineEntityType(entity: number): "player" | "vehicle" | "ped" | "object" | undefined {
    if (!DoesEntityExist(entity)) return undefined;

    try {
      if (IsEntityAVehicle(entity)) {
        return "vehicle";
      } else if (IsEntityAPed(entity)) {
        const playerPed = PlayerPedId();
        if (entity === playerPed) {
          return undefined; // Don't target self
        }

        // Check if it's a player more safely
        try {
          const playerIndex = NetworkGetPlayerIndexFromPed(entity);
          if (playerIndex !== -1 && NetworkIsPlayerActive(playerIndex)) {
            return "player";
          }
          return "ped";
        } catch (e) {
          return "ped";
        }
      } else if (IsEntityAnObject(entity)) {
        return "object";
      }
    } catch (error) {
      console.error('[Admin-Target] Error determining entity type:', error);
    }

    return undefined;
  }

  /**
   * Convert rotation to direction vector
   */
  private rotationToDirection(rotation: number[]): { x: number; y: number; z: number } {
    const z = rotation[2] * (Math.PI / 180.0);
    const x = rotation[0] * (Math.PI / 180.0);
    const absX = Math.abs(Math.cos(x));

    return {
      x: -Math.sin(z) * absX,
      y: Math.cos(z) * absX,
      z: Math.sin(x)
    };
  }

  /**
   * Calculate 3D distance between two points
   */
  private getDistance3D(pos1: { x: number; y: number; z: number }, pos2: { x: number; y: number; z: number }): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * Teleport player to coordinates
   */
  private teleportToCoords(x: number, y: number, z: number): void {
    const playerPed = PlayerPedId();
    SetEntityCoords(playerPed, x, y, z, false, false, false, false);
    this.showNotification(`Teleported to: ${x.toFixed(2)}, ${y.toFixed(2)}, ${z.toFixed(2)}`, 'success');
  }

  /**
   * Freeze/unfreeze an entity
   */
  private freezeEntity(entity: number): void {
    if (!DoesEntityExist(entity)) {
      this.showNotification('Entity not found', 'error');
      return;
    }

    const isFrozen = IsEntityPositionFrozen(entity);
    FreezeEntityPosition(entity, !isFrozen);
    
    const entityType = this.determineEntityType(entity);
    const action = !isFrozen ? 'Frozen' : 'Unfrozen';
    this.showNotification(`${action} ${entityType || 'entity'} (${entity})`, 'success');
  }

  /**
   * Show a notification to the player
   */
  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    // Use GTA's notification system for now
    SetNotificationTextEntry('STRING');
    AddTextComponentString(message);
    DrawNotification(false, false);
    console.log(`[Admin-Target] Notification (${type}): ${message}`);
  }

  /**
   * Initialize the target integration system
   */
  public static initialize(): void {
    TargetIntegration.getInstance();
    console.log('[Admin-Target] Target integration system initialized');
  }
}

// Auto-initialize when the script loads
TargetIntegration.initialize();

// Export the class for external use
export default TargetIntegration;
