{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@shared/*": ["shared/*"], "@client/*": ["client/*"], "@server/*": ["server/*"]}, "outDir": "./build", "rootDirs": ["client", "server", "shared"], "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "lib": ["ES2020"], "types": ["node"]}, "include": ["client/**/*", "server/**/*", "shared/**/*"], "exclude": ["node_modules", "ui"]}