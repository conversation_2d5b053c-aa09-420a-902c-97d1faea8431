local QBCore = exports['qb-core']:GetCoreObject()

-- Citizen.CreateThread(function ()
--     Scaleform("digiscanner", "WEAPON_HACKINGDEVICE", "W_AM_HackDevice_M32")
-- end)

RegisterCommand("digiscanner", function ()
    Scaleform("digiscanner", "digiscanner")
	-- W_AM_HackDevice_M32
end)

function Setuptext(rtname, scaleform)
    -- acres = GetActualScreenResolution()
	-- res = GetScreenResolution()
	-- print("res => "..tostring(res))
	-- SetTextRenderId(rtname)
	-- SetScriptGfxAlign(73, 73)
	-- SetTextWrap(((0 * 1.7777777) / GetScreenAspectRatio(true)), ((1065353216 * 1.7777777) / GetScreenAspectRatio(true)))
	-- SetTextJustification(0)
	-- -- SetTextLineHeightMult((1.7777777 / GetScreenAspectRatio(true)))
	-- SetTextScale(0.7, 0.7)
	-- SetTextColour(0, 255, 100, 255)
	-- SetTextFont(0)
	-- BeginTextCommandDisplayText("STRING")
	-- AddTextComponentSubstringPlayerName("BYTE BREACH")
	-- EndTextCommandDisplayText((0.5 - ((0.5 - 0.5) / GetScreenAspectRatio(true))), 0.02)
	-- SetTextRenderId(GetDefaultScriptRendertargetRenderId())
	-- ResetScriptGfxAlign()

	-- 
	print("scaleform")
	BeginScaleformMovieMethod(scaleform, "SET_COLOUR")
	ScaleformMovieMethodAddParamInt(255)
	ScaleformMovieMethodAddParamInt(10)
	ScaleformMovieMethodAddParamInt(10)
	ScaleformMovieMethodAddParamInt(255)
	ScaleformMovieMethodAddParamInt(10)
	ScaleformMovieMethodAddParamInt(10)
	ScaleformMovieMethodAddParamInt(255)
	ScaleformMovieMethodAddParamInt(10)
	ScaleformMovieMethodAddParamInt(10)
	EndScaleformMovieMethod()
	BeginScaleformMovieMethod(scaleform, "SET_DISTANCE")
	ScaleformMovieMethodAddParamFloat(10.0)
	EndScaleformMovieMethod()
	SetTextRenderId(rtname)
	while IsPlayerFreeAiming(PlayerPedId()) do
		DrawScaleformMovie(scaleform, 0.1, 0.24, 0.21, 0.51, 100, 100, 100, 255, 0)
	end
end

function Scaleform(movie, rt)
    -- GiveWeaponToPed(PlayerPedId(), GetHashKey(weapon), 1, false, true)
    -- weapon = GetWeapontypeModel(GetHashKey(weapon))
	_, weap = GetCurrentPedWeapon(PlayerPedId(), 1)
	weapon = GetWeapontypeModel(weap)
	-- TriggerServerEvent('hm_rendertargets_sv:GetCurrentPedWeaponWeapon')
	print("rt => "..tostring(rt))
	print("weapon => "..tostring(weapon))
    local scaleform = RequestScaleformMovie(movie)
	while not HasScaleformMovieLoaded(scaleform) do
	print("loading scaleform")
		Citizen.Wait(0)
	end
	print("DONE LOADING Scaleform")
    if not IsNamedRendertargetRegistered(rt) then
        RegisterNamedRendertarget(rt, false)
    end
    LinkNamedRendertarget(weapon)
    if IsNamedRendertargetRegistered(rt) then
        rtname = GetNamedRendertargetRenderId(rt)
    end
	Citizen.Wait(2000)
    Setuptext(rtname, scaleform)

end


-- RegisterNetEvent('hm_rendertargets_cl:GetCurrentPedWeaponWeapon')
-- AddEventHandler('hm_rendertargets_cl:GetCurrentPedWeaponWeapon', function(weapon)
-- 	print(weapon)
-- end)