/**
 * HM-Admin Server
 * Main server-side file for the admin system
 */

import { AdminEvents, AdminPlayerInfo, AdminCommandRequest, AdminPermissionLevel, AdminCommands } from '@shared/main';

// Admin data storage
const adminLevels: Map<string, AdminPermissionLevel> = new Map();

// Initialize admin system
const init = async () => {
  console.log('Initializing HM-Admin server...');
  
  // Load admin levels from database
  await loadAdminLevels();
  
  // Register event handlers
  registerEventHandlers();
  
  // Register commands
  registerCommands();
  
  console.log('HM-Admin server initialized');
};

// Load admin levels from database
const loadAdminLevels = async () => {
  try {
    // TODO: Implement database loading of admin levels
    // For now, set some default admin levels for testing
    adminLevels.set('license:**********', AdminPermissionLevel.OWNER); // Replace with your license
    
    console.log(`Loaded ${adminLevels.size} admin levels`);
  } catch (error) {
    console.error('Failed to load admin levels:', error);
  }
};

// Register event handlers
const registerEventHandlers = () => {
  // Handle player list requests
  onNet(AdminEvents.REQUEST_PLAYER_LIST, () => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.HELPER)) {
    //   return;
    // }
    
    // Get player list
    const players = getPlayerList();
    
    // Send player list to client
    emitNet(AdminEvents.PLAYER_LIST_UPDATE, source, players);
  });
  
  // Handle command execution
  onNet(AdminEvents.EXECUTE_COMMAND, (request: AdminCommandRequest) => {
    const source = global.source;
    
    // Execute the command
    executeCommand(source, request);
  });

  // Handle player management events
  onNet('hm-admin:server:requestItemList', () => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    // Get item list (this would typically come from your inventory system)
    const items = getItemList();
    
    // Register global export for items
    global.exports('getAllItems', () => items);
    
    // Send item list to client
    emitNet('hm-admin:client:sendItemList', source, items);
  });

  onNet('hm-admin:server:giveItem', (data: { targetId: number; itemName: string; quantity: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    giveItemToPlayer(source, data.targetId, data.itemName, data.quantity);
  });

  onNet('hm-admin:server:requestWeaponList', () => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    // Get weapon list
    const weapons = getWeaponList();
    
    // Send weapon list to client
    emitNet('hm-admin:client:sendWeaponList', source, weapons);
  });

  onNet('hm-admin:server:giveWeapon', (data: { targetId: number; weaponName: string; ammo?: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    giveWeaponToPlayer(source, data.targetId, data.weaponName);
  });

  // Handle vehicle list requests
  onNet('hm-admin:server:requestVehicleList', () => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    try {
      // Get vehicle models from hm-core exports
      const vehicleModels = global.exports['hm-core'].getAllVehicleModels();
            
      // Send vehicle list to client
      emitNet('hm-admin:client:vehicleListUpdate', source, vehicleModels);
    } catch (error) {
      console.error('Failed to get vehicle models from hm-core:', error);
      
      // Send error response
      emitNet('hm-admin:client:sendOperationResult', source, {
        success: false,
        message: 'Failed to load vehicle list'
      });
    }
  });

  onNet('hm-admin:server:teleportPlayerToPlayer', (data: { targetId: number; destinationPlayerId: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    teleportPlayerToPlayer(source, data.targetId, data.destinationPlayerId);
  });

  onNet('hm-admin:server:teleportPlayerToCoords', (data: { targetId: number; x: number; y: number; z: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    teleportPlayerToCoords(source, data.targetId, data.x, data.y, data.z);
  });

  onNet('hm-admin:server:kickPlayer', (data: { targetId: number; reason: string }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    kickPlayer(source, data.targetId, data.reason);
  });

  onNet('hm-admin:server:banPlayer', (data: { targetId: number; reason: string; duration?: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.ADMIN)) {
    //   return;
    // }
    
    banPlayer(source, data.targetId, data.reason, data.duration);
  });

  onNet('hm-admin:server:healPlayer', (data: { targetId: number; health: number }) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MODERATOR)) {
    //   return;
    // }
    
    healPlayer(source, data.targetId, data.health);
  });

  onNet('hm-admin:server:requestPlayerInfo', (playerId: number) => {
    const source = global.source;
    
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.HELPER)) {
    //   return;
    // }
    
    const playerInfo = getDetailedPlayerInfo(playerId);
    emitNet('hm-admin:client:playerInfoUpdate', source, playerInfo);
  });
};

// Register commands
const registerCommands = () => {
  // Register server-side admin commands
  RegisterCommand('addadmin', (source: number, args: string[]) => {
    // Check if player has admin permissions
    // if (!hasAdminPermission(source, AdminPermissionLevel.MANAGEMENT)) {
    //   return;
    // }
    
    if (args.length < 2) {
      sendCommandResult(source, 'addadmin', false, 'Usage: /addadmin [id] [level]');
      return;
    }
    
    const targetId = parseInt(args[0]);
    const level = parseInt(args[1]);
    
    if (isNaN(targetId) || isNaN(level)) {
      sendCommandResult(source, 'addadmin', false, 'Invalid ID or level');
      return;
    }
    
    // Get target player's license
    const targetLicense = getPlayerLicense(targetId);
    
    if (!targetLicense) {
      sendCommandResult(source, 'addadmin', false, 'Player not found');
      return;
    }
    
    // Set admin level
    adminLevels.set(targetLicense, level);
    
    // TODO: Save to database
    
    sendCommandResult(source, 'addadmin', true, `Set admin level ${level} for player ${targetId}`);
  }, true);
};

// Get player list for admin menu
const getPlayerList = (): AdminPlayerInfo[] => {
  const players: AdminPlayerInfo[] = [];
  
  // Get all players
  const playerIds = getPlayers();
  
  for (const playerId of playerIds) {
    const id = parseInt(playerId);
    const name = GetPlayerName(playerId);
    
    // Get player identifiers
    const identifiers: any = {};
    for (let i = 0; i < GetNumPlayerIdentifiers(playerId); i++) {
      const identifier = GetPlayerIdentifier(playerId, i);
      if (!identifier) continue;
      
      const [type, value] = identifier.split(':');
      identifiers[type] = value;
    }
    
    // Get player position
    const ped = GetPlayerPed(playerId);
    const [x, y, z] = GetEntityCoords(ped);
    
    // Get player health and armor
    const health = GetEntityHealth(ped);
    const armor = GetPedArmour(ped);
    
    // Add player to list
    players.push({
      id,
      name,
      identifiers: {
        license: identifiers.license || '',
        discord: identifiers.discord,
        steam: identifiers.steam,
        ip: identifiers.ip,
        live: identifiers.live,
        xbl: identifiers.xbl,
        fivem: identifiers.fivem,
      },
      ping: GetPlayerPing(playerId),
      health,
      armor,
      position: { x, y, z },
    });
  }
  
  return players;
};

// Execute admin command
const executeCommand = (source: number, request: AdminCommandRequest) => {
  const { command, targetId, args } = request;
  
  // Check if player has admin permissions
  const requiredLevel = getRequiredPermissionLevel(command);
  
  // if (!hasAdminPermission(source, requiredLevel)) {
  //   sendCommandResult(source, command, false, 'You do not have permission to use this command');
  //   return;
  // }
  
  // Execute command based on type
  switch (command) {
    case AdminCommands.KILL_PLAYER.name:
      if (!targetId) {
        sendCommandResult(source, command, false, 'No target player specified');
        return;
      }
      // Kill player
      const targetPed = GetPlayerPed(targetId.toString());
      SetEntityHealth(targetPed, 0);
      sendCommandResult(source, command, true, `Killed player ${GetPlayerName(targetId.toString())}`);
      break;
    case AdminCommands.REVIVE_PLAYER.name:
      if (!targetId) {
        sendCommandResult(source, command, false, 'No target player specified');
        return;
      }
      // Revive player
      const targetPed2 = GetPlayerPed(targetId.toString());
      SetEntityHealth(targetPed2, 200);
      sendCommandResult(source, command, true, `Revived player ${GetPlayerName(targetId.toString())}`);
      break;
    // TODO: Implement other commands
    default:
      sendCommandResult(source, command, false, `Unknown command: ${command}`);
      break;
  }
};

// Get required permission level for a command
const getRequiredPermissionLevel = (command: string): AdminPermissionLevel => {
  switch (command) {
    // Helper commands
    case AdminCommands.TELEPORT_TO_MARKER.name:
    case AdminCommands.NOCLIP.name:
      return AdminPermissionLevel.HELPER;
    // Moderator commands
    case AdminCommands.TELEPORT_TO_PLAYER.name:
    case AdminCommands.TELEPORT_PLAYER_TO_ME.name:
    case AdminCommands.SPECTATE_PLAYER.name:
    case AdminCommands.FREEZE_PLAYER.name:
    case AdminCommands.UNFREEZE_PLAYER.name:
    case AdminCommands.SPAWN_VEHICLE?.name:
    case AdminCommands.DELETE_VEHICLE?.name:
    case AdminCommands.REPAIR_VEHICLE?.name:
    case AdminCommands.CLEAN_VEHICLE?.name:
      return AdminPermissionLevel.MODERATOR;
    // Admin commands
    case AdminCommands.KILL_PLAYER.name:
    case AdminCommands.REVIVE_PLAYER.name:
    case AdminCommands.TELEPORT_TO_COORDS.name:
    case AdminCommands.GOD_MODE?.name:
    case AdminCommands.INVISIBLE?.name:
    case AdminCommands.CLEAR_AREA?.name:
    case AdminCommands.ANNOUNCE?.name:
      return AdminPermissionLevel.ADMIN;
    // Senior admin commands
    case AdminCommands.KICK_PLAYER.name:
    case AdminCommands.WARN_PLAYER?.name:
    case AdminCommands.WEATHER?.name:
    case AdminCommands.TIME?.name:
      return AdminPermissionLevel.SENIOR_ADMIN;
    // Management commands
    case AdminCommands.BAN_PLAYER.name:
    case AdminCommands.UNBAN_PLAYER.name:
      return AdminPermissionLevel.MANAGEMENT;
    // Default to owner level for unknown commands
    default:
      return AdminPermissionLevel.OWNER;
  }
};

// Check if player has admin permission
const hasAdminPermission = (playerId: number, requiredLevel: AdminPermissionLevel): boolean => {
  const license = getPlayerLicense(playerId);
  
  if (!license) {
    return false;
  }
  
  const playerLevel = adminLevels.get(license) || AdminPermissionLevel.NONE;
  
  return playerLevel >= requiredLevel;
};

// Get player's license identifier
const getPlayerLicense = (playerId: number): string | null => {
  const playerIdStr = playerId.toString();
  
  for (let i = 0; i < GetNumPlayerIdentifiers(playerIdStr); i++) {
    const identifier = GetPlayerIdentifier(playerIdStr, i);
    
    if (identifier && identifier.startsWith('license:')) {
      return identifier;
    }
  }
  
  return null;
};

// Send command result to client
const sendCommandResult = (playerId: number, command: string, success: boolean, message: string) => {
  emitNet(AdminEvents.COMMAND_RESULT, playerId, {
    command,
    success,
    message,
  });
};

// Initialize admin system on resource start
init();

// Player management helper functions
const getItemList = (): any[] => {
  try {
    // Get item definitions from hm-inventory
    const inventoryExports = global.exports['hm-inventory'];
    if (!inventoryExports || !inventoryExports.getItemDefinitions) {
      console.warn('[hm-admin] hm-inventory resource not found or missing exports');
      return [];
    }

    // Get item definitions using proper exports
    const itemDefinitions = inventoryExports.getItemDefinitions();
    
    // Convert item definitions to lightweight format for dropdown (name and label only)
    const items = Object.values(itemDefinitions).map((item: any) => ({
      name: item.name || item.id,
      label: item.label
    }));

    console.log(`[hm-admin] Loaded ${items.length} items from hm-inventory`);
    return items;
  } catch (error) {
    console.error('[hm-admin] Error loading items from hm-inventory:', error);
    return [];
  }
};

const getWeaponList = (): any[] => {
  try {
    // Get weapon definitions from hm-inventory
    const inventoryExports = global.exports['hm-inventory'];
    if (!inventoryExports || !inventoryExports.getWeapons) {
      console.warn('[hm-admin] hm-inventory resource not found or missing weapon exports');
      return [];
    }

    // Get weapon definitions using proper exports
    const weaponDefinitions = inventoryExports.getWeapons();
    
    // Convert weapon definitions to lightweight format for dropdown (name and label only)
    const weaponItems = Object.values(weaponDefinitions).map((weapon: any) => ({
      name: weapon.name || weapon.id,
      label: weapon.label
    }));

    console.log(`[hm-admin] Loaded ${weaponItems.length} weapons from hm-inventory`);
    console.log(`[hm-admin] Sample weapon names:`, weaponItems.slice(0, 5).map(w => w.name));
    return weaponItems;
  } catch (error) {
    console.error('[hm-admin] Error loading weapons from hm-inventory:', error);
    return [];
  }
};

/**
 * Determine the type of an item based on its name
 * @param itemName - The name/ID of the item
 * @returns The item type
 */
const getItemType = (itemName: string): string => {
  // Simple check: if it starts with WEAPON_, it's a weapon
  if (itemName.toUpperCase().startsWith('WEAPON_')) {
    return 'weapon';
  }
  
  // Default to item for everything else
  return 'item';
};

// No fallback functions - will only use actual exports

const giveItemToPlayer = async (adminSource: number, targetId: number, itemName: string, quantity: number) => {
  // Check if target player exists
  if (!GetPlayerName(targetId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Target player not found'
    });
    return;
  }

  const targetName = GetPlayerName(targetId.toString());
  const adminName = GetPlayerName(adminSource.toString());

  try {
    // Get inventory exports
    const inventoryExports = global.exports['hm-inventory'];
    if (!inventoryExports || !inventoryExports.addItem) {
      console.error('[hm-admin] hm-inventory resource not found or missing addItem export');
      emitNet('hm-admin:client:sendOperationResult', adminSource, {
        success: false,
        message: 'Inventory system not available'
      });
      return;
    }

    // Determine item type and validate accordingly
    const itemType = getItemType(itemName);
    let isValid = false;
    let itemLabel = itemName;

    switch (itemType) {
      case 'weapon':
        // Check if weapon definition exists
        if (inventoryExports.getWeaponDefinition) {
          const weaponDef = inventoryExports.getWeaponDefinition(itemName);
          console.log(`[hm-admin] Weapon validation - itemName: ${itemName}, weaponDef:`, weaponDef);
          if (weaponDef) {
            isValid = true;
            itemLabel = weaponDef.label || itemName;
          }
        }
        break;

      case 'item':
        // Check if item definition exists using getItemDefinitions (plural)
        if (inventoryExports.getItemDefinitions) {
          const itemDefinitions = inventoryExports.getItemDefinitions();
          const itemDef = itemDefinitions[itemName];
          if (itemDef) {
            isValid = true;
            itemLabel = itemDef.label || itemName;
          }
        }
        break;

      default:
        // Unknown type - try both definitions first check weapons, then items
        if (inventoryExports.getWeaponDefinition) {
          const weaponDef = inventoryExports.getWeaponDefinition(itemName);
          console.log(`[hm-admin] Default validation - itemName: ${itemName}, weaponDef:`, weaponDef);
          if (weaponDef) {
            isValid = true;
            itemLabel = weaponDef.label || itemName;
          }
        }
        
        // If not a weapon, try as item using getItemDefinitions (plural)
        if (!isValid && inventoryExports.getItemDefinitions) {
          const itemDefinitions = inventoryExports.getItemDefinitions();
          const itemDef = itemDefinitions[itemName];
          if (itemDef) {
            isValid = true;
            itemLabel = itemDef.label || itemName;
          }
        }
        break;
    }

    if (!isValid) {
      emitNet('hm-admin:client:sendOperationResult', adminSource, {
        success: false,
        message: `${itemName} is not a valid item or weapon`
      });
      return;
    }

    // Add the item to the player's inventory
    const success = await inventoryExports.addItem(targetId, itemName, quantity);

    if (success) {
      // Send success result to admin
      emitNet('hm-admin:client:sendOperationResult', adminSource, {
        success: true,
        message: `Successfully given ${quantity}x ${itemLabel} to ${targetName}`
      });

      // Notify target player
      emitNet('hm-admin:client:sendOperationResult', targetId, {
        success: true,
        message: `Admin ${adminName} gave you ${quantity}x ${itemLabel}`
      });

      console.log(`[Admin] ${adminName} successfully gave ${quantity}x ${itemLabel} (${itemName}) to ${targetName}`);
    } else {
      // Send failure result to admin
      emitNet('hm-admin:client:sendOperationResult', adminSource, {
        success: false,
        message: `Failed to give ${quantity}x ${itemLabel} to ${targetName} (inventory full or system error)`
      });

      console.log(`[Admin] ${adminName} failed to give ${quantity}x ${itemLabel} (${itemName}) to ${targetName}`);
    }
  } catch (error) {
    console.error(`[hm-admin] Error giving item to player:`, error);
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: `Error giving item: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
};


const giveWeaponToPlayer = async (adminSource: number, targetId: number, weaponName: string) => {
  // Weapons are just items in the inventory system, so we use the same logic as giveItemToPlayer
  // The quantity is always 1 for weapons since they're not stackable
  await giveItemToPlayer(adminSource, targetId, weaponName, 1);
};

const healPlayer = (adminSource: number, targetId: number, health: number) => {
  // Check if target player exists
  if (!GetPlayerName(targetId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Target player not found'
    });
    return;
  }

  // Validate health amount (1-200)
  const validHealth = Math.max(1, Math.min(200, health));

  // Get target player's ped
  const targetPed = GetPlayerPed(targetId.toString());
  
  // Set player health (100 base health + actual health)
  SetEntityHealth(targetPed, 100 + validHealth);
  
  const targetName = GetPlayerName(targetId.toString());
  const adminName = GetPlayerName(adminSource.toString());
  
  // Send result to admin
  emitNet('hm-admin:client:sendOperationResult', adminSource, {
    success: true,
    message: `Healed ${targetName} to ${validHealth} HP`
  });

  // Notify target player
  emitNet('hm-admin:client:sendOperationResult', targetId, {
    success: true,
    message: `Admin ${adminName} healed you to ${validHealth} HP`
  });

  console.log(`[Admin] ${adminName} healed ${targetName} to ${validHealth} HP`);
};

const teleportPlayerToPlayer = (adminSource: number, targetId: number, destinationPlayerId: number) => {
  // Check if both players exist
  if (!GetPlayerName(targetId.toString()) || !GetPlayerName(destinationPlayerId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'One or both players not found'
    });
    return;
  }

  const targetPed = GetPlayerPed(targetId.toString());
  const destinationPed = GetPlayerPed(destinationPlayerId.toString());
  const destinationCoords = GetEntityCoords(destinationPed, false);

  SetEntityCoords(targetPed, destinationCoords[0], destinationCoords[1], destinationCoords[2], false, false, false, false);

  const targetName = GetPlayerName(targetId.toString());
  const destinationName = GetPlayerName(destinationPlayerId.toString());
  const adminName = GetPlayerName(adminSource.toString());

  emitNet('hm-admin:client:sendOperationResult', adminSource, {
    success: true,
    message: `Teleported ${targetName} to ${destinationName}`
  });

  console.log(`[Admin] ${adminName} teleported ${targetName} to ${destinationName}`);
};

const teleportPlayerToCoords = (adminSource: number, targetId: number, x: number, y: number, z: number) => {
  // Check if target player exists
  if (!GetPlayerName(targetId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Target player not found'
    });
    return;
  }

  const targetPed = GetPlayerPed(targetId.toString());
  SetEntityCoords(targetPed, x, y, z, false, false, false, false);

  const targetName = GetPlayerName(targetId.toString());
  const adminName = GetPlayerName(adminSource.toString());

  emitNet('hm-admin:client:sendOperationResult', adminSource, {
    success: true,
    message: `Teleported ${targetName} to coordinates (${x.toFixed(2)}, ${y.toFixed(2)}, ${z.toFixed(2)})`
  });

  console.log(`[Admin] ${adminName} teleported ${targetName} to (${x}, ${y}, ${z})`);
};

const kickPlayer = (adminSource: number, targetId: number, reason: string) => {
  // Check if target player exists
  if (!GetPlayerName(targetId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Target player not found'
    });
    return;
  }

  const targetName = GetPlayerName(targetId.toString());
  const adminName = GetPlayerName(adminSource.toString());

  // Kick the player
  DropPlayer(targetId.toString(), `Kicked by admin: ${reason}`);

  emitNet('hm-admin:client:sendOperationResult', adminSource, {
    success: true,
    message: `Kicked ${targetName} - Reason: ${reason}`
  });

  console.log(`[Admin] ${adminName} kicked ${targetName} - Reason: ${reason}`);
};

const banPlayer = (adminSource: number, targetId: number, reason: string, duration?: number) => {
  // Check if target player exists
  if (!GetPlayerName(targetId.toString())) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Target player not found'
    });
    return;
  }

  const targetName = GetPlayerName(targetId.toString());
  const adminName = GetPlayerName(adminSource.toString());
  const targetLicense = getPlayerLicense(targetId);

  if (!targetLicense) {
    emitNet('hm-admin:client:sendOperationResult', adminSource, {
      success: false,
      message: 'Could not get player license'
    });
    return;
  }

  // Here you would save the ban to your database
  // For now, we'll just kick the player with a ban message
  const banMessage = duration 
    ? `Banned by admin for ${duration} hours: ${reason}`
    : `Permanently banned by admin: ${reason}`;

  DropPlayer(targetId.toString(), banMessage);

  const durationText = duration ? `${duration} hours` : 'permanently';
  emitNet('hm-admin:client:sendOperationResult', adminSource, {
    success: true,
    message: `Banned ${targetName} ${durationText} - Reason: ${reason}`
  });

  console.log(`[Admin] ${adminName} banned ${targetName} ${durationText} - Reason: ${reason}`);
};

const getDetailedPlayerInfo = (playerId: number): any => {
  if (!GetPlayerName(playerId.toString())) {
    return null;
  }

  const playerPed = GetPlayerPed(playerId.toString());
  const coords = GetEntityCoords(playerPed, false);
  const health = GetEntityHealth(playerPed);
  const armor = GetPedArmour(playerPed);
  const ping = GetPlayerPing(playerId.toString());

  // Get player identifiers
  const numIdentifiers = GetNumPlayerIdentifiers(playerId.toString());
  const identifiers: any = {};

  for (let i = 0; i < numIdentifiers; i++) {
    const identifier = GetPlayerIdentifier(playerId.toString(), i);
    const [type, value] = identifier.split(':');
    identifiers[type] = value;
  }

  return {
    id: playerId,
    name: GetPlayerName(playerId.toString()),
    identifiers: {
      license: identifiers.license || 'Unknown',
      discord: identifiers.discord,
      steam: identifiers.steam,
      ip: identifiers.ip,
      live: identifiers.live,
      xbl: identifiers.xbl,
      fivem: identifiers.fivem
    },
    ping: ping,
    health: health,
    armor: armor,
    position: {
      x: coords[0],
      y: coords[1],
      z: coords[2]
    },
    vehicle: IsPedInAnyVehicle(playerPed, false) ? GetVehiclePedIsIn(playerPed, false) : undefined
  };
};