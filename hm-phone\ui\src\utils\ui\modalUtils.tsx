/**
 * Modal Utilities
 *
 * This module provides utilities for creating and managing modals.
 */
import React, { useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Create a confirmation dialog component
 * @returns A hook that returns a confirmation dialog component and a function to show it
 */
export function useConfirmDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<{
    title: string;
    message: string;
    confirmText: string;
    cancelText: string;
    onConfirm: () => void;
    onCancel?: () => void;
    type?: 'default' | 'danger' | 'warning';
  }>({
    title: 'Confirm',
    message: 'Are you sure?',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    onConfirm: () => {},
    type: 'default'
  });

  const showConfirmDialog = useCallback((newConfig: typeof config) => {
    setConfig(newConfig);
    setIsOpen(true);
  }, []);

  const handleConfirm = useCallback(() => {
    config.onConfirm();
    setIsOpen(false);
  }, [config]);

  const handleCancel = useCallback(() => {
    if (config.onCancel) {
      config.onCancel();
    }
    setIsOpen(false);
  }, [config]);

  const ConfirmDialog = useCallback(() => {
    if (!isOpen) return null;

    const typeStyles = {
      default: {
        button: 'bg-blue-500 hover:bg-blue-600',
        icon: 'fa-question-circle text-blue-500'
      },
      danger: {
        button: 'bg-red-500 hover:bg-red-600',
        icon: 'fa-exclamation-triangle text-red-500'
      },
      warning: {
        button: 'bg-yellow-500 hover:bg-yellow-600',
        icon: 'fa-exclamation-circle text-yellow-500'
      }
    };

    const style = typeStyles[config.type || 'default'];

    const modalContent = (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/75"
            onClick={handleCancel}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#0a0f1a] rounded-lg p-4 w-full max-w-xs mx-4 shadow-xl"
              onClick={e => e.stopPropagation()}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center mr-3">
                  <i className={`fas ${style.icon}`}></i>
                </div>
                <h3 className="text-white text-lg font-medium">{config.title}</h3>
              </div>
              <p className="text-white/80 mb-4">{config.message}</p>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded text-white text-sm transition-colors"
                >
                  {config.cancelText}
                </button>
                <button
                  onClick={handleConfirm}
                  className={`px-4 py-2 ${style.button} rounded text-white text-sm transition-colors`}
                >
                  {config.confirmText}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    );

    return createPortal(modalContent, document.body);
  }, [isOpen, config, handleConfirm, handleCancel]);

  return { ConfirmDialog, showConfirmDialog };
}

/**
 * Create an image preview modal component
 * @returns A hook that returns an image preview modal component and a function to show it
 */
export function useImagePreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<{
    imageUrl: string;
    onDelete?: () => void;
    metadata?: {
      size?: string;
      timestamp?: string;
      dimensions?: string;
    };
  }>({
    imageUrl: ''
  });

  const showImagePreview = useCallback((newConfig: typeof config) => {
    setConfig(newConfig);
    setIsOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const ImagePreviewModal = useCallback(() => {
    if (!isOpen) return null;

    const [isZoomed, setIsZoomed] = useState(false);
    const [loading, setLoading] = useState(true);

    // Load image
    React.useEffect(() => {
      const img = new Image();
      img.src = config.imageUrl;
      img.onload = () => setLoading(false);
    }, [config.imageUrl]);

    const modalContent = (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/95"
            onClick={e => e.target === e.currentTarget && handleClose()}
          >
            {/* Main Content */}
            <div className="relative w-full h-full flex flex-col items-center justify-center p-4">
              {/* Loading Indicator */}
              {loading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-10 h-10 border-3 border-white/20 border-t-white/90 rounded-full animate-spin"></div>
                </div>
              )}

              {/* Image Container with Controls */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: 'spring', duration: 0.5 }}
                className="relative max-w-[90vw] max-h-[90vh] group"
              >
                {/* Image */}
                <img
                  src={config.imageUrl}
                  alt="Full size"
                  className={`max-w-full max-h-[90vh] object-contain ${
                    isZoomed ? 'cursor-zoom-out scale-150' : 'cursor-zoom-in scale-100'
                  }`}
                  onClick={() => setIsZoomed(!isZoomed)}
                  style={{ transition: 'transform 0.3s ease' }}
                />

                {/* Controls Overlay */}
                <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      setIsZoomed(!isZoomed);
                    }}
                    className="w-10 h-10 rounded-full bg-black/50 hover:bg-black/70 transition-colors flex items-center justify-center"
                  >
                    <i
                      className={`fas fa-${
                        isZoomed ? 'search-minus' : 'search-plus'
                      } text-white/90`}
                    ></i>
                  </button>
                  {config.onDelete && (
                    <button
                      onClick={e => {
                        e.stopPropagation();
                        config.onDelete?.();
                        handleClose();
                      }}
                      className="w-10 h-10 rounded-full bg-red-500/50 hover:bg-red-500/70 transition-colors flex items-center justify-center"
                    >
                      <i className="fas fa-trash text-white/90"></i>
                    </button>
                  )}
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      handleClose();
                    }}
                    className="w-10 h-10 rounded-full bg-black/50 hover:bg-black/70 transition-colors flex items-center justify-center"
                  >
                    <i className="fas fa-times text-white/90"></i>
                  </button>
                </div>

                {/* Image Info */}
                {config.metadata && (
                  <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {config.metadata.size && (
                      <div className="text-sm text-white/80 bg-black/50 px-4 py-2 rounded-full">
                        {config.metadata.size}
                      </div>
                    )}
                  </div>
                )}
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );

    return createPortal(modalContent, document.body);
  }, [isOpen, config, handleClose]);

  return { ImagePreviewModal, showImagePreview };
}

/**
 * Create a selection modal component
 * @returns A hook that returns a selection modal component and a function to show it
 */
export function useSelectionModal<T>() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<{
    title: string;
    items: T[];
    renderItem: (item: T, isSelected: boolean) => React.ReactNode;
    onSelect: (selectedItems: T[]) => void;
    onCancel?: () => void;
    multiSelect?: boolean;
    initialSelected?: T[];
    maxSelection?: number;
  }>({
    title: 'Select',
    items: [],
    renderItem: () => null,
    onSelect: () => {},
    multiSelect: false,
    initialSelected: []
  });

  const [selectedItems, setSelectedItems] = useState<T[]>([]);

  const showSelectionModal = useCallback((newConfig: typeof config) => {
    setConfig(newConfig);
    setSelectedItems(newConfig.initialSelected || []);
    setIsOpen(true);
  }, []);

  const handleSelect = useCallback(
    (item: T) => {
      setSelectedItems(prev => {
        // Check if item is already selected
        const isSelected = prev.includes(item);

        // If multi-select is disabled, replace the selection
        if (!config.multiSelect) {
          return isSelected ? [] : [item];
        }

        // For multi-select, toggle the selection
        if (isSelected) {
          return prev.filter(i => i !== item);
        } else {
          // Check max selection limit
          if (config.maxSelection && prev.length >= config.maxSelection) {
            return prev;
          }
          return [...prev, item];
        }
      });
    },
    [config.multiSelect, config.maxSelection]
  );

  const handleConfirm = useCallback(() => {
    config.onSelect(selectedItems);
    setIsOpen(false);
  }, [config, selectedItems]);

  const handleCancel = useCallback(() => {
    if (config.onCancel) {
      config.onCancel();
    }
    setIsOpen(false);
  }, [config]);

  const SelectionModal = useCallback(() => {
    if (!isOpen) return null;

    const modalContent = (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/75"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#0a0f1a] rounded-lg w-full max-w-md mx-4 shadow-xl flex flex-col max-h-[80vh]"
            >
              {/* Header */}
              <div className="flex justify-between items-center px-4 py-3 border-b border-white/10">
                <h3 className="text-white font-medium">{config.title}</h3>
                {config.multiSelect && config.maxSelection && (
                  <span className="text-white/60 text-sm">
                    {selectedItems.length} / {config.maxSelection}
                  </span>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto">
                {config.items.map((item, index) => (
                  <div key={index} onClick={() => handleSelect(item)} className="cursor-pointer">
                    {config.renderItem(item, selectedItems.includes(item))}
                  </div>
                ))}

                {config.items.length === 0 && (
                  <div className="p-4 text-center text-white/60">No items to select</div>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end space-x-2 px-4 py-3 border-t border-white/10">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded text-white text-sm transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirm}
                  disabled={selectedItems.length === 0}
                  className={`px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded text-white text-sm transition-colors ${
                    selectedItems.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  Select
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    );

    return createPortal(modalContent, document.body);
  }, [isOpen, config, selectedItems, handleSelect, handleConfirm, handleCancel]);

  return { SelectionModal, showSelectionModal };
}
