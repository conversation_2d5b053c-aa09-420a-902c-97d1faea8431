import React, { useState } from 'react';
import { motion } from 'framer-motion';
import CreateGroupForm from './CreateGroupForm';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { Job, JobGroup } from '../types/jobCenterTypes';

interface JobGroupListProps {
  job: Job;
  groups: JobGroup[];
  onBack: () => void;
  onCreateGroup: (jobId: number, name: string, isPrivate: boolean, password?: string) => void;
  onJoinGroup: (groupId: number, password?: string) => void;
  onViewGroup: (groupId: number) => void;
  isInAnyGroup: boolean;
}

const JobGroupList: React.FC<JobGroupListProps> = ({
  job,
  groups,
  onBack,
  onCreateGroup,
  onJoinGroup,
  onViewGroup,
  isInAnyGroup
}) => {
  const [showCreateGroupForm, setShowCreateGroupForm] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const formatTimeAgo = (timestamp: number): string => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);

    if (seconds < 60) return `${seconds}s ago`;

    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;

    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  // const formatPayout = (job: Job): string => {
  //   const { amount, type } = job.payout;

  //   switch (type) {
  //     case 'PER_PERSON':
  //       return `$${amount} per person`;
  //     case 'SPLIT':
  //       return `$${amount} split between all members`;
  //     case 'FIXED':
  //       return `$${amount} fixed payout`;
  //     default:
  //       return `$${amount}`;
  //   }
  // };

  const handleJoinGroup = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    if (!group) return;

    if (group.isPrivate) {
      setSelectedGroupId(groupId);
    } else {
      onJoinGroup(groupId);
    }
  };

  const handlePasswordSubmit = () => {
    if (!selectedGroupId) return;

    if (!password.trim()) {
      setPasswordError('Password is required');
      return;
    }

    onJoinGroup(selectedGroupId, password);
    setSelectedGroupId(null);
    setPassword('');
    setPasswordError(null);
  };

  const handleCreateGroup = (name: string, isPrivate: boolean, password?: string) => {
    onCreateGroup(job.id, name, isPrivate, password);
    setShowCreateGroupForm(false);
  };

  if (showCreateGroupForm) {
    return (
      <CreateGroupForm
        job={job}
        onSubmit={handleCreateGroup}
        onCancel={() => setShowCreateGroupForm(false)}
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={onBack}
            className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
          >
            <i className="fas fa-arrow-left"></i>
          </button>
          <h2 className="text-white font-medium ml-2">{job.title}</h2>
        </div>

        {!isInAnyGroup && (
          <button
            onClick={() => setShowCreateGroupForm(true)}
            className="px-3 py-1.5 rounded-lg bg-teal-500 text-white text-sm hover:bg-teal-600 transition-colors"
          >
            <i className="fas fa-plus mr-1"></i>
            Create Group
          </button>
        )}
      </div>

      {/* Group list */}
      <div className="flex-1 overflow-y-auto p-4">
        <h3 className="text-white font-medium mb-3">Available Groups</h3>

        {groups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-white/60 p-4 bg-white/5 rounded-lg">
            <i className="fas fa-users text-3xl mb-2"></i>
            <p className="text-center">No active groups for this job.</p>
            {!isInAnyGroup && (
              <button
                onClick={() => setShowCreateGroupForm(true)}
                className="mt-3 px-4 py-2 rounded-lg bg-teal-500 text-white text-sm hover:bg-teal-600 transition-colors"
              >
                Create a Group
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {/* Sort groups to show user's group at the top */}
            {groups
              .sort((a, b) => {
                const { userProfile } = usePhoneStore.getState();
                const isInGroupA = a.members.some(m => m.id === userProfile.stateid);
                const isInGroupB = b.members.some(m => m.id === userProfile.stateid);

                if (isInGroupA && !isInGroupB) return -1;
                if (!isInGroupA && isInGroupB) return 1;
                return 0;
              })
              .map(group => {
                const { userProfile } = usePhoneStore.getState();
                const isInThisGroup = group.members.some(m => m.id === userProfile.stateid);
                const isLeaderOfThisGroup = group.leader.id === userProfile.stateid;
                return (
                  <div
                    key={group.id}
                    className={`bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border ${
                      isInThisGroup ? 'border-teal-500' : 'border-white/10'
                    } ${
                      isInThisGroup ? 'bg-teal-900/10' : ''
                    } cursor-pointer hover:bg-white/15 transition-colors`}
                    onClick={() => {
                      if (isInThisGroup) {
                        onViewGroup(group.id);
                      } else if (!isInAnyGroup) {
                        handleJoinGroup(group.id);
                      }
                    }}
                  >
                    <div className="p-2.5">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-white font-medium text-base">{group.name}</h3>
                            {isInThisGroup && (
                              <span className="bg-teal-500/20 text-teal-400 text-[10px] px-1.5 py-0.5 rounded-full">
                                {isLeaderOfThisGroup ? 'Your Group (Leader)' : 'Your Group'}
                              </span>
                            )}
                          </div>
                          <p className="text-white/70 text-xs mt-0.5">
                            Leader: {group.leader.name}
                          </p>
                        </div>

                        <div className="flex items-center">
                          {group.isPrivate && (
                            <span className="bg-yellow-500/20 text-yellow-400 text-[10px] px-1.5 py-0.5 rounded-full">
                              <i className="fas fa-lock mr-1"></i>
                              Private
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="mt-1.5 flex flex-wrap gap-x-3 gap-y-1 text-[10px] text-white/60">
                        <div className="flex items-center gap-1">
                          <i className="fas fa-users"></i>
                          <span>
                            {group.members.length}/{job.maxPlayers || '∞'} members
                          </span>
                        </div>

                        <div className="flex items-center gap-1">
                          <i className="fas fa-clock"></i>
                          <span>Created {formatTimeAgo(group.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>

      {/* Password modal */}
      {selectedGroupId && (
        <div
          className="absolute inset-0 bg-black/70 flex items-center justify-center p-4"
          onClick={e => e.stopPropagation()}
        >
          <div className="bg-[#0a0f1a] rounded-lg p-4 w-full max-w-xs border border-white/10">
            <h3 className="text-white font-medium mb-3">Enter Group Password</h3>
            <input
              type="password"
              value={password}
              onChange={e => {
                setPassword(e.target.value);
                setPasswordError(null);
              }}
              placeholder="Password"
              className={`w-full p-3 rounded-lg bg-white/10 text-white border ${
                passwordError ? 'border-red-500' : 'border-white/20'
              } focus:outline-none focus:border-teal-500 mb-2`}
            />
            {passwordError && <p className="text-red-500 text-xs mb-2">{passwordError}</p>}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setSelectedGroupId(null);
                  setPassword('');
                  setPasswordError(null);
                }}
                className="flex-1 py-2 rounded-lg bg-white/10 text-white/70 font-medium hover:bg-white/20 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handlePasswordSubmit}
                className="flex-1 py-2 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors text-sm"
              >
                Join
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default JobGroupList;
