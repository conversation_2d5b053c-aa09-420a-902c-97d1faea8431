import { create } from 'zustand';
import { notesMockData } from '../../../fivem/mockData';

export type NoteCategory = {
  id: string;
  name: string;
  color: string;
};

export type NoteAttachment = {
  id: string;
  type: 'image' | 'audio' | 'file';
  url: string;
  name: string;
  size: number;
};

export interface Note {
  id: number;
  title: string;
  content: string;
  timestamp: number;
  color?: string;
  isPinned?: boolean;
  categories: string[];
  attachments: NoteAttachment[];
  isFavorite?: boolean;
  lastEdited: number;
  createdAt: number;
  fontSize?: number;
  backgroundColor?: string;
}

interface NotesState {
  // State
  notes: Note[];
  categories: NoteCategory[];

  // Original methods
  addNote: (note: Omit<Note, 'id' | 'timestamp' | 'lastEdited' | 'createdAt'>) => void;
  updateNote: (note: Note) => void;
  deleteNote: (id: number) => void;
  togglePin: (id: number) => void;
  toggleFavorite: (id: number) => void;
  addCategory: (category: Omit<NoteCategory, 'id'>) => void;
  deleteCategory: (id: string) => void;
  addAttachment: (noteId: number, attachment: Omit<NoteAttachment, 'id'>) => void;
  removeAttachment: (noteId: number, attachmentId: string) => void;

  // Methods for the global message handler
  setNotes: (notes: Note[]) => void;
}

export const useNotesStore = create<NotesState>(set => ({
  // State
  notes: notesMockData.notes,
  categories: notesMockData.categories,

  addNote: note =>
    set(state => ({
      notes: [
        {
          ...note,
          id: Date.now(),
          timestamp: Date.now(),
          lastEdited: Date.now(),
          createdAt: Date.now()
        },
        ...state.notes
      ]
    })),

  updateNote: updatedNote =>
    set(state => ({
      notes: state.notes.map(note => (note.id === updatedNote.id ? updatedNote : note))
    })),

  deleteNote: id =>
    set(state => ({
      notes: state.notes.filter(note => note.id !== id)
    })),

  togglePin: id =>
    set(state => ({
      notes: state.notes.map(note =>
        note.id === id ? { ...note, isPinned: !note.isPinned } : note
      )
    })),

  toggleFavorite: id =>
    set(state => ({
      notes: state.notes.map(note =>
        note.id === id ? { ...note, isFavorite: !note.isFavorite } : note
      )
    })),

  addCategory: category =>
    set(state => ({
      categories: [...state.categories, { ...category, id: Date.now().toString() }]
    })),

  deleteCategory: id =>
    set(state => ({
      categories: state.categories.filter(category => category.id !== id),
      notes: state.notes.map(note => ({
        ...note,
        categories: note.categories.filter(catId => catId !== id)
      }))
    })),

  addAttachment: (noteId, attachment) =>
    set(state => ({
      notes: state.notes.map(note =>
        note.id === noteId
          ? {
              ...note,
              attachments: [...note.attachments, { ...attachment, id: Date.now().toString() }]
            }
          : note
      )
    })),

  removeAttachment: (noteId, attachmentId) =>
    set(state => ({
      notes: state.notes.map(note =>
        note.id === noteId
          ? {
              ...note,
              attachments: note.attachments.filter(attachment => attachment.id !== attachmentId)
            }
          : note
      )
    })),

  // Methods for the global message handler
  setNotes: (notes: Note[]) => {
    console.log('[NotesStore] Setting notes:', notes.length);
    set({ notes });
  }
}));
