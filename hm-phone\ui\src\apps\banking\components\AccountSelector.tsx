import React, { useState } from 'react';
import { useBankingStore } from '../stores/bankingStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { useNavigation } from '../../../navigation/hooks';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@iconify/react';

const AccountSelector: React.FC = () => {
  const { accounts, selectedAccountId, setSelectedAccount } = useBankingStore();
  const userProfile = usePhoneStore(state => state.userProfile);
  const { openView } = useNavigation();
  const [isExpanded, setIsExpanded] = useState(false);

  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);

  return (
    <div className="relative px-4 pt-2">
      <div className="relative" onClick={() => setIsExpanded(!isExpanded)}>
        {/* Main Card */}
        <div
          className="w-full rounded-xl backdrop-blur-sm border p-4 relative overflow-hidden
            bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700"
        >
          {/* Card Info */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="flex flex-col">
                <span className="text-white text-sm font-medium">{userProfile.name}</span>
                <span className="font-mono text-white/70 text-xs mt-1">
                  {selectedAccount?.number}
                </span>
              </div>
              <div className="text-lg font-bold text-white mt-2">
                {selectedAccount?.balance.toLocaleString('en-US', {
                  style: 'currency',
                  currency: selectedAccount?.currency
                })}
              </div>
              <span className="text-sm text-white mt-1">{selectedAccount?.name}</span>
            </div>
            <div className="flex flex-col items-end gap-2">
              <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                <Icon icon={selectedAccount?.icon || ''} className="w-4 h-4 text-white" />
              </div>
              <span className="text-white text-xs uppercase tracking-wider">
                {selectedAccount?.type}
              </span>
            </div>
          </div>

          {/* Main Actions */}
          <div className="grid grid-cols-3 gap-1.5">
            <button
              onClick={e => {
                e.stopPropagation();
                openView('transfer', { mode: 'send' });
              }}
              className="group relative overflow-hidden rounded-xl bg-white/10 hover:bg-white/15 active:bg-white/20 border border-white/10 transition-all duration-300"
            >
              <div className="px-2 py-2.5 flex flex-col items-center">
                <i className="fas fa-paper-plane mb-1 text-white group-hover:scale-110 transition-transform"></i>
                <span className="text-xs text-white/90">Send</span>
              </div>
            </button>

            <button
              onClick={e => {
                e.stopPropagation();
                openView('transfer', { mode: 'request' });
              }}
              className="group relative overflow-hidden rounded-xl bg-white/10 hover:bg-white/15 active:bg-white/20 border border-white/10 transition-all duration-300"
            >
              <div className="px-2 py-2.5 flex flex-col items-center">
                <i className="fas fa-hand-holding-usd mb-1 text-white group-hover:scale-110 transition-transform"></i>
                <span className="text-xs text-white/90">Request</span>
              </div>
            </button>

            <button
              onClick={e => {
                e.stopPropagation();
                openView('transfer', { mode: 'transfer' });
              }}
              className="group relative overflow-hidden rounded-xl bg-white/10 hover:bg-white/15 active:bg-white/20 border border-white/10 transition-all duration-300"
            >
              <div className="px-2 py-2.5 flex flex-col items-center">
                <i className="fas fa-exchange-alt mb-1 text-white group-hover:scale-110 transition-transform"></i>
                <span className="text-xs text-white/90">Transfer</span>
              </div>
            </button>
          </div>

          {/* Account Selector Indicator */}
          {accounts.length > 1 && (
            <div className="absolute top-3 right-3 flex items-center gap-1.5">
              <span className="text-white/80 text-xs">{accounts.length} accounts</span>
              <Icon
                icon={isExpanded ? 'mdi:chevron-up' : 'mdi:chevron-down'}
                className="text-white/80"
              />
            </div>
          )}
        </div>
      </div>

      {/* Dropdown for Other Cards */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute top-full left-0 right-0 z-10 px-4 pt-2"
          >
            <div className="rounded-xl shadow-lg overflow-hidden space-y-2 p-2 bg-black/20 backdrop-blur-lg">
              {accounts
                .filter(acc => acc.id !== selectedAccountId)
                .map(account => (
                  <div
                    key={account.id}
                    className="w-full p-3 rounded-xl transition-colors relative overflow-hidden cursor-pointer
                      bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700"
                    onClick={() => {
                      setSelectedAccount(account.id);
                      setIsExpanded(false);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                          <Icon icon={account.icon} className="w-4 h-4 text-white" />
                        </div>
                        <div className="text-left">
                          <div className="text-sm font-medium text-white">{account.name}</div>
                          <span className="text-xs text-white/70">{account.number}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-white">
                          {account.balance.toLocaleString('en-US', {
                            style: 'currency',
                            currency: account.currency
                          })}
                        </div>
                        <span className="text-white/80 text-xs uppercase">{account.type}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AccountSelector;
