/**
 * Settings App - Server Side
 *
 * This file handles server-side functionality for the Settings app.
 */

import config from '@shared/config';

/**
 * Initialize the settings app
 */
export function initializeSettingsApp(): void {
    // Register server events
    registerServerEvents();
    // Ensure database tables exist
    ensureDatabaseTables();
    
    // Listen for player loaded event to ensure settings data is available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Settings] Player ${source} loaded, ensuring settings data is available`);
        
        // Initialize player data if needed
        await ensurePlayerSettingsData(source);
    });
}

/**
 * Ensure player has settings data initialized
 */
async function ensurePlayerSettingsData(source: number): Promise<void> {
    try {
        const player = global.getServerPlayerData(source);
        if (!player) return;

        const identifier = player.stateid || player.identifier;
        if (!identifier) return;

        // Get or create default settings for the player
        const settings = await getSettings(identifier);
        console.log(`[Settings] Player ${source} settings initialized`);
    } catch (error) {
        console.error(`[Settings] Error ensuring player settings data:`, error);
    }
}

/**
 * Register server events for the settings app
 */
function registerServerEvents(): void {
    // Register event for getting settings
    onNet('hm-phone:getSettings', async () => {
        const source = global.source;
        try {
            // Get player data from hm-core
            const player = global.getServerPlayerData(source);
            if (!player) {
                emitNet('hm-phone:settingsError', source, 'Player not found or hm-core not loaded');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                emitNet('hm-phone:settingsError', source, 'Player has no stateid or identifier');
                return;
            }

            const playerIdentifier = stateid || identifier;

            const settings = await getSettings(playerIdentifier);

            emitNet('hm-phone:settingsLoaded', source, settings);
        } catch (error) {
            console.error('[Settings] Error getting settings:', error);
            emitNet('hm-phone:settingsError', source, 'Failed to get settings');
        }
    });

    // Register event for saving settings
    onNet('hm-phone:saveSettings', async (settings: any) => {
        const source = global.source;

        try {
            // Get player data from hm-core
            const player = global.getServerPlayerData(source);
            if (!player) {
                emitNet('hm-phone:settingsError', source, 'Player not found or hm-core not loaded');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                emitNet('hm-phone:settingsError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Validate settings
            const validatedSettings = validateSettings(settings);

            // Save the settings
            await saveSettings(playerIdentifier, validatedSettings);

            // Send the settings back to the client
            emitNet('hm-phone:settingsLoaded', source, validatedSettings);
        } catch (error) {
            console.error('[Settings] Error saving settings:', error);
            emitNet('hm-phone:settingsError', source, 'Failed to save settings');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_settings table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    settings JSON NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY idx_identifier (identifier)
                )
            `,
                [],
                () => {
                    // Log success
                }
            );
        } else {
            console.error('[Settings] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[Settings] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get settings for a player
 * @param identifier Player identifier
 * @returns Settings object
 */
async function getSettings(identifier: string): Promise<any> {
    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            return getDefaultSettings();
        }

        let settings = null;

        try {
            // Check if query_async method exists
            if (typeof global.exports.oxmysql.query_async === 'function') {
                // Use query_async if available
                const result = await global.exports.oxmysql.query_async(
                    'SELECT settings FROM phone_settings WHERE identifier = ?',
                    [identifier]
                );

                if (result && result.length > 0) {
                    settings = JSON.parse(result[0].settings);
                }
            } else if (typeof global.exports.oxmysql.query === 'function') {
                // Fall back to query if query_async is not available
                settings = await new Promise<any>(resolve => {
                    global.exports.oxmysql.query(
                        'SELECT settings FROM phone_settings WHERE identifier = ?',
                        [identifier],
                        (result: any) => {
                            if (result && result.length > 0) {
                                try {
                                    resolve(JSON.parse(result[0].settings));
                                } catch (parseError) {
                                    resolve(null);
                                }
                            } else {
                                resolve(null);
                            }
                        }
                    );
                });
            } else {
                return getDefaultSettings();
            }
        } catch (dbError) {
            return getDefaultSettings();
        }

        if (!settings) {
            return getDefaultSettings();
        }

        return settings;
    } catch (error: any) {
        return getDefaultSettings();
    }
}

/**
 * Save settings for a player
 * @param identifier Player identifier
 * @param settings Settings object
 */
async function saveSettings(identifier: string, settings: any): Promise<void> {
    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            return;
        }

        // Convert settings to JSON string
        let settingsJson;
        try {
            settingsJson = JSON.stringify(settings);
        } catch (jsonError) {
            return;
        }

        try {
            // Check if query_async method exists
            if (typeof global.exports.oxmysql.query_async === 'function') {
                // Use query_async if available
                await global.exports.oxmysql.query_async(
                    `
                    INSERT INTO phone_settings (identifier, settings)
                    VALUES (?, ?)
                    ON DUPLICATE KEY UPDATE settings = ?
                `,
                    [identifier, settingsJson, settingsJson]
                );
            } else if (typeof global.exports.oxmysql.query === 'function') {
                // Fall back to query if query_async is not available
                await new Promise<void>(resolve => {
                    global.exports.oxmysql.query(
                        `
                        INSERT INTO phone_settings (identifier, settings)
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE settings = ?
                    `,
                        [identifier, settingsJson, settingsJson],
                        (error: any) => {
                            if (error) {
                                console.error('[Settings] Error executing query:', error);
                            } else {
                                console.log(`[Settings] Settings saved successfully for player ${identifier}`);
                            }
                            resolve();
                        }
                    );
                });
            } else {
                // If neither method is available, log a warning
                console.warn('[Settings] No suitable database query method found, cannot save settings');
            }
        } catch (dbError) {
            // Don't throw, just log the error
        }
    } catch (error: any) {
        // Don't throw, just log the error
    }
}

/**
 * Get default settings
 * @returns Default settings object
 */
function getDefaultSettings(): any {
    return {
        wallpaper: config.ui.defaultSettings.wallpaper || '1',
        ringtone: config.ui.defaultSettings.ringtone || 'default',
        notificationSound: config.ui.defaultSettings.notificationSound || 'default',
        theme: config.ui.defaultSettings.theme || 'dark',
        fontSize: config.ui.defaultSettings.fontSize || 'medium',
        language: config.ui.defaultSettings.language || 'en',
        doNotDisturb:
            config.ui.defaultSettings.doNotDisturb !== undefined ? config.ui.defaultSettings.doNotDisturb : false,
        airplaneMode:
            config.ui.defaultSettings.airplaneMode !== undefined ? config.ui.defaultSettings.airplaneMode : false,
        showNotificationsOnLockScreen:
            config.ui.defaultSettings.showNotificationsOnLockScreen !== undefined
                ? config.ui.defaultSettings.showNotificationsOnLockScreen
                : true,
        vibrate: config.ui.defaultSettings.vibrate !== undefined ? config.ui.defaultSettings.vibrate : true,
        volume: config.ui.defaultSettings.volume !== undefined ? config.ui.defaultSettings.volume : 80,
        brightness: config.ui.defaultSettings.brightness !== undefined ? config.ui.defaultSettings.brightness : 70,
        autoLock: config.ui.defaultSettings.autoLock !== undefined ? config.ui.defaultSettings.autoLock : 30,
        lockScreenType: config.ui.defaultSettings.lockScreenType || 'none'
    };
}

/**
 * Validate settings
 * @param settings Settings object to validate
 * @returns Validated settings object
 */
function validateSettings(settings: any): any {
    // Start with default settings
    const defaultSettings = getDefaultSettings();

    // Create a new object with validated settings
    const validatedSettings: any = { ...defaultSettings };

    // Validate each setting
    if (settings.wallpaper && typeof settings.wallpaper === 'string') {
        validatedSettings.wallpaper = settings.wallpaper;
    }

    if (settings.ringtone && typeof settings.ringtone === 'string') {
        validatedSettings.ringtone = settings.ringtone;
    }

    if (settings.notificationSound && typeof settings.notificationSound === 'string') {
        validatedSettings.notificationSound = settings.notificationSound;
    }

    if (settings.theme && ['light', 'dark', 'system'].includes(settings.theme)) {
        validatedSettings.theme = settings.theme;
    }

    if (settings.fontSize && ['small', 'medium', 'large'].includes(settings.fontSize)) {
        validatedSettings.fontSize = settings.fontSize;
    }

    if (settings.language && typeof settings.language === 'string') {
        validatedSettings.language = settings.language;
    }

    if (typeof settings.doNotDisturb === 'boolean') {
        validatedSettings.doNotDisturb = settings.doNotDisturb;
    }

    if (typeof settings.airplaneMode === 'boolean') {
        validatedSettings.airplaneMode = settings.airplaneMode;
    }

    if (typeof settings.showNotificationsOnLockScreen === 'boolean') {
        validatedSettings.showNotificationsOnLockScreen = settings.showNotificationsOnLockScreen;
    }

    if (typeof settings.vibrate === 'boolean') {
        validatedSettings.vibrate = settings.vibrate;
    }

    if (typeof settings.volume === 'number' && settings.volume >= 0 && settings.volume <= 100) {
        validatedSettings.volume = settings.volume;
    }

    if (typeof settings.brightness === 'number' && settings.brightness >= 0 && settings.brightness <= 100) {
        validatedSettings.brightness = settings.brightness;
    }

    if (typeof settings.autoLock === 'number' && settings.autoLock >= 0) {
        validatedSettings.autoLock = settings.autoLock;
    }

    if (settings.lockScreenType && ['none', 'pin', 'pattern', 'password'].includes(settings.lockScreenType)) {
        validatedSettings.lockScreenType = settings.lockScreenType;
    }

    return validatedSettings;
}
