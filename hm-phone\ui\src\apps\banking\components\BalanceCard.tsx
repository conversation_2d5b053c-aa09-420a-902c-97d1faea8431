import React from 'react';
import { useBankingStore } from '../stores/bankingStore';

const BalanceCard: React.FC = () => {
  const { accounts, selectedAccountId } = useBankingStore();
  const selectedAccount = accounts.find(a => a.id === selectedAccountId);

  const handleTransfer = () => {
    // You could implement a modal or drawer here for transfer functionality
    console.log('Transfer clicked');
  };

  return (
    <div
      className={`bg-gradient-to-br from-${selectedAccount?.color}-500/20 to-${selectedAccount?.color}-500/20 rounded-2xl p-4 border border-white/10 backdrop-blur-sm`}
    >
      <div className="flex flex-col">
        <span className="text-white/60 text-sm">Available Balance</span>
        <span className="text-3xl font-bold text-white mt-1 mb-3">
          €{selectedAccount?.balance.toFixed(2)}
        </span>
        <div className="flex gap-2">
          <button
            onClick={handleTransfer}
            className={`flex-1 bg-${selectedAccount?.color}-500/20 hover:bg-${selectedAccount?.color}-500/30 text-white rounded-xl py-2.5 text-sm font-medium backdrop-blur-sm transition-all duration-300 border border-${selectedAccount?.color}-500/30 hover:border-${selectedAccount?.color}-500/50`}
          >
            <i className="fas fa-exchange-alt mr-2"></i>Transfer
          </button>
        </div>
      </div>
    </div>
  );
};

export default BalanceCard;
