import React from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faGear, 
  faSun, 
  faVolumeUp, 
  faPalette, 
  faLock,
  faChevronRight 
} from '@fortawesome/free-solid-svg-icons';
import { useTabletStore } from '../../stores/tabletStore';

const SettingsApp: React.FC = () => {
  const { config, updateConfig } = useTabletStore();
  const settingsItems = [
    {
      icon: faSun,
      title: 'Brightness',
      value: `${config.brightness}%`,
      onClick: () => {}
    },
    {
      icon: faVolumeUp,
      title: 'Volume',
      value: `${config.volume}%`,
      onClick: () => {}
    },
    {
      icon: faPalette,
      title: 'Theme',
      value: config.theme === 'dark' ? 'Dark' : 'Light',
      onClick: () => updateConfig({ theme: config.theme === 'dark' ? 'light' : 'dark' })
    },
    {
      icon: faLock,
      title: 'Auto Lock',
      value: config.autoLock ? 'On' : 'Off',
      onClick: () => updateConfig({ autoLock: !config.autoLock })
    }
  ];

  return (
    <div className="h-full bg-tablet-background p-4">
      <div className="max-w-sm mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-6"
        >
          <FontAwesomeIcon 
            icon={faGear} 
            className="text-4xl text-tablet-accent-primary mb-2" 
          />
          <h2 className="text-xl font-semibold text-tablet-text-primary">
            Tablet Settings
          </h2>
        </motion.div>

        {/* Settings List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-3"
        >
          {settingsItems.map((item, index) => (
            <motion.button
              key={item.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.05 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={item.onClick}
              className="w-full card card-hover p-4 flex items-center justify-between"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-tablet-accent-primary/20 rounded-full flex items-center justify-center">
                  <FontAwesomeIcon 
                    icon={item.icon} 
                    className="text-tablet-accent-primary" 
                  />
                </div>
                <div className="text-left">
                  <div className="text-tablet-text-primary font-medium">
                    {item.title}
                  </div>
                  <div className="text-tablet-text-muted text-sm">
                    {item.value}
                  </div>
                </div>
              </div>
              <FontAwesomeIcon 
                icon={faChevronRight} 
                className="text-tablet-text-muted" 
              />
            </motion.button>
          ))}
        </motion.div>

        {/* System Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 card p-4"
        >
          <h3 className="text-tablet-text-primary font-medium mb-3">
            System Information
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-tablet-text-muted">Version</span>
              <span className="text-tablet-text-secondary">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-tablet-text-muted">Storage</span>
              <span className="text-tablet-text-secondary">128 GB</span>
            </div>
            <div className="flex justify-between">
              <span className="text-tablet-text-muted">Battery</span>
              <span className="text-tablet-accent-primary">85%</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SettingsApp;
