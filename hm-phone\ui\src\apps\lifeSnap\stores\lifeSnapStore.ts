import { create } from 'zustand';
import { Post, Profile, Story } from '../types/lifeSnapTypes';
import { lifeSnapMockData } from '../../../fivem/mockData';
import { clientRequests } from '../../../fivem/clientRequestSender';

export interface LifeSnapState {
  currentProfile: Profile | null;
  posts: Post[];
  stories: Story[];
  profiles: Profile[]; // Keep this for backward compatibility
  viewedStories: number[];
  loading: boolean;

  actions: {
    getPosts: () => Promise<void>;
    getStories: () => Promise<void>;
  };
  handlers: {
    onSetPosts: (posts: Post[]) => void;
    onSetStories: (stories: Story[]) => void;
    onSetProfiles: (profiles: Profile[]) => void; // Keep this for backward compatibility
    onClearPostContent: () => void; // New handler for cleanup
  };
}

export const useLifeSnapStore = create<LifeSnapState>((set, get) => ({
  currentProfile: lifeSnapMockData.currentProfile,
  posts: lifeSnapMockData.posts,
  stories: lifeSnapMockData.stories,
  profiles: [],
  viewedStories: [],
  loading: false,

  actions: {
    getPosts: async () => {
      // Check if we already have posts or are already loading
      const state = get();
      if (state.loading) {
        console.log('[LifeSnapStore] Posts already loading, skipping request');
        return;
      }

      // If we already have posts, no need to reload
      if (state.posts.length > 0) {
        console.log('[LifeSnapStore] Posts already loaded, skipping request');
        return;
      }

      set({ loading: true });
      try {
        // Use clientRequests with mockdata and storeMethod for browser testing
        await clientRequests.send(
          'lifeSnap',
          'getAllPosts',
          {},
          lifeSnapMockData.posts,
          state.handlers.onSetPosts as (data: unknown) => void
        );
        console.log('[LifeSnapStore] Posts request sent');
      } catch (error) {
        console.error('[LifeSnapStore] Error loading posts:', error);
        set({ loading: false });
      } finally {
        // Reset loading state after a short delay to ensure the UI has time to process
        setTimeout(() => {
          set({ loading: false });
        }, 500);
      }
    },
    getStories: async () => {
      // Check if we already have stories or are already loading
      const state = get();
      if (state.loading) {
        console.log('[LifeSnapStore] Stories already loading, skipping request');
        return;
      }

      // If we already have stories, no need to reload
      if (state.stories.length > 0) {
        console.log('[LifeSnapStore] Stories already loaded, skipping request');
        return;
      }

      set({ loading: true });
      try {
        // Use clientRequests with mockdata and storeMethod for browser testing
        await clientRequests.send(
          'lifeSnap',
          'getAllStories',
          {},
          lifeSnapMockData.stories,
          state.handlers.onSetStories as (data: unknown) => void
        );
        console.log('[LifeSnapStore] Stories request sent');
      } catch (error) {
        console.error('[LifeSnapStore] Error loading stories:', error);
        set({ loading: false });
      } finally {
        // Reset loading state after a short delay to ensure the UI has time to process
        setTimeout(() => {
          set({ loading: false });
        }, 500);
      }
    }
    // getProfiles removed since profile information is now embedded in posts
  },

  handlers: {
    onSetPosts: (posts: Post[]) => {
      set({ posts: posts });
    },
    onSetStories: (stories: Story[]) => {
      set({ stories: stories });
    },
    onSetProfiles: (profiles: Profile[]) => {
      set({ profiles: profiles });
    },
    onClearPostContent: () => {
      // Keep basic post data but clear content to save memory
      const { posts, stories } = get();

      // For posts, keep metadata but clear content fields
      const lightweightPosts = posts.map(post => {
        // Create a new object with the same properties
        const lightPost = { ...post };

        // Replace image URL with placeholder
        lightPost.imageUrl = 'thumbnail';

        // Replace content with placeholder if it exists
        if (lightPost.content) {
          lightPost.content = '...';
        }

        // Replace caption with placeholder
        lightPost.caption = lightPost.caption ? '...' : '';

        // Clear comments array
        lightPost.comments = [];

        return lightPost;
      });

      // For stories, keep metadata but clear content fields
      const lightweightStories = stories.map(story => {
        // Create a new object with the same properties
        const lightStory = { ...story };

        // Replace image URL with placeholder
        lightStory.imageUrl = 'thumbnail';

        // Replace video URL with placeholder if it exists
        if (lightStory.videoUrl) {
          lightStory.videoUrl = 'thumbnail';
        }

        return lightStory;
      });

      // Update the store with lightweight versions
      set({
        posts: lightweightPosts,
        stories: lightweightStories
      });

      console.log('[LifeSnapStore] Cleared post and story content to save memory');
    }
  }
}));
