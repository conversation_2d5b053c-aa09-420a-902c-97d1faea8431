// navigation/provider.tsx
import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNavigationStore } from './navigationStore';
import { useAppStore } from '../common/stores/appStore';

interface NavigationProviderProps {
  children: React.ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { apps } = useAppStore();

  const { currentApp, currentView, currentTab, history } = useNavigationStore();

  // Keep React Router in sync with our navigation state
  useEffect(() => {
    if (!currentApp) {
      // Navigate to home
      if (location.pathname !== '/') {
        navigate('/', { replace: true });
      }
      return;
    }

    // Validate the app exists and is installed
    const appExists = apps.some(
      app =>
        app.path === currentApp &&
        (app.type === 'system' || (app.type === 'store' && app.settings.installed))
    );

    if (!appExists) {
      console.error(`Navigation error: App "${currentApp}" does not exist or is not installed`);
      navigate('/', { replace: true });
      return;
    }

    // Construct the path
    let path = `/${currentApp}`;
    if (currentView && currentView !== 'main') {
      path += `/${currentView}`;
    }

    // Add tab as a query parameter if present
    const searchParams = new URLSearchParams();
    if (currentTab) {
      searchParams.set('tab', currentTab);
    }

    // Get the current entry's data
    const currentEntry = history[history.length - 1];
    if (currentEntry?.data) {
      // Add data as query parameters, excluding internal properties
      Object.entries(currentEntry.data).forEach(([key, value]) => {
        if (!key.startsWith('_')) {
          // Skip internal properties
          searchParams.set(key, String(value));
        }
      });
    }

    const search = searchParams.toString() ? `?${searchParams.toString()}` : '';
    const fullPath = `${path}${search}`;

    // Only navigate if the path has changed
    if (location.pathname + location.search !== fullPath) {
      navigate(fullPath, { replace: true });
    }
  }, [currentApp, currentView, currentTab, history, navigate, location, apps]);

  // Deep links are now handled by the global message handler

  return <>{children}</>;
};
