import React, { useState, useEffect } from 'react';
import {
  PhoneContext,
  registerSetPhoneStateFunction
} from './PhoneContext';
import { usePhoneStore, PhoneStateEnum } from '../common/stores/phoneStateStore';
import { useNavigationStore } from '../navigation/navigationStore';
import { NavigationStateData } from '../navigation/navigationTypes';
import { clientRequests } from '../fivem/clientRequestSender';
import { isBrowser } from '../utils/environment';

export const PhoneProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize with PEEK state in browser mode for testing, CLOSED in FiveM mode
  const [phoneState, setPhoneState] = useState<PhoneStateEnum>(
    isBrowser ? PhoneStateEnum.OPEN : PhoneStateEnum.CLOSED
  );

  // Register the setPhoneState function with the context
  useEffect(() => {
    // Register the setPhoneState function so it can be called from outside the component
    registerSetPhoneStateFunction(setPhoneState);
  }, []);

  // Update store when state changes
  useEffect(() => {
    // Get current store state
    const storeState = usePhoneStore.getState();

    // Show state transition (old -> new)
    console.log(`[Phone] State changed: ${PhoneStateEnum[storeState.phoneState]} -> ${PhoneStateEnum[phoneState]}`);

    // Only update store if the state is different to avoid circular updates
    if (storeState.phoneState !== phoneState) {
      usePhoneStore.getState().actions.setPhoneState(phoneState);
    }

    // Restore navigation state when phone becomes visible
    if (phoneState === PhoneStateEnum.OPEN) {
      try {
        const savedState = localStorage.getItem('phone_navigation_state');
        if (savedState) {
          const stateData = JSON.parse(savedState) as NavigationStateData;
          useNavigationStore.getState().restoreNavigationState(stateData);
        }
      } catch (error) {
        console.error('[PhoneProvider] Error restoring navigation state:', error);
      }
    }
  }, [phoneState]);

  const closePhone = () => {
    useNavigationStore.getState().saveNavigationState();

    if (isBrowser) {
      setPhoneState(PhoneStateEnum.CLOSED);
      return;
    }

    clientRequests
      .send('phone', 'closePhone', {})
      .catch((error: Error) => console.error('[PhoneProvider] Error closing phone:', error));
  };

  return (
    <PhoneContext.Provider
      value={{
        phoneState,
        setPhoneState,
        closePhone
      }}
    >
      {children}
    </PhoneContext.Provider>
  );
};
