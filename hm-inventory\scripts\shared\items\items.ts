import { ItemType, ItemDefinition } from '../types';
import { SlotType } from '../types/inventory.types';
import { weaponDefinitions } from './weapons';

/**
 * All of your game’s item definitions.
 * Add new entries here as needed.
 */
export const itemDefinitions: Record<string, ItemDefinition> = {
    // Consumables
    bread: {
        id: 'bread',
        name: 'bread',
        label: 'Bread',
        description: 'A fresh loaf of bread.',
        type: ItemType.CONSUMABLE,
        weight: 250,
        stackable: true,
        maxStack: 10,
        icon: 'bread.png',
        model: 'prop_food_bs_bag_01', // Bread bag model
        usable: true,
        usage: {
            consumable: true,
            category: 'food',
            effects: {
                hunger: 25,
                health: 5
            },
            animation: {
                dict: 'mp_player_inteat@burger',
                name: 'mp_player_int_eat_burger',
                duration: 3000,
                flag: 49
            },
            progressBar: {
                label: 'Eating bread...',
                duration: 3000,
                color: 'green'
            }
        },
        metadata: {}
    },
    // other items like lockpick, etc..
    lockpick: {
        id: 'lockpick',
        name: 'lockpick',
        label: 'Lockpick',
        description: 'A tool used for picking locks.',
        type: ItemType.GENERAL,
        weight: 100,
        stackable: true,
        maxStack: 20,
        icon: 'lockpick.png',
        model: 'prop_tool_screwdvr01', // Screwdriver model as substitute
        usable: true,
        usage: {
            consumable: false,
            category: 'tool',
            customHandler: 'hm-lockpicking:start',
            cooldown: 1000,
            progressBar: {
                label: 'Preparing lockpick...',
                duration: 1000,
                color: 'blue'
            },
            requirements: {
                location: 'near_door' // Could be checked by the lockpicking system
            }
        },
        metadata: {}
    },
    bandage: {
        id: 'bandage',
        name: 'bandage',
        label: 'Bandage',
        description: 'A simple bandage for treating wounds.',
        type: ItemType.CONSUMABLE,
        weight: 50,
        stackable: true,
        maxStack: 20,
        icon: 'bandage.png',
        model: 'prop_ld_health_pack', // Health pack model
        usable: true,
        usage: {
            consumable: true,
            category: 'medical',
            effects: {
                health: 25
            },
            animation: {
                dict: 'missheistdockssetup1clipboard@base',
                name: 'base',
                duration: 4000,
                flag: 49
            },
            progressBar: {
                label: 'Applying bandage...',
                duration: 4000,
                color: 'red'
            },
            cooldown: 5000 // 5 second cooldown between medical items
        },
        metadata: {}
    },
    
    medkit: {
        id: 'medkit',
        name: 'medkit',
        label: 'Medical Kit',
        description: 'A comprehensive medical kit for serious injuries.',
        type: ItemType.CONSUMABLE,
        weight: 800,
        stackable: true,
        maxStack: 5,
        icon: 'medkit.png',
        model: 'prop_ld_health_pack',
        usable: true,
        usage: {
            consumable: true,
            category: 'medical',
            effects: {
                health: 75
            },
            animation: {
                dict: 'missheistdockssetup1clipboard@base',
                name: 'base',
                duration: 8000,
                flag: 49
            },
            progressBar: {
                label: 'Using medical kit...',
                duration: 8000,
                color: 'red'
            },
            cooldown: 15000 // 15 second cooldown for powerful medical items
        },
        metadata: {}
    },
    // Crafting Materials
    wood_plank: {
        id: 'wood_plank',
        name: 'wood_plank',
        label: 'Wood Plank',
        description: 'A sturdy wooden plank used for crafting.',
        type: ItemType.GENERAL,
        weight: 100,
        stackable: true,
        maxStack: 50,
        icon: 'wood_plank.png',
        metadata: {}
    },
    metal_fragment: {
        id: 'metal_fragment',
        name: 'metal_fragment',
        label: 'Metal Fragment',
        description: 'Processed metal fragment ready for crafting.',
        type: ItemType.GENERAL,
        weight: 150,
        stackable: true,
        maxStack: 50,
        icon: 'metal_fragment.png',
        metadata: {}
    },

    // Craftable Items
    wooden_sword: {
        id: 'wooden_sword',
        name: 'wooden_sword',
        label: 'Wooden Sword',
        description: 'A basic wooden sword for combat.',
        type: ItemType.WEAPON,
        weight: 500,
        stackable: false,
        icon: 'fa-sword',
        durability: 100,
        maxDurability: 100,
        metadata: { damage: 15, weaponType: 'melee' }
    },
    steel_plate: {
        id: 'steel_plate',
        name: 'steel_plate',
        label: 'Steel Plate',
        description: 'A reinforced steel plate.',
        type: ItemType.GENERAL,
        weight: 2000,
        stackable: true,
        maxStack: 20,
        icon: 'steel_plate.png',
        metadata: {}
    },

    // Electronics/Gadgets
    phone: {
        id: 'phone',
        name: 'phone',
        label: 'Smartphone',
        description: 'A modern smartphone.',
        type: ItemType.GADGET,
        weight: 300,
        stackable: false,
        icon: 'phone.png',
        model: 'prop_phone_cs_frank', // Phone model
        usable: true,
        usage: {
            consumable: false,
            category: 'device',
            customHandler: 'hm-phone:open',
            progressBar: {
                label: 'Opening phone...',
                duration: 500,
                color: 'blue'
            }
        },
        durability: 100,
        maxDurability: 100,
        validSlotTypes: [SlotType.PHONE],
        metadata: {}
    },
    tablet: {
        id: 'tablet',
        name: 'tablet',
        label: 'Tablet',
        description: 'A portable tablet device.',
        type: ItemType.GADGET,
        weight: 400,
        stackable: false,
        icon: 'tablet.png',
        durability: 100,
        maxDurability: 100,
        validSlotTypes: [SlotType.TABLET],
        metadata: {}
    },

    // Armor/Protection
    vest: {
        id: 'vest',
        name: 'vest',
        label: 'Bulletproof Vest',
        description: 'Protects against bullets.',
        type: ItemType.GENERAL,
        weight: 1500,
        stackable: false,
        icon: 'vest.png',
        durability: 100,
        maxDurability: 100,
        validSlotTypes: [SlotType.ARMOR],
        metadata: {}
    },
    backpack: {
        id: 'backpack',
        name: 'backpack',
        label: 'Backpack',
        description: 'A sturdy backpack for carrying items.',
        type: ItemType.GENERAL,
        weight: 800,
        stackable: false,
        icon: 'backpack.png',
        durability: 100,
        maxDurability: 100,
        validSlotTypes: [SlotType.BACKPACK],
        metadata: {}
    }
    // …add more items here…
};

/**
 * Lookup a single definition by ID.
 */
export function getItemDefinition(id: string): ItemDefinition | undefined {
    // First check regular items
    if (itemDefinitions[id]) {
        return itemDefinitions[id];
    }
    
    // Then check weapon definitions
    if (weaponDefinitions[id]) {
        return weaponDefinitions[id];
    }
    
    return undefined;
}

/**
 * Lookup a weapon definition by hash (for FiveM weapon hashes).
 * This function searches through weapons to find one with a matching hash.
 */
export function getItemDefinitionByHash(hash: number | string): ItemDefinition | undefined {
    // Convert string hash to number if needed
    const searchHash = typeof hash === 'string' ? parseInt(hash) : hash;
    
    // Find weapon with matching hash in weaponDefinitions
    const weapon = Object.values(weaponDefinitions).find(w => w.hash === searchHash);
    
    return weapon;
}
