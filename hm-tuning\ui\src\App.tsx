import { useState } from "react";

const categories = ['Performance', 'Visual', 'Wheels', 'Colors', 'Extras'];

export default function App() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0]);

  // Detect if running in browser (not FiveM NUI)
  const isBrowser = typeof window !== 'undefined' && !window.invokeNative;
  const bgStyle = isBrowser
    ? {
        background: `url('/bg.jpg') center center / cover no-repeat, #181a1b`,
        minHeight: '100vh',
        height: '100vh',
        width: '100vw',
        position: 'fixed',
        top: 0,
        left: 0,
        color: '#fff',
        overflow: 'hidden',
      }
    : {
        background: '#181a1b',
        minHeight: '100vh',
        height: '100vh',
        width: '100vw',
        position: 'fixed',
        top: 0,
        left: 0,
        color: '#fff',
        overflow: 'hidden',
      };

  return (
    <div className="tuning-app" style={bgStyle}>
      {/* Centered car preview placeholder */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -55%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1
      }}>
        {/* Replace this with actual car preview/canvas/3D view */}
        <div style={{
          width: 600,
          height: 320,
          background: 'rgba(30,30,30,0.7)',
          borderRadius: 24,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: 32,
          color: '#00e1ff',
          boxShadow: '0 8px 32px #000a',
        }}>
          Car Preview
        </div>
      </div>

      {/* Bottom category bar */}
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100vw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0.5rem 0',
        background: 'rgba(24,26,27,0.85)',
        backdropFilter: 'blur(8px)',
        zIndex: 2
      }}>
        <div style={{ display: 'flex', gap: 24 }}>
          {categories.map(cat => (
            <button
              key={cat}
              style={{
                padding: '0.75rem 2rem',
                fontSize: 18,
                border: 'none',
                borderRadius: 8,
                background: cat === selectedCategory ? '#00e1ff' : 'rgba(44,44,44,0.7)',
                color: cat === selectedCategory ? '#181a1b' : '#fff',
                fontWeight: cat === selectedCategory ? 700 : 400,
                cursor: 'pointer',
                transition: 'background 0.2s',
                boxShadow: cat === selectedCategory ? '0 2px 12px #00e1ff44' : undefined
              }}
              onClick={() => setSelectedCategory(cat)}
            >
              {cat}
            </button>
          ))}
        </div>
        {/* Action buttons at right */}
        <div style={{ display: 'flex', gap: 12, marginLeft: 48 }}>
          <button style={{ padding: '0.5rem 1.5rem', borderRadius: 6, border: 'none', background: '#444', color: '#fff', fontWeight: 600, cursor: 'pointer' }}>Reset</button>
          <button style={{ padding: '0.5rem 1.5rem', borderRadius: 6, border: 'none', background: '#00e1ff', color: '#181a1b', fontWeight: 700, cursor: 'pointer' }}>Apply</button>
          <button style={{ padding: '0.5rem 1.5rem', borderRadius: 6, border: 'none', background: '#2ecc40', color: '#fff', fontWeight: 700, cursor: 'pointer' }}>Save</button>
        </div>
      </div>
    </div>
  );
}
