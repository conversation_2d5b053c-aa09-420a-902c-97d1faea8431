import { useState } from "react";

// Tuning categories based on the reference image
const categories = [
  { id: 'body', name: 'BODY', icon: '🔧' },
  { id: 'paint', name: 'PAINT & WRAP', icon: '🎨' },
  { id: 'effects', name: 'EFFECTS', icon: '💫' },
  { id: 'stance', name: 'STANCE', icon: '🏎️' },
  { id: 'exhaust', name: 'EXHAUST SOUND', icon: '🔊' }
];

export default function App() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].id);

  return (
    <div className="w-full h-screen bg-black text-white overflow-hidden relative">
      {/* Top HUD Bar - like in the reference */}
      <div className="absolute top-0 right-0 z-50 p-4">
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-400">💰</span>
            <span className="text-white font-medium">127</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-yellow-400">⚡</span>
            <span className="text-white font-medium">2</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-green-400">💵</span>
            <span className="text-white font-medium">7,425</span>
          </div>
        </div>
      </div>

      {/* Main Content Area - Car will be rendered here by FiveM */}
      <div className="w-full h-full flex items-center justify-center">
        {/* This area will be transparent for the 3D car view */}
        <div className="text-center text-gray-500 text-lg">
          {/* Car rendering area - handled by FiveM client */}
        </div>
      </div>

      {/* Bottom Navigation Bar */}
      <div className="absolute bottom-0 left-0 right-0 z-40">
        {/* CUSTOMIZE Label */}
        <div className="absolute bottom-full left-8 mb-4">
          <h2 className="text-white text-2xl font-bold tracking-wider">CUSTOMIZE</h2>
        </div>

        {/* Category Navigation */}
        <div className="bg-black bg-opacity-80 backdrop-blur-sm border-t border-gray-700">
          <div className="flex items-center justify-center py-4">
            <div className="flex gap-8">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex flex-col items-center gap-2 px-6 py-4 transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-white bg-opacity-20 rounded-lg'
                      : 'hover:bg-white hover:bg-opacity-10 rounded-lg'
                  }`}
                >
                  <div className={`text-3xl ${
                    selectedCategory === category.id ? 'text-white' : 'text-gray-400'
                  }`}>
                    {category.icon}
                  </div>
                  <span className={`text-xs font-medium tracking-wider ${
                    selectedCategory === category.id ? 'text-white' : 'text-gray-400'
                  }`}>
                    {category.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Bottom Control Bar */}
          <div className="flex items-center justify-between px-8 py-3 bg-black bg-opacity-60 border-t border-gray-800">
            <div className="flex items-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xs">❌</span>
                <span className="text-gray-300">SELECT</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xs">⭕</span>
                <span className="text-gray-300">BACK</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xs">🔺</span>
                <span className="text-gray-300">VIEW CAR</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xs">🔄</span>
                <span className="text-gray-300">REV ENGINE</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
