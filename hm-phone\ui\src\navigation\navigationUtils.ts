// navigation/utils.ts
import { useAppStore } from '../common/stores/appStore';
import { App } from '../common/types/appTypes';
import appConfigurations from '../common/stores/appConfigurations';

// Helper function to parse deep links
export function parseDeepLink(link: string) {
  // Parse deep link format: phone://app/view?param1=value1&param2=value2
  const regex = /^phone:\/\/([^/]+)(?:\/([^?]+))?(?:\?(.+))?$/;
  const match = link.match(regex);

  if (!match) return null;

  const [, appPath, view, queryString] = match;

  // Parse query parameters
  const data: Record<string, unknown> = {};
  if (queryString) {
    new URLSearchParams(queryString).forEach((value, key) => {
      // Try to parse numbers and booleans
      if (!isNaN(Number(value))) {
        data[key] = Number(value);
      } else if (value === 'true' || value === 'false') {
        data[key] = value === 'true';
      } else {
        data[key] = value;
      }
    });
  }

  return { appPath, view, data };
}

// Get app by path
export function getAppByPath(appPath: string): App | undefined {
  const { apps } = useAppStore.getState();
  return apps.find(app => app.path === appPath);
}

// Validation functions
export function isValidApp(appPath: string): boolean {
  const app = getAppByPath(appPath);
  if (!app) return false;

  // Check if store app is installed
  if (app.type === 'store' && !app.settings.installed) {
    console.warn(`App "${appPath}" is not installed`);
    return false;
  }

  return true;
}

// Validate if a view exists in an app
export function isValidView(appPath: string, view: string): boolean {
  if (!isValidApp(appPath)) return false;

  // Find the app configuration
  const appConfig = appConfigurations.find(app => app.path === appPath);
  if (!appConfig || !appConfig.views) {
    // If no views are defined in the configuration, assume all views are valid
    return true;
  }

  // Check if the view exists in the app's views
  return view in appConfig.views;
}

// Validate if a tab exists in a view
export function isValidTab(appPath: string, view: string, tab: string): boolean {
  // If tab is undefined or empty, it's always valid (using default tab)
  if (!tab) return true;

  if (!isValidApp(appPath) || !isValidView(appPath, view)) return false;

  // Find the app configuration
  const appConfig = appConfigurations.find(app => app.path === appPath);
  if (!appConfig || !appConfig.views) return true;

  // Get the view configuration
  const viewConfig = appConfig.views[view];
  if (!viewConfig || !viewConfig.tabs) {
    // If no tabs are defined for this view, assume all tabs are valid
    return true;
  }

  // Check if the tab exists in the view's tabs
  return viewConfig.tabs.includes(tab);
}
