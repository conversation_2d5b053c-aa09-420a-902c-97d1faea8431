/**
 * UI Component Utilities
 *
 * This module provides utility functions for common UI components and patterns.
 */
import React, { JSX } from 'react';

/**
 * Render a loading state with customizable spinner
 * @param message - Optional message to display
 * @param size - Size of the spinner (default: 'md')
 * @returns JSX element for loading state
 */
export function renderLoadingState(
  message: string = 'Loading...',
  size: 'sm' | 'md' | 'lg' = 'md'
): JSX.Element {
  const sizeClasses = {
    sm: 'h-6 w-6 border-2',
    md: 'h-8 w-8 border-2',
    lg: 'h-12 w-12 border-3'
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-4">
      <div
        className={`animate-spin rounded-full ${sizeClasses[size]} border-b-2 border-white`}
      ></div>
      {message && <div className="mt-3 text-white/60 text-sm">{message}</div>}
    </div>
  );
}

/**
 * Render an error state with retry option
 * @param message - Error message to display
 * @param onRetry - Optional callback for retry button
 * @returns JSX element for error state
 */
export function renderErrorState(message: string, onRetry?: () => void): JSX.Element {
  return (
    <div className="flex flex-col items-center justify-center h-full p-4 text-center">
      <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mb-3">
        <i className="fas fa-exclamation-triangle text-red-500 text-xl"></i>
      </div>
      <div className="text-white/80 font-medium mb-1">Something went wrong</div>
      <div className="text-white/60 text-sm mb-4">{message}</div>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-full text-white text-sm transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  );
}

/**
 * Render an empty state with customizable message and icon
 * @param message - Message to display
 * @param subMessage - Optional secondary message
 * @param icon - Icon class (FontAwesome)
 * @param action - Optional action button
 * @returns JSX element for empty state
 */
export function renderEmptyState(
  message: string,
  subMessage?: string,
  icon: string = 'fa-inbox',
  action?: { label: string; onClick: () => void }
): JSX.Element {
  return (
    <div className="flex flex-col items-center justify-center h-full p-4 text-center">
      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center mb-4 shadow-inner">
        <i className={`fas ${icon} text-2xl text-white/40`}></i>
      </div>
      <div className="text-lg font-medium text-white/80 mb-1">{message}</div>
      {subMessage && <div className="text-sm text-white/40 mb-4 max-w-xs">{subMessage}</div>}
      {action && (
        <button
          onClick={action.onClick}
          className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-full text-white text-sm transition-colors"
        >
          {action.label}
        </button>
      )}
    </div>
  );
}

/**
 * Render a set of action buttons with consistent styling
 * @param actions - Array of action objects
 * @returns JSX element for action buttons
 */
export function renderActionButtons(
  actions: Array<{
    icon: string;
    label?: string;
    onClick: () => void;
    color?: string;
    disabled?: boolean;
  }>
): JSX.Element {
  return (
    <div className="flex space-x-2">
      {actions.map((action, index) => {
        const colorClasses = action.color || 'bg-white/10 text-white';
        const disabledClasses = action.disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'hover:bg-opacity-80';

        return (
          <button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className={`flex items-center justify-center px-3 py-2 rounded-full ${colorClasses} ${disabledClasses} transition-colors`}
          >
            <i className={`fas ${action.icon} ${action.label ? 'mr-2' : ''}`}></i>
            {action.label && <span>{action.label}</span>}
          </button>
        );
      })}
    </div>
  );
}

/**
 * Render an avatar with initials or image
 * @param name - Name for initials
 * @param imageUrl - Optional image URL
 * @param size - Size of avatar (default: 'md')
 * @returns JSX element for avatar
 */
export function renderAvatar(
  name: string,
  imageUrl?: string,
  size: 'xs' | 'sm' | 'md' | 'lg' = 'md'
): JSX.Element {
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-16 h-16 text-xl'
  };

  if (imageUrl) {
    return (
      <div
        className={`${sizeClasses[size]} rounded-full bg-cover bg-center`}
        style={{ backgroundImage: `url(${imageUrl})` }}
      />
    );
  }

  // Get initials
  const initials = name ? name[0].toUpperCase() : '#';

  return (
    <div
      className={`${sizeClasses[size]} rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center`}
    >
      <span className="text-white font-medium">{initials}</span>
    </div>
  );
}

/**
 * Render a badge with count
 * @param count - Number to display
 * @param maxCount - Maximum count before showing "+"
 * @param color - Badge color (default: 'red')
 * @returns JSX element for badge
 */
export function renderBadge(
  count: number,
  maxCount: number = 99,
  color: 'red' | 'blue' | 'green' | 'yellow' = 'red'
): JSX.Element | null {
  if (count <= 0) return null;

  const colorClasses = {
    red: 'bg-red-500',
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500'
  };

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  return (
    <div
      className={`${colorClasses[color]} text-white text-xs font-bold rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center`}
    >
      {displayCount}
    </div>
  );
}

/**
 * Render a list item with optional avatar, title, subtitle, and actions
 * @param title - Main text
 * @param subtitle - Secondary text
 * @param avatar - Avatar props
 * @param actions - Action buttons
 * @param onClick - Click handler for the entire item
 * @returns JSX element for list item
 */
export function renderListItem(
  title: string,
  subtitle?: string,
  avatar?: { name: string; imageUrl?: string },
  actions?: Array<{ icon: string; onClick: (e: React.MouseEvent) => void; color?: string }>,
  onClick?: () => void
): JSX.Element {
  return (
    <div
      className={`flex items-center px-3 py-2 ${onClick ? 'hover:bg-white/5 cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {avatar && <div className="mr-3">{renderAvatar(avatar.name, avatar.imageUrl)}</div>}

      <div className="flex-1 min-w-0">
        <div className="text-white font-medium text-sm truncate">{title}</div>
        {subtitle && <div className="text-white/60 text-xs truncate">{subtitle}</div>}
      </div>

      {actions && actions.length > 0 && (
        <div className="flex space-x-1">
          {actions.map((action, index) => {
            const colorClasses = action.color || 'bg-white/10 text-white/80';

            return (
              <button
                key={index}
                onClick={e => {
                  e.stopPropagation();
                  action.onClick(e);
                }}
                className={`w-8 h-8 rounded-full ${colorClasses} flex items-center justify-center`}
              >
                <i className={`fas ${action.icon} text-xs`}></i>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
}

/**
 * Render a section header
 * @param title - Section title
 * @param action - Optional action button
 * @returns JSX element for section header
 */
export function renderSectionHeader(
  title: string,
  action?: { label: string; onClick: () => void }
): JSX.Element {
  return (
    <div className="flex justify-between items-center px-3 py-2 sticky top-0 bg-[#0a0f1a] z-10">
      <h3 className="text-white/60 text-xs font-medium">{title}</h3>
      {action && (
        <button onClick={action.onClick} className="text-blue-500 text-xs">
          {action.label}
        </button>
      )}
    </div>
  );
}
