import React from 'react';
import { motion } from 'framer-motion';
import { Contact } from '@shared/types';

interface FavoriteContactsProps {
  contacts: Contact[];
  onCallClick?: (contact: Contact) => void;
  onMessageClick?: (contact: Contact) => void;
  onContactSelect?: (contact: Contact) => void;
}

const FavoriteContacts: React.FC<FavoriteContactsProps> = ({
  contacts,
  onCallClick,
  onMessageClick,
  onContactSelect
}) => {
  const favoriteContacts = contacts.filter(contact => contact.favorite === 1);
  if (favoriteContacts.length === 0) return null;

  return (
    <div className="px-3 mb-2">
      <h2 className="text-white/60 text-xs font-medium sticky top-0 bg-[#0a0f1a] py-1.5">
        Favorites
      </h2>
      <div className="grid grid-cols-4 gap-2">
        {favoriteContacts.map(contact => (
          <motion.div
            key={contact.id}
            whileTap={{ scale: 0.95 }}
            onClick={() => onContactSelect?.(contact)}
            className="flex flex-col items-center w-full cursor-pointer"
          >
            <div className="w-12 h-12 rounded-full flex items-center justify-center mb-1 relative group">
              {contact?.avatar ? (
                <div
                  className="w-full h-full rounded-full bg-cover bg-center"
                  style={{ backgroundImage: `url(${contact.avatar})` }}
                />
              ) : (
                <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <span className="text-white text-base font-medium">
                    {contact?.name ? contact.name.charAt(0).toUpperCase() : '?'}
                  </span>
                </div>
              )}
              <div className="absolute -bottom-1 right-0 flex gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                {onMessageClick && (
                  <div
                    onClick={e => {
                      e.stopPropagation();
                      onMessageClick(contact);
                    }}
                    className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center cursor-pointer"
                  >
                    <i className="fas fa-comment text-xs text-white"></i>
                  </div>
                )}
                {onCallClick && (
                  <div
                    onClick={e => {
                      e.stopPropagation();
                      onCallClick(contact);
                    }}
                    className="w-5 h-5 rounded-full bg-[#0a0f1a] border border-white/20 flex items-center justify-center cursor-pointer"
                  >
                    <i className="fas fa-phone text-[10px] text-white/60" />
                  </div>
                )}
              </div>
            </div>
            <span className="text-white/80 text-xs truncate w-full text-center">
              {contact?.name || 'Unknown'}
            </span>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default FavoriteContacts;
