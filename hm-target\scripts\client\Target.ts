import {
  TargetOption,
  TargetZone,
  TargetEntity,
  TargetState, // TargetState is imported
  RaycastResult,
  Vector3,
} from "../shared/types";
import { targetConfig } from "../shared/config";
import {
  getDistance3D,
  isInZone,
  isValidEntity,
  validateOptions,
} from "../shared/utils";
import {
  isTargetDebugMode,
  isDebugFeatureEnabled,
  visualizeTargetZone,
  removeZoneVisualization,
  renderTargetDebugVisualizations,
  addRaycastDebug,
  addEntityDebug,
  clearEntityDebug,
} from "./debug";
import { zoneOptimizer } from "./zone-optimization";

// Define initialTargetState locally
const initialTargetState: TargetState = {
  active: false,
  currentTarget: undefined,
  showingOptions: false,
  selectedOption: undefined,
  progress: undefined,
};

export class Target {
  private static instance: Target | null = null;
  private state: TargetState = { ...initialTargetState };
  private currentTargetData: TargetState["currentTarget"] | null = null;
  private eventHandlersSetup = false;

  // Consolidated tick system
  private mainSystemTick: number | null = null;
  private lastRaycastUpdate = 0;
  
  // Raycast optimization properties
  private lastCameraPosition: Vector3 = { x: 0, y: 0, z: 0 };
  private lastCameraRotation: Vector3 = { x: 0, y: 0, z: 0 };
  private cachedRaycastResult: RaycastResult | null = null;
  private raycastCacheTime = 0;
  private entityTypeCache: Map<number, { type: string; timestamp: number }> = new Map();
  private entityExistenceCache: Map<number, { exists: boolean; timestamp: number }> = new Map();
  
  private raycastResult: RaycastResult | null = null;
  private entities: Map<number, TargetEntity> = new Map();
  private zones: Map<string, TargetZone> = new Map();
  private globalTypes: Map<string, TargetOption[]> = new Map(); // Global entity type options
  private modelTargets: Map<number, TargetOption[]> = new Map(); // For model-specific targets
  private lastNuiState = "";
  private isNuiOpen = false;
  private nuiFocused = false;
  private lastTargetedEntity = 0;
  private lastDebugEntity = 0;
  private lastTargetState = "";

  private constructor() {
    this.setupEventHandlers();
  }

  public static getInstance(): Target {
    if (!Target.instance) {
      Target.instance = new Target();
    }
    return Target.instance;
  }

  /**
   * Add target options for a specific model (identified by modelHash)
   */
  public addTargetModel(modelHash: number, options: TargetOption[]): void {
    if (typeof modelHash !== "number") {
      console.error("[hm-target] addTargetModel: modelHash must be a number.");
      return;
    }
    if (!validateOptions(options)) {
      console.error("[hm-target] addTargetModel: Invalid options provided.");
      return;
    }
    this.modelTargets.set(modelHash, options);
    if (isTargetDebugMode() && isDebugFeatureEnabled("showEntities")) {
      console.log(
        `[hm-target] Added target options for model hash: ${modelHash}`
      );
    }
  }

  /**
   * Consolidated main system tick - handles all targeting operations
   * Manages different update frequencies within a single tick
   */
  private startMainSystemTick(): void {
    if (this.mainSystemTick) {
      clearTick(this.mainSystemTick);
    }

    this.mainSystemTick = setTick(async () => {
      if (!this.state.active) return;

      const currentTime = GetGameTimer();

      // High frequency operations (every frame)
      this.handleControlDisabling();
      this.handleMouseInteraction();
      this.handleNuiControls();
      this.handleDebugRendering();

      // Lower frequency operations (based on updateFrequency)
      if (currentTime - this.lastRaycastUpdate >= targetConfig.updateFrequency) {
        this.lastRaycastUpdate = currentTime;
        // Only perform raycast and updates when active AND NUI doesn't have focus
        if (!this.nuiFocused) {
          await this.performRaycast();
          this.updateTargetState();
          this.sendNuiUpdate();
        }
      }
    });
  }

  /**
   * Stop the main system tick
   */
  private stopMainSystemTick(): void {
    if (this.mainSystemTick) {
      clearTick(this.mainSystemTick);
      this.mainSystemTick = null;
    }
  }

  /**
   * Handle control disabling (runs every frame)
   */
  private handleControlDisabling(): void {
    if (this.state.active) {
      DisableControlAction(0, 140, true); // Melee light attack
      DisableControlAction(0, 141, true); // Melee heavy attack
      DisableControlAction(0, 142, true); // Melee alternate attack
    }
  }

  /**
   * Handle mouse interaction detection (runs every frame)
   */
  private handleMouseInteraction(): void {
    if (!this.state.active) return;

    if (IsControlJustPressed(0, 24)) {
      if (!this.state.currentTarget) {
        this.setNuiFocus(true);
        return;
      }
      if (this.state.showingOptions) {
        this.setNuiFocus(true);
      } else {
        if (
          this.state.currentTarget.options &&
          this.state.currentTarget.options.length > 0
        ) {
          this.setNuiFocus(true);
          this.state.showingOptions = true;
          this.sendNuiUpdate();
        } else {
          console.log(
            "[hm-target] Clicked with current target, but showingOptions was false and target has no options (or not configured to show on this click). No action."
          );
        }
      }
    }
  }

  /**
   * Handle NUI control disabling (runs every frame when NUI focused)
   */
  private handleNuiControls(): void {
    if (this.nuiFocused) {
      // Disable all player movement and actions while NUI has focus
      DisableControlAction(0, 30, true); // Move left/right
      DisableControlAction(0, 31, true); // Move forward/back
      DisableControlAction(0, 32, true); // Move up/down
      DisableControlAction(0, 33, true); // Move left/right (alternative)
      DisableControlAction(0, 34, true); // Move left
      DisableControlAction(0, 35, true); // Move right
      DisableControlAction(0, 36, true); // Move up
      DisableControlAction(0, 21, true); // Sprint
      DisableControlAction(0, 22, true); // Jump
      DisableControlAction(0, 23, true); // Enter vehicle
      DisableControlAction(0, 75, true); // Exit vehicle
      DisableControlAction(0, 140, true); // Melee light attack
      DisableControlAction(0, 141, true); // Melee heavy attack
      DisableControlAction(0, 142, true); // Melee alternate attack
      DisableControlAction(0, 106, true); // VehicleMouseControlOverride

      // Disable camera controls when NUI has focus
      DisableControlAction(0, 1, true); // Look left/right
      DisableControlAction(0, 2, true); // Look up/down

      // Allow only mouse controls for NUI interaction
      EnableControlAction(0, 24, true); // Left mouse button
      EnableControlAction(0, 25, true); // Right mouse button
    }
  }

  /**
   * Handle debug rendering (runs every frame when debug enabled)
   */
  private handleDebugRendering(): void {
    if (isTargetDebugMode()) {
      // Update debug renderer
      renderTargetDebugVisualizations();

      // Draw raycast debug if we have an active raycast
      if (
        this.raycastResult &&
        this.state.active &&
        this.raycastResult.position
      ) {
        // Use the same camera coordinates as the raycast origin for the debug line
        const camCoordsArray = GetGameplayCamCoord();
        const start: Vector3 = {
          x: camCoordsArray[0],
          y: camCoordsArray[1],
          z: camCoordsArray[2],
        };

        addRaycastDebug(
          start,
          this.raycastResult.position,
          this.raycastResult.hit,
          // Pass hit entity position only if an entity was hit
          this.raycastResult.entity !== 0
            ? this.raycastResult.position
            : undefined
        );
      }
    }
  }

  /**
   * Perform raycast from camera to world (optimized with caching)
   */
  private async performRaycast(): Promise<void> {
    const currentTime = GetGameTimer();
    
    // Step 1: Check if we can use cached results
    if (this.canUseCachedRaycast(currentTime)) {
      this.raycastResult = this.cachedRaycastResult;
      return;
    }

    const ped = PlayerPedId();
    
    // Step 2: Get camera position and rotation
    const camCoordsArray = GetGameplayCamCoord();
    const camPos: Vector3 = {
      x: camCoordsArray[0],
      y: camCoordsArray[1],
      z: camCoordsArray[2],
    };

    const camRotArray = GetGameplayCamRot(2);
    const camRot: Vector3 = {
      x: camRotArray[0],
      y: camRotArray[1],
      z: camRotArray[2],
    };

    // Step 3: Check if camera has moved significantly
    if (!this.hasCameraMovedSignificantly(camPos, camRot)) {
      // Camera hasn't moved much, extend cache validity
      if (this.cachedRaycastResult) {
        this.raycastCacheTime = currentTime;
        this.raycastResult = this.cachedRaycastResult;
        return;
      }
    }

    // Step 4: Update camera tracking
    this.lastCameraPosition = { ...camPos };
    this.lastCameraRotation = { ...camRot };

    // Step 5: Get direction by converting screen center to world coordinate
    const [worldCoordArray] = GetWorldCoordFromScreenCoord(0.5, 0.5);

    if (!worldCoordArray || worldCoordArray.length < 3) {
      console.error(
        "[RAYCAST] Failed to get world coordinate from screen center"
      );
      return;
    }

    const worldCoord: Vector3 = {
      x: worldCoordArray[0],
      y: worldCoordArray[1],
      z: worldCoordArray[2],
    };

    // Step 6: Calculate direction vector
    const dirX = worldCoord.x - camPos.x;
    const dirY = worldCoord.y - camPos.y;
    const dirZ = worldCoord.z - camPos.z;

    // Step 7: Normalize the direction
    const length = Math.sqrt(dirX * dirX + dirY * dirY + dirZ * dirZ);
    const direction: Vector3 = {
      x: dirX / length,
      y: dirY / length,
      z: dirZ / length,
    };

    // Step 8: Calculate destination point
    const destination: Vector3 = {
      x: camPos.x + direction.x * targetConfig.raycastDistance,
      y: camPos.y + direction.y * targetConfig.raycastDistance,
      z: camPos.z + direction.z * targetConfig.raycastDistance,
    };

    // Step 9: Perform raycast - Targeting vehicles, peds, and objects
    const raycastHandle = StartShapeTestRay(
      camPos.x,
      camPos.y,
      camPos.z,
      destination.x,
      destination.y,
      destination.z,
      2 | 4 | 16, // 2 = Vehicles, 4 = Peds, 16 = Objects
      ped,
      4
    );

    const raycastResult = GetShapeTestResult(raycastHandle);

    const hit = raycastResult[1] === 1; // 1 = true, 0 = false
    const endCoords = raycastResult[2]; // coordinates
    const surfaceNormal = raycastResult[3]; // normal vector
    const entityHit = raycastResult[4]; // entity ID

    // Step 10: Optimized entity checking with caching
    if (hit && entityHit !== 0) {
      const entityExists = this.getEntityExistence(entityHit);
      if (entityExists && this.isEntityAnObject(entityHit)) {
        const modelHash = GetEntityModel(entityHit);
        if (this.modelTargets.has(modelHash)) {
          if (isTargetDebugMode()) {
            console.log(
              `[hm-target] Raycast hit object with registered model: ${modelHash}`
            );
          }
        }
      }
    }

    // Step 11: Calculate position and distance if we have valid coordinates
    let position: Vector3;

    if (hit && endCoords && endCoords.length >= 3) {
      position = { x: endCoords[0], y: endCoords[1], z: endCoords[2] };
    } else {
      position = destination;
    }
    
    // Step 12: Calculate distance from player to destination
    const playerCoords = GetEntityCoords(ped, true);
    const distance = getDistance3D(
      { x: playerCoords[0], y: playerCoords[1], z: playerCoords[2] },
      position
    );

    // Step 13: Create and cache the result
    const result: RaycastResult = {
      hit,
      position,
      normal:
        surfaceNormal && surfaceNormal.length >= 3
          ? { x: surfaceNormal[0], y: surfaceNormal[1], z: surfaceNormal[2] }
          : undefined,
      entity: entityHit || 0,
      distance,
    };

    this.raycastResult = result;
    this.cachedRaycastResult = { ...result }; // Deep copy for cache
    this.raycastCacheTime = currentTime;

    // Step 14: Optimized debug logging with entity type caching
    if (this.state.active) {
      this.handleRaycastDebug(entityHit || 0);
    }
  }

  /**
   * Update targeting state based on raycast results
   */
  private resetTargetState(reason?: string): void {
    if (isTargetDebugMode() && reason) {
      console.log(`[hm-target] Clearing target: ${reason}`);
    }
    // Check if currentTargetData and its 'type' property are defined
    if (
      this.currentTargetData &&
      this.currentTargetData.type === "zone" &&
      isDebugFeatureEnabled("showZones")
    ) {
      // Ensure zoneId is correctly accessed if currentTargetData.zone is defined
      if (this.currentTargetData.zone?.id) {
        // removeZoneVisualization(this.currentTargetData.zone.id); // Corrected access
      }
    }

    this.state.currentTarget = undefined;
    this.state.showingOptions = false;
    this.state.selectedOption = undefined;
    this.state.progress = undefined;
    this.currentTargetData = null;
    if (
      this.lastTargetedEntity !== 0 &&
      isDebugFeatureEnabled("showEntities")
    ) {
      clearEntityDebug(this.lastTargetedEntity);
    }
    this.lastTargetedEntity = 0;
  }

  private updateTargetState(): void {
    if (!this.raycastResult) {
      this.resetTargetState("No raycast result");
      return;
    }

    const { hit, entity, distance, position } = this.raycastResult;
    const currentEntity = entity || 0;

    if (!hit || !distance || distance > targetConfig.maxRange || !position) {
      this.resetTargetState("No hit, out of range, or no position");
      return;
    }

    let targetOptions: TargetOption[] | undefined;
    let targetIdentifier: string | number = currentEntity;
    let identifiedByType: "entity" | "zone" | "model" = "entity";
    let modelHashForTarget: number | undefined = undefined;

    if (currentEntity !== 0 && this.entities.has(currentEntity)) {
      targetOptions = this.entities.get(currentEntity)?.options;
      identifiedByType = "entity";
      targetIdentifier = currentEntity;
    } else if (currentEntity !== 0 && IsEntityAnObject(currentEntity)) {
      const modelHash = GetEntityModel(currentEntity);
      modelHashForTarget = modelHash;
      if (this.modelTargets.has(modelHash)) {
        targetOptions = this.modelTargets.get(modelHash);
        targetIdentifier = modelHash;
        identifiedByType = "model";
        if (isTargetDebugMode()) {
          console.log(
            `[hm-target] Using options from model target: ${modelHash}`
          );
        }
      }
    }

    if (!targetOptions) {
      for (const [zoneId, zoneData] of this.zones) {
        if (isInZone(position, zoneData)) {
          targetOptions = zoneData.options;
          targetIdentifier = zoneId;
          identifiedByType = "zone";
          break;
        }
      }
    }

    if (!targetOptions && currentEntity !== 0) {
      let entityTypeStr: string | undefined;
      if (IsEntityAPed(currentEntity)) entityTypeStr = "ped";
      else if (IsEntityAVehicle(currentEntity)) entityTypeStr = "vehicle";
      else if (IsEntityAnObject(currentEntity)) entityTypeStr = "object";

      if (entityTypeStr && this.globalTypes.has(entityTypeStr)) {
        targetOptions = this.globalTypes.get(entityTypeStr);
        targetIdentifier = currentEntity;
        if (identifiedByType !== "model" && identifiedByType !== "zone") {
          identifiedByType = "entity";
        }
      }
    }

    if (targetOptions && targetOptions.length > 0) {
      const actualEntityForTarget =
        currentEntity !== 0 ? currentEntity : undefined;

      this.currentTargetData = {
        entity: actualEntityForTarget,
        modelHash: modelHashForTarget,
        zone:
          identifiedByType === "zone" && typeof targetIdentifier === "string"
            ? this.zones.get(targetIdentifier)
            : undefined,
        type: identifiedByType,
        position: position,
        options: targetOptions,
        distance: distance,
      };

      this.state.currentTarget = {
        entity: actualEntityForTarget,
        modelHash: modelHashForTarget,
        zone:
          identifiedByType === "zone" && typeof targetIdentifier === "string"
            ? this.zones.get(targetIdentifier)
            : undefined,
        position: position,
        options: targetOptions,
        distance: distance,
        type: identifiedByType, // Added type to NUI state
      };
      this.lastTargetedEntity = currentEntity;
      this.state.showingOptions = true;
    } else {
      this.resetTargetState(
        "No valid target options available for the hit entity/zone/model."
      );
    }
  }

  /**
   * Get available options for an entity
   */
  private getEntityOptions(entity: TargetEntity): TargetOption[] {
    return entity.options.filter((option) => {
      if (option.canInteract && !option.canInteract()) return false;
      if (option.job && !this.hasJob(option.job)) return false;
      if (option.gang && !this.hasGang(option.gang)) return false;
      return true;
    });
  }

  /**
   * Get available options for a zone
   */
  private getZoneOptions(zone: TargetZone): TargetOption[] {
    return zone.options.filter((option) => {
      if (option.canInteract && !option.canInteract()) return false;
      if (option.job && !this.hasJob(option.job)) return false;
      if (option.gang && !this.hasGang(option.gang)) return false;
      return true;
    });
  }

  /**
   * Get available options for global entity types (vehicles, peds, etc.)
   */
  private getGlobalEntityOptions(entity: number): TargetOption[] {
    if (!isValidEntity(entity)) return [];

    let entityType: string | null = null;

    try {
      // Determine entity type safely
      if (DoesEntityExist(entity)) {
        if (IsEntityAVehicle(entity)) {
          entityType = "vehicle";
        } else if (IsEntityAPed(entity)) {
          const playerPed = PlayerPedId();
          if (entity === playerPed) {
            // Don't target yourself
            return [];
          } else {
            // Safer player check
            let isPlayer = false;
            try {
              isPlayer = IsPedAPlayer(entity);
            } catch (e) {
              // If IsPedAPlayer fails, treat as NPC
              isPlayer = false;
            }
            entityType = isPlayer ? "player" : "ped";
          }
        } else if (IsEntityAnObject(entity)) {
          entityType = "object";
        }
      }
    } catch (error) {
      console.error(
        `[TARGET] Error determining entity type for ${entity}:`,
        error
      );
      return [];
    }

    if (!entityType) return [];

    // Get options for this entity type
    const options = this.globalTypes.get(entityType);
    if (!options) return [];

    // Filter options based on conditions
    try {
      return options.filter((option) => {
        try {
          if (option.canInteract && !option.canInteract()) return false;
          if (option.job && !this.hasJob(option.job)) return false;
          if (option.gang && !this.hasGang(option.gang)) return false;
          return true;
        } catch (e) {
          console.error(`[TARGET] Error filtering option ${option.id}:`, e);
          return false;
        }
      });
    } catch (error) {
      console.error(
        `[TARGET] Error filtering options for entity ${entity}:`,
        error
      );
      return [];
    }
  }

  /**
   * Check if player has required job
   */
  private hasJob(_jobs: string | string[]): boolean {
    // This would typically integrate with your framework's job system
    // For now, we'll emit an event and expect a response
    return true; // Placeholder
  }

  /**
   * Check if player has required gang
   */
  private hasGang(_gangs: string | string[]): boolean {
    // This would typically integrate with your framework's gang system
    return true; // Placeholder
  }

  /**
   * Send NUI update with current state - optimized to prevent unnecessary updates
   */
  private sendNuiUpdate(): void {
    const shouldShowBasicOverlay = this.state.active;
    const shouldShowOptions = Boolean(
      this.state.active && this.state.showingOptions && this.state.currentTarget
    );

    const currentEntity = this.state.currentTarget?.entity || 0;
    const currentZoneId = this.state.currentTarget?.zone?.id || "none";
    const optionCount = this.state.currentTarget?.options.length || 0;

    const currentStateHash = `${shouldShowBasicOverlay}-${shouldShowOptions}-${currentEntity}-${currentZoneId}-${optionCount}`;

    if (currentStateHash === this.lastNuiState) {
      return;
    }
    this.lastNuiState = currentStateHash;
    if (shouldShowBasicOverlay !== this.isNuiOpen) {
      this.isNuiOpen = shouldShowBasicOverlay;

      if (shouldShowBasicOverlay) {
        const targetData = this.state.currentTarget || {
          position: { x: 0, y: 0, z: 0 }, // Will be centered by CSS
          options: [],
          distance: 0,
        };

        SendNUIMessage({
          action: "showTarget",
          data: {
            target: targetData,
          },
        });
      } else {
        SendNUIMessage({
          action: "hideTarget",
          data: {},
        });
      }
    }

    // Handle options menu visibility (separate from basic overlay)
    if (shouldShowOptions && this.state.currentTarget) {
      SendNUIMessage({
        action: "showOptions",
        data: {
          target: this.state.currentTarget,
        },
      });
    } else if (this.state.active && !shouldShowOptions) {
      SendNUIMessage({
        action: "hideOptions",
        data: {},
      });
    }

    // Send target updates when showing options (reduced frequency)
    if (shouldShowOptions && this.state.currentTarget && this.isNuiOpen) {
      SendNUIMessage({
        action: "updateTarget",
        data: {
          target: this.state.currentTarget,
          selectedOption: this.state.selectedOption,
        },
      });
    }
  }

  /**
   * Setup event handlers for NUI and other interactions
   */
  private setupEventHandlers(): void {
    if (this.eventHandlersSetup) {
      console.log("[hm-target] Event handlers already set up.");
      return;
    }

    console.log("[hm-target] Setting up event handlers...");

    RegisterCommand(
      "+hm-target:toggle",
      () => {
        this.show();
      },
      false
    );

    RegisterCommand(
      "-hm-target:toggle",
      () => {
        this.hide();
      },
      false
    );

    RegisterKeyMapping(
      "+hm-target:toggle",
      "Toggle Target System", // Updated description
      "keyboard",
      "LMENU" // Changed to Left Alt
    );

    on("onClientResourceStart", (resourceName: string) => {
      if (GetCurrentResourceName() === resourceName) {
        console.log(
          `[hm-target][Target.ts] Client resource (${resourceName}) started.`
        );

        // Add global type for Peds
        const pedOptions: TargetOption[] = [
          {
            id: "global_ped_talk",
            label: "Talk",
            icon: "fas fa-comments",
            action: "hm-target:ped:talk", // This event needs to be handled by some other script
            canInteract: (entity?: number) => {
              if (entity && IsEntityAPed(entity)) {
                return entity !== PlayerPedId(); // Don't target self
              }
              return false;
            },
          },
        ];
        this.addGlobalType("ped", pedOptions);
        if (isTargetDebugMode()) {
          console.log("[hm-target] Registered global 'ped' options.");
        }

        // Add global type for Vehicles
        const vehicleOptions: TargetOption[] = [
          {
            id: "global_vehicle_fix",
            label: "Fix Vehicle",
            icon: "fas fa-tools",
            action: "hm-target:vehicle:fix", // This event needs to be handled by some other script
            canInteract: (entity?: number) => {
              if (entity && IsEntityAVehicle(entity)) {
                return GetVehicleEngineHealth(entity) < 900.0; // Only interact if engine is damaged
              }
              return false;
            },
          },
        ];
        this.addGlobalType("vehicle", vehicleOptions);
        if (isTargetDebugMode()) {
          console.log("[hm-target] Registered global 'vehicle' options.");
        }
      }
    });

    RegisterNuiCallbackType("selectOption");
    on(
      "__cfx_nui:selectOption",
      (data: { index: number }, cb: (response: string) => void) => {
        this.selectOption(data.index);
        cb("ok");
      }
    );

    RegisterNuiCallbackType("hideTarget");
    on("__cfx_nui:hideTarget", (_data: any, cb: (response: string) => void) => {
      this.hide();
      cb("ok");
    });
    this.eventHandlersSetup = true; // Mark as setup
  }

  /**
   * Manage NUI focus state for mouse interaction
   */
  private setNuiFocus(enabled: boolean): void {
    if (enabled && !this.nuiFocused) {
      SetCursorLocation(0.5, 0.5);
      SetNuiFocus(true, true);
      SetNuiFocusKeepInput(true); // Keep input focus for mouse interaction
      this.nuiFocused = true;

      // NUI control disabling is now handled by the consolidated tick system
    } else if (!enabled && this.nuiFocused) {
      SetNuiFocus(false, false);
      SetNuiFocusKeepInput(false); // Release input focus
      this.nuiFocused = false;

      // NUI control disabling is now handled by the consolidated tick system
    }
  }

  /**
   * Select an option from the current available options
   */
  private selectOption(index: number): void {
    if (
      !this.state.currentTarget ||
      !this.state.currentTarget.options[index] ||
      !this.currentTargetData
    ) {
      console.error(
        `[hm-target] Invalid option selection: index ${index} or missing target data`
      );
      return;
    }

    const option = this.state.currentTarget.options[index];
    const eventData = {
      target: this.currentTargetData,
      option: option,
      timestamp: Date.now(),
      triggeredBy: "selectOption",
    };

    // Trigger the action event
    try {
      // Pass the entity from currentTargetData if available, otherwise undefined.
      // Also pass the modelHash if available.
      // The action string itself is just an event name, the data is passed as the second argument.
      emit(option.action, eventData);
    } catch (error) {
      console.error(
        `[hm-target] Error triggering event ${option.action}:`,
        error
      );
    }

    // Hide target system after event processing
    setTimeout(() => {
      this.hide();
    }, 50);
  }

  /**
   * Show the targeting system
   */
  public show(): void {
    if (this.state.active) return;
    this.state.active = true;
    this.startMainSystemTick(); // Start the consolidated tick system
    this.sendNuiUpdate(); // Update NUI
  }

  /**
   * Hide the targeting system
   */
  public hide(): void {
    const wasActive = this.state.active;
    this.state.active = false;
    this.state.showingOptions = false;
    this.state.currentTarget = undefined;
    this.lastTargetedEntity = 0;
    this.lastDebugEntity = 0;

    this.stopMainSystemTick(); // Stop the consolidated tick system
    if (this.nuiFocused) {
      this.setNuiFocus(false);
    }
    if (wasActive || this.isNuiOpen) {
      SendNUIMessage({ action: "hideTarget", data: {} });
      SendNUIMessage({ action: "hideOptions", data: {} });
    }
    this.lastNuiState = "";
    this.isNuiOpen = false;
  }

  /**
   * Add a target zone
   */
  public addZone(id: string, zone: Omit<TargetZone, "id">): void {
    if (!validateOptions(zone.options)) {
      console.error(`[hm-target] Invalid options for zone ${id}`);
      return;
    }
    // console.log(`[Target] Adding zone: ${id} at position:`, zone.position);
    const fullZone = { ...zone, id };
    this.zones.set(id, fullZone);
    // console.log(`[Target] Total zones: ${this.zones.size}`);

    // Add to zone optimizer
    zoneOptimizer.addZone(fullZone);

    // Add debug visualization if debug mode is enabled
    if (isTargetDebugMode()) {
      visualizeTargetZone(fullZone);
    }
  }

  /**
   * Remove a target zone
   */
  public removeZone(id: string): void {
    this.zones.delete(id);

    // Remove from zone optimizer
    zoneOptimizer.removeZone(id);

    // Remove debug visualization if debug mode is enabled
    if (isTargetDebugMode()) {
      removeZoneVisualization(id);
    }
  }

  /**
   * Add entity target
   */
  public addEntity(entity: number, options: TargetOption[]): void {
    if (!isValidEntity(entity)) {
      console.error(`[hm-target] Invalid entity: ${entity}`);
      return;
    }
    if (!validateOptions(options)) {
      console.error(`[hm-target] Invalid options for entity ${entity}`);
      return;
    }

    // Generate a unique ID for the entity
    const entityId = `entity_${entity}_${Date.now()}`;
    this.entities.set(entity, {
      id: entityId,
      entity,
      options,
    });
  }

  /**
   * Remove entity target
   */
  public removeEntity(entity: number): void {
    this.entities.delete(entity);
  }

  /**
   * Add global type targeting (applies to all entities of this type)
   */
  public addGlobalType(
    type: "player" | "vehicle" | "ped" | "object",
    options: TargetOption[]
  ): void {
    if (!validateOptions(options)) {
      console.error(`[hm-target] Invalid options for global type ${type}`);
      return;
    }
    this.globalTypes.set(type, options);
  }

  /**
   * Remove global type targeting
   */
  public removeGlobalType(type: "player" | "vehicle" | "ped" | "object"): void {
    this.globalTypes.delete(type);
  }

  /**
   * Get current targeting state
   */
  public getState(): TargetState {
    return { ...this.state };
  }

  /**
   * Check if targeting is active
   */
  public isActive(): boolean {
    return this.state.active;
  }

  /**
   * Draw debug outline for detected entities during scanning
   */
  private drawEntityDebugOutline(entity: number): void {
    if (!DoesEntityExist(entity)) {
      if (this.lastDebugEntity !== 0 && DoesEntityExist(this.lastDebugEntity)) {
        clearEntityDebug(this.lastDebugEntity);
      }
      this.lastDebugEntity = 0;
      return;
    }

    if (!isDebugFeatureEnabled("showEntities")) {
      if (this.lastDebugEntity !== 0 && DoesEntityExist(this.lastDebugEntity)) {
        clearEntityDebug(this.lastDebugEntity);
      }
      this.lastDebugEntity = 0;
      return;
    }

    let determinedEntityType = "UNKNOWN";
    let color = [255, 255, 255]; // White default

    try {
      if (IsEntityAVehicle(entity)) {
        determinedEntityType = "VEHICLE";
        color = [0, 255, 0]; // Green for vehicles
      } else if (IsEntityAPed(entity)) {
        const playerPed = PlayerPedId();
        if (entity === playerPed) {
          determinedEntityType = "SELF";
          color = [255, 255, 255]; // White for self
        } else {
          const playerIndex = NetworkGetPlayerIndexFromPed(entity);
          if (playerIndex !== -1 && NetworkIsPlayerActive(playerIndex)) {
            determinedEntityType = "PLAYER";
            color = [0, 0, 255]; // Blue for players
          } else {
            determinedEntityType = "NPC";
            color = [255, 255, 0]; // Yellow for NPCs
          }
        }
      } else if (IsEntityAnObject(entity)) {
        determinedEntityType = "OBJECT";
        color = [255, 0, 255]; // Magenta for objects
      }
    } catch (error) {
      console.error(
        `[TARGET] Error determining entity type in drawEntityDebugOutline for ${entity}:`,
        error
      );
      if (this.lastDebugEntity !== 0 && DoesEntityExist(this.lastDebugEntity)) {
        clearEntityDebug(this.lastDebugEntity);
      }
      this.lastDebugEntity = 0;
      return;
    }

    const isPedType =
      determinedEntityType === "PLAYER" ||
      determinedEntityType === "NPC" ||
      determinedEntityType === "SELF";

    if (!isPedType) {
      // Not a ped: Draw or update outline
      if (
        this.lastDebugEntity !== 0 &&
        this.lastDebugEntity !== entity &&
        DoesEntityExist(this.lastDebugEntity)
      ) {
        clearEntityDebug(this.lastDebugEntity);
      }
      addEntityDebug(entity, determinedEntityType, color);
      this.lastDebugEntity = entity; // This entity now has the outline
    } else {
      // Is a ped: Do not draw outline for this entity. Clear any existing outline from a non-ped.
      if (this.lastDebugEntity !== 0 && DoesEntityExist(this.lastDebugEntity)) {
        clearEntityDebug(this.lastDebugEntity);
      }
      this.lastDebugEntity = 0; // No outline on current ped, so no active outline entity.
    }
  }

  /**
   * Performs a full cleanup of the Target instance.
   * This is intended to be called when the resource is stopping.
   */
  public performFullCleanup(): void {
    console.log("[hm-target][Target.ts] Performing full cleanup...");

    this.hide();
    this.stopMainSystemTick();

    if (this.nuiFocused) {
      this.setNuiFocus(false);
    }

    this.state = { ...initialTargetState };
    this.currentTargetData = null;
    this.raycastResult = null;
    this.lastRaycastUpdate = 0;
    this.lastNuiState = "";
    this.isNuiOpen = false;
    this.lastDebugEntity = 0;

    // Clear raycast optimization caches
    this.cachedRaycastResult = null;
    this.raycastCacheTime = 0;
    this.entityTypeCache.clear();
    this.entityExistenceCache.clear();
    this.lastCameraPosition = { x: 0, y: 0, z: 0 };
    this.lastCameraRotation = { x: 0, y: 0, z: 0 };

    this.entities.clear();
    this.zones.clear();
    this.globalTypes.clear();
    zoneOptimizer.clear();
  }

  /**
   * Check if cached raycast result can be used
   */
  private canUseCachedRaycast(currentTime: number): boolean {
    const cacheMaxAge = targetConfig.raycastCaching?.maxCacheAge || 100;
    return (
      this.cachedRaycastResult !== null &&
      currentTime - this.raycastCacheTime < cacheMaxAge
    );
  }

  /**
   * Check if camera has moved significantly since last check
   */
  private hasCameraMovedSignificantly(
    currentPos: Vector3,
    currentRot: Vector3
  ): boolean {
    const posThreshold = targetConfig.raycastCaching?.cameraMovementThreshold || 0.1;
    const rotThreshold = targetConfig.raycastCaching?.cameraRotationThreshold || 1.0;

    const posDistance = Math.sqrt(
      Math.pow(currentPos.x - this.lastCameraPosition.x, 2) +
        Math.pow(currentPos.y - this.lastCameraPosition.y, 2) +
        Math.pow(currentPos.z - this.lastCameraPosition.z, 2)
    );

    const rotDistance = Math.sqrt(
      Math.pow(currentRot.x - this.lastCameraRotation.x, 2) +
        Math.pow(currentRot.y - this.lastCameraRotation.y, 2) +
        Math.pow(currentRot.z - this.lastCameraRotation.z, 2)
    );

    return posDistance > posThreshold || rotDistance > rotThreshold;
  }

  /**
   * Get cached entity existence or check and cache it
   */
  private getEntityExistence(entity: number): boolean {
    const currentTime = GetGameTimer();
    const cacheMaxAge = targetConfig.raycastCaching?.entityCacheAge || 1000;
    
    const cached = this.entityExistenceCache.get(entity);
    if (cached && currentTime - cached.timestamp < cacheMaxAge) {
      return cached.exists;
    }

    const exists = DoesEntityExist(entity);
    this.entityExistenceCache.set(entity, { exists, timestamp: currentTime });
    
    // Clean old cache entries (keep cache size manageable)
    const maxCacheSize = targetConfig.raycastCaching?.entityCacheSize || 50;
    if (this.entityExistenceCache.size > maxCacheSize) {
      const cutoffTime = currentTime - cacheMaxAge * 2;
      for (const [id, cache] of this.entityExistenceCache.entries()) {
        if (cache.timestamp < cutoffTime) {
          this.entityExistenceCache.delete(id);
        }
      }
    }

    return exists;
  }

  /**
   * Cached entity type checking - specifically for objects
   */
  private isEntityAnObject(entity: number): boolean {
    const currentTime = GetGameTimer();
    const cacheMaxAge = targetConfig.raycastCaching?.entityCacheAge || 1000;
    
    const cached = this.entityTypeCache.get(entity);
    if (cached && currentTime - cached.timestamp < cacheMaxAge) {
      return cached.type === "OBJECT";
    }

    const isObject = IsEntityAnObject(entity);
    const type = isObject ? "OBJECT" : "OTHER";
    this.entityTypeCache.set(entity, { type, timestamp: currentTime });
    
    // Clean old cache entries
    const maxCacheSize = targetConfig.raycastCaching?.entityCacheSize || 50;
    if (this.entityTypeCache.size > maxCacheSize) {
      const cutoffTime = currentTime - cacheMaxAge * 2;
      for (const [id, cache] of this.entityTypeCache.entries()) {
        if (cache.timestamp < cutoffTime) {
          this.entityTypeCache.delete(id);
        }
      }
    }

    return isObject;
  }

  /**
   * Handle raycast debug logging with optimized entity type detection
   */
  private handleRaycastDebug(entity: number): void {
    // Only log when the entity actually changes (no time-based spam)
    if (entity !== this.lastDebugEntity) {
      if (entity && entity !== 0 && this.getEntityExistence(entity)) {
        let entityType = this.getCachedEntityType(entity);

        if (isDebugFeatureEnabled("showEntities")) {
          console.log(`[RAYCAST DEBUG] Entity Type: ${entityType}, ID: ${entity}`);
          this.drawEntityDebugOutline(entity);
        }
      } else if (this.raycastResult?.hit) {
        console.log(`[RAYCAST DEBUG] Hit world/terrain, no entity`);
      } else {
        console.log(`[RAYCAST DEBUG] No hit detected`);
      }

      this.lastDebugEntity = entity;
    }
  }

  /**
   * Get cached entity type or determine and cache it
   */
  private getCachedEntityType(entity: number): string {
    const currentTime = GetGameTimer();
    const cacheMaxAge = targetConfig.raycastCaching?.entityCacheAge || 1000;
    
    const cached = this.entityTypeCache.get(entity);
    if (cached && currentTime - cached.timestamp < cacheMaxAge) {
      return cached.type;
    }

    let entityType = "UNKNOWN";
    try {
      if (IsEntityAVehicle(entity)) {
        entityType = "VEHICLE";
      } else if (IsEntityAPed(entity)) {
        const playerPed = PlayerPedId();
        if (entity === playerPed) {
          entityType = "SELF";
        } else {
          // Check if it's a player more safely
          let isPlayer = false;
          try {
            isPlayer = IsPedAPlayer(entity);
          } catch (e) {
            isPlayer = false;
          }
          entityType = isPlayer ? "PLAYER" : "NPC";
        }
      } else if (IsEntityAnObject(entity)) {
        entityType = "OBJECT";
      }
    } catch (error) {
      entityType = "ERROR";
    }

    this.entityTypeCache.set(entity, { type: entityType, timestamp: currentTime });
    return entityType;
  }
}

// Ensure this is at the very end of the file, outside the class definition.
if (Target.getInstance()) {
  on("onResourceStop", (resourceName: string) => {
    if (GetCurrentResourceName() === resourceName) {
      console.log(
        `[hm-target][Target.ts] Resource ${resourceName} is stopping. Performing cleanup.`
      );
      Target.getInstance().performFullCleanup();
    }
  });
}

// Export for addTargetModel - this is the standard way for FiveM resources
global.exports(
  "addTargetModel",
  (modelHash: number, options: TargetOption[]) => {
    Target.getInstance().addTargetModel(modelHash, options);
  }
);
