import { create } from 'zustand';
import { App } from '../types/appTypes';
import appConfigurations from './appConfigurations';

interface AppState {
  apps: App[];
  installedApps: App[];
  toggleAppInstallation: (appId: number) => void;
}

export const useAppStore = create<AppState>(set => {
  const initialApps = appConfigurations.map(app =>
    app.type === 'store' ? { ...app, settings: { ...app.settings, installed: true } } : app
  );

  return {
    apps: initialApps,
    installedApps: initialApps.filter(
      app => app.type === 'system' || (app.type === 'store' && app.settings.installed)
    ),
    toggleAppInstallation: appId => {
      set(state => {
        const updatedApps = state.apps.map(app =>
          app.id === appId && app.type === 'store'
            ? { ...app, settings: { ...app.settings, installed: !app.settings.installed } }
            : app
        );
        return {
          apps: updatedApps,
          installedApps: updatedApps.filter(
            app => app.type === 'system' || (app.type === 'store' && app.settings.installed)
          )
        };
      });
    }
  };
});
