import React from 'react';
import { motion } from 'framer-motion';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';

interface MyContactCardSelectableProps {
  onEditProfile: () => void;
  onSelect: () => void;
  isSelected: boolean;
}

const MyContactCardSelectable: React.FC<MyContactCardSelectableProps> = ({
  onEditProfile,
  onSelect,
  isSelected
}) => {
  const { userProfile } = usePhoneStore();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gradient-to-r from-green-900/30 to-green-800/30 rounded-lg p-2 mb-2 mx-2 ${
        isSelected ? 'bg-blue-500/20' : ''
      }`}
      onClick={onSelect}
      aria-selected={isSelected}
    >
      <div className="flex items-center">
        {/* Selection indicator */}
        <div className="mr-2 flex items-center justify-center">
          <div
            className={`w-5 h-5 rounded-full border ${
              isSelected ? 'bg-blue-500 border-blue-500' : 'border-white/40'
            } flex items-center justify-center`}
            onClick={e => {
              e.stopPropagation();
              onSelect();
            }}
          >
            {isSelected && <i className="fas fa-check text-white text-xs"></i>}
          </div>
        </div>

        {/* Avatar - Smaller size */}
        <div className="w-9 h-9 rounded-full overflow-hidden bg-gradient-to-br from-green-600 to-green-700 flex-shrink-0 flex items-center justify-center">
          {userProfile.imageUrl ? (
            <img
              src={userProfile.imageUrl}
              alt={userProfile.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-white text-sm font-medium">
              {userProfile.name ? userProfile.name[0].toUpperCase() : 'M'}
            </span>
          )}
        </div>

        {/* Info - More compact */}
        <div className="ml-2 flex-1 min-w-0">
          <div className="flex items-center">
            <h3 className="text-white font-medium text-sm truncate">
              {userProfile.name || 'My Profile'}
            </h3>
            <span className="ml-1.5 text-xs bg-green-500/20 text-green-400 px-1 py-0.5 rounded-full">
              You
            </span>
          </div>
          <p className="text-white/70 text-xs truncate">
            {userProfile.phoneNumber || 'No phone number'}
          </p>
        </div>

        {/* Edit button - Hidden in selection mode */}
        {!isSelected && (
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={e => {
              e.stopPropagation();
              onEditProfile();
            }}
            className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center ml-1"
          >
            <i className="fas fa-pencil-alt text-white/70 text-xs"></i>
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default MyContactCardSelectable;
