// Utility function to convert DragPanelType to PanelType
import { PanelType, SecondaryPanelType } from '../stores/inventoryStore';
import { DragPanelType } from '@shared';

/**
 * Convert a DragPanelType to a PanelType suitable for use with inventory store functions
 * @param dragPanelType The drag panel type from the UI components
 * @returns A PanelType suitable for inventory operations
 */
export function convertDragPanelToStorePanel(dragPanelType: DragPanelType): PanelType {
  // Direct mappings
  if (dragPanelType === 'main' || dragPanelType === 'quickAccess' || dragPanelType === 'action') {
    return dragPanelType;
  }
  
  // Check if it's already a valid SecondaryPanelType
  if (Object.values(SecondaryPanelType).includes(dragPanelType as SecondaryPanelType)) {
    return dragPanelType as SecondaryPanelType;
  }
  
  // Fallback
  console.warn(`Unhandled panel type conversion: ${dragPanelType}`);
  return 'main';
}
