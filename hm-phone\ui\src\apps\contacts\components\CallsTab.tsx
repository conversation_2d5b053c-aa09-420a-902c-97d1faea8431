import React from 'react';
import { useContactsStore } from '../stores/contactsStore';
import { useNavigation } from '../../../navigation/hooks';
import CompactCallHistory from './CompactCallHistory';
import { useDialerStore } from '../stores/dialerStore';

const CallsTab: React.FC = () => {
  const { actions } = useDialerStore();
  const { calls, contacts } = useContactsStore();
  const { openView } = useNavigation();

  // Debug logging
  console.log('[CallsTab] Rendering with calls:', calls);

  // Handle call press
  const handleCallPress = (number: string, name?: string, photo?: string) => {
    actions.makeCall(number, name, photo);
  };

  // Handle contact press
  const handleContactPress = (number: string, name?: string) => {
    // Find the contact by phone number
    const contact = contacts?.find(c => c.number === number);

    if (contact) {
      // Open the contact detail view
      openView('detail', { id: contact.id });
    } else if (name) {
      // Open the add contact view with pre-filled data
      openView('add', { name, phone: number });
    } else {
      // Open the add contact view with just the phone number
      openView('add', { phone: number });
    }
  };

  return (
    <div className="flex-1 overflow-hidden h-full">
      <CompactCallHistory
        calls={calls}
        onCallPress={handleCallPress}
        onContactPress={handleContactPress}
      />
    </div>
  );
};

export default CallsTab;
