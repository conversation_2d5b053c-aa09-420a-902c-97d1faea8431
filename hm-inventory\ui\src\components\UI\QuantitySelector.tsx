import React, { useState, useEffect } from 'react';

interface QuantitySelectorProps {
  initialValue?: number;
  min?: number;
  max?: number;
  maxStack?: number; // For respecting item maxStack limits
  availableMaterials?: number; // For material-limited crafting
  disabled?: boolean;
  onChange: (value: number) => void;
  presets?: number[]; // Custom preset buttons, defaults to [1, 5, 10, 'Max']
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  initialValue = 1,
  min = 1,
  max = 999,
  maxStack,
  availableMaterials,
  disabled = false,
  onChange,
  presets
}) => {
  const [value, setValue] = useState(initialValue);

  // Calculate the effective maximum considering all constraints
  const effectiveMax = Math.min(
    max,
    maxStack || max,
    availableMaterials || max
  );

  // Default presets: 1, 5, 10, and Max
  const defaultPresets = [1, 5, 10, effectiveMax].filter((preset, index, arr) => 
    preset <= effectiveMax && arr.indexOf(preset) === index
  );
  const actualPresets = presets || defaultPresets;
  useEffect(() => {
    // Clamp value to valid range when constraints change
    const clampedValue = Math.max(min, Math.min(value, effectiveMax));
    if (clampedValue !== value) {
      setValue(clampedValue);
      onChange(clampedValue);
    }
  }, [effectiveMax, min, value, onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value) || min;
    const clampedValue = Math.max(min, Math.min(newValue, effectiveMax));
    setValue(clampedValue);
    onChange(clampedValue);
  };

  const handleIncrement = () => {
    const newValue = Math.min(value + 1, effectiveMax);
    setValue(newValue);
    onChange(newValue);
  };

  const handleDecrement = () => {
    const newValue = Math.max(value - 1, min);
    setValue(newValue);
    onChange(newValue);
  };

  const handlePresetClick = (preset: number) => {
    const clampedValue = Math.max(min, Math.min(preset, effectiveMax));
    setValue(clampedValue);
    onChange(clampedValue);
  };

  const getConstraintText = () => {
    const constraints = [];
    if (maxStack && maxStack < max) constraints.push(`Stack: ${maxStack}`);
    if (availableMaterials && availableMaterials < max) constraints.push(`Materials: ${availableMaterials}`);
    return constraints.length > 0 ? `(${constraints.join(', ')})` : '';
  };  return (
    <div className="flex flex-col space-y-1 items-end">
      {/* Compact Input and +/- Buttons Row */}      <div className="flex items-center space-x-1">
        <button
          onClick={handleDecrement}
          disabled={disabled || value <= min}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700 border border-neutral-600 rounded text-white hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          <i className="fas fa-minus text-[0.6rem]" />
        </button>
        
        <input
          type="number"
          min={min}
          max={effectiveMax}
          value={value}
          onChange={handleInputChange}
          disabled={disabled}
          tabIndex={-1}
          className="w-12 px-1 py-1 bg-neutral-700 border border-neutral-600 rounded text-white text-xs text-center focus:border-green-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        />
        
        <button
          onClick={handleIncrement}
          disabled={disabled || value >= effectiveMax}
          tabIndex={-1}
          className="w-6 h-6 bg-neutral-700 border border-neutral-600 rounded text-white hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          <i className="fas fa-plus text-[0.6rem]" />
        </button>
      </div>
      
      {/* Preset Buttons Row */}
      <div className="flex space-x-1">
        {actualPresets.slice(0, 4).map((preset, index) => (
          <button
            key={index}
            onClick={() => handlePresetClick(preset)}
            disabled={disabled || preset > effectiveMax}
            tabIndex={-1}
            className={`w-6 h-5 text-[0.6rem] rounded transition-colors ${
              value === preset
                ? 'bg-green-600 text-white'
                : 'bg-neutral-600 text-neutral-300 hover:bg-neutral-500'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {preset === effectiveMax && preset > 10 ? 'M' : preset.toString()}
          </button>
        ))}
      </div>      {/* Constraint text in small format */}
      {getConstraintText() && (
        <span className="text-[0.6rem] text-neutral-500 leading-none text-right">{getConstraintText()}</span>
      )}

      {/* Compact validation messages */}
      {availableMaterials && value > availableMaterials && (
        <p className="text-[0.6rem] text-red-400 leading-none text-right">Materials: {availableMaterials}</p>
      )}
      {maxStack && value > maxStack && (
        <p className="text-[0.6rem] text-yellow-400 leading-none text-right">Stack: {maxStack}</p>
      )}
    </div>
  );
};

export default QuantitySelector;
