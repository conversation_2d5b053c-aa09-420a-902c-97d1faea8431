import { create } from 'zustand';
import { Job, JobGroup } from '../types/jobCenterTypes';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';

// Mock data for jobs
const mockJobs: Job[] = [
  {
    id: 1,
    title: 'Delivery Run',
    company: 'PostOP',
    description:
      'Deliver packages to various locations around Los Santos. Simple work, decent pay.',
    requirements: ["Valid driver's license", 'Clean driving record', 'Reliable vehicle'],
    payout: {
      amount: 1500,
      type: 'PER_PERSON'
    },
    location: 'Davis PostOP Warehouse',
    category: 'PRIVATE',
    imageUrl: 'https://picsum.photos/800/500',
    isRecurring: true,
    minPlayers: 1,
    maxPlayers: 4,
    difficulty: 'EASY',
    duration: '30-45 minutes'
  },
  {
    id: 2,
    title: 'Bank Security',
    company: 'Fleeca Bank',
    description:
      'Provide additional security for a high-value transfer. Must be armed and experienced.',
    requirements: [
      'Security license',
      'Firearms permit',
      'Previous security experience',
      'Clean criminal record'
    ],
    payout: {
      amount: 4000,
      type: 'FIXED'
    },
    location: 'Fleeca Bank - Legion Square',
    category: 'PRIVATE',
    imageUrl: 'https://picsum.photos/800/501',
    isRecurring: false,
    minPlayers: 2,
    maxPlayers: 4,
    difficulty: 'MEDIUM',
    duration: '2-3 hours'
  },
  {
    id: 3,
    title: 'Fishing Trip',
    company: 'Self-Employed',
    description:
      'Head out to sea and catch some fish. Sell your catch to local restaurants for profit.',
    payout: {
      amount: 2500,
      type: 'SPLIT'
    },
    location: 'Paleto Bay Pier',
    category: 'FREELANCE',
    imageUrl: 'https://picsum.photos/800/502',
    isRecurring: true,
    minPlayers: 1,
    maxPlayers: 6,
    difficulty: 'EASY',
    duration: '1-2 hours',
    cooldown: 60
  },
  {
    id: 4,
    title: 'Jewelry Store Heist',
    company: 'Anonymous',
    description:
      'High-risk, high-reward operation targeting Vangelico Jewelry Store. Need skilled hackers and getaway drivers.',
    requirements: [
      'Hacking skills',
      'Getaway driving',
      'Firearms experience',
      'Previous heist experience'
    ],
    payout: {
      amount: 25000,
      type: 'SPLIT'
    },
    location: 'Rockford Hills',
    category: 'CRIMINAL',
    imageUrl: 'https://picsum.photos/800/503',
    isRecurring: false,
    minPlayers: 4,
    maxPlayers: 6,
    difficulty: 'EXPERT',
    duration: '1 hour',
    cooldown: 1440 // 24 hours
  },
  {
    id: 5,
    title: 'Paramedic Assistance',
    company: 'Los Santos Medical Services',
    description: 'Assist paramedics during a major event. First aid certification required.',
    requirements: ['First aid certification', 'Clean criminal record', 'Medical knowledge'],
    payout: {
      amount: 3000,
      type: 'PER_PERSON'
    },
    location: 'Pillbox Hill Medical Center',
    category: 'EMERGENCY',
    imageUrl: 'https://picsum.photos/800/504',
    isRecurring: false,
    minPlayers: 2,
    maxPlayers: 8,
    difficulty: 'MEDIUM',
    duration: '4 hours'
  },
  {
    id: 6,
    title: 'Tow Truck Service',
    company: 'Los Santos Customs',
    description: 'Clear accident scenes and tow vehicles to the impound lot or repair shop.',
    requirements: ['Tow truck license', "Valid driver's license"],
    payout: {
      amount: 200,
      type: 'PER_PERSON'
    },
    location: 'Davis Impound Lot',
    category: 'PRIVATE',
    imageUrl: 'https://picsum.photos/800/505',
    isRecurring: true,
    minPlayers: 1,
    maxPlayers: 2,
    difficulty: 'EASY',
    duration: 'Ongoing'
  },
  {
    id: 7,
    title: 'Nightclub Security',
    company: 'Vanilla Unicorn',
    description:
      'Provide security for the nightclub during busy hours. Handle rowdy customers and maintain order.',
    requirements: ['Security license', 'Previous bouncer experience', 'Good physical condition'],
    payout: {
      amount: 2000,
      type: 'PER_PERSON'
    },
    location: 'Strawberry',
    category: 'PRIVATE',
    imageUrl: 'https://picsum.photos/800/506',
    isRecurring: true,
    minPlayers: 2,
    maxPlayers: 4,
    difficulty: 'MEDIUM',
    duration: '6 hours'
  },
  {
    id: 8,
    title: 'Prison Transport',
    company: 'Los Santos Police Department',
    description:
      'Assist in transporting high-risk prisoners from the courthouse to Bolingbroke Penitentiary.',
    requirements: ['Law enforcement background', 'Firearms permit', 'Clean criminal record'],
    payout: {
      amount: 5000,
      type: 'FIXED'
    },
    location: 'Mission Row Police Station',
    category: 'GOVERNMENT',
    imageUrl: 'https://picsum.photos/800/507',
    isRecurring: false,
    minPlayers: 4,
    maxPlayers: 8,
    difficulty: 'HARD',
    duration: '1-2 hours'
  }
];

// Mock data for job groups
const mockGroups: JobGroup[] = [
  {
    id: 1,
    jobId: 3, // Fishing Trip
    name: 'Morning Fishing Crew',
    leader: {
      id: 'TA6789',
      name: 'John Doe',
      phone: '555-0123'
    },
    members: [
      {
        id: 'TA6789',
        name: 'John Doe',
        phone: '555-0123',
        role: 'Leader',
        joinedAt: Date.now() - 3600000 // 1 hour ago
      },
      {
        id: 'TA6790',
        name: 'Jane Smith',
        phone: '555-0124',
        joinedAt: Date.now() - 1800000 // 30 minutes ago
      }
    ],
    status: 'RECRUITING',
    createdAt: Date.now() - 3600000, // 1 hour ago
    isPrivate: false,
    meetupLocation: 'Paleto Bay Gas Station'
  },
  {
    id: 2,
    jobId: 1, // Delivery Run
    name: 'Express Delivery Team',
    leader: {
      id: 'TA6791',
      name: 'Mike Johnson',
      phone: '555-0125'
    },
    members: [
      {
        id: 'TA6791',
        name: 'Mike Johnson',
        phone: '555-0125',
        role: 'Leader',
        joinedAt: Date.now() - 7200000 // 2 hours ago
      }
    ],
    status: 'RECRUITING',
    createdAt: Date.now() - 7200000, // 2 hours ago
    isPrivate: false
  },
  {
    id: 3,
    jobId: 4, // Jewelry Store Heist
    name: 'Professional Crew',
    leader: {
      id: 'TA6792',
      name: 'Alex Rodriguez',
      phone: '555-0126'
    },
    members: [
      {
        id: 'TA6792',
        name: 'Alex Rodriguez',
        phone: '555-0126',
        role: 'Leader',
        joinedAt: Date.now() - 10800000 // 3 hours ago
      },
      {
        id: 'TA6793',
        name: 'Sarah Williams',
        phone: '555-0127',
        role: 'Hacker',
        joinedAt: Date.now() - 9000000 // 2.5 hours ago
      },
      {
        id: 'TA6794',
        name: 'Tom Wilson',
        phone: '555-0128',
        role: 'Driver',
        joinedAt: Date.now() - 7200000 // 2 hours ago
      }
    ],
    status: 'RECRUITING',
    createdAt: Date.now() - 10800000, // 3 hours ago
    isPrivate: true,
    password: 'diamonds',
    notes:
      'Need one more person with firearms experience. Meet at the planning location 30 minutes before start.'
  }
];

interface JobCenterState {
  jobs: Job[];
  groups: JobGroup[];
  filteredJobs: Job[];
  filteredGroups: JobGroup[];
  selectedJob: Job | null;
  selectedGroup: JobGroup | null;
  searchTerm: string;
  loading: boolean;

  // Actions
  setSearchTerm: (term: string) => void;
  selectJob: (jobId: number | null) => void;
  selectGroup: (groupId: number | null) => void;
  createGroup: (jobId: number, name: string, isPrivate: boolean, password?: string) => void;
  leaveGroup: (groupId: number, memberId?: string) => void;
  startJob: (groupId: number) => void;
  completeJob: (groupId: number, success: boolean) => void;
  getMyGroups: () => JobGroup[];
  getGroupsForJob: (jobId: number) => JobGroup[];
  updateGroupSettings: (groupId: number, updates: Partial<JobGroup>) => void;
}

export const useJobCenterStore = create<JobCenterState>((set, get) => ({
  jobs: mockJobs,
  groups: mockGroups,
  filteredJobs: mockJobs,
  filteredGroups: mockGroups,
  selectedJob: null,
  selectedGroup: null,
  searchTerm: '',
  loading: false,

  setSearchTerm: (term: string) => {
    set(state => {
      const searchTerm = term.toLowerCase();
      const filteredJobs = state.jobs.filter(job => {
        return (
          job.title.toLowerCase().includes(searchTerm) ||
          job.company.toLowerCase().includes(searchTerm) ||
          job.description.toLowerCase().includes(searchTerm)
        );
      });

      const filteredGroups = state.groups.filter(group => {
        const job = state.jobs.find(j => j.id === group.jobId);
        if (!job) return false;

        return (
          group.name.toLowerCase().includes(searchTerm) ||
          job.title.toLowerCase().includes(searchTerm) ||
          job.company.toLowerCase().includes(searchTerm) ||
          group.leader.name.toLowerCase().includes(searchTerm)
        );
      });

      return { searchTerm: term, filteredJobs, filteredGroups };
    });
  },

  selectJob: (jobId: number | null) => {
    if (jobId === null) {
      set({ selectedJob: null });
      return;
    }

    const job = get().jobs.find(job => job.id === jobId);
    set({ selectedJob: job || null });
  },

  selectGroup: (groupId: number | null) => {
    if (groupId === null) {
      set({ selectedGroup: null });
      return;
    }

    const group = get().groups.find(group => group.id === groupId);
    set({ selectedGroup: group || null });
  },

  createGroup: (jobId, name, isPrivate, password) => {
    const { userProfile } = usePhoneStore.getState();
    const job = get().jobs.find(j => j.id === jobId);

    if (!job) return;

    const newGroup: JobGroup = {
      id: Date.now(),
      jobId,
      name,
      leader: {
        id: userProfile.stateid,
        name: userProfile.name,
        phone: userProfile.phoneNumber ?? ''
      },
      members: [
        {
          id: userProfile.stateid,
          name: userProfile.name,
          phone: userProfile.phoneNumber ?? '',
          role: 'Leader',
          joinedAt: Date.now()
        }
      ],
      status: 'RECRUITING',
      createdAt: Date.now(),
      isPrivate,
      password
    };

    set(state => ({
      groups: [...state.groups, newGroup],
      filteredGroups: [...state.filteredGroups, newGroup]
    }));

    // Simulate server interaction
    fetch('https://hm-phone/createJobGroup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newGroup)
    }).catch(() => {
      // Silently fail in development
      console.log('Job group created:', newGroup);
    });
  },

  leaveGroup: (groupId, memberId) => {
    const { userProfile } = usePhoneStore.getState();
    const group = get().groups.find(g => g.id === groupId);

    if (!group) return;

    // If memberId is provided, it's a kick action (leader kicking a member)
    if (memberId) {
      // Only the leader can kick members
      if (group.leader.id !== userProfile.stateid) {
        return;
      }

      // Can't kick yourself as leader
      if (memberId === userProfile.stateid) {
        return;
      }

      // Remove the member
      set(state => ({
        groups: state.groups.map(g =>
          g.id === groupId ? { ...g, members: g.members.filter(m => m.id !== memberId) } : g
        ),
        filteredGroups: state.filteredGroups.map(g =>
          g.id === groupId ? { ...g, members: g.members.filter(m => m.id !== memberId) } : g
        )
      }));

      // Simulate server interaction
      fetch('https://hm-phone/kickGroupMember', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ groupId, memberId })
      }).catch(() => {
        // Silently fail in development
        console.log('Kicked member from group:', groupId, memberId);
      });

      return;
    }

    // Regular leave group action (no memberId provided)
    // Check if member of the group
    if (!group.members.some(m => m.id === userProfile.stateid)) {
      return;
    }

    // If leader, disband the group
    if (group.leader.id === userProfile.stateid) {
      set(state => ({
        groups: state.groups.filter(g => g.id !== groupId),
        filteredGroups: state.filteredGroups.filter(g => g.id !== groupId)
      }));

      // Simulate server interaction
      fetch('https://hm-phone/disbandJobGroup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ groupId })
      }).catch(() => {
        // Silently fail in development
        console.log('Disbanded job group:', groupId);
      });

      return;
    }

    // Otherwise, just leave the group
    set(state => ({
      groups: state.groups.map(g =>
        g.id === groupId
          ? { ...g, members: g.members.filter(m => m.id !== userProfile.stateid) }
          : g
      ),
      filteredGroups: state.filteredGroups.map(g =>
        g.id === groupId
          ? { ...g, members: g.members.filter(m => m.id !== userProfile.stateid) }
          : g
      )
    }));

    // Simulate server interaction
    fetch('https://hm-phone/leaveJobGroup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ groupId, memberId: userProfile.stateid })
    }).catch(() => {
      // Silently fail in development
      console.log('Left job group:', groupId);
    });
  },

  startJob: groupId => {
    const group = get().groups.find(g => g.id === groupId);
    const { userProfile } = usePhoneStore.getState();

    if (!group || group.leader.id !== userProfile.stateid) return;

    const job = get().jobs.find(j => j.id === group.jobId);
    if (!job) return;

    // Check minimum players
    if (job.minPlayers && group.members.length < job.minPlayers) {
      return;
    }

    set(state => ({
      groups: state.groups.map(g =>
        g.id === groupId ? { ...g, status: 'IN_PROGRESS', startedAt: Date.now() } : g
      ),
      filteredGroups: state.filteredGroups.map(g =>
        g.id === groupId ? { ...g, status: 'IN_PROGRESS', startedAt: Date.now() } : g
      )
    }));

    // Simulate server interaction
    fetch('https://hm-phone/startJob', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ groupId, jobId: group.jobId })
    }).catch(() => {
      // Silently fail in development
      console.log('Started job:', group.jobId, 'with group:', groupId);
    });
  },

  completeJob: (groupId, success) => {
    const group = get().groups.find(g => g.id === groupId);
    const { userProfile } = usePhoneStore.getState();

    if (!group || group.leader.id !== userProfile.stateid) return;

    set(state => ({
      groups: state.groups.map(g =>
        g.id === groupId
          ? {
              ...g,
              status: success ? 'COMPLETED' : 'FAILED',
              completedAt: Date.now()
            }
          : g
      ),
      filteredGroups: state.filteredGroups.map(g =>
        g.id === groupId
          ? {
              ...g,
              status: success ? 'COMPLETED' : 'FAILED',
              completedAt: Date.now()
            }
          : g
      )
    }));

    // Simulate server interaction
    fetch('https://hm-phone/completeJob', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ groupId, success })
    }).catch(() => {
      // Silently fail in development
      console.log('Completed job group:', groupId, 'Success:', success);
    });
  },

  getMyGroups: () => {
    const { userProfile } = usePhoneStore.getState();
    return get().groups.filter(group =>
      group.members.some(member => member.id === userProfile.stateid)
    );
  },

  getGroupsForJob: jobId => {
    return get().groups.filter(group => group.jobId === jobId && group.status === 'RECRUITING');
  },

  updateGroupSettings: (groupId, updates) => {
    const group = get().groups.find(g => g.id === groupId);
    const { userProfile } = usePhoneStore.getState();

    if (!group || group.leader.id !== userProfile.stateid) return;

    set(state => ({
      groups: state.groups.map(g => (g.id === groupId ? { ...g, ...updates } : g)),
      filteredGroups: state.filteredGroups.map(g => (g.id === groupId ? { ...g, ...updates } : g))
    }));

    // Simulate server interaction
    fetch('https://hm-phone/updateJobGroup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ groupId, updates })
    }).catch(() => {
      // Silently fail in development
      console.log('Updated job group settings:', groupId, updates);
    });
  }
}));
