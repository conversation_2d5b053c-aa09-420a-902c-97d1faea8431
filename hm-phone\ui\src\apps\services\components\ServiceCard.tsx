import React from 'react';
import { Service } from '../types/servicesTypes';
import { motion } from 'framer-motion';

interface ServiceCardProps {
  service: Service;
  onCallClick: (service: Service) => void;
  onMessageClick: (service: Service) => void;
  onFavoriteToggle: (serviceId: number) => void;
  isFavorite: boolean;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onCallClick,
  onMessageClick,
  onFavoriteToggle,
  isFavorite
}) => {
  return (
    <motion.div
      whileTap={{ scale: 0.98 }}
      className="bg-[#1a1a1a] rounded-lg overflow-hidden mb-3 shadow-md"
    >
      <div className="p-3">
        <div className="flex items-center mb-2">
          <div
            className={`w-10 h-10 rounded-full bg-${service.color}-500/20 flex items-center justify-center mr-3`}
          >
            <i className={`fas fa-${service.icon} text-${service.color}-500`}></i>
          </div>
          <div className="flex-1">
            <h3 className="text-white font-medium text-base">{service.name}</h3>
            <p className="text-white/60 text-xs">{service.category}</p>
          </div>
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={e => {
              e.stopPropagation();
              onFavoriteToggle(service.id);
            }}
            className="w-8 h-8 flex items-center justify-center text-white/60 hover:text-white"
          >
            <i
              className={`fas fa-${isFavorite ? 'star text-yellow-500' : 'star text-white/30'}`}
            ></i>
          </motion.button>
        </div>

        <p className="text-white/80 text-sm mb-2">{service.description}</p>

        <div className="flex items-center text-white/60 text-xs mb-3">
          <i className="fas fa-map-marker-alt mr-1"></i>
          <span>{service.location}</span>
        </div>

        <div className="flex items-center text-white/60 text-xs mb-3">
          <i className="fas fa-phone mr-1"></i>
          <span>{service.phone}</span>
        </div>

        <div className="flex justify-between mt-2">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={e => {
              e.stopPropagation();
              onCallClick(service);
            }}
            className="flex-1 mr-2 py-2 rounded-md bg-green-500/20 text-green-500 text-sm font-medium"
          >
            <i className="fas fa-phone mr-2"></i>
            Call
          </motion.button>

          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={e => {
              e.stopPropagation();
              onMessageClick(service);
            }}
            className="flex-1 py-2 rounded-md bg-blue-500/20 text-blue-500 text-sm font-medium"
          >
            <i className="fas fa-comment mr-2"></i>
            Message
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default ServiceCard;
