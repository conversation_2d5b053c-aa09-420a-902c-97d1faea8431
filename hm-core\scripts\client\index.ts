// /**
//  * Client Module Exports
//  *
//  * This file centralizes all exports from the client directory,
//  * allowing for simplified imports throughout the codebase.
//  */

// // Re-export everything from individual modules
// export * from './player';
// export * from './resource.manager';

// // Export a default object with all modules for convenience
// import * as Player from './player';
// import * as ResourceManager from './resource.manager';

// export default {
//     Player,
//     ResourceManager,
// };

import * as Player from './player';

export default {
    Player,
};