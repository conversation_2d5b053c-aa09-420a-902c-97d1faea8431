import React, { useState } from 'react';
import NearbyPlayersPanel from '../../../common/components/NearbyPlayersPanel';
import Modal from '../../../common/components/CommonModal';
import { useDialerStore } from '../stores/dialerStore';
import { useContactsStore } from '../stores/contactsStore';
import MessageModal from '../../messages/components/MessageModal';
import { Contact } from '@shared/types';

interface ContactProfileViewProps {
  contact: Contact;
  onBack: () => void;
  onEdit?: () => void;
  onFavoriteToggle?: (contactId: number) => void;
  onShare?: (playerId: number) => void;
  onDelete?: (contactId: number) => void;
  onCall?: (contact: Contact) => void;
  onMessage?: (contact: Contact) => void;
}

const ContactProfileView: React.FC<ContactProfileViewProps> = ({
  contact,
  onBack,
  onEdit,
  onFavoriteToggle,
  onShare,
  onDelete,
  onCall,
  onMessage
}) => {
  const [showSharePanel, setShowSharePanel] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const dialerStore = useDialerStore();
  const contactsStore = useContactsStore();

  const handleShare = (playerId: number) => {
    console.log(`Sharing contact ${contact.name} with player ${playerId}`);
    // Call the onShare prop if it exists
    if (onShare) {
      onShare(playerId);
    }
    // Don't close the panel - let the user see the status update
    // setShowSharePanel(false);
  };

  const handleDelete = () => {
    console.log(`Deleting contact ${contact.name}`);
    // Call the onDelete prop if it exists
    if (onDelete && contact.id) {
      onDelete(contact.id);
    }
    setShowDeleteConfirm(false);
  };

  const handleCall = () => {
    console.log(`Calling contact ${contact.name}`);
    if (onCall) {
      onCall(contact);
    } else {
      // Fallback to using the dialer store directly
      dialerStore.actions.makeCall(contact.number, contact.name, contact.avatar || '');
    }
  };

  const handleMessage = () => {
    console.log(`Messaging contact ${contact.name}`);
    // Show the message modal
    setShowMessageModal(true);

    // Also call the onMessage prop if it exists
    if (onMessage) {
      onMessage(contact);
    } else {
      // Fallback to using the contacts store directly
      contactsStore.actions.messageContact(contact);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Simple Header */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-white/10">
        <button onClick={onBack} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left" />
        </button>
        <h1 className="text-white/80 text-sm font-medium">Contact Details</h1>
        <div className="w-5"></div> {/* Empty div for balanced spacing */}
      </div>

      {/* Clean Modern Layout */}
      <div className="h-full flex flex-col bg-[#0a0f1a]">
        {/* Contact Info Card */}
        <div className="mx-4 mt-4 p-4 bg-white/5 rounded-xl">
          <div className="flex items-center">
            {/* Avatar */}
            {contact?.avatar ? (
              <div
                className="w-16 h-16 rounded-xl bg-cover bg-center"
                style={{ backgroundImage: `url(${contact.avatar})` }}
              />
            ) : (
              <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500/30 to-blue-600/30 flex items-center justify-center">
                <span className="text-white text-xl">
                  {contact?.name ? contact.name.charAt(0).toUpperCase() : '?'}
                </span>
              </div>
            )}

            {/* Contact Details */}
            <div className="ml-4 flex-1">
              <h2 className="text-white text-lg">{contact?.name || 'Unknown'}</h2>
              <div className="flex items-center mt-1">
                <span className="text-white/50 text-sm">{contact?.number || 'No number'}</span>
                <button className="ml-2 text-white/40 hover:text-white/70">
                  <i className="fas fa-copy text-xs" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex justify-between mx-4 mt-4">
          <button
            onClick={handleCall}
            className="flex-1 mx-1 py-2.5 bg-white/5 rounded-lg flex flex-col items-center hover:bg-white/10 transition-colors"
          >
            <i className="fas fa-phone text-green-400 mb-1" />
            <span className="text-white/70 text-xs">Call</span>
          </button>
          <button
            onClick={handleMessage}
            className="flex-1 mx-1 py-2.5 bg-white/5 rounded-lg flex flex-col items-center hover:bg-white/10 transition-colors"
          >
            <i className="fas fa-comment text-blue-400 mb-1" />
            <span className="text-white/70 text-xs">Message</span>
          </button>
          <button
            onClick={() => setShowSharePanel(true)}
            className="flex-1 mx-1 py-2.5 bg-white/5 rounded-lg flex flex-col items-center hover:bg-white/10 transition-colors"
          >
            <i className="fas fa-share-alt text-yellow-400 mb-1" />
            <span className="text-white/70 text-xs">Share</span>
          </button>
        </div>

        {/* Contact Actions */}
        <div className="mx-4 mt-6">
          <h3 className="text-white/40 text-xs uppercase tracking-wider mb-2 px-1">
            Contact Actions
          </h3>
          <div className="bg-white/5 rounded-xl overflow-hidden">
            <button
              onClick={onEdit}
              className="w-full flex items-center p-3.5 hover:bg-white/10 transition-colors border-b border-white/5"
            >
              <i className="fas fa-edit text-purple-400 w-6" />
              <span className="text-white ml-3">Edit Contact</span>
            </button>

            <button
              onClick={() => contact.id && onFavoriteToggle?.(contact.id)}
              className="w-full flex items-center p-3.5 hover:bg-white/10 transition-colors"
            >
              <i
                className={`fas fa-star w-6 ${
                  contact.favorite === 1 ? 'text-yellow-400' : 'text-white/40'
                }`}
              />
              <span className="text-white ml-3">
                {contact.favorite === 1 ? 'Remove from Favorites' : 'Add to Favorites'}
              </span>
            </button>
          </div>
        </div>

        {/* Delete Button */}
        {onDelete && (
          <div className="mx-4 mt-auto mb-4">
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="w-full flex items-center justify-center p-3 rounded-xl text-red-400 hover:bg-red-500/10 transition-colors"
            >
              <i className="fas fa-trash mr-2" />
              <span>Delete Contact</span>
            </button>
          </div>
        )}
      </div>

      {/* Nearby Players Panel for Contact Sharing */}
      <NearbyPlayersPanel
        show={showSharePanel}
        onClose={() => setShowSharePanel(false)}
        title="Share Contact"
        onPlayerSelect={handleShare}
        getPlayerStatus={playerId => {
          // Get sharing status for a specific player
          const key = `${contact.id}_${playerId}`;
          const status = contactsStore.sharingStatus[key]?.status || null;

          switch (status) {
            case 'sent':
              return {
                status,
                text: 'Sent',
                color: 'text-blue-400',
                icon: 'fa-paper-plane'
              };
            case 'accepted':
              return {
                status,
                text: 'Accepted',
                color: 'text-green-400',
                icon: 'fa-check'
              };
            case 'declined':
              return {
                status,
                text: 'Declined',
                color: 'text-red-400',
                icon: 'fa-times'
              };
            case 'error':
              return {
                status,
                text: 'Failed',
                color: 'text-red-400',
                icon: 'fa-exclamation'
              };
            default:
              return {
                status: null,
                text: 'Share',
                color: 'text-white',
                icon: 'fa-share-alt'
              };
          }
        }}
        contentAbove={
          <div className="bg-white/5 rounded-lg p-3 mb-2 flex items-center">
            {contact.avatar ? (
              <div
                className="w-10 h-10 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${contact.avatar})` }}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-lg font-medium">
                  {contact.name ? contact.name[0].toUpperCase() : '#'}
                </span>
              </div>
            )}
            <div className="ml-3">
              <div className="text-white font-medium">{contact.name}</div>
              <div className="text-white/60 text-sm">{contact.number}</div>
            </div>
          </div>
        }
      />

      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
        <div className="text-white">
          <h3 className="text-xl font-medium mb-4">Delete Contact</h3>
          <p className="mb-6">
            Are you sure you want to delete {contact.name}? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded text-white"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 rounded text-white"
            >
              Delete
            </button>
          </div>
        </div>
      </Modal>

      {/* Message Modal */}
      {showMessageModal && (
        <MessageModal
          phoneNumber={contact.number}
          name={contact.name}
          onClose={() => setShowMessageModal(false)}
        />
      )}
    </div>
  );
};

export default ContactProfileView;
