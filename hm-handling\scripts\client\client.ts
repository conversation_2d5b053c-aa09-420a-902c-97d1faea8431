import { VehicleInfo, BaseHandlingData, HandlingProfile } from '../shared/types';

// Simple state management (instead of Zustand for now)
interface HandlingState {
  isUIOpen: boolean;
  vehicleInfo: VehicleInfo | null;
  originalHandling: Partial<BaseHandlingData>;
  currentHandling: Partial<BaseHandlingData>;
  profiles: HandlingProfile[];
  hasUnsavedChanges: boolean;
}

let handlingState: HandlingState = {
  isUIOpen: false,
  vehicleInfo: null,
  originalHandling: {},
  currentHandling: {},
  profiles: [],
  hasUnsavedChanges: false
};

// UI State - consolidated for better performance
let isUIOpen = false;
let currentVehicle: number | null = null;

// Performance optimization: Cache for vehicle handling data
const vehicleHandlingCache = new Map<number, {
  data: Partial<BaseHandlingData>;
  timestamp: number;
  vehicleInfo: VehicleInfo;
}>();

const CACHE_DURATION = 5000; // 5 seconds cache

// Performance optimization: Debouncing for real-time updates
let debounceTimers = new Map<string, NodeJS.Timeout>();
const DEBOUNCE_DELAY = 150; // milliseconds

// Debounced field update function
function debouncedFieldUpdate(field: string, value: any, callback: () => void): void {
  // Clear existing timer for this field
  const existingTimer = debounceTimers.get(field);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }
  
  // Set new timer
  const timer = setTimeout(() => {
    callback();
    debounceTimers.delete(field);
  }, DEBOUNCE_DELAY);
  
  debounceTimers.set(field, timer);
}

// Performance optimization: Batch handling field updates
interface PendingUpdate {
  field: string;
  value: any;
  timestamp: number;
}

let pendingUpdates = new Map<string, PendingUpdate>();
let batchUpdateTimer: NodeJS.Timeout | null = null;
const BATCH_UPDATE_DELAY = 200; // milliseconds

function batchFieldUpdate(field: string, value: any): void {
  // Add to pending updates
  pendingUpdates.set(field, {
    field,
    value,
    timestamp: GetGameTimer()
  });
  
  // Schedule batch update if not already scheduled
  if (!batchUpdateTimer) {
    batchUpdateTimer = setTimeout(() => {
      processBatchUpdates();
    }, BATCH_UPDATE_DELAY);
  }
}

function processBatchUpdates(): void {
  if (!currentVehicle || pendingUpdates.size === 0) {
    clearBatchUpdate();
    return;
  }
  
  const updates = Array.from(pendingUpdates.values());
  pendingUpdates.clear();
    // Apply all updates at once
  for (const update of updates) {
    // TODO: Implement field-specific update logic
    console.log(`Applying batched update: ${update.field} = ${update.value}`);
  }
  
  clearBatchUpdate();
}

function clearBatchUpdate(): void {
  if (batchUpdateTimer) {
    clearTimeout(batchUpdateTimer);
    batchUpdateTimer = null;
  }
}

// Efficient change detection without JSON.stringify
function hasHandlingChanges(current: Partial<BaseHandlingData>, original: Partial<BaseHandlingData>): boolean {
  const currentKeys = Object.keys(current);
  const originalKeys = Object.keys(original);
  
  if (currentKeys.length !== originalKeys.length) return true;
  
  for (const key of currentKeys) {
    const currentValue = current[key as keyof BaseHandlingData];
    const originalValue = original[key as keyof BaseHandlingData];
    
    // Handle Vector3 objects
    if (typeof currentValue === 'object' && currentValue !== null && 'x' in currentValue) {
      const currentVec = currentValue as { x: number; y: number; z: number };
      const originalVec = originalValue as { x: number; y: number; z: number };
      if (currentVec.x !== originalVec.x || currentVec.y !== originalVec.y || currentVec.z !== originalVec.z) {
        return true;
      }
    } else if (currentValue !== originalValue) {
      return true;
    }
  }
  
  return false;
}

// State management functions
function updateState(updates: Partial<HandlingState>): void {
  handlingState = { ...handlingState, ...updates };
  
  // Efficient change detection
  if (updates.currentHandling || updates.originalHandling) {
    handlingState.hasUnsavedChanges = hasHandlingChanges(
      handlingState.currentHandling, 
      handlingState.originalHandling
    );
  }
}

// Initialize when client starts
setImmediate(() => {
  console.log('Handling editor client initialized');
});

// F7 Keybind to toggle UI
RegisterCommand('toggle_handling', () => {
  toggleHandlingUI();
}, false);

RegisterKeyMapping('toggle_handling', 'Toggle Handling Editor', 'keyboard', 'F7');

// Main UI toggle function
function toggleHandlingUI(): void {
  if (isUIOpen) {
    closeHandlingUI();
  } else {
    openHandlingUI();
  }
}

// Unit conversion functions
function toUIValue(field: string, gameValue: number): number {
  // Keep values as-is - no conversion needed
  return gameValue;
}

function toGameValue(field: string, uiValue: number): number {
  // Keep values as-is - no conversion needed
  return uiValue;
}

// Open handling UI
async function openHandlingUI(): Promise<void> {
  const playerPed = PlayerPedId();
  
  // Debug logging
  console.log('[hm-handling] Opening handling UI...');
  console.log('[hm-handling] Player ped:', playerPed);
  
  // First check if player is in any vehicle at all
  if (!IsPedInAnyVehicle(playerPed, false)) {
    console.log('[hm-handling] Player is not in any vehicle');
    
    // Clear cache and reset state when no vehicle
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no vehicle message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat of a vehicle to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  
  const vehicle = GetVehiclePedIsIn(playerPed, false);
  console.log('[hm-handling] Vehicle handle:', vehicle);
  
  // Double-check vehicle exists
  if (vehicle === 0 || !DoesEntityExist(vehicle)) {
    console.log('[hm-handling] Vehicle does not exist or invalid handle');
    
    // Clear cache and reset state when no valid vehicle
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no vehicle message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat of a vehicle to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  // Verify player is in driver seat (seat -1)
  // Check if the player ped is the driver by getting who's in the driver seat
  const driverPed = GetPedInVehicleSeat(vehicle, -1);
  console.log('[hm-handling] Driver ped:', driverPed, 'Player ped:', playerPed);
  
  if (driverPed !== playerPed) {
    console.log('[hm-handling] Player is not in driver seat');
    
    // Clear cache and reset state when not in driver seat
    if (currentVehicle) {
      vehicleHandlingCache.delete(currentVehicle);
      currentVehicle = null;
    }
    
    // Send UI with no driver seat message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'You must be in the driver seat to use the handling editor.',
        profiles: handlingState.profiles
      }
    });
    
    SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  // Vehicle changed - invalidate old cache and update reference
  if (currentVehicle && currentVehicle !== vehicle) {
    console.log('[hm-handling] Vehicle changed from', currentVehicle, 'to', vehicle);
    vehicleHandlingCache.delete(currentVehicle);
  }
  currentVehicle = vehicle;
  console.log('[hm-handling] Current vehicle set to:', currentVehicle);
  // Get vehicle info and handling data
  console.log('[hm-handling] Getting vehicle info and handling data...');
  const vehicleInfo = await getVehicleInfo(currentVehicle);
  
  // Initialize baseline values if this is the first time with this vehicle
  await initializeBaseline(currentVehicle);
  const uiState = getUIState(currentVehicle);
  
  console.log('[hm-handling] Vehicle info:', vehicleInfo);
  console.log('[hm-handling] UI state available:', !!uiState);
  
  if (!vehicleInfo) {
    console.log('[hm-handling] Failed to get vehicle info');
    
    // Send UI with error message
    SendNUIMessage({
      type: 'setVisible',
      visible: true,
      data: {
        vehicle: null,
        handling: {},
        noVehicleMessage: 'Failed to get vehicle information. Please try again.',
        profiles: handlingState.profiles
      }
    });
      SetNuiFocus(true, true);
    updateState({ isUIOpen: true });
    isUIOpen = true;
    return;
  }
  
  console.log('[hm-handling] Successfully retrieved vehicle data, opening UI');
  console.log(`[hm-handling] Modified fields: ${uiState.modified.join(', ')}`);
  console.log(`[hm-handling] Applied fields: ${uiState.applied.join(', ')}`);

  // Update state with persistent values
  updateState({
    isUIOpen: true,
    vehicleInfo,
    originalHandling: uiState.baseline,
    currentHandling: uiState.working
  });
  // Send data to UI with persistent state information
  SendNUIMessage({
    type: 'setVisible',
    visible: true,
    data: {
      vehicle: vehicleInfo,
      handling: uiState.working,
      originalHandling: uiState.baseline,
      modifiedFields: uiState.modified,
      appliedFields: uiState.applied,
      profiles: handlingState.profiles
    }
  });

  SetNuiFocus(true, true);
  isUIOpen = true;
  console.log('[hm-handling] UI opened successfully');
}

// Close handling UI
function closeHandlingUI(): void {
  console.log('[hm-handling] Closing handling UI...');
  
  SendNUIMessage({
    type: 'setVisible',
    visible: false
  });

  SetNuiFocus(false, false);
  updateState({ isUIOpen: false });
  isUIOpen = false;
  
  console.log('[hm-handling] UI closed successfully');
}

// Get vehicle information
async function getVehicleInfo(vehicle: number): Promise<VehicleInfo | null> {
  if (!DoesEntityExist(vehicle)) return null;

  const model = GetEntityModel(vehicle);
  const modelName = GetDisplayNameFromVehicleModel(model);
  const displayName = GetLabelText(modelName);
  const vehicleClass = GetVehicleClass(vehicle);
  const className = GetLabelText(`VEH_CLASS_${vehicleClass}`);  const playerPed = PlayerPedId();
  const isInVehicle = GetVehiclePedIsIn(playerPed, false) === vehicle;
  const driverPed = GetPedInVehicleSeat(vehicle, -1);
  const isDriverSeat = isInVehicle && driverPed === playerPed;

  return {
    model: modelName.toLowerCase(),
    displayName: displayName !== 'NULL' ? displayName : modelName,
    hash: model,
    vehicleClass,
    className: className !== 'NULL' ? className : `Class ${vehicleClass}`,
    isInVehicle,
    isDriverSeat,
    vehicleHandle: vehicle
  };
}

// Get vehicle handling data
// Performance-optimized vehicle handling retrieval with caching
async function getVehicleHandling(vehicle: number): Promise<Partial<BaseHandlingData> | null> {
  if (!DoesEntityExist(vehicle)) return null;

  const now = GetGameTimer();
  const cached = vehicleHandlingCache.get(vehicle);
  
  // Return cached data if still valid
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  // Batch retrieve handling values from the vehicle for better performance
  const handlingData: Partial<BaseHandlingData> = {
    fMass: toUIValue('fMass', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fMass')),
    fInitialDragCoeff: toUIValue('fInitialDragCoeff', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDragCoeff')),
    fPercentSubmerged: toUIValue('fPercentSubmerged', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fPercentSubmerged')),
    
    // Centre of mass offset
    vecCentreOfMassOffset: {
      x: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_x'),
      y: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_y'),
      z: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_z')
    },

    // Inertia multiplier
    vecInertiaMultiplier: {
      x: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_x'),
      y: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_y'),
      z: GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_z')
    },

    // Drive properties
    fDriveBiasFront: toUIValue('fDriveBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDriveBiasFront')),
    nInitialDriveGears: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'nInitialDriveGears'),
    fInitialDriveForce: toUIValue('fInitialDriveForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDriveForce')),
    fDriveInertia: toUIValue('fDriveInertia', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDriveInertia')),
    fClutchChangeRateScaleUpShift: toUIValue('fClutchChangeRateScaleUpShift', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fClutchChangeRateScaleUpShift')),
    fClutchChangeRateScaleDownShift: toUIValue('fClutchChangeRateScaleDownShift', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fClutchChangeRateScaleDownShift')),
    fInitialDriveMaxFlatVel: toUIValue('fInitialDriveMaxFlatVel', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fInitialDriveMaxFlatVel')),

    // Brake properties
    fBrakeForce: toUIValue('fBrakeForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce')),
    fBrakeBiasFront: toUIValue('fBrakeBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeBiasFront')),
    fHandBrakeForce: toUIValue('fHandBrakeForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fHandBrakeForce')),

    // Steering
    fSteeringLock: toUIValue('fSteeringLock', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSteeringLock')),

    // Traction
    fTractionCurveMax: toUIValue('fTractionCurveMax', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveMax')),
    fTractionCurveMin: toUIValue('fTractionCurveMin', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveMin')),
    fTractionCurveLateral: toUIValue('fTractionCurveLateral', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionCurveLateral')),
    fTractionSpringDeltaMax: toUIValue('fTractionSpringDeltaMax', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionSpringDeltaMax')),
    fLowSpeedTractionLossMult: toUIValue('fLowSpeedTractionLossMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fLowSpeedTractionLossMult')),
    fCamberStiffnesss: toUIValue('fCamberStiffnesss', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCamberStiffnesss')),
    fTractionBiasFront: toUIValue('fTractionBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionBiasFront')),
    fTractionLossMult: toUIValue('fTractionLossMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fTractionLossMult')),

    // Suspension
    fSuspensionForce: toUIValue('fSuspensionForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionForce')),
    fSuspensionCompDamp: toUIValue('fSuspensionCompDamp', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionCompDamp')),
    fSuspensionReboundDamp: toUIValue('fSuspensionReboundDamp', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionReboundDamp')),
    fSuspensionUpperLimit: toUIValue('fSuspensionUpperLimit', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionUpperLimit')),
    fSuspensionLowerLimit: toUIValue('fSuspensionLowerLimit', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionLowerLimit')),
    fSuspensionRaise: toUIValue('fSuspensionRaise', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionRaise')),
    fSuspensionBiasFront: toUIValue('fSuspensionBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSuspensionBiasFront')),
    fAntiRollBarForce: toUIValue('fAntiRollBarForce', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fAntiRollBarForce')),
    fAntiRollBarBiasFront: toUIValue('fAntiRollBarBiasFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fAntiRollBarBiasFront')),
    fRollCentreHeightFront: toUIValue('fRollCentreHeightFront', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fRollCentreHeightFront')),
    fRollCentreHeightRear: toUIValue('fRollCentreHeightRear', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fRollCentreHeightRear')),

    // Damage
    fCollisionDamageMult: toUIValue('fCollisionDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult')),
    fWeaponDamageMult: toUIValue('fWeaponDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fWeaponDamageMult')),
    fDeformationDamageMult: toUIValue('fDeformationDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult')),
    fEngineDamageMult: toUIValue('fEngineDamageMult', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult')),

    // Fuel and oil
    fPetrolTankVolume: toUIValue('fPetrolTankVolume', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fPetrolTankVolume')),
    fOilVolume: toUIValue('fOilVolume', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fOilVolume')),

    // Misc    fSeatOffsetDistX: toUIValue('fSeatOffsetDistX', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistX')),
    fSeatOffsetDistY: toUIValue('fSeatOffsetDistY', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistY')),
    fSeatOffsetDistZ: toUIValue('fSeatOffsetDistZ', GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSeatOffsetDistZ')),
    nMonetaryValue: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'nMonetaryValue'),

    // Flags (retrieved as strings from handling data)
    strModelFlags: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'strModelFlags').toString(),
    strHandlingFlags: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'strHandlingFlags').toString(),
    strDamageFlags: GetVehicleHandlingInt(vehicle, 'CHandlingData', 'strDamageFlags').toString()
  };

  // Cache the result with vehicle info
  const vehicleInfo = await getVehicleInfo(vehicle);
  if (vehicleInfo) {
    vehicleHandlingCache.set(vehicle, {
      data: handlingData,
      timestamp: now,
      vehicleInfo
    });
  }

  return handlingData;
}

// Apply handling changes to vehicle - SIMPLIFIED to match working test commands
function applyHandlingToVehicle(vehicle: number, handlingData: Partial<BaseHandlingData>): boolean {
  if (!DoesEntityExist(vehicle)) return false;

  console.log('[hm-handling] Applying handling data (simplified approach):', handlingData);

  try {
    // Apply each handling value using the same approach as our working test commands
    Object.entries(handlingData).forEach(([field, value]) => {
      if (value === undefined || value === null) return;

      console.log(`[hm-handling] Processing field: ${field} = ${value}`);

      if (field === 'vecCentreOfMassOffset' && typeof value === 'object' && 'x' in value) {
        console.log(`[hm-handling] Setting vecCentreOfMassOffset: x=${value.x}, y=${value.y}, z=${value.z}`);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_x', value.x);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_y', value.y);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecCentreOfMassOffset_z', value.z);
      } else if (field === 'vecInertiaMultiplier' && typeof value === 'object' && 'x' in value) {
        console.log(`[hm-handling] Setting vecInertiaMultiplier: x=${value.x}, y=${value.y}, z=${value.z}`);        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_x', value.x);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_y', value.y);
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'vecInertiaMultiplier_z', value.z);
      } else if (field === 'nInitialDriveGears' || field === 'nMonetaryValue') {
        console.log(`[hm-handling] Setting ${field} (int): ${value}`);
        SetVehicleHandlingInt(vehicle, 'CHandlingData', field, value as number);
      } else if (field === 'strModelFlags' || field === 'strHandlingFlags' || field === 'strDamageFlags') {
        console.log(`[hm-handling] Setting ${field} (flag string): ${value}`);
        // Use SetVehicleHandlingField for string flag values (native accepts any value type)
        (SetVehicleHandlingField as any)(vehicle, 'CHandlingData', field, value as string);
      } else if (typeof value === 'string') {
        console.log(`[hm-handling] Setting ${field} (string): ${value}`);
        // Handle other string fields using the general field setter
        (SetVehicleHandlingField as any)(vehicle, 'CHandlingData', field, value);
      } else if (typeof value === 'number') {
        console.log(`[hm-handling] Setting ${field} (float): ${value}`);
        // Direct set - no conversions, exactly like our working test commands
        SetVehicleHandlingFloat(vehicle, 'CHandlingData', field, value);
        
        // Verify the value was set correctly (like our test commands do)
        const verifyValue = GetVehicleHandlingFloat(vehicle, 'CHandlingData', field);
        console.log(`[hm-handling] ${field}: Set ${value}, Verified ${verifyValue}, Success: ${Math.abs(verifyValue - value) < 0.1}`);
      }
    });

    console.log('[hm-handling] All handling values applied successfully');
    return true;
  } catch (error) {
    console.error('[hm-handling] Error applying handling to vehicle:', error);
    return false;
  }
}

// Apply single field to vehicle
function applySingleField(field: string, value: number | string | object): void {
  if (!currentVehicle) return;

  const handlingData: Partial<BaseHandlingData> = {
    [field]: value
  };

  const success = applyHandlingToVehicle(currentVehicle, handlingData);
  
  if (success) {
    // Update current state with the new value
    updateState({
      currentHandling: {
        ...handlingState.currentHandling,
        [field]: value
      }
    });

    // Send success feedback to UI
    SendNUIMessage({
      type: 'fieldApplied',
      field,
      success: true
    });
  } else {
    // Send error feedback to UI
    SendNUIMessage({
      type: 'fieldApplied',
      field,
      success: false    });
  }
}

// NUI Callbacks
RegisterNuiCallback('closeUI', (data: unknown, cb: (response: unknown) => void) => {
  closeHandlingUI();
  cb({ ok: true });
});

RegisterNuiCallback('applyHandling', (data: { handlingData: Partial<BaseHandlingData> }, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  const success = applyHandlingToVehicle(currentVehicle, data.handlingData);
  
  if (success) {
    updateState({ currentHandling: data.handlingData });
    cb({ success: true });
  } else {
    cb({ success: false, error: 'Failed to apply handling changes' });
  }
});

RegisterNuiCallback('refreshVehicle', async (data: unknown, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  console.log('[hm-handling] Refreshing vehicle - maintaining persistent state...');

  // Don't clear cache - we want to maintain our persistent state
  // Only refresh the vehicle info, not the handling state
  const vehicleInfo = await getVehicleInfo(currentVehicle);
  const uiState = getUIState(currentVehicle);

  if (vehicleInfo) {
    updateState({
      vehicleInfo,
      originalHandling: uiState.baseline,
      currentHandling: uiState.working
    });

    cb({ 
      success: true, 
      data: { 
        vehicle: vehicleInfo, 
        handling: uiState.working,
        originalHandling: uiState.baseline,
        modifiedFields: uiState.modified,
        appliedFields: uiState.applied
      } 
    });
    
    console.log('[hm-handling] Vehicle refreshed while maintaining state');
    console.log(`[hm-handling] Modified fields: ${uiState.modified.join(', ')}`);
    console.log(`[hm-handling] Applied fields: ${uiState.applied.join(', ')}`);
  } else {
    cb({ success: false, error: 'Failed to refresh vehicle data' });
  }
});

RegisterNuiCallback('applyField', (data: { field: string; value: number | string | object }, cb: (response: unknown) => void) => {
  console.log(`[hm-handling] NUI callback applyField: ${data.field} = ${data.value}`);
  
  if (!currentVehicle) {
    console.log('[hm-handling] No current vehicle for applyField');
    cb({ ok: false, error: 'No vehicle' });
    return;
  }

  // Update working value first
  updateWorkingValue(currentVehicle, data.field, data.value);
  
  // Apply immediately if it's a number value
  if (typeof data.value === 'number') {
    console.log(`[hm-handling] Applying ${data.field} immediately to ${data.value}`);
    
    const success = applySingleFieldImmediate(currentVehicle, data.field, data.value);
    
    if (success) {
      // Get updated UI state to send back
      const uiState = getUIState(currentVehicle);
      
      cb({ 
        ok: true, 
        applied: true,
        modifiedFields: uiState.modified,
        appliedFields: uiState.applied
      });
    } else {
      cb({ ok: false, error: 'Failed to apply to vehicle' });
    }
  } else {
    console.log(`[hm-handling] Non-number value for ${data.field}, saved but not applied`);
    const uiState = getUIState(currentVehicle);
    cb({ 
      ok: true, 
      applied: false,
      modifiedFields: uiState.modified,
      appliedFields: uiState.applied
    });
  }
});

RegisterNuiCallback('applyAllChanges', (data: { handlingData: Partial<BaseHandlingData> }, cb: (response: unknown) => void) => {
  if (!currentVehicle) {
    cb({ success: false, error: 'No vehicle selected' });
    return;
  }

  console.log('[hm-handling] Applying all changes - only changed fields...');
  
  // Update all working values from the UI data
  for (const [field, value] of Object.entries(data.handlingData)) {
    updateWorkingValue(currentVehicle, field, value);
  }
  
  // Apply only the changed fields
  const results = applyChangedFields(currentVehicle);
  
  if (results.failed === 0) {
    // Update state with latest values
    const uiState = getUIState(currentVehicle);
    updateState({ currentHandling: uiState.working });
    
    cb({ 
      success: true,
      applied: results.applied,
      skipped: results.skipped,
      modifiedFields: uiState.modified,
      appliedFields: uiState.applied
    });
  } else {
    cb({ 
      success: false, 
      error: `Failed to apply ${results.failed} fields`,
      applied: results.applied,
      skipped: results.skipped,
      failed: results.failed
    });
  }
});

RegisterNuiCallback('saveProfile', (data: { profile: Omit<HandlingProfile, 'id' | 'createdAt'> }, cb: (response: unknown) => void) => {
  // TODO: Implement profile saving logic
  cb({ success: true });
});

RegisterNuiCallback('loadProfile', (data: { profileId: string }, cb: (response: unknown) => void) => {
  // TODO: Implement profile loading logic
  cb({ success: true });
});

// Reset vehicle handling to default (if needed)
function resetVehicleHandling(vehicle: number): boolean {
  if (!DoesEntityExist(vehicle)) return false;

  try {
    // This would require storing original values or reloading from handling.meta
    // For now, we'll just return success
    return true;
  } catch (error) {
    console.error('Error resetting vehicle handling:', error);
    return false;
  }
}

RegisterNuiCallback('deleteProfile', (data: { profileId: string }, cb: (response: unknown) => void) => {
  // TODO: Implement profile deletion logic
  cb({ success: true });
});



// Enhanced state management for persistent handling editor
interface PersistentVehicleState {
  vehicleId: number;
  baselineValues: Partial<BaseHandlingData>; // Never changes after first capture
  currentAppliedValues: Partial<BaseHandlingData>; // What's actually applied to vehicle
  workingValues: Partial<BaseHandlingData>; // What user is editing in UI
  modifiedFields: Set<string>; // Fields that have been changed from baseline
  appliedFields: Set<string>; // Fields that have been applied to vehicle
}

// Global persistent state store
const persistentVehicleStates = new Map<number, PersistentVehicleState>();

// Get or create persistent state for a vehicle
function getOrCreatePersistentState(vehicleId: number): PersistentVehicleState {
  if (!persistentVehicleStates.has(vehicleId)) {
    persistentVehicleStates.set(vehicleId, {
      vehicleId,
      baselineValues: {},
      currentAppliedValues: {},
      workingValues: {},
      modifiedFields: new Set(),
      appliedFields: new Set()
    });
  }
  return persistentVehicleStates.get(vehicleId)!;
}

// Initialize baseline values (only done once per vehicle)
async function initializeBaseline(vehicleId: number): Promise<void> {
  const state = getOrCreatePersistentState(vehicleId);
  
  // Only set baseline if it's empty (first time)
  if (Object.keys(state.baselineValues).length === 0) {
    const currentHandling = await getVehicleHandling(vehicleId);
    if (currentHandling) {
      state.baselineValues = { ...currentHandling };
      state.currentAppliedValues = { ...currentHandling };
      state.workingValues = { ...currentHandling };
      console.log(`[hm-handling] Initialized baseline for vehicle ${vehicleId}`);
    }
  }
}

// Update working value and track changes
function updateWorkingValue(vehicleId: number, field: string, value: number | string | object): void {
  const state = getOrCreatePersistentState(vehicleId);
  
  // Update working value
  state.workingValues = {
    ...state.workingValues,
    [field]: value
  };
  
  // Check if this field is different from baseline
  const baselineValue = state.baselineValues[field as keyof BaseHandlingData];
  const isDifferent = baselineValue !== value;
  
  if (isDifferent) {
    state.modifiedFields.add(field);
  } else {
    state.modifiedFields.delete(field);
  }
  
  console.log(`[hm-handling] Updated working value ${field} = ${value}, modified: ${isDifferent}`);
}

// Apply single field immediately and track application
function applySingleFieldImmediate(vehicleId: number, field: string, value: number | string | object): boolean {
  try {
    // Handle different value types
    if (field === 'strModelFlags' || field === 'strHandlingFlags' || field === 'strDamageFlags') {
      if (typeof value !== 'string') {
        console.log(`[hm-handling] Flag field ${field} requires string value, got ${typeof value}`);
        return false;
      }
      
      // Apply flag field using SetVehicleHandlingField
      (SetVehicleHandlingField as any)(vehicleId, 'CHandlingData', field, value);
      console.log(`[hm-handling] Applied flag ${field} = ${value}`);
      
      const state = getOrCreatePersistentState(vehicleId);
      state.currentAppliedValues = {
        ...state.currentAppliedValues,
        [field]: value
      };
      state.appliedFields.add(field);
      
      return true;
    } else if (typeof value === 'string') {
      // Handle other string fields
      (SetVehicleHandlingField as any)(vehicleId, 'CHandlingData', field, value);
      console.log(`[hm-handling] Applied string field ${field} = ${value}`);
      
      const state = getOrCreatePersistentState(vehicleId);
      state.currentAppliedValues = {
        ...state.currentAppliedValues,
        [field]: value
      };
      state.appliedFields.add(field);
      
      return true;
    } else if (typeof value === 'number') {
      // Handle numeric fields (existing logic)
      if (field === 'nInitialDriveGears' || field === 'nMonetaryValue') {
        SetVehicleHandlingInt(vehicleId, 'CHandlingData', field, value);
      } else {
        SetVehicleHandlingFloat(vehicleId, 'CHandlingData', field, value);
      }
      
      // Verify numeric values
      const verified = (field === 'nInitialDriveGears' || field === 'nMonetaryValue') 
        ? GetVehicleHandlingInt(vehicleId, 'CHandlingData', field)
        : GetVehicleHandlingFloat(vehicleId, 'CHandlingData', field);
      const success = Math.abs(verified - value) < 0.1;
      
      if (success) {
        const state = getOrCreatePersistentState(vehicleId);
        state.currentAppliedValues = {
          ...state.currentAppliedValues,
          [field]: value
        };
        state.appliedFields.add(field);
        console.log(`[hm-handling] Applied ${field} = ${value} (verified: ${verified})`);
      }
      
      return success;
    } else {
      console.log(`[hm-handling] Cannot apply unsupported value type ${typeof value} for ${field}`);
      return false;
    }
  } catch (error) {
    console.error(`[hm-handling] Error applying ${field}:`, error);
    return false;
  }
}

// Apply only changed fields that haven't been applied yet
function applyChangedFields(vehicleId: number): { applied: number; skipped: number; failed: number } {
  const state = getOrCreatePersistentState(vehicleId);
  let applied = 0;
  let skipped = 0;
  let failed = 0;
  
  console.log(`[hm-handling] Applying changed fields. Modified: ${state.modifiedFields.size}`);
    for (const field of state.modifiedFields) {
    const workingValue = state.workingValues[field as keyof BaseHandlingData];
    const currentAppliedValue = state.currentAppliedValues[field as keyof BaseHandlingData];
    
    // Skip if working value is undefined
    if (workingValue === undefined) {
      skipped++;
      continue;
    }
    
    // Skip if already applied with this value
    if (workingValue === currentAppliedValue && state.appliedFields.has(field)) {
      skipped++;
      console.log(`[hm-handling] Skipping ${field} - already applied with value ${workingValue}`);
      continue;
    }
    
    // Apply the field
    if (applySingleFieldImmediate(vehicleId, field, workingValue)) {
      applied++;
    } else {
      failed++;
    }
  }
  
  console.log(`[hm-handling] Apply results - Applied: ${applied}, Skipped: ${skipped}, Failed: ${failed}`);
  return { applied, skipped, failed };
}

// Get current state for UI
function getUIState(vehicleId: number): { baseline: Partial<BaseHandlingData>; working: Partial<BaseHandlingData>; modified: string[]; applied: string[] } {
  const state = getOrCreatePersistentState(vehicleId);
  return {
    baseline: { ...state.baselineValues },
    working: { ...state.workingValues },
    modified: Array.from(state.modifiedFields),
    applied: Array.from(state.appliedFields)
  };
}


