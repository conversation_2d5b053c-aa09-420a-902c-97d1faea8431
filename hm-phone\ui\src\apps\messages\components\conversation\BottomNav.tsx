import React from 'react';

interface BottomNavProps {
  activeTab: string;
  switchTab: (tab: string) => void;
  totalUnread: number;
}

const BottomNav: React.FC<BottomNavProps> = ({ activeTab, switchTab, totalUnread }) => {
  // Default theme values
  const colors = {
    bg: {
      primary: 'bg-gray-900'
    },
    text: {
      primary: 'text-white',
      tertiary: 'text-gray-500'
    },
    border: {
      primary: 'border-gray-800'
    }
  };

  return (
    <div
      className={`flex justify-around items-center py-2 ${colors.bg.primary} border-t ${colors.border.primary}`}
    >
      <button
        onClick={() => switchTab('chats')}
        className={`flex flex-col items-center relative cursor-pointer ${
          activeTab === 'chats' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-comments text-lg"></i>
        <span className="text-xs mt-1">Chats</span>
        {totalUnread > 0 && (
          <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
            {totalUnread}
          </span>
        )}
        {activeTab === 'chats' && (
          <div className={`h-0.5 w-full ${colors.text.primary} mt-1`}></div>
        )}
      </button>
      <button
        onClick={() => switchTab('profile')}
        className={`flex flex-col items-center cursor-pointer ${
          activeTab === 'profile' ? colors.text.primary : colors.text.tertiary
        }`}
      >
        <i className="fas fa-user text-lg"></i>
        <span className="text-xs mt-1">Profile</span>
        {activeTab === 'profile' && (
          <div className={`h-0.5 w-full ${colors.text.primary} mt-1`}></div>
        )}
      </button>
    </div>
  );
};

export default BottomNav;
