/**
 * HM Weapons - Weapon Benches Server
 * 
 * Handles server-side weapon bench interactions and validation
 */

/// <reference types="@citizenfx/server" />

import { 
  getWeaponBench, 
  canAccessWeaponBench,
  type WeaponBench 
} from '../shared/weapon-benches-config';

/**
 * Get player data from hm-core
 */
function getPlayerData(source: number): { job: string; gang: string } {
  try {
    // Check if hm-core export exists
    if (!global.exports['hm-core']) {
      console.error(`[hm-weapons] hm-core exports not found!`);
      return { job: 'unemployed', gang: 'none' };
    }
    
    // Get player data from hm-core
    const playerData = global.exports['hm-core'].getPlayer(source);
    if (!playerData) {
      console.warn(`[hm-weapons] Player data not found for source ${source}`);
      return { job: 'unemployed', gang: 'none' };
    }
    
    return {
      job: playerData.job?.name || 'unemployed',
      gang: playerData.gang?.name || 'none'
    };
  } catch (error) {
    console.error(`[hm-weapons] Error getting player data for ${source}:`, error);
    return { job: 'unemployed', gang: 'none' };
  }
}

/**
 * Check if player has required item
 */
function hasRequiredItem(source: number, itemName?: string): boolean {
  if (!itemName) return true;
  
  try {
    if (!global.exports['hm-inventory']) {
      console.error(`[hm-weapons] hm-inventory exports not found!`);
      return false;
    }
    
    return global.exports['hm-inventory'].hasItem(source, itemName, 1) === true;
  } catch (error) {
    console.error(`[hm-weapons] Error checking required item ${itemName}:`, error);
    return false;
  }
}

/**
 * Validate player access to weapon bench
 */
function validateBenchAccess(source: number, benchId: string): { valid: boolean; bench?: WeaponBench; reason?: string } {
  const bench = getWeaponBench(benchId);
  
  if (!bench) {
    return { valid: false, reason: 'Weapon bench not found' };
  }

  if (!bench.enabled) {
    return { valid: false, reason: 'Weapon bench is disabled' };
  }

  // Get actual player data from hm-core
  const playerData = getPlayerData(source);
  const hasItem = hasRequiredItem(source, bench.requiredItem);

  if (!canAccessWeaponBench(bench, playerData.job, playerData.gang, hasItem)) {
    return { valid: false, reason: 'Access denied to this weapon bench' };
  }

  return { valid: true, bench };
}

/**
 * Send notification to player
 */
function sendNotification(source: number, message: string, type: 'success' | 'error' | 'info' = 'info'): void {
  // TODO: Use hm-core notification system
  console.log(`[HM-Weapons] Notification to ${source} (${type}): ${message}`);
}

console.log('[HM-Weapons] Weapon benches server module loaded');

// Event handlers for weapon bench interactions
onNet('hm-weapons:requestBenchAccess', (benchId: string) => {
  const source = global.source;
  console.log(`[HM-Weapons] Player ${source} requesting access to bench: ${benchId}`);
  
  const result = validateBenchAccess(source, benchId);
  
  if (result.valid) {
    console.log(`[HM-Weapons] ✓ Access granted to bench ${benchId} for player ${source}`);
    emitNet('hm-weapons:benchAccessGranted', source, {
      benchId,
      bench: result.bench
    });
  } else {
    console.log(`[HM-Weapons] ✗ Access denied to bench ${benchId} for player ${source}: ${result.reason}`);
    emitNet('hm-weapons:benchAccessDenied', source, {
      benchId,
      reason: result.reason
    });
    sendNotification(source, result.reason || 'Access denied', 'error');
  }
});

// Event handler for when player starts weapon modding at a bench
onNet('hm-weapons:startModdingAtBench', (data: { benchId: string; weaponHash: string }) => {
  const source = global.source;
  const { benchId, weaponHash } = data;
  
  console.log(`[HM-Weapons] Player ${source} starting weapon modding at bench ${benchId} with weapon ${weaponHash}`);
  
  // Validate bench access again for security
  const result = validateBenchAccess(source, benchId);
  
  if (!result.valid) {
    console.log(`[HM-Weapons] ✗ Bench access validation failed: ${result.reason}`);
    sendNotification(source, result.reason || 'Access denied', 'error');
    return;
  }
  
  // Log successful start
  console.log(`[HM-Weapons] ✓ Weapon modding started successfully for player ${source} at bench ${benchId}`);
  
  // The rest of the weapon modding logic is handled by the weapon-modding.ts server file
  // through the existing 'hm-weapons:getAvailableMods' event
});
