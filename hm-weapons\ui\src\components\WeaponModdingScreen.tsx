import React, { useState, useEffect } from 'react';
import { useWeaponModdingStore, AttachmentPoint, AttachmentItem } from '../stores/weaponModdingStore';
import { getModIconPath, isFontAwesomeIcon } from '../utils/iconUtils';
import PositioningControls from './PositioningControls';

// Attachment Slot Component - Similar to inventory slot design
interface AttachmentSlotProps {
  attachmentPoint: AttachmentPoint;
  onSlotClick: (pointId: string, bonePosition: { x: number; y: number }, slotPosition: { x: number; y: number }) => void;
  isSelected: boolean;
}

const AttachmentSlot: React.FC<AttachmentSlotProps> = ({
  attachmentPoint,
  onSlotClick,
  isSelected
}) => {
  const currentMod = attachmentPoint.currentMod;
  const baseStyle =
    'w-28 h-28 bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg flex flex-col items-center justify-center relative cursor-pointer transition-all duration-200 ease-in-out overflow-hidden shadow-md';

  const hoverStyle = 'hover:scale-105 hover:shadow-lg hover:shadow-green-400/10 hover:bg-neutral-700/70 hover:border-neutral-600 hover:border-t-green-400/40';

  const selectedStyle = isSelected
    ? 'ring-2 ring-green-500 border-green-400 bg-green-500/20'
    : ''; return (
      <div
        className={`${baseStyle} ${hoverStyle} ${selectedStyle}`}
        data-slot-element onClick={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const slotPosition = {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
          };        // Parse position values - they come as percentages from the client
          const boneXPercent = parseFloat(attachmentPoint.position.x.toString().replace('%', ''));
          const boneYPercent = parseFloat(attachmentPoint.position.y.toString().replace('%', ''));

          // Convert percentages to viewport pixels with bounds checking
          const viewportWidth = window.innerWidth || 1920;
          const viewportHeight = window.innerHeight || 1080;
          const boneX = Math.max(0, Math.min((boneXPercent / 100) * viewportWidth, viewportWidth));
          const boneY = Math.max(0, Math.min((boneYPercent / 100) * viewportHeight, viewportHeight));

          const bonePosition = {
            x: boneX,
            y: boneY
          };
          onSlotClick(attachmentPoint.id, bonePosition, slotPosition);
        }}
      >
        {/* Subtle green gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-green-400/5 via-transparent to-transparent pointer-events-none rounded-lg z-0" />

        {currentMod ? (
          <>          {/* Mod Icon */}
            <div className="w-full h-full flex items-center justify-center z-10">
              {isFontAwesomeIcon(currentMod.icon) ? (
                <i className={`fas ${currentMod.icon} text-2xl text-neutral-200`} />
              ) : currentMod.icon ? (
                <img
                  src={getModIconPath(currentMod.icon)}
                  alt={currentMod.label}
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    // Fallback to default icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = '<i class="fas fa-cog text-2xl text-neutral-200"></i>';
                    }
                  }}
                />
              ) : (
                <i className="fas fa-cog text-2xl text-neutral-200" />
              )}
            </div>

            {/* Mod Label */}
            <div className="absolute bottom-0 left-0 right-0 z-20">
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-green-400/30 via-green-500/50 to-green-400/30" />
              <div className="absolute inset-0 bg-gradient-to-t from-neutral-900/80 via-neutral-900/40 to-transparent" />
              <div className="relative px-1 py-0.5">
                <p className="text-neutral-200 text-[8px] font-medium text-center truncate leading-tight">
                  {currentMod.name}
                </p>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Empty slot icon */}
            <i className="fas fa-plus text-neutral-600 text-xl opacity-50 z-10" />

            {/* Slot type label */}
            <div className="absolute bottom-0 left-0 right-0 z-20">
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-neutral-600/30 via-neutral-500/50 to-neutral-600/30" />
              <div className="absolute inset-0 bg-gradient-to-t from-neutral-900/80 via-neutral-900/40 to-transparent" />
              <div className="relative px-1 py-0.5">
                <p className="text-neutral-400 text-[8px] font-medium text-center truncate leading-tight">
                  {attachmentPoint.slotType}
                </p>
              </div>
            </div>
          </>
        )}
      </div>
    );
};

// Component List Panel - Shows as a list of slots to the right/left of selected attachment
interface ComponentListProps {
  attachmentPoint: AttachmentPoint;
  availableMods: AttachmentItem[];
  onModSelect: (pointId: string, mod: AttachmentItem) => void;
  onRemoveMod: (pointId: string) => void;
  onClose: () => void;
  slotPosition: { x: number; y: number };
}

const ComponentList: React.FC<ComponentListProps> = ({
  attachmentPoint,
  availableMods,
  onModSelect,
  onRemoveMod,
  onClose,
  slotPosition
}) => {
  // Determine if we should show slots on the left or right based on slot position relative to screen center
  const screenWidth = window.innerWidth;
  const centerX = screenWidth / 2;
  const slotIsOnLeftSide = slotPosition.x < centerX;

  const showOnLeft = slotIsOnLeftSide;
  const spaceOnRight = screenWidth - slotPosition.x;
  const spaceOnLeft = slotPosition.x;
  const hasSpaceOnLeft = spaceOnLeft >= 200;
  const hasSpaceOnRight = spaceOnRight >= 200;

  const finalShowOnLeft = showOnLeft && hasSpaceOnLeft ? true :
    !showOnLeft && hasSpaceOnRight ? false :
      hasSpaceOnLeft;

  const slotsDirection = finalShowOnLeft ? -1 : 1;
  const baseOffset = finalShowOnLeft ? -80 : 80;

  return (
    <div className="fixed inset-0 pointer-events-none z-[9999]">
      {/* Close button */}      <div
        className="absolute w-8 h-8 bg-red-700 hover:bg-red-600 border-2 border-white rounded text-white font-bold transition-colors flex items-center justify-center cursor-pointer pointer-events-auto"
        style={{
          left: `${slotPosition.x + (finalShowOnLeft ? -100 : 100)}px`,
          top: `${slotPosition.y - 40}px`,
        }}
        data-close-button="true"
        onMouseDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onClose();
        }}
      >
        X
      </div>

      {/* Current Mod Slot (if any) */}      {attachmentPoint.currentMod && (<div
        className="absolute w-28 h-28 bg-neutral-800/70 border border-green-400 border-t-green-400/40 rounded-lg flex flex-col items-center justify-center overflow-hidden shadow-md cursor-pointer transition-all duration-200 ease-in-out hover:scale-110 hover:shadow-lg hover:shadow-red-400/30 hover:border-red-400/80 hover:bg-red-600/20 pointer-events-auto"
        style={{
          left: `${slotPosition.x + baseOffset}px`,
          top: `${slotPosition.y}px`,
          transform: 'translate(-50%, -50%)'
        }}
        data-mod-element="true"
        onMouseDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onRemoveMod(attachmentPoint.id);
        }}
        title={`Remove ${attachmentPoint.currentMod.name}`}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-green-400/10 via-transparent to-transparent pointer-events-none rounded-lg z-0" />
        {/* Remove indicator */}
        <div className="absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center z-30">
          <i className="fas fa-times text-white text-[6px]" />
        </div>
        <div className="w-full h-full flex items-center justify-center z-10">
          {isFontAwesomeIcon(attachmentPoint.currentMod.icon) ? (
            <i className={`fas ${attachmentPoint.currentMod.icon} text-xl text-neutral-200`} />
          ) : attachmentPoint.currentMod.icon ? (
            <img
              src={getModIconPath(attachmentPoint.currentMod.icon)}
              alt={attachmentPoint.currentMod.name}
              className="max-w-full max-h-full object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = '<i class="fas fa-cog text-xl text-neutral-200"></i>';
                }
              }}
            />
          ) : (
            <i className="fas fa-cog text-xl text-neutral-200" />
          )}
        </div>
        <div className="absolute bottom-0 left-0 right-0 z-20">
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-green-400/30 via-green-500/50 to-green-400/30" />
          <div className="absolute inset-0 bg-gradient-to-t from-neutral-900/80 via-neutral-900/40 to-transparent" />
          <div className="relative px-1 py-0.5">
            <p className="text-neutral-200 text-[8px] font-medium text-center truncate leading-tight">
              {attachmentPoint.currentMod.name}
            </p>
          </div>
        </div>
      </div>
      )}

      {/* Available Mods */}
      {availableMods.length > 0 ? (
        availableMods.map((mod, index) => {
          const modX = slotPosition.x + baseOffset + ((attachmentPoint.currentMod ? 1 : 0) + index) * 70 * slotsDirection;
          const modY = slotPosition.y;          return (<div
            key={mod.id}
            className="absolute w-28 h-28 bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg flex flex-col items-center justify-center overflow-hidden shadow-md cursor-pointer transition-all duration-200 ease-in-out hover:scale-105 hover:shadow-lg hover:shadow-green-400/10 hover:bg-neutral-700/70 hover:border-neutral-600 hover:border-t-green-400/40 pointer-events-auto"
            style={{
              left: `${modX}px`,
              top: `${modY}px`,
              transform: 'translate(-50%, -50%)'
            }}
            data-mod-element="true" onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log(`[WeaponModding] ComponentList: Mod clicked:`, mod);
              console.log(`[WeaponModding] ComponentList: Mod id:`, mod.id);
              onModSelect(attachmentPoint.id, mod);
            }}
            title={`${mod.name}${mod.description ? ` - ${mod.description}` : ''}`}
          >
            <div className="absolute inset-0 bg-gradient-to-b from-green-400/5 via-transparent to-transparent pointer-events-none rounded-lg z-0" />
            <div className="w-full h-full flex items-center justify-center z-10">
              {isFontAwesomeIcon(mod.icon) ? (
                <i className={`fas ${mod.icon} text-xl text-neutral-200`} />
              ) : mod.icon ? (
                <img
                  src={getModIconPath(mod.icon)}
                  alt={mod.name}
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = '<i class="fas fa-cog text-xl text-neutral-200"></i>';
                    }
                  }}
                />
              ) : (
                <i className="fas fa-cog text-xl text-neutral-200" />
              )}
            </div>
            <div className="absolute bottom-0 left-0 right-0 z-20">
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-green-400/30 via-green-500/50 to-green-400/30" />
              <div className="absolute inset-0 bg-gradient-to-t from-neutral-900/80 via-neutral-900/40 to-transparent" />
              <div className="relative px-1 py-0.5">
                <p className="text-neutral-200 text-[8px] font-medium text-center truncate leading-tight">
                  {mod.name}
                </p>
              </div>
            </div>
          </div>
          );
        })
      ) : (
        /* Empty State */        <div
          className="absolute w-28 h-28 bg-neutral-900/50 border border-neutral-800/60 border-dashed rounded-lg flex flex-col items-center justify-center overflow-hidden"
          style={{
            left: `${slotPosition.x + baseOffset}px`,
            top: `${slotPosition.y}px`,
            transform: 'translate(-50%, -50%)'
          }}
          title={`No ${attachmentPoint.slotType} mods available in inventory`}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-neutral-700/5 via-transparent to-transparent pointer-events-none rounded-lg z-0" />
          <i className="fas fa-ban text-neutral-600 text-lg opacity-40 z-10" />
          <div className="absolute bottom-0 left-0 right-0 z-20">
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-neutral-700/20 via-neutral-600/30 to-neutral-700/20" />
            <div className="absolute inset-0 bg-gradient-to-t from-neutral-900/60 via-neutral-900/30 to-transparent" />
            <div className="relative px-1 py-0.5">
              <p className="text-neutral-500 text-[7px] font-medium text-center truncate leading-tight opacity-60">
                None
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Main Weapon Modding Screen Component
const WeaponModdingScreen: React.FC = () => {  const {
    isVisible,
    isLoading,
    error,
    attachmentPoints,
    availableMods,
    currentWeapon,
    cameraFOV,
    isApplyingMod,
    showPositioningControls,
    actions
  } = useWeaponModdingStore();const [selectedPointId, setSelectedPointId] = useState<string | null>(null);
  const [showComponentList, setShowComponentList] = useState(false);
  const [selectedSlotPosition, setSelectedSlotPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });  // Click outside and ESC key handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showComponentList) {
        setShowComponentList(false);
        setSelectedPointId(null);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (!showComponentList) return;

      const target = event.target as HTMLElement;

      // Don't close if clicking on mod elements or close button
      if (target?.closest('[data-mod-element="true"]') ||
        target?.closest('[data-close-button="true"]')) {
        return;
      }

      // Close the component list if clicking elsewhere
      setShowComponentList(false);
      setSelectedPointId(null);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showComponentList]);

  const handleSlotClick = (pointId: string, _bonePosition: { x: number; y: number }, slotPosition: { x: number; y: number }) => {
    setSelectedPointId(pointId);
    setSelectedSlotPosition(slotPosition);
    setShowComponentList(true);
  }; const handleModSelect = (pointId: string, mod: AttachmentItem) => {
    console.log(`[WeaponModding] handleModSelect called with:`, { pointId, mod });
    console.log(`[WeaponModding] handleModSelect mod.id:`, mod.id);
    actions.applyMod(pointId, mod);
    setShowComponentList(false);
    setSelectedPointId(null);
  }; const handleRemoveMod = (pointId: string) => {
    actions.removeMod(pointId);
    setShowComponentList(false);
    setSelectedPointId(null);
  };

  const handleCloseComponentList = () => {
    setShowComponentList(false);
    setSelectedPointId(null);
  };

  const handleCloseUI = () => {
    actions.stopModding();
  };
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-40 pointer-events-auto">      {/* Close Button - Top Right */}
      <button
        onClick={handleCloseUI}
        className="fixed top-4 right-4 w-12 h-12 bg-neutral-800/80 hover:bg-red-600 border border-neutral-700/50 rounded-xl text-neutral-300 hover:text-white transition-all duration-200 flex items-center justify-center z-50 shadow-lg hover:shadow-red-500/20"
      >
        <i className="fas fa-times text-lg" />
      </button>      {/* Camera Controls - Top Right */}
      <div className="fixed top-4 right-20 flex flex-col gap-3 z-50">
        {/* FOV Slider */}
        <div className="bg-neutral-800/80 border border-neutral-700/50 rounded-lg p-3 shadow-lg">
          <div className="flex items-center gap-2 mb-2">
            <i className="fas fa-eye text-neutral-300 text-xs" />
            <span className="text-neutral-300 text-xs font-medium">FOV</span>
          </div>          <div className="flex items-center gap-2">
            <span className="text-neutral-400 text-xs">10</span>
            <input
              type="range"
              min="10"
              max="120"
              value={cameraFOV}
              onChange={(e) => actions.adjustCameraFOV(parseFloat(e.target.value))}
              className="flex-1 h-1 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #22c55e 0%, #22c55e ${((cameraFOV - 10) / (120 - 10)) * 100}%, #374151 ${((cameraFOV - 10) / (120 - 10)) * 100}%, #374151 100%)`
              }}
            />
            <span className="text-neutral-400 text-xs">120</span>
          </div>
          <div className="text-center mt-1">
            <span className="text-green-400 text-xs font-bold">{cameraFOV.toFixed(0)}°</span>
          </div>        </div>
      </div>{/* Main Content Area */}
      <div className="relative w-full h-full flex items-center justify-center pointer-events-none">
        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center z-30 pointer-events-auto">
            <div className="bg-neutral-800/90 border border-neutral-700 rounded-xl p-6 flex flex-col items-center gap-4">
              <div className="w-8 h-8 border-2 border-green-500/30 border-t-green-500 rounded-full animate-spin"></div>
              <p className="text-neutral-200">Loading weapon modifications...</p>
            </div>
          </div>
        )}        {/* Error State */}
        {error && (
          <div className="absolute top-20 left-1/2 transform -translate-x-1/2 bg-red-600/90 border border-red-500 rounded-lg p-4 text-white z-30 shadow-lg pointer-events-auto">
            <div className="flex items-center gap-2">
              <i className="fas fa-exclamation-triangle" />
              <span>{error}</span>
              <button
                onClick={() => actions.setError(null)}
                className="ml-2 hover:text-red-200"
              >
                <i className="fas fa-times" />
              </button>
            </div>
          </div>
        )}        {/* Weapon Info Header */}
        {currentWeapon && (
          <div className="absolute top-4 left-4 bg-neutral-800/80 border border-neutral-700/50 rounded-xl p-4 pointer-events-auto">
            <h2 className="text-xl font-bold text-neutral-100 flex items-center gap-2">
              <i className="fas fa-gun text-green-400/80" />
              {currentWeapon.label || currentWeapon.name}
              {isApplyingMod && (
                <div className="ml-2 flex items-center gap-1">
                  <div className="w-3 h-3 border border-green-500/30 border-t-green-500 rounded-full animate-spin"></div>
                  <span className="text-xs text-green-400">Applying...</span>
                </div>
              )}
            </h2>
            <p className="text-neutral-400 text-sm">{attachmentPoints.length} attachment points available</p>
          </div>
        )}{/* Instructions */}
        <div className="absolute bottom-4 left-4 bg-neutral-800/80 border border-neutral-700/50 rounded-xl p-4 max-w-sm pointer-events-auto">
          <h3 className="text-sm font-semibold text-neutral-100 mb-2 flex items-center gap-2">
            <i className="fas fa-info-circle text-green-400/80" />
            Instructions
          </h3>
          <ul className="text-xs text-neutral-300 space-y-1">
            <li>• Click on attachment slots to view available modifications</li>
            <li>• Green dots show attachment points on your weapon</li>
            <li>• Only modifications in your inventory are shown</li>
            <li>• Use camera controls to adjust the view</li>
          </ul>
        </div>        {/* Attachment Points */}
        <div className="fixed inset-0 pointer-events-none z-30">
          {/* Weapon Center Indicator - subtle crosshair */}
          <div
            className="absolute w-6 h-6 border border-green-400/30 rounded-full z-10 pointer-events-none"
            style={{
              left: `${(window.innerWidth || 1920) / 2}px`,
              top: `${(window.innerHeight || 1080) / 2}px`,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div className="absolute inset-0 border border-green-400/20 rounded-full scale-150" />
            <div className="absolute inset-0 border border-green-400/10 rounded-full scale-200" />
          </div>

          {(() => {
            // Calculate all slot positions first to check for overlaps
            const slotPositions: Array<{
              point: AttachmentPoint;
              boneX: number;
              boneY: number;
              slotX: number;
              slotY: number;
              angle: number;
            }> = [];

            const viewportWidth = window.innerWidth || 1920;
            const viewportHeight = window.innerHeight || 1080;
            const centerX = viewportWidth / 2;
            const centerY = viewportHeight / 2;

            // First pass: calculate initial positions
            attachmentPoints.forEach((point) => {
              const boneXPercent = parseFloat(point.position.x.toString().replace('%', ''));
              const boneYPercent = parseFloat(point.position.y.toString().replace('%', ''));

              const boneX = Math.max(0, Math.min((boneXPercent / 100) * viewportWidth, viewportWidth));
              const boneY = Math.max(0, Math.min((boneYPercent / 100) * viewportHeight, viewportHeight));

              const deltaX = boneX - centerX;
              const deltaY = boneY - centerY;
              const angle = Math.atan2(deltaY, deltaX);
              const boneDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

              const minOffsetFromBone = 60;
              const maxOffsetFromBone = 120;
              const minSlotDistance = Math.max(180, boneDistance + minOffsetFromBone);
              const maxSlotDistance = Math.min(350, Math.min(centerX, centerY) - 80);

              const baseSlotDistance = boneDistance + Math.min(maxOffsetFromBone, Math.max(minOffsetFromBone, boneDistance * 0.3));
              const slotDistance = Math.max(minSlotDistance, Math.min(maxSlotDistance, baseSlotDistance));

              const slotX = centerX + Math.cos(angle) * slotDistance;
              const slotY = centerY + Math.sin(angle) * slotDistance;

              slotPositions.push({
                point,
                boneX,
                boneY,
                slotX,
                slotY,
                angle
              });
            });            // Second pass: check for overlaps and adjust positions
            const slotSize = 112; // 28 * 4 (w-28 h-28 in pixels)
            const minDistance = slotSize + 20; // Minimum distance between slot centers

            for (let i = 0; i < slotPositions.length; i++) {
              for (let j = i + 1; j < slotPositions.length; j++) {
                const slot1 = slotPositions[i];
                const slot2 = slotPositions[j];

                const dx = slot2.slotX - slot1.slotX;
                const dy = slot2.slotY - slot1.slotY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < minDistance) {
                  // Slots are overlapping, push them apart
                  const pushDistance = (minDistance - distance) / 2;
                  const pushAngle = Math.atan2(dy, dx);

                  // Push slot1 away from slot2
                  slot1.slotX -= Math.cos(pushAngle) * pushDistance;
                  slot1.slotY -= Math.sin(pushAngle) * pushDistance;

                  // Push slot2 away from slot1
                  slot2.slotX += Math.cos(pushAngle) * pushDistance;
                  slot2.slotY += Math.sin(pushAngle) * pushDistance;

                  // Ensure slots stay within bounds
                  slot1.slotX = Math.max(slotSize / 2, Math.min(viewportWidth - slotSize / 2, slot1.slotX));
                  slot1.slotY = Math.max(slotSize / 2, Math.min(viewportHeight - slotSize / 2, slot1.slotY));
                  slot2.slotX = Math.max(slotSize / 2, Math.min(viewportWidth - slotSize / 2, slot2.slotX));
                  slot2.slotY = Math.max(slotSize / 2, Math.min(viewportHeight - slotSize / 2, slot2.slotY));
                }
              }
            }

            return slotPositions.map(({ point, boneX, boneY, slotX, slotY }) => (
              <div key={point.id}>
                {/* Bone Circle - positioned exactly on bone coordinates with distance indicator */}
                <div
                  className="absolute w-4 h-4 bg-green-500/80 border-2 border-green-400 rounded-full shadow-lg shadow-green-500/30 z-20 pointer-events-none"
                  style={{
                    left: `${boneX}px`,
                    top: `${boneY}px`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {/* Subtle pulse ring to indicate bone position */}
                  <div className="absolute inset-0 border-2 border-green-400/40 rounded-full animate-ping scale-150" />
                </div>                {/* Connection Line from bone to slot edge - straight line */}
                <svg
                  className="absolute pointer-events-none z-10"
                  style={{
                    left: `${Math.min(boneX, slotX) - 60}px`,
                    top: `${Math.min(boneY, slotY) - 60}px`,
                    width: `${Math.abs(slotX - boneX) + 120}px`,
                    height: `${Math.abs(slotY - boneY) + 120}px`
                  }}
                >
                  {/* Calculate line endpoint at slot edge */}
                  {(() => {
                    const lineLength = Math.sqrt((slotX - boneX) ** 2 + (slotY - boneY) ** 2);
                    const slotRadius = 56; // Half of w-28 h-28 (112px / 2)
                    
                    // Calculate direction vector (normalized)
                    const dirX = (slotX - boneX) / lineLength;
                    const dirY = (slotY - boneY) / lineLength;
                    
                    // Calculate edge point of slot (center minus radius in direction toward bone)
                    const edgeX = slotX - (dirX * slotRadius);
                    const edgeY = slotY - (dirY * slotRadius);

                    // SVG coordinate offsets
                    const svgOffsetX = Math.min(boneX, slotX) - 60;
                    const svgOffsetY = Math.min(boneY, slotY) - 60;

                    return (
                      <>
                        {/* Main connection line - straight from bone to slot edge */}
                        <line
                          x1={boneX - svgOffsetX}
                          y1={boneY - svgOffsetY}
                          x2={edgeX - svgOffsetX}
                          y2={edgeY - svgOffsetY}
                          stroke="rgba(34, 197, 94, 0.6)"
                          strokeWidth="2"
                          strokeDasharray="8,4"
                          className="animate-pulse"
                        />
                        {/* Small circle at bone end to show connection point */}
                        <circle
                          cx={boneX - svgOffsetX}
                          cy={boneY - svgOffsetY}
                          r="3"
                          fill="rgba(34, 197, 94, 0.4)"
                          stroke="rgba(34, 197, 94, 0.8)"
                          strokeWidth="1"
                        />
                      </>
                    );
                  })()}
                </svg>

                {/* Attachment Slot - positioned with offset from bone */}
                <div
                  className="absolute z-30 pointer-events-auto"
                  style={{
                    left: `${slotX}px`,
                    top: `${slotY}px`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <AttachmentSlot
                    attachmentPoint={point}
                    onSlotClick={handleSlotClick}
                    isSelected={selectedPointId === point.id}
                  />
                </div>
              </div>));
          })()}
        </div>        {/* Component List Modal */}
        {showComponentList && selectedPointId && (() => {
          const selectedPoint = attachmentPoints.find(p => p.id === selectedPointId);
          if (!selectedPoint) return null;
          // Use attachBone directly for available mods lookup
          // The selectedPoint.id is the bone name (e.g., 'WAPClip', 'WAPScop', etc.)
          // And the availableMods are now grouped by attachBone on the server side
          const attachBone = selectedPoint.id;
          // Directly access mods for this attachBone
          const modsForSlot: AttachmentItem[] = availableMods[attachBone] || [];

          return (
            <ComponentList
              attachmentPoint={selectedPoint}
              availableMods={modsForSlot}
              onModSelect={handleModSelect}
              onRemoveMod={handleRemoveMod}
              onClose={handleCloseComponentList}
              slotPosition={selectedSlotPosition}            />
          );
        })()}
        
        {/* Positioning Controls */}
        <PositioningControls isVisible={showPositioningControls} />
      </div>
    </div>
  );
};

export default WeaponModdingScreen;
