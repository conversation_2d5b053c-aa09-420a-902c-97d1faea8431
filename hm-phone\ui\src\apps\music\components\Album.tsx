import React, { useState } from 'react';
import { useNavigation } from '../../../navigation/hooks';
import SongCard from './SongCard';
import { useNavigationStore } from '../../../navigation/navigationStore';
import { useMusicStore } from '../stores/musicStore';
import { Album as AlbumType } from '../types/musicTypes';

const Album: React.FC = () => {
  const { goBack, openView } = useNavigation();
  // Get the current navigation entry and its data
  const currentEntry = useNavigationStore.getState().history.slice(-1)[0];
  const navigationData = currentEntry?.data || {};
  const albumId = Number(navigationData.albumId) || 1;
  const { albums, songs, setCurrentArtistId } = useMusicStore();
  const [otherAlbumsList] = useState<AlbumType[]>([]);

  // Find the current album
  const album = albums.find(a => a.id === albumId);

  // Get songs for this album
  const albumSongsList = songs.filter(s => s.albumId === albumId);

  if (!album) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-[#121212] text-white">
        Loading...
      </div>
    );
  }

  // Get the artist for this album
  const artist = album.artist ||
    albums.find(a => a.id === album.artistId)?.artist || { id: 0, name: 'Unknown Artist' };

  // Format the release date (using current year as fallback)
  const releaseYear = new Date().getFullYear();

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white pt-[32px]">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-2">
        <button onClick={goBack} className="text-white cursor-pointer">
          <i className="fas fa-arrow-left text-xl"></i>
        </button>
        <div className="text-center">
          <h1 className="text-lg font-medium">Album</h1>
        </div>
        <button className="text-white cursor-pointer">
          <i className="fas fa-ellipsis-v"></i>
        </button>
      </div>

      {/* Album Header */}
      <div className="px-4 py-4 flex items-center">
        <div className="w-24 h-24 rounded-lg overflow-hidden shadow-lg mr-4">
          <img
            src={album.imageUrl || 'https://picsum.photos/200/200?random=301'}
            alt={album.title}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <h2 className="text-xl font-bold">{album.title}</h2>
          <div
            className="text-sm text-gray-400 cursor-pointer hover:text-pink-500"
            onClick={() => {
              setCurrentArtistId(album.artistId);
              openView('artist', { artistId: album.artistId });
            }}
          >
            {artist.name}
          </div>
          <div className="text-xs text-gray-500 mt-1">Released: {releaseYear}</div>
          <div className="text-xs text-gray-500">{albumSongsList.length} songs</div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex px-4 py-2 space-x-4">
        <button className="bg-pink-500 text-white px-6 py-2 rounded-full font-medium flex-1 cursor-pointer">
          Play All
        </button>
        <button className="border border-white/30 text-white w-10 h-10 rounded-full flex items-center justify-center cursor-pointer">
          <i className="fas fa-heart"></i>
        </button>
        <button className="border border-white/30 text-white w-10 h-10 rounded-full flex items-center justify-center cursor-pointer">
          <i className="fas fa-share-alt"></i>
        </button>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto">
        {/* Songs List */}
        <div className="mb-6">
          <div className="px-4 py-2 border-b border-white/10 text-xs text-gray-500 flex">
            <div className="w-8 text-center">#</div>
            <div className="flex-1">TITLE</div>
            <div className="w-16 text-right">PLAYS</div>
            <div className="w-12 text-right">TIME</div>
          </div>

          <div className="space-y-1 px-4 pt-2">
            {albumSongsList.map((song, index) => (
              <SongCard
                key={song.id}
                song={{
                  id: song.id,
                  title: song.title,
                  artist: artist,
                  imageUrl: song.imageUrl,
                  duration: `${Math.floor(song.duration / 60)}:${(song.duration % 60)
                    .toString()
                    .padStart(2, '0')}`,
                  plays: `${Math.floor(Math.random() * 900) + 100}K`
                }}
                index={index}
                showIndex={true}
                showDuration={true}
                showPlays={true}
                compact={true}
              />
            ))}
          </div>
        </div>

        {/* More from Artist */}
        <div className="mb-6">
          <div className="px-4 flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">More from {artist.name}</h2>
            <button
              className="text-sm text-pink-500 cursor-pointer"
              onClick={() => {
                setCurrentArtistId(album.artistId);
                openView('artist', { artistId: album.artistId });
              }}
            >
              more
            </button>
          </div>

          <div className="flex overflow-x-auto space-x-4 px-4 pb-2">
            {otherAlbumsList.map(otherAlbum => (
              <div
                key={otherAlbum.id}
                className="flex-shrink-0 w-32 cursor-pointer"
                onClick={() => openView('album', { albumId: otherAlbum.id })}
              >
                <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
                  <img
                    src={otherAlbum.imageUrl || 'https://picsum.photos/200/200?random=302'}
                    alt={otherAlbum.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-sm font-medium text-white truncate">{otherAlbum.title}</h3>
                <p className="text-xs text-gray-400 truncate">{new Date().getFullYear()}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Album;
