/**
 * Player data types for the settings app
 * These types define the structure of player data used throughout the application
 */

export interface PlayerData {
  // Basic info (required fields)
  name: string; // Computed from firstname + lastname
  stateid: string; // Character identifier for multi-character support

  // Basic info (optional fields)
  firstName?: string; // From charinfo.firstname
  lastName?: string; // From charinfo.lastname
  phoneNumber?: string; // From charinfo.phone
  identifier?: string; // Server identifier

  // Job info
  job: {
    name: string; // From job.name
    label: string; // From job.label
    grade: number; // From job.grade.level
    gradeName?: string; // From job.grade.name
    payment?: number; // From job.payment
    onDuty?: boolean; // From job.onduty
    isBoss?: boolean; // From job.isboss
  };

  // Gang info (optional)
  gang?: {
    name: string; // From gang.name
    label: string; // From gang.label
    grade: number; // From gang.grade.level
    isBoss: boolean; // From gang.isboss
  };

  // Character status (optional)
  status?: {
    hunger: number; // From metadata.hunger
    thirst: number; // From metadata.thirst
    stress: number; // From metadata.stress
    isDead: boolean; // From metadata.isdead
  };

  // Additional info (optional)
  money?: {
    cash: number; // From money.cash
    bank: number; // From money.bank
  };

  imageUrl?: string; // Profile picture URL
}

export interface PhoneSettings {
  theme: string;
  wallpaper: number;
  ringtone: string;
  notificationSound: string;
  brightness: number;
  doNotDisturb: boolean;
  airplaneMode: boolean;
  hourFormat24: boolean;
  language: string;
  fontSize: string;
  // Allow additional settings with specific types
  [key: string]: string | number | boolean | object;
}
