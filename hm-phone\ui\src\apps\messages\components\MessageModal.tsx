import React, { useState } from 'react';
import { useMessagesStore } from '../stores/messagesStore';
import { useNavigation } from '../../../navigation/hooks';
import { Contact } from '@shared/types';

interface MessageModalProps {
  phoneNumber: string;
  name: string;
  onClose: () => void;
}

const MessageModal: React.FC<MessageModalProps> = ({ phoneNumber, name, onClose }) => {
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);
  const { actions, ui } = useMessagesStore();
  const { openAppView } = useNavigation();

  const handleSend = async () => {
    if (!message.trim() && !sending) {
      return;
    }

    setSending(true);
    try {
      // Create a contact object for the phone number
      const contact: Contact = {
        id: Date.now(),
        identifier: '',  // Will be set by the server
        stateid:  '',  // Will be set by the server
        number: phoneNumber,
        name: name,
        favorite: 0,
        avatar: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Create a message object
      const initialMessage = {
        id: 0,
        conversation_id: 0,
        sender: '',  // Will be set by the server
        message: message.trim(),
        type: 'text' as const,
        metadata: null,
        timestamp: new Date().toISOString(),
        is_deleted: 0,
        content: message.trim()
      };

      // Create a new conversation and get the ID
      const conversationId = await actions.createConversation([contact], initialMessage);

      if (conversationId) {
        // Set the active chat
        ui.setActiveChat(conversationId);

        // Navigate directly to the conversation view in the messages app
        openAppView('messages', 'conversation', { id: conversationId });

        // The initial message is already sent as part of createConversation
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#0a0f1a] p-4 rounded-lg max-w-xs w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold text-white">Message {name}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2 bg-[#1a1f2e] p-2 rounded-lg">
            <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
              <i className="fas fa-user text-blue-500/80"></i>
            </div>
            <div>
              <div className="text-white">{name}</div>
              <div className="text-gray-400 text-xs">{phoneNumber}</div>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <textarea
            value={message}
            onChange={e => setMessage(e.target.value)}
            placeholder="Type your message..."
            className="w-full bg-[#1a1f2e] text-white p-3 rounded-lg resize-none h-24 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSend}
            disabled={!message.trim() || sending}
            className="px-4 py-2 bg-blue-500/20 text-blue-500 rounded-lg hover:bg-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[80px]"
          >
            {sending ? (
              <span className="animate-pulse">...</span>
            ) : (
              <>
                Send <i className="fas fa-paper-plane ml-1"></i>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageModal;
