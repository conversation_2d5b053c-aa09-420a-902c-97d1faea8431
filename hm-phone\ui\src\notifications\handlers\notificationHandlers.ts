/**
 * Notification message handlers
 */
import { registerEvent<PERSON>and<PERSON> } from '../../fivem/clientEventReceiver';
import { useNotificationStore } from '../stores/notificationStore';
import { Notification } from '../types/notificationTypes';

// Define interfaces for notification message data
interface RemoveNotificationData {
  appId: number | string;
  notificationId: number | string;
}

interface ClearNotificationsData {
  appId?: number | string;
}

// Register handler for adding a notification
registerEventHandler('notifications', 'addNotification', data => {
  if (typeof data === 'object' && data !== null) {
    useNotificationStore.getState().addNotification(data as Notification);
  } else {
    console.error('[Notifications] Received invalid notification data:', data);
  }
});

// Register handler for removing a notification
registerEventHandler('notifications', 'removeNotification', data => {
  if (typeof data === 'object' && data !== null && 'appId' in data && 'notificationId' in data) {
    const typedData = data as RemoveNotificationData;
    useNotificationStore
      .getState()
      .removeNotification(Number(typedData.appId), Number(typedData.notificationId));
  } else {
    console.error('[Notifications] Received invalid removeNotification data:', data);
  }
});

// Register handler for clearing notifications
registerEventHandler('notifications', 'clearNotifications', data => {
  if (data && typeof data === 'object' && 'appId' in data) {
    const typedData = data as ClearNotificationsData;
    useNotificationStore.getState().clearNotifications(Number(typedData.appId));
  } else {
    useNotificationStore.getState().clearNotifications();
  }
});
