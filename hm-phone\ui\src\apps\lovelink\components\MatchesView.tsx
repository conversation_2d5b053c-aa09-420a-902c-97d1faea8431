import React from 'react';
import { lovelinkMockData } from '../../../fivem';

const MatchesView: React.FC = () => {
  const matches = lovelinkMockData.matches;

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Matches List */}
      <div className="flex-1 overflow-y-auto">
        {matches.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-white/60 p-4 text-center">
            <i className="fas fa-heart-broken text-3xl mb-2"></i>
            <p>No matches yet. Keep swiping!</p>
          </div>
        ) : (
          <div className="p-2">
            {matches.map(match => (
              <div
                key={match.id}
                onClick={() => {
                  /* Handle match click */
                }}
                className="flex items-center p-3 rounded-lg hover:bg-white/5 cursor-pointer transition-colors"
              >
                <div
                  className="w-12 h-12 rounded-full bg-cover bg-center mr-3"
                  style={{ backgroundImage: `url('default-avatar.png'})` }}
                />
                <div className="flex-1">
                  <h3 className="text-white font-medium">
                    {lovelinkMockData.profiles.find(p => p.id === match.matchedId)?.name}
                  </h3>
                  {match.lastMessage && (
                    <p className="text-white/60 text-sm truncate">{match.lastMessage.text}</p>
                  )}
                </div>
                {match.lastMessage && !match.lastMessage.isRead && (
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MatchesView;
