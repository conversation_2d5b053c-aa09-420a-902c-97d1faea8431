import { useHudStore } from '../store/hudStore';

// Helper function to determine which armor segments should be filled
const getArmorSegments = (armor: number): boolean[] => {
  const segments = [false, false, false, false, false];
  const segmentValue = 20; // Each segment represents 20% armor
  
  for (let i = 0; i < 5; i++) {
    const segmentThreshold = (i + 1) * segmentValue;
    if (armor >= segmentThreshold) {
      segments[i] = true;
    } else if (armor > i * segmentValue) {
      // Partial fill for the current segment
      segments[i] = true;
    }
  }
  
  return segments;
};

// Status bars showing health, armor, oxygen, hunger, thirst, and stress
const StatusBars = () => {
  const { hudData, settings } = useHudStore();

  // Only show armor when player has armor
  const hasArmor = hudData.armor > 0;

  // Only show oxygen when underwater (oxygen < 100)
  const isUnderwater = hudData.oxygen < 100;

  return (
    <div className="animate-slide-in-left flex items-end space-x-4">
      {/* Main horizontal status bars */}
      <div className="flex flex-col space-y-2">
        {/* Health bar - always visible */}
        {settings.show.health && (
          <div className="flex items-center space-x-2 h-4">
            <i className="fas fa-heart text-gray-200 text-xs drop-shadow-lg w-4 flex items-center justify-center"></i>
            <div className="relative h-3 w-[13vw] bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm">
              <div
                className="h-full bg-gradient-to-r from-gray-100 to-gray-300 transition-all duration-300 shadow-inner"
                style={{ width: `${hudData.health}%` }}
              />
            </div>
          </div>
        )}

        {/* Armor bar - only when player has armor */}
        {settings.show.armor && hasArmor && (
          <div className="flex items-center space-x-2 h-4">
            <i className="fas fa-shield-alt text-blue-400 text-xs drop-shadow-lg w-4 flex items-center justify-center"></i>
            <div className="flex space-x-1">
              {getArmorSegments(hudData.armor).map((isFilled, index) => {
                const segmentValue = 20; // Each segment represents 20%
                const segmentMin = index * segmentValue;
                const segmentMax = (index + 1) * segmentValue;
                const segmentFill = Math.max(0, Math.min(100, ((hudData.armor - segmentMin) / segmentValue) * 100));
                
                return (
                  <div 
                    key={index}
                    className="relative h-2 w-[2.44vw] bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm"
                  >
                    {isFilled && (
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-300 shadow-inner"
                        style={{ width: `${segmentFill}%` }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Vertical bars section */}
      <div className="flex items-end space-x-2 h-16">
        {/* Hunger - vertical bar */}
        <div className="flex flex-col items-center space-y-1">
          <div className="relative w-2 h-12 bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm">
            <div
              className="absolute bottom-0 w-full bg-gradient-to-t from-orange-400 to-orange-600 transition-all duration-300 shadow-inner"
              style={{ height: `${hudData.hunger}%` }}
            />
          </div>
          <i className="fas fa-utensils text-orange-400 text-xs drop-shadow-lg"></i>
        </div>

        {/* Thirst - vertical bar */}
        <div className="flex flex-col items-center space-y-1">
          <div className="relative w-2 h-12 bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm">
            <div
              className="absolute bottom-0 w-full bg-gradient-to-t from-blue-300 to-blue-500 transition-all duration-300 shadow-inner"
              style={{ height: `${hudData.thirst}%` }}
            />
          </div>
          <i className="fas fa-tint text-blue-300 text-xs drop-shadow-lg"></i>
        </div>

        {/* Stress - vertical bar */}
        <div className="flex flex-col items-center space-y-1">
          <div className="relative w-2 h-12 bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm">
            <div
              className="absolute bottom-0 w-full bg-gradient-to-t from-purple-400 to-purple-600 transition-all duration-300 shadow-inner"
              style={{ height: `${hudData.stress}%` }}
            />
          </div>
          <i className="fas fa-brain text-purple-400 text-xs drop-shadow-lg"></i>
        </div>

        {/* Oxygen - vertical bar, only when underwater */}
        {settings.show.oxygen && isUnderwater && (
          <div className="flex flex-col items-center space-y-1">
            <div className="relative w-2 h-12 bg-black/90 border border-neutral-600/60 rounded-sm overflow-hidden backdrop-blur-sm">
              <div
                className="absolute bottom-0 w-full bg-gradient-to-t from-cyan-400 to-cyan-600 transition-all duration-300 shadow-inner"
                style={{ height: `${hudData.oxygen}%` }}
              />
            </div>
            <i className="fas fa-lungs text-cyan-400 text-xs drop-shadow-lg"></i>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatusBars;
