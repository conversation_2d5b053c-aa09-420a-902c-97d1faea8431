import { ItemSlot, InventoryItem } from '@shared/inventory.types';
import { ItemType } from '@shared/items.types';

// Type definitions for smart stacking features
export interface StackOptimizationSuggestion {
  itemId: string;
  itemLabel: string;
  currentSlots: number;
  optimalSlots: number;
  totalQuantity: number;
  spaceSaved: number;
  slots: number[];
}

export interface SplitSuggestion {
  targetSlotIndex: number;
  splitQuantity: number;
  remainingQuantity: number;
  reason: string;
}

export interface StackHealthAnalysis {
  totalStacks: number;
  inefficientStacks: number;
  potentialSpaceSaved: number;
  recommendations: Array<{
    itemLabel: string;
    action: 'consolidate' | 'fill';
    currentSlots?: number;
    optimalSlots?: number;
    spaceSaved?: number;
    currentQuantity?: number;
    maxStack?: number;
    efficiency?: number;
  }>;
}

export interface SortOptions {
  method: 'type' | 'name' | 'quantity' | 'weight' | 'rarity';
  direction: 'asc' | 'desc';
  groupStackable?: boolean;
}

/**
 * Sort and organize inventory items
 */
export const sortInventoryItems = (items: ItemSlot[], options: SortOptions): ItemSlot[] => {
  const { method, direction, groupStackable = true } = options;
  
  // Filter out empty slots and get items
  const filledSlots = items.filter(slot => slot.item);
  
  // Group stackable items first if enabled
  const processedSlots = groupStackable ? consolidateStackableItems(filledSlots) : filledSlots;
  
  // Sort based on method
  const sortedSlots = processedSlots.sort((a, b) => {
    if (!a.item || !b.item) return 0;
    
    let comparison = 0;
    
    switch (method) {
      case 'type':
        comparison = compareByType(a.item, b.item);
        break;
      case 'name':
        comparison = a.item.label.localeCompare(b.item.label);
        break;
      case 'quantity':
        comparison = (a.quantity || 1) - (b.quantity || 1);
        break;
      case 'weight':
        comparison = (a.item.weight || 0) - (b.item.weight || 0);
        break;
      case 'rarity':
        comparison = compareByRarity(a.item, b.item);
        break;
      default:
        comparison = 0;
    }
    
    return direction === 'desc' ? -comparison : comparison;
  });

  // Reassign indices
  const result: ItemSlot[] = [];
  let currentIndex = 0;
  
  // Add sorted items
  sortedSlots.forEach(slot => {
    result.push({ ...slot, index: currentIndex++ });
  });
  
  // Fill remaining slots with empty slots
  while (currentIndex < items.length) {
    result.push({ index: currentIndex++, item: undefined, quantity: undefined });
  }
  
  return result;
};

/**
 * Consolidate stackable items into fewer slots
 */
export const consolidateStackableItems = (slots: ItemSlot[]): ItemSlot[] => {
  const consolidatedMap = new Map<string, ItemSlot>();
  
  slots.forEach(slot => {
    if (!slot.item) return;
    
    const key = slot.item.id;
    const existing = consolidatedMap.get(key);
    
    if (existing && slot.item.stackable) {
      // Add to existing stack
      existing.quantity = (existing.quantity || 1) + (slot.quantity || 1);
    } else {
      // Create new entry
      consolidatedMap.set(key, { ...slot });
    }
  });
  
  return Array.from(consolidatedMap.values());
};

/**
 * Compare items by type priority
 */
const compareByType = (a: InventoryItem, b: InventoryItem): number => {
  const typePriority: Partial<Record<ItemType, number>> = {
    [ItemType.WEAPON]: 1,
    [ItemType.GADGET]: 2,
    [ItemType.GENERAL]: 3,
    [ItemType.CONSUMABLE]: 4,
  };
  
  const aPriority = typePriority[a.type] || 999;
  const bPriority = typePriority[b.type] || 999;
  
  return aPriority - bPriority;
};

/**
 * Compare items by rarity (if metadata contains rarity info)
 */
const compareByRarity = (a: InventoryItem, b: InventoryItem): number => {
  // Default rarity values if not in metadata
  const getRarity = (item: InventoryItem): number => {
    if (item.metadata?.rarity) return item.metadata.rarity;
    
    // Fallback to type-based rarity
    switch (item.type) {
      case ItemType.WEAPON: return 3;
      case ItemType.GADGET: return 2;
      case ItemType.CONSUMABLE: return 1;
      default: return 0;
    }
  };
  
  return getRarity(b) - getRarity(a); // Higher rarity first
};

/**
 * Find optimal slot for a new item (smart placement)
 */
export const findOptimalSlot = (
  items: ItemSlot[], 
  newItem: InventoryItem, 
  quantity: number = 1
): number | null => {
  // First, try to stack with existing items
  if (newItem.stackable) {
    for (let i = 0; i < items.length; i++) {
      const slot = items[i];
      if (slot.item?.id === newItem.id) {
        const currentQuantity = slot.quantity || 1;
        const maxStack = newItem.maxStack || 999;
        
        if (currentQuantity + quantity <= maxStack) {
          return i;
        }
      }
    }
  }
  
  // Find first empty slot
  for (let i = 0; i < items.length; i++) {
    if (!items[i].item) {
      return i;
    }
  }
  
  return null; // No space available
};

/**
 * Calculate inventory statistics
 */
export const calculateInventoryStats = (items: ItemSlot[]) => {
  const stats = {
    totalItems: 0,
    totalWeight: 0,
    usedSlots: 0,
    availableSlots: 0,
    itemsByType: {} as Partial<Record<ItemType, number>>,
    stackableItems: 0,
    nonStackableItems: 0,
  };
  
  items.forEach(slot => {
    if (slot.item) {
      stats.usedSlots++;
      stats.totalItems += slot.quantity || 1;
      stats.totalWeight += (slot.item.weight || 0) * (slot.quantity || 1);
      
      const type = slot.item.type;
      stats.itemsByType[type] = (stats.itemsByType[type] || 0) + (slot.quantity || 1);
      
      if (slot.item.stackable) {
        stats.stackableItems += slot.quantity || 1;
      } else {
        stats.nonStackableItems++;
      }
    } else {
      stats.availableSlots++;
    }
  });
  
  return stats;
};

/**
 * Smart item transfer suggestions
 */
export const getTransferSuggestions = (
  targetItems: ItemSlot[],
  item: InventoryItem,
  quantity: number = 1
): Array<{ slotIndex: number; reason: string; priority: number }> => {
  const suggestions: Array<{ slotIndex: number; reason: string; priority: number }> = [];
  
  // Priority 1: Stack with existing items
  if (item.stackable) {
    targetItems.forEach((slot, index) => {
      if (slot.item?.id === item.id) {
        const currentQuantity = slot.quantity || 1;
        const maxStack = item.maxStack || 999;
        const canStack = maxStack - currentQuantity;
        
        if (canStack >= quantity) {
          suggestions.push({
            slotIndex: index,
            reason: `Stack with existing ${item.label} (${currentQuantity}/${maxStack})`,
            priority: 1,
          });
        }
      }
    });
  }
  
  // Priority 2: Empty slots
  targetItems.forEach((slot, index) => {
    if (!slot.item) {
      suggestions.push({
        slotIndex: index,
        reason: 'Empty slot',
        priority: 2,
      });
    }
  });
  
  // Sort by priority
  return suggestions.sort((a, b) => a.priority - b.priority);
};

/**
 * Analyze inventory for stack optimization opportunities
 */
export const analyzeStackOptimization = (items: ItemSlot[]): StackOptimizationSuggestion[] => {
  const suggestions: StackOptimizationSuggestion[] = [];
  const itemGroups = new Map<string, ItemSlot[]>();
  
  // Group items by ID
  items.forEach(slot => {
    if (slot.item?.stackable) {
      const key = slot.item.id;
      if (!itemGroups.has(key)) {
        itemGroups.set(key, []);
      }
      itemGroups.get(key)!.push(slot);
    }
  });
  
  // Find optimization opportunities
  itemGroups.forEach((slots, itemId) => {
    if (slots.length > 1) {
      const item = slots[0].item!;
      const maxStack = item.maxStack || 999;
      const totalQuantity = slots.reduce((sum, slot) => sum + (slot.quantity || 1), 0);
      const optimalSlots = Math.ceil(totalQuantity / maxStack);
      
      if (optimalSlots < slots.length) {
        suggestions.push({
          itemId,
          itemLabel: item.label,
          currentSlots: slots.length,
          optimalSlots,
          totalQuantity,
          spaceSaved: slots.length - optimalSlots,
          slots: slots.map(slot => slot.index),
        });
      }
    }
  });
  
  return suggestions.sort((a, b) => b.spaceSaved - a.spaceSaved);
};

/**
 * Auto-optimize stacks in inventory
 */
export const autoOptimizeStacks = (items: ItemSlot[]): ItemSlot[] => {
  const result = [...items];
  const optimizations = analyzeStackOptimization(items);
  
  optimizations.forEach(opt => {
    const itemSlots = opt.slots.map((index: number) => result[index]).filter((slot: ItemSlot) => slot.item);
    if (itemSlots.length === 0) return;
    
    const item = itemSlots[0].item!;
    const maxStack = item.maxStack || 999;
    let remainingQuantity = opt.totalQuantity;
    
    // Clear old slots
    opt.slots.forEach((index: number) => {
      result[index] = { index, item: undefined, quantity: undefined };
    });
    
    // Fill optimal slots
    let slotIndex = 0;
    while (remainingQuantity > 0 && slotIndex < opt.slots.length) {
      const targetIndex = opt.slots[slotIndex];
      const quantity = Math.min(remainingQuantity, maxStack);
      
      result[targetIndex] = {
        index: targetIndex,
        item,
        quantity,
      };
      
      remainingQuantity -= quantity;
      slotIndex++;
    }
  });
  
  return result;
};

/**
 * Get suggestions for splitting an item stack
 */
export const getSplitSuggestions = (
  items: ItemSlot[],
  sourceSlot: ItemSlot,
  targetQuantity: number
): SplitSuggestion[] => {
  if (!sourceSlot.item?.stackable) return [];
  
  const currentQuantity = sourceSlot.quantity || 1;
  if (targetQuantity >= currentQuantity) return [];
  
  const suggestions: SplitSuggestion[] = [];
  const remainingQuantity = currentQuantity - targetQuantity;
  
  // Find empty slots for the split
  items.forEach((slot, index) => {
    if (!slot.item && index !== sourceSlot.index) {
      suggestions.push({
        targetSlotIndex: index,
        splitQuantity: targetQuantity,
        remainingQuantity,
        reason: 'Move to empty slot',
      });
    }
  });
  
  // Find existing stacks that can accommodate the split
  items.forEach((slot, index) => {
    if (slot.item?.id === sourceSlot.item!.id && index !== sourceSlot.index) {
      const maxStack = sourceSlot.item!.maxStack || 999;
      const currentSlotQuantity = slot.quantity || 1;
      const availableSpace = maxStack - currentSlotQuantity;
      
      if (availableSpace >= targetQuantity) {
        suggestions.push({
          targetSlotIndex: index,
          splitQuantity: targetQuantity,
          remainingQuantity,
          reason: `Stack with existing ${sourceSlot.item!.label} (${currentSlotQuantity}/${maxStack})`,
        });
      }
    }
  });
  
  return suggestions;
};

/**
 * Smart consolidation with better logic
 */
export const smartConsolidateItems = (items: ItemSlot[]): ItemSlot[] => {
  const result: ItemSlot[] = items.map(slot => ({ ...slot }));
  const itemGroups = new Map<string, ItemSlot[]>();
  
  // Group stackable items
  result.forEach(slot => {
    if (slot.item?.stackable) {
      const key = slot.item.id;
      if (!itemGroups.has(key)) {
        itemGroups.set(key, []);
      }
      itemGroups.get(key)!.push(slot);
    }
  });
  
  // Consolidate each group
  itemGroups.forEach(slots => {
    if (slots.length <= 1) return;
    
    const item = slots[0].item!;
    const maxStack = item.maxStack || 999;
    let totalQuantity = slots.reduce((sum, slot) => sum + (slot.quantity || 1), 0);
    
    // Clear all slots first
    slots.forEach(slot => {
      const index = result.findIndex(s => s.index === slot.index);
      if (index !== -1) {
        result[index] = { index: slot.index, item: undefined, quantity: undefined };
      }
    });
    
    // Fill slots optimally
    let slotIndex = 0;
    while (totalQuantity > 0 && slotIndex < slots.length) {
      const targetSlot = slots[slotIndex];
      const quantity = Math.min(totalQuantity, maxStack);
      
      const resultIndex = result.findIndex(s => s.index === targetSlot.index);
      if (resultIndex !== -1) {
        result[resultIndex] = {
          index: targetSlot.index,
          item,
          quantity,
        };
      }
      
      totalQuantity -= quantity;
      slotIndex++;
    }
  });
  
  return result;
};

/**
 * Get stack health analysis
 */
export const getStackHealthAnalysis = (items: ItemSlot[]): StackHealthAnalysis => {
  const analysis: StackHealthAnalysis = {
    totalStacks: 0,
    inefficientStacks: 0,
    potentialSpaceSaved: 0,
    recommendations: [],
  };
  
  const itemGroups = new Map<string, ItemSlot[]>();
  
  // Group stackable items
  items.forEach(slot => {
    if (slot.item?.stackable) {
      const key = slot.item.id;
      if (!itemGroups.has(key)) {
        itemGroups.set(key, []);
      }
      itemGroups.get(key)!.push(slot);
    }
  });
  
  itemGroups.forEach(slots => {
    analysis.totalStacks += slots.length;
    
    if (slots.length > 1) {
      const item = slots[0].item!;
      const maxStack = item.maxStack || 999;
      const totalQuantity = slots.reduce((sum, slot) => sum + (slot.quantity || 1), 0);
      const optimalSlots = Math.ceil(totalQuantity / maxStack);
      
      if (optimalSlots < slots.length) {
        analysis.inefficientStacks += slots.length;
        analysis.potentialSpaceSaved += slots.length - optimalSlots;
        
        analysis.recommendations.push({
          itemLabel: item.label,
          action: 'consolidate',
          currentSlots: slots.length,
          optimalSlots,
          spaceSaved: slots.length - optimalSlots,
        });
      }
    }
    
    // Check for partial stacks
    slots.forEach(slot => {
      const quantity = slot.quantity || 1;
      const maxStack = slot.item!.maxStack || 999;
      
      if (quantity < maxStack * 0.5) { // Less than 50% full
        analysis.recommendations.push({
          itemLabel: slot.item!.label,
          action: 'fill',
          currentQuantity: quantity,
          maxStack,
          efficiency: Math.round((quantity / maxStack) * 100),
        });
      }
    });
  });
  
  return analysis;
};
