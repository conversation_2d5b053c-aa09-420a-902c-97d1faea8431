/// <reference types="@citizenfx/client" />

interface Vector3 {
    x: number;
    y: number;
    z: number;
}

export class WeaponModdingCoords {
    static worldToScreen(worldPos: Vector3): { x: number; y: number } | null {
        const [onScreen, screenX, screenY] = GetScreenCoordFromWorldCoord(
            worldPos.x, worldPos.y, worldPos.z
        );

        if (!onScreen) {
            return null;
        }

        const result = {
            x: screenX * 100, // Convert to percentage
            y: screenY * 100
        };

        return result;
    }      // Get attachment bone world coordinates
    static getAttachmentBoneCoords(weaponEntity: number, boneName: string): Vector3 | null {

        let boneIndex = GetEntityBoneIndexByName(weaponEntity, boneName);

        // If bone not found (returns -1), return null
        if (boneIndex === -1) {
            return null;
        }

        const [x, y, z] = GetWorldPositionOfEntityBone(weaponEntity, boneIndex);
        const result = { x, y, z };
        return result;
    }
}

export class WeaponModdingManager {
    private weaponEntity: number | null = null;
    private originalCam: number = 0;
    private moddingCam: number = 0;
    private attachmentPoints: Map<string, Vector3> = new Map();    public isModding: boolean = false;  // Make this public
    private weaponHeading: number = 0;  // Track weapon heading
    private cameraFOV: number = 20.0;   // Adjustable camera FOV
    private weaponHashKey: number = 0;  // Store weapon hash for ped operations
    private cameraSetupInterval: NodeJS.Timeout | null = null;  // Track interval for cleanup
    
    // Manual positioning controls
    private benchPosition: Vector3 = { x: 0, y: 0, z: 0 };  // Store bench position as reference
    private weaponOffset: Vector3 = { x: 0.6, y: 0, z: 1.5 };  // Weapon offset from bench
    private cameraOffset: Vector3 = { x: -2.5, y: 0, z: 0.6 };  // Camera offset from bench
    private weaponRotationOffset: Vector3 = { x: 0, y: 0, z: 0 };  // Weapon rotation offsets (pitch, roll, yaw)
    private cameraRotationOffset: Vector3 = { x: 0, y: 0, z: 0 };  // Camera pitch, roll, yaw offsets
    private controlsActive: boolean = false;  // Are manual controls active
      async startModding(weaponHash: string | number, customPosition?: Vector3) {
        if (this.isModding) {
            return;
        }
        
        const playerPed = PlayerPedId();
        const coords = GetEntityCoords(playerPed, false);
        const heading = GetEntityHeading(playerPed);
        
        // Handle both string and number weapon hashes
        if (typeof weaponHash === 'number') {
            this.weaponHashKey = weaponHash;
        } else {
            this.weaponHashKey = GetHashKey(weaponHash);
        }

        console.log(`[WeaponModding Client] Starting modding - Hash: ${weaponHash}, Key: ${this.weaponHashKey}`);

        // Check if ped already has the weapon, if not give it to them
        if (!HasPedGotWeapon(playerPed, this.weaponHashKey, false)) {
            GiveWeaponToPed(playerPed, this.weaponHashKey, 1, false, false);
        }

        // Request weapon asset for weapon object
        RequestWeaponAsset(this.weaponHashKey, 31, 0);
        while (!HasWeaponAssetLoaded(this.weaponHashKey)) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }        // Use custom position if provided, otherwise calculate spawn position in front of player
        let spawnCoords: Vector3;
        if (customPosition) {
            spawnCoords = customPosition;
        } else {
            // Calculate spawn position in front of player based on their heading
            const radians = (heading * Math.PI) / 180;
            spawnCoords = {
                x: coords[0] + Math.sin(radians) * 2.0,  // 2 units in front
                y: coords[1] + Math.cos(radians) * 2.0,
                z: coords[2] + 0.8  // Slightly above ground level
            };
        }

        console.log(`[WeaponModding Client] Weapon spawn position:`, JSON.stringify(spawnCoords));

        this.weaponEntity = CreateWeaponObject(
            this.weaponHashKey,
            1,
            spawnCoords.x, spawnCoords.y, spawnCoords.z,
            true, 1.0, 0
        );// Freeze the weapon object so it doesn't fall due to physics
        if (this.weaponEntity) {
            FreezeEntityPosition(this.weaponEntity, true);
            SetEntityCollision(this.weaponEntity, false, false);            // Rotate weapon to face the other direction (opposite side)
            this.weaponHeading = heading + 180; // Face the opposite direction
            SetEntityHeading(this.weaponEntity, this.weaponHeading);console.log(`[WeaponModding Client] Weapon entity created and positioned`);

            // Synchronize any existing components from ped to weapon object
            this.synchronizeComponentsToWeaponObject();

            // Wait a frame for the entity to fully initialize before calculating bones
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        // Setup camera
        this.setupModdingCamera(spawnCoords);

        // Wait for camera setup and then calculate attachment points
        setTimeout(() => {
            this.calculateAttachmentPoints();
        }, 2000);

        // Request available mods from server
        emitNet('hm-weapons:getAvailableMods', weaponHash);

        this.isModding = true;

        // Open NUI
        SetNuiFocus(true, true);
        SendNuiMessage(JSON.stringify({
            type: 'OPEN_WEAPON_MODDING',
            data: { weaponHash }
        }));
    }    stopModding() {
        if (!this.isModding) return;

        // Disable manual controls
        this.controlsActive = false;        // Hide positioning controls in UI
        SendNuiMessage(JSON.stringify({
            type: 'SHOW_POSITIONING_CONTROLS',
            data: { show: false }
        }));

        // Clean up any running intervals
        if (this.cameraSetupInterval) {
            clearInterval(this.cameraSetupInterval);
            this.cameraSetupInterval = null;
        }

        // Clean up weapon entity
        if (this.weaponEntity) {
            DeleteObject(this.weaponEntity);
            this.weaponEntity = null;
        }

        // Restore camera
        if (this.moddingCam) {
            SetCamActive(this.moddingCam, false);
            DestroyCam(this.moddingCam, false);
            RenderScriptCams(false, true, 1000, true, false);
        }

        // Close NUI
        SetNuiFocus(false, false);
        SendNuiMessage(JSON.stringify({
            type: 'CLOSE_WEAPON_MODDING'
        }));

        this.isModding = false;
        this.attachmentPoints.clear();
    }/**
     * Setup manual positioning system with ammunation_downtown bench as reference
     */
    private setupModdingCamera(weaponCoords: Vector3) {
        // Set ammunation_downtown as reference bench
        this.benchPosition = { x: -1574.53, y: -236.06, z: 60.22 };
        
        console.log(`[WeaponModding] Using ammunation_downtown bench at [${this.benchPosition.x}, ${this.benchPosition.y}, ${this.benchPosition.z}] as reference`);

        // Initialize positions and enable manual controls
        this.updateWeaponPosition();
        this.updateCameraPosition();
        this.controlsActive = true;
        this.displayCurrentOffsets();

        // Start control loop and send initial offsets to UI
        this.startControlLoop();
        this.sendOffsetsToUI();        // Send positioning controls activation to UI
        SendNuiMessage(JSON.stringify({
            type: 'SHOW_POSITIONING_CONTROLS',
            data: { show: true }
        }));
    }

    /**
     * Update weapon position based on current offsets from bench
     */
    private updateWeaponPosition() {
        if (!this.weaponEntity || !DoesEntityExist(this.weaponEntity)) return;

        const benchHeading = 339.68; // ammunation_downtown heading
        const benchRadians = (benchHeading * Math.PI) / 180;
        
        // Calculate weapon world position from bench position + offsets
        const weaponWorldPos = {
            x: this.benchPosition.x + 
               this.weaponOffset.x * Math.cos(benchRadians) - 
               this.weaponOffset.y * Math.sin(benchRadians),
            y: this.benchPosition.y + 
               this.weaponOffset.x * Math.sin(benchRadians) + 
               this.weaponOffset.y * Math.cos(benchRadians),
            z: this.benchPosition.z + this.weaponOffset.z
        };        SetEntityCoords(this.weaponEntity, weaponWorldPos.x, weaponWorldPos.y, weaponWorldPos.z, false, false, false, true);
        
        // Apply 3-axis rotation
        const finalRotation = {
            x: this.weaponRotationOffset.x,
            y: this.weaponRotationOffset.y,
            z: benchHeading + this.weaponRotationOffset.z
        };
        SetEntityRotation(this.weaponEntity, finalRotation.x, finalRotation.y, finalRotation.z, 2, true);

        FreezeEntityPosition(this.weaponEntity, true);
        SetEntityCollision(this.weaponEntity, false, false);
    }

    /**
     * Update camera position based on current offsets from bench
     */
    private updateCameraPosition() {
        if (!this.moddingCam) {
            this.moddingCam = CreateCam("DEFAULT_SCRIPTED_CAMERA", false);
        }

        const benchHeading = 339.68; // ammunation_downtown heading
        const benchRadians = (benchHeading * Math.PI) / 180;
        
        // Calculate camera world position from bench position + offsets
        const cameraWorldPos = {
            x: this.benchPosition.x + 
               this.cameraOffset.x * Math.cos(benchRadians) - 
               this.cameraOffset.y * Math.sin(benchRadians),
            y: this.benchPosition.y + 
               this.cameraOffset.x * Math.sin(benchRadians) + 
               this.cameraOffset.y * Math.cos(benchRadians),
            z: this.benchPosition.z + this.cameraOffset.z
        };

        SetCamCoord(this.moddingCam, cameraWorldPos.x, cameraWorldPos.y, cameraWorldPos.z);        SetCamRot(this.moddingCam, 
            this.cameraRotationOffset.x, 
            this.cameraRotationOffset.y, 
            benchHeading + this.cameraRotationOffset.z,
            2 // rotation order
        );
        SetCamFov(this.moddingCam, this.cameraFOV || 20.0);
        SetCamActive(this.moddingCam, true);
        RenderScriptCams(true, true, 500, true, false);
    }    /**
     * Display current offsets in console
     */
    private displayCurrentOffsets() {
        console.log(`[WeaponModding] ===== CURRENT OFFSETS =====`);
        console.log(`[WeaponModding] Weapon Offset: X=${this.weaponOffset?.x?.toFixed(2) || '0.00'}, Y=${this.weaponOffset?.y?.toFixed(2) || '0.00'}, Z=${this.weaponOffset?.z?.toFixed(2) || '0.00'}`);
        console.log(`[WeaponModding] Weapon Rotation: X=${this.weaponRotationOffset?.x?.toFixed(2) || '0.00'}°, Y=${this.weaponRotationOffset?.y?.toFixed(2) || '0.00'}°, Z=${this.weaponRotationOffset?.z?.toFixed(2) || '0.00'}°`);
        console.log(`[WeaponModding] Camera Offset: X=${this.cameraOffset?.x?.toFixed(2) || '0.00'}, Y=${this.cameraOffset?.y?.toFixed(2) || '0.00'}, Z=${this.cameraOffset?.z?.toFixed(2) || '0.00'}`);
        console.log(`[WeaponModding] Camera Rotation: Pitch=${this.cameraRotationOffset?.x?.toFixed(2) || '0.00'}°, Roll=${this.cameraRotationOffset?.y?.toFixed(2) || '0.00'}°, Yaw=${this.cameraRotationOffset?.z?.toFixed(2) || '0.00'}°`);
        console.log(`[WeaponModding] FOV: ${this.cameraFOV?.toFixed(1) || '20.0'}`);
        console.log(`[WeaponModding] ============================`);
    }    /**
     * Start the control loop for manual positioning - now just sends offset data to UI
     */
    private startControlLoop() {
        const uiUpdateInterval = setInterval(() => {
            if (!this.controlsActive || !this.isModding) {
                clearInterval(uiUpdateInterval);
                return;
            }
            // Send current offsets to UI for display
            this.sendOffsetsToUI();
        }, 100); // Update UI every 100ms
    }

    /**
     * Send current offset values to UI for display
     */    private sendOffsetsToUI() {
        SendNuiMessage(JSON.stringify({
            type: 'UPDATE_OFFSETS',
            data: {
                weaponPosition: {
                    x: this.weaponOffset?.x || 0,
                    y: this.weaponOffset?.y || 0,
                    z: this.weaponOffset?.z || 0
                },                weaponRotation: {
                    x: this.weaponRotationOffset?.x || 0,
                    y: this.weaponRotationOffset?.y || 0,
                    z: this.weaponRotationOffset?.z || 0
                },
                cameraPosition: {
                    x: this.cameraOffset?.x || 0,
                    y: this.cameraOffset?.y || 0,
                    z: this.cameraOffset?.z || 0
                },
                cameraRotation: {
                    x: this.cameraRotationOffset?.x || 0,
                    y: this.cameraRotationOffset?.y || 0,
                    z: this.cameraRotationOffset?.z || 0
                }
            }
        }));
    }

    /**
     * Adjust weapon position via UI button
     */
    public adjustWeaponPosition(axis: 'x' | 'y' | 'z', direction: number) {
        if (!this.isModding) return;

        const step = 0.1;
        this.weaponOffset[axis] += direction * step;
        
        this.updateWeaponPosition();
        this.displayCurrentOffsets();
        this.sendOffsetsToUI();
    }

    /**
     * Adjust weapon rotation via UI button
     */    public adjustWeaponRotation(axis: 'x' | 'y' | 'z', direction: number) {
        if (!this.isModding) return;

        const rotStep = 5.0;
        this.weaponRotationOffset[axis] += direction * rotStep;
        
        this.updateWeaponPosition();
        this.displayCurrentOffsets();
        this.sendOffsetsToUI();
    }

    /**
     * Adjust camera position via UI button
     */
    public adjustCameraPosition(axis: 'x' | 'y' | 'z', direction: number) {
        if (!this.isModding) return;

        const step = 0.1;
        this.cameraOffset[axis] += direction * step;
        
        this.updateCameraPosition();
        this.displayCurrentOffsets();
        this.sendOffsetsToUI();
    }

    /**
     * Adjust camera rotation via UI button
     */
    public adjustCameraRotation(axis: 'x' | 'y' | 'z', direction: number) {
        if (!this.isModding) return;

        const rotStep = 5.0;
        this.cameraRotationOffset[axis] += direction * rotStep;
        
        this.updateCameraPosition();
        this.displayCurrentOffsets();
        this.sendOffsetsToUI();
    }private availableModsData: any = null; // Store available mods data

    // Store the available mods data when received
    setAvailableModsData(data: any) {
        this.availableModsData = data;
        // Recalculate attachment points using actual weapon definition bones
        this.calculateAttachmentPoints();
    }    private calculateAttachmentPoints() {
        if (!this.weaponEntity) {
            return;
        }

        // Clear existing points
        this.attachmentPoints.clear();

        // If we have available mods data, use the attach bone names from it
        let attachmentBones: string[] = [];

        if (this.availableModsData) {
            // Extract unique attach bone names from the available mods
            const boneSet = new Set<string>();
            Object.values(this.availableModsData).forEach((mods: any) => {
                if (Array.isArray(mods)) {
                    mods.forEach((mod: any) => {
                        if (mod.attachBone) {
                            boneSet.add(mod.attachBone);
                        }
                    });
                }
            });
            attachmentBones = Array.from(boneSet);
            console.log(`[WeaponModding Client] Using weapon-specific bones:`, JSON.stringify(attachmentBones));
        } else {
            // Fallback to standard attachment bone names
            attachmentBones = [
                'WAPClip',        // Magazine/Clip
                'WAPFlshLasr',    // Flashlight/Laser  
                'WAPScop',        // Scope/Optics
                'WAPGrip',        // Grip
                'WAPScop_2',      // Secondary Scope
                'WAPSupp',        // Suppressor/Muzzle
                'gun_root',       // Weapon skin/finish
                'gun_gripr'       // Mk2 upgrades
            ];
            console.log(`[WeaponModding Client] Using standard bones (no weapon data)`);
        }
        
        attachmentBones.forEach(boneName => {
            const coords = WeaponModdingCoords.getAttachmentBoneCoords(this.weaponEntity!, boneName);
            if (coords) {
                this.attachmentPoints.set(boneName, coords);
            }
        });

        console.log(`[WeaponModding Client] Found ${this.attachmentPoints.size} attachment points`);

        // Force immediate UI update
        this.updateUIAttachmentPoints();
    }

    private updateUIAttachmentPoints() {
        const uiPoints: Array<{ id: string, position: { x: string, y: string } }> = [];

        this.attachmentPoints.forEach((worldCoords, boneName) => {
            const screenCoords = WeaponModdingCoords.worldToScreen(worldCoords);
            if (screenCoords) {
                uiPoints.push({
                    id: boneName,
                    position: {
                        x: `${screenCoords.x}%`,
                        y: `${screenCoords.y}%`
                    }
                });
            }
        });

        // Send to NUI
        SendNuiMessage(JSON.stringify({
            type: 'UPDATE_ATTACHMENT_POINTS',
            data: uiPoints
        }));
    } async applyMod(componentName: string) {
        if (!this.weaponEntity) {
            console.warn(`[WeaponModding Client] No weapon entity for applying mod ${componentName}`);
            return;
        }

        const playerPed = PlayerPedId();
        const componentHash = GetHashKey(componentName);
        const componentModel = GetWeaponComponentTypeModel(componentHash);

        console.log(`[WeaponModding Client] ===== APPLYING COMPONENT =====`);
        console.log(`[WeaponModding Client] Component Name: ${componentName}`);
        console.log(`[WeaponModding Client] Component Hash: ${componentHash} (0x${componentHash.toString(16).toUpperCase()})`);
        console.log(`[WeaponModding Client] Component Model: ${componentModel} (0x${componentModel.toString(16).toUpperCase()})`);
        console.log(`[WeaponModding Client] Weapon Object: ${this.weaponEntity}`);
        console.log(`[WeaponModding Client] Weapon Hash: ${this.weaponHashKey}`);

        // Check if component hash is valid
        if (componentHash === 0) {
            console.error(`[WeaponModding Client] ERROR: Invalid component name ${componentName}`);
            return;
        }

        // Check if weapon entity still exists
        if (!DoesEntityExist(this.weaponEntity)) {
            console.error(`[WeaponModding Client] ERROR: Weapon entity ${this.weaponEntity} no longer exists!`);
            return;
        }

        // Load the component model if not already loaded (using componentModel hash)
        if (!HasModelLoaded(componentModel)) {
            console.log(`[WeaponModding Client] Requesting component model: ${componentModel}`);
            RequestModel(componentModel);
            while (!HasModelLoaded(componentModel)) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
            console.log(`[WeaponModding Client] Component model loaded successfully`);
        } else {
            console.log(`[WeaponModding Client] Component model already loaded`);
        }

        // Apply to the ped's weapon (for inventory synchronization)
        console.log(`[WeaponModding Client] Applying component to ped weapon...`);
        GiveWeaponComponentToPed(playerPed, this.weaponHashKey, componentHash);

        // Verify ped has component
        const pedHasComponent = HasPedGotWeaponComponent(playerPed, this.weaponHashKey, componentHash);
        console.log(`[WeaponModding Client] Ped has component: ${pedHasComponent}`);

        // Apply to the weapon object (for visual display in UI)
        console.log(`[WeaponModding Client] Applying component to weapon object...`);
        GiveWeaponComponentToWeaponObject(this.weaponEntity, componentHash);

        // Verify weapon object has the component
        const weaponObjectHasComponent = HasWeaponGotWeaponComponent(this.weaponEntity, componentHash);
        console.log(`[WeaponModding Client] Weapon object has component: ${weaponObjectHasComponent}`);

        // Wait a frame for the change to take effect
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log(`[WeaponModding Client] Component ${componentName} application completed`);
        console.log(`[WeaponModding Client] ===== END APPLY =====`);
    } removeMod(componentName: string) {
        if (!this.weaponEntity) {
            console.warn(`[WeaponModding Client] No weapon entity for removing mod ${componentName}`);
            return;
        }

        const playerPed = PlayerPedId();
        const componentHash = GetHashKey(componentName);
        const componentModel = GetWeaponComponentTypeModel(componentHash);

        console.log(`[WeaponModding Client] ===== REMOVING COMPONENT =====`);
        console.log(`[WeaponModding Client] Component Name: ${componentName}`);
        console.log(`[WeaponModding Client] Component Hash: ${componentHash} (0x${componentHash.toString(16).toUpperCase()})`);
        console.log(`[WeaponModding Client] Component Model: ${componentModel} (0x${componentModel.toString(16).toUpperCase()})`);
        console.log(`[WeaponModding Client] Weapon Object: ${this.weaponEntity}`);
        console.log(`[WeaponModding Client] Weapon Hash: ${this.weaponHashKey}`);

        // Check if component hash is valid
        if (componentHash === 0) {
            console.error(`[WeaponModding Client] ERROR: Invalid component name ${componentName}`);
            return;
        }

        // Check if weapon entity still exists
        if (!DoesEntityExist(this.weaponEntity)) {
            console.error(`[WeaponModding Client] ERROR: Weapon entity ${this.weaponEntity} no longer exists!`);
            return;
        }

        // Remove from the ped's weapon (for inventory synchronization)
        console.log(`[WeaponModding Client] Removing component from ped weapon...`);
        RemoveWeaponComponentFromPed(playerPed, this.weaponHashKey, componentHash);

        // Verify ped no longer has the component
        const pedHasComponent = HasPedGotWeaponComponent(playerPed, this.weaponHashKey, componentHash);
        console.log(`[WeaponModding Client] Ped still has component: ${pedHasComponent}`);

        // Remove from the weapon object (for visual display in UI)
        console.log(`[WeaponModding Client] Removing component from weapon object...`);
        RemoveWeaponComponentFromWeaponObject(this.weaponEntity, componentHash);

        // Verify weapon object no longer has the component
        const weaponObjectHasComponent = HasWeaponGotWeaponComponent(this.weaponEntity, componentHash);
        console.log(`[WeaponModding Client] Weapon object still has component: ${weaponObjectHasComponent}`);

        console.log(`[WeaponModding Client] Component ${componentName} removal completed`);
        console.log(`[WeaponModding Client] ===== END REMOVE =====`);
    }

    // Methods for adjusting camera and weapon positioning
    adjustCameraFOV(newFOV: number) {
        this.cameraFOV = Math.max(10.0, Math.min(120.0, newFOV)); // Clamp between 10-120

        if (this.moddingCam && this.isModding) {
            SetCamFov(this.moddingCam, this.cameraFOV);
            console.log(`[WeaponModding Client] Camera FOV adjusted to: ${this.cameraFOV}`);

            // Recalculate attachment points since FOV change affects screen coordinates
            setTimeout(() => {
                this.calculateAttachmentPoints();
                this.updateUIAttachmentPoints();
            }, 50); // Small delay to allow FOV change to take effect
        }
    }

    rotateWeapon(headingOffset: number) {
        if (this.weaponEntity && this.isModding) {
            this.weaponHeading += headingOffset;
            SetEntityHeading(this.weaponEntity, this.weaponHeading);

            // Recalculate attachment points after rotation
            this.calculateAttachmentPoints();
            this.updateUIAttachmentPoints();

            console.log(`[WeaponModding Client] Weapon rotated to heading: ${this.weaponHeading}`);
        }
    }

    resetWeaponPosition() {
        if (this.weaponEntity && this.isModding) {
            const playerPed = PlayerPedId();
            const coords = GetEntityCoords(playerPed, false);
            const heading = GetEntityHeading(playerPed);

            // Recalculate spawn position
            const radians = (heading * Math.PI) / 180;
            const spawnCoords = {
                x: coords[0] + Math.sin(radians) * 2.0,
                y: coords[1] + Math.cos(radians) * 2.0,
                z: coords[2] + 0.8
            };            // Reset weapon position and heading
            SetEntityCoords(this.weaponEntity, spawnCoords.x, spawnCoords.y, spawnCoords.z, false, false, false, true);
            this.weaponHeading = heading + 180; // Face the other direction (consistent with startModding)
            SetEntityHeading(this.weaponEntity, this.weaponHeading);

            // Update camera to point at new position
            this.setupModdingCamera(spawnCoords);

            // Recalculate attachment points after reset
            setTimeout(() => {
                this.calculateAttachmentPoints();
            }, 200);
        }
    }    /**
     * Setup camera with bench context for optimal positioning
     */
    public setupCameraWithBenchContext(weaponCoords: Vector3, benchId?: string) {
        console.log(`[WeaponModding] Setting up camera with bench context: ${benchId || 'none'}`);
        
        // If we have bench context, get additional positioning info
        let benchHeading = 0;
        let benchCoords = weaponCoords;
        
        if (benchId) {
            // Try to get bench information from the bench manager
            // For now, we'll use the weapon position as primary reference
            console.log(`[WeaponModding] Using bench context for camera positioning: ${benchId}`);
        }
        
        // Setup camera using the standard method but with bench awareness
        this.setupModdingCamera(weaponCoords);
    }

    /**
     * Get current weapon entity (for debugging)
     */
    public getWeaponEntity(): number | null {
        return this.weaponEntity;
    }

    // Synchronize existing weapon components from ped to weapon object
    private synchronizeComponentsToWeaponObject() {
        if (!this.weaponEntity || !this.weaponHashKey) {
            return;
        }

        const playerPed = PlayerPedId();
        console.log(`[WeaponModding Client] Synchronizing existing components from ped to weapon object...`);

        // Common weapon components to check and sync
        const commonComponents = [
            'COMPONENT_AT_PI_FLSH',      // Pistol Flashlight
            'COMPONENT_AT_PI_SUPP',      // Pistol Suppressor
            'COMPONENT_AT_PI_SUPP_02',   // Pistol Suppressor 2
            'COMPONENT_PISTOL_CLIP_02',  // Extended Clip
            'COMPONENT_AT_AR_FLSH',      // AR Flashlight
            'COMPONENT_AT_AR_SUPP',      // AR Suppressor
            'COMPONENT_AT_AR_SUPP_02',   // AR Suppressor 2
            'COMPONENT_AT_SCOPE_LARGE',  // Large Scope
            'COMPONENT_AT_AR_AFGRIP',    // Foregrip
            'COMPONENT_ASSAULTRIFLE_CLIP_02', // AR Extended Clip
            'COMPONENT_ASSAULTRIFLE_CLIP_03', // AR Drum Mag
        ];

        let syncedCount = 0;

        commonComponents.forEach(componentName => {
            const componentHash = GetHashKey(componentName);

            // Check if ped has this component
            if (HasPedGotWeaponComponent(playerPed, this.weaponHashKey, componentHash)) {
                console.log(`[WeaponModding Client] Syncing component: ${componentName}`);

                // Apply to weapon object
                GiveWeaponComponentToWeaponObject(this.weaponEntity!, componentHash);
                syncedCount++;
            }
        });

        console.log(`[WeaponModding Client] Synchronized ${syncedCount} components to weapon object`);
    }
}

// Initialize the modding manager
const moddingManager = new WeaponModdingManager();

// Event handlers for server communication
onNet('hm-weapons:availableModsResponse', (availableMods: any) => {
    console.log(`[WeaponModding Client] Received available mods from server:`, availableMods);

    // Store the available mods data in the modding manager
    moddingManager.setAvailableModsData(availableMods);

    // Send the available mods to the UI
    SendNuiMessage(JSON.stringify({
        type: 'UPDATE_AVAILABLE_MODS',
        data: availableMods
    }));

    // Also stop the loading state
    SendNuiMessage(JSON.stringify({
        type: 'SET_LOADING',
        data: { loading: false }
    }));
});

onNet('hm-weapons:modApplied', (data: any) => {
    console.log(`[WeaponModding Client] Mod applied:`, data);

    // Notify NUI that mod was applied
    SendNuiMessage(JSON.stringify({
        type: 'MOD_APPLIED',
        data: data
    }));
});

onNet('hm-weapons:modRemoved', (data: any) => {
    console.log(`[WeaponModding Client] Mod removed:`, data);

    // Notify NUI that mod was removed
    SendNuiMessage(JSON.stringify({
        type: 'MOD_REMOVED',
        data: data
    }));
});

onNet('hm-weapons:applyComponent', async (componentName: string) => {
    console.log(`[WeaponModding Client] Applying component to weapon: ${componentName}`);

    if (moddingManager.isModding) {
        await moddingManager.applyMod(componentName);
    } else {
        console.warn(`[WeaponModding Client] Cannot apply component - not in modding mode`);
    }
});

onNet('hm-weapons:removeComponent', (componentName: string) => {
    console.log(`[WeaponModding Client] Removing component from weapon: ${componentName}`);

    if (moddingManager.isModding) {
        moddingManager.removeMod(componentName);
    } else {
        console.warn(`[WeaponModding Client] Cannot remove component - not in modding mode`);
    }
});

// NUI Callbacks
RegisterNuiCallback('startModding', (data: any, cb: Function) => {
    const customPosition = data.weaponPosition ? data.weaponPosition : undefined;
    moddingManager.startModding(data.weaponHash, customPosition);
    cb('ok');
});

RegisterNuiCallback('stopModding', (data: any, cb: Function) => {
    moddingManager.stopModding();
    cb('ok');
});

RegisterNuiCallback('applyMod', async (data: any, cb: Function) => {
    console.log(`[WeaponModding Client] Applying mod:`, data);
    emitNet('hm-weapons:applyMod', data.componentName);
    cb('ok');
});

RegisterNuiCallback('removeMod', (data: any, cb: Function) => {
    console.log(`[WeaponModding Client] Removing mod:`, data);
    emitNet('hm-weapons:removeMod', data.componentName);
    cb('ok');
});

RegisterNuiCallback('adjustFOV', (data: any, cb: Function) => {
    moddingManager.adjustCameraFOV(data.fov);
    cb('ok');
});

RegisterNuiCallback('rotateWeapon', (data: any, cb: Function) => {
    moddingManager.rotateWeapon(data.headingOffset);
    cb('ok');
});

RegisterNuiCallback('resetWeaponPosition', (data: any, cb: Function) => {
    moddingManager.resetWeaponPosition();
    cb('ok');
});

// NUI Callbacks for manual positioning
RegisterNuiCallback('adjustWeaponPosition', (data: { axis: 'x' | 'y' | 'z'; direction: number }, cb: Function) => {
    moddingManager.adjustWeaponPosition(data.axis, data.direction);
    cb('ok');
});

RegisterNuiCallback('adjustWeaponRotation', (data: { axis: 'x' | 'y' | 'z'; direction: number }, cb: Function) => {
    moddingManager.adjustWeaponRotation(data.axis, data.direction);
    cb('ok');
});

RegisterNuiCallback('adjustCameraPosition', (data: { axis: 'x' | 'y' | 'z'; direction: number }, cb: Function) => {
    moddingManager.adjustCameraPosition(data.axis, data.direction);
    cb('ok');
});

RegisterNuiCallback('adjustCameraRotation', (data: { axis: 'x' | 'y' | 'z'; direction: number }, cb: Function) => {
    moddingManager.adjustCameraRotation(data.axis, data.direction);
    cb('ok');
});

RegisterNuiCallback('copyOffsets', (data: unknown, cb: Function) => {
    // Log the final offsets for easy copying
    const offsets = {
        weaponOffset: {
            x: parseFloat((moddingManager as any).weaponOffset.x.toFixed(2)),
            y: parseFloat((moddingManager as any).weaponOffset.y.toFixed(2)),
            z: parseFloat((moddingManager as any).weaponOffset.z.toFixed(2))
        },
        weaponRotation: parseFloat((moddingManager as any).weaponRotationOffset.toFixed(2)),
        cameraOffset: {
            x: parseFloat((moddingManager as any).cameraOffset.x.toFixed(2)),
            y: parseFloat((moddingManager as any).cameraOffset.y.toFixed(2)),
            z: parseFloat((moddingManager as any).cameraOffset.z.toFixed(2))
        },
        cameraRotation: {
            pitch: parseFloat((moddingManager as any).cameraRotationOffset.x.toFixed(2)),
            roll: parseFloat((moddingManager as any).cameraRotationOffset.y.toFixed(2)),
            yaw: parseFloat((moddingManager as any).cameraRotationOffset.z.toFixed(2))
        },
        fov: parseFloat((moddingManager as any).cameraFOV.toFixed(1))
    };
    
    console.log(`[WeaponModding] ===== FINAL OFFSETS FOR CODE =====`);
    console.log(`[WeaponModding] weaponOffset: ${JSON.stringify(offsets.weaponOffset)}`);
    console.log(`[WeaponModding] weaponRotation: ${offsets.weaponRotation}`);
    console.log(`[WeaponModding] cameraOffset: ${JSON.stringify(offsets.cameraOffset)}`);
    console.log(`[WeaponModding] cameraRotation: ${JSON.stringify(offsets.cameraRotation)}`);
    console.log(`[WeaponModding] fov: ${offsets.fov}`);
    console.log(`[WeaponModding] =====================================`);
    
    cb('ok');
});

// Command to show manual control instructions
RegisterCommand('weaponcontrols', (source: number, args: string[]) => {
    console.log(`[WeaponModding] ===== MANUAL CONTROL INSTRUCTIONS =====`);
    console.log(`[WeaponModding] WEAPON POSITION:`);
    console.log(`[WeaponModding]   Arrow Up/Down: Move weapon forward/backward`);
    console.log(`[WeaponModding]   Arrow Left/Right: Move weapon left/right`);
    console.log(`[WeaponModding]   Shift + Arrow Up/Down: Move weapon up/down`);
    console.log(`[WeaponModding]   Q/E: Rotate weapon left/right`);
    console.log(`[WeaponModding] `);
    console.log(`[WeaponModding] CAMERA POSITION:`);
    console.log(`[WeaponModding]   W/S: Move camera forward/backward`);
    console.log(`[WeaponModding]   A/D: Move camera left/right`);
    console.log(`[WeaponModding]   Alt + W/S: Move camera up/down`);
    console.log(`[WeaponModding]   Page Up/Down: Camera pitch up/down`);
    console.log(`[WeaponModding]   Home/End: Camera yaw left/right`);
    console.log(`[WeaponModding] `);
    console.log(`[WeaponModding] REFERENCE: ammunation_downtown bench`);
    console.log(`[WeaponModding] All positions are offsets from the bench location`);
    console.log(`[WeaponModding] =========================================`);
}, false);

// Command to manually trigger ammunition bench testing
RegisterCommand('testammu', (source: number, args: string[]) => {
    if (moddingManager.isModding) {
        console.log(`[WeaponModding] Stopping current weapon modding session`);
        moddingManager.stopModding();
    } else {
        console.log(`[WeaponModding] Starting weapon modding at ammunation_downtown bench`);
          // Use ammunation_downtown bench coordinates for weapon positioning
        const ammuPosition = { x: -1574.53, y: -236.06, z: 60.22 };
        moddingManager.startModding('WEAPON_ASSAULTRIFLE', ammuPosition);
    }
}, false);

// Test command - with toggle functionality
RegisterCommand('weaponmod', (source: number, args: string[]) => {
    console.log(`[WeaponModding Client] ========== /weaponmod command executed ==========`);

    if (moddingManager.isModding) {
        console.log(`[WeaponModding Client] Stopping weapon modding (already active)`);
        moddingManager.stopModding();
    } else {
        const weaponHash = args[0] || 'WEAPON_ASSAULTRIFLE';
        moddingManager.startModding(weaponHash);
    }
}, false);

// Commands for testing camera and weapon adjustments
RegisterCommand('weaponfov', (source: number, args: string[]) => {
    if (!moddingManager.isModding) {
        console.log(`[WeaponModding Client] Weapon modding not active - use /weaponmod first`);
        return;
    }

    const fov = parseFloat(args[0]);
    if (isNaN(fov)) {
        return;
    }

    console.log(`[WeaponModding Client] Adjusting camera FOV to: ${fov}`);
    moddingManager.adjustCameraFOV(fov);
}, false);

RegisterCommand('weaponcam', (source: number, args: string[]) => {
    if (!moddingManager.isModding) {
        console.log(`[WeaponModding Client] Weapon modding not active - use /weaponmod first`);
        return;
    }

    // Force camera recalculation
    const weaponEntity = moddingManager.getWeaponEntity();
    if (weaponEntity && DoesEntityExist(weaponEntity)) {
        const weaponCoords = GetEntityCoords(weaponEntity, false);
        console.log(`[WeaponModding Client] Recalculating camera for weapon at [${weaponCoords[0].toFixed(2)}, ${weaponCoords[1].toFixed(2)}, ${weaponCoords[2].toFixed(2)}]`);
        
        // Force camera setup
        moddingManager.setupCameraWithBenchContext({
            x: weaponCoords[0],
            y: weaponCoords[1], 
            z: weaponCoords[2]
        });
    }
}, false);

RegisterCommand('weaponrotate', (source: number, args: string[]) => {
    if (!moddingManager.isModding) {
        console.log(`[WeaponModding Client] Weapon modding not active - use /weaponmod first`);
        return;
    }

    const rotation = parseFloat(args[0]) || 15; // Default 15 degrees
    console.log(`[WeaponModding Client] Rotating weapon by: ${rotation} degrees`);
    moddingManager.rotateWeapon(rotation);
}, false);

RegisterCommand('weaponreset', (source: number, args: string[]) => {
    if (!moddingManager.isModding) {
        console.log(`[WeaponModding Client] Weapon modding not active - use /weaponmod first`);
        return;
    }

    console.log(`[WeaponModding Client] Resetting weapon position`);
    moddingManager.resetWeaponPosition();
}, false);

console.log('[hm-weapons] Weapon modding client loaded successfully');

// Event handler for starting weapon modding - can be triggered from any resource
on('hm-weapons:startweaponmodding', (data: { weaponHash: string | number; weaponPosition: any; benchId: string }) => {
    console.log(`[WeaponModding Client] Starting weapon modding:`, data);

    const customPosition = data.weaponPosition;

    // Start the weapon modding process - pass weaponHash directly (can be string or number)
    moddingManager.startModding(data.weaponHash, customPosition);
});

// Event handler for receiving available mods from server
onNet('hm-weapons:availableModsResponse', (availableModsData: any) => {
    console.log(`[WeaponModding Client] Received available mods:`, JSON.stringify(availableModsData));
    moddingManager.setAvailableModsData(availableModsData);
});