import React, { useEffect, useState } from 'react';
import { useMusicStore } from '../../apps/music/stores/musicStore';
import { useNavigation } from '../../navigation/hooks';
import { motion } from 'framer-motion';

interface NotificationMusicPlayerProps {
  closeDrawer: () => void;
}

const NotificationMusicPlayer: React.FC<NotificationMusicPlayerProps> = ({ closeDrawer }) => {
  const { currentSong, isPlaying, currentTime, duration, togglePlay, nextSong, previousSong } =
    useMusicStore();
  const { openAppView } = useNavigation();

  // Force re-render every second to update progress bar
  const [, setForceUpdate] = useState(0);

  useEffect(() => {
    if (isPlaying && currentSong) {
      const forceUpdateTimer = setInterval(() => {
        // This forces a re-render which will update the progress bar
        setForceUpdate(prev => prev + 1);
      }, 1000);

      return () => clearInterval(forceUpdateTimer);
    }
  }, [isPlaying, currentSong]);

  if (!currentSong) return null;

  // Calculate progress percentage
  const progress = ((currentTime || 0) / (duration || 1)) * 100;

  const handleOpenMusicApp = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Use openAppView to directly navigate to the specific view within the app
    // This works even when no app is currently active
    openAppView('music', 'nowPlaying', { songId: currentSong.id });
    // Close the drawer when navigating
    closeDrawer();
  };

  return (
    <motion.div
      className="col-span-2 row-span-2 rounded-xl overflow-hidden h-auto aspect-square relative group"
      onClick={handleOpenMusicApp}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      style={{
        backgroundImage: `url(${currentSong.imageUrl || 'https://picsum.photos/200'})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-900/80 to-purple-900/95"></div>
      {/* Additional dark overlay */}
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Equalizer bars background */}
      <div className="absolute inset-0 flex items-end justify-around px-2 pb-16 opacity-20">
        {[...Array(16)].map((_, i) => {
          const height = isPlaying
            ? 20 + Math.sin(i * 0.8) * 30 + Math.cos(i * 0.4) * 20
            : 10 + (i % 5) * 5;

          return (
            <div
              key={i}
              className="w-1.5 bg-pink-500 rounded-t-sm"
              style={{
                height: `${height}%`,
                opacity: isPlaying ? 0.7 : 0.3
              }}
            ></div>
          );
        })}
      </div>

      {/* Main content */}
      <div className="absolute inset-0 flex flex-col justify-between p-3 overflow-hidden">
        {/* Song info */}
        <div className="mb-2">
          <h3 className="text-white font-medium truncate">{currentSong.title}</h3>
          <p className="text-gray-300 text-sm truncate">
            {currentSong.artist?.name || 'Unknown Artist'}
          </p>

          {/* Playing status */}
          {isPlaying && (
            <div className="flex items-center mt-1">
              <div className="flex space-x-0.5">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-2 bg-pink-500 rounded-full animate-pulse"
                    style={{ animationDelay: `${i * 0.15}s` }}
                  ></div>
                ))}
              </div>
              <span className="text-pink-500 text-xs ml-1.5">Playing</span>
            </div>
          )}
        </div>

        {/* Time display with progress bar */}
        <div className="my-auto w-full">
          <div className="flex justify-between mb-0.5 px-0.5">
            <div className="text-[10px] text-gray-300/80">
              {Math.floor((currentTime || 0) / 60)}:
              {String(Math.floor((currentTime || 0) % 60)).padStart(2, '0')}
            </div>

            <div className="text-[10px] text-gray-300/80">
              {Math.floor((duration || 0) / 60)}:
              {String(Math.floor((duration || 0) % 60)).padStart(2, '0')}
            </div>
          </div>

          <div className="h-1 bg-black/30 rounded-full overflow-hidden w-full">
            <div
              className="h-full bg-pink-500 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center space-x-6">
          <motion.button
            onClick={e => {
              e.stopPropagation();
              previousSong();
            }}
            className="flex items-center justify-center text-white hover:text-pink-500 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <i className="fas fa-step-backward"></i>
          </motion.button>

          <motion.button
            onClick={e => {
              e.stopPropagation();
              togglePlay();
            }}
            className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center text-white hover:bg-pink-600 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            animate={{
              rotate: isPlaying ? [0, 5, 0, -5, 0] : 0,
              transition: {
                repeat: isPlaying ? Infinity : 0,
                repeatType: 'loop',
                duration: isPlaying ? 2 : 0,
                ease: 'easeInOut'
              }
            }}
          >
            <i
              className={`fas ${isPlaying ? 'fa-pause' : 'fa-play'} ${
                isPlaying ? '' : 'ml-0.5'
              } text-xs`}
            ></i>
          </motion.button>

          <motion.button
            onClick={e => {
              e.stopPropagation();
              nextSong();
            }}
            className="flex items-center justify-center text-white hover:text-pink-500 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <i className="fas fa-step-forward"></i>
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default NotificationMusicPlayer;
