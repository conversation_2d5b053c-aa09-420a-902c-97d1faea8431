import React, { useEffect, useState } from 'react';
import { useServicesStore } from './stores/servicesStore';
import { useNavigation } from '../../navigation/hooks';
import ServiceCard from './components/ServiceCard';
import CategoryFilter from './components/CategoryFilter';
import LoadingSpinner from '../../common/components/LoadingSpinner';

const Services: React.FC = () => {
  const { services, loading, error, favorites } = useServicesStore();
  const { searchTerm, ui, actions } = useServicesStore();
  const { goBack } = useNavigation();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get all unique categories
  const categories = ui.getAllCategories();

  // Filter services based on search term and selected category
  const filteredServices = searchTerm
    ? ui.searchServices(searchTerm)
    : selectedCategory
    ? ui.getServicesByCategory(selectedCategory)
    : services;

  // Load services when component mounts
  useEffect(() => {
    actions.getServices();
  }, [actions]);

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8">
      {/* Search header */}
      <div className="flex items-center gap-4 px-4 py-2">
        <button onClick={goBack} className="text-white/80 hover:text-white cursor-pointer">
          <i className="fas fa-arrow-left text-lg"></i>
        </button>
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Search services..."
            value={searchTerm}
            onChange={e => ui.setSearchTerm(e.target.value)}
            className="w-full bg-white/10 text-white rounded-full px-4 py-2 pl-9 focus:outline-none focus:ring-2 focus:ring-white/20"
          />
          <i className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-white/60" />
        </div>
      </div>

      {/* Category filter */}
      <CategoryFilter
        categories={categories}
        selectedCategory={selectedCategory}
        onSelectCategory={setSelectedCategory}
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto px-4 pb-6">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-64 text-white/60 px-6">
            <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mb-4">
              <i className="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
            <p className="text-lg font-medium text-center text-red-500">{error}</p>
            <button
              onClick={() => actions.getServices()}
              className="mt-4 px-4 py-2 bg-white/10 rounded-md text-white hover:bg-white/20"
            >
              Try Again
            </button>
          </div>
        ) : filteredServices.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-white/60 px-6">
            <div className="w-16 h-16 rounded-full bg-white/10 flex items-center justify-center mb-4">
              <i className="fas fa-search text-white/40 text-xl"></i>
            </div>
            <p className="text-sm text-white/40 text-center mt-1">
              {searchTerm
                ? `No services matching "${searchTerm}"`
                : selectedCategory
                ? `No services in the "${selectedCategory}" category`
                : 'No services available'}
            </p>
          </div>
        ) : (
          filteredServices.map(service => (
            <ServiceCard
              key={service.id}
              service={service}
              onCallClick={actions.callService}
              onMessageClick={actions.messageService}
              onFavoriteToggle={actions.toggleFavorite}
              isFavorite={favorites.includes(service.id)}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default Services;
