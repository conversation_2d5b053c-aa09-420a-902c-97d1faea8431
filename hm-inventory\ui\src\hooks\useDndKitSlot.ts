import { useMemo, useCallback } from 'react';
import { useDraggable, useDroppable, useDndContext } from '@dnd-kit/core';
import { useInventoryStore, PanelType } from '../stores/inventoryStore';
import { DragPanelType, DragItem, DropItem } from '@shared';
import { InventoryItem, SlotType, ItemSlot } from '@shared/inventory.types';
import { ItemType } from '@shared/items.types';
import { useCraftingStore } from '../stores/craftingStore';
import { ToastType } from '../utils/toastUtils';
import * as SharedTypes from "@shared";
import { getItemDefinition } from '../../../scripts/shared/items/items';

// ==================== CLIENT REQUEST SENDER ====================

/**
 * Sends a message to the NUI callback using dynamic resource name.
 * @param event The event name.
 * @param data The event data.
 */
async function sendNuiMessage<T = unknown>(event: string, data: T): Promise<Response> {
  // Get the resource name dynamically to ensure correct URL
  const resourceName = 'hm-inventory';
  const url = `https://${resourceName}/${event}`;
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  };

  console.log(`[useDndKitSlot] Sending message to: ${url}`);
  return fetch(url, options);
}

// Specific request functions
export const sendMoveItemRequest = (data: SharedTypes.MoveItemRequest) => {
  return sendNuiMessage('MoveItemRequest', data);
};

export const sendDropItemRequest = (data: SharedTypes.DropItemRequest) => {
  return sendNuiMessage('DropItemRequest', data);
};

export const sendEquipItemRequest = (data: SharedTypes.EquipItemRequest) => {
  return sendNuiMessage('EquipItemRequest', data);
};

export const sendPickupItemRequest = (data: SharedTypes.PickupItemRequest) => {
  return sendNuiMessage('PickupItemRequest', data);
};

export const sendTransferItemRequest = (data: SharedTypes.TransferItemRequest) => {
  return sendNuiMessage('TransferItemRequest', data);
};

import { SplitStackRequest } from '@shared';

/**
 * Send a request to split a stack of items into two stacks
 * @param data The split stack request data
 * @returns Promise with the response from the server
 */
export const sendSplitStackRequest = (data: SplitStackRequest) => {
  console.log(`Sending split stack request: ${JSON.stringify(data)}`);
  return sendNuiMessage('SplitStackRequest', data);
};

// ==================== CENTRALIZED INVENTORY MOVE SYSTEM ====================

interface InventoryAdapter {
  getItem(index: number): ItemSlot | null;
  setItem(index: number, item: ItemSlot | null): void;
  canPlaceItem(index: number, item: ItemSlot): boolean;
  getMaxSlots(): number;
  getPanelType(): PanelType;
  getPanelId(): string | undefined;
}

class MainInventoryAdapter implements InventoryAdapter {
  constructor(private panelType: 'main' | 'quickAccess' = 'main') {}
    getItem(index: number): ItemSlot | null {
    const items = useInventoryStore.getState().gridItems;
    const item = items.find(slot => slot.index === index);
    return item?.item ? item : null;
  }
    setItem(index: number, item: ItemSlot | null): void {
    const store = useInventoryStore.getState();
    const items = [...store.gridItems];
    
    // Remove any existing item at this index
    const existingIndex = items.findIndex(slot => slot.index === index);
    if (existingIndex !== -1) {
      items.splice(existingIndex, 1);
    }
    
    // Add new item if provided
    if (item?.item) {
      items.push({ ...item, index });
    }
      store.updateGridItems(items);
  }
  
  canPlaceItem(index: number, item: ItemSlot): boolean {
    const currentItem = this.getItem(index);
    if (!currentItem?.item) return true;
    
    // Use the centralized stacking logic
    if (canStackItems(item, currentItem)) {
      const availableSpace = getAvailableStackSpace(currentItem);
      return availableSpace > 0;
    }
    
    // If not stackable, can still place (will trigger swap)
    return true;
  }
  
  getMaxSlots(): number {
    return useInventoryStore.getState().gridItems.length;
  }
  
  getPanelType(): PanelType { return this.panelType; }
  getPanelId(): string | undefined { return undefined; }
}

class ActionSlotsAdapter implements InventoryAdapter {
  getItem(index: number): ItemSlot | null {
    const items = useInventoryStore.getState().actionSlots;
    const item = items.find(slot => slot.index === index);
    return item?.item ? item : null;
  }
  
  setItem(index: number, item: ItemSlot | null): void {
    const store = useInventoryStore.getState();
    const items = [...store.actionSlots];
    
    // Remove any existing item at this index
    const existingIndex = items.findIndex(slot => slot.index === index);
    if (existingIndex !== -1) {
      items.splice(existingIndex, 1);
    }
    
    // Add new item if provided
    if (item?.item) {
      items.push({ ...item, index });
    }
      store.updateActionSlots(items);  }  canPlaceItem(index: number, item: ItemSlot): boolean {
    // Get the action slot to determine its slotType
    const actionSlots = useInventoryStore.getState().actionSlots;
    const actionSlot = actionSlots.find(slot => slot.index === index);
    const slotType = actionSlot?.slotType;
    
    // Check if we have a slot type defined for this action slot
    if (slotType && item?.item) {
      // Get the item definition to check compatibility
      const itemDefinition = getItemDefinition(item.item.definitionId);
      
      if (!itemDefinition) {
        console.log(`[ActionSlot] Item definition not found for ${item.item.definitionId}`);
        return false;
      }
      
      // First check if the item has explicit validSlotTypes defined
      if (itemDefinition.validSlotTypes && itemDefinition.validSlotTypes.length > 0) {
        const isValidForSlot = itemDefinition.validSlotTypes.includes(slotType);
        if (!isValidForSlot) {
          console.log(`[ActionSlot] Item ${item.item.label || item.item.name} cannot be placed in ${slotType} slot (explicit validSlotTypes)`);
          return false;
        }
      } else {
        // Fall back to item type-based validation
        const isCompatible = this.isItemTypeCompatibleWithSlot(itemDefinition.type, slotType);
        if (!isCompatible) {
          console.log(`[ActionSlot] Item ${item.item.label || item.item.name} (type: ${itemDefinition.type}) cannot be placed in ${slotType} slot (type-based validation)`);
          return false;
        }
      }
    }
    
    const currentItem = this.getItem(index);
    if (!currentItem?.item) return true;
    
    // Use the centralized stacking logic
    if (canStackItems(item, currentItem)) {
      const availableSpace = getAvailableStackSpace(currentItem);
      return availableSpace > 0;
    }
    
    // If not stackable, can still place (will trigger swap)
    return true;
  }  
  getMaxSlots(): number {
    return useInventoryStore.getState().actionSlots.length;
  }
  
  getPanelType(): PanelType { return 'action'; }
  getPanelId(): string | undefined { return undefined; }

  /**
   * Check if an item type is compatible with a specific slot type
   */
  private isItemTypeCompatibleWithSlot(itemType: ItemType, slotType: SlotType): boolean {
    switch (slotType) {
      case SlotType.PRIMARY_WEAPON:
        return itemType === ItemType.WEAPON;
      
      case SlotType.PHONE:
        return itemType === ItemType.GADGET; // Phones are gadgets
      
      case SlotType.TABLET:
        return itemType === ItemType.GADGET; // Tablets are gadgets
      
      case SlotType.ARMOR:
        return itemType === ItemType.GENERAL; // Armor items are typically general items
      
      case SlotType.BACKPACK:
        return itemType === ItemType.GENERAL; // Backpacks are general items
      
      default:
        // For unknown slot types, allow all items (fallback behavior)
        return true;
    }
  }
}

class CraftingInventoryAdapter implements InventoryAdapter {
  constructor(private panelId: string = 'crafting-station-1') {}
  
  getItem(index: number): ItemSlot | null {
    const items = useCraftingStore.getState().stationInventory;
    const item = items.find(slot => slot.index === index);
    return item?.item ? item : null;
  }
  
  setItem(index: number, item: ItemSlot | null): void {
    const store = useCraftingStore.getState();
    const items = [...store.stationInventory];
    
    // Remove any existing item at this index
    const existingIndex = items.findIndex(slot => slot.index === index);
    if (existingIndex !== -1) {
      items.splice(existingIndex, 1);
    }
    
    // Add new item if provided
    if (item?.item) {
      items.push({ ...item, index });
    }
      store.updateStationInventory(items);
  }
    canPlaceItem(index: number, item: ItemSlot): boolean {
    const currentItem = this.getItem(index);
    if (!currentItem?.item) return true;
    
    // Use the centralized stacking logic
    if (canStackItems(item, currentItem)) {
      const availableSpace = getAvailableStackSpace(currentItem);
      return availableSpace > 0;
    }
    
    // If not stackable, can still place (will trigger swap)
    return true;
  }
  
  getMaxSlots(): number { return 20; }
  getPanelType(): PanelType { return 'crafting'; }
  getPanelId(): string | undefined { return this.panelId; }
}

class SecondaryInventoryAdapter implements InventoryAdapter {
  constructor(private panelId: string) {}
  
  getItem(index: number): ItemSlot | null {
    const store = useInventoryStore.getState();
    const secondaryInv = store.secondaryInventories[this.panelId];
    
    if (!secondaryInv || index < 0 || index >= secondaryInv.items.length) {
      return null;
    }
    
    const item = secondaryInv.items[index];
    return item?.item ? item : null;
  }
  
  setItem(index: number, item: ItemSlot | null): void {
    const store = useInventoryStore.getState();
    const secondaryInv = store.secondaryInventories[this.panelId];
    
    if (!secondaryInv) return;
    
    const items = [...secondaryInv.items];
    if (index >= 0 && index < items.length) {
      items[index] = item || { index };
      store.updateSecondaryInventoryItems(this.panelId, items);    }
  }
    canPlaceItem(index: number, item: ItemSlot): boolean {
    const currentItem = this.getItem(index);
    if (!currentItem?.item) return true;
    
    // Use the centralized stacking logic
    if (canStackItems(item, currentItem)) {
      const availableSpace = getAvailableStackSpace(currentItem);
      return availableSpace > 0;
    }
    
    // If not stackable, can still place (will trigger swap)
    return true;
  }
  
  getMaxSlots(): number {
    const store = useInventoryStore.getState();
    const secondaryInv = store.secondaryInventories[this.panelId];
    return secondaryInv?.items.length || 0;
  }
  
  getPanelType(): PanelType { return 'secondary' as PanelType; }
  getPanelId(): string | undefined { return this.panelId; }
}

// Adapter factory
function getAdapter(panelType: PanelType, panelId?: string): InventoryAdapter {
  switch (panelType) {
    case 'main': return new MainInventoryAdapter('main');
    case 'quickAccess': return new MainInventoryAdapter('quickAccess');
    case 'action': return new ActionSlotsAdapter();
    case 'crafting': return new CraftingInventoryAdapter(panelId || 'crafting-station-1');
    default: 
      if (panelId) return new SecondaryInventoryAdapter(panelId);
      throw new Error(`Unknown panel type: ${panelType}`);
  }
}

// Centralized move service
function canStackItems(srcSlot: ItemSlot | undefined, dstSlot: ItemSlot | undefined): boolean {
  if (!srcSlot?.item || !dstSlot?.item) return false;
  
  // Check if items are the same type and both are stackable
  const isSameItem = srcSlot.item.definitionId === dstSlot.item.definitionId;
  const bothStackable = srcSlot.item.stackable && dstSlot.item.stackable;
  
  if (!isSameItem || !bothStackable) return false;
  
  // Get the effective max stack - use the destination item's maxStack as reference
  const maxStack = dstSlot.item.maxStack || 1;
  const currentStack = dstSlot.quantity || 0;
  
  // Can only stack if there's available space
  return currentStack < maxStack;
}

function getAvailableStackSpace(dstSlot: ItemSlot): number {
  if (!dstSlot?.item) return 0;
  
  const maxStack = dstSlot.item.maxStack || 1;
  const currentStack = dstSlot.quantity || 0;
  
  return Math.max(0, maxStack - currentStack);
}

// Helper function to show inventory operation toasts
function showInventoryToast(
  operation: 'move' | 'stack' | 'swap' | 'partial_stack' | 'failed_stack' | 'failed_move',
  item: InventoryItem,
  srcPanelType: PanelType,
  dstPanelType: PanelType,
  quantity?: number,
  availableSpace?: number
): void {
  const store = useInventoryStore.getState();
  
  switch (operation) {
    case 'move':
      store.showToast(ToastType.ITEM_MOVED, {
        itemName: item.label || item.name,
        itemIcon: item.icon,
        quantity: quantity,
        type: 'info'
      });
      break;
      
    case 'stack':
      if (srcPanelType === dstPanelType) {
        // Same panel stacking
        store.showToast(ToastType.ITEM_MOVED, {
          itemName: `Stacked ${quantity}x ${item.label || item.name}`,
          itemIcon: item.icon,
          type: 'success'
        });
      } else {
        // Cross-panel stacking
        store.showToast(ToastType.ITEM_MOVED, {
          itemName: `Stacked ${quantity}x ${item.label || item.name} to ${dstPanelType}`,
          itemIcon: item.icon,
          type: 'success'
        });
      }
      break;
      
    case 'partial_stack':
      store.showToast(ToastType.NOT_ENOUGH_SPACE, {
        itemName: `Only ${quantity} of ${item.label || item.name} could be stacked (${availableSpace} space available)`,
        itemIcon: item.icon,
        type: 'warning'
      });
      break;
      
    case 'swap':
      store.showToast(ToastType.ITEM_MOVED, {
        itemName: `Swapped ${item.label || item.name}`,
        itemIcon: item.icon,
        type: 'info'
      });
      break;
      
    case 'failed_stack':
      store.showToast(ToastType.NOT_ENOUGH_SPACE, {
        itemName: `Cannot stack ${item.label || item.name} - destination is full`,
        itemIcon: item.icon,
        type: 'warning'
      });
      break;
      
    case 'failed_move':
      store.showToast(ToastType.CUSTOM, {
        customMessage: `Failed to move ${item.label || item.name}`,
        itemIcon: item.icon,
        type: 'error'
      });
      break;
  }
}

function executeCentralizedMove(
  srcPanelType: PanelType,
  srcPanelId: string | undefined,
  srcIndex: number,
  dstPanelType: PanelType,
  dstPanelId: string | undefined,
  dstIndex: number,
  qty?: number,
  dstSlotType?: SlotType
): boolean {  try {
    const srcAdapter = getAdapter(srcPanelType, srcPanelId);
    const dstAdapter = getAdapter(dstPanelType, dstPanelId);
    
    const srcItem = srcAdapter.getItem(srcIndex);
    const dstItem = dstAdapter.getItem(dstIndex);
    
    if (!srcItem?.item) {
      console.warn('[CentralizedMove] No source item found');
      return false;
    }    // Check if destination adapter can accept this item
    if (!dstAdapter.canPlaceItem(dstIndex, srcItem)) {
      console.warn('[CentralizedMove] Destination cannot accept this item type');
      
      // Show toast notification about invalid item type for actual move attempts
      if (dstPanelType === 'action' && dstSlotType && srcItem?.item) {
        const store = useInventoryStore.getState();
        store.showToast(ToastType.CUSTOM, {
          customMessage: `${srcItem.item.label || srcItem.item.name} cannot be equipped in ${dstSlotType} slot`,
          itemIcon: srcItem.item.icon,
          type: 'warning'
        });
      }
      
      return false;
    }
    
    // Prevent dropping on the same slot
    if (srcAdapter === dstAdapter && srcIndex === dstIndex) {
      console.warn('[CentralizedMove] Cannot move to same slot');
      return false;
    }
    
    const moveQuantity = qty || srcItem.quantity || 1;
    const srcQuantity = srcItem.quantity || 1;
    
    if (!dstItem?.item) {
      // Move to empty slot
      if (moveQuantity >= srcQuantity) {
        // Move entire stack
        const newItem = { ...srcItem, index: dstIndex };
        dstAdapter.setItem(dstIndex, newItem);
        srcAdapter.setItem(srcIndex, { index: srcIndex });
        
        // Show toast for move operation
        showInventoryToast('move', srcItem.item, srcPanelType, dstPanelType, srcQuantity);
      } else {
        // Partial move
        const newDstItem = { index: dstIndex, item: srcItem.item, quantity: moveQuantity };
        const newSrcItem = { ...srcItem, quantity: srcQuantity - moveQuantity };
        
        dstAdapter.setItem(dstIndex, newDstItem);
        srcAdapter.setItem(srcIndex, newSrcItem);
        
        // Show toast for partial move
        showInventoryToast('move', srcItem.item, srcPanelType, dstPanelType, moveQuantity);
      }
    } else if (canStackItems(srcItem, dstItem)) {
      // Stack items - enhanced logic with proper maxStack handling
      const availableSpace = getAvailableStackSpace(dstItem);
      const actualMoveQuantity = Math.min(moveQuantity, availableSpace);
      
      if (actualMoveQuantity > 0) {
        const currentStack = dstItem.quantity || 0;
        const newDstItem = { ...dstItem, quantity: currentStack + actualMoveQuantity };
        dstAdapter.setItem(dstIndex, newDstItem);
        
        const remainingQuantity = srcQuantity - actualMoveQuantity;
        if (remainingQuantity > 0) {
          const newSrcItem = { ...srcItem, quantity: remainingQuantity };
          srcAdapter.setItem(srcIndex, newSrcItem);
        } else {
          srcAdapter.setItem(srcIndex, { index: srcIndex });
        }
        
        console.log(`[CentralizedMove] Stacked ${actualMoveQuantity} items. Available space was: ${availableSpace}, Remaining at source: ${remainingQuantity}`);
        
        // Show appropriate toast based on whether it was a partial stack or full stack
        if (actualMoveQuantity < moveQuantity) {
          showInventoryToast('partial_stack', srcItem.item, srcPanelType, dstPanelType, actualMoveQuantity, availableSpace);
        } else {
          showInventoryToast('stack', srcItem.item, srcPanelType, dstPanelType, actualMoveQuantity);
        }
      } else {
        console.log(`[CentralizedMove] Cannot stack - destination is full (maxStack: ${dstItem.item.maxStack || 1}, current: ${dstItem.quantity || 0})`);
        
        // Show failed stack toast
        showInventoryToast('failed_stack', srcItem.item, srcPanelType, dstPanelType);
        return false;
      }    } else {
      // Swap items - first check if both items can be placed in their new locations
      if (!srcAdapter.canPlaceItem(srcIndex, dstItem)) {
        console.warn('[CentralizedMove] Cannot swap - source cannot accept destination item type');
        const store = useInventoryStore.getState();
        store.showToast(ToastType.CUSTOM, {
          customMessage: `Cannot swap items - incompatible slot types`,
          type: 'warning'
        });
        return false;
      }
      
      const newSrcItem = { ...dstItem, index: srcIndex };
      const newDstItem = { ...srcItem, index: dstIndex };
      
      srcAdapter.setItem(srcIndex, newSrcItem);
      dstAdapter.setItem(dstIndex, newDstItem);
      
      // Show toast for swap operation
      showInventoryToast('swap', srcItem.item, srcPanelType, dstPanelType);
    }
    
    return true;
  } catch (error) {
    console.error('Centralized move failed:', error);
    
    // Show failed move toast if we have the source item
    if (srcPanelType && srcIndex >= 0) {
      try {
        const srcAdapter = getAdapter(srcPanelType, srcPanelId);
        const srcItem = srcAdapter.getItem(srcIndex);
        if (srcItem?.item) {
          showInventoryToast('failed_move', srcItem.item, srcPanelType, dstPanelType);
        }
      } catch (toastError) {
        console.error('Failed to show error toast:', toastError);
      }
    }
    
    return false;
  }
}

// ==================== END CENTRALIZED SYSTEM ====================

// Use the centralized move system
export const executeOptimizedMoveItem = (
  srcPanelType: PanelType,
  srcPanelId: string | undefined,
  srcIndex: number,
  dstPanelType: PanelType,
  dstPanelId: string | undefined,
  dstIndex: number,
  qty?: number,
  dstSlotType?: SlotType
): boolean => {
  console.log(`[CentralizedMove] ${srcPanelType}(${srcPanelId},${srcIndex}) → ${dstPanelType}(${dstPanelId},${dstIndex}) qty:${qty} slotType:${dstSlotType}`);
  
  // Basic validation
  if (srcIndex < 0 || dstIndex < 0) {
    console.warn('Invalid slot indices provided');
    const store = useInventoryStore.getState();
    store.showToast(ToastType.CUSTOM, {
      customMessage: 'Invalid slot indices provided',
      type: 'error'
    });
    return false;
  }
  
  // For main panel to main panel moves, send directly to server without optimistic update
  // The server will handle the move and send back the updated inventory
  if (srcPanelType === 'main' && dstPanelType === 'main') {
    // Send to backend for processing
    const request = { 
      action: 'moveItem' as const,
      sourceIndex: srcIndex,
      destinationIndex: dstIndex, 
      quantity: qty || 1,
      sourcePanelType: srcPanelType,
      sourcePanelId: srcPanelId,
      destinationPanelType: dstPanelType,
      destinationPanelId: dstPanelId
    };

    sendMoveItemRequest(request).catch((error) => {
      console.error('Failed to send move request to backend:', error);
    });
    
    return true; // Assume success, server will handle the actual move
  }
  
  // For other panel types, use the centralized move system
  const success = executeCentralizedMove(
    srcPanelType, srcPanelId, srcIndex,
    dstPanelType, dstPanelId, dstIndex,
    qty, dstSlotType
  );
  
  if (success) {
    // Send to backend for persistence
    const request = { 
      action: 'moveItem' as const,
      sourceIndex: srcIndex,
      destinationIndex: dstIndex, 
      quantity: qty || 1,
      sourcePanelType: srcPanelType,
      sourcePanelId: srcPanelId,
      destinationPanelType: dstPanelType,
      destinationPanelId: dstPanelId
    };

    sendMoveItemRequest(request).catch((error) => {
      console.error('Failed to send move request to backend:', error);
    });
  }
  
  return success;
};

interface UseDndKitSlotProps {
  panelType: DragPanelType;
  panelId?: string;
  slotIndex: number;
  item?: InventoryItem;
  quantity?: number;
  slotType?: SlotType;
}

// Execute drop operation - integrates with optimized moveItem logic
export const executeDropOperation = (
  dragData: DragItem,
  dropData: DropItem
): boolean => {
  const source = dragData.source;
  const destination = dropData.target;

  return executeOptimizedMoveItem(
    source.panelType as PanelType,
    source.panelId,
    source.slotIndex,
    destination.panelType as PanelType,
    destination.panelId,
    destination.slotIndex,
    source.quantity,
    destination.slotType as SlotType
  );
};

export const useDndKitSlot = ({
  panelType,
  panelId,
  slotIndex,
  item,
  quantity,
  slotType,
}: UseDndKitSlotProps) => {
  // Memoize unique IDs for drag and drop to prevent unnecessary re-renders
  const dragId = useMemo(
    () => `drag-${panelType}-${panelId || 'default'}-${slotIndex}`,
    [panelType, panelId, slotIndex]
  );
  const dropId = useMemo(
    () => `drop-${panelType}-${panelId || 'default'}-${slotIndex}`,
    [panelType, panelId, slotIndex]
  );
  // Memoize drag data to prevent unnecessary re-creation
  const dragData = useMemo(() => ({
    item,
    source: {
      panelType,
      panelId,
      slotIndex,
      quantity,
    },
  } as DragItem), [item, panelType, panelId, slotIndex, quantity]);

  // Memoize drop data to prevent unnecessary re-creation
  const dropData = useMemo(() => ({
    target: {
      panelType,
      panelId,
      slotIndex,
      slotType,
    },
  } as DropItem), [panelType, panelId, slotIndex, slotType]);

  // Draggable configuration
  const {
    attributes: dragAttributes,
    listeners: dragListeners,
    setNodeRef: setDragRef,
    isDragging,
  } = useDraggable({
    id: dragId,
    data: dragData,
    disabled: !item || item.name === 'empty' || item.name === '',
  });
  // Droppable configuration
  const {
    setNodeRef: setDropRef,
    isOver,
  } = useDroppable({
    id: dropId,
    data: dropData,
  });  // Get the current drag context to access the dragged item
  const { active } = useDndContext();
  const dragState = useInventoryStore((state) => state.isDragging);

// Custom validation for drop - check if current dragged item can be dropped here
  // Optimized dependency array to prevent unnecessary recalculations
  const canDropHere = useMemo(() => {
    // If nothing is being dragged, allow drop
    if (!active || !dragState) return true;
    
    // Get the dragged item data
    const draggedData = active.data.current as DragItem;
    if (!draggedData?.item) return true;
    
    // Don't allow dropping on the same slot
    if (draggedData.source.panelType === panelType && 
        draggedData.source.panelId === panelId && 
        draggedData.source.slotIndex === slotIndex) {
      return false;
    }
    
    // Create a temporary ItemSlot from the dragged item
    const draggedItemSlot: ItemSlot = {
      index: draggedData.source.slotIndex,
      item: draggedData.item,
      quantity: draggedData.source.quantity || 1,
      slotType: slotType
    };
    
    // Use the adapter validation logic
    try {
      const adapter = getAdapter(panelType as PanelType, panelId);
      const canPlace = adapter.canPlaceItem(slotIndex, draggedItemSlot);
      
      // Debug logging for visual feedback
      if (!canPlace && panelType === 'action') {
        console.log(`[Visual Feedback] Item ${draggedData.item.label} cannot be placed in ${slotType} slot`);
      }
      
      return canPlace;
    } catch (error) {
      console.warn('[useDndKitSlot] Error during validation:', error);
      return false;
    }
  }, [active, dragState, panelType, panelId, slotIndex, slotType]);// Memoize the combine refs function to prevent unnecessary re-renders
  const combineRefs = useCallback((node: HTMLElement | null) => {
    setDragRef(node);
    setDropRef(node);
  }, [setDragRef, setDropRef]);

  // Memoize data attributes to prevent object recreation on each render
  const dataAttributes = useMemo(() => ({
    'data-drag-id': dragId,
    'data-drop-id': dropId,
    'data-panel-type': panelType,
    'data-slot-index': slotIndex.toString(),
  }), [dragId, dropId, panelType, slotIndex]);  return {
    // State
    isDragging,
    isOver,
    canDrop: canDropHere,
    dragState,

    // Refs and handlers
    ref: combineRefs,
    dragRef: setDragRef,
    dropRef: setDropRef,
    
    // Drag attributes and listeners for @dnd-kit
    dragAttributes,
    dragListeners,    // Data attributes for debugging (optional)
    dataAttributes,

    // Execute drop operation with optimized logic
    executeDropOperation: () => executeDropOperation,
    
    // Direct access to optimized move function
    executeOptimizedMove: executeOptimizedMoveItem,
  }
  ;
};
