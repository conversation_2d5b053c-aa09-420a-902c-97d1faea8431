import React, { useState } from 'react';
import { useBankingStore } from '../store/bankingStore';

type ActionType = 'deposit' | 'withdraw' | 'transfer' | null;

const ActionPanel: React.FC = () => {
  const selectedAccountId = useBankingStore((state) => state.selectedAccountId);
  const [activeAction, setActiveAction] = useState<ActionType>(null);

  // Form state from store
  const depositAmount = useBankingStore((state) => state.depositAmount);
  const withdrawAmount = useBankingStore((state) => state.withdrawAmount);
  const transferAmount = useBankingStore((state) => state.transferAmount);
  const transferTargetAccountNumber = useBankingStore((state) => state.transferTargetAccountNumber);
  const transferDescription = useBankingStore((state) => state.transferDescription);

  // Form setters
  const setDepositAmount = useBankingStore((state) => state.setDepositAmount);
  const setWithdrawAmount = useBankingStore((state) => state.setWithdrawAmount);
  const setTransferAmount = useBankingStore((state) => state.setTransferAmount);
  const setTransferTargetAccountNumber = useBankingStore((state) => state.setTransferTargetAccountNumber);
  const setTransferDescription = useBankingStore((state) => state.setTransferDescription);

  // Actions
  const submitDeposit = useBankingStore((state) => state.submitDeposit);
  const submitWithdrawal = useBankingStore((state) => state.submitWithdrawal);
  const submitTransfer = useBankingStore((state) => state.submitTransfer);
  const resetForms = useBankingStore((state) => state.resetForms);

  const isLoading = useBankingStore((state) => state.isLoading);

  const formatCurrency = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(num);
  };

  const handleActionSelect = (action: ActionType) => {
    if (activeAction === action) {
      setActiveAction(null);
      resetForms();
    } else {
      setActiveAction(action);
      resetForms();
    }
  };

  const handleSubmit = async (action: ActionType) => {
    if (!selectedAccountId) return;

    try {
      switch (action) {
        case 'deposit':
          await submitDeposit();
          break;
        case 'withdraw':
          await submitWithdrawal();
          break;
        case 'transfer':
          await submitTransfer();
          break;
      }
      setActiveAction(null);
      resetForms();
    } catch (error) {
      console.error('Transaction failed:', error);
    }
  };

  const isValidAmount = (amount: string) => {
    const num = parseFloat(amount);
    return !isNaN(num) && num > 0;
  };

  if (!selectedAccountId) {
    return (
      <div className="p-4">
        <div className="text-center py-8">
          <div className="w-12 h-12 bg-neutral-700/50 rounded-full flex items-center justify-center mx-auto mb-3 border border-neutral-600/30">
            <i className="fas fa-hand-point-up text-neutral-400" />
          </div>
          <p className="text-neutral-400 text-sm">Select an account to perform actions</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-3">
      <h3 className="text-neutral-200 font-medium text-sm mb-4">Quick Actions</h3>

      {/* Action Buttons */}
      <div className="space-y-2">
        <button
          onClick={() => handleActionSelect('deposit')}
          className={`w-full p-3 rounded-lg border transition-all text-left ${
            activeAction === 'deposit'
              ? 'bg-green-500/10 border-green-500/30 border-l-4 border-l-green-400'
              : 'bg-neutral-700/30 border-neutral-600/30 hover:bg-neutral-700/50'
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center border border-green-500/30">
              <i className="fas fa-arrow-down text-green-400 text-sm" />
            </div>
            <div>
              <div className="text-neutral-200 font-medium text-sm">Deposit</div>
              <div className="text-neutral-400 text-xs">Add money to account</div>
            </div>
          </div>
        </button>

        <button
          onClick={() => handleActionSelect('withdraw')}
          className={`w-full p-3 rounded-lg border transition-all text-left ${
            activeAction === 'withdraw'
              ? 'bg-red-500/10 border-red-500/30 border-l-4 border-l-red-400'
              : 'bg-neutral-700/30 border-neutral-600/30 hover:bg-neutral-700/50'
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center border border-red-500/30">
              <i className="fas fa-arrow-up text-red-400 text-sm" />
            </div>
            <div>
              <div className="text-neutral-200 font-medium text-sm">Withdraw</div>
              <div className="text-neutral-400 text-xs">Take money from account</div>
            </div>
          </div>
        </button>

        <button
          onClick={() => handleActionSelect('transfer')}
          className={`w-full p-3 rounded-lg border transition-all text-left ${
            activeAction === 'transfer'
              ? 'bg-blue-500/10 border-blue-500/30 border-l-4 border-l-blue-400'
              : 'bg-neutral-700/30 border-neutral-600/30 hover:bg-neutral-700/50'
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center border border-blue-500/30">
              <i className="fas fa-exchange-alt text-blue-400 text-sm" />
            </div>
            <div>
              <div className="text-neutral-200 font-medium text-sm">Transfer</div>
              <div className="text-neutral-400 text-xs">Send money to another account</div>
            </div>
          </div>
        </button>
      </div>

      {/* Action Forms */}
      {activeAction === 'deposit' && (
        <div className="mt-4 p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
          <h4 className="text-green-400 font-medium text-sm mb-3">Deposit Funds</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-neutral-300 text-xs mb-2">Amount</label>
              <input
                type="number"
                min="0.01"
                step="0.01"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                placeholder="0.00"
                className="w-full bg-neutral-700 border border-neutral-600 rounded-lg px-3 py-2 text-neutral-200 text-sm focus:outline-none focus:border-green-500/50 focus:ring-1 focus:ring-green-500/30"
              />
              {isValidAmount(depositAmount) && (
                <p className="text-green-400 text-xs mt-1">
                  {formatCurrency(depositAmount)}
                </p>
              )}
            </div>
            <button
              onClick={() => handleSubmit('deposit')}
              disabled={!isValidAmount(depositAmount) || isLoading}
              className="w-full bg-green-500 hover:bg-green-600 disabled:bg-green-500/50 disabled:cursor-not-allowed text-white py-2 px-4 rounded-lg font-medium text-sm transition-colors"
            >
              {isLoading ? 'Processing...' : 'Deposit Funds'}
            </button>
          </div>
        </div>
      )}

      {activeAction === 'withdraw' && (
        <div className="mt-4 p-4 bg-red-500/5 border border-red-500/20 rounded-lg">
          <h4 className="text-red-400 font-medium text-sm mb-3">Withdraw Funds</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-neutral-300 text-xs mb-2">Amount</label>
              <input
                type="number"
                min="0.01"
                step="0.01"
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                placeholder="0.00"
                className="w-full bg-neutral-700 border border-neutral-600 rounded-lg px-3 py-2 text-neutral-200 text-sm focus:outline-none focus:border-red-500/50 focus:ring-1 focus:ring-red-500/30"
              />
              {isValidAmount(withdrawAmount) && (
                <p className="text-red-400 text-xs mt-1">
                  {formatCurrency(withdrawAmount)}
                </p>
              )}
            </div>
            <button
              onClick={() => handleSubmit('withdraw')}
              disabled={!isValidAmount(withdrawAmount) || isLoading}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 disabled:cursor-not-allowed text-white py-2 px-4 rounded-lg font-medium text-sm transition-colors"
            >
              {isLoading ? 'Processing...' : 'Withdraw Funds'}
            </button>
          </div>
        </div>
      )}

      {activeAction === 'transfer' && (
        <div className="mt-4 p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
          <h4 className="text-blue-400 font-medium text-sm mb-3">Transfer Funds</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-neutral-300 text-xs mb-2">Target Account Number</label>
              <input
                type="text"
                value={transferTargetAccountNumber}
                onChange={(e) => setTransferTargetAccountNumber(e.target.value)}
                placeholder="1234-5678"
                className="w-full bg-neutral-700 border border-neutral-600 rounded-lg px-3 py-2 text-neutral-200 text-sm focus:outline-none focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/30"
              />
            </div>
            <div>
              <label className="block text-neutral-300 text-xs mb-2">Amount</label>
              <input
                type="number"
                min="0.01"
                step="0.01"
                value={transferAmount}
                onChange={(e) => setTransferAmount(e.target.value)}
                placeholder="0.00"
                className="w-full bg-neutral-700 border border-neutral-600 rounded-lg px-3 py-2 text-neutral-200 text-sm focus:outline-none focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/30"
              />
              {isValidAmount(transferAmount) && (
                <p className="text-blue-400 text-xs mt-1">
                  {formatCurrency(transferAmount)}
                </p>
              )}
            </div>
            <div>
              <label className="block text-neutral-300 text-xs mb-2">Description (Optional)</label>
              <input
                type="text"
                value={transferDescription}
                onChange={(e) => setTransferDescription(e.target.value)}
                placeholder="Payment description..."
                className="w-full bg-neutral-700 border border-neutral-600 rounded-lg px-3 py-2 text-neutral-200 text-sm focus:outline-none focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/30"
              />
            </div>
            <button
              onClick={() => handleSubmit('transfer')}
              disabled={!isValidAmount(transferAmount) || !transferTargetAccountNumber.trim() || isLoading}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 disabled:cursor-not-allowed text-white py-2 px-4 rounded-lg font-medium text-sm transition-colors"
            >
              {isLoading ? 'Processing...' : 'Transfer Funds'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionPanel;
