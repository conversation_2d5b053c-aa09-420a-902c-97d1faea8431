/**
 * Environment utility functions
 */

/**
 * Check if the application is running in a browser environment
 * @returns True if running in a browser, false if running in FiveM NUI
 */
export const isBrowser = (): boolean => {
  return typeof window !== 'undefined' && !(window as any).invokeNative;
};

/**
 * Check if the application is running in development mode
 * @returns True if running in development mode
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Check if the application is running in production mode
 * @returns True if running in production mode
 */
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

/**
 * Get the resource name
 * @returns Resource name
 */
export const getResourceName = (): string => {
  return (window as any).GetParentResourceName ? (window as any).GetParentResourceName() : 'unknown';
};