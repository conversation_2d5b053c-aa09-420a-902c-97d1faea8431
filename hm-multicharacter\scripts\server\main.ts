/// <reference types="@citizenfx/server" />
/**
 * Server-side entry point for hm-multicharacter
 */
import { MulticharacterEvents } from '../shared/events';
import { CharacterAppearance, CharacterCreationData, CharacterSelectionData } from '../shared/character.types';
import { MULTICHARACTER_PREVIEW_CONFIG } from '../shared/config';

// Maximum number of characters per player (can be overridden in config)
const MAX_CHARACTERS = MULTICHARACTER_PREVIEW_CONFIG.maxCharacters || 5;

// Utility function to generate a unique state ID
function generateStateId(): string {
    // Generate a random alphanumeric string of length 8
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Utility function to get player identifier
function getPlayerIdentifier(source: number): string {
    // Get the player's license identifier
    const identifiers = getPlayerIdentifiers(source);
    const license = identifiers.find(id => id.startsWith('license:'))?.replace('license:', '');
    

    if (!license) {
        throw new Error('Player license identifier not found');
    }

    return license;
}

// Resource start event
on('onResourceStart', (resourceName: string) => {
    if (resourceName !== GetCurrentResourceName()) return;

    console.log('[hm-multicharacter] Resource started');
});

// Get characters for a player
async function getPlayerCharacters(license: string): Promise<CharacterSelectionData[]> {
    try {
        const result = await global.exports.oxmysql.query_async(
            `SELECT id, stateid, first_name, last_name, gender, birthdate, state, money, position, appearance, metadata, created_at, updated_at
             FROM characters
             WHERE license = ?
             ORDER BY updated_at DESC`,
            [license]
        );

        // Transform database results to match the CharacterSelectionData interface
        return (result as any[]).map(char => ({
            id: char.id,
            stateid: char.stateid,
            license: license,
            first_name: char.first_name,
            last_name: char.last_name,
            birthdate: char.birthdate,
            gender: char.gender,
            state: char.state || 'alive',
            money: char.money ? JSON.parse(char.money) : {
                cash: 500,
                bank: 5000,
                crypto: 0
            },
            position: char.position ? JSON.parse(char.position) : { x: 0, y: 0, z: 100.0, heading: 0 },
            appearance: char.appearance ? JSON.parse(char.appearance) : {} as CharacterAppearance,
            metadata: char.metadata ? JSON.parse(char.metadata) : {},
            created_at: new Date(char.created_at),
            updated_at: new Date(char.updated_at)
        }));
    } catch (error) {
        console.error('[hm-multicharacter] Error getting player characters:', error);
        return [];
    }
}

// Create a new character
async function createCharacter(
    license: string,
    characterData: CharacterCreationData
): Promise<CharacterSelectionData | null> {
    try {
        // Check if player has reached the maximum number of characters
        const existingCharacters = await getPlayerCharacters(license);
        if (existingCharacters.length >= MAX_CHARACTERS) {
            throw new Error(`Maximum number of characters (${MAX_CHARACTERS}) reached`);
        }

        // Generate a unique state ID
        const stateid = generateStateId();

        // Insert the new character into the database
        const result = await global.exports.oxmysql.insert_async(
            `INSERT INTO characters
             (license, stateid, first_name, last_name, birthdate, gender, state, money, position, appearance, tattoos, metadata)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
             ,
            [
                license,
                stateid,
                characterData.first_name,
                characterData.last_name,
                characterData.birthdate,
                characterData.gender,
                characterData.state,
                JSON.stringify(characterData.money),
                JSON.stringify(characterData.position),
                JSON.stringify({}),
                JSON.stringify({}),
                JSON.stringify({
                    job: 'unemployed'
                }),
            ]
        );

        if (!result) {
            throw new Error('Failed to insert new character');
        }

        // Return the newly created character data
        return {
            id: result,
            stateid: stateid,
            license: license,
            first_name: characterData.first_name,
            last_name: characterData.last_name,
            birthdate: characterData.birthdate,
            state: 'alive', // Default state
            gender: characterData.gender,
            money: {
                cash: characterData.money.cash || 500, // Default cash if not provided
                bank: characterData.money.bank || 5000, // Default bank balance if not provided
                crypto: characterData.money.crypto || 0 // Default crypto if not provided
            },
            position: { x: 0, y: 0, z: 100.0, heading: 0 }, // Default position, can be modified later
            metadata: {
                job: 'unemployed'
            },
            appearance: {
                // Default appearance, can be modified later
                model: 'mp_m_freemode_01', // Default model, can be modified later
                components: [],
                props: [],
                headBlend: {
                    shapeFirst: 0,
                    shapeSecond: 0,
                    skinFirst: 0,
                    skinSecond: 0,
                    shapeMix: 0.5,
                    skinMix: 0.5,
                    thirdMix: 0.5,
                    shapeThird: 0,
                    skinThird: 0,
                },
                faceFeatures: {
                    noseWidth: 0,
                    nosePeakHeight: 0,
                    nosePeakLength: 0,
                    noseBoneHeight: 0,
                    nosePeakLowering: 0,
                    noseBoneTwist: 0,
                    eyeBrowHeight: 0,
                    eyeBrowForward: 0,
                    cheekBoneHeight: 0,
                    cheekBoneWidth: 0,
                    cheeksWidth: 0,
                    eyesOpening: 0,
                    lipsThickness: 0,
                    jawBoneWidth: 0,
                    jawBoneBackLength: 0,
                    chinBoneLowering: 0,
                    chinBoneLength: 0,
                    chinBoneWidth: 0,
                    chinDimple: 0,
                    neckThickness: 0
                },
                headOverlays: {
                    blemishes: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    beard: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    eyebrows: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    ageing: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    makeUp: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    blush: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    complexion: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    sunDamage: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    lipstick: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    moleAndFreckles: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    chestHair: { index: 0, opacity: 0, color: 0, secondColor: 0 },
                    bodyBlemishes: { index: 0, opacity: 0, color: 0, secondColor: 0 }
                },
                hair: {
                    style: 0, // Default hair style, can be modified later
                    color: 0, // Default hair color, can be modified later
                    highlight: 0 // Default highlight color, can be modified later
                },
                eyeColor: 0, // Default eye color, can be modified later
                tattoos: [
                    { collection: '', overlay: '' } // Default tattoos, can be modified later
                ], // Default tattoos, can be modified later
            }, // Default appearance, can be modified later
        };
    } catch (error) {
        console.error('[hm-multicharacter] Error creating character:', error);
        return null;
    }
}

// Select a character
async function selectCharacter(source: number, stateid: string): Promise<boolean> {
    try {
        const license = getPlayerIdentifier(source);

        // Get the character data
        const result = await global.exports.oxmysql.query_async(
            `SELECT * FROM characters WHERE stateid = ? AND license = ?`,
            [stateid, license]
        );

        if (!result || (result as any[]).length === 0) {
            throw new Error('Character not found or does not belong to this player');
        }

        const character = (result as any[])[0];

        // Update the last played timestamp
        await global.exports.oxmysql.update_async(
            `UPDATE characters SET updated_at = CURRENT_TIMESTAMP WHERE stateid = ?`,
            [stateid]
        );

        // Set player state with character data
        // This would typically be handled by your core resource
        // For now, we'll just emit an event that the core resource can listen for
        emitNet('hm-core:server:characterSelected', source, {
            stateid: character.stateid,
            firstName: character.first_name,
            lastName: character.last_name,
            birthdate: character.birthdate,
            position: JSON.parse(character.position || '{"x":0,"y":0,"z":100.0,"heading":0}'),
            appearance: JSON.parse(character.appearance || '{}'),
            id: character.id,
            license: character.license,
            state: 'alive', // Default state, can be modified later

            gender: character.gender,
            money: {
                cash: JSON.parse(character.money).cash,
                bank: JSON.parse(character.money).bank,
                crypto: JSON.parse(character.money).crypto
            },

            metadata: JSON.parse(character.metadata || '{}')
        });

        return true;
    } catch (error) {
        console.error('[hm-multicharacter] Error selecting character:', error);
        return false;
    }
}

// Delete a character
async function deleteCharacter(source: number, stateid: string): Promise<boolean> {
    try {
        const license = getPlayerIdentifier(source);

        // Check if the character belongs to this player
        const result = await global.exports.oxmysql.query_async(
            `SELECT id FROM characters WHERE stateid = ? AND license = ?`,
            [stateid, license]
        );

        if (!result || (result as any[]).length === 0) {
            throw new Error('Character not found or does not belong to this player');
        }

        // Delete the character
        await global.exports.oxmysql.update_async(
            `DELETE FROM characters WHERE stateid = ?`,
            [stateid]
        );

        return true;
    } catch (error) {
        console.error('[hm-multicharacter] Error deleting character:', error);
        return false;
    }
}

// Event handlers
onNet(MulticharacterEvents.REQUEST_CHARACTERS, async () => {
    const src = global.source;
    try {
        const license = getPlayerIdentifier(src);
        const characters = await getPlayerCharacters(license);
        emitNet(MulticharacterEvents.RECEIVE_CHARACTERS, src, { characters });
    } catch (error) {
        console.error('[hm-multicharacter] Error handling REQUEST_CHARACTERS:', error);
        emitNet(MulticharacterEvents.NUI_ERROR, src, {
            message: 'Failed to retrieve characters'
        });
    }
});

onNet(MulticharacterEvents.CREATE_CHARACTER, async (payload: { characterData: CharacterCreationData }) => {
    const src = global.source;
    try {
        const license = getPlayerIdentifier(src);
        const newCharacter = await createCharacter(license, payload.characterData);

        if (newCharacter) {
            // Send success response to client
            emitNet(MulticharacterEvents.CHARACTER_CREATED, src, {
                success: true,
                message: 'Character created successfully',
                character: newCharacter
            });
        } else {
            // Send failure response to client
            emitNet(MulticharacterEvents.CREATE_CHARACTER_FAILED, src, {
                message: 'Failed to create character'
            });
        }
    } catch (error) {
        console.error('[hm-multicharacter] Error handling CREATE_CHARACTER:', error);
        emitNet(MulticharacterEvents.CREATE_CHARACTER_FAILED, src, {
            message: error instanceof Error ? error.message : 'Failed to create character'
        });
    }
});

onNet(MulticharacterEvents.SELECT_CHARACTER, async (payload: { stateid: string }) => {
    const src = global.source;
    try {
        const success = await selectCharacter(src, payload.stateid);
        if (success) {
            // Send success response to client
            emitNet(MulticharacterEvents.CHARACTER_SELECTED, src, {
                success: true,
                message: 'Character selected successfully',
                stateid: payload.stateid
            });
        } else {
            // Send failure response to client
            emitNet(MulticharacterEvents.SELECT_CHARACTER_FAILED, src, {
                message: 'Failed to select character'
            });
        }
    } catch (error) {
        console.error('[hm-multicharacter] Error handling SELECT_CHARACTER:', error);
        emitNet(MulticharacterEvents.SELECT_CHARACTER_FAILED, src, {
            message: error instanceof Error ? error.message : 'Failed to select character'
        });
    }
});

onNet(MulticharacterEvents.DELETE_CHARACTER, async (payload: { stateid: string }) => {
    const src = global.source;
    try {
        const success = await deleteCharacter(src, payload.stateid);

        if (success) {
            // Send success response to client
            emitNet(MulticharacterEvents.CHARACTER_DELETED, src, {
                success: true,
                message: 'Character deleted successfully',
                stateid: payload.stateid
            });
        } else {
            // Send failure response to client
            emitNet(MulticharacterEvents.DELETE_CHARACTER_FAILED, src, {
                message: 'Failed to delete character'
            });
        }
    } catch (error) {
        console.error('[hm-multicharacter] Error handling DELETE_CHARACTER:', error);
        emitNet(MulticharacterEvents.DELETE_CHARACTER_FAILED, src, {
            message: error instanceof Error ? error.message : 'Failed to delete character'
        });
    }
});

// Player connecting event - trigger character selection
on('playerConnecting', (name: string) => {
    // This is just to ensure the player gets the character selection screen
    // The actual character selection will be handled by the client
    console.log(`[hm-multicharacter] Player ${name} connecting`);
});

// Handle routing bucket for character selection
onNet('hm-multicharacter:server:setRoutingBucket', (bucketId: number) => {
    const src = global.source;
    // Convert source to string for SetPlayerRoutingBucket
    SetPlayerRoutingBucket(src.toString(), bucketId);
    console.log(`[hm-multicharacter] Set player ${src} to routing bucket ${bucketId}`);
});

// Reset routing bucket after character selection
onNet('hm-multicharacter:server:resetRoutingBucket', () => {
    const src = global.source;
    // Convert source to string for SetPlayerRoutingBucket
    SetPlayerRoutingBucket(src.toString(), 0); // Reset to default bucket
    console.log(`[hm-multicharacter] Reset player ${src} to default routing bucket`);
});

console.log('[hm-multicharacter] Server initialized');
