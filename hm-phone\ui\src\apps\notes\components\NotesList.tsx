import React from 'react';
import { useNotesStore } from '../stores/notesStore';

interface NotesListProps {
  searchQuery: string;
  onNoteSelect: (id: number) => void;
}

const NotesList: React.FC<NotesListProps> = ({ searchQuery, onNoteSelect }) => {
  const { notes, togglePin } = useNotesStore();

  // Use notes from the store
  const displayNotes = notes;

  const filteredNotes = displayNotes.filter(
    note =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedNotes = [...filteredNotes].sort((a, b) => {
    // Sort by pinned status first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    // Then by timestamp
    return b.timestamp - a.timestamp;
  });

  return (
    <div className="flex-1 overflow-y-auto px-4">
      {sortedNotes.map(note => (
        <div
          key={note.id}
          className="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-3 cursor-pointer shadow-md hover:bg-white/30 transition-colors"
          onClick={() => onNoteSelect(note.id)}
        >
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-white font-medium truncate">{note.title}</h3>
            <button
              onClick={e => {
                e.stopPropagation();
                togglePin(note.id);
              }}
              className="text-white/60 hover:text-white"
            >
              <i className={`fas fa-thumbtack ${note.isPinned ? 'text-blue-500' : ''}`} />
            </button>
          </div>
          <p className="text-white/60 text-sm line-clamp-2">{note.content}</p>
          <div className="text-white/40 text-xs mt-2">
            {new Date(note.timestamp).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotesList;
