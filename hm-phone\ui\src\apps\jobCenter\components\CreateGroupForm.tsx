import React, { useState } from 'react';
import { Job } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';

interface CreateGroupFormProps {
  job: Job;
  onSubmit: (name: string, isPrivate: boolean, password?: string) => void;
  onCancel: () => void;
}

const CreateGroupForm: React.FC<CreateGroupFormProps> = ({ job, onSubmit, onCancel }) => {
  const [name, setName] = useState(`${job.title} Group`);
  const [isPrivate, setIsPrivate] = useState(false);
  const [password, setPassword] = useState('');

  const [errors, setErrors] = useState<{ name?: string; password?: string }>({});

  const validateForm = (): boolean => {
    const newErrors: { name?: string; password?: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Group name is required';
    }

    if (isPrivate && !password.trim()) {
      newErrors.password = 'Password is required for private groups';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(name, isPrivate, isPrivate ? password : undefined);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex items-center">
        <button
          onClick={onCancel}
          className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <h2 className="text-white font-medium ml-2">Create Group for {job.title}</h2>
      </div>

      {/* Form */}
      <div className="flex-1 overflow-y-auto p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-white/70 text-sm mb-1">Group Name</label>
            <input
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter a name for your group"
              className={`w-full p-3 rounded-lg bg-white/10 text-white border ${
                errors.name ? 'border-red-500' : 'border-white/20'
              } focus:outline-none focus:border-teal-500`}
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          <div className="flex items-center">
            <label className="flex items-center text-white/70 text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={isPrivate}
                onChange={e => setIsPrivate(e.target.checked)}
                className="mr-2 h-4 w-4 rounded border-white/20 text-teal-500 focus:ring-teal-500"
              />
              Private Group (Password Protected)
            </label>
          </div>

          {isPrivate && (
            <div>
              <label className="block text-white/70 text-sm mb-1">Password</label>
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                placeholder="Enter a password for your group"
                className={`w-full p-3 rounded-lg bg-white/10 text-white border ${
                  errors.password ? 'border-red-500' : 'border-white/20'
                } focus:outline-none focus:border-teal-500`}
              />
              {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
            </div>
          )}
        </form>
      </div>

      {/* Submit button */}
      <div className="p-4 border-t border-white/10 flex gap-2">
        <button
          onClick={onCancel}
          className="flex-1 py-3 rounded-lg bg-white/10 text-white/70 font-medium hover:bg-white/20 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          className="flex-1 py-3 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors"
        >
          Create Group
        </button>
      </div>
    </motion.div>
  );
};

export default CreateGroupForm;
