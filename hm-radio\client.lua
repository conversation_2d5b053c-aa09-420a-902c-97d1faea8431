local SetRadioStationIsVisible = SetRadioStationIsVisible
local radioDisabled = false

local function DisableRadioStations()
    local radioStations = {
        -- "RADIO_100_HM1",
        "RADIO_01_CLASS_ROCK",
        "RADIO_02_POP",
        "RADIO_03_HIPHOP_NEW",
        "RADIO_04_PUNK",
        "RADIO_05_TALK_01",
        "RADIO_06_COUNTRY",
        "RADIO_07_DANCE_01",
        "RADIO_08_MEXICAN",
        "RADIO_09_HIPHOP_OLD",
        "RADIO_11_TALK_02",
        "RADIO_12_REGGAE",
        "RADIO_13_JAZZ",
        "RADIO_14_DANCE_02",
        "RADIO_15_MOTOWN",
        "RADIO_16_SILVERLAKE",
        "RADIO_17_FUNK",
        "RADIO_18_90S_ROCK",
        "RADIO_19_USER", -- Self Radio
        "RADIO_20_THELAB",
        "RADIO_21_DLC_XM17",
        "RADIO_22_DLC_BATTLE_MIX1_RADIO",
        "RADIO_23_DLC_XM19_RADIO",
        "RADIO_34_DLC_HEI4_KULT",
        "RADIO_35_DLC_HEI4_MLR",
        "RADIO_36_AUDIOPLAYER",
        "RADIO_37_MOTOMAMI"
    }

    for i = 1, #radioStations do
        local radioStation = radioStations[i]
        SetRadioStationIsVisible(radioStation, false)
    end
end


-- CreateThread(function()
--     if radioDisabled then return end
--     radioDisabled = true
--     DisableRadioStations()
-- end)

function SetRadioTrackWithStartOffset(stationName, trackName, startOffset)
    -- // help on how the

    FreezeRadioStation(GetPlayerRadioStationName())
    SetRadioToStationName(stationName)
    print('freezing current station ' .. GetPlayerRadioStationName()) 
    -- FreezeRadioStation(stationName)
    -- print ('freezing station ' .. stationName)
    -- local test = SetRadioTrackMix(stationName, trackName, startOffset) -- Replace with actual native hash if known
    -- print ('test: ' .. tostring(test))
    -- UnfreezeRadioStation(stationName)
    -- Wait(4000)
    local freeze = FreezeRadioStation(stationName)
    Wait(1000) -- Wait for the freeze to take effect
    local set = Citizen.InvokeNative(0x2CB0075110BE1E56, stationName, trackName, startOffset) -- SetNextRadioTrack
    Wait(1000) -- Wait for the track to be set
    local freeze2 = UnfreezeRadioStation(stationName)
    print('freeze: ' .. tostring(freeze) .. ' set: ' .. tostring(set) .. ' unfreeze: ' .. tostring(freeze2))
    -- SetRadioToStationName(stationName)
    -- local veh = GetVehiclePedIsIn(PlayerPedId(), false)
    -- if veh and veh ~= 0 then
    --     SetVehRadioStation(veh, stationName)
    -- end
    -- print ('playing ' .. trackName .. ' on station ' .. stationName .. ' with offset ' .. startOffset)
end

local tracklist = "dlc_hm_radio_music"

RegisterCommand("setTrackOffset", function(source, args, rawCommand)
    -- help on how the command works
    if not args[1] or not args[2] or not args[3] then
        print("Invalid arguments. Usage: /setTrackOffset [stationName] [trackName] [offset]")
        return
    end
    local ped = PlayerPedId()
    local currentStation = GetPlayerRadioStationName()
    local Stationname = args[1]
    local trackName = args[2]
    local offset = args[3]
    print('playing ' .. trackName .. ' on station ' .. Stationname .. ' with offset ' .. offset)
    SetRadioTrackWithStartOffset(Stationname, trackName, offset*1000) -- Reset track to start
end, false)




			-- FREEZE_RADIO_STATION("RADIO_23_DLC_XM19_RADIO")
			-- SET_RADIO_TRACK_WITH_START_OFFSET("RADIO_23_DLC_XM19_RADIO", "DLC_HEIST3_CRIME_PAYS", 120000)
			-- UNFREEZE_RADIO_STATION("RADIO_23_DLC_XM19_RADIO")
			-- SET_NEXT_RADIO_TRACK("RADIO_23_DLC_XM19_RADIO", "RADIO_TRACK_CAT_DJSOLO", "", "dlc_ch_finale_radio_djsolo")
			
			-- SET_RADIO_TO_STATION_NAME("RADIO_23_DLC_XM19_RADIO")
			
			-- IF NETWORK_HAS_CONTROL_OF_ENTITY(tempVeh)
			-- 	SET_VEH_RADIO_STATION(tempVeh, "RADIO_23_DLC_XM19_RADIO")
			-- ENDIF


            -- Citizen.invokeNative("SET_RADIO_TO_STATION_NAME", "RADIO_23_DLC_XM19_RADIO")