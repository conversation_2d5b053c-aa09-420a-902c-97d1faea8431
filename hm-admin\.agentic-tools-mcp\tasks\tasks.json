{"projects": [{"id": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "name": "Weather and Time Control Feature", "description": "Implementation of comprehensive weather and time control system for HM Admin panel with real-time weather changes, time manipulation, and environmental controls", "createdAt": "2025-06-03T00:01:29.215Z", "updatedAt": "2025-06-03T00:01:29.215Z"}, {"id": "5bf1da07-2107-47f2-8b30-4bcf1474b912", "name": "Weather and Time Control System", "description": "Implement a comprehensive weather and time control system for the HM Admin panel. This will include weather type changes, time manipulation, time scale control, and blackout mode functionality using FiveM natives.", "createdAt": "2025-06-03T00:04:13.798Z", "updatedAt": "2025-06-03T00:04:13.798Z"}], "tasks": [{"id": "55f746a0-1397-41dd-b6b0-5bf5798236d0", "name": "Create WeatherManager Core Implementation", "details": "Create the core WeatherManager class with all weather and time control functionality including weather types, time manipulation, and environmental effects", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:33.111Z", "updatedAt": "2025-06-03T00:01:33.111Z"}, {"id": "62a5ffc2-2d5e-48e3-9e17-2ffe3ded56ca", "name": "Integrate WeatherManager with AdminManager", "details": "Integrate the WeatherManager into the AdminManager by adding initialization, NUI callbacks, and proper lifecycle management", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:55.179Z", "updatedAt": "2025-06-03T00:01:55.179Z"}, {"id": "93054aed-e9dc-4070-9b1b-b5e619eaf65f", "name": "Create WeatherManager Class Structure", "details": "Create the core WeatherManager class file that will handle all weather and time control functionality. This includes the basic class structure, initialization, and integration with the admin manager system.", "projectId": "5bf1da07-2107-47f2-8b30-4bcf1474b912", "completed": false, "createdAt": "2025-06-03T00:04:18.150Z", "updatedAt": "2025-06-03T00:04:18.150Z"}, {"id": "4c517e1f-15af-4ef8-8af1-928c5d4efa8a", "name": "Implement Weather Control Features", "details": "Implement all weather control methods including setting weather types (clear, rain, fog, snow, etc.), weather transitions, and weather persistence. Use FiveM weather natives for proper functionality.", "projectId": "5bf1da07-2107-47f2-8b30-4bcf1474b912", "completed": false, "createdAt": "2025-06-03T00:04:22.838Z", "updatedAt": "2025-06-03T00:04:22.838Z"}, {"id": "9d2d99fb-15f8-4eff-ad2f-a155db19541c", "name": "Implement Time Control Features", "details": "Implement time control functionality including setting specific time, adding/subtracting time, pausing time, and time scale control (slow motion/fast forward). Use FiveM time and clock natives.", "projectId": "5bf1da07-2107-47f2-8b30-4bcf1474b912", "completed": false, "createdAt": "2025-06-03T00:04:27.024Z", "updatedAt": "2025-06-03T00:04:27.024Z"}], "subtasks": [{"id": "5c665f55-e3cc-4192-8820-d28f53a906d4", "name": "Setup folder structure and basic WeatherManager class", "details": "Create the features folder structure and weather-manager.ts file with basic class setup and TypeScript interfaces for weather and time data", "taskId": "55f746a0-1397-41dd-b6b0-5bf5798236d0", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:37.105Z", "updatedAt": "2025-06-03T00:01:37.105Z"}, {"id": "0a4b06b8-0c50-4587-8e92-fb5645f51733", "name": "Implement weather control methods", "details": "Implement weather control methods using FiveM natives: SET_WEATHER_TYPE_NOW, SET_WEATHER_TYPE_PERSIST, SET_WEATHER_TYPE_OVERTIME with validation and error handling", "taskId": "55f746a0-1397-41dd-b6b0-5bf5798236d0", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:41.213Z", "updatedAt": "2025-06-03T00:01:41.213Z"}, {"id": "8f42a513-6734-4912-b7df-6aeb0db1e97d", "name": "Implement time control methods", "details": "Implement time control methods using SET_CLOCK_TIME, ADD_TO_CLOCK_TIME, PAUSE_CLOCK, and SET_TIME_SCALE natives with time validation and smooth transitions", "taskId": "55f746a0-1397-41dd-b6b0-5bf5798236d0", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:46.085Z", "updatedAt": "2025-06-03T00:01:46.085Z"}, {"id": "38e141d4-129d-4db5-ac4e-6eac49fc3382", "name": "Implement environmental effects", "details": "Implement environmental effects including blackout mode (SET_ARTIFICIAL_LIGHTS_STATE), wind control, and other atmospheric effects", "taskId": "55f746a0-1397-41dd-b6b0-5bf5798236d0", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:50.533Z", "updatedAt": "2025-06-03T00:01:50.533Z"}, {"id": "42dd5b75-1489-40c8-b2d2-6807031ac69a", "name": "Add WeatherManager to AdminManager initialization", "details": "Add WeatherManager property to AdminManager class and initialize it in initializeSubsystems method", "taskId": "62a5ffc2-2d5e-48e3-9e17-2ffe3ded56ca", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:01:59.155Z", "updatedAt": "2025-06-03T00:01:59.155Z"}, {"id": "362af437-ae4a-4d3d-b259-86347231b3c2", "name": "Register weather and time NUI callbacks", "details": "Register NUI callbacks for weather control: setWeather, setTime, setTimeScale, toggleBlackout, pauseTime, addTimeToClockTime", "taskId": "62a5ffc2-2d5e-48e3-9e17-2ffe3ded56ca", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:02:03.362Z", "updatedAt": "2025-06-03T00:02:03.362Z"}, {"id": "67f0dd5a-423b-4a8f-af23-9d5e375c27ec", "name": "Add WeatherManager cleanup", "details": "Add WeatherManager cleanup in cleanupSubsystems method to properly dispose of any resources when admin panel closes", "taskId": "62a5ffc2-2d5e-48e3-9e17-2ffe3ded56ca", "projectId": "ad4de751-0f7b-4915-b9bd-c14d1ea3cf9e", "completed": false, "createdAt": "2025-06-03T00:02:07.634Z", "updatedAt": "2025-06-03T00:02:07.634Z"}]}