import { create } from 'zustand';
import { TabletApp, TabletConfig, AppState, Notification } from '@shared';
import { BUILTIN_APPS, DEFAULT_TABLET_CONFIG } from '@shared';

interface TabletStore extends AppState {
  // Configuration
  config: TabletConfig;
  
  // Notifications
  notifications: Notification[];
  
  // App Management
  installedApps: TabletApp[];
  
  // UI State
  isHomeScreen: boolean;
  isAppDrawerOpen: boolean;
  isNotificationPanelOpen: boolean;
  isSettingsOpen: boolean;
  
  // Actions
  setTabletVisible: (visible: boolean) => void;
  setCurrentApp: (appId: string | null) => void;
  setLocked: (locked: boolean) => void;
  
  // App Actions
  openApp: (appId: string) => void;
  closeApp: (appId: string) => void;
  goHome: () => void;
  
  // Configuration Actions
  updateConfig: (config: Partial<TabletConfig>) => void;
  
  // Notification Actions
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  markNotificationRead: (id: string) => void;
  
  // UI Actions
  toggleAppDrawer: () => void;
  toggleNotificationPanel: () => void;
  toggleSettings: () => void;
}

export const useTabletStore = create<TabletStore>((set, get) => ({
  // Initial State
  currentApp: null,
  isTabletVisible: false,
  isLocked: false,
  backgroundApps: [],
  
  config: {
    ...DEFAULT_TABLET_CONFIG,
    installedApps: BUILTIN_APPS,
  },
  
  notifications: [],
  installedApps: BUILTIN_APPS,
  
  isHomeScreen: true,
  isAppDrawerOpen: false,
  isNotificationPanelOpen: false,
  isSettingsOpen: false,
  
  // Basic Actions
  setTabletVisible: (visible) => set({ isTabletVisible: visible }),
  setCurrentApp: (appId) => set({ currentApp: appId, isHomeScreen: !appId }),
  setLocked: (locked) => set({ isLocked: locked }),
  
  // App Management
  openApp: (appId) => {
    const state = get();
    
    // Add to recent apps
    const recentApps = state.config.recentApps.filter(id => id !== appId);
    recentApps.unshift(appId);
    if (recentApps.length > 10) recentApps.pop();
    
    // Add to background apps if not already there
    const backgroundApps = state.backgroundApps.includes(appId) 
      ? state.backgroundApps 
      : [...state.backgroundApps, appId];
    
    set({
      currentApp: appId,
      isHomeScreen: false,
      backgroundApps,
      config: {
        ...state.config,
        recentApps
      }
    });
  },
  
  closeApp: (appId) => {
    const state = get();
    const backgroundApps = state.backgroundApps.filter(id => id !== appId);
    
    set({
      currentApp: state.currentApp === appId ? null : state.currentApp,
      isHomeScreen: state.currentApp === appId ? true : state.isHomeScreen,
      backgroundApps
    });
  },
  
  goHome: () => set({ 
    currentApp: null, 
    isHomeScreen: true,
    isAppDrawerOpen: false,
    isNotificationPanelOpen: false,
    isSettingsOpen: false
  }),
  
  // Configuration
  updateConfig: (newConfig) => {
    const state = get();
    set({
      config: { ...state.config, ...newConfig }
    });
  },
  
  // Notifications
  addNotification: (notification) => {
    const state = get();
    set({
      notifications: [notification, ...state.notifications]
    });
  },
  
  removeNotification: (id) => {
    const state = get();
    set({
      notifications: state.notifications.filter(n => n.id !== id)
    });
  },
  
  clearNotifications: () => set({ notifications: [] }),
  
  markNotificationRead: (id) => {
    const state = get();
    set({
      notifications: state.notifications.map(n => 
        n.id === id ? { ...n, isRead: true } : n
      )
    });
  },
  
  // UI Actions
  toggleAppDrawer: () => {
    const state = get();
    set({ 
      isAppDrawerOpen: !state.isAppDrawerOpen,
      isNotificationPanelOpen: false,
      isSettingsOpen: false
    });
  },
  
  toggleNotificationPanel: () => {
    const state = get();
    set({ 
      isNotificationPanelOpen: !state.isNotificationPanelOpen,
      isAppDrawerOpen: false,
      isSettingsOpen: false
    });
  },
  
  toggleSettings: () => {
    const state = get();
    set({ 
      isSettingsOpen: !state.isSettingsOpen,
      isAppDrawerOpen: false,
      isNotificationPanelOpen: false
    });
  },
}));
