import React, { memo, useMemo, useState, useEffect, useRef, useCallback } from 'react'; // Added useCallback
import { useBankingStore } from '../store/bankingStore';
import { TransactionType } from '../../../scripts/shared/types';

const TransactionHistory: React.FC = memo(() => {
  const transactions = useBankingStore((state) => state.transactions);
  const selectedAccountId = useBankingStore((state) => state.selectedAccountId);
  const isLoadingTransactions = useBankingStore((state) => state.isLoadingTransactions);
  
  const [displayCount, setDisplayCount] = useState(10);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  const LOAD_INCREMENT = 10;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(Math.abs(amount));
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case 'deposit':
        return 'fa-arrow-down';
      case 'withdrawal':
        return 'fa-arrow-up';
      case 'transfer_in':
        return 'fa-arrow-right';
      case 'transfer_out':
        return 'fa-arrow-left';
      case 'fee':
        return 'fa-receipt';
      case 'interest_payment':
        return 'fa-percentage';
      case 'salary':
        return 'fa-money-check';
      case 'business_payout':
        return 'fa-business-time';
      case 'invoice_payment':
        return 'fa-file-invoice-dollar';
      case 'account_creation':
        return 'fa-plus-circle';
      case 'system_adjustment':
        return 'fa-cog';
      default:
        return 'fa-exchange-alt';
    }
  };

  const getTransactionColor = (amount: number) => {
    if (amount > 0) {
      return 'text-green-400';
    } else {
      return 'text-red-400';
    }
  };

  const getTransactionBgColor = (amount: number) => {
    if (amount > 0) {
      return 'bg-green-500/10 border-green-500/20';
    } else {
      return 'bg-red-500/10 border-red-500/20';
    }
  };

  const getTransactionTypeLabel = (type: TransactionType) => {
    switch (type) {
      case 'deposit':
        return 'Deposit';
      case 'withdrawal':
        return 'Withdrawal';
      case 'transfer_in':
        return 'Transfer In';
      case 'transfer_out':
        return 'Transfer Out';
      case 'fee':
        return 'Fee';
      case 'interest_payment':
        return 'Interest';
      case 'salary':
        return 'Salary';
      case 'business_payout':
        return 'Business Payout';
      case 'invoice_payment':
        return 'Invoice Payment';
      case 'account_creation':
        return 'Account Creation';
      case 'system_adjustment':
        return 'System Adjustment';
      default:
        return 'Transaction';
    }
  };  // Sort transactions by timestamp (newest first)
  const sortedTransactions = useMemo(() => {
    return [...transactions].sort((a, b) => b.timestamp - a.timestamp);
  }, [transactions]);

  // Get transactions to display based on current display count
  const displayedTransactions = useMemo(() => {
    return sortedTransactions.slice(0, displayCount);
  }, [sortedTransactions, displayCount]);

  // Memoize loadMoreTransactions
  const loadMoreTransactions = useCallback(() => {
    if (isLoadingMore || displayCount >= sortedTransactions.length) {
      return;
    }
    setIsLoadingMore(true);
    setTimeout(() => {
      setDisplayCount(prevCount => Math.min(prevCount + LOAD_INCREMENT, sortedTransactions.length));
      setIsLoadingMore(false);
    }, 500);
  }, [isLoadingMore, displayCount, sortedTransactions.length, LOAD_INCREMENT]);

  // Memoize the check logic for loading more
  const checkAndLoadMore = useCallback(() => {
    if (scrollContainerRef.current && !isLoadingMore) {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
      // If content is not scrollable OR scrolled to near bottom
      if (scrollTop + clientHeight >= scrollHeight - 15) { // Use a small threshold
        if (displayCount < sortedTransactions.length) { // Only if there are more to load
            loadMoreTransactions();
        }
      }
    }
  }, [isLoadingMore, loadMoreTransactions, displayCount, sortedTransactions.length]);

  // Scroll event handler
  const handleScroll = useCallback(() => {
    checkAndLoadMore();
  }, [checkAndLoadMore]);

  // Effect for scroll listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);
  
  // Effect to check for loading more when displayed items change or on initial load if space available
  useEffect(() => {
    // Use requestAnimationFrame to ensure DOM has been updated and dimensions are stable
    const frameId = requestAnimationFrame(() => {
      checkAndLoadMore();
    });
    return () => cancelAnimationFrame(frameId);
  }, [displayedTransactions, checkAndLoadMore]); // Re-check when items or check function changes

  // Reset display count when account changes
  useEffect(() => {
    setDisplayCount(10);
    // The checkAndLoadMore will be triggered by displayedTransactions changing
  }, [selectedAccountId]);

  if (!selectedAccountId) {
    return null;
  }

  // Show loading state when fetching transactions
  if (isLoadingTransactions && transactions.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <div className="w-5 h-5 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin mx-auto mb-3" />
          <p className="text-neutral-400 text-sm">Loading transactions...</p>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-neutral-700/50 rounded-full flex items-center justify-center mx-auto mb-4 border border-neutral-600/30">
            <i className="fas fa-history text-neutral-400 text-xl" />
          </div>
          <h3 className="text-neutral-200 font-medium mb-2">No Transactions</h3>
          <p className="text-neutral-400 text-sm max-w-sm">
            This account has no transaction history yet. Make a deposit, withdrawal, or transfer to get started.
          </p>
        </div>
      </div>
    );
  }return (
    <div className="flex-1 flex flex-col min-h-0">
      {/* Simple Header - This part remains fixed */}
      <div className="p-4 flex-shrink-0">
        <h3 className="text-neutral-200 font-medium text-sm">Recent Transactions</h3>
      </div>

      {/* Transaction List with Infinite Scroll - This part scrolls */}
      <div
        ref={scrollContainerRef}
        className="flex-1 overflow-y-auto px-4 pb-4 space-y-2 min-h-0" // Added min-h-0
      >
        {displayedTransactions.map((transaction) => (
          <div
            key={transaction.transactionId}
            className="bg-neutral-900/60 border border-neutral-700/40 rounded-lg p-3 hover:bg-neutral-800/60 hover:border-neutral-600/50 transition-all duration-200"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                <div className={`w-7 h-7 rounded-lg flex items-center justify-center ${getTransactionBgColor(transaction.amount)} border`}>
                  <i className={`fas ${getTransactionIcon(transaction.type)} ${getTransactionColor(transaction.amount)} text-xs`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-neutral-200 font-medium text-sm">
                      {getTransactionTypeLabel(transaction.type)}
                    </span>
                    {transaction.counterpartyName && (
                      <span className="text-neutral-500 text-xs">
                        → {transaction.counterpartyName}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs">
                    <span className="text-neutral-500">
                      {formatDate(transaction.timestamp)}
                    </span>
                    {transaction.description && (
                      <span className="text-neutral-600 truncate max-w-48">
                        {transaction.description}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className={`font-semibold text-sm ${getTransactionColor(transaction.amount)}`}>
                  {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                </div>
                <div className="text-neutral-500 text-xs">
                  {formatCurrency(transaction.newBalance)}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Loading More Indicator */}
        {isLoadingMore && (
          <div className="text-center py-4">
            <div className="inline-flex items-center gap-2 text-neutral-400">
              <div className="w-4 h-4 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin" />
              <span className="text-sm">Loading more transactions...</span>
            </div>
          </div>
        )}
        
        {/* End of list indicator */}
        {displayCount >= sortedTransactions.length && sortedTransactions.length > 10 && (
          <div className="text-center py-4">
            <div className="text-neutral-500 text-sm">
              <i className="fas fa-check-circle mr-2" />
              All transactions loaded
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

TransactionHistory.displayName = 'TransactionHistory';

export default TransactionHistory;
