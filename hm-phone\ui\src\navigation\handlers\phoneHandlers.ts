/**
 * Phone core message handlers
 */

import { registerEventHandler } from '../../fivem/clientEventReceiver';
import { setPhoneState } from '../../providers/PhoneContext';
import { PhoneStateEnum } from '../../common/stores/phoneStateStore';

// Register handler for phone state changes
registerEventHandler('phone', 'setState', data => {
  console.log('[Phone] Received state change request:', data);

  // Validate and convert the state value
  let newState: PhoneStateEnum;

  if (typeof data === 'object' && data !== null && 'state' in data) {
    // Extract state from the data object
    const stateValue = data.state;

    // Convert to PhoneStateEnum
    if (typeof stateValue === 'number' &&
        stateValue >= 0 &&
        stateValue <= 2) {
      newState = stateValue as PhoneStateEnum;
    } else {
      console.error('[Phone] Invalid state value received:', stateValue);
      return;
    }
  } else if (typeof data === 'number' && data >= 0 && data <= 2) {
    // Direct numeric value
    newState = data as PhoneStateEnum;
  } else {
    console.error('[Phone] Invalid state data received:', data);
    return;
  }

  // Only update via the context - this will trigger the PhoneProvider effect
  // which will update the store. This prevents circular updates.
  setPhoneState(newState);
});