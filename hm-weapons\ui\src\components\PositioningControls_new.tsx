import React from 'react';
import { useWeaponModdingStore } from '../stores/weaponModdingStore';
import '../styles/sliders.css';

interface PositioningControlsProps {
  isVisible: boolean;
}

const PositioningControls: React.FC<PositioningControlsProps> = ({ isVisible }) => {
  const { currentOffsets, actions } = useWeaponModdingStore();
  if (!isVisible) return null;
  
  const sectionClass = "bg-neutral-800/80 border border-neutral-700/50 rounded-lg p-3 shadow-lg pointer-events-auto";
  const labelClass = "text-neutral-300 text-xs font-medium mb-2";
  const valueClass = "text-green-400 text-xs font-mono";

  return (
    <div className="fixed bottom-4 right-4 flex flex-col gap-3 z-[9999] max-w-xs pointer-events-auto">
      {/* Offset Display */}
      <div className={sectionClass}>
        <div className="flex items-center gap-2 mb-3">
          <i className="fas fa-crosshairs text-green-400 text-sm" />
          <span className="text-green-400 text-sm font-bold">Position Offsets</span>
        </div>
        
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div>
            <div className={labelClass}>Weapon Position</div>
            <div className={valueClass}>
              X: {currentOffsets.weaponPosition.x.toFixed(3)}<br />
              Y: {currentOffsets.weaponPosition.y.toFixed(3)}<br />
              Z: {currentOffsets.weaponPosition.z.toFixed(3)}
            </div>
          </div>
          
          <div>
            <div className={labelClass}>Weapon Rotation</div>
            <div className={valueClass}>
              X: {currentOffsets.weaponRotation.x.toFixed(3)}<br />
              Y: {currentOffsets.weaponRotation.y.toFixed(3)}<br />
              Z: {currentOffsets.weaponRotation.z.toFixed(3)}
            </div>
          </div>
          
          <div>
            <div className={labelClass}>Camera Position</div>
            <div className={valueClass}>
              X: {currentOffsets.cameraPosition.x.toFixed(3)}<br />
              Y: {currentOffsets.cameraPosition.y.toFixed(3)}<br />
              Z: {currentOffsets.cameraPosition.z.toFixed(3)}
            </div>
          </div>
          
          <div>
            <div className={labelClass}>Camera Rotation</div>
            <div className={valueClass}>
              X: {currentOffsets.cameraRotation.x.toFixed(3)}<br />
              Y: {currentOffsets.cameraRotation.y.toFixed(3)}<br />
              Z: {currentOffsets.cameraRotation.z.toFixed(3)}
            </div>
          </div>
        </div>
      </div>

      {/* Weapon Position Controls */}
      <div className={sectionClass}>
        <div className={labelClass}>
          <i className="fas fa-gun text-green-400 mr-2" />
          Weapon Position
        </div>
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">X</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponPosition.x.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.weaponPosition.x))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponPosition('x', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon X Position"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Y</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponPosition.y.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.weaponPosition.y))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponPosition('y', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon Y Position"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Z</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponPosition.z.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.weaponPosition.z))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponPosition('z', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon Z Position"
            />
          </div>
        </div>
      </div>

      {/* Weapon Rotation Controls */}
      <div className={sectionClass}>
        <div className={labelClass}>
          <i className="fas fa-redo text-green-400 mr-2" />
          Weapon Rotation
        </div>
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">X (Pitch)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponRotation.x.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.weaponRotation.x))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponRotation('x', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon X Rotation (Pitch)"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Y (Roll)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponRotation.y.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.weaponRotation.y))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponRotation('y', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon Y Rotation (Roll)"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Z (Yaw)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.weaponRotation.z.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.weaponRotation.z))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setWeaponRotation('z', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Weapon Z Rotation (Yaw)"
            />
          </div>
        </div>
      </div>

      {/* Camera Position Controls */}
      <div className={sectionClass}>
        <div className={labelClass}>
          <i className="fas fa-video text-green-400 mr-2" />
          Camera Position
        </div>
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">X</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraPosition.x.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.cameraPosition.x))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraPosition('x', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera X Position"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Y</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraPosition.y.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.cameraPosition.y))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraPosition('y', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera Y Position"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Z</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraPosition.z.toFixed(3)}</span>
            </div>
            <input
              type="range"
              min="-5"
              max="5"
              step="0.01"
              value={Math.max(-5, Math.min(5, currentOffsets.cameraPosition.z))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraPosition('z', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera Z Position"
            />
          </div>
        </div>
      </div>

      {/* Camera Rotation Controls */}
      <div className={sectionClass}>
        <div className={labelClass}>
          <i className="fas fa-camera text-green-400 mr-2" />
          Camera Rotation
        </div>
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">X (Pitch)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraRotation.x.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.cameraRotation.x))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraRotation('x', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera X Rotation (Pitch)"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Y (Roll)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraRotation.y.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.cameraRotation.y))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraRotation('y', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera Y Rotation (Roll)"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-neutral-400 text-xs">Z (Yaw)</span>
              <span className="text-green-400 text-xs font-mono">{currentOffsets.cameraRotation.z.toFixed(1)}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="1"
              value={Math.max(-180, Math.min(180, currentOffsets.cameraRotation.z))}
              onChange={(e) => {
                const newValue = parseFloat(e.target.value);
                actions.setCameraRotation('z', newValue);
              }}
              className="w-full h-2 bg-neutral-700 rounded-lg appearance-none cursor-pointer slider"
              title="Camera Z Rotation (Yaw)"
            />
          </div>
        </div>
      </div>

      {/* Copy Offsets Button */}
      <div className={sectionClass}>
        <button
          onClick={() => actions.copyOffsets()}
          className="w-full px-4 py-2 bg-green-600 hover:bg-green-500 border border-green-500 rounded-lg text-white font-medium transition-colors flex items-center justify-center gap-2 pointer-events-auto cursor-pointer"
          title="Copy current offsets to clipboard"
        >
          <i className="fas fa-copy" />
          Copy Offsets
        </button>
      </div>
    </div>
  );
};

export default PositioningControls;
