import React from 'react';
import { useNavigation } from '../../../navigation/hooks';
import { useBankingStore } from '../stores/bankingStore';
import { motion } from 'framer-motion';

interface TransactionDetailViewProps {
  transactionId: number;
}

const TransactionDetailView: React.FC<TransactionDetailViewProps> = ({ transactionId }) => {
  const { goBack } = useNavigation();
  const { transactions, accounts } = useBankingStore();

  // Find the transaction by ID
  const transaction = transactions.find(t => t.id === transactionId);

  // If transaction not found, show error state
  if (!transaction) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <div className="w-12 h-12 rounded-full backdrop-blur-md bg-white/10 border border-white/20 flex items-center justify-center mb-2">
          <i className="fas fa-exclamation-triangle text-xl text-yellow-400"></i>
        </div>
        <h3 className="text-base font-medium text-white mb-1">Transaction not found</h3>
        <p className="text-white/60 text-xs max-w-xs mb-3">This transaction may no longer be available</p>
        <button
          onClick={goBack}
          className="px-4 py-2 backdrop-blur-md bg-white/10 hover:bg-white/20 text-white text-xs rounded-lg border border-white/20 transition-colors"
        >
          Back to Transactions
        </button>
      </div>
    );
  }

  // Find the account this transaction belongs to
  const account = accounts.find(a => a.id === transaction.accountId);

  // Format date and time - using more compact format
  const transactionDate = new Date(transaction.timestamp);
  const formattedDate = transactionDate.toLocaleDateString(undefined, {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  const formattedTime = transactionDate.toLocaleTimeString(undefined, {
    hour: '2-digit',
    minute: '2-digit'
  });

  // Determine if debit or credit
  const isDebit = transaction.type === 'debit';
  const amount = isDebit ? -transaction.amount : transaction.amount;

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-2 pb-2">
      {/* Header */}
      <div className="flex items-center px-4 mb-2">
        <button
          onClick={goBack}
          className="w-8 h-8 flex items-center justify-center text-white/80 hover:text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <h1 className="text-white font-medium text-lg ml-2">Transaction Details</h1>
      </div>

      {/* Transaction Card - Main info */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
        className="mx-4 bg-white/5 border border-white/10 rounded-xl p-3 mb-2"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div
              className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                isDebit
                  ? 'bg-rose-500/20 text-rose-400'
                  : 'bg-teal-500/20 text-teal-400'
              }`}
            >
              {transaction.icon ? (
                <img
                  src={transaction.icon}
                  alt={transaction.merchantName}
                  className="w-6 h-6 rounded-lg"
                />
              ) : (
                <i className={`fas fa-${isDebit ? 'shopping-bag' : 'arrow-down'}`}></i>
              )}
            </div>
            <div>
              <div className="text-white font-medium text-sm">{transaction.merchantName}</div>
              <div className="text-white/60 text-xs">
                {formattedDate} • {formattedTime}
              </div>
            </div>
          </div>
          <div className={`font-medium ${isDebit ? 'text-rose-400' : 'text-teal-400'}`}>
            {isDebit ? '-' : '+'}€{Math.abs(amount).toFixed(2)}
          </div>
        </div>
      </motion.div>

      {/* Transaction Details - Grid layout for more compact display */}
      <div className="px-4 grid grid-cols-2 gap-2 mb-2">
        {/* Description - Full width if present */}
        {transaction.description && (
          <div className="bg-white/5 rounded-lg p-2 col-span-2">
            <div className="text-white/60 text-xs">Description</div>
            <div className="text-white text-sm truncate">{transaction.description}</div>
          </div>
        )}

        {/* Account */}
        <div className="bg-white/5 rounded-lg p-2">
          <div className="text-white/60 text-xs">Account</div>
          <div className="text-white text-sm truncate">{account?.name || 'Unknown'}</div>
          <div className="text-white/60 text-xs">{account?.number || ''}</div>
        </div>

        {/* Status */}
        <div className="bg-white/5 rounded-lg p-2">
          <div className="text-white/60 text-xs">Status</div>
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-teal-400 mr-1"></div>
            <div className="text-white text-sm">Completed</div>
          </div>
          <div className="text-white/60 text-xs">ID: {transaction.id}</div>
        </div>
      </div>

      {/* Actions - Compact layout with icons */}
      <div className="px-4 mt-auto grid grid-cols-2 gap-2">
        <button className="py-2 bg-white/10 hover:bg-white/15 text-white rounded-xl transition-colors text-sm">
          <i className="fas fa-receipt mr-1"></i>
          View Receipt
        </button>
        <button className="py-2 bg-white/10 hover:bg-white/15 text-white rounded-xl transition-colors text-sm">
          <i className="fas fa-question-circle mr-1"></i>
          Report Issue
        </button>
      </div>
    </div>
  );
};

export default TransactionDetailView;