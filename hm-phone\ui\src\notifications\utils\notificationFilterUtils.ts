/**
 * Utility functions for filtering notifications
 */

import { Notification } from '../types/notificationTypes';

/**
 * Get message notifications that have been moved to the top bar
 * @param notifications Record of notifications by app ID
 * @returns Array of message notifications
 */
export const getMessageNotifications = (
  notifications: Record<number, Notification[]>
): Notification[] => {
  return Object.values(notifications)
    .flat()
    .filter(n => n.type === 'message' && n.movedToTopBar)
    .sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * Get non-message notifications that have been moved to the top bar
 * @param notifications Record of notifications by app ID
 * @returns Array of non-message notifications
 */
export const getOtherNotifications = (
  notifications: Record<number, Notification[]>
): Notification[] => {
  return Object.values(notifications)
    .flat()
    .filter(n => n.type !== 'message' && n.movedToTopBar)
    .sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * Get notifications for a specific app
 * @param notifications Record of notifications by app ID
 * @param appId App ID
 * @returns Array of notifications for the app
 */
export const getNotificationsByApp = (
  notifications: Record<number, Notification[]>,
  appId: number
): Notification[] => {
  return notifications[appId] || [];
};

/**
 * Get notifications by type
 * @param notifications Record of notifications by app ID
 * @param type Notification type
 * @returns Array of notifications of the specified type
 */
export const getNotificationsByType = (
  notifications: Record<number, Notification[]>,
  type: string
): Notification[] => {
  return Object.values(notifications)
    .flat()
    .filter(n => n.type === type)
    .sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * Get notifications by group ID
 * @param notifications Record of notifications by app ID
 * @param groupId Group ID
 * @returns Array of notifications in the group
 */
export const getNotificationsByGroup = (
  notifications: Record<number, Notification[]>,
  groupId: string
): Notification[] => {
  return Object.values(notifications)
    .flat()
    .filter(n => n.groupId === groupId)
    .sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * Get summary notifications
 * @param notifications Record of notifications by app ID
 * @returns Array of summary notifications
 */
export const getSummaryNotifications = (
  notifications: Record<number, Notification[]>
): Notification[] => {
  return Object.values(notifications)
    .flat()
    .filter(n => n.isGroupSummary)
    .sort((a, b) => b.timestamp - a.timestamp);
};
