import React from 'react';
import { motion } from 'framer-motion';
import { AlbumCardProps } from '../types/musicTypes';

const AlbumCard: React.FC<AlbumCardProps> = ({ album, onClick }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      className="flex-shrink-0 w-32 cursor-pointer"
      onClick={onClick}
    >
      <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
        <img src={album.imageUrl} alt={album.title} className="w-full h-full object-cover" />
      </div>
      <h3 className="text-sm font-medium text-white truncate">{album.title}</h3>
      <p className="text-xs text-gray-400 truncate">{album.artist?.name || 'Unknown Artist'}</p>
    </motion.div>
  );
};

export default AlbumCard;
