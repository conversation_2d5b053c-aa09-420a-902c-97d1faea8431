import React, { memo, useState } from "react";
import { motion } from "framer-motion";

interface TargetInfo {
  entity?: number;
  modelHash?: number;
  zone?: any;
  position: { x: number; y: number; z: number };
  options: any[];
  distance: number;
  type?: "entity" | "zone" | "model";
  entityType?: "player" | "vehicle" | "ped" | "object"; // Add entity type detection
}

interface InfoPanelProps {
  target: TargetInfo;
  onButtonClick: (action: string, data?: any) => void;
}

const InfoPanel: React.FC<InfoPanelProps> = memo(({ target, onButtonClick }) => {
  const [healthAmount, setHealthAmount] = useState(200);

  const formatCoords = (pos: { x: number; y: number; z: number }) => {
    return `${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)}`;
  };

  const getEntityType = () => {
    if (target.type === "zone") return "Zone";
    if (target.type === "model") return "Model";
    if (target.entity) {
      // Show more specific entity type if available
      if (target.entityType === "player") return "Player";
      if (target.entityType === "vehicle") return "Vehicle";
      if (target.entityType === "ped") return "NPC";
      if (target.entityType === "object") return "Object";
      return "Entity";
    }
    return "Unknown";
  };

  const getEntityId = () => {
    if (target.entity) return target.entity.toString();
    if (target.zone?.id) return target.zone.id;
    return "N/A";
  };

  const getModelHash = () => {
    if (target.modelHash) return target.modelHash.toString();
    return "N/A";
  };  // Universal actions that work on all entities
  const universalButtons = [
    {
      id: "teleport",
      label: "Teleport",
      icon: "fa-magic",
      action: "teleport",
      data: target.position,
      disabled: false,
      color: "green",
    },
    {
      id: "debug-info",
      label: "Debug Info",
      icon: "fa-bug",
      action: "debugInfo",
      data: target,
      disabled: false,
      color: "blue",
    },
    {
      id: "freeze",
      label: "Freeze Entity",
      icon: "fa-snowflake",
      action: "freeze",
      data: target.entity,
      disabled: !target.entity,
      color: "blue",
    },
  ];
  // Entity-specific actions based on entity type
  const getEntitySpecificButtons = () => {
    if (!target.entity || !target.entityType) return [];

    switch (target.entityType) {
      case "player":
        return [
          {
            id: "heal-player",
            label: "Heal Player",
            icon: "fa-heart",
            action: "healPlayer",
            data: { entity: target.entity, health: healthAmount },
            color: "green",
            disabled: false,
          },
          {
            id: "revive-player",
            label: "Revive Player",
            icon: "fa-plus-circle",
            action: "revivePlayer",
            data: { entity: target.entity },
            color: "blue",
            disabled: false,
          },
          {
            id: "spectate-player",
            label: "Spectate",
            icon: "fa-eye",
            action: "spectatePlayer", 
            data: { entity: target.entity },
            color: "purple",
            disabled: false,
          },
        ];
      
      case "vehicle":
        return [
          {
            id: "repair-vehicle",
            label: "Repair Vehicle",
            icon: "fa-wrench",
            action: "repairVehicle",
            data: { entity: target.entity },
            color: "blue",
            disabled: false,
          },
          {
            id: "delete-vehicle",
            label: "Delete Vehicle",
            icon: "fa-trash",
            action: "deleteVehicle",
            data: { entity: target.entity },
            color: "red",
            disabled: false,
          },
          {
            id: "flip-vehicle",
            label: "Flip Vehicle",
            icon: "fa-redo",
            action: "flipVehicle",
            data: { entity: target.entity },
            color: "yellow",
            disabled: false,
          },
        ];

      case "ped":
        return [
          {
            id: "delete-ped",
            label: "Delete NPC",
            icon: "fa-user-times",
            action: "deletePed",
            data: { entity: target.entity },
            color: "red",
            disabled: false,
          },
          {
            id: "clone-ped",
            label: "Clone NPC",
            icon: "fa-clone",
            action: "clonePed",
            data: { entity: target.entity },
            color: "blue",
            disabled: false,
          },
        ];

      case "object":
        return [
          {
            id: "delete-object",
            label: "Delete Object",
            icon: "fa-trash",
            action: "deleteObject",
            data: { entity: target.entity },
            color: "red",
            disabled: false,
          },
          {
            id: "clone-object",
            label: "Clone Object",
            icon: "fa-clone",
            action: "cloneObject",
            data: { entity: target.entity },
            color: "blue",
            disabled: false,
          },
        ];

      default:
        return [];
    }
  };

  const entitySpecificButtons = getEntitySpecificButtons();
  const allButtons = [...universalButtons, ...entitySpecificButtons];
  const getButtonColor = (button: any) => {
    if (button.disabled) return "text-neutral-600";
    
    switch (button.color) {
      case "green": return "text-green-400";
      case "blue": return "text-blue-400";
      case "red": return "text-red-400";
      case "yellow": return "text-yellow-400";
      case "purple": return "text-purple-400";
      default: return "text-green-400";
    }
  };

  const getBorderColor = (button: any) => {
    if (button.disabled) return "border-neutral-700/50";
    
    switch (button.color) {
      case "green": return "border-green-500/20 hover:border-green-500/40";
      case "blue": return "border-blue-500/20 hover:border-blue-500/40";
      case "red": return "border-red-500/20 hover:border-red-500/40";
      case "yellow": return "border-yellow-500/20 hover:border-yellow-500/40";
      case "purple": return "border-purple-500/20 hover:border-purple-500/40";
      default: return "border-green-500/20 hover:border-green-500/40";
    }
  };

  const handleHealPlayer = () => {
    onButtonClick("healPlayer", { 
      targetId: target.entity, 
      health: healthAmount 
    });
  };

  return (
    <div className="fixed inset-0 z-40 pointer-events-none">
      <motion.div
        className="pointer-events-auto absolute right-8 top-1/2 w-[18vw]"
        initial={{ opacity: 0, x: "100%", y: "-50%" }}
        animate={{ opacity: 1, x: 0, y: "-50%" }}
        exit={{ opacity: 0, x: "100%", y: "-50%" }}
        transition={{ duration: 0.4, ease: "easeOut" }}
      >
        <div className="bg-gradient-to-br from-neutral-800/95 via-neutral-850/90 to-neutral-900/95 rounded-2xl shadow-2xl border border-green-500/20 relative overflow-hidden backdrop-blur-md">
          {/* Smooth top gradient bar */}
          <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-green-400/15 via-green-500/25 to-green-400/15 z-10 rounded-t-2xl" />
          
          {/* Subtle corner glow effects */}
          <div className="absolute left-0 top-0 w-8 h-8 bg-gradient-to-br from-green-400/8 to-transparent rounded-tl-2xl z-10" />
          <div className="absolute right-0 top-0 w-8 h-8 bg-gradient-to-bl from-green-400/8 to-transparent rounded-tr-2xl z-10" />
          
          {/* Header with smooth gradient background */}
          <div className="flex items-center justify-between px-4 py-3 bg-gradient-to-r from-neutral-800/80 via-neutral-750/70 to-neutral-800/80 border-b border-green-500/15 relative z-20 backdrop-blur-sm">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse shadow-sm shadow-green-400/40"></div>
              <span className="text-neutral-100 text-sm font-medium tracking-wide">Target Info</span>
            </div>
            <button
              onClick={() => onButtonClick("close")}
              className="text-neutral-400 hover:text-red-400 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 rounded-full p-1"
              title="Close Panel"
            >
              <i className="fas fa-times text-sm" />
            </button>
          </div>

          {/* Content with gradient background */}
          <div className="p-4 space-y-3 bg-gradient-to-b from-neutral-900/50 to-neutral-900/80 backdrop-blur-sm">
            {/* Data Fields - Enhanced with gradients */}
            <div className="space-y-2">
              {/* Entity Type */}
              <div className="bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border border-neutral-700/50 rounded-lg overflow-hidden">
                <div className="flex items-center justify-center px-3 py-2">
                  <div className="flex items-center gap-1">
                    <span className="text-green-400 text-xs uppercase tracking-wide font-medium">Type:</span>
                    <span className="text-neutral-200 font-semibold text-sm">{getEntityType()}</span>
                  </div>
                </div>
              </div>

              {/* Entity ID and Hash - Labels outside divs */}
              <div className="flex gap-2">
                {/* Entity ID */}
                <div className="flex-1">
                  <span className="text-neutral-500 text-[10px] font-medium mb-1 block">ID</span>
                  <div className="group bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border border-neutral-700/50 hover:border-green-500/30 rounded-lg overflow-hidden transition-colors h-10">
                    <div className="flex items-center justify-between h-full px-3">
                      <span className="text-neutral-200 font-mono text-sm">{getEntityId()}</span>
                      <button
                        onClick={() => onButtonClick("copyId", getEntityId())}
                        className="opacity-0 group-hover:opacity-100 p-1 text-neutral-400 hover:text-green-400 transition-all"
                        title="Copy ID"
                      >
                        <i className="fas fa-copy text-xs" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Model Hash */}
                {target.modelHash && (
                  <div className="flex-1 min-w-0">
                    <span className="text-neutral-500 text-[10px] font-medium mb-1 block">HASH</span>
                    <div className="group bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border border-neutral-700/50 hover:border-green-500/30 rounded-lg overflow-hidden transition-colors h-10">
                      <div className="flex items-center justify-between h-full px-3">
                        <span className="text-neutral-200 font-mono text-sm truncate">{getModelHash()}</span>
                        <button
                          onClick={() => onButtonClick("copyHash", getModelHash())}
                          className="opacity-0 group-hover:opacity-100 p-1 text-neutral-400 hover:text-green-400 transition-all"
                          title="Copy Hash"
                        >
                          <i className="fas fa-copy text-xs" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Coordinates - Label outside div */}
              <div>
                <span className="text-neutral-500 text-[10px] font-medium mb-1 block">COORDS</span>
                <div className="group bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border border-neutral-700/50 hover:border-green-500/30 rounded-lg overflow-hidden transition-colors h-10">
                  <div className="flex items-center justify-between h-full px-3">
                    <div className="flex gap-2 text-xs">
                      <span className="text-neutral-200 font-mono">{target.position.x.toFixed(2)}</span>
                      <span className="text-neutral-500">,</span>
                      <span className="text-neutral-200 font-mono">{target.position.y.toFixed(2)}</span>
                      <span className="text-neutral-500">,</span>
                      <span className="text-neutral-200 font-mono">{target.position.z.toFixed(2)}</span>
                    </div>
                    <button
                      onClick={() => onButtonClick("copyCoords", formatCoords(target.position))}
                      className="opacity-0 group-hover:opacity-100 p-1 text-neutral-400 hover:text-green-400 transition-all"
                      title="Copy Coordinates"
                    >
                      <i className="fas fa-copy text-xs" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Special input for heal player action */}
            {target.entityType === "player" && (
              <div className="space-y-2">
                <div className="border-t border-green-500/20 pt-3">
                  <span className="text-neutral-500 text-[10px] font-medium mb-2 block">HEAL AMOUNT</span>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="1"
                      max="200"
                      value={healthAmount}
                      onChange={(e) => setHealthAmount(Math.min(200, Math.max(1, parseInt(e.target.value) || 1)))}
                      className="flex-1 bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border border-neutral-700/50 rounded-lg px-3 py-2 h-8 text-neutral-200 text-sm focus:outline-none focus:border-green-500/50 font-mono"
                    />
                    <span className="text-neutral-500 text-xs">HP</span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons - Fixed height for consistency */}
            <div className="border-t border-green-500/20 pt-3 mt-4">
              <div className="space-y-2">
                {allButtons.map((button) => {
                  // Special handling for heal player button
                  if (button.id === "heal-player") {
                    return (
                      <motion.button
                        key={button.id}
                        onClick={handleHealPlayer}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`
                          w-full flex items-center gap-2 px-3 py-2 h-10 bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border ${getBorderColor(button)} rounded-lg transition-all text-sm
                          text-white hover:bg-gradient-to-r hover:from-neutral-700/50 hover:via-neutral-600/40 hover:to-neutral-700/50 cursor-pointer
                        `}
                      >
                        <i className={`fas ${button.icon} text-xs w-4 text-center ${getButtonColor(button)}`} />
                        <span className="font-medium">{button.label}</span>
                        <span className="text-neutral-400 text-xs ml-auto">({healthAmount} HP)</span>
                      </motion.button>
                    );
                  }

                  // Regular buttons
                  return (
                    <motion.button
                      key={button.id}
                      onClick={() => !button.disabled && onButtonClick(button.action, button.data)}
                      disabled={button.disabled}
                      whileHover={{ scale: button.disabled ? 1 : 1.02 }}
                      whileTap={{ scale: button.disabled ? 1 : 0.98 }}
                      className={`
                        w-full flex items-center gap-2 px-3 py-2 h-10 bg-gradient-to-r from-neutral-900/40 via-neutral-800/30 to-neutral-900/40 border ${getBorderColor(button)} rounded-lg transition-all text-sm
                        ${button.disabled 
                          ? "text-neutral-600 cursor-not-allowed opacity-50" 
                          : "text-white hover:bg-gradient-to-r hover:from-neutral-700/50 hover:via-neutral-600/40 hover:to-neutral-700/50 cursor-pointer"
                        }
                      `}
                    >
                      <i className={`fas ${button.icon} text-xs w-4 text-center ${button.disabled ? 'text-neutral-600' : getButtonColor(button)}`} />
                      <span className="font-medium">{button.label}</span>
                      {button.disabled && <span className="text-neutral-500 text-xs ml-auto">[Disabled]</span>}
                    </motion.button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
});

InfoPanel.displayName = "InfoPanel";

export default InfoPanel;
