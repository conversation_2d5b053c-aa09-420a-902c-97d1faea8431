const path = require('path');

module.exports = {
    entry: {
        client: './client/index.ts',
        server: './server/index.ts',
        shared: './shared/index.ts'
    },
    output: {
        path: path.resolve(__dirname, 'build'),
        filename: '[name].js',
        libraryTarget: 'this'
    },
    resolve: {
        extensions: ['.ts', '.js'],
        alias: {
            '@shared': path.resolve(__dirname, 'shared'),
            '@client': path.resolve(__dirname, 'client'),
            '@server': path.resolve(__dirname, 'server')
        }
    },
    module: {
        rules: [
            {
                test: /\.ts$/,
                use: 'ts-loader',
                exclude: /node_modules/
            }
        ]
    },
    target: 'node',
    mode: 'production',
    optimization: {
        minimize: true
    }
};
