/// <reference types="@citizenfx/client" />
/**
 * Client-side entry point for hm-multicharacter
 */
import { MulticharacterEvents } from '../shared/events';
import { CharacterSelectionData, CharacterCreationData } from '../shared/character.types';
import { MULTICHARACTER_PREVIEW_CONFIG } from '../shared/config';

on('onResourceStop', (resourceName: string) => {
    if (resourceName === GetCurrentResourceName()) {
        console.log('[hm-multicharacter] Resource stopped');
    }
});

console.log('[hm-multicharacter] Client initialized');

// State variables
let isSelectionActive = false;
let currentCharacters: CharacterSelectionData[] = [];
let ghostCharacterModels: number[] = []; // Store ghost character models
let characterModels: number[] = []; // Store regular character models
let selectedCharacterId: string | null = null;
let selectionCam: number | null = null;
let playerPed: number | null = null;
let originalCoords: number[] | null = null;
let routingBucket: number | null = null;

// 3D character interaction state
let characterModelMap: Map<number, { character: CharacterSelectionData, spawnPointIndex: number }> = new Map();
let ghostModelMap: Map<number, { spawnPointIndex: number }> = new Map();
let hoveredCharacterModel: number | null = null;
let hoveredSpawnPointIndex: number | null = null;
let is3DInteractionActive = false;
let interactionTick: number | null = null;

// Debug variables
let showDebugVisuals = false;

// Camera and scene configuration
const cameraConfig = MULTICHARACTER_PREVIEW_CONFIG.interior.camera;
const interiorCoords = MULTICHARACTER_PREVIEW_CONFIG.interior.coords;
const interiorHeading = MULTICHARACTER_PREVIEW_CONFIG.interior.heading;
const spawnPoints = MULTICHARACTER_PREVIEW_CONFIG.spawnPoints;
const defaultModels = MULTICHARACTER_PREVIEW_CONFIG.defaultModels;

/**
 * Initialize the character selection screen
 */
async function initializeCharacterSelection() {

    if (isSelectionActive) {
        console.log('[hm-multicharacter] Selection already active, skipping initialization');
        return;
    }
    isSelectionActive = true;
    ShutdownLoadingScreen();
    ShutdownLoadingScreenNui();

    // Store original player data
    const playerPedId = PlayerPedId();
    originalCoords = [GetEntityCoords(playerPedId)[0], GetEntityCoords(playerPedId)[1], GetEntityCoords(playerPedId)[2]];

    //Set up routing bucket if configured
    if (MULTICHARACTER_PREVIEW_CONFIG.interior.useRoutingBucket) {
        const bucketBase = MULTICHARACTER_PREVIEW_CONFIG.interior.selectionBucketBase || 1000;
        routingBucket = bucketBase + GetPlayerServerId(PlayerId());
        emitNet('hm-multicharacter:server:setRoutingBucket', routingBucket);
    }

    // Disable HUD components
    DisplayRadar(false);
    DisplayHud(false);
    DoScreenFadeOut(500);
    await new Promise(resolve => setTimeout(resolve, 600));

    // Set up interior
    await setupInterior();

    // Create camera
    createSelectionCamera();

    // Request characters from server
    console.log('[hm-multicharacter] Requesting characters from server');
    emitNet(MulticharacterEvents.REQUEST_CHARACTERS);

    // Show NUI - but don't capture mouse for 3D interaction
    SetNuiFocus(true, false); // Focus NUI but keep mouse for game interaction
    // Send a message to show the UI
    SendNUIMessage({
        type: 'showUI',
        config: MULTICHARACTER_PREVIEW_CONFIG
    });

    // Fade in screen
    console.log('[hm-multicharacter] Fading in screen');
    DoScreenFadeIn(500);
}

/**
 * Set up the interior for character selection
 */
async function setupInterior() {
    // Teleport player to interior
    SetEntityCoords(PlayerPedId(), interiorCoords[0], interiorCoords[1], interiorCoords[2], false, false, false, false);
    SetEntityHeading(PlayerPedId(), interiorHeading);

    // Load interior
    if (MULTICHARACTER_PREVIEW_CONFIG.interior.interiorId) {
        const interiorId = MULTICHARACTER_PREVIEW_CONFIG.interior.interiorId;
        LoadInterior(interiorId);
        while (!IsInteriorReady(interiorId)) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // Load IPLs if specified
    // if (MULTICHARACTER_PREVIEW_CONFIG.interior.ipls && MULTICHARACTER_PREVIEW_CONFIG.interior.ipls.length > 0) {
    //     for (const ipl of MULTICHARACTER_PREVIEW_CONFIG.interior.ipls) {
    //         RequestIpl(ipl);
    //     }
    // }

    // Make player invisible
    SetEntityVisible(PlayerPedId(), false, false);
    FreezeEntityPosition(PlayerPedId(), true);
}

/**
 * Create the selection camera
 */
function createSelectionCamera() {
    // Destroy existing camera if it exists
    if (selectionCam !== null) {
        DestroyCam(selectionCam, true);
        selectionCam = null;
    }

    // Create new camera
    selectionCam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true);
    SetCamCoord(selectionCam, cameraConfig.pos[0], cameraConfig.pos[1], cameraConfig.pos[2]);
    SetCamRot(selectionCam, cameraConfig.rot[0], cameraConfig.rot[1], cameraConfig.rot[2], 2);
    SetCamFov(selectionCam, cameraConfig.fov);
    RenderScriptCams(true, false, 0, true, true);
}

/**
 * Get camera position for focusing on a specific character spawn point
 */
function getCameraPositionForCharacter(spawnPointIndex: number): { pos: number[], rot: number[] } {
    const spawnPoint = spawnPoints[spawnPointIndex % spawnPoints.length];
    
    // Calculate camera position to focus on the character
    // Position camera slightly in front and to the side of the character
    const offsetX = 2.0; // Distance in front
    const offsetY = 1.5; // Distance to the side
    const offsetZ = 0.5; // Height offset
    
    const heading = spawnPoint.heading * (Math.PI / 180); // Convert to radians
    
    const camX = spawnPoint.coords[0] + (Math.sin(heading) * offsetX) + (Math.cos(heading) * offsetY);
    const camY = spawnPoint.coords[1] + (Math.cos(heading) * offsetX) - (Math.sin(heading) * offsetY);
    const camZ = spawnPoint.coords[2] + offsetZ;
    
    // Calculate rotation to look at the character
    const lookAtX = spawnPoint.coords[0];
    const lookAtY = spawnPoint.coords[1];
    const lookAtZ = spawnPoint.coords[2] + 0.7; // Look at character's upper body
    
    // Calculate pitch and yaw to look at character
    const deltaX = lookAtX - camX;
    const deltaY = lookAtY - camY;
    const deltaZ = lookAtZ - camZ;
    
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const pitch = Math.atan2(deltaZ, distance) * (180 / Math.PI);
    const yaw = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
    
    return {
        pos: [camX, camY, camZ],
        rot: [pitch, 0.0, yaw - 90.0] // Adjust yaw for proper orientation
    };
}

/**
 * Camera focusing disabled - using 3D interaction system instead
 */
async function focusCameraOnCharacter(spawnPointIndex: number, duration: number = 2000) {
    // Camera transitions disabled to prevent interference with 3D interaction
    console.log(`[hm-multicharacter] Camera transition to character ${spawnPointIndex} skipped (3D interaction mode)`);
}

/**
 * Camera overview return disabled - using 3D interaction system instead
 */
async function returnCameraToOverview(duration: number = 1500) {
    // Camera transitions disabled to prevent interference with 3D interaction
    console.log('[hm-multicharacter] Camera return to overview skipped (3D interaction mode)');
}

async function spawnCharacter(characterData: CharacterSelectionData) {
    console.log('[hm-multicharacter] Spawning character:', JSON.stringify(characterData, null, 2));

    try {
        // Determine model to use based on character gender
        const modelHash = 'hicham';
        RequestModel(GetHashKey(modelHash));
        while (!HasModelLoaded(GetHashKey(modelHash))) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        let playerPed = PlayerPedId();        // Set player model
        SetPlayerModel(playerPed, GetHashKey(modelHash));
        SetModelAsNoLongerNeeded(GetHashKey(modelHash));

        SetPedComponentVariation(playerPed, 0, 0, 0, 2)

        // Use character's last position if available, otherwise use default spawn point
        const spawnPosition = characterData.position && characterData.position.x !== 0 && characterData.position.y !== 0 
            ? characterData.position 
            : { 
                x: MULTICHARACTER_PREVIEW_CONFIG.spawnPoints[0].coords[0], 
                y: MULTICHARACTER_PREVIEW_CONFIG.spawnPoints[0].coords[1], 
                z: MULTICHARACTER_PREVIEW_CONFIG.spawnPoints[0].coords[2], 
                heading: 0.0 
              };

        console.log('[hm-multicharacter] Spawning at position:', spawnPosition);
        
        SetEntityCoords(playerPed, spawnPosition.x, spawnPosition.y, spawnPosition.z, false, false, false, false);
        SetEntityHeading(playerPed, spawnPosition.heading || 0.0);
        SetEntityHealth(playerPed, 200);
        SetPedArmour(playerPed, 0);
        console.log('[hm-multicharacter] Player position and stats set');

        // Make sure player is visible and not frozen
        SetEntityVisible(playerPed, true, false);
        FreezeEntityPosition(playerPed, false);
        console.log('[hm-multicharacter] Player visibility and movement restored');

        emitNet('hm-core:playerLoaded', { 
            characterId: characterData.id,
            stateid: characterData.stateid,
            characterData: characterData 
        });

        console.log('[hm-multicharacter] Character spawned successfully');
    } catch (error) {
        console.error('[hm-multicharacter] Error spawning character:', error);
        // Fallback to ensure player isn't stuck in a black screen
        SetEntityVisible(PlayerPedId(), true, false);
        FreezeEntityPosition(PlayerPedId(), false);
        DisplayRadar(true);
        DisplayHud(true);
    }
}

/**
 * Clean up all character models (both regular and ghost)
 */
function cleanupAllCharacterModels() {
    // Delete all regular character models
    if (characterModels.length > 0) {
        for (const characterModel of characterModels) {
            if (characterModel !== null && characterModel !== undefined && DoesEntityExist(characterModel)) {
                DeleteEntity(characterModel);
                console.log(`[hm-multicharacter] Deleted character model: ${characterModel}`);
            }
        }
        characterModels = [];
    }

    // Delete all ghost character models
    if (ghostCharacterModels.length > 0) {
        for (const ghostModel of ghostCharacterModels) {
            if (ghostModel !== null && ghostModel !== undefined && DoesEntityExist(ghostModel)) {
                DeleteEntity(ghostModel);
                console.log(`[hm-multicharacter] Deleted ghost character model: ${ghostModel}`);
            }
        }
        ghostCharacterModels = [];
    }

    // Clean up the old playerPed if it still exists
    if (playerPed !== null) {
        if (DoesEntityExist(playerPed)) {
            DeleteEntity(playerPed);
        }
        playerPed = null;
    }

    // Clear 3D interaction maps
    characterModelMap.clear();
    ghostModelMap.clear();

    console.log('[hm-multicharacter] All character models cleaned up');
}

/**
 * Clean up the character selection screen
 */
async function cleanupCharacterSelection() {
    if (!isSelectionActive) return;
    isSelectionActive = false;

    // Fade out screen
    console.log('[hm-multicharacter] Fading out screen');
    DoScreenFadeOut(500);
    await new Promise(resolve => setTimeout(resolve, 600));

    // Stop 3D interaction system
    stop3DInteraction();

    // Destroy camera
    if (selectionCam !== null) {
        DestroyCam(selectionCam, true);
        selectionCam = null;
        RenderScriptCams(false, false, 0, true, true);
    }

    // Clean up all character models
    cleanupAllCharacterModels();

    // Reset routing bucket if used
    if (routingBucket !== null) {
        emitNet('hm-multicharacter:server:resetRoutingBucket');
        routingBucket = null;
    }

    // Make sure player is visible and not frozen
    SetEntityVisible(PlayerPedId(), true, false);
    FreezeEntityPosition(PlayerPedId(), false);

    // Enable HUD components
    DisplayRadar(true);
    DisplayHud(true);

    // Disable NUI focus
    SetNuiFocus(false, false);

    // Close NUI completely (unmount and clean up)
    SendNUIMessage({ type: 'closeUI' });

    // Force a small delay to ensure NUI cleanup completes
    await new Promise(resolve => setTimeout(resolve, 100));

    // Send a second closeUI message to ensure it's processed
    SendNUIMessage({ type: 'closeUI' });

    console.log('[hm-multicharacter] NUI closed and selected character:', selectedCharacterId);

    // Make sure we have a valid character selected
    if (selectedCharacterId !== null) {
        // Find the character with the matching stateid
        const selectedCharacter = currentCharacters.find(char => char.stateid === selectedCharacterId);
        if (selectedCharacter) {
            console.log('[hm-multicharacter] Found character to spawn:', selectedCharacter);
            // Spawn the selected character
            await spawnCharacter(selectedCharacter);
        } else {
            console.error('[hm-multicharacter] No character found with stateid:', selectedCharacterId);
            console.error('[hm-multicharacter] Available characters:', currentCharacters.map(c => c.stateid));
            // Fallback to ensure player isn't stuck in a black screen
            if (originalCoords) {
                SetEntityCoords(
                    PlayerPedId(),
                    originalCoords[0],
                    originalCoords[1],
                    originalCoords[2],
                    false, false, false, false
                );
            }
        }
    } else {
        console.error('[hm-multicharacter] No valid character selected for spawning');
        // Fallback to ensure player isn't stuck in a black screen
        if (originalCoords) {
            SetEntityCoords(
                PlayerPedId(),
                originalCoords[0],
                originalCoords[1],
                originalCoords[2],
                false, false, false, false
            );
        }
    }
    //reset bucket
    if (routingBucket !== null) {
        emitNet('hm-multicharacter:server:resetRoutingBucket');
        routingBucket = null;
    }

    // Force another small delay to ensure everything is cleaned up
    await new Promise(resolve => setTimeout(resolve, 100));

    // Ensure NUI focus is disabled again
    SetNuiFocus(false, false);

    // Fade screen back in
    DoScreenFadeIn(500);
    console.log('[hm-multicharacter] Character selection cleanup complete');
}

// spawn ghost character model
async function spawnGhostCharacterModel(index: number) {
    // Get spawn point
    const spawnPoint = spawnPoints[index % spawnPoints.length];
    // Use a default ghost model, replace with your own
    const modelHash = defaultModels[Math.floor(Math.random() * defaultModels.length)];
    RequestModel(modelHash);
    while (!HasModelLoaded(modelHash)) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    console.log(`[hm-multicharacter] Ghost model ${modelHash} loaded successfully`);
    
    // Create the ghost ped
    const ghostPed = CreatePed(4, modelHash, spawnPoint.coords[0], spawnPoint.coords[1], spawnPoint.coords[2], spawnPoint.heading, false, true);
    
    // Add to ghostCharacterModels array
    ghostCharacterModels.push(ghostPed);
    
    // Add to ghost model map for 3D interaction
    ghostModelMap.set(ghostPed, { spawnPointIndex: index });
    
    FreezeEntityPosition(ghostPed, true);
    SetEntityInvincible(ghostPed, true);
    SetBlockingOfNonTemporaryEvents(ghostPed, true);
    // Set ghost alpha
    SetEntityAlpha(ghostPed, 160, false); // Semi-transparent ghost
    SetEntityVisible(ghostPed, true, false);
    SetEntityAsNoLongerNeeded(ghostPed);
    
    // Apply animation if specified
    if (spawnPoint.animation) {
        if (spawnPoint.animation.includes('@')) {
            // It's an animation dict + anim name
            const [dict, anim] = spawnPoint.animation.split('@');
            RequestAnimDict(dict);
            while (!HasAnimDictLoaded(dict)) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            TaskPlayAnim(ghostPed, dict, anim, 8.0, 1.0, -1, 1, 0, false, false, false);
        } else {
            // It's a scenario
            TaskStartScenarioInPlace(ghostPed, spawnPoint.animation, 0, true);
        }
    }
    console.log(`[hm-multicharacter] Spawned ghost character model at index ${index}`);
}

RegisterCommand('closeui', async () => {
    // Only run cleanup if selection is active
    if (isSelectionActive && selectedCharacterId !== null) {
        cleanupCharacterSelection();
    } else {
        // Just close the UI without full cleanup
        SendNUIMessage({ type: 'closeUI' });

        // Force a small delay to ensure NUI cleanup completes
        await new Promise(resolve => setTimeout(resolve, 100));

        // Send a second closeUI message to ensure it's processed
        SendNUIMessage({ type: 'closeUI' });

        // Ensure NUI focus is disabled
        SetNuiFocus(false, false);
    }
}, false);

RegisterCommand('showui', () => {
    SendNUIMessage({ type: 'closeUI' });
    SetNuiFocus(false, false);
}, false);

/**
 * Spawn a character model for preview
 * @param characterData Character data to preview
 * @param spawnPointIndex Index of spawn point to use
 */
async function spawnCharacterModel(characterData: CharacterSelectionData, spawnPointIndex: number) {
    // Get spawn point
    const spawnPoint = spawnPoints[spawnPointIndex % spawnPoints.length];

    // Determine model to use
    let modelHash: string | number;

    // If we have appearance data, use that model
    if (characterData && characterData.gender) {
        modelHash = 'hicham';
    } else {
        // Otherwise use a default model
        modelHash = defaultModels[Math.floor(Math.random() * defaultModels.length)];
    }

    // Request the model
    RequestModel(modelHash);
    while (!HasModelLoaded(modelHash)) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Create the ped
    const characterPed = CreatePed(4, modelHash, spawnPoint.coords[0], spawnPoint.coords[1], spawnPoint.coords[2], spawnPoint.heading, false, true);

    // Add to characterModels array to track it
    characterModels.push(characterPed);

    // Add to character model map for 3D interaction
    characterModelMap.set(characterPed, { character: characterData, spawnPointIndex: spawnPointIndex });

    // Set ped properties
    FreezeEntityPosition(characterPed, true);
    SetEntityInvincible(characterPed, true);
    SetBlockingOfNonTemporaryEvents(characterPed, true);

    // Apply animation
    if (spawnPoint.animation) {
        if (spawnPoint.animation.includes('@')) {
            // It's an animation dict + anim name
            const [dict, anim] = spawnPoint.animation.split('@');
            RequestAnimDict(dict);
            while (!HasAnimDictLoaded(dict)) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            TaskPlayAnim(characterPed, dict, anim, 8.0, 1.0, -1, 1, 0, false, false, false);
        } else {
            // It's a scenario
            TaskStartScenarioInPlace(characterPed, spawnPoint.animation, 0, true);
        }
    }

   // TODO: Apply appearance data if available
   // This would require implementing the appearance system
   
   console.log(`[hm-multicharacter] Spawned character model for ${characterData.first_name} ${characterData.last_name} at index ${spawnPointIndex}`);
}

onNet(MulticharacterEvents.RECEIVE_CHARACTERS, (data: { characters: CharacterSelectionData[] }) => {
    currentCharacters = data.characters;
    SendNUIMessage({
        type: 'updateCharacters',
        characters: currentCharacters
    });
    
    // Clean up any existing character models before spawning new ones
    cleanupAllCharacterModels();
    
    if (currentCharacters.length > 0) {
        currentCharacters.forEach((character, index) => {
            spawnCharacterModel(character, index);
        });
        // spawn the rest of available character slots as ghost characters with alpha
        for (let i = currentCharacters.length; i < spawnPoints.length; i++) {
            spawnGhostCharacterModel(i);
        }
    } else if (currentCharacters.length < spawnPoints.length) {
        // If no characters, spawn ghost characters for available slots
        for (let i = 0; i < spawnPoints.length; i++) {
            spawnGhostCharacterModel(i);
        }
    } else {
        SendNUIMessage({
            type: 'updateCharacters',
            characters: []
        });
    }
    
    // Start 3D interaction system after models are spawned
    setTimeout(() => {
        start3DInteraction();
    }, 1000); // Small delay to ensure all models are properly created
});

onNet(MulticharacterEvents.CHARACTER_SELECTED, (data: { success: boolean, message: string, stateid: string }) => {
    console.log('[hm-multicharacter] Final Character selected from server');
    if (data.success) {
        console.log('Character selection successful, stateid:', data.stateid);
        // The cleanup and spawning will be handled by the NUI callback
    } else {
        // Character selection failed, show error
        SendNUIMessage({
            type: 'error',
            message: data.message
        });
    }
});

onNet(MulticharacterEvents.CHARACTER_CREATED, (data: { success: boolean, message: string, character: CharacterSelectionData | null }) => {
    if (data.success && data.character) {
        // Add new character to list
        currentCharacters.push(data.character);

        // Update NUI
        SendNUIMessage({
            type: 'characterCreated',
            success: true,
            message: data.message,
            character: data.character
        });

        // Also update the character list
        SendNUIMessage({
            type: 'updateCharacters',
            characters: currentCharacters
        });

        // Clean up all models and respawn them
        cleanupAllCharacterModels();
        
        // Respawn all characters
        currentCharacters.forEach((character, index) => {
            spawnCharacterModel(character, index);
        });
        
        // Spawn ghost characters for remaining slots
        for (let i = currentCharacters.length; i < spawnPoints.length; i++) {
            spawnGhostCharacterModel(i);
        }
        
        // Start 3D interaction system after models are spawned
        setTimeout(() => {
            start3DInteraction();
        }, 1000);
    } else {
        // Character creation failed, show error
        SendNUIMessage({
            type: 'error',
            message: data.message
        });
    }
});

onNet(MulticharacterEvents.CHARACTER_DELETED, (data: { success: boolean, message: string, stateid: string }) => {
    if (data.success) {
        // Remove character from list
        const index = currentCharacters.findIndex(c => c.stateid === data.stateid);
        if (index !== -1) {
            currentCharacters.splice(index, 1);

            // Send character deleted message
            SendNUIMessage({
                type: 'characterDeleted',
                success: true,
                message: data.message,
                stateid: data.stateid
            });

            // Update character list
            SendNUIMessage({
                type: 'updateCharacters',
                characters: currentCharacters
            });

            // Clean up all models and respawn them
            cleanupAllCharacterModels();
            
            // Respawn remaining characters
            currentCharacters.forEach((character, idx) => {
                spawnCharacterModel(character, idx);
            });
            
            // Spawn ghost characters for remaining slots
            for (let i = currentCharacters.length; i < spawnPoints.length; i++) {
                spawnGhostCharacterModel(i);
            }
            
            // Start 3D interaction system after models are spawned
            setTimeout(() => {
                start3DInteraction();
            }, 1000);
        }
    } else {
        // Character deletion failed, show error
        SendNUIMessage({
            type: 'error',
            message: data.message
        });
    }
});

// NUI callbacks
RegisterNuiCallback(MulticharacterEvents.NUI_READY, (_data: any, cb: (response: any) => void) => {
    console.log('[hm-multicharacter] NUI ready callback received');
    initializeCharacterSelection();
    cb({
        status: 'ok'
    });
});

// Character hover callback - disabled for 3D interaction
RegisterNuiCallback(MulticharacterEvents.NUI_CHARACTER_HOVER, (data: { stateid: string, spawnPointIndex: number }, cb: (response: any) => void) => {
    console.log(`[hm-multicharacter] UI Character hover: ${data.stateid} at spawn point ${data.spawnPointIndex} (camera transition disabled)`);
    
    // Camera transition disabled for 3D interaction system
    
    cb({
        status: 'ok'
    });
});

// Character hover end callback - disabled for 3D interaction
RegisterNuiCallback(MulticharacterEvents.NUI_CHARACTER_HOVER_END, (_data: any, cb: (response: any) => void) => {
    console.log('[hm-multicharacter] UI Character hover ended (camera transition disabled)');
    
    // Camera transition disabled for 3D interaction system
    
    cb({
        status: 'ok'
    });
});

RegisterNuiCallback(MulticharacterEvents.NUI_SELECT_CHARACTER, (data: { stateid: string }, cb: (response: any) => void) => {
    selectedCharacterId = data.stateid;
    console.log(`[hm-multicharacter] UI Selected character: ${data.stateid}`);
    
    // Verify the character exists in our current list
    const selectedCharacter = currentCharacters.find(char => char.stateid === data.stateid);
    if (selectedCharacter) {
        emitNet(MulticharacterEvents.SELECT_CHARACTER, { stateid: data.stateid });
        console.log('[hm-multicharacter] Character selection request sent to server');
    } else {
        console.error('[hm-multicharacter] Selected character not found in current list:', data.stateid);
        SendNUIMessage({
            type: 'error',
            message: 'Selected character not found'
        });
    }
    
    cb({
        status: 'ok'
    });
});

RegisterNuiCallback(MulticharacterEvents.NUI_CREATE_CHARACTER, (data: { characterData: CharacterCreationData }, cb: (response: any) => void) => {
    emitNet(MulticharacterEvents.CREATE_CHARACTER, { characterData: data.characterData });
    cb({
        status: 'ok'
    });
});

RegisterNuiCallback(MulticharacterEvents.NUI_DELETE_CHARACTER, (data: { stateid: string }, cb: (response: any) => void) => {
    emitNet(MulticharacterEvents.DELETE_CHARACTER, { stateid: data.stateid });
    cb({
        status: 'ok'
    });
});

RegisterNuiCallback(MulticharacterEvents.NUI_CLOSE, (_data: any, cb: (response: any) => void) => {
    // Only allow closing if we have selected a character
    if (selectedCharacterId !== null) {
        cleanupCharacterSelection();
    } else {
        SendNUIMessage({
            type: 'error',
            message: 'You must select a character first'
        });
    }
    cb({
        status: 'ok'
    });
});

// Add play character callback
RegisterNuiCallback(MulticharacterEvents.NUI_PLAY_CHARACTER, (data: { stateid: string }, cb: (response: any) => void) => {
    console.log(`[hm-multicharacter] Clicked play for character stateid: ${data.stateid}`);
    
    // Immediately start fade-out for smooth transition
    console.log('[hm-multicharacter] Starting fade-out for character selection');
    DoScreenFadeOut(500);
    
    // Verify the character exists in our current list
    const selectedCharacter = currentCharacters.find(char => char.stateid === data.stateid);
    if (selectedCharacter) {
        selectedCharacterId = data.stateid;
        
        // Immediate cleanup of character models when play is clicked
        console.log('[hm-multicharacter] Performing immediate cleanup for character play');
        cleanupAllCharacterModels();
        
        // Send selection to server first
        emitNet(MulticharacterEvents.SELECT_CHARACTER, { stateid: data.stateid });
        
        // Close the UI after a short delay to allow server processing and fade-out
        setTimeout(() => {
            cleanupCharacterSelection();
        }, 600); // Increased delay to allow fade-out to complete
        
        console.log('[hm-multicharacter] Character play request sent, cleaning up UI');
    } else {
        console.error('[hm-multicharacter] Character not found for play request:', data.stateid);
        SendNUIMessage({
            type: 'error',
            message: 'Character not found'
        });
    }

    cb({
        status: 'ok'
    });
});

on('hm-core:client:ready', () => {
    console.log('[hm-multicharacter] Core client ready event received');
    initializeCharacterSelection();
});

// Cleanup when resource stops
on('onResourceStop', (resourceName: string) => {
    if (resourceName !== GetCurrentResourceName()) return;
    
    console.log('[hm-multicharacter] Resource stopping, cleaning up...');
    
    // Stop 3D Interaction System
    stop3DInteraction();
    
    // Destroy camera
    if (selectionCam !== null) {
        DestroyCam(selectionCam, true);
        selectionCam = null;
        RenderScriptCams(false, false, 0, true, true);
    }
    
    // Clean up all character models
    cleanupAllCharacterModels();
    
    // Reset routing bucket if used
    if (routingBucket !== null) {
        emitNet('hm-multicharacter:server:resetRoutingBucket');
        routingBucket = null;
    }
    
    // Make sure player is visible and not frozen
    SetEntityVisible(PlayerPedId(), true, false);
    FreezeEntityPosition(PlayerPedId(), false);
    
    // Enable HUD components
    DisplayRadar(true);
    DisplayHud(true);
    
    // Disable NUI focus
    SetNuiFocus(false, false);
    
    // Reset state
    isSelectionActive = false;
    selectedCharacterId = null;
    currentCharacters = [];
    originalCoords = null;
    
    console.log('[hm-multicharacter] Resource cleanup complete');
});

/**
 * 3D Character Interaction System
 */

/**
 * Start the 3D character interaction system
 */
function start3DInteraction() {
    if (is3DInteractionActive || !isSelectionActive) {
        return;
    }
    is3DInteractionActive = true;
    // Start the interaction tick
    interactionTick = setTick(() => {
        if (!isSelectionActive || !is3DInteractionActive) return;
        
        // Draw debug ray visualization if enabled
        if (showDebugVisuals) {
            drawDebugRay();
        }
        
        // Draw outline for hovered character
        if (hoveredCharacterModel !== null && DoesEntityExist(hoveredCharacterModel)) {
            drawCharacterOutline(hoveredCharacterModel);
        }
        
        // Cast ray from camera to detect character models
        const hit = castRayFromMouseCursor();
        
        if (hit.hit && hit.entity) {
            // Debug visualization for hit entity when debugging is enabled
            if (showDebugVisuals && hit.coords) {
                DrawLine(
                    hit.coords[0], hit.coords[1], hit.coords[2],
                    hit.coords[0], hit.coords[1], hit.coords[2] + 2.0,
                    0, 255, 0, 255 // Green line pointing up
                );
            }
            handleCharacterModelHover(hit.entity);
        } else {
            handleNoCharacterHover();
        }
        
        // Handle click detection
        if (IsControlJustPressed(0, 24)) { // Left mouse button
            handleCharacterModelClick();
        }
    });
    
}

/**
 * Stop the 3D character interaction system
 */
function stop3DInteraction() {
    if (!is3DInteractionActive) return;
    
    is3DInteractionActive = false;
    console.log('[hm-multicharacter] Stopping 3D character interaction system');
    
    // Clear the interaction tick
    if (interactionTick !== null) {
        clearTick(interactionTick);
        interactionTick = null;
    }
    
    // Reset any visual effects
    resetCharacterVisualEffects();
    hoveredCharacterModel = null;
    hoveredSpawnPointIndex = null;
}

/**
 * Cast a ray from the camera based on mouse cursor position
 */
function castRayFromMouseCursor(): { hit: boolean, entity: number | null, coords: number[] | null } {
    try {
        // Get mouse cursor position on screen
        const [mouseX, mouseY] = GetNuiCursorPosition();
        
        // Get screen resolution
        const [screenX, screenY] = GetActiveScreenResolution();
        
        // Convert mouse position to normalized screen coordinates (-1 to 1)
        const normalizedX = (mouseX / screenX) * 2.0 - 1.0;
        const normalizedY = -((mouseY / screenY) * 2.0 - 1.0); // Flip Y axis
        
        // Get camera info
        const camera = selectionCam || GetRenderingCam();
        const camCoords = GetCamCoord(camera);
        const camRot = GetCamRot(camera, 2);
        
        // Get camera FOV
        const fov = GetCamFov(camera);
        const aspectRatio = screenX / screenY;
        
        // Calculate the ray direction using camera rotation and mouse position
        const fovRad = fov * (Math.PI / 180);
        const halfHeight = Math.tan(fovRad / 2);
        const halfWidth = halfHeight * aspectRatio;
        
        // Convert camera rotation to radians
        const pitch = camRot[0] * (Math.PI / 180);
        const roll = camRot[1] * (Math.PI / 180);
        const yaw = camRot[2] * (Math.PI / 180);
        
        // Calculate camera's forward, right, and up vectors
        const cosPitch = Math.cos(pitch);
        const sinPitch = Math.sin(pitch);
        const cosYaw = Math.cos(yaw);
        const sinYaw = Math.sin(yaw);
        
        const forward = [-sinYaw * cosPitch, cosYaw * cosPitch, sinPitch];
        const right = [cosYaw, sinYaw, 0];
        const up = [sinYaw * sinPitch, -cosYaw * sinPitch, cosPitch];
        
        // Calculate ray direction based on mouse position
        const rayDirection = [
            forward[0] + (normalizedX * halfWidth * right[0]) + (normalizedY * halfHeight * up[0]),
            forward[1] + (normalizedX * halfWidth * right[1]) + (normalizedY * halfHeight * up[1]),
            forward[2] + (normalizedX * halfWidth * right[2]) + (normalizedY * halfHeight * up[2])
        ];
        
        // Normalize the direction vector
        const length = Math.sqrt(rayDirection[0] * rayDirection[0] + rayDirection[1] * rayDirection[1] + rayDirection[2] * rayDirection[2]);
        const normalizedDirection = [
            rayDirection[0] / length,
            rayDirection[1] / length,
            rayDirection[2] / length
        ];
        
        // Calculate end point
        const rayDistance = 100.0;
        const endCoords = [
            camCoords[0] + normalizedDirection[0] * rayDistance,
            camCoords[1] + normalizedDirection[1] * rayDistance,
            camCoords[2] + normalizedDirection[2] * rayDistance
        ];
        
        // Cast the ray
        const rayHandle = StartShapeTestRay(
            camCoords[0], camCoords[1], camCoords[2],
            endCoords[0], endCoords[1], endCoords[2],
            -1, // Hit everything
            PlayerPedId(), // Ignore player ped
            7 // Use more detailed ray test
        );
        
        // Get result
        const [status, hit, hitCoords, surfaceNormal, entityHit] = GetShapeTestResult(rayHandle);
    
        
        return {
            hit: hit === 1,
            entity: (hit === 1 && entityHit && typeof entityHit === 'number') ? entityHit : null,
            coords: hit === 1 && hitCoords ? [hitCoords[0], hitCoords[1], hitCoords[2]] : null
        };
    } catch (error) {
        console.error('[ERROR] Mouse ray casting error:', error);
        return {
            hit: false,
            entity: null,
            coords: null
        };
    }
}

/**
 * Draw debug ray visualization to show what the ray casting is detecting
 */
function drawDebugRay() {
    if (!selectionCam) return;
    
    try {
        // Get mouse cursor position
        const [mouseX, mouseY] = GetNuiCursorPosition();
        const [screenX, screenY] = GetActiveScreenResolution();
        
        // Get camera info
        const camera = selectionCam;
        const camCoords = GetCamCoord(camera);
        const camRot = GetCamRot(camera, 2);
        
        // Convert mouse position to normalized screen coordinates
        const normalizedX = (mouseX / screenX) * 2.0 - 1.0;
        const normalizedY = -((mouseY / screenY) * 2.0 - 1.0);
        
        // Calculate ray direction (simplified version of the main ray cast)
        const fov = GetCamFov(camera);
        const aspectRatio = screenX / screenY;
        const fovRad = fov * (Math.PI / 180);
        const halfHeight = Math.tan(fovRad / 2);
        const halfWidth = halfHeight * aspectRatio;
        
        const pitch = camRot[0] * (Math.PI / 180);
        const yaw = camRot[2] * (Math.PI / 180);
        const cosPitch = Math.cos(pitch);
        const sinPitch = Math.sin(pitch);
        const cosYaw = Math.cos(yaw);
        const sinYaw = Math.sin(yaw);
        
        const forward = [-sinYaw * cosPitch, cosYaw * cosPitch, sinPitch];
        const right = [cosYaw, sinYaw, 0];
        const up = [sinYaw * sinPitch, -cosYaw * sinPitch, cosPitch];
        
        const rayDirection = [
            forward[0] + (normalizedX * halfWidth * right[0]) + (normalizedY * halfHeight * up[0]),
            forward[1] + (normalizedX * halfWidth * right[1]) + (normalizedY * halfHeight * up[1]),
            forward[2] + (normalizedX * halfWidth * right[2]) + (normalizedY * halfHeight * up[2])
        ];
        
        // Normalize direction
        const length = Math.sqrt(rayDirection[0] * rayDirection[0] + rayDirection[1] * rayDirection[1] + rayDirection[2] * rayDirection[2]);
        const normalizedDirection = [rayDirection[0] / length, rayDirection[1] / length, rayDirection[2] / length];
        
        // Draw debug ray
        const rayDistance = 10.0;
        const endCoords = [
            camCoords[0] + normalizedDirection[0] * rayDistance,
            camCoords[1] + normalizedDirection[1] * rayDistance,
            camCoords[2] + normalizedDirection[2] * rayDistance
        ];
        
        // Draw red line for the ray
        DrawLine(
            camCoords[0], camCoords[1], camCoords[2],
            endCoords[0], endCoords[1], endCoords[2],
            255, 0, 0, 255 // Red color
        );
        
        // Draw small sphere at mouse ray direction
        DrawSphere(endCoords[0], endCoords[1], endCoords[2], 0.1, 255, 255, 0, 150);
    } catch (error) {
        console.error('[ERROR] Debug ray drawing error:', error);
    }
}

/**
 * Handle hovering over a character model
 */
function handleCharacterModelHover(entity: number) {
    // Check if this entity is a character model we're tracking
    const characterData = characterModelMap.get(entity);
    const ghostData = ghostModelMap.get(entity);
    
    // DEBUG: Always log what we're checking
    
    if (characterData) {
        // Hovering over a regular character
        if (hoveredCharacterModel !== entity) {
            
            // Reset previous hover effects
            resetCharacterVisualEffects();
            
            // Set new hover state
            hoveredCharacterModel = entity;
            hoveredSpawnPointIndex = characterData.spawnPointIndex;
            
            // Apply visual effects
            applyCharacterHoverEffect(entity);
            
            // Update UI to show this character as hovered
            SendNUIMessage({
                type: 'character3DHover',
                stateid: characterData.character.stateid,
                spawnPointIndex: characterData.spawnPointIndex
            });
        }
        
        // Character hovered - no additional processing needed for clean 3D interaction
    } else if (ghostData) {
        // Ghost character detected but hover interaction disabled
        // Don't set hover state for ghost characters - treat as no hover
        handleNoCharacterHover();
    } else {
        // DEBUG: Entity is not in our tracking maps
        // Treat as no hover
        handleNoCharacterHover();
    }
}

/**
 * Handle when not hovering over any character
 */
function handleNoCharacterHover() {
    if (hoveredCharacterModel !== null) {
        console.log('[hm-multicharacter] 3D Hover ended');
        
        // Disable outline for the previously hovered character
        if (DoesEntityExist(hoveredCharacterModel)) {
            SetEntityDrawOutline(hoveredCharacterModel, false);
        }
        
        // Reset visual effects
        resetCharacterVisualEffects();
        
        // Update UI
        SendNUIMessage({
            type: 'character3DHoverEnd'
        });
        
        hoveredCharacterModel = null;
        hoveredSpawnPointIndex = null;
    }
}

/**
 * Handle clicking on a character model
 */
function handleCharacterModelClick() {
    if (hoveredCharacterModel === null) return;
    
    const characterData = characterModelMap.get(hoveredCharacterModel);
    
    if (characterData) {
        console.log(`[hm-multicharacter] 3D Clicked on character: ${characterData.character.first_name} ${characterData.character.last_name}`);
        
        // Immediately start fade-out for smooth transition
        console.log('[hm-multicharacter] Starting fade-out for character selection');
        DoScreenFadeOut(500);
        
        // Select this character
        selectedCharacterId = characterData.character.stateid;
        
        // Update UI to show selection
        SendNUIMessage({
            type: 'character3DSelected',
            stateid: characterData.character.stateid,
            spawnPointIndex: characterData.spawnPointIndex
        });
        
        // Add a brief delay then start the character (allow fade-out to complete)
        setTimeout(() => {
            console.log('[hm-multicharacter] Starting selected character from 3D interaction');
            
            // Immediate cleanup of character models
            cleanupAllCharacterModels();
            
            // Send selection to server
            emitNet(MulticharacterEvents.SELECT_CHARACTER, { stateid: characterData.character.stateid });
            
            // Close the UI
            setTimeout(() => {
                cleanupCharacterSelection();
            }, 100); // Reduced delay since fade-out already started
        }, 600); // Increased delay to allow fade-out to complete
        
    } else {
        // Ghost character detected but click interaction disabled
        const ghostData = ghostModelMap.get(hoveredCharacterModel);
        if (ghostData) {
            console.log(`[hm-multicharacter] 3D Clicked on ghost character at spawn point ${ghostData.spawnPointIndex} - click disabled for ghosts`);
            // Don't trigger character creation for ghost clicks
        }
    }
}

/**
 * Apply visual effects when hovering over a character
 */
function applyCharacterHoverEffect(entity: number) {
    if (!DoesEntityExist(entity)) return;
    
    // Increase alpha for better visibility when hovered
    SetEntityAlpha(entity, 255, false);
    
    // Add a subtle upward animation to make the character more prominent
    const originalZ = GetEntityCoords(entity)[2];
    const hoverOffset = Math.sin(GetGameTimer() * 0.005) * 0.05; // Subtle floating effect
    
    // The outline will be drawn separately in the interaction tick
    // This function focuses on entity-level effects
}

/**
 * Reset all character visual effects
 */
function resetCharacterVisualEffects() {
    // Reset all character models to default alpha and disable outlines
    characterModels.forEach(model => {
        if (DoesEntityExist(model)) {
            SetEntityAlpha(model, 255, false);
            SetEntityDrawOutline(model, false); // Disable entity outline
        }
    });
    
    // Reset ghost character models to transparent and disable outlines
    ghostCharacterModels.forEach(model => {
        if (DoesEntityExist(model)) {
            SetEntityAlpha(model, 160, false);
            SetEntityDrawOutline(model, false); // Disable entity outline
        }
    });
}

/**
 * Draw an outline around a character entity for better hover feedback
 */
function drawCharacterOutline(entity: number) {
    if (!DoesEntityExist(entity)) return;
    
    // Use FiveM's built-in entity outline system
    // Set entity outline with cyan/blue color and pulsing effect
    const pulseAlpha = Math.floor(150 + Math.sin(GetGameTimer() * 0.01) * 50);
    
    // Apply entity outline (RGB: 0, 200, 255 with pulsing alpha)
    SetEntityDrawOutline(entity, true);
    SetEntityDrawOutlineColor(0, 200, 255, pulseAlpha);
    SetEntityDrawOutlineShader(1); // Use outline shader for better visibility
}

