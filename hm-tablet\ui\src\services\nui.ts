// NUI Communication Service
type NuiMessageData = {
  action: string;
  data?: any;
};

// Send a message to the FiveM client
export function sendNuiMessage(action: string, data?: any): void {
  if (window.invokeNative) {
    window.invokeNative('sendNuiMessage', JSON.stringify({ action, data }));
  } else {
    console.log('[DEV] NUI Message:', { action, data });
  }
}

// Setup NUI event listeners
export function setupNuiListener(): void {
  window.addEventListener('message', (event) => {
    const { action, data } = event.data as NuiMessageData;
    
    switch (action) {
      case 'tablet:show':
        document.body.style.display = 'block';
        break;
        
      case 'tablet:hide':
        document.body.style.display = 'none';
        break;
        
      case 'tablet:toggle':
        document.body.style.display = document.body.style.display === 'none' ? 'block' : 'none';
        break;
        
      default:
        console.log('[Tablet] Unknown NUI action:', action, data);
    }
  });

  // Send ready signal to FiveM
  sendNuiMessage('tablet:ready');
}

// Development mode helpers
export function setDevMode(enabled: boolean): void {
  if (enabled) {
    document.body.style.display = 'block';
    document.body.style.background = '#1a1a1a';
  }
}

// Check if running in development mode
export function isDevMode(): boolean {
  return !window.invokeNative;
}

// Global type extension
declare global {
  interface Window {
    invokeNative?: (natives: string, data: string) => void;
  }
}
