/**
 * HM Weapons - Weapon Benches Client
 * 
 * Handles initialization and registration of weapon benches with hm-target
 */

/// <reference types="@citizenfx/client" />

import { 
  WEAPON_BENCHES, 
  WEAPON_BENCH_CONFIG, 
  getEnabledWeaponBenches,
  canAccessWeaponBench,
  type WeaponBench 
} from '../shared/weapon-benches-config';

/**
 * Weapon Benches Manager
 */
export class WeaponBenchesManager {
  private static instance: WeaponBenchesManager;
  private initializedBenches: Set<string> = new Set();
  private spawnedProps: Map<string, number> = new Map();

  public static getInstance(): WeaponBenchesManager {
    if (!WeaponBenchesManager.instance) {
      WeaponBenchesManager.instance = new WeaponBenchesManager();
    }
    return WeaponBenchesManager.instance;
  }
  /**
   * Initialize all weapon benches
   */
  public async initializeWeaponBenches(): Promise<void> {
    const enabledBenches = getEnabledWeaponBenches();
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Initializing ${enabledBenches.length} weapon benches...`);
    }

    // Group benches by model for efficient registration
    const benchesByModel = new Map<string, WeaponBench[]>();
    
    for (const bench of enabledBenches) {
      if (bench.autoSpawn) {
        await this.spawnBenchProp(bench);
      }
      
      if (!benchesByModel.has(bench.model)) {
        benchesByModel.set(bench.model, []);
      }
      benchesByModel.get(bench.model)!.push(bench);
      
      this.initializedBenches.add(bench.id);
    }

    // Register targets for each model (once per model)
    for (const [model, benches] of benchesByModel) {
      await this.registerModelTargets(model, benches);
    }

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Successfully initialized ${this.initializedBenches.size} weapon benches`);
    }
  }
  /**
   * Initialize a single weapon bench
   */
  private async initializeBench(bench: WeaponBench): Promise<void> {
    try {
      // Spawn prop if auto-spawn is enabled
      if (bench.autoSpawn) {
        await this.spawnBenchProp(bench);
      }

      // Register with hm-target using model targeting
      await this.registerBenchTarget(bench);

      this.initializedBenches.add(bench.id);

      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Initialized weapon bench: ${bench.name} (${bench.id})`);
      }
    } catch (error) {
      console.error(`[HM-Weapons] Failed to initialize weapon bench ${bench.id}:`, error);
    }
  }  /**
   * Spawn the prop for a weapon bench
   */
  private async spawnBenchProp(bench: WeaponBench): Promise<void> {
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Attempting to spawn bench prop for ${bench.id} with model ${bench.model}`);
    }

    // Check if bench prop already exists
    if (this.doesBenchPropExist(bench)) {
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Bench prop for ${bench.id} already exists, skipping spawn`);
      }
      return;
    }

    const modelHash = GetHashKey(bench.model);
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Model hash for ${bench.model}: ${modelHash} (0x${modelHash.toString(16).toUpperCase()})`);
    }

    // Request the model
    RequestModel(modelHash);
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Requesting model ${bench.model}...`);
    }
    
    // Wait for model to load
    let attempts = 0;
    while (!HasModelLoaded(modelHash) && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
      
      if (attempts % 10 === 0 && WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Still waiting for model to load... attempt ${attempts}/50`);
      }
    }

    if (!HasModelLoaded(modelHash)) {
      console.error(`[HM-Weapons] Failed to load model ${bench.model} for bench ${bench.id} after ${attempts} attempts`);
      console.error(`[HM-Weapons] Check if model name ${bench.model} is valid in GTA V`);
      return;
    }

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Model ${bench.model} loaded successfully after ${attempts} attempts`);
    }    // Get the ground Z coordinate for proper placement
    const [foundGround, groundZ] = GetGroundZFor_3dCoord(
      bench.coords.x,
      bench.coords.y,
      bench.coords.z + 10.0, // Start search from above
      false
    );

    // Use the configured Z if ground detection fails or gives a very different result
    let finalZ = bench.coords.z;
    if (foundGround) {
      const heightDiff = Math.abs(groundZ - bench.coords.z);
      // Only use ground Z if it's reasonably close to configured Z (within 2 units)
      if (heightDiff <= 2.0) {
        finalZ = groundZ;
      } else {
        if (WEAPON_BENCH_CONFIG.debug) {
          console.log(`[HM-Weapons] Ground Z ${groundZ} differs too much from config Z ${bench.coords.z} (diff: ${heightDiff}), using config Z`);
        }
      }
    }

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Ground detection: found=${foundGround}, groundZ=${groundZ}, finalZ=${finalZ}`);
      console.log(`[HM-Weapons] Creating object at coords: [${bench.coords.x}, ${bench.coords.y}, ${finalZ}]`);
    }

    // Create the prop with proper networking for persistence
    const prop = CreateObject(
      modelHash,
      bench.coords.x,
      bench.coords.y,
      finalZ,
      false, // networkObject - set to false for local object first
      false, // netMissionEntity - set to false initially
      false  // doorFlag
    );

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] CreateObject returned: ${prop}`);
    }

    if (prop && prop !== 0 && DoesEntityExist(prop)) {
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] ✓ Object created successfully with handle: ${prop}`);
      }      // Set the heading
      SetEntityHeading(prop, bench.heading);
      
      // Make it static and persistent
      FreezeEntityPosition(prop, true);
      SetEntityInvincible(prop, true);
      
      // Now make it persistent
      SetEntityAsMissionEntity(prop, true, true);
      
      // Store the prop handle
      this.spawnedProps.set(bench.id, prop);

      // Get final position for logging
      const finalCoords = GetEntityCoords(prop, false);
      
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] ✓ Bench ${bench.id} spawned successfully!`);
        console.log(`[HM-Weapons] Final position: [${finalCoords[0].toFixed(2)}, ${finalCoords[1].toFixed(2)}, ${finalCoords[2].toFixed(2)}]`);
        console.log(`[HM-Weapons] Heading: ${GetEntityHeading(prop).toFixed(2)}`);
        console.log(`[HM-Weapons] Entity exists: ${DoesEntityExist(prop)}`);
        console.log(`[HM-Weapons] Entity frozen: ${IsEntityPositionFrozen(prop)}`);
      }
    } else {
      console.error(`[HM-Weapons] ✗ Failed to create prop for bench ${bench.id}`);
      console.error(`[HM-Weapons] CreateObject returned: ${prop}`);
      console.error(`[HM-Weapons] DoesEntityExist: ${prop ? DoesEntityExist(prop) : 'N/A'}`);
    }

    // Clean up model
    SetModelAsNoLongerNeeded(modelHash);
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Model cleanup completed for ${bench.model}`);
    }
  }  /**
   * Check if a bench prop already exists at the specified location
   */
  private doesBenchPropExist(bench: WeaponBench): boolean {
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Checking if bench prop exists for ${bench.id}`);
    }

    // Check if we have a stored prop handle that still exists
    const storedProp = this.spawnedProps.get(bench.id);
    if (storedProp && DoesEntityExist(storedProp)) {
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Found existing stored prop for ${bench.id}: ${storedProp}`);
      }
      return true;
    }

    // Check for any objects of the same model near the bench coordinates
    const modelHash = GetHashKey(bench.model);
    const closestObject = GetClosestObjectOfType(
      bench.coords.x,
      bench.coords.y,
      bench.coords.z,
      2.0, // search radius
      modelHash,
      false,
      false,
      false
    );

    if (closestObject && closestObject !== 0 && DoesEntityExist(closestObject)) {
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Found existing object near bench ${bench.id}: ${closestObject}`);
      }
      // Update our tracking with the found prop
      this.spawnedProps.set(bench.id, closestObject);
      return true;
    }

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] No existing prop found for bench ${bench.id}`);
    }
    return false;
  }

  /**
   * Register a weapon bench with hm-target using coordinate-based targeting
   */
  private async registerBenchTarget(bench: WeaponBench): Promise<void> {
    try {
      // Get the hm-target export
      const hmTarget = global.exports['hm-target'];
      
      if (!hmTarget) {
        console.error('[HM-Weapons] hm-target export not found! Make sure hm-target is running.');
        return;
      }

      // Prepare target options based on bench type
      const targetOptions = this.getBenchTargetOptions(bench);
      
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Registering coordinate-based target for bench ${bench.id} at coords:`, bench.coords);
        console.log(`[HM-Weapons] Target options:`, targetOptions);
      }

      // Register coordinate-based target instead of model-based
      hmTarget.addBoxZone(
        `weapon_bench_${bench.id}`, // unique zone name
        bench.coords, // center position
        1.5, // width
        1.5, // length
        {
          name: `weapon_bench_${bench.id}`,
          heading: bench.heading,
          debugPoly: WEAPON_BENCH_CONFIG.debug,
          minZ: bench.coords.z - 0.5,
          maxZ: bench.coords.z + 2.0
        },
        {
          options: targetOptions,
          distance: bench.interactionDistance
        }
      );

      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Registered coordinate-based target for bench ${bench.id}`);
      }
    } catch (error) {
      console.error(`[HM-Weapons] Failed to register target for bench ${bench.id}:`, error);
    }
  }  
  /**
   * Get target options for a weapon bench
   */
  private getBenchTargetOptions(bench: WeaponBench): any[] {
    return [{
      id: `weapon_bench_${bench.id}`,
      label: 'Open Weapon Modding',
      icon: 'fas fa-tools',
      action: 'hm-weapons:benchInteraction',
      data: { benchId: bench.id },
      canInteract: (entity?: number) => {
        // Use entity position to determine which bench this is
        if (entity && DoesEntityExist(entity)) {
          const entityCoords = GetEntityCoords(entity, false);
          const distance = Vdist(entityCoords[0], entityCoords[1], entityCoords[2], 
                                bench.coords.x, bench.coords.y, bench.coords.z);
          
          if (WEAPON_BENCH_CONFIG.debug) {
            console.log(`[HM-Weapons] Checking bench ${bench.id}: entity at [${entityCoords[0].toFixed(2)}, ${entityCoords[1].toFixed(2)}, ${entityCoords[2].toFixed(2)}], bench at [${bench.coords.x}, ${bench.coords.y}, ${bench.coords.z}], distance: ${distance.toFixed(2)}`);
          }
          
          // Only show this option if we're close to this specific bench
          if (distance <= 2.0) {
            return this.canPlayerAccessBench(bench, entity);
          }
          return false;
        }
        return this.canPlayerAccessBench(bench, entity);
      }
    }];
  }
  /**
   * Check if player can access a weapon bench
   */
  private canPlayerAccessBench(bench: WeaponBench, entity?: number): boolean {
    // TODO: Get actual player data from hm-core
    const playerJob = 'police'; // This should come from player data
    const playerGang = 'none'; // This should come from player data
    const hasRequiredItem = true; // This should check player inventory

    return canAccessWeaponBench(bench, playerJob, playerGang, hasRequiredItem);
  }    /**
   * Handle weapon bench interaction
   */
  public handleBenchInteraction(bench: WeaponBench): void {
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Player interacted with bench ${bench.id}`);
    }

    // Check if player can access this bench
    if (!this.canPlayerAccessBench(bench)) {
      // Show notification that access is denied
      this.showNotification('Access denied to this weapon bench', 'error');
      return;
    }

    // Get the actual bench prop position for accurate weapon positioning
    const benchProp = this.spawnedProps.get(bench.id);
    let benchPosition = bench.coords;
    
    if (benchProp && DoesEntityExist(benchProp)) {
      // Use actual prop position instead of configured coordinates
      const propCoords = GetEntityCoords(benchProp, false);
      const propHeading = GetEntityHeading(benchProp);
      
      benchPosition = {
        x: propCoords[0],
        y: propCoords[1], 
        z: propCoords[2]
      };
      
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Using actual bench prop position: [${benchPosition.x.toFixed(2)}, ${benchPosition.y.toFixed(2)}, ${benchPosition.z.toFixed(2)}]`);
        console.log(`[HM-Weapons] Bench prop heading: ${propHeading.toFixed(2)}`);
      }
    } else {
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] No bench prop found, using configured coordinates`);
      }
    }

    // Calculate weapon spawn position with proper offset from the bench
    const weaponPosition = this.calculateWeaponPosition(bench, benchPosition);

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Calculated weapon position: [${weaponPosition.x.toFixed(2)}, ${weaponPosition.y.toFixed(2)}, ${weaponPosition.z.toFixed(2)}]`);
    }

    // Get player's current weapon to modify
    const playerPed = PlayerPedId();
    const [hasWeapon, weaponHash] = GetCurrentPedWeapon(playerPed, true);
    
    if (!hasWeapon || weaponHash === GetHashKey('WEAPON_UNARMED')) {
      this.showNotification('You need to have a weapon equipped to modify it', 'error');
      return;
    }    // Send message to open modding UI with weapon position
    SendNuiMessage(JSON.stringify({
      type: 'OPEN_WEAPON_MODDING',
      data: {
        weaponHash: weaponHash,
        benchId: bench.id,
        weaponPosition: weaponPosition,
        benchPosition: benchPosition
      }    }));

    // Directly start the weapon modding process
    // We need to import the weapon modding manager and call it directly
    console.log(`[HM-Weapons] Starting weapon modding directly for weapon ${weaponHash}`);
    
    // Emit event to trigger weapon modding start
    emit('hm-weapons:startweaponmodding', {
      weaponHash: weaponHash,
      weaponPosition: weaponPosition,
      benchId: bench.id
    });

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Opened weapon modding for weapon ${weaponHash} at bench ${bench.id}`);
    }
  }

  /**
   * Calculate optimal weapon position relative to bench
   */
  private calculateWeaponPosition(bench: WeaponBench, benchPosition: { x: number; y: number; z: number }): { x: number; y: number; z: number } {
    // Get the bench prop for more accurate positioning
    const benchProp = this.spawnedProps.get(bench.id);
    let actualHeading = bench.heading;
    
    if (benchProp && DoesEntityExist(benchProp)) {
      actualHeading = GetEntityHeading(benchProp);
    }
    
    // Convert heading to radians
    const headingRadians = (actualHeading * Math.PI) / 180;
    
    // Calculate position offset based on bench orientation
    // Position weapon slightly in front and above the bench
    const forwardOffset = 0.6; // Distance in front of bench
    const upwardOffset = 1.5;  // Height above bench surface
    
    const weaponPosition = {
      x: benchPosition.x + Math.sin(headingRadians) * forwardOffset,
      y: benchPosition.y + Math.cos(headingRadians) * forwardOffset,
      z: benchPosition.z + upwardOffset
    };
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Weapon positioning calculation:`);
      console.log(`[HM-Weapons]   Bench heading: ${actualHeading.toFixed(2)}°`);
      console.log(`[HM-Weapons]   Forward offset: ${forwardOffset}m`);
      console.log(`[HM-Weapons]   Upward offset: ${upwardOffset}m`);
      console.log(`[HM-Weapons]   Final position: [${weaponPosition.x.toFixed(2)}, ${weaponPosition.y.toFixed(2)}, ${weaponPosition.z.toFixed(2)}]`);
    }
    
    return weaponPosition;
  }

  /**
   * Show notification to player
   */
  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    // TODO: Use hm-core notification system
    console.log(`[HM-Weapons] ${type.toUpperCase()}: ${message}`);
  }  
  /**
   * Clean up weapon benches (only removes from tracking, props remain persistent)
   */
  public cleanup(): void {
    // Note: We don't delete the props as they should persist throughout the session
    // Only clear our tracking of them
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Cleaning up tracking for ${this.spawnedProps.size} persistent bench props`);
    }
    
    // Clear our maps but leave the props in the world
    this.spawnedProps.clear();
    this.initializedBenches.clear();

    if (WEAPON_BENCH_CONFIG.debug) {
      console.log('[HM-Weapons] Weapon benches cleanup completed (props remain persistent)');
    }
  }

  /**
   * Get bench by ID
   */
  public getBench(benchId: string): WeaponBench | undefined {
    return WEAPON_BENCHES.find(bench => bench.id === benchId);
  }
  /**
   * Check if a bench is initialized
   */
  public isBenchInitialized(benchId: string): boolean {
    return this.initializedBenches.has(benchId);
  }

  /**
   * Debug method to get bench status
   */
  public debugBenchStatus(benchId: string): void {
    const bench = WEAPON_BENCHES.find(b => b.id === benchId);
    if (!bench) {
      console.log(`[HM-Weapons] Bench ${benchId} not found`);
      return;
    }

    console.log(`[HM-Weapons] ========== DEBUG BENCH ${benchId} ==========`);
    console.log(`[HM-Weapons] Name: ${bench.name}`);
    console.log(`[HM-Weapons] Model: ${bench.model}`);
    console.log(`[HM-Weapons] Coords: [${bench.coords.x}, ${bench.coords.y}, ${bench.coords.z}]`);
    console.log(`[HM-Weapons] Heading: ${bench.heading}`);
    console.log(`[HM-Weapons] AutoSpawn: ${bench.autoSpawn}`);
    console.log(`[HM-Weapons] Enabled: ${bench.enabled}`);

    // Check current status
    const existing = this.spawnedProps.get(benchId);
    if (existing) {
      console.log(`[HM-Weapons] Existing prop handle: ${existing}`);
      console.log(`[HM-Weapons] Prop exists: ${DoesEntityExist(existing)}`);
      if (DoesEntityExist(existing)) {
        const coords = GetEntityCoords(existing, false);
        console.log(`[HM-Weapons] Prop position: [${coords[0].toFixed(2)}, ${coords[1].toFixed(2)}, ${coords[2].toFixed(2)}]`);
      }
    } else {
      console.log(`[HM-Weapons] No existing prop tracked`);
    }
    console.log(`[HM-Weapons] ========================================`);
  }
  /**
   * Debug method to force spawn a bench
   */
  public async debugSpawnBench(benchId: string): Promise<void> {
    const bench = WEAPON_BENCHES.find(b => b.id === benchId);
    if (!bench) {
      console.log(`[HM-Weapons] Bench ${benchId} not found`);
      return;
    }

    console.log(`[HM-Weapons] Force spawning bench ${benchId}...`);
    await this.spawnBenchProp(bench);
  }

  /**
   * Debug method to test weapon positioning
   */
  public debugWeaponPosition(benchId: string): { x: number; y: number; z: number } | null {
    const bench = WEAPON_BENCHES.find(b => b.id === benchId);
    if (!bench) {
      console.log(`[HM-Weapons] Bench ${benchId} not found`);
      return null;
    }

    const benchProp = this.spawnedProps.get(bench.id);
    let benchPosition = bench.coords;
    
    if (benchProp && DoesEntityExist(benchProp)) {
      const propCoords = GetEntityCoords(benchProp, false);
      benchPosition = { x: propCoords[0], y: propCoords[1], z: propCoords[2] };
      console.log(`[HM-Weapons] Bench prop position: [${benchPosition.x.toFixed(2)}, ${benchPosition.y.toFixed(2)}, ${benchPosition.z.toFixed(2)}]`);
    }
    
    const weaponPos = this.calculateWeaponPosition(bench, benchPosition);
    console.log(`[HM-Weapons] Weapon would spawn at: [${weaponPos.x.toFixed(2)}, ${weaponPos.y.toFixed(2)}, ${weaponPos.z.toFixed(2)}]`);
    
    return weaponPos;
  }

  /**
   * Register model targets for a specific model with all bench options
   */
  private async registerModelTargets(model: string, benches: WeaponBench[]): Promise<void> {
    try {
      // Get the hm-target export
      const hmTarget = global.exports['hm-target'];
      
      if (!hmTarget) {
        console.error('[HM-Weapons] hm-target export not found! Make sure hm-target is running.');
        return;
      }

      // Get the model hash
      const modelHash = GetHashKey(model);
      
      // Create a single target option that handles all benches for this model
      const targetOptions = [{
        id: `weapon_bench_model_${modelHash}`,
        label: 'Open Weapon Modding',
        icon: 'fas fa-tools',
        action: 'hm-weapons:benchInteraction',
        data: { modelHash: modelHash, benches: benches.map(b => b.id) },
        canInteract: (entity?: number) => {
          if (!entity || !DoesEntityExist(entity)) return false;
          
          // Find which bench this entity represents based on position
          const entityCoords = GetEntityCoords(entity, false);
          
          for (const bench of benches) {
            const distance = Vdist(entityCoords[0], entityCoords[1], entityCoords[2], 
                                  bench.coords.x, bench.coords.y, bench.coords.z);
            
            if (distance <= 2.0) {
              // Check if player can access this specific bench
              return this.canPlayerAccessBench(bench, entity);
            }
          }
          
          return false;
        }
      }];
      
      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] Registering model targets for ${model} (hash: ${modelHash}) with ${benches.length} benches:`, benches.map(b => b.id));
      }

      // Register model target with hm-target
      hmTarget.addTargetModel(modelHash, targetOptions);

      if (WEAPON_BENCH_CONFIG.debug) {
        console.log(`[HM-Weapons] ✓ Successfully registered model targets for ${model}`);
      }

    } catch (error) {
      console.error(`[HM-Weapons] Failed to register model targets for ${model}:`, error);
    }
  }
}

// Initialize when resource starts
const weaponBenchesManager = WeaponBenchesManager.getInstance();

// Event handler for bench interactions
on('hm-weapons:benchInteraction', (eventData: { target: any; option: any }) => {
  if (WEAPON_BENCH_CONFIG.debug) {
    console.log(`[HM-Weapons] Bench interaction event received:`, eventData);
  }
  
  // Get the entity and its position
  const entity = eventData.target.entity;
  const entityPosition = eventData.target.position;
  
  if (!entity || !DoesEntityExist(entity)) {
    console.error(`[HM-Weapons] Invalid entity in bench interaction`);
    return;
  }
  // Find which bench this entity represents based on position
  let targetBench: WeaponBench | undefined;
  const enabledBenches = getEnabledWeaponBenches();
  
  for (const bench of enabledBenches) {
    // Calculate both 2D and 3D distances
    const distance2D = Math.sqrt(
      Math.pow(entityPosition.x - bench.coords.x, 2) + 
      Math.pow(entityPosition.y - bench.coords.y, 2)
    );
    const distance3D = Vdist(entityPosition.x, entityPosition.y, entityPosition.z, 
                            bench.coords.x, bench.coords.y, bench.coords.z);
    
    if (WEAPON_BENCH_CONFIG.debug) {
      console.log(`[HM-Weapons] Checking bench ${bench.id}: 2D distance ${distance2D.toFixed(2)}, 3D distance ${distance3D.toFixed(2)}`);
    }
    
    // Use 2D distance for matching (more flexible for ground placement differences)
    if (distance2D <= 3.0) { // Slightly increased tolerance
      targetBench = bench;
      break;
    }
  }
  
  if (!targetBench) {
    console.error(`[HM-Weapons] No bench found at entity position:`, entityPosition);
    return;
  }

  if (WEAPON_BENCH_CONFIG.debug) {
    console.log(`[HM-Weapons] Found target bench: ${targetBench.id}`);
  }

  weaponBenchesManager.handleBenchInteraction(targetBench);
});

// Resource start handler
on('onResourceStart', (resourceName: string) => {
  if (resourceName === GetCurrentResourceName()) {
    setTimeout(async () => {
      await weaponBenchesManager.initializeWeaponBenches();
    }, 1000); // Delay to ensure hm-target is loaded
  }
});

// Resource stop handler
on('onResourceStop', (resourceName: string) => {
  if (resourceName === GetCurrentResourceName()) {
    weaponBenchesManager.cleanup();
  }
});

export { weaponBenchesManager };

// Debug command to manually test bench spawning
RegisterCommand('debugbench', async (source: number, args: string[]) => {
  const benchId = args[0];
  
  if (!benchId) {
    console.log('[HM-Weapons] Usage: /debugbench <benchId>');
    console.log('[HM-Weapons] Available benches:');
    WEAPON_BENCHES.forEach(bench => {
      console.log(`[HM-Weapons]   - ${bench.id}: ${bench.name}`);
    });
    return;
  }

  weaponBenchesManager.debugBenchStatus(benchId);
  
  // Force spawn
  await weaponBenchesManager.debugSpawnBench(benchId);
}, false);

// Debug command to teleport to bench
RegisterCommand('tpbench', (source: number, args: string[]) => {
  const benchId = args[0];
  
  if (!benchId) {
    console.log('[HM-Weapons] Usage: /tpbench <benchId>');
    return;
  }

  const bench = WEAPON_BENCHES.find(b => b.id === benchId);
  if (!bench) {
    console.log(`[HM-Weapons] Bench ${benchId} not found`);
    return;
  }

  const playerPed = PlayerPedId();
  SetEntityCoords(playerPed, bench.coords.x, bench.coords.y, bench.coords.z + 1.0, false, false, false, true);
  console.log(`[HM-Weapons] Teleported to bench ${benchId}`);
}, false);

// Debug command to test weapon positioning
RegisterCommand('testweaponpos', (source: number, args: string[]) => {
  const benchId = args[0] || 'ammunation_downtown';
  
  const bench = WEAPON_BENCHES.find(b => b.id === benchId);
  if (!bench) {
    console.log(`[HM-Weapons] Bench ${benchId} not found`);
    return;
  }

  const weaponPos = weaponBenchesManager.debugWeaponPosition(benchId);
  if (!weaponPos) return;
  
  // Teleport player to weapon position for testing
  const playerPed = PlayerPedId();
  SetEntityCoords(playerPed, weaponPos.x, weaponPos.y, weaponPos.z, false, false, false, true);
  console.log(`[HM-Weapons] Teleported to weapon spawn position`);
}, false);
