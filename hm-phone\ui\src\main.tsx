import { createRoot } from 'react-dom/client';
import './index.css';
import AppRoutes from './apps/AppRoutes';
import { PhoneProvider } from './providers/PhoneProvider';
import { initializeEventReceiver } from './fivem/clientEventReceiver';

// Initialize the client event receiver
initializeEventReceiver();

// Import and register handlers for all apps
import './apps/contacts/handlers';
import './apps/messages/handlers';
import './apps/notes/handlers';
import './apps/yellowPages/handlers';
import './apps/lifeSnap/handlers';
import './apps/lovelink/handlers';
import './apps/settings/handlers';
import './apps/music/handlers';
import './apps/garage/handlers';
import './apps/camera/handlers';
import './apps/photos/handlers';
import './apps/services/handlers';
import './notifications/handlers';
import './navigation/handlers';
import './navigation/handlers/playerDataHandlers';
import './navigation/handlers/cleanupHandlers'; // Import cleanup handlers

// DO NOT initialize app data at startup
// This will be done when the phone is opened and when specific apps are accessed
console.log('[Main] Deferring app data initialization until needed');

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Failed to find the root element');
}

try {
  const root = createRoot(rootElement);
  root.render(
    <PhoneProvider>
      <AppRoutes />
    </PhoneProvider>
  );
} catch (error) {
  console.error('Error rendering the application:', error);
}
