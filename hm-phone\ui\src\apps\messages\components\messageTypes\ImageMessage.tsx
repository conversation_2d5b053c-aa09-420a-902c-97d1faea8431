import React from 'react';
import { ImageMessageType } from '../../../../../../shared/types';
import ImageModal from '../../../../common/components/ImageModal';

// Extended ImageMessageType with UI-specific properties
interface UIImageMessage extends Omit<ImageMessageType, 'content'> {
  message?: string;
  timestamp?: string;
  metadata?: {
    mediaUrls?: string[];
    thumbnailUrls?: string[];
    hasText?: boolean;
    timestamp?: number;
    [key: string]: unknown;
  };
}

interface ImageMessageProps {
  message: UIImageMessage;
  showImageModal: boolean;
  selectedImageIndex: number;
  setSelectedImageIndex: (index: number) => void;
  setShowImageModal: (show: boolean) => void;
}

export const ImageMessage: React.FC<ImageMessageProps> = ({
  message,
  showImageModal,
  selectedImageIndex,
  setSelectedImageIndex,
  setShowImageModal
}) => {
  const mediaUrls = message.metadata?.mediaUrls || [];
  const hasText = message.metadata?.hasText || false;
  const showContent = hasText || (message.message && message.message !== 'Photo');

  // Single image display
  if (mediaUrls.length === 1) {
    return (
      <>
        <div className="space-y-1">
          <div
            className="relative max-w-full group cursor-pointer"
            onClick={() => setShowImageModal(true)}
          >
            <div className="aspect-square relative rounded-lg overflow-hidden bg-white/5">
              <img
                src={mediaUrls[0]}
                alt="Image message"
                className="w-full h-full object-cover"
                loading="lazy"
              />

              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent">
                  <div className="flex items-center gap-3">
                    <div className="text-xs text-white/70" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {showContent && <div className="px-2 text-[14px]">{message.message}</div>}
          <div className="flex justify-end items-center gap-1 px-2 mt-1">
            <span className="text-[9px] text-white/50">
              {new Date(
                message.timestamp ||
                (message.metadata?.timestamp ? message.metadata.timestamp : Date.now())
              ).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </div>
        </div>
        {showImageModal && (
          <ImageModal
            isVisible={showImageModal}
            onClose={() => setShowImageModal(false)}
            imageUrl={mediaUrls[0]}
            metadata={{
              size: undefined,
              timestamp: message.metadata?.timestamp?.toString(),
              dimensions: undefined
            }}
          />
        )}
      </>
    );
  }

  // Multiple images display
  return (
    <>
      <div className="space-y-1">
        <div className="max-w-full p-0.5">
          <div
            className={`grid gap-0.5 ${
              mediaUrls.length === 2
                ? 'grid-cols-2'
                : mediaUrls.length === 3
                ? 'grid-cols-2'
                : mediaUrls.length === 4
                ? 'grid-cols-2 grid-rows-2'
                : mediaUrls.length === 5
                ? 'grid-cols-3'
                : mediaUrls.length === 6
                ? 'grid-cols-3 grid-rows-2'
                : 'grid-cols-2'
            }`}
          >
            {mediaUrls.map((url: string, index: number) => {
              if (index >= 6) return null;

              return (
                <div
                  key={index}
                  className={`relative group cursor-pointer ${
                    mediaUrls.length === 3 && index === 0
                      ? 'col-span-2'
                      : mediaUrls.length === 5 && index === 0
                      ? 'col-span-2'
                      : ''
                  }`}
                  onClick={() => {
                    setSelectedImageIndex(index);
                    setShowImageModal(true);
                  }}
                >
                  <div
                    className={`relative ${
                      (mediaUrls.length === 3 && index === 0) ||
                      (mediaUrls.length === 5 && index === 0)
                        ? 'aspect-[2/1]'
                        : 'aspect-square'
                    }`}
                  >
                    <img
                      src={message.metadata?.thumbnailUrls?.[index] || url}
                      alt={`Image ${index + 1}`}
                      className="w-full h-full object-cover rounded-sm"
                      loading="lazy"
                    />

                    {index === 5 && (mediaUrls.length ?? 0) > 6 && (
                      <div className="absolute inset-0 bg-black/60 flex items-center justify-center rounded-sm">
                        <span className="text-white text-lg font-medium">
                          +{mediaUrls.length ? mediaUrls.length - 6 : 0}
                        </span>
                      </div>
                    )}

                    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-sm" />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {showContent && <div className="px-2 text-[14px]">{message.message}</div>}
        <div className="flex justify-end items-center gap-1 px-2 mt-1">
          <span className="text-[11px] text-white/50">
            {new Date(
              message.timestamp ||
              (message.metadata?.timestamp ? message.metadata.timestamp : Date.now())
            ).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
      </div>
      {showImageModal && (
        <ImageModal
          isVisible={showImageModal}
          onClose={() => setShowImageModal(false)}
          imageUrl={message.metadata?.mediaUrls?.[selectedImageIndex] || ''}
          metadata={{
            size: undefined,
            timestamp: message.metadata?.timestamp?.toString(),
            dimensions: undefined
          }}
        />
      )}
    </>
  );
};
