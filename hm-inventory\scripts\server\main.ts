/// <reference types="@citizenfx/server" />

// Import and initialize the inventory manager
import './inventory.manager';

// Set up global error handling for inventory operations
process.on('uncaughtException', (error) => {
    console.error(`[hm-inventory] Unhandled error: ${error.message}`);
});

console.log('[hm-inventory] Server script loaded successfully');

// Export functions for other resources to use
export * from './inventory.manager';
