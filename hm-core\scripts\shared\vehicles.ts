/**
 * Complete Vehicle Database - HM Framework
 * True single file containing all vehicles from QBCore
 * No external dependencies - optimized for FiveM performance
 */

export enum VehicleCategory {
    COMPACTS = 'compacts',
    SEDANS = 'sedans',
    SUVS = 'suvs',
    COUPES = 'coupes',
    MUSCLE = 'muscle',
    SPORTSCLASSICS = 'sportsclassics',
    SPORTS = 'sports',
    SUPER = 'super',
    MOTORCYCLES = 'motorcycles',
    OFFROAD = 'offroad',
    INDUSTRIAL = 'industrial',
    UTILITY = 'utility',
    VANS = 'vans',
    CYCLES = 'cycles',
    BOATS = 'boats',
    HELICOPTERS = 'helicopters',
    PLANES = 'planes',
    SERVICE = 'service',
    EMERGENCY = 'emergency',
    MILITARY = 'military',
    COMMERCIAL = 'commercial',
    TRAINS = 'trains',
    OPENWHEEL = 'openwheel',
}
export interface VehicleMetadata {
    model: string;
    name: string;
    category: VehicleCategory;
    brand: string;
}

export type VehicleDatabase = Record<string, VehicleMetadata>;

// Complete vehicle database with all 882+ vehicles
export const allVehicles: VehicleDatabase = {
    // Boats vehicles (26 vehicles)
    avisa: {
        model: 'avisa',
        name: 'Avisa',
        brand: 'Kraken',
        category: VehicleCategory.BOATS,
    },
    dinghy: {
        model: 'dinghy',
        name: 'Dinghy',
        brand: 'Nagasaki',
        category: VehicleCategory.BOATS,
    },
    dinghy2: {
        model: 'dinghy2',
        name: 'Dinghy',
        brand: 'Nagasaki',
        category: VehicleCategory.BOATS,
    },
    dinghy3: {
        model: 'dinghy3',
        name: 'Dinghy (Heist)',
        brand: 'Nagasaki',
        category: VehicleCategory.BOATS,
    },
    dinghy4: {
        model: 'dinghy4',
        name: 'Dinghy (Yacht)',
        brand: 'Nagasaki',
        category: VehicleCategory.BOATS,
    },
    dinghy5: {
        model: 'dinghy5',
        name: 'Weaponized Dinghy',
        brand: 'Nagasaki',
        category: VehicleCategory.BOATS,
    },
    jetmax: {
        model: 'jetmax',
        name: 'Jetmax',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    kosatka: {
        model: 'kosatka',
        name: 'Kosatka',
        brand: 'RUNE',
        category: VehicleCategory.BOATS,
    },
    longfin: {
        model: 'longfin',
        name: 'Longfin',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    marquis: {
        model: 'marquis',
        name: 'Marquis',
        brand: 'Dinka',
        category: VehicleCategory.BOATS,
    },
    patrolboat: {
        model: 'patrolboat',
        name: 'Kurtz 31 Patrol Boat',
        brand: '',
        category: VehicleCategory.BOATS,
    },
    predator: {
        model: 'predator',
        name: 'Police Predator',
        brand: '',
        category: VehicleCategory.BOATS,
    },
    seashark: {
        model: 'seashark',
        name: 'Seashark',
        brand: 'Speedophile',
        category: VehicleCategory.BOATS,
    },
    seashark2: {
        model: 'seashark2',
        name: 'Seashark (Lifeguard)',
        brand: 'Speedophile',
        category: VehicleCategory.BOATS,
    },
    seashark3: {
        model: 'seashark3',
        name: 'Seashark (Yacht)',
        brand: 'Speedophile',
        category: VehicleCategory.BOATS,
    },
    speeder: {
        model: 'speeder',
        name: 'Speeder (Yacht)',
        brand: 'Pegassi',
        category: VehicleCategory.BOATS,
    },
    speeder2: {
        model: 'speeder2',
        name: 'Speeder',
        brand: 'Pegassi',
        category: VehicleCategory.BOATS,
    },
    squalo: {
        model: 'squalo',
        name: 'Squalo',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    submersible: {
        model: 'submersible',
        name: 'Submersible',
        brand: '',
        category: VehicleCategory.BOATS,
    },
    submersible2: {
        model: 'submersible2',
        name: 'Kraken',
        brand: 'Kraken',
        category: VehicleCategory.BOATS,
    },
    suntrap: {
        model: 'suntrap',
        name: 'Suntrap',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    toro: {
        model: 'toro',
        name: 'Toro',
        brand: 'Lampadati',
        category: VehicleCategory.BOATS,
    },
    toro2: {
        model: 'toro2',
        name: 'Toro (Yacht)',
        brand: 'Lampadati',
        category: VehicleCategory.BOATS,
    },
    tropic: {
        model: 'tropic',
        name: 'Tropic',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    tropic2: {
        model: 'tropic2',
        name: 'Tropic (Yacht)',
        brand: 'Shitzu',
        category: VehicleCategory.BOATS,
    },
    tug: {
        model: 'tug',
        name: 'Tug',
        brand: 'Buckingham',
        category: VehicleCategory.BOATS,
    },
    // Commercial vehicles (23 vehicles)
    benson: {
        model: 'benson',
        name: 'Benson',
        brand: 'Vapid',
        category: VehicleCategory.COMMERCIAL,
    },
    benson2: {
        model: 'benson2',
        name: 'Benson (Cluckin)',
        brand: 'Vapid',
        category: VehicleCategory.COMMERCIAL,
    },
    biff: {
        model: 'biff',
        name: 'Biff',
        brand: 'HVY',
        category: VehicleCategory.COMMERCIAL,
    },
    cerberus: {
        model: 'cerberus',
        name: 'Apocalypse Cerberus',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    cerberus2: {
        model: 'cerberus2',
        name: 'Future Shock Cerberus',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    cerberus3: {
        model: 'cerberus3',
        name: 'Nightmare Cerberus',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    hauler: {
        model: 'hauler',
        name: 'Hauler',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    hauler2: {
        model: 'hauler2',
        name: 'Hauler Custom',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    mule: {
        model: 'mule',
        name: 'Mule',
        brand: 'Maibatsu',
        category: VehicleCategory.COMMERCIAL,
    },
    mule2: {
        model: 'mule2',
        name: 'Mule (Ramp Door)',
        brand: 'Maibatsu',
        category: VehicleCategory.COMMERCIAL,
    },
    mule3: {
        model: 'mule3',
        name: 'Mule (Ramp Door)',
        brand: 'Maibatsu',
        category: VehicleCategory.COMMERCIAL,
    },
    mule4: {
        model: 'mule4',
        name: 'Mule Custom',
        brand: 'Maibatsu',
        category: VehicleCategory.COMMERCIAL,
    },
    mule5: {
        model: 'mule5',
        name: 'Box Truck Mule',
        brand: 'Maibatsu',
        category: VehicleCategory.COMMERCIAL,
    },
    packer: {
        model: 'packer',
        name: 'Packer',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    phantom: {
        model: 'phantom',
        name: 'Phantom',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    phantom2: {
        model: 'phantom2',
        name: 'Phantom Wedge',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    phantom3: {
        model: 'phantom3',
        name: 'Phantom Custom',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    phantom4: {
        model: 'phantom4',
        name: 'Phantom (Christmas)',
        brand: 'JoBuilt',
        category: VehicleCategory.COMMERCIAL,
    },
    pounder: {
        model: 'pounder',
        name: 'Pounder',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    pounder2: {
        model: 'pounder2',
        name: 'Pounder Custom',
        brand: 'MTL',
        category: VehicleCategory.COMMERCIAL,
    },
    stockade: {
        model: 'stockade',
        name: 'Stockade',
        brand: 'Brute',
        category: VehicleCategory.COMMERCIAL,
    },
    stockade3: {
        model: 'stockade3',
        name: 'Stockade (Bobcat Security/Snow)',
        brand: 'Brute',
        category: VehicleCategory.COMMERCIAL,
    },
    terbyte: {
        model: 'terbyte',
        name: 'Terrorbyte',
        brand: 'Benefactor',
        category: VehicleCategory.COMMERCIAL,
    },
    // Compacts vehicles (18 vehicles)
    asbo: {
        model: 'asbo',
        name: 'Asbo',
        brand: 'Maxwell',
        category: VehicleCategory.COMPACTS,
    },
    blista: {
        model: 'blista',
        name: 'Blista',
        brand: 'Dinka',
        category: VehicleCategory.COMPACTS,
    },
    brioso: {
        model: 'brioso',
        name: 'Brioso R/A',
        brand: 'Grotti',
        category: VehicleCategory.COMPACTS,
    },
    brioso2: {
        model: 'brioso2',
        name: 'Brioso 300',
        brand: 'Grotti',
        category: VehicleCategory.COMPACTS,
    },
    brioso3: {
        model: 'brioso3',
        name: 'Brioso 300 Widebody',
        brand: 'Grotti',
        category: VehicleCategory.COMPACTS,
    },
    club: {
        model: 'club',
        name: 'Club',
        brand: 'BF',
        category: VehicleCategory.COMPACTS,
    },
    dilettante: {
        model: 'dilettante',
        name: 'Dilettante',
        brand: 'Karin',
        category: VehicleCategory.COMPACTS,
    },
    dilettante2: {
        model: 'dilettante2',
        name: 'Dilettante (Security)',
        brand: 'Karin',
        category: VehicleCategory.COMPACTS,
    },
    issi2: {
        model: 'issi2',
        name: 'Issi',
        brand: 'Weeny',
        category: VehicleCategory.COMPACTS,
    },
    issi3: {
        model: 'issi3',
        name: 'Issi Classic',
        brand: 'Weeny',
        category: VehicleCategory.COMPACTS,
    },
    issi4: {
        model: 'issi4',
        name: 'Apocalypse Issi',
        brand: 'Weeny',
        category: VehicleCategory.COMPACTS,
    },
    issi5: {
        model: 'issi5',
        name: 'Future Shock Issi',
        brand: 'Weeny',
        category: VehicleCategory.COMPACTS,
    },
    issi6: {
        model: 'issi6',
        name: 'Nightmare Issi',
        brand: 'Weeny',
        category: VehicleCategory.COMPACTS,
    },
    kanjo: {
        model: 'kanjo',
        name: 'Blista Kanjo',
        brand: 'Dinka',
        category: VehicleCategory.COMPACTS,
    },
    panto: {
        model: 'panto',
        name: 'Panto',
        brand: 'Benefactor',
        category: VehicleCategory.COMPACTS,
    },
    prairie: {
        model: 'prairie',
        name: 'Prairie',
        brand: 'Bollokan',
        category: VehicleCategory.COMPACTS,
    },
    rhapsody: {
        model: 'rhapsody',
        name: 'Rhapsody',
        brand: 'Declasse',
        category: VehicleCategory.COMPACTS,
    },
    weevil: {
        model: 'weevil',
        name: 'Weevil',
        brand: 'BF',
        category: VehicleCategory.COMPACTS,
    },
    // Coupes vehicles (19 vehicles)
    cogcabrio: {
        model: 'cogcabrio',
        name: 'Cognoscenti Cabrio',
        brand: 'Enus',
        category: VehicleCategory.COUPES,
    },
    driftfr36: {
        model: 'driftfr36',
        name: 'FR36 (Drift)',
        brand: 'Fathom',
        category: VehicleCategory.COUPES,
    },
    exemplar: {
        model: 'exemplar',
        name: 'Exemplar',
        brand: 'Dewbauchee',
        category: VehicleCategory.COUPES,
    },
    f620: {
        model: 'f620',
        name: 'F620',
        brand: 'Ocelot',
        category: VehicleCategory.COUPES,
    },
    felon: {
        model: 'felon',
        name: 'Felon',
        brand: 'Lampadati',
        category: VehicleCategory.COUPES,
    },
    felon2: {
        model: 'felon2',
        name: 'Felon GT',
        brand: 'Lampadati',
        category: VehicleCategory.COUPES,
    },
    fr36: {
        model: 'fr36',
        name: 'FR36',
        brand: 'Fathom',
        category: VehicleCategory.COUPES,
    },
    jackal: {
        model: 'jackal',
        name: 'Jackal',
        brand: 'Ocelot',
        category: VehicleCategory.COUPES,
    },
    kanjosj: {
        model: 'kanjosj',
        name: 'Kanjo SJ',
        brand: 'Dinka',
        category: VehicleCategory.COUPES,
    },
    oracle: {
        model: 'oracle',
        name: 'Oracle XS',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    oracle2: {
        model: 'oracle2',
        name: 'Oracle',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    postlude: {
        model: 'postlude',
        name: 'Postlude',
        brand: 'Dinka',
        category: VehicleCategory.COUPES,
    },
    previon: {
        model: 'previon',
        name: 'Previon',
        brand: 'Karin',
        category: VehicleCategory.COUPES,
    },
    sentinel: {
        model: 'sentinel',
        name: 'Sentinel',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    sentinel2: {
        model: 'sentinel2',
        name: ' Sentinel XS',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    windsor: {
        model: 'windsor',
        name: 'Windsor',
        brand: 'Enus',
        category: VehicleCategory.COUPES,
    },
    windsor2: {
        model: 'windsor2',
        name: 'Windsor Drop',
        brand: 'Enus',
        category: VehicleCategory.COUPES,
    },
    zion: {
        model: 'zion',
        name: 'Zion',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    zion2: {
        model: 'zion2',
        name: 'Zion Cabrio',
        brand: 'Übermacht',
        category: VehicleCategory.COUPES,
    },
    // Cycles vehicles (9 vehicles)
    bmx: {
        model: 'bmx',
        name: 'BMX',
        brand: 'PED',
        category: VehicleCategory.CYCLES,
    },
    cruiser: {
        model: 'cruiser',
        name: 'Cruiser',
        brand: 'PED',
        category: VehicleCategory.CYCLES,
    },
    fixter: {
        model: 'fixter',
        name: 'Fixter',
        brand: '',
        category: VehicleCategory.CYCLES,
    },
    inductor: {
        model: 'inductor',
        name: 'Inductor',
        brand: 'Coil',
        category: VehicleCategory.CYCLES,
    },
    inductor2: {
        model: 'inductor2',
        name: 'Junk Energy Inductor',
        brand: 'Coil',
        category: VehicleCategory.CYCLES,
    },
    scorcher: {
        model: 'scorcher',
        name: 'Scorcher',
        brand: 'PED',
        category: VehicleCategory.CYCLES,
    },
    tribike: {
        model: 'tribike',
        name: 'Whippet Race Bike',
        brand: '',
        category: VehicleCategory.CYCLES,
    },
    tribike2: {
        model: 'tribike2',
        name: 'Endurex Race Bike',
        brand: '',
        category: VehicleCategory.CYCLES,
    },
    tribike3: {
        model: 'tribike3',
        name: 'Tri-Cycles Race Bike',
        brand: '',
        category: VehicleCategory.CYCLES,
    },
    // Emergency vehicles (31 vehicles)
    ambulance: {
        model: 'ambulance',
        name: 'Ambulance',
        brand: 'Brute',
        category: VehicleCategory.EMERGENCY,
    },
    fbi: {
        model: 'fbi',
        name: 'FIB',
        brand: 'Bravado',
        category: VehicleCategory.EMERGENCY,
    },
    fbi2: {
        model: 'fbi2',
        name: 'FIB',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    firebolt: {
        model: 'firebolt',
        name: 'Firebolt ASP',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    firetruk: {
        model: 'firetruk',
        name: 'Fire Truck',
        brand: 'MTL',
        category: VehicleCategory.EMERGENCY,
    },
    lguard: {
        model: 'lguard',
        name: 'Lifeguard',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    pbus: {
        model: 'pbus',
        name: 'Prison Bus',
        brand: 'Brute',
        category: VehicleCategory.EMERGENCY,
    },
    polcaracara: {
        model: 'polcaracara',
        name: 'Caracara Pursuit',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    polcoquette4: {
        model: 'polcoquette4',
        name: 'Coquette D10 Pursuit',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    poldominator10: {
        model: 'poldominator10',
        name: 'Dominator FX Interceptor',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    poldorado: {
        model: 'poldorado',
        name: 'Dorado Cruiser',
        brand: 'Bravado',
        category: VehicleCategory.EMERGENCY,
    },
    polfaction2: {
        model: 'polfaction2',
        name: 'Outreach Faction',
        brand: 'Willard',
        category: VehicleCategory.EMERGENCY,
    },
    polgauntlet: {
        model: 'polgauntlet',
        name: 'Gauntlet Interceptor',
        brand: 'Bravado',
        category: VehicleCategory.EMERGENCY,
    },
    polgreenwood: {
        model: 'polgreenwood',
        name: 'Greenwood Cruiser',
        brand: 'Bravado',
        category: VehicleCategory.EMERGENCY,
    },
    police: {
        model: 'police',
        name: 'Police Cruiser',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    police2: {
        model: 'police2',
        name: 'Police Cruiser',
        brand: 'Buffalo',
        category: VehicleCategory.EMERGENCY,
    },
    police3: {
        model: 'police3',
        name: 'Police Cruiser (Interceptor)',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    police4: {
        model: 'police4',
        name: 'Unmarked Cruiser',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    police5: {
        model: 'police5',
        name: 'Stanier LE Cruiser',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    policeb: {
        model: 'policeb',
        name: 'Police Bike',
        brand: 'Western',
        category: VehicleCategory.EMERGENCY,
    },
    policeold1: {
        model: 'policeold1',
        name: 'Police Rancher',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    policeold2: {
        model: 'policeold2',
        name: 'Police Roadcruiser',
        brand: 'Albany',
        category: VehicleCategory.EMERGENCY,
    },
    policet: {
        model: 'policet',
        name: 'Police Transporter',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    policet3: {
        model: 'policet3',
        name: 'Burrito (Bail Enforcement)',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    polimpaler5: {
        model: 'polimpaler5',
        name: 'Impaler SZ Cruiser',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    polimpaler6: {
        model: 'polimpaler6',
        name: 'Impaler LX Cruiser',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    pranger: {
        model: 'pranger',
        name: 'Park Ranger',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    riot: {
        model: 'riot',
        name: 'Police Riot',
        brand: 'Brute',
        category: VehicleCategory.EMERGENCY,
    },
    riot2: {
        model: 'riot2',
        name: 'RCV',
        brand: 'Brute',
        category: VehicleCategory.EMERGENCY,
    },
    sheriff: {
        model: 'sheriff',
        name: 'Sheriff Cruiser',
        brand: 'Vapid',
        category: VehicleCategory.EMERGENCY,
    },
    sheriff2: {
        model: 'sheriff2',
        name: 'Sheriff SUV',
        brand: 'Declasse',
        category: VehicleCategory.EMERGENCY,
    },
    // Helicopters vehicles (31 vehicles)
    akula: {
        model: 'akula',
        name: 'Akula',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    annihilator: {
        model: 'annihilator',
        name: 'Annihilator',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    annihilator2: {
        model: 'annihilator2',
        name: 'Annihilator Stealth',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    buzzard: {
        model: 'buzzard',
        name: 'Buzzard Attack Chopper',
        brand: 'Nagasaki',
        category: VehicleCategory.HELICOPTERS,
    },
    buzzard2: {
        model: 'buzzard2',
        name: 'Buzzard',
        brand: 'Nagasaki',
        category: VehicleCategory.HELICOPTERS,
    },
    cargobob: {
        model: 'cargobob',
        name: 'Cargobob',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    cargobob2: {
        model: 'cargobob2',
        name: 'Cargobob (Jetsam)',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    cargobob3: {
        model: 'cargobob3',
        name: 'Cargobob (Trevor Philips Enterprises)',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    cargobob4: {
        model: 'cargobob4',
        name: 'Cargobob (Drop Zone)',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    cargobob5: {
        model: 'cargobob5',
        name: 'DH-7 Iron Mule',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    conada: {
        model: 'conada',
        name: 'Conada',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    conada2: {
        model: 'conada2',
        name: 'Weaponized Conada',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    frogger: {
        model: 'frogger',
        name: 'Frogger',
        brand: 'Maibatsu',
        category: VehicleCategory.HELICOPTERS,
    },
    frogger2: {
        model: 'frogger2',
        name: 'Frogger (Trevor Philips Enterprises)',
        brand: 'Maibatsu',
        category: VehicleCategory.HELICOPTERS,
    },
    havok: {
        model: 'havok',
        name: 'Havok',
        brand: 'Nagasaki',
        category: VehicleCategory.HELICOPTERS,
    },
    hunter: {
        model: 'hunter',
        name: 'FH-1 Hunter',
        brand: '',
        category: VehicleCategory.HELICOPTERS,
    },
    maverick: {
        model: 'maverick',
        name: 'Maverick',
        brand: 'Western',
        category: VehicleCategory.HELICOPTERS,
    },
    polmav: {
        model: 'polmav',
        name: 'Police Maverick',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    polterminus: {
        model: 'polterminus',
        name: 'Terminus Patrol',
        brand: 'Canis',
        category: VehicleCategory.HELICOPTERS,
    },
    savage: {
        model: 'savage',
        name: 'Savage',
        brand: '',
        category: VehicleCategory.HELICOPTERS,
    },
    seasparrow: {
        model: 'seasparrow',
        name: 'Sea Sparrow',
        brand: '',
        category: VehicleCategory.HELICOPTERS,
    },
    seasparrow2: {
        model: 'seasparrow2',
        name: 'Sparrow',
        brand: '',
        category: VehicleCategory.HELICOPTERS,
    },
    seasparrow3: {
        model: 'seasparrow3',
        name: 'Sparrow (Prop)',
        brand: '',
        category: VehicleCategory.HELICOPTERS,
    },
    skylift: {
        model: 'skylift',
        name: 'Skylift',
        brand: 'HVY',
        category: VehicleCategory.HELICOPTERS,
    },
    supervolito: {
        model: 'supervolito',
        name: 'SuperVolito',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    supervolito2: {
        model: 'supervolito2',
        name: 'SuperVolito Carbon',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    swift: {
        model: 'swift',
        name: 'Swift',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    swift2: {
        model: 'swift2',
        name: 'Swift Deluxe',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    valkyrie: {
        model: 'valkyrie',
        name: 'Valkyrie',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    valkyrie2: {
        model: 'valkyrie2',
        name: 'Valkyrie MOD.0',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    volatus: {
        model: 'volatus',
        name: 'Volatus',
        brand: 'Buckingham',
        category: VehicleCategory.HELICOPTERS,
    },
    // Industrial vehicles (11 vehicles)
    bulldozer: {
        model: 'bulldozer',
        name: 'Dozer',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    cutter: {
        model: 'cutter',
        name: 'Cutter',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    dump: {
        model: 'dump',
        name: 'Dump',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    flatbed: {
        model: 'flatbed',
        name: 'Flatbed',
        brand: 'MTL',
        category: VehicleCategory.INDUSTRIAL,
    },
    guardian: {
        model: 'guardian',
        name: 'Guardian',
        brand: 'Vapid',
        category: VehicleCategory.INDUSTRIAL,
    },
    handler: {
        model: 'handler',
        name: 'Dock Handler',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    mixer: {
        model: 'mixer',
        name: 'Mixer',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    mixer2: {
        model: 'mixer2',
        name: 'Mixer',
        brand: 'HVY',
        category: VehicleCategory.INDUSTRIAL,
    },
    rubble: {
        model: 'rubble',
        name: 'Rubble',
        brand: 'JoBuilt',
        category: VehicleCategory.INDUSTRIAL,
    },
    tiptruck: {
        model: 'tiptruck',
        name: 'Tipper',
        brand: 'Brute',
        category: VehicleCategory.INDUSTRIAL,
    },
    tiptruck2: {
        model: 'tiptruck2',
        name: 'Tipper',
        brand: 'Brute',
        category: VehicleCategory.INDUSTRIAL,
    },
    // Military vehicles (17 vehicles)
    apc: {
        model: 'apc',
        name: 'APC',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    barracks: {
        model: 'barracks',
        name: 'Barracks',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    barracks2: {
        model: 'barracks2',
        name: 'Barracks Semi',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    barracks3: {
        model: 'barracks3',
        name: 'Barracks',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    barrage: {
        model: 'barrage',
        name: 'Barrage',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    chernobog: {
        model: 'chernobog',
        name: 'Chernobog',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    crusader: {
        model: 'crusader',
        name: 'Crusader',
        brand: 'Canis',
        category: VehicleCategory.MILITARY,
    },
    halftrack: {
        model: 'halftrack',
        name: 'Half-track',
        brand: 'Bravado',
        category: VehicleCategory.MILITARY,
    },
    khanjali: {
        model: 'khanjali',
        name: 'TM-02 Khanjali',
        brand: '',
        category: VehicleCategory.MILITARY,
    },
    minitank: {
        model: 'minitank',
        name: 'Invade and Persuade Tank',
        brand: '',
        category: VehicleCategory.MILITARY,
    },
    rhino: {
        model: 'rhino',
        name: 'Rhino Tank',
        brand: '',
        category: VehicleCategory.MILITARY,
    },
    scarab: {
        model: 'scarab',
        name: 'Apocalypse Scarab',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    scarab2: {
        model: 'scarab2',
        name: 'Future Shock Scarab',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    scarab3: {
        model: 'scarab3',
        name: 'Nightmare Scarab',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    thruster: {
        model: 'thruster',
        name: 'Thruster',
        brand: 'Mammoth',
        category: VehicleCategory.MILITARY,
    },
    trailersmall2: {
        model: 'trailersmall2',
        name: 'Anti-Aircraft Trailer',
        brand: 'Vom Feuer',
        category: VehicleCategory.MILITARY,
    },
    vetir: {
        model: 'vetir',
        name: 'Vetir',
        brand: 'HVY',
        category: VehicleCategory.MILITARY,
    },
    // Motorcycles vehicles (59 vehicles)
    akuma: {
        model: 'akuma',
        name: 'Akuma',
        brand: 'Dinka',
        category: VehicleCategory.MOTORCYCLES,
    },
    avarus: {
        model: 'avarus',
        name: 'Avarus',
        brand: 'LCC',
        category: VehicleCategory.MOTORCYCLES,
    },
    bagger: {
        model: 'bagger',
        name: 'Bagger',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    bati: {
        model: 'bati',
        name: 'Bati 801',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    bati2: {
        model: 'bati2',
        name: 'Bati 801RR',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    bf400: {
        model: 'bf400',
        name: 'BF400',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    carbonrs: {
        model: 'carbonrs',
        name: 'Carbon RS',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    chimera: {
        model: 'chimera',
        name: 'Chimera',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    cliffhanger: {
        model: 'cliffhanger',
        name: 'Cliffhanger',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    daemon: {
        model: 'daemon',
        name: 'Daemon',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    daemon2: {
        model: 'daemon2',
        name: 'Daemon Custom',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    deathbike: {
        model: 'deathbike',
        name: 'Apocalypse Deathbike',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    deathbike2: {
        model: 'deathbike2',
        name: 'Future Shock Deathbike',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    deathbike3: {
        model: 'deathbike3',
        name: 'Nightmare Deathbike',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    defiler: {
        model: 'defiler',
        name: 'Defiler',
        brand: 'Shitzu',
        category: VehicleCategory.MOTORCYCLES,
    },
    diablous: {
        model: 'diablous',
        name: 'Diablous',
        brand: 'Principe',
        category: VehicleCategory.MOTORCYCLES,
    },
    diablous2: {
        model: 'diablous2',
        name: 'Diablous Custom',
        brand: 'Principe',
        category: VehicleCategory.MOTORCYCLES,
    },
    double: {
        model: 'double',
        name: 'Double-T',
        brand: 'Dinka',
        category: VehicleCategory.MOTORCYCLES,
    },
    enduro: {
        model: 'enduro',
        name: 'Enduro',
        brand: 'Dinka',
        category: VehicleCategory.MOTORCYCLES,
    },
    esskey: {
        model: 'esskey',
        name: 'Esskey',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    faggio: {
        model: 'faggio',
        name: 'Faggio Sport',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    faggio2: {
        model: 'faggio2',
        name: 'Faggio Sport',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    faggio3: {
        model: 'faggio3',
        name: 'Faggio Mod',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    fcr: {
        model: 'fcr',
        name: 'FCR 1000',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    fcr2: {
        model: 'fcr2',
        name: 'FCR 1000 Custom',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    gargoyle: {
        model: 'gargoyle',
        name: 'Gargoyle',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    hakuchou: {
        model: 'hakuchou',
        name: 'Hakuchou',
        brand: 'Shitzu',
        category: VehicleCategory.MOTORCYCLES,
    },
    hakuchou2: {
        model: 'hakuchou2',
        name: 'Hakuchou Drag',
        brand: 'Shitzu',
        category: VehicleCategory.MOTORCYCLES,
    },
    hexer: {
        model: 'hexer',
        name: 'Hexer',
        brand: 'LCC',
        category: VehicleCategory.MOTORCYCLES,
    },
    innovation: {
        model: 'innovation',
        name: 'Innovation',
        brand: 'LCC',
        category: VehicleCategory.MOTORCYCLES,
    },
    lectro: {
        model: 'lectro',
        name: 'Lectro',
        brand: 'Principe',
        category: VehicleCategory.MOTORCYCLES,
    },
    manchez: {
        model: 'manchez',
        name: 'Manchez',
        brand: 'Maibatsu',
        category: VehicleCategory.MOTORCYCLES,
    },
    manchez2: {
        model: 'manchez2',
        name: 'Manchez Scout',
        brand: 'Maibatsu',
        category: VehicleCategory.MOTORCYCLES,
    },
    manchez3: {
        model: 'manchez3',
        name: 'Manchez Scout C',
        brand: 'Maibatsu',
        category: VehicleCategory.MOTORCYCLES,
    },
    nemesis: {
        model: 'nemesis',
        name: 'Nemesis',
        brand: 'Principe',
        category: VehicleCategory.MOTORCYCLES,
    },
    nightblade: {
        model: 'nightblade',
        name: 'Nightblade',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    oppressor: {
        model: 'oppressor',
        name: 'Oppressor',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    oppressor2: {
        model: 'oppressor2',
        name: 'Oppressor Mk II',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    pcj: {
        model: 'pcj',
        name: 'PCJ-600',
        brand: 'Shitzu',
        category: VehicleCategory.MOTORCYCLES,
    },
    pizzaboy: {
        model: 'pizzaboy',
        name: 'Pizza Boy',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    powersurge: {
        model: 'powersurge',
        name: 'Powersurge',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    ratbike: {
        model: 'ratbike',
        name: 'Rat Bike',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    reever: {
        model: 'reever',
        name: 'Reever',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    rrocket: {
        model: 'rrocket',
        name: 'Rampant Rocket',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    ruffian: {
        model: 'ruffian',
        name: 'Ruffian',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    sanchez: {
        model: 'sanchez',
        name: 'Sanchez (livery)',
        brand: 'Maibatsu',
        category: VehicleCategory.MOTORCYCLES,
    },
    sanchez2: {
        model: 'sanchez2',
        name: 'Sanchez',
        brand: 'Maibatsu',
        category: VehicleCategory.MOTORCYCLES,
    },
    sanctus: {
        model: 'sanctus',
        name: 'Sanctus',
        brand: 'LCC',
        category: VehicleCategory.MOTORCYCLES,
    },
    shinobi: {
        model: 'shinobi',
        name: 'Shinobi',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    shotaro: {
        model: 'shotaro',
        name: 'Shotaro Concept',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    sovereign: {
        model: 'sovereign',
        name: 'Sovereign',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    stryder: {
        model: 'stryder',
        name: 'Stryder',
        brand: 'Nagasaki',
        category: VehicleCategory.MOTORCYCLES,
    },
    thrust: {
        model: 'thrust',
        name: 'Thrust',
        brand: 'Dinka',
        category: VehicleCategory.MOTORCYCLES,
    },
    vader: {
        model: 'vader',
        name: 'Vader',
        brand: 'Shitzu',
        category: VehicleCategory.MOTORCYCLES,
    },
    vindicator: {
        model: 'vindicator',
        name: 'Vindicator',
        brand: 'Dinka',
        category: VehicleCategory.MOTORCYCLES,
    },
    vortex: {
        model: 'vortex',
        name: 'Vortex',
        brand: 'Pegassi',
        category: VehicleCategory.MOTORCYCLES,
    },
    wolfsbane: {
        model: 'wolfsbane',
        name: 'Wolfsbane',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    zombiea: {
        model: 'zombiea',
        name: 'Zombie Bobber',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    zombieb: {
        model: 'zombieb',
        name: 'Zombie Chopper',
        brand: 'Western',
        category: VehicleCategory.MOTORCYCLES,
    },
    // Muscle vehicles (90 vehicles)
    blade: {
        model: 'blade',
        name: 'Blade',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    brigham: {
        model: 'brigham',
        name: 'Brigham',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    broadway: {
        model: 'broadway',
        name: 'Broadway',
        brand: 'Classique',
        category: VehicleCategory.MUSCLE,
    },
    buccaneer: {
        model: 'buccaneer',
        name: 'Buccaneer',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    buccaneer2: {
        model: 'buccaneer2',
        name: 'Buccaneer Custom',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    buffalo4: {
        model: 'buffalo4',
        name: 'Buffalo STX',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    buffalo5: {
        model: 'buffalo5',
        name: 'Buffalo EVX',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    chino: {
        model: 'chino',
        name: 'Chino',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    chino2: {
        model: 'chino2',
        name: 'Chino Custom',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    clique: {
        model: 'clique',
        name: 'Clique',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    clique2: {
        model: 'clique2',
        name: 'Clique Wagon',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    coquette3: {
        model: 'coquette3',
        name: 'Coquette BlackFin',
        brand: 'Invetero',
        category: VehicleCategory.MUSCLE,
    },
    deviant: {
        model: 'deviant',
        name: 'Deviant',
        brand: 'Schyster',
        category: VehicleCategory.MUSCLE,
    },
    dominator: {
        model: 'dominator',
        name: 'Dominator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator2: {
        model: 'dominator2',
        name: 'Pisswasser Dominator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator3: {
        model: 'dominator3',
        name: 'Dominator GTX',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator4: {
        model: 'dominator4',
        name: 'Dominator Arena',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator5: {
        model: 'dominator5',
        name: 'Future Shock Dominator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator6: {
        model: 'dominator6',
        name: 'Nightmare Dominator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator7: {
        model: 'dominator7',
        name: 'Dominator ASP',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator8: {
        model: 'dominator8',
        name: 'Dominator GTT',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator9: {
        model: 'dominator9',
        name: 'Dominator GT',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    dominator10: {
        model: 'dominator10',
        name: 'Dominator FX',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    driftyosemite: {
        model: 'driftyosemite',
        name: 'Drift Yosemite',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    dukes: {
        model: 'dukes',
        name: 'Dukes',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    dukes2: {
        model: 'dukes2',
        name: 'Dukes Nightrider',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    dukes3: {
        model: 'dukes3',
        name: 'Beater Dukes',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    ellie: {
        model: 'ellie',
        name: 'Ellie',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    eudora: {
        model: 'eudora',
        name: 'Eudora',
        brand: 'Willard',
        category: VehicleCategory.MUSCLE,
    },
    faction: {
        model: 'faction',
        name: 'Faction',
        brand: 'Willard',
        category: VehicleCategory.MUSCLE,
    },
    faction2: {
        model: 'faction2',
        name: 'Faction Rider',
        brand: 'Willard',
        category: VehicleCategory.MUSCLE,
    },
    faction3: {
        model: 'faction3',
        name: 'Faction Custom Donk',
        brand: 'Willard',
        category: VehicleCategory.MUSCLE,
    },
    gauntlet: {
        model: 'gauntlet',
        name: 'Gauntlet',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    gauntlet2: {
        model: 'gauntlet2',
        name: 'Redwood Gauntlet',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    gauntlet3: {
        model: 'gauntlet3',
        name: 'Gauntlet Classic',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    gauntlet4: {
        model: 'gauntlet4',
        name: 'Gauntlet Hellfire',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    gauntlet5: {
        model: 'gauntlet5',
        name: 'Gauntlet Classic Custom',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    greenwood: {
        model: 'greenwood',
        name: 'Greenwood',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    hermes: {
        model: 'hermes',
        name: 'Hermes',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    hotknife: {
        model: 'hotknife',
        name: 'Hotknife',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    hustler: {
        model: 'hustler',
        name: 'Hustler',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    impaler: {
        model: 'impaler',
        name: 'Impaler',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    impaler2: {
        model: 'impaler2',
        name: 'Apocalypse Impaler',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    impaler3: {
        model: 'impaler3',
        name: 'Future Shock Impaler',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    impaler4: {
        model: 'impaler4',
        name: 'Nightmare Impaler',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    impaler6: {
        model: 'impaler6',
        name: 'Impaler LX',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    imperator: {
        model: 'imperator',
        name: 'Apocalypse Imperator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    imperator2: {
        model: 'imperator2',
        name: 'Future Shock Imperator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    imperator3: {
        model: 'imperator3',
        name: 'Nightmare Imperator',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    lurcher: {
        model: 'lurcher',
        name: 'Lurcher',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    manana2: {
        model: 'manana2',
        name: 'Manana Custom',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    moonbeam: {
        model: 'moonbeam',
        name: 'Moonbeam',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    moonbeam2: {
        model: 'moonbeam2',
        name: 'Moonbeam Custom',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    nightshade: {
        model: 'nightshade',
        name: 'Nightshade',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    peyote2: {
        model: 'peyote2',
        name: 'Peyote Gasser',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    phoenix: {
        model: 'phoenix',
        name: 'Phoenix',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    picador: {
        model: 'picador',
        name: 'Picador',
        brand: 'Cheval',
        category: VehicleCategory.MUSCLE,
    },
    ratloader: {
        model: 'ratloader',
        name: 'Rat-Loader',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    ratloader2: {
        model: 'ratloader2',
        name: 'Rat-Truck',
        brand: 'Bravado',
        category: VehicleCategory.MUSCLE,
    },
    ruiner: {
        model: 'ruiner',
        name: 'Ruiner',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    ruiner2: {
        model: 'ruiner2',
        name: 'Ruiner 2000',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    ruiner3: {
        model: 'ruiner3',
        name: 'Ruiner (Wrecked)',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    ruiner4: {
        model: 'ruiner4',
        name: 'Ruiner ZZ-8',
        brand: 'Imponte',
        category: VehicleCategory.MUSCLE,
    },
    sabregt: {
        model: 'sabregt',
        name: 'Sabre Turbo',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    sabregt2: {
        model: 'sabregt2',
        name: 'Sabre GT',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    slamvan: {
        model: 'slamvan',
        name: 'Slam Van',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    slamvan2: {
        model: 'slamvan2',
        name: 'Lost Slam Van',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    slamvan3: {
        model: 'slamvan3',
        name: 'Slam Van Custom',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    slamvan4: {
        model: 'slamvan4',
        name: 'Apocalypse Slamvan',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    slamvan5: {
        model: 'slamvan5',
        name: 'Future Shock Slamvan',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    slamvan6: {
        model: 'slamvan6',
        name: 'Nightmare Slamvan',
        brand: 'Vapid',
        category: VehicleCategory.MUSCLE,
    },
    stalion: {
        model: 'stalion',
        name: 'Stallion',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    stalion2: {
        model: 'stalion2',
        name: 'Stallion Burgershot',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    tahoma: {
        model: 'tahoma',
        name: 'Tahoma Coupe',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    tampa: {
        model: 'tampa',
        name: 'Tampa',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    tampa3: {
        model: 'tampa3',
        name: 'Weaponized Tampa',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    tulip: {
        model: 'tulip',
        name: 'Tulip',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    tulip2: {
        model: 'tulip2',
        name: 'Tulip M-100',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    vamos: {
        model: 'vamos',
        name: 'Vamos',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    vigero: {
        model: 'vigero',
        name: 'Vigero',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    vigero2: {
        model: 'vigero2',
        name: 'Vigero ZX',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    vigero3: {
        model: 'vigero3',
        name: 'Vigero ZX Convertible',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    virgo: {
        model: 'virgo',
        name: 'Virgo',
        brand: 'Albany',
        category: VehicleCategory.MUSCLE,
    },
    virgo2: {
        model: 'virgo2',
        name: 'Virgo Custom Classic',
        brand: 'Dundreary',
        category: VehicleCategory.MUSCLE,
    },
    virgo3: {
        model: 'virgo3',
        name: 'Virgo Custom Classic',
        brand: 'Dundreary',
        category: VehicleCategory.MUSCLE,
    },
    voodoo: {
        model: 'voodoo',
        name: 'Voodoo Custom',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    voodoo2: {
        model: 'voodoo2',
        name: 'Voodoo',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    weevil2: {
        model: 'weevil2',
        name: 'Weevil Custom',
        brand: 'BF',
        category: VehicleCategory.MUSCLE,
    },
    yosemite: {
        model: 'yosemite',
        name: 'Yosemite',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    yosemite2: {
        model: 'yosemite2',
        name: 'Yosemite Drift',
        brand: 'Declasse',
        category: VehicleCategory.MUSCLE,
    },
    // Offroad vehicles (67 vehicles)
    bfinjection: {
        model: 'bfinjection',
        name: 'Injection',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    bifta: {
        model: 'bifta',
        name: 'Bifta',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    blazer: {
        model: 'blazer',
        name: 'Blazer',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    blazer2: {
        model: 'blazer2',
        name: 'Blazer Lifeguard',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    blazer3: {
        model: 'blazer3',
        name: 'Hot Rod Blazer',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    blazer4: {
        model: 'blazer4',
        name: 'Blazer Sport',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    blazer5: {
        model: 'blazer5',
        name: 'Blazer Aqua',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    bodhi2: {
        model: 'bodhi2',
        name: 'Bodhi',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    boor: {
        model: 'boor',
        name: 'Boor',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    brawler: {
        model: 'brawler',
        name: 'Brawler',
        brand: 'Coil',
        category: VehicleCategory.OFFROAD,
    },
    bruiser: {
        model: 'bruiser',
        name: 'Apocalypse Bruiser',
        brand: 'Benefactor',
        category: VehicleCategory.OFFROAD,
    },
    bruiser2: {
        model: 'bruiser2',
        name: 'Future Shock Bruiser',
        brand: 'Benefactor',
        category: VehicleCategory.OFFROAD,
    },
    bruiser3: {
        model: 'bruiser3',
        name: 'Nightmare Bruiser',
        brand: 'Benefactor',
        category: VehicleCategory.OFFROAD,
    },
    brutus: {
        model: 'brutus',
        name: 'Apocalypse Brutus',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    brutus2: {
        model: 'brutus2',
        name: 'Future Shock Brutus',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    brutus3: {
        model: 'brutus3',
        name: 'Nightmare Brutus',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    caracara: {
        model: 'caracara',
        name: 'Caracara',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    caracara2: {
        model: 'caracara2',
        name: 'Caracara 4x4',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    dloader: {
        model: 'dloader',
        name: 'Duneloader',
        brand: 'Bravado',
        category: VehicleCategory.OFFROAD,
    },
    draugur: {
        model: 'draugur',
        name: 'Draugur',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    dubsta3: {
        model: 'dubsta3',
        name: 'Dubsta 6x6',
        brand: 'Benefactor',
        category: VehicleCategory.OFFROAD,
    },
    dune: {
        model: 'dune',
        name: 'Dune Buggy',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    dune2: {
        model: 'dune2',
        name: 'Space Docker',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    dune3: {
        model: 'dune3',
        name: 'Dune FAV',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    dune4: {
        model: 'dune4',
        name: 'Ramp Buggy',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    dune5: {
        model: 'dune5',
        name: 'Ramp Buggy',
        brand: 'BF',
        category: VehicleCategory.OFFROAD,
    },
    everon: {
        model: 'everon',
        name: 'Everon',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    freecrawler: {
        model: 'freecrawler',
        name: 'Freecrawler',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    hellion: {
        model: 'hellion',
        name: 'Hellion',
        brand: 'Annis',
        category: VehicleCategory.OFFROAD,
    },
    insurgent: {
        model: 'insurgent',
        name: 'Insurgent Pick-Up',
        brand: 'HVY',
        category: VehicleCategory.OFFROAD,
    },
    insurgent2: {
        model: 'insurgent2',
        name: 'Insurgent',
        brand: 'HVY',
        category: VehicleCategory.OFFROAD,
    },
    insurgent3: {
        model: 'insurgent3',
        name: 'Insurgent Pick-Up Custom',
        brand: 'HVY',
        category: VehicleCategory.OFFROAD,
    },
    kalahari: {
        model: 'kalahari',
        name: 'Kalahari',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    kamacho: {
        model: 'kamacho',
        name: 'Kamacho',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    l35: {
        model: 'l35',
        name: 'Walton L35',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    marshall: {
        model: 'marshall',
        name: 'Marshall',
        brand: 'Cheval',
        category: VehicleCategory.OFFROAD,
    },
    menacer: {
        model: 'menacer',
        name: 'Menacer',
        brand: 'HVY',
        category: VehicleCategory.OFFROAD,
    },
    mesa3: {
        model: 'mesa3',
        name: 'Mesa (Merryweather)',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    monster: {
        model: 'monster',
        name: 'Liberator',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    monster3: {
        model: 'monster3',
        name: 'Apocalypse Sasquatch',
        brand: 'Bravado',
        category: VehicleCategory.OFFROAD,
    },
    monster4: {
        model: 'monster4',
        name: 'Future Shock Sasquatch',
        brand: 'Bravado',
        category: VehicleCategory.OFFROAD,
    },
    monster5: {
        model: 'monster5',
        name: 'Nightmare Sasquatch',
        brand: 'Bravado',
        category: VehicleCategory.OFFROAD,
    },
    monstrociti: {
        model: 'monstrociti',
        name: 'MonstroCiti',
        brand: 'Maibatsu',
        category: VehicleCategory.OFFROAD,
    },
    nightshark: {
        model: 'nightshark',
        name: 'Nightshark',
        brand: 'HVY',
        category: VehicleCategory.OFFROAD,
    },
    outlaw: {
        model: 'outlaw',
        name: 'Outlaw',
        brand: 'Nagasaki',
        category: VehicleCategory.OFFROAD,
    },
    patriot3: {
        model: 'patriot3',
        name: 'Mil-Spec Patriot',
        brand: 'Mammoth',
        category: VehicleCategory.OFFROAD,
    },
    rancherxl: {
        model: 'rancherxl',
        name: 'Rancher XL',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    rancherxl2: {
        model: 'rancherxl2',
        name: 'Rancher XL (Snow)',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    ratel: {
        model: 'ratel',
        name: 'Ratel',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    rcbandito: {
        model: 'rcbandito',
        name: 'RC Bandito',
        brand: '',
        category: VehicleCategory.OFFROAD,
    },
    rebel: {
        model: 'rebel',
        name: 'Rusty Rebel',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    rebel2: {
        model: 'rebel2',
        name: 'Rebel',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    riata: {
        model: 'riata',
        name: 'Riata',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    sandking: {
        model: 'sandking',
        name: 'Sandking',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    sandking2: {
        model: 'sandking2',
        name: 'Sandking SWB',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    technical: {
        model: 'technical',
        name: 'Technical',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    technical2: {
        model: 'technical2',
        name: 'Technical Aqua',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    technical3: {
        model: 'technical3',
        name: 'Technical Custom',
        brand: 'Karin',
        category: VehicleCategory.OFFROAD,
    },
    terminus: {
        model: 'terminus',
        name: 'Terminus',
        brand: 'Canis',
        category: VehicleCategory.OFFROAD,
    },
    trophytruck: {
        model: 'trophytruck',
        name: 'Trophy Truck',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    trophytruck2: {
        model: 'trophytruck2',
        name: 'Desert Raid',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    vagrant: {
        model: 'vagrant',
        name: 'Vagrant',
        brand: 'Maxwell',
        category: VehicleCategory.OFFROAD,
    },
    verus: {
        model: 'verus',
        name: 'Verus',
        brand: 'Dinka',
        category: VehicleCategory.OFFROAD,
    },
    winky: {
        model: 'winky',
        name: 'Winky',
        brand: 'Vapid',
        category: VehicleCategory.OFFROAD,
    },
    yosemite3: {
        model: 'yosemite3',
        name: 'Yosemite Rancher',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    yosemite1500: {
        model: 'yosemite1500',
        name: 'Yosemite 1500',
        brand: 'Declasse',
        category: VehicleCategory.OFFROAD,
    },
    zhaba: {
        model: 'zhaba',
        name: 'Zhaba',
        brand: 'RUNE',
        category: VehicleCategory.OFFROAD,
    },
    // Openwheel vehicles (4 vehicles)
    formula: {
        model: 'formula',
        name: 'PR4',
        brand: 'Progen',
        category: VehicleCategory.OPENWHEEL,
    },
    formula2: {
        model: 'formula2',
        name: 'R88',
        brand: 'Ocelot',
        category: VehicleCategory.OPENWHEEL,
    },
    openwheel1: {
        model: 'openwheel1',
        name: 'BR8',
        brand: 'Benefactor',
        category: VehicleCategory.OPENWHEEL,
    },
    openwheel2: {
        model: 'openwheel2',
        name: 'DR1',
        brand: 'Declasse',
        category: VehicleCategory.OPENWHEEL,
    },
    // Planes vehicles (46 vehicles)
    alkonost: {
        model: 'alkonost',
        name: 'RO-86 Alkonost',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    alphaz1: {
        model: 'alphaz1',
        name: 'Alpha-Z1',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    avenger: {
        model: 'avenger',
        name: 'Avenger',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    avenger2: {
        model: 'avenger2',
        name: 'Avenger (Prop)',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    avenger3: {
        model: 'avenger3',
        name: 'Avenger (Upgraded)',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    avenger4: {
        model: 'avenger4',
        name: 'Avenger (Upgraded Prop)',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    besra: {
        model: 'besra',
        name: 'Besra',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    blimp: {
        model: 'blimp',
        name: 'Atomic Blimp',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    blimp2: {
        model: 'blimp2',
        name: 'Xero Blimp',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    blimp3: {
        model: 'blimp3',
        name: 'Blimp',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    bombushka: {
        model: 'bombushka',
        name: 'RM-10 Bombushka',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    cargoplane: {
        model: 'cargoplane',
        name: 'Cargo Plane',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    cargoplane2: {
        model: 'cargoplane2',
        name: 'Cargo Plane',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    cuban800: {
        model: 'cuban800',
        name: 'Cuban 800',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    dodo: {
        model: 'dodo',
        name: 'Dodo',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    duster: {
        model: 'duster',
        name: 'Duster',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    duster2: {
        model: 'duster2',
        name: 'Duster 300-H',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    howard: {
        model: 'howard',
        name: 'Howard NX-25',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    hydra: {
        model: 'hydra',
        name: 'Hydra',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    jet: {
        model: 'jet',
        name: 'Jet',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    lazer: {
        model: 'lazer',
        name: 'P-996 LAZER',
        brand: 'Jobuilt',
        category: VehicleCategory.PLANES,
    },
    luxor: {
        model: 'luxor',
        name: 'Luxor',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    luxor2: {
        model: 'luxor2',
        name: 'Luxor Deluxe',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    mammatus: {
        model: 'mammatus',
        name: 'Mammatus',
        brand: 'JoBuilt',
        category: VehicleCategory.PLANES,
    },
    microlight: {
        model: 'microlight',
        name: 'Ultralight',
        brand: 'Nagasaki',
        category: VehicleCategory.PLANES,
    },
    miljet: {
        model: 'miljet',
        name: 'Miljet',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    mogul: {
        model: 'mogul',
        name: 'Mogul',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    molotok: {
        model: 'molotok',
        name: 'V-65 Molotok',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    nimbus: {
        model: 'nimbus',
        name: 'Nimbus',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    nokota: {
        model: 'nokota',
        name: 'P-45 Nokota',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    pyro: {
        model: 'pyro',
        name: 'Pyro',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    raiju: {
        model: 'raiju',
        name: 'F-160 Raiju',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    rogue: {
        model: 'rogue',
        name: 'Rogue',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    seabreeze: {
        model: 'seabreeze',
        name: 'Seabreeze',
        brand: 'Western',
        category: VehicleCategory.PLANES,
    },
    shamal: {
        model: 'shamal',
        name: 'Shamal',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    starling: {
        model: 'starling',
        name: 'LF-22 Starling',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    streamer216: {
        model: 'streamer216',
        name: 'Streamer216',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    strikeforce: {
        model: 'strikeforce',
        name: 'B-11 Strikeforce',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    stunt: {
        model: 'stunt',
        name: 'Mallard',
        brand: 'Western Company',
        category: VehicleCategory.PLANES,
    },
    titan: {
        model: 'titan',
        name: 'Titan',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    titan2: {
        model: 'titan2',
        name: 'Titan 250 D',
        brand: 'Eberhard',
        category: VehicleCategory.PLANES,
    },
    tula: {
        model: 'tula',
        name: 'Tula',
        brand: 'Mammoth',
        category: VehicleCategory.PLANES,
    },
    velum: {
        model: 'velum',
        name: 'Velum',
        brand: 'JoBuilt',
        category: VehicleCategory.PLANES,
    },
    velum2: {
        model: 'velum2',
        name: 'Velum',
        brand: 'JoBuilt',
        category: VehicleCategory.PLANES,
    },
    vestra: {
        model: 'vestra',
        name: 'Vestra',
        brand: 'Buckingham',
        category: VehicleCategory.PLANES,
    },
    volatol: {
        model: 'volatol',
        name: 'Volatol',
        brand: '',
        category: VehicleCategory.PLANES,
    },
    // Sedans vehicles (43 vehicles)
    asea: {
        model: 'asea',
        name: 'Asea',
        brand: 'Declasse',
        category: VehicleCategory.SEDANS,
    },
    asea2: {
        model: 'asea2',
        name: 'Asea (Snow)',
        brand: 'Declasse',
        category: VehicleCategory.SEDANS,
    },
    asterope: {
        model: 'asterope',
        name: 'Asterope',
        brand: 'Karin',
        category: VehicleCategory.SEDANS,
    },
    asterope2: {
        model: 'asterope2',
        name: 'Asterope GZ',
        brand: 'Karin',
        category: VehicleCategory.SEDANS,
    },
    chavosv6: {
        model: 'chavosv6',
        name: 'Chavos V6',
        brand: 'Dewbauchee',
        category: VehicleCategory.SEDANS,
    },
    cinquemila: {
        model: 'cinquemila',
        name: 'Cinquemila',
        brand: 'Lampadati',
        category: VehicleCategory.SEDANS,
    },
    cog55: {
        model: 'cog55',
        name: 'Cognoscenti 55',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    cog552: {
        model: 'cog552',
        name: 'Cognoscenti 55 (Armored)',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    cognoscenti: {
        model: 'cognoscenti',
        name: 'Cognoscenti',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    cognoscenti2: {
        model: 'cognoscenti2',
        name: 'Cognoscenti (Armored)',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    deity: {
        model: 'deity',
        name: 'Deity',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    driftvorschlag: {
        model: 'driftvorschlag',
        name: 'Vorschlaghammer (Drift)',
        brand: 'Declasse',
        category: VehicleCategory.SEDANS,
    },
    emperor: {
        model: 'emperor',
        name: 'Emperor',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    emperor2: {
        model: 'emperor2',
        name: 'Emperor (Beater)',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    emperor3: {
        model: 'emperor3',
        name: 'Emperor (Snow)',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    fugitive: {
        model: 'fugitive',
        name: 'Fugitive',
        brand: 'Cheval',
        category: VehicleCategory.SEDANS,
    },
    glendale: {
        model: 'glendale',
        name: 'Glendale',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    glendale2: {
        model: 'glendale2',
        name: 'Glendale Custom',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    impaler5: {
        model: 'impaler5',
        name: 'Impaler SZ',
        brand: 'Declasse',
        category: VehicleCategory.SEDANS,
    },
    ingot: {
        model: 'ingot',
        name: 'Ingot',
        brand: 'Vulcar',
        category: VehicleCategory.SEDANS,
    },
    intruder: {
        model: 'intruder',
        name: 'Intruder',
        brand: 'Karin',
        category: VehicleCategory.SEDANS,
    },
    limo2: {
        model: 'limo2',
        name: 'Turreted Limo',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    premier: {
        model: 'premier',
        name: 'Premier',
        brand: 'Declasse',
        category: VehicleCategory.SEDANS,
    },
    primo: {
        model: 'primo',
        name: 'Primo',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    primo2: {
        model: 'primo2',
        name: 'Primo Custom',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    regina: {
        model: 'regina',
        name: 'Regina',
        brand: 'Dundreary',
        category: VehicleCategory.SEDANS,
    },
    rhinehart: {
        model: 'rhinehart',
        name: 'Rhinehart',
        brand: 'Übermacht',
        category: VehicleCategory.SEDANS,
    },
    romero: {
        model: 'romero',
        name: 'Romero Hearse',
        brand: 'Chariot',
        category: VehicleCategory.SEDANS,
    },
    schafter2: {
        model: 'schafter2',
        name: 'Schafter',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    schafter5: {
        model: 'schafter5',
        name: 'Schafter V12 (Armored)',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    schafter6: {
        model: 'schafter6',
        name: 'Schafter LWB (Armored)',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    stafford: {
        model: 'stafford',
        name: 'Stafford',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    stanier: {
        model: 'stanier',
        name: 'Stanier',
        brand: 'Vapid',
        category: VehicleCategory.SEDANS,
    },
    stratum: {
        model: 'stratum',
        name: 'Stratum',
        brand: 'Zirconium',
        category: VehicleCategory.SEDANS,
    },
    stretch: {
        model: 'stretch',
        name: 'Stretch',
        brand: 'Dundreary',
        category: VehicleCategory.SEDANS,
    },
    superd: {
        model: 'superd',
        name: 'Super Diamond',
        brand: 'Enus',
        category: VehicleCategory.SEDANS,
    },
    surge: {
        model: 'surge',
        name: 'Surge',
        brand: 'Cheval',
        category: VehicleCategory.SEDANS,
    },
    tailgater: {
        model: 'tailgater',
        name: 'Tailgater',
        brand: 'Obey',
        category: VehicleCategory.SEDANS,
    },
    tailgater2: {
        model: 'tailgater2',
        name: 'Tailgater S',
        brand: 'Obey',
        category: VehicleCategory.SEDANS,
    },
    vorschlaghammer: {
        model: 'vorschlaghammer',
        name: 'Vorschlaghammer',
        brand: 'Benefactor',
        category: VehicleCategory.SEDANS,
    },
    warrener: {
        model: 'warrener',
        name: 'Warrener',
        brand: 'Vulcar',
        category: VehicleCategory.SEDANS,
    },
    warrener2: {
        model: 'warrener2',
        name: 'Warrener HKR',
        brand: 'Vulcar',
        category: VehicleCategory.SEDANS,
    },
    washington: {
        model: 'washington',
        name: 'Washington',
        brand: 'Albany',
        category: VehicleCategory.SEDANS,
    },
    // Service vehicles (13 vehicles)
    airbus: {
        model: 'airbus',
        name: 'Airport Bus',
        brand: '',
        category: VehicleCategory.SERVICE,
    },
    brickade: {
        model: 'brickade',
        name: 'Brickade',
        brand: 'MTL',
        category: VehicleCategory.SERVICE,
    },
    brickade2: {
        model: 'brickade2',
        name: 'Brickade 6x6',
        brand: 'MTL',
        category: VehicleCategory.SERVICE,
    },
    bus: {
        model: 'bus',
        name: 'Bus',
        brand: '',
        category: VehicleCategory.SERVICE,
    },
    coach: {
        model: 'coach',
        name: 'Dashound',
        brand: '',
        category: VehicleCategory.SERVICE,
    },
    pbus2: {
        model: 'pbus2',
        name: 'Festival Bus',
        brand: 'Brute',
        category: VehicleCategory.SERVICE,
    },
    rallytruck: {
        model: 'rallytruck',
        name: 'Dune',
        brand: 'MTL',
        category: VehicleCategory.SERVICE,
    },
    rentalbus: {
        model: 'rentalbus',
        name: 'Rental Shuttle Bus',
        brand: 'Brute',
        category: VehicleCategory.SERVICE,
    },
    taxi: {
        model: 'taxi',
        name: 'Taxi',
        brand: 'Vapid',
        category: VehicleCategory.SERVICE,
    },
    tourbus: {
        model: 'tourbus',
        name: 'Tour Bus',
        brand: 'Brute',
        category: VehicleCategory.SERVICE,
    },
    trash: {
        model: 'trash',
        name: 'Trashmaster',
        brand: 'Jobuilt',
        category: VehicleCategory.SERVICE,
    },
    trash2: {
        model: 'trash2',
        name: 'Trashmaster (Heist)',
        brand: 'Jobuilt',
        category: VehicleCategory.SERVICE,
    },
    wastelander: {
        model: 'wastelander',
        name: 'Wastelander',
        brand: 'MTL',
        category: VehicleCategory.SERVICE,
    },
    // Sports vehicles (120 vehicles)
    alpha: {
        model: 'alpha',
        name: 'Alpha',
        brand: 'Albany',
        category: VehicleCategory.SPORTS,
    },
    banshee: {
        model: 'banshee',
        name: 'Banshee',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    banshee3: {
        model: 'banshee3',
        name: 'Banshee GTS',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    bestiagts: {
        model: 'bestiagts',
        name: 'Bestia GTS',
        brand: 'Grotti',
        category: VehicleCategory.SPORTS,
    },
    blista2: {
        model: 'blista2',
        name: 'Blista Compact',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    blista3: {
        model: 'blista3',
        name: 'Blista Go Go Monkey',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    buffalo: {
        model: 'buffalo',
        name: 'Buffalo',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    buffalo2: {
        model: 'buffalo2',
        name: 'Buffalo S',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    buffalo3: {
        model: 'buffalo3',
        name: 'Sprunk Buffalo',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    calico: {
        model: 'calico',
        name: 'Calico GTF',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    carbonizzare: {
        model: 'carbonizzare',
        name: 'Carbonizzare',
        brand: 'Grotti',
        category: VehicleCategory.SPORTS,
    },
    comet2: {
        model: 'comet2',
        name: 'Comet',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    comet3: {
        model: 'comet3',
        name: 'Comet Retro Custom',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    comet4: {
        model: 'comet4',
        name: 'Comet Safari',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    comet5: {
        model: 'comet5',
        name: 'Comet SR',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    comet6: {
        model: 'comet6',
        name: 'Comet S2',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    comet7: {
        model: 'comet7',
        name: 'Comet S2 Cabrio',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    coquette: {
        model: 'coquette',
        name: 'Coquette',
        brand: 'Invetero',
        category: VehicleCategory.SPORTS,
    },
    coquette4: {
        model: 'coquette4',
        name: 'Coquette D10',
        brand: 'Invetero',
        category: VehicleCategory.SPORTS,
    },
    coquette6: {
        model: 'coquette6',
        name: 'Coquette D5',
        brand: 'Invetero',
        category: VehicleCategory.SPORTS,
    },
    corsita: {
        model: 'corsita',
        name: 'Corsita',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTS,
    },
    coureur: {
        model: 'coureur',
        name: 'La Coureuse',
        brand: 'Penaud',
        category: VehicleCategory.SPORTS,
    },
    cypher: {
        model: 'cypher',
        name: 'Cypher',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    drafter: {
        model: 'drafter',
        name: '8F Drafter',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    driftcypher: {
        model: 'driftcypher',
        name: 'Cypher (Drift)',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    drifteuros: {
        model: 'drifteuros',
        name: 'Euros (Drift)',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    driftfuto: {
        model: 'driftfuto',
        name: 'Futo GTX (Drift)',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    driftfuto2: {
        model: 'driftfuto2',
        name: 'Futo (Drift)',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    driftjester: {
        model: 'driftjester',
        name: 'Jester RR (Drift)',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    driftremus: {
        model: 'driftremus',
        name: 'Remus (Drift)',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    driftsentinel: {
        model: 'driftsentinel',
        name: 'Sentinel Classic Widebody (Drift)',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    drifttampa: {
        model: 'drifttampa',
        name: 'Drift Tampa',
        brand: 'Declasse',
        category: VehicleCategory.SPORTS,
    },
    driftzr350: {
        model: 'driftzr350',
        name: 'ZR350 (Drift)',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    elegy: {
        model: 'elegy',
        name: 'Elegy Retro Custom',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    elegy2: {
        model: 'elegy2',
        name: 'Elegy RH8',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    euros: {
        model: 'euros',
        name: 'Euros',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    eurosx32: {
        model: 'eurosx32',
        name: 'Euros X32',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    everon2: {
        model: 'everon2',
        name: 'Hotring Everon',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    envisage: {
        model: 'envisage',
        name: 'Envisage',
        brand: 'Bollokan',
        category: VehicleCategory.SPORTS,
    },
    feltzer2: {
        model: 'feltzer2',
        name: 'Feltzer',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    flashgt: {
        model: 'flashgt',
        name: 'Flash GT',
        brand: 'Vapid',
        category: VehicleCategory.SPORTS,
    },
    furoregt: {
        model: 'furoregt',
        name: 'Furore GT',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTS,
    },
    fusilade: {
        model: 'fusilade',
        name: 'Fusilade',
        brand: 'Schyster',
        category: VehicleCategory.SPORTS,
    },
    futo: {
        model: 'futo',
        name: 'Futo',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    futo2: {
        model: 'futo2',
        name: 'Futo GTX',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    gauntlet6: {
        model: 'gauntlet6',
        name: 'Hotring Hellfire',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    gb200: {
        model: 'gb200',
        name: 'GB 200',
        brand: 'Vapid',
        category: VehicleCategory.SPORTS,
    },
    growler: {
        model: 'growler',
        name: 'Growler',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    hotring: {
        model: 'hotring',
        name: 'Hotring Sabre',
        brand: 'Declasse',
        category: VehicleCategory.SPORTS,
    },
    imorgon: {
        model: 'imorgon',
        name: 'Imorgon',
        brand: 'Överflöd',
        category: VehicleCategory.SPORTS,
    },
    issi7: {
        model: 'issi7',
        name: 'Issi Sport',
        brand: 'Weeny',
        category: VehicleCategory.SPORTS,
    },
    italigto: {
        model: 'italigto',
        name: 'Itali GTO',
        brand: 'Grotti',
        category: VehicleCategory.SPORTS,
    },
    italirsx: {
        model: 'italirsx',
        name: 'Itali RSX',
        brand: 'Grotti',
        category: VehicleCategory.SPORTS,
    },
    jester: {
        model: 'jester',
        name: 'Jester',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    jester2: {
        model: 'jester2',
        name: 'Jester (Racecar)',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    jester3: {
        model: 'jester3',
        name: 'Jester Classic',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    jester4: {
        model: 'jester4',
        name: 'Jester RR',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    jester5: {
        model: 'jester5',
        name: 'Jester RR Widebody',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    jugular: {
        model: 'jugular',
        name: 'Jugular',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTS,
    },
    khamelion: {
        model: 'khamelion',
        name: 'Khamelion',
        brand: 'Hijak',
        category: VehicleCategory.SPORTS,
    },
    komoda: {
        model: 'komoda',
        name: 'Komoda',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTS,
    },
    kuruma: {
        model: 'kuruma',
        name: 'Kuruma',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    kuruma2: {
        model: 'kuruma2',
        name: 'Kuruma (Armored)',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    locust: {
        model: 'locust',
        name: 'Locust',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTS,
    },
    lynx: {
        model: 'lynx',
        name: 'Lynx',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTS,
    },
    massacro: {
        model: 'massacro',
        name: 'Massacro',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    massacro2: {
        model: 'massacro2',
        name: 'Massacro Racecar',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    neo: {
        model: 'neo',
        name: 'Neo',
        brand: 'Vysser',
        category: VehicleCategory.SPORTS,
    },
    neon: {
        model: 'neon',
        name: 'Neon',
        brand: 'Pfister',
        category: VehicleCategory.SPORTS,
    },
    ninef: {
        model: 'ninef',
        name: '9F',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    ninef2: {
        model: 'ninef2',
        name: '9F Cabrio',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    niobe: {
        model: 'niobe',
        name: 'Niobe',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    omnis: {
        model: 'omnis',
        name: 'Omnis',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    omnisegt: {
        model: 'omnisegt',
        name: 'Omnis e-GT',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    panthere: {
        model: 'panthere',
        name: 'Panthere',
        brand: 'Toundra',
        category: VehicleCategory.SPORTS,
    },
    paragon: {
        model: 'paragon',
        name: 'Paragon',
        brand: 'Enus',
        category: VehicleCategory.SPORTS,
    },
    paragon2: {
        model: 'paragon2',
        name: 'Paragon R (Armored)',
        brand: 'Enus',
        category: VehicleCategory.SPORTS,
    },
    paragon3: {
        model: 'paragon3',
        name: 'Paragon S',
        brand: 'Enus',
        category: VehicleCategory.SPORTS,
    },
    pariah: {
        model: 'pariah',
        name: 'Pariah',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTS,
    },
    penumbra: {
        model: 'penumbra',
        name: 'Penumbra',
        brand: 'Maibatsu',
        category: VehicleCategory.SPORTS,
    },
    penumbra2: {
        model: 'penumbra2',
        name: 'Penumbra FF',
        brand: 'Maibatsu',
        category: VehicleCategory.SPORTS,
    },
    r300: {
        model: 'r300',
        name: '300R',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    raiden: {
        model: 'raiden',
        name: 'Raiden',
        brand: 'Coil',
        category: VehicleCategory.SPORTS,
    },
    rapidgt: {
        model: 'rapidgt',
        name: 'Rapid GT',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    rapidgt2: {
        model: 'rapidgt2',
        name: 'Rapid GT Cabrio',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    raptor: {
        model: 'raptor',
        name: 'Raptor',
        brand: 'BF',
        category: VehicleCategory.SPORTS,
    },
    remus: {
        model: 'remus',
        name: 'Remus',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    revolter: {
        model: 'revolter',
        name: 'Revolter',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    rt3000: {
        model: 'rt3000',
        name: 'RT3000',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    ruston: {
        model: 'ruston',
        name: 'Ruston',
        brand: 'Hijak',
        category: VehicleCategory.SPORTS,
    },
    schafter3: {
        model: 'schafter3',
        name: 'Schafter V12',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    schafter4: {
        model: 'schafter4',
        name: 'Schafter LWB',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    schlagen: {
        model: 'schlagen',
        name: 'Schlagen GT',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    schwarzer: {
        model: 'schwarzer',
        name: 'Schwartzer',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    sentinel3: {
        model: 'sentinel3',
        name: 'Sentinel Classic',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    sentinel4: {
        model: 'sentinel4',
        name: 'Sentinel Classic Widebody',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTS,
    },
    seven70: {
        model: 'seven70',
        name: 'Seven-70',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    sm722: {
        model: 'sm722',
        name: 'SM722',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    specter: {
        model: 'specter',
        name: 'Specter',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    specter2: {
        model: 'specter2',
        name: 'Specter Custom',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTS,
    },
    stingertt: {
        model: 'stingertt',
        name: 'Itali GTO Stinger TT',
        brand: 'Grotti',
        category: VehicleCategory.SPORTS,
    },
    streiter: {
        model: 'streiter',
        name: 'Streiter',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    sugoi: {
        model: 'sugoi',
        name: 'Sugoi',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    sultan: {
        model: 'sultan',
        name: 'Sultan',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    sultan2: {
        model: 'sultan2',
        name: 'Sultan Custom',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    sultan3: {
        model: 'sultan3',
        name: 'Sultan Classic Custom',
        brand: 'Karin',
        category: VehicleCategory.SPORTS,
    },
    surano: {
        model: 'surano',
        name: 'Surano',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTS,
    },
    tampa2: {
        model: 'tampa2',
        name: 'Drift Tampa',
        brand: 'Declasse',
        category: VehicleCategory.SPORTS,
    },
    tenf: {
        model: 'tenf',
        name: '10F',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    tenf2: {
        model: 'tenf2',
        name: '10F Widebody',
        brand: 'Obey',
        category: VehicleCategory.SPORTS,
    },
    tropos: {
        model: 'tropos',
        name: 'Tropos Rallye',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTS,
    },
    vectre: {
        model: 'vectre',
        name: 'Vectre',
        brand: 'Emperor',
        category: VehicleCategory.SPORTS,
    },
    verlierer2: {
        model: 'verlierer2',
        name: 'Verlierer',
        brand: 'Bravado',
        category: VehicleCategory.SPORTS,
    },
    veto: {
        model: 'veto',
        name: 'Veto Classic',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    veto2: {
        model: 'veto2',
        name: 'Veto Modern',
        brand: 'Dinka',
        category: VehicleCategory.SPORTS,
    },
    vstr: {
        model: 'vstr',
        name: 'V-STR',
        brand: 'Albany',
        category: VehicleCategory.SPORTS,
    },
    zr350: {
        model: 'zr350',
        name: 'ZR350',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    zr380: {
        model: 'zr380',
        name: 'Apocalypse ZR380',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    zr3802: {
        model: 'zr3802',
        name: 'Future Shock ZR380',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    zr3803: {
        model: 'zr3803',
        name: 'Nightmare ZR380',
        brand: 'Annis',
        category: VehicleCategory.SPORTS,
    },
    // Sportsclassics vehicles (50 vehicles)
    ardent: {
        model: 'ardent',
        name: 'Ardent',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    btype: {
        model: 'btype',
        name: 'Roosevelt',
        brand: 'Albany',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    btype2: {
        model: 'btype2',
        name: 'Franken Stange',
        brand: 'Albany',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    btype3: {
        model: 'btype3',
        name: 'Roosevelt Valor',
        brand: 'Albany',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    casco: {
        model: 'casco',
        name: 'Casco',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    cheburek: {
        model: 'cheburek',
        name: 'Cheburek',
        brand: 'RUNE',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    cheetah2: {
        model: 'cheetah2',
        name: 'Cheetah Classic',
        brand: 'Grotti',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    coquette2: {
        model: 'coquette2',
        name: 'Coquette Classic',
        brand: 'Invetero',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    coquette5: {
        model: 'coquette5',
        name: 'Coquette D1',
        brand: 'Invetero',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    deluxo: {
        model: 'deluxo',
        name: 'Deluxo',
        brand: 'Imponte',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    driftcheburek: {
        model: 'driftcheburek',
        name: 'Cheburek (Drift)',
        brand: 'RUNE',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    driftjester3: {
        model: 'driftjester3',
        name: 'Jester Classic (Drift)',
        brand: 'Dinka',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    driftnebula: {
        model: 'driftnebula',
        name: 'Nebula Turbo (Drift)',
        brand: 'Vulcar',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    dynasty: {
        model: 'dynasty',
        name: 'Dynasty',
        brand: 'Weeny',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    fagaloa: {
        model: 'fagaloa',
        name: 'Fagaloa',
        brand: 'Vulcar',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    feltzer3: {
        model: 'feltzer3',
        name: 'Stirling GT',
        brand: 'Benefactor',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    gt500: {
        model: 'gt500',
        name: 'GT500',
        brand: 'Grotti',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    infernus2: {
        model: 'infernus2',
        name: 'Infernus Classic',
        brand: 'Pegassi',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    jb700: {
        model: 'jb700',
        name: 'JB 700',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    jb7002: {
        model: 'jb7002',
        name: 'JB 700W',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    mamba: {
        model: 'mamba',
        name: 'Mamba',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    manana: {
        model: 'manana',
        name: 'Manana',
        brand: 'Albany',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    michelli: {
        model: 'michelli',
        name: 'Michelli GT',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    monroe: {
        model: 'monroe',
        name: 'Monroe',
        brand: 'Pegassi',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    nebula: {
        model: 'nebula',
        name: 'Nebula Turbo',
        brand: 'Vulcar',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    peyote: {
        model: 'peyote',
        name: 'Peyote',
        brand: 'Vapid',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    peyote3: {
        model: 'peyote3',
        name: 'Peyote Custom',
        brand: 'Vapid',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    pigalle: {
        model: 'pigalle',
        name: 'Pigalle',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    rapidgt3: {
        model: 'rapidgt3',
        name: 'Rapid GT Classic',
        brand: 'Dewbauchee',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    retinue: {
        model: 'retinue',
        name: 'Retinue',
        brand: 'Vapid',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    retinue2: {
        model: 'retinue2',
        name: 'Retinue MKII',
        brand: 'Vapid',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    savestra: {
        model: 'savestra',
        name: 'Savestra',
        brand: 'Annis',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    stinger: {
        model: 'stinger',
        name: 'Stinger',
        brand: 'Grotti',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    stingergt: {
        model: 'stingergt',
        name: 'Stinger GT',
        brand: 'Grotti',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    stromberg: {
        model: 'stromberg',
        name: 'Stromberg',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    swinger: {
        model: 'swinger',
        name: 'Swinger',
        brand: 'Ocelot',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    toreador: {
        model: 'toreador',
        name: 'Toreador',
        brand: 'Pegassi',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    torero: {
        model: 'torero',
        name: 'Torero',
        brand: 'Pegassi',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado: {
        model: 'tornado',
        name: 'Tornado',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado2: {
        model: 'tornado2',
        name: 'Tornado Gang',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado3: {
        model: 'tornado3',
        name: 'Tornado (Beater)',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado4: {
        model: 'tornado4',
        name: 'Tornado (Mariachi)',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado5: {
        model: 'tornado5',
        name: 'Tornado Custom',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    tornado6: {
        model: 'tornado6',
        name: 'Tornado Rat Rod',
        brand: 'Declasse',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    turismo2: {
        model: 'turismo2',
        name: 'Turismo Classic',
        brand: 'Grotti',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    uranus: {
        model: 'uranus',
        name: 'Uranus LozSpeed',
        brand: 'Vapid',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    viseris: {
        model: 'viseris',
        name: 'Viseris',
        brand: 'Lampadati',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    z190: {
        model: 'z190',
        name: '190Z',
        brand: 'Karin',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    zion3: {
        model: 'zion3',
        name: 'Zion Classic',
        brand: 'Übermacht',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    ztype: {
        model: 'ztype',
        name: 'Z-Type',
        brand: 'Truffade',
        category: VehicleCategory.SPORTSCLASSICS,
    },
    // Super vehicles (57 vehicles)
    adder: {
        model: 'adder',
        name: 'Adder',
        brand: 'Truffade',
        category: VehicleCategory.SUPER,
    },
    autarch: {
        model: 'autarch',
        name: 'Autarch',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    banshee2: {
        model: 'banshee2',
        name: 'Banshee 900R',
        brand: 'Bravado',
        category: VehicleCategory.SUPER,
    },
    bullet: {
        model: 'bullet',
        name: 'Bullet',
        brand: 'Vapid',
        category: VehicleCategory.SUPER,
    },
    champion: {
        model: 'champion',
        name: 'Champion',
        brand: 'Dewbauchee',
        category: VehicleCategory.SUPER,
    },
    cheetah: {
        model: 'cheetah',
        name: 'Cheetah',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    cyclone: {
        model: 'cyclone',
        name: 'Cyclone',
        brand: 'Coil',
        category: VehicleCategory.SUPER,
    },
    deveste: {
        model: 'deveste',
        name: 'Deveste',
        brand: 'Principe',
        category: VehicleCategory.SUPER,
    },
    emerus: {
        model: 'emerus',
        name: 'Progen Emerus',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    entity2: {
        model: 'entity2',
        name: 'Entity XXR',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    entity3: {
        model: 'entity3',
        name: 'Entity MT',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    entityxf: {
        model: 'entityxf',
        name: 'Entity XF',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    fmj: {
        model: 'fmj',
        name: 'FMJ',
        brand: 'Vapid',
        category: VehicleCategory.SUPER,
    },
    furia: {
        model: 'furia',
        name: 'Furia',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    gp1: {
        model: 'gp1',
        name: 'GP1',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    ignus: {
        model: 'ignus',
        name: 'Ignus',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    infernus: {
        model: 'infernus',
        name: 'Infernus',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    italigtb: {
        model: 'italigtb',
        name: 'Itali GTB',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    italigtb2: {
        model: 'italigtb2',
        name: 'Itali GTB Custom',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    krieger: {
        model: 'krieger',
        name: 'Krieger',
        brand: 'Benefactor',
        category: VehicleCategory.SUPER,
    },
    le7b: {
        model: 'le7b',
        name: 'RE-7B',
        brand: 'Annis',
        category: VehicleCategory.SUPER,
    },
    lm87: {
        model: 'lm87',
        name: 'LM87',
        brand: 'Benefactor',
        category: VehicleCategory.SUPER,
    },
    nero: {
        model: 'nero',
        name: 'Nero',
        brand: 'Truffade',
        category: VehicleCategory.SUPER,
    },
    nero2: {
        model: 'nero2',
        name: 'Nero Custom',
        brand: 'Truffade',
        category: VehicleCategory.SUPER,
    },
    osiris: {
        model: 'osiris',
        name: 'Osiris',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    penetrator: {
        model: 'penetrator',
        name: 'Penetrator',
        brand: 'Ocelot',
        category: VehicleCategory.SUPER,
    },
    pfister811: {
        model: 'pfister811',
        name: '811',
        brand: 'Pfister',
        category: VehicleCategory.SUPER,
    },
    pipistrello: {
        model: 'pipistrello',
        name: 'Pipistrello',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    prototipo: {
        model: 'prototipo',
        name: 'X80 Proto',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    reaper: {
        model: 'reaper',
        name: 'Reaper',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    s80: {
        model: 's80',
        name: 'S80RR',
        brand: 'Annis',
        category: VehicleCategory.SUPER,
    },
    sc1: {
        model: 'sc1',
        name: 'SC1',
        brand: 'Übermacht',
        category: VehicleCategory.SUPER,
    },
    scramjet: {
        model: 'scramjet',
        name: 'Scramjet',
        brand: 'Declasse',
        category: VehicleCategory.SUPER,
    },
    sheava: {
        model: 'sheava',
        name: 'ETR1',
        brand: 'Emperor',
        category: VehicleCategory.SUPER,
    },
    sultanrs: {
        model: 'sultanrs',
        name: 'Sultan RS',
        brand: 'Karin',
        category: VehicleCategory.SUPER,
    },
    t20: {
        model: 't20',
        name: 'Progen T20',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    taipan: {
        model: 'taipan',
        name: 'Taipan',
        brand: 'Cheval',
        category: VehicleCategory.SUPER,
    },
    tempesta: {
        model: 'tempesta',
        name: 'Tempesta',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    tezeract: {
        model: 'tezeract',
        name: 'Tezeract',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    thrax: {
        model: 'thrax',
        name: 'Thrax',
        brand: 'Truffade',
        category: VehicleCategory.SUPER,
    },
    tigon: {
        model: 'tigon',
        name: 'Tigon',
        brand: 'Lampadati',
        category: VehicleCategory.SUPER,
    },
    torero2: {
        model: 'torero2',
        name: 'Torero XO',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    turismo3: {
        model: 'turismo3',
        name: 'Turismo Omaggio',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    turismor: {
        model: 'turismor',
        name: 'Grotti Turismo R',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    tyrant: {
        model: 'tyrant',
        name: 'Tyrant',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    tyrus: {
        model: 'tyrus',
        name: 'Tyrus',
        brand: 'Progen',
        category: VehicleCategory.SUPER,
    },
    vacca: {
        model: 'vacca',
        name: 'Vacca',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    vagner: {
        model: 'vagner',
        name: 'Vagner',
        brand: 'Dewbauchee',
        category: VehicleCategory.SUPER,
    },
    vigilante: {
        model: 'vigilante',
        name: 'Vigilante',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    virtue: {
        model: 'virtue',
        name: 'Virtue',
        brand: 'Ocelot',
        category: VehicleCategory.SUPER,
    },
    visione: {
        model: 'visione',
        name: 'Visione',
        brand: 'Grotti',
        category: VehicleCategory.SUPER,
    },
    voltic: {
        model: 'voltic',
        name: 'Voltic',
        brand: 'Coil',
        category: VehicleCategory.SUPER,
    },
    voltic2: {
        model: 'voltic2',
        name: 'Rocket Voltic',
        brand: 'Coil',
        category: VehicleCategory.SUPER,
    },
    xa21: {
        model: 'xa21',
        name: 'XA-21',
        brand: 'Ocelot',
        category: VehicleCategory.SUPER,
    },
    zeno: {
        model: 'zeno',
        name: 'Zeno',
        brand: 'Överflöd',
        category: VehicleCategory.SUPER,
    },
    zentorno: {
        model: 'zentorno',
        name: 'Zentorno',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    zorrusso: {
        model: 'zorrusso',
        name: 'Pegassi Zorrusso',
        brand: 'Pegassi',
        category: VehicleCategory.SUPER,
    },
    // Suvs vehicles (46 vehicles)
    aleutian: {
        model: 'aleutian',
        name: 'Aleutian',
        brand: 'Vapid',
        category: VehicleCategory.SUVS,
    },
    astron: {
        model: 'astron',
        name: 'Astron',
        brand: 'Pfister',
        category: VehicleCategory.SUVS,
    },
    baller: {
        model: 'baller',
        name: 'Baller',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller2: {
        model: 'baller2',
        name: 'Baller',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller3: {
        model: 'baller3',
        name: 'Baller LE',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller4: {
        model: 'baller4',
        name: 'Baller LE LWB',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller5: {
        model: 'baller5',
        name: ' Baller LE (Armored)',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller6: {
        model: 'baller6',
        name: 'Baller LE LWB (Armored)',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller7: {
        model: 'baller7',
        name: 'Baller ST',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    baller8: {
        model: 'baller8',
        name: 'Baller ST-D',
        brand: 'Gallivanter',
        category: VehicleCategory.SUVS,
    },
    bjxl: {
        model: 'bjxl',
        name: 'BeeJay XL',
        brand: 'Karin',
        category: VehicleCategory.SUVS,
    },
    castigator: {
        model: 'castigator',
        name: 'Castigator',
        brand: 'Canis',
        category: VehicleCategory.SUVS,
    },
    cavalcade: {
        model: 'cavalcade',
        name: 'Cavalcade',
        brand: 'Albany',
        category: VehicleCategory.SUVS,
    },
    cavalcade2: {
        model: 'cavalcade2',
        name: 'Cavalcade',
        brand: 'Albany',
        category: VehicleCategory.SUVS,
    },
    cavalcade3: {
        model: 'cavalcade3',
        name: 'Cavalcade XL',
        brand: 'Albany',
        category: VehicleCategory.SUVS,
    },
    contender: {
        model: 'contender',
        name: 'Contender',
        brand: 'Vapid',
        category: VehicleCategory.SUVS,
    },
    dorado: {
        model: 'dorado',
        name: 'Dorado',
        brand: 'Bravado',
        category: VehicleCategory.SUVS,
    },
    dubsta: {
        model: 'dubsta',
        name: 'Dubsta',
        brand: 'Benefactor',
        category: VehicleCategory.SUVS,
    },
    dubsta2: {
        model: 'dubsta2',
        name: 'Dubsta',
        brand: 'Benefactor',
        category: VehicleCategory.SUVS,
    },
    fq2: {
        model: 'fq2',
        name: 'FQ2',
        brand: 'Fathom',
        category: VehicleCategory.SUVS,
    },
    granger: {
        model: 'granger',
        name: 'Granger',
        brand: 'Declasse',
        category: VehicleCategory.SUVS,
    },
    granger2: {
        model: 'granger2',
        name: 'Granger 3600LX',
        brand: 'Declasse',
        category: VehicleCategory.SUVS,
    },
    gresley: {
        model: 'gresley',
        name: 'Gresley',
        brand: 'Bravado',
        category: VehicleCategory.SUVS,
    },
    habanero: {
        model: 'habanero',
        name: 'Habanero',
        brand: 'Emperor',
        category: VehicleCategory.SUVS,
    },
    huntley: {
        model: 'huntley',
        name: 'Huntley S',
        brand: 'Enus',
        category: VehicleCategory.SUVS,
    },
    issi8: {
        model: 'issi8',
        name: 'Issi Rally',
        brand: 'Weeny',
        category: VehicleCategory.SUVS,
    },
    iwagen: {
        model: 'iwagen',
        name: 'I-Wagen',
        brand: 'Obey',
        category: VehicleCategory.SUVS,
    },
    jubilee: {
        model: 'jubilee',
        name: 'Jubilee',
        brand: 'Enus',
        category: VehicleCategory.SUVS,
    },
    landstalker: {
        model: 'landstalker',
        name: 'Landstalker',
        brand: 'Dundreary',
        category: VehicleCategory.SUVS,
    },
    landstalker2: {
        model: 'landstalker2',
        name: 'Landstalker XL',
        brand: 'Dundreary',
        category: VehicleCategory.SUVS,
    },
    mesa: {
        model: 'mesa',
        name: 'Mesa',
        brand: 'Canis',
        category: VehicleCategory.SUVS,
    },
    mesa2: {
        model: 'mesa2',
        name: 'Mesa (Snow)',
        brand: 'Canis',
        category: VehicleCategory.SUVS,
    },
    novak: {
        model: 'novak',
        name: 'Novak',
        brand: 'Lampadati',
        category: VehicleCategory.SUVS,
    },
    patriot: {
        model: 'patriot',
        name: 'Patriot',
        brand: 'Mammoth',
        category: VehicleCategory.SUVS,
    },
    patriot2: {
        model: 'patriot2',
        name: 'Patriot Stretch',
        brand: 'Mammoth',
        category: VehicleCategory.SUVS,
    },
    radi: {
        model: 'radi',
        name: 'Radius',
        brand: 'Vapid',
        category: VehicleCategory.SUVS,
    },
    rebla: {
        model: 'rebla',
        name: 'Rebla GTS',
        brand: 'Übermacht',
        category: VehicleCategory.SUVS,
    },
    rocoto: {
        model: 'rocoto',
        name: 'Rocoto',
        brand: 'Obey',
        category: VehicleCategory.SUVS,
    },
    seminole: {
        model: 'seminole',
        name: 'Seminole',
        brand: 'Canis',
        category: VehicleCategory.SUVS,
    },
    seminole2: {
        model: 'seminole2',
        name: 'Seminole Frontier',
        brand: 'Canis',
        category: VehicleCategory.SUVS,
    },
    serrano: {
        model: 'serrano',
        name: 'Serrano',
        brand: 'Benefactor',
        category: VehicleCategory.SUVS,
    },
    squaddie: {
        model: 'squaddie',
        name: 'Squaddie',
        brand: 'Mammoth',
        category: VehicleCategory.SUVS,
    },
    toros: {
        model: 'toros',
        name: 'Toros',
        brand: 'Pegassi',
        category: VehicleCategory.SUVS,
    },
    vivanite: {
        model: 'vivanite',
        name: 'Vivanite',
        brand: 'Karin',
        category: VehicleCategory.SUVS,
    },
    xls: {
        model: 'xls',
        name: 'XLS',
        brand: 'Benefactor',
        category: VehicleCategory.SUVS,
    },
    xls2: {
        model: 'xls2',
        name: 'XLS (Armored)',
        brand: 'Benefactor',
        category: VehicleCategory.SUVS,
    },
    // Trains vehicles (11 vehicles)
    cablecar: {
        model: 'cablecar',
        name: 'Cable Car',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freight: {
        model: 'freight',
        name: 'Freight Train (Locomotive)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freight2: {
        model: 'freight2',
        name: 'Freight Train (Chop Shop)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightcar: {
        model: 'freightcar',
        name: 'Freight Train (Container)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightcar2: {
        model: 'freightcar2',
        name: 'Freight Train (Flatbed Trailer)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightcar3: {
        model: 'freightcar3',
        name: 'Freight Train (Flatbed Trailer)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightcont1: {
        model: 'freightcont1',
        name: 'Freight Train (Container)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightcont2: {
        model: 'freightcont2',
        name: 'Freight Train (Lando Container)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    freightgrain: {
        model: 'freightgrain',
        name: 'Freight Train (Grain Trailer)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    metrotrain: {
        model: 'metrotrain',
        name: 'Freight Train (Tram)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    tankercar: {
        model: 'tankercar',
        name: 'Freight Train (Tanker Trailer)',
        brand: '',
        category: VehicleCategory.TRAINS,
    },
    // Utility vehicles (50 vehicles)
    airtug: {
        model: 'airtug',
        name: 'Airtug',
        brand: 'HVY',
        category: VehicleCategory.UTILITY,
    },
    armytanker: {
        model: 'armytanker',
        name: 'Army Trailer (Tanker)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    armytrailer: {
        model: 'armytrailer',
        name: 'Army Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    armytrailer2: {
        model: 'armytrailer2',
        name: 'Army Trailer (Civilian)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    baletrailer: {
        model: 'baletrailer',
        name: 'Baletrailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    boattrailer: {
        model: 'boattrailer',
        name: 'Boat Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    boattrailer2: {
        model: 'boattrailer2',
        name: 'Boat Trailer (Dinghy)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    boattrailer3: {
        model: 'boattrailer3',
        name: 'Boat Trailer (Seashark)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    caddy: {
        model: 'caddy',
        name: 'Caddy (Golf)',
        brand: 'Nagasaki',
        category: VehicleCategory.UTILITY,
    },
    caddy2: {
        model: 'caddy2',
        name: 'Caddy',
        brand: 'Nagasaki',
        category: VehicleCategory.UTILITY,
    },
    caddy3: {
        model: 'caddy3',
        name: 'Caddy (Bunker)',
        brand: 'Nagasaki',
        category: VehicleCategory.UTILITY,
    },
    docktrailer: {
        model: 'docktrailer',
        name: 'Dock Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    docktug: {
        model: 'docktug',
        name: 'Docktug',
        brand: 'HVY',
        category: VehicleCategory.UTILITY,
    },
    forklift: {
        model: 'forklift',
        name: 'Forklift',
        brand: 'HVY',
        category: VehicleCategory.UTILITY,
    },
    freighttrailer: {
        model: 'freighttrailer',
        name: 'Army Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    graintrailer: {
        model: 'graintrailer',
        name: 'Grain Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    mower: {
        model: 'mower',
        name: 'Lawn Mower',
        brand: 'Jack Sheepe',
        category: VehicleCategory.UTILITY,
    },
    proptrailer: {
        model: 'proptrailer',
        name: 'Prop Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    raketrailer: {
        model: 'raketrailer',
        name: 'Trailer (Rake)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    ripley: {
        model: 'ripley',
        name: 'Ripley',
        brand: 'HVY',
        category: VehicleCategory.UTILITY,
    },
    sadler: {
        model: 'sadler',
        name: 'Sadler',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    sadler2: {
        model: 'sadler2',
        name: 'Sadler (Snow)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    scrap: {
        model: 'scrap',
        name: 'Scrap Truck',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    slamtruck: {
        model: 'slamtruck',
        name: 'Slam Truck',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    tanker: {
        model: 'tanker',
        name: 'Tanker Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tanker2: {
        model: 'tanker2',
        name: 'Tanker Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    towtruck: {
        model: 'towtruck',
        name: 'Tow Truck',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    towtruck2: {
        model: 'towtruck2',
        name: 'Tow Truck (Small)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    towtruck3: {
        model: 'towtruck3',
        name: 'Tow Truck (Beater)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    towtruck4: {
        model: 'towtruck4',
        name: 'Tow Truck',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    tr2: {
        model: 'tr2',
        name: 'Trailer (Car Carrier)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tr3: {
        model: 'tr3',
        name: 'Trailer (Boat)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tr4: {
        model: 'tr4',
        name: 'Trailer (Packed Car Carrier)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tractor: {
        model: 'tractor',
        name: 'Tractor',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tractor2: {
        model: 'tractor2',
        name: 'Fieldmaster',
        brand: 'Stanley',
        category: VehicleCategory.UTILITY,
    },
    tractor3: {
        model: 'tractor3',
        name: 'Fieldmaster (Snow)',
        brand: 'Stanley',
        category: VehicleCategory.UTILITY,
    },
    trailerlarge: {
        model: 'trailerlarge',
        name: 'Mobile Operations Center',
        brand: 'Pegasus',
        category: VehicleCategory.UTILITY,
    },
    trailerlogs: {
        model: 'trailerlogs',
        name: 'Trailer (Logs)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailers: {
        model: 'trailers',
        name: 'Trailer (Container)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailers2: {
        model: 'trailers2',
        name: 'Trailer (Box)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailers3: {
        model: 'trailers3',
        name: 'Trailer (Ramp box)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailers4: {
        model: 'trailers4',
        name: 'Trailer (Container)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailers5: {
        model: 'trailers5',
        name: 'Trailer (Christmas)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trailersmall: {
        model: 'trailersmall',
        name: 'Trailer (Storage/generator)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    trflat: {
        model: 'trflat',
        name: 'Trailer (Flatbed)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tvtrailer: {
        model: 'tvtrailer',
        name: 'Trailer (Fame or Shame)',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    tvtrailer2: {
        model: 'tvtrailer2',
        name: 'Trailer',
        brand: '',
        category: VehicleCategory.UTILITY,
    },
    utillitruck: {
        model: 'utillitruck',
        name: 'Utility Truck (Cherry Picker)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    utillitruck2: {
        model: 'utillitruck2',
        name: 'Utility Truck (Cargo)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
    utillitruck3: {
        model: 'utillitruck3',
        name: 'Utility Truck (Van)',
        brand: 'Vapid',
        category: VehicleCategory.UTILITY,
    },
        // Vans vehicles (41 vehicles)
    bison: {
        model: 'bison',
        name: 'Bison',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    bison2: {
        model: 'bison2',
        name: 'Bison (McGill-Olsen)',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    bison3: {
        model: 'bison3',
        name: 'Bison (The Mighty Bush)',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    bobcatxl: {
        model: 'bobcatxl',
        name: 'Bobcat XL Open',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    boxville: {
        model: 'boxville',
        name: 'Boxville (LSDWP)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    boxville2: {
        model: 'boxville2',
        name: 'Boxville (Go Postal)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    boxville3: {
        model: 'boxville3',
        name: 'Boxville (Humane Labs)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    boxville4: {
        model: 'boxville4',
        name: 'Boxville (Post Op)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    boxville5: {
        model: 'boxville5',
        name: 'Armored Boxville',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    boxville6: {
        model: 'boxville6',
        name: 'Boxville (LSDS)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    burrito: {
        model: 'burrito',
        name: 'Burrito (LSDWP)',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    burrito2: {
        model: 'burrito2',
        name: 'Bugstars Burrito',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    burrito3: {
        model: 'burrito3',
        name: 'Burrito',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    burrito4: {
        model: 'burrito4',
        name: 'Burrito (McGill-Olsen)',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    burrito5: {
        model: 'burrito5',
        name: 'Burrito (Snow)',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    camper: {
        model: 'camper',
        name: 'Camper',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    gburrito: {
        model: 'gburrito',
        name: 'Gang Burrito (Lost MC)',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    gburrito2: {
        model: 'gburrito2',
        name: 'Burrito Custom',
        brand: 'Declasse',
        category: VehicleCategory.VANS,
    },
    journey: {
        model: 'journey',
        name: 'Journey',
        brand: 'Zirconium',
        category: VehicleCategory.VANS,
    },
    journey2: {
        model: 'journey2',
        name: 'Journey II',
        brand: 'Zirconium',
        category: VehicleCategory.VANS,
    },
    minivan: {
        model: 'minivan',
        name: 'Minivan',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    minivan2: {
        model: 'minivan2',
        name: 'Minivan Custom',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    paradise: {
        model: 'paradise',
        name: 'Paradise',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    pony: {
        model: 'pony',
        name: 'Pony',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    pony2: {
        model: 'pony2',
        name: 'Pony (Smoke on the Water)',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    rumpo: {
        model: 'rumpo',
        name: 'Rumpo',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    rumpo2: {
        model: 'rumpo2',
        name: 'Rumpo (Deludamol)',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    rumpo3: {
        model: 'rumpo3',
        name: 'Rumpo Custom',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    speedo: {
        model: 'speedo',
        name: 'Speedo',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    speedo2: {
        model: 'speedo2',
        name: 'Clown Van',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    speedo4: {
        model: 'speedo4',
        name: 'Speedo Custom (Nightclub)',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    speedo5: {
        model: 'speedo5',
        name: 'Speedo Custom',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    surfer: {
        model: 'surfer',
        name: 'Surfer',
        brand: 'BF',
        category: VehicleCategory.VANS,
    },
    surfer2: {
        model: 'surfer2',
        name: 'Surfer',
        brand: 'BF',
        category: VehicleCategory.VANS,
    },
    surfer3: {
        model: 'surfer3',
        name: 'Surfer Custom',
        brand: 'BF',
        category: VehicleCategory.VANS,
    },
    taco: {
        model: 'taco',
        name: 'Taco Van',
        brand: 'Brute',
        category: VehicleCategory.VANS,
    },
    youga: {
        model: 'youga',
        name: 'Youga',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    youga2: {
        model: 'youga2',
        name: 'Youga Classic',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    youga3: {
        model: 'youga3',
        name: 'Youga Classic 4x4',
        brand: 'Bravado',
        category: VehicleCategory.VANS,
    },
    youga4: {
        model: 'youga4',
        name: 'Youga Custom',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
    youga5: {
        model: 'youga5',
        name: 'Youga Custom',
        brand: 'Vapid',
        category: VehicleCategory.VANS,
    },
};

// Statistics
export const TOTAL_VEHICLES = Object.keys(allVehicles).length;
export const TOTAL_CATEGORIES = 23;

// Simple caches for performance
const categoryCache = new Map<VehicleCategory, Record<string, VehicleMetadata>>();
const brandCache = new Map<string, Record<string, VehicleMetadata>>();
const allModelsCache = Object.keys(allVehicles);

// Utility functions
export function getVehicleByModel(model: string): VehicleMetadata | undefined {
    return allVehicles[model];
}

export function getVehiclesByCategory(category: VehicleCategory): Record<string, VehicleMetadata> {
    // Use cached result if available
    if (categoryCache.has(category)) {
        return categoryCache.get(category)!;
    }

    const categoryVehicles: Record<string, VehicleMetadata> = {};

    // Use for...in loop for better performance than Object.entries()
    for (const model in allVehicles) {
        if (allVehicles[model].category === category) {
            categoryVehicles[model] = allVehicles[model];
        }
    }

    // Cache the result for future calls
    categoryCache.set(category, categoryVehicles);
    return categoryVehicles;
}

export function getVehiclesByBrand(brand: string): Record<string, VehicleMetadata> {
    const brandKey = brand.toLowerCase();
    
    // Use cached result if available
    if (brandCache.has(brandKey)) {
        return brandCache.get(brandKey)!;
    }

    const brandVehicles: Record<string, VehicleMetadata> = {};

    // Use for...in loop and avoid repeated toLowerCase() calls
    for (const model in allVehicles) {
        if (allVehicles[model].brand.toLowerCase() === brandKey) {
            brandVehicles[model] = allVehicles[model];
        }
    }

    // Cache the result for future calls
    brandCache.set(brandKey, brandVehicles);
    return brandVehicles;
}

export function getAllVehicleModels(): string[] {
    // Return cached array instead of computing every time
    return allModelsCache;
}

export function getAllCategories(): VehicleCategory[] {
    return Object.values(VehicleCategory);
}

export function getVehicleCount(): number {
    // Use cached total instead of computing every time
    return TOTAL_VEHICLES;
}

export function getCategoryCount(category: VehicleCategory): number {
    // Use for...in loop for better performance than Object.values().filter()
    let count = 0;
    for (const model in allVehicles) {
        if (allVehicles[model].category === category) {
            count++;
        }
    }
    return count;
}

/**
 * Vehicle Manager - Singleton class for managing vehicle operations
 */
export class VehicleManager {
    private static instance: VehicleManager;
    private vehicles: VehicleDatabase;

    private constructor() {
        this.vehicles = allVehicles;
    }

    public static getInstance(): VehicleManager {
        if (!VehicleManager.instance) {
            VehicleManager.instance = new VehicleManager();
        }
        return VehicleManager.instance;
    }

    public getVehicle(model: string): VehicleMetadata | undefined {
        return this.vehicles[model];
    }

    public getAllVehicles(): VehicleDatabase {
        // Return reference instead of creating copy for read-only access
        return this.vehicles;
    }

    public getVehiclesByCategory(category: VehicleCategory): Record<string, VehicleMetadata> {
        return getVehiclesByCategory(category);
    }

    public getVehiclesByBrand(brand: string): Record<string, VehicleMetadata> {
        return getVehiclesByBrand(brand);
    }

    public isValidVehicle(model: string): boolean {
        return model in this.vehicles;
    }

    public getVehicleName(model: string): string | undefined {
        const vehicle = this.vehicles[model];
        return vehicle?.name;
    }

    public getVehicleBrand(model: string): string | undefined {
        const vehicle = this.vehicles[model];
        return vehicle?.brand;
    }

    public getRandomVehicle(): VehicleMetadata {
        // Use cached models array for better performance
        const randomModel = allModelsCache[Math.floor(Math.random() * allModelsCache.length)];
        return this.vehicles[randomModel];
    }

    public getRandomVehicleByCategory(category: VehicleCategory): VehicleMetadata | undefined {
        const categoryVehicles = this.getVehiclesByCategory(category);
        // Use cached keys from the result for better performance
        const models = Object.keys(categoryVehicles);
        if (models.length === 0) return undefined;
        const randomModel = models[Math.floor(Math.random() * models.length)];
        return categoryVehicles[randomModel];
    }
}

// Register exports for FiveM global.exports (server-side only)
if (
    typeof global !== 'undefined' &&
    typeof IsDuplicityVersion === 'function' &&
    IsDuplicityVersion()
) {
    global.exports('getVehicleByModel', getVehicleByModel);
    global.exports('getVehiclesByCategory', getVehiclesByCategory);
    global.exports('getVehiclesByBrand', getVehiclesByBrand);
    global.exports('getAllVehicleModels', getAllVehicleModels);
    global.exports('getAllCategories', getAllCategories);
    global.exports('getVehicleCount', getVehicleCount);
    global.exports('getCategoryCount', getCategoryCount);
    // Add more exports as needed
}

// Export default instance
export const vehicleManager = VehicleManager.getInstance();
