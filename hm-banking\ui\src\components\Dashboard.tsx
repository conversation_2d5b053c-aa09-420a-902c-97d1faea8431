import React, { memo, useState } from 'react';
import { useBankingStore } from '../store/bankingStore';

type ActionType = 'deposit' | 'withdraw' | 'transfer' | null;

const Dashboard: React.FC = memo(() => {
  const accounts = useBankingStore((state) => state.accounts);
  const selectedAccountId = useBankingStore((state) => state.selectedAccountId);
  const [activeAction, setActiveAction] = useState<ActionType>(null);

  // Form state from store
  const depositAmount = useBankingStore((state) => state.depositAmount);
  const withdrawAmount = useBankingStore((state) => state.withdrawAmount);
  const transferAmount = useBankingStore((state) => state.transferAmount);
  const transferTargetAccountNumber = useBankingStore((state) => state.transferTargetAccountNumber);
  const transferDescription = useBankingStore((state) => state.transferDescription);

  // Form setters
  const setDepositAmount = useBankingStore((state) => state.setDepositAmount);
  const setWithdrawAmount = useBankingStore((state) => state.setWithdrawAmount);
  const setTransferAmount = useBankingStore((state) => state.setTransferAmount);
  const setTransferTargetAccountNumber = useBankingStore((state) => state.setTransferTargetAccountNumber);
  const setTransferDescription = useBankingStore((state) => state.setTransferDescription);

  // Actions
  const submitDeposit = useBankingStore((state) => state.submitDeposit);
  const submitWithdrawal = useBankingStore((state) => state.submitWithdrawal);
  const submitTransfer = useBankingStore((state) => state.submitTransfer);
  const resetForms = useBankingStore((state) => state.resetForms);

  const isLoading = useBankingStore((state) => state.isLoading);

  const selectedAccount = accounts.find(acc => acc.accountId === selectedAccountId);

  if (!selectedAccount) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'personal':
        return 'fa-user';
      case 'savings':
        return 'fa-piggy-bank';
      case 'business':
        return 'fa-briefcase';
      default:
        return 'fa-credit-card';
    }
  };

  const handleActionSelect = (action: ActionType) => {
    if (activeAction === action) {
      setActiveAction(null);
      resetForms();
    } else {
      setActiveAction(action);
      resetForms();
    }
  };

  const handleSubmit = async (action: ActionType) => {
    if (!selectedAccountId) return;

    try {
      switch (action) {
        case 'deposit':
          await submitDeposit();
          break;
        case 'withdraw':
          await submitWithdrawal();
          break;
        case 'transfer':
          await submitTransfer();
          break;
      }
      setActiveAction(null);
      resetForms();
    } catch (error) {
      console.error('Transaction failed:', error);
    }
  };

  const isValidAmount = (amount: string) => {
    const num = parseFloat(amount);
    return !isNaN(num) && num > 0;
  };

  const formatCurrencyInput = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(num);
  };
  return (
    <div className="p-4">
      {/* Account Header - Compact */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center border border-green-500/30">
            <i className={`fas ${getAccountTypeIcon(selectedAccount.accountType)} text-green-400 text-sm`} />
          </div>
          <div>
            <h2 className="text-neutral-100 font-medium text-sm">
              {selectedAccount.accountNumber}
            </h2>
            <p className="text-neutral-400 text-xs capitalize">
              {selectedAccount.accountType}
              {selectedAccount.isDefault && (
                <span className="ml-2 bg-green-500/20 text-green-400 text-xs px-1.5 py-0.5 rounded border border-green-500/30">
                  Default
                </span>
              )}
            </p>
          </div>
        </div>
      </div>      {/* Balance Display with Quick Actions - Compact Side by Side */}
      <div className="bg-gradient-to-r from-green-500/10 via-green-500/5 to-transparent border border-green-500/20 rounded-lg p-4 mb-4 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-2 right-2 text-4xl">
            <i className="fas fa-dollar-sign" />
          </div>
        </div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            {/* Balance on the left */}
            <div>
              <p className="text-neutral-400 text-xs mb-1">Current Balance</p>
              <p className="text-2xl font-bold text-neutral-100">
                {formatCurrency(selectedAccount.balance)}
              </p>
            </div>
            
            {/* Quick Action Buttons on the right - Compact horizontal layout */}
            <div className="flex gap-2">
              <button
                onClick={() => handleActionSelect('deposit')}
                className={`px-3 py-2 rounded-lg border transition-all flex items-center gap-1.5 ${
                  activeAction === 'deposit'
                    ? 'bg-green-500/20 border-green-500/40 text-green-400'
                    : 'bg-green-500/5 border-green-500/20 text-green-300 hover:bg-green-500/10 hover:border-green-500/30'
                }`}
              >
                <i className="fas fa-plus text-xs" />
                <span className="text-xs font-medium">Deposit</span>
              </button>
              
              <button
                onClick={() => handleActionSelect('withdraw')}
                className={`px-3 py-2 rounded-lg border transition-all flex items-center gap-1.5 ${
                  activeAction === 'withdraw'
                    ? 'bg-red-500/20 border-red-500/40 text-red-400'
                    : 'bg-red-500/5 border-red-500/20 text-red-300 hover:bg-red-500/10 hover:border-red-500/30'
                }`}
              >
                <i className="fas fa-minus text-xs" />
                <span className="text-xs font-medium">Withdraw</span>
              </button>
              
              <button
                onClick={() => handleActionSelect('transfer')}
                className={`px-3 py-2 rounded-lg border transition-all flex items-center gap-1.5 ${
                  activeAction === 'transfer'
                    ? 'bg-blue-500/20 border-blue-500/40 text-blue-400'
                    : 'bg-blue-500/5 border-blue-500/20 text-blue-300 hover:bg-blue-500/10 hover:border-blue-500/30'
                }`}
              >
                <i className="fas fa-exchange-alt text-xs" />
                <span className="text-xs font-medium">Transfer</span>
              </button>
            </div>
          </div>{/* Action Forms - Compact */}
          {activeAction && (
            <div className="bg-neutral-800/80 rounded-lg p-3 border border-neutral-600/30 mt-3">
              {activeAction === 'deposit' && (
                <div className="space-y-3">
                  <h4 className="text-green-400 font-medium text-xs flex items-center gap-2">
                    <i className="fas fa-plus" />
                    Deposit Funds
                  </h4>
                  <div>
                    <label className="block text-neutral-300 text-xs mb-1">Amount</label>
                    <div className="relative">
                      <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-neutral-400 text-xs">$</span>
                      <input
                        type="number"
                        value={depositAmount}
                        onChange={(e) => setDepositAmount(e.target.value)}
                        className="w-full bg-neutral-700/50 border border-neutral-600/50 rounded-lg pl-6 pr-2 py-1.5 text-neutral-200 text-xs focus:outline-none focus:border-green-500/50"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                      />
                    </div>
                    {depositAmount && (
                      <p className="text-neutral-400 text-xs mt-1">
                        Preview: {formatCurrencyInput(depositAmount)}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleSubmit('deposit')}
                      disabled={!isValidAmount(depositAmount) || isLoading}
                      className="flex-1 bg-green-500/20 hover:bg-green-500/30 text-green-400 border border-green-500/30 rounded-lg py-1.5 text-xs font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-1">
                          <div className="w-2 h-2 border border-green-400/30 border-t-green-400 rounded-full animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Confirm'
                      )}
                    </button>
                    <button
                      onClick={() => setActiveAction(null)}
                      className="px-3 bg-neutral-700/50 hover:bg-neutral-600/50 text-neutral-400 border border-neutral-600/50 rounded-lg py-1.5 text-xs transition-all"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {activeAction === 'withdraw' && (
                <div className="space-y-3">
                  <h4 className="text-red-400 font-medium text-xs flex items-center gap-2">
                    <i className="fas fa-minus" />
                    Withdraw Funds
                  </h4>
                  <div>
                    <label className="block text-neutral-300 text-xs mb-1">Amount</label>
                    <div className="relative">
                      <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-neutral-400 text-xs">$</span>
                      <input
                        type="number"
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        className="w-full bg-neutral-700/50 border border-neutral-600/50 rounded-lg pl-6 pr-2 py-1.5 text-neutral-200 text-xs focus:outline-none focus:border-red-500/50"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                        max={selectedAccount.balance}
                      />
                    </div>
                    {withdrawAmount && (
                      <p className="text-neutral-400 text-xs mt-1">
                        Preview: {formatCurrencyInput(withdrawAmount)}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleSubmit('withdraw')}
                      disabled={!isValidAmount(withdrawAmount) || parseFloat(withdrawAmount) > selectedAccount.balance || isLoading}
                      className="flex-1 bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30 rounded-lg py-1.5 text-xs font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-1">
                          <div className="w-2 h-2 border border-red-400/30 border-t-red-400 rounded-full animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Confirm'
                      )}
                    </button>
                    <button
                      onClick={() => setActiveAction(null)}
                      className="px-3 bg-neutral-700/50 hover:bg-neutral-600/50 text-neutral-400 border border-neutral-600/50 rounded-lg py-1.5 text-xs transition-all"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {activeAction === 'transfer' && (
                <div className="space-y-3">
                  <h4 className="text-blue-400 font-medium text-xs flex items-center gap-2">
                    <i className="fas fa-exchange-alt" />
                    Transfer Funds
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-neutral-300 text-xs mb-1">Amount</label>
                      <div className="relative">
                        <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-neutral-400 text-xs">$</span>
                        <input
                          type="number"
                          value={transferAmount}
                          onChange={(e) => setTransferAmount(e.target.value)}
                          className="w-full bg-neutral-700/50 border border-neutral-600/50 rounded-lg pl-6 pr-2 py-1.5 text-neutral-200 text-xs focus:outline-none focus:border-blue-500/50"
                          placeholder="0.00"
                          step="0.01"
                          min="0"
                          max={selectedAccount.balance}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-neutral-300 text-xs mb-1">To Account</label>
                      <input
                        type="text"
                        value={transferTargetAccountNumber}
                        onChange={(e) => setTransferTargetAccountNumber(e.target.value)}
                        className="w-full bg-neutral-700/50 border border-neutral-600/50 rounded-lg px-2 py-1.5 text-neutral-200 text-xs focus:outline-none focus:border-blue-500/50"
                        placeholder="Account number"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-neutral-300 text-xs mb-1">Description (Optional)</label>
                    <input
                      type="text"
                      value={transferDescription}
                      onChange={(e) => setTransferDescription(e.target.value)}
                      className="w-full bg-neutral-700/50 border border-neutral-600/50 rounded-lg px-2 py-1.5 text-neutral-200 text-xs focus:outline-none focus:border-blue-500/50"
                      placeholder="Payment description"
                    />
                  </div>
                  {transferAmount && (
                    <p className="text-neutral-400 text-xs">
                      Preview: {formatCurrencyInput(transferAmount)}
                    </p>
                  )}
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleSubmit('transfer')}
                      disabled={!isValidAmount(transferAmount) || !transferTargetAccountNumber.trim() || parseFloat(transferAmount) > selectedAccount.balance || isLoading}
                      className="flex-1 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border border-blue-500/30 rounded-lg py-1.5 text-xs font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-1">
                          <div className="w-2 h-2 border border-blue-400/30 border-t-blue-400 rounded-full animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Confirm'
                      )}
                    </button>
                    <button
                      onClick={() => setActiveAction(null)}
                      className="px-3 bg-neutral-700/50 hover:bg-neutral-600/50 text-neutral-400 border border-neutral-600/50 rounded-lg py-1.5 text-xs transition-all"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

Dashboard.displayName = 'Dashboard';

export default Dashboard;
