/* Custom Slider Styles */
.slider {
  background: #404040;
  outline: none;
  opacity: 0.8;
  transition: opacity 0.15s ease-in-out;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #065f46;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  transition: all 0.15s ease-in-out;
}

.slider::-webkit-slider-thumb:hover {
  background: #059669;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
  transform: scale(1.1);
}

.slider::-webkit-slider-track {
  width: 100%;
  height: 8px;
  cursor: pointer;
  background: #404040;
  border-radius: 4px;
  border: 1px solid #525252;
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #065f46;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  transition: all 0.15s ease-in-out;
}

.slider::-moz-range-thumb:hover {
  background: #059669;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
  transform: scale(1.1);
}

.slider::-moz-range-track {
  width: 100%;
  height: 8px;
  cursor: pointer;
  background: #404040;
  border-radius: 4px;
  border: 1px solid #525252;
}

/* Focus styles */
.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
}

.slider:focus::-moz-range-thumb {
  box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
}
