/**
 * Error Utilities
 *
 * This module provides utilities for error handling, logging, and display.
 */

// Define interfaces for error types
interface ErrorWithMessage {
  message: string;
}

interface ErrorWithResponse {
  response: {
    data?: {
      message?: string;
    };
    statusText?: string;
    status?: number;
  };
}

interface ErrorWithStack {
  stack: string;
}

interface ErrorWithCode {
  code?: string;
  name?: string;
  type?: string;
}

/**
 * Handle API errors consistently
 * @param error - The error object
 * @param fallbackMessage - Fallback message if error doesn't have a message
 * @returns Formatted error message
 */
export function handleApiError(error: unknown, fallbackMessage = 'An error occurred'): string {
  // Check if error is a string
  if (typeof error === 'string') {
    return error;
  }

  // Check if error is an object with a message
  if (error && typeof error === 'object') {
    const errorWithMessage = error as Partial<ErrorWithMessage>;
    if (errorWithMessage.message) {
      return errorWithMessage.message;
    }

    // Check for response error
    const errorWithResponse = error as Partial<ErrorWithResponse>;
    if (errorWithResponse.response) {
      if (errorWithResponse.response.data?.message) {
        return errorWithResponse.response.data.message;
      }

      // Return status text if available
      if (errorWithResponse.response.statusText) {
        return `Error: ${errorWithResponse.response.statusText}`;
      }
    }
  }

  // Return fallback message
  return fallbackMessage;
}

/**
 * Log errors with proper context
 * @param error - The error object
 * @param context - Context information
 */
export function logError(error: unknown, context: string): void {
  console.error(`[${context}] Error:`, error);

  // Additional logging for specific error types
  if (error && typeof error === 'object') {
    const errorWithStack = error as Partial<ErrorWithStack>;
    if (errorWithStack.stack) {
      console.error(`[${context}] Stack trace:`, errorWithStack.stack);
    }

    const errorWithResponse = error as Partial<ErrorWithResponse>;
    if (errorWithResponse.response) {
      console.error(`[${context}] Response data:`, errorWithResponse.response.data);
    }
  }
}

/**
 * Create a user-friendly error message
 * @param error - The error object
 * @param userFriendlyMessages - Map of error codes/types to user-friendly messages
 * @param defaultMessage - Default message if no match is found
 * @returns User-friendly error message
 */
export function createUserFriendlyError(
  error: unknown,
  userFriendlyMessages: Record<string, string> = {},
  defaultMessage = 'Something went wrong. Please try again.'
): string {
  // Extract error code or type
  let errorCode = '';

  if (typeof error === 'string') {
    errorCode = error;
  } else if (error && typeof error === 'object') {
    const errorWithCode = error as Partial<ErrorWithCode>;
    const errorWithResponse = error as Partial<ErrorWithResponse>;

    if (errorWithCode.code) {
      errorCode = errorWithCode.code;
    } else if (errorWithCode.name) {
      errorCode = errorWithCode.name;
    } else if (errorWithCode.type) {
      errorCode = errorWithCode.type;
    } else if (errorWithResponse.response?.status) {
      errorCode = `HTTP_${errorWithResponse.response.status}`;
    }
  }

  // Look up user-friendly message
  if (errorCode && userFriendlyMessages[errorCode]) {
    return userFriendlyMessages[errorCode];
  }

  // Return default message
  return defaultMessage;
}

/**
 * Check if an error is a network error
 * @param error - The error object
 * @returns True if it's a network error
 */
export function isNetworkError(error: unknown): boolean {
  if (!error) return false;

  // Check for specific network error patterns
  if (typeof error === 'string') {
    return error.includes('network') || error.includes('connection') || error.includes('offline');
  }

  if (typeof error === 'object') {
    const errorWithMessage = error as Partial<ErrorWithMessage>;
    const errorWithCode = error as Partial<ErrorWithCode>;
    const errorWithResponse = error as Partial<ErrorWithResponse>;

    // Check for axios network error
    if (
      errorWithMessage.message &&
      (errorWithMessage.message.includes('Network Error') ||
        errorWithMessage.message.includes('timeout') ||
        errorWithMessage.message.includes('offline'))
    ) {
      return true;
    }

    // Check for fetch network error
    if (errorWithCode.name === 'TypeError' && errorWithMessage.message?.includes('fetch')) {
      return true;
    }

    // Check for specific status codes
    if (
      errorWithResponse.response?.status === 0 ||
      errorWithResponse.response?.status === 502 ||
      errorWithResponse.response?.status === 503 ||
      errorWithResponse.response?.status === 504
    ) {
      return true;
    }
  }

  return false;
}

/**
 * Create a retry function with exponential backoff
 * @param fn - The function to retry
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @returns A function that will retry on failure
 */
export function createRetryFunction<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): () => Promise<T> {
  return async function retryFn(): Promise<T> {
    let lastError: unknown;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;

        // If this was the last attempt, don't delay, just throw
        if (attempt === maxRetries) {
          throw error;
        }

        // Calculate delay with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);

        // Wait before next attempt
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // This should never be reached due to the throw in the loop,
    // but TypeScript needs it for type safety
    throw lastError;
  };
}

/**
 * Formats an error object or string into a user-friendly message.
 * @param error - The error to format (Error, string, or unknown)
 * @returns A string message suitable for display
 */
export function formatErrorMessage(error: unknown): string {
  if (!error) return 'An unknown error occurred.';
  if (typeof error === 'string') return error;
  if (error instanceof Error) return error.message;
  if (typeof error === 'object' && 'message' in error) {
    const errorWithMessage = error as { message: string };
    return errorWithMessage.message;
  }
  return 'An unexpected error occurred.';
}

/**
 * Logs an error to the console and optionally returns a formatted message.
 * @param error - The error to log
 * @param context - Optional context string for debugging
 * @returns The formatted error message
 */
export function handleError(error: unknown, context?: string): string {
  if (context) {
    console.error(`[ErrorUtils] ${context}:`, error);
  } else {
    console.error('[ErrorUtils]', error);
  }
  return formatErrorMessage(error);
}
