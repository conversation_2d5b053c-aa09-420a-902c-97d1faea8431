/**
 * Utility functions for notification grouping
 */

import { Notification } from '../types/notificationTypes';

/**
 * Group notifications by app ID
 * @param notifications Array of notifications
 * @returns Record of notifications grouped by app ID
 */
export const groupNotificationsByApp = (
  notifications: Notification[]
): Record<number, Notification[]> => {
  return notifications.reduce((acc, notification) => {
    if (!acc[notification.appId]) {
      acc[notification.appId] = [];
    }
    acc[notification.appId].push(notification);
    return acc;
  }, {} as Record<number, Notification[]>);
};

/**
 * Group message notifications by conversation ID and sender
 * @param notifications Array of notifications
 * @returns Record of notifications grouped by conversation ID and sender
 */
export const groupMessagesByConversation = (
  appId: number,
  notifications: Notification[]
): Record<string, Notification[]> => {
  const groupedByConversation: Record<string, Notification[]> = {};

  notifications.forEach(notification => {
    if (notification.isGroupSummary) return; // Skip existing summaries

    // For message type, group by conversationId and sender
    if (notification.type === 'message' && notification.metadata?.conversationId) {
      const conversationId = notification.metadata.conversationId;
      const sender = notification.metadata?.sender || 'unknown';
      // Create a unique key that includes both the conversation ID and sender
      const groupKey = `app-${appId}-conv-${conversationId}-sender-${sender}`;

      if (!groupedByConversation[groupKey]) {
        groupedByConversation[groupKey] = [];
      }

      groupedByConversation[groupKey].push({
        ...notification,
        groupId: groupKey
      });
    }
  });

  return groupedByConversation;
};

/**
 * Group non-message notifications by type
 * @param notifications Array of notifications
 * @returns Record of notifications grouped by type
 */
export const groupNotificationsByType = (
  appId: number,
  notifications: Notification[]
): Record<string, Notification[]> => {
  const groupedByType: Record<string, Notification[]> = {};

  notifications.forEach(notification => {
    if (notification.isGroupSummary) return; // Skip existing summaries

    // For non-message notifications, use existing groupId or create one by type
    if (notification.type !== 'message') {
      const groupKey = notification.groupId || `app-${appId}-type-${notification.type}`;

      if (!groupedByType[groupKey]) {
        groupedByType[groupKey] = [];
      }

      groupedByType[groupKey].push({
        ...notification,
        groupId: groupKey
      });
    }
  });

  return groupedByType;
};

/**
 * Group similar notifications based on content similarity
 * This is more advanced than simple type grouping and looks at actual content
 * @param appId App ID
 * @param notifications Array of notifications
 * @returns Record of notifications grouped by similarity
 */
export const groupSimilarNotifications = (
  appId: number,
  notifications: Notification[]
): Record<string, Notification[]> => {
  const similarityGroups: Record<string, Notification[]> = {};

  // First pass: group by exact title match
  notifications.forEach(notification => {
    if (notification.isGroupSummary) return; // Skip existing summaries

    // Create a similarity key based on title and type
    const similarityKey = `app-${appId}-similar-${notification.title.toLowerCase().replace(/\s+/g, '-')}-${notification.type}`;

    if (!similarityGroups[similarityKey]) {
      similarityGroups[similarityKey] = [];
    }

    similarityGroups[similarityKey].push({
      ...notification,
      groupId: similarityKey
    });
  });

  // Second pass: for single notifications, try to find similar ones
  // This handles cases where titles are slightly different but content is similar
  Object.entries(similarityGroups).forEach(([key, group]) => {
    // Only process groups with a single notification
    if (group.length !== 1) return;

    const notification = group[0];
    let foundSimilar = false;

    // Look for similar notifications in other single-notification groups
    Object.entries(similarityGroups).forEach(([otherKey, otherGroup]) => {
      if (key === otherKey || otherGroup.length !== 1) return;

      const otherNotification = otherGroup[0];

      // Check if notifications are from the same app and have similar content
      if (
        notification.appId === otherNotification.appId &&
        notification.type === otherNotification.type &&
        areSimilarMessages(notification.message, otherNotification.message)
      ) {
        // Merge the groups
        similarityGroups[key].push(otherNotification);
        // Mark the other group for removal
        similarityGroups[otherKey] = [];
        foundSimilar = true;
      }
    });

    // If we found similar notifications, update the group ID
    if (foundSimilar) {
      similarityGroups[key].forEach(n => {
        n.groupId = key;
      });
    }
  });

  // Remove empty groups
  Object.keys(similarityGroups).forEach(key => {
    if (similarityGroups[key].length === 0) {
      delete similarityGroups[key];
    }
  });

  return similarityGroups;
};

/**
 * Check if two message strings are similar
 * @param message1 First message
 * @param message2 Second message
 * @returns True if messages are similar
 */
export const areSimilarMessages = (message1: string, message2: string): boolean => {
  // Convert to lowercase and remove punctuation for comparison
  const normalize = (str: string) => str.toLowerCase().replace(/[^\w\s]/g, '').trim();

  const norm1 = normalize(message1);
  const norm2 = normalize(message2);

  // Check for exact match after normalization
  if (norm1 === norm2) return true;

  // Check if one is a substring of the other
  if (norm1.includes(norm2) || norm2.includes(norm1)) return true;

  // Check for similarity using Levenshtein distance
  // For longer messages, we allow more differences
  const maxLength = Math.max(norm1.length, norm2.length);
  if (maxLength === 0) return true;

  const distance = levenshteinDistance(norm1, norm2);
  const similarityRatio = 1 - distance / maxLength;

  // Messages are similar if they're at least 70% similar
  return similarityRatio >= 0.7;
};

/**
 * Calculate Levenshtein distance between two strings
 * @param a First string
 * @param b Second string
 * @returns Levenshtein distance
 */
export const levenshteinDistance = (a: string, b: string): number => {
  const matrix: number[][] = [];

  // Initialize the matrix
  for (let i = 0; i <= a.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= b.length; j++) {
    matrix[0][j] = j;
  }

  // Fill the matrix
  for (let i = 1; i <= a.length; i++) {
    for (let j = 1; j <= b.length; j++) {
      const cost = a[i - 1] === b[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  return matrix[a.length][b.length];
};

/**
 * Create a summary notification for a group of notifications
 * @param appId App ID
 * @param groupKey Group key
 * @param groupNotifications Notifications in the group
 * @returns Summary notification
 */
export const createSummaryNotification = (
  appId: number,
  groupKey: string,
  groupNotifications: Notification[]
): Notification => {
  // Find most recent notification to use as template
  const mostRecent = groupNotifications.reduce(
    (latest, notif) => (notif.timestamp > latest.timestamp ? notif : latest),
    groupNotifications[0]
  );

  // For message notifications, include all messages in the summary
  const messageContent = mostRecent.type === 'message'
    ? groupNotifications
        .sort((a, b) => a.timestamp - b.timestamp) // Sort by timestamp (oldest first)
        .map(n => n.message)
        .join('\n')
    : `${groupNotifications.length} notifications`;

  // Create summary notification
  return {
    id: Date.now() + Math.random(), // Unique ID
    appId: appId,
    title:
      mostRecent.type === 'message'
        ? mostRecent.metadata?.isGroup
          ? String(mostRecent.metadata?.sender || 'Group Chat')
          : String(mostRecent.metadata?.sender || 'Conversation')
        : mostRecent.title,
    message: messageContent,
    type: mostRecent.type,
    timestamp: Math.max(...groupNotifications.map(n => n.timestamp)),
    read: false,
    movedToTopBar: true,
    count: groupNotifications.length,
    isGroupSummary: true,
    groupId: groupKey,
    metadata: { ...mostRecent.metadata },
    // Pass the deep link from the most recent notification
    deepLink: mostRecent.deepLink
  };
};

/**
 * Process notifications to create grouped and summary notifications
 * @param notifications Array of notifications
 * @param settings Optional notification settings to control grouping behavior
 * @returns Record of processed notifications grouped by app ID
 */
export const processNotifications = (
  notifications: Notification[],
  settings?: {
    groupSimilarNotifications?: boolean;
    collapseThreshold?: number;
    appSettings?: Record<number, { grouping?: { enabled?: boolean; collapseThreshold?: number } }>
  }
): Record<number, Notification[]> => {
  // Group notifications by app ID
  const notificationsByApp = groupNotificationsByApp(notifications);

  // Default settings
  const globalGrouping = settings?.groupSimilarNotifications !== false;
  const defaultCollapseThreshold = settings?.collapseThreshold || 3;

  // Process each app's notifications
  Object.entries(notificationsByApp).forEach(([appId, appNotifications]) => {
    const numericAppId = parseInt(appId);

    // Check app-specific settings
    const appGroupingEnabled = settings?.appSettings?.[numericAppId]?.grouping?.enabled;
    const shouldGroupForApp = appGroupingEnabled !== undefined ? appGroupingEnabled : globalGrouping;

    // Skip grouping if disabled for this app
    if (!shouldGroupForApp) return;

    // Get app-specific collapse threshold or use default
    const appCollapseThreshold = settings?.appSettings?.[numericAppId]?.grouping?.collapseThreshold || defaultCollapseThreshold;

    // Group message notifications by conversation
    const messageGroups = groupMessagesByConversation(numericAppId, appNotifications);

    // Group non-message notifications by type
    const typeGroups = groupNotificationsByType(numericAppId, appNotifications);

    // Group similar notifications based on content
    const similarityGroups = groupSimilarNotifications(numericAppId, appNotifications);

    // Combine all groups
    const allGroups = { ...messageGroups, ...typeGroups, ...similarityGroups };

    // Create summary notifications for groups that exceed the threshold
    Object.entries(allGroups).forEach(([groupKey, groupNotifications]) => {
      if (groupNotifications.length >= appCollapseThreshold) {
        // Create summary notification
        const summaryNotification = createSummaryNotification(
          numericAppId,
          groupKey,
          groupNotifications
        );

        // Add summary to the app notifications
        notificationsByApp[numericAppId].push(summaryNotification);

        // Set groupId on all notifications in the group
        groupNotifications.forEach(notification => {
          notification.groupId = groupKey;
        });
      }
    });
  });

  return notificationsByApp;
};
