import React from 'react';
import { useInventoryStore, SecondaryPanelType } from '../stores/inventoryStore';
import SecondaryInventoryPanel from './Panels/SecondaryInventoryPanel';
import CraftingPanel from './Panels/CraftingPanel';
import GroundPanel from './Panels/GroundPanel';

/**
 * Renders the appropriate secondary panel based on current context.
 * Each panel component now handles its own configuration (title, rows, columns, etc.)
 */
const ContextualSecondaryPanel: React.FC = () => {
  const panelType = useInventoryStore(state => state.secondaryPanel);

  if (panelType === SecondaryPanelType.NONE) return null;

  switch (panelType) {
    case SecondaryPanelType.GROUND:
      return (
        <GroundPanel
          title="Ground"
          initiallyOpen={true}
        />
      );
    
    case SecondaryPanelType.CONTAINER:
      return (
        <SecondaryInventoryPanel
          panelType={SecondaryPanelType.CONTAINER}
          title="Container"
          rows={5}
          columns={6}
          instanceId="container"
          initiallyOpen={true}
        />
      );
    
    case SecondaryPanelType.STASH:
      return (
        <SecondaryInventoryPanel
          panelType={SecondaryPanelType.STASH}
          title="Stash"
          rows={8}
          columns={6}
          instanceId="stash"
          initiallyOpen={true}
        />
      );
    
    case SecondaryPanelType.SHOP:
      return null;
    
    case SecondaryPanelType.CRAFTING:
      return <CraftingPanel />;
    
    default:
      return null;
  }
};

export default ContextualSecondaryPanel;
