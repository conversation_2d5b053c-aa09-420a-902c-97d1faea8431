// Mock player inventory data for UI testing
import { ItemSlot } from '@shared';
import { getItemDefinition } from '../../../scripts/shared/items/items';
import { getWeaponDefinition } from '../../../scripts/shared/items/weapons';

// Simple JSON structure - only instance data, reference definitions
export const mockInventoryJSON = [
  {
    "index": 0,
    "quantity": 8,
    "item": {
      "instanceId": "inst-bread-1703123456789",
      "definitionId": "bread",
      "currentDurability": 95,
      "metadata": {
        "expiryDate": "2024-01-15",
        "quality": 90
      }
    }
  },
  {
    "index": 1,
    "quantity": 3,
    "item": {
      "instanceId": "inst-bandage-1703123456790",
      "definitionId": "bandage",
      "metadata": {}
    }
  },
  {
    "index": 2,
    "quantity": 1,
    "item": {
      "instanceId": "inst-pistol-1703123456791",
      "definitionId": "WEAPON_PISTOL",
      "currentDurability": 73,
      "metadata": {
        "serial": "XYZ123",
        "ammoCount": 12,
        "attachments": ["suppressor"]
      }
    }
  },
  {
    "index": 3,
    "quantity": 40,
    "item": {
      "instanceId": "inst-wood_plank-1703123456792",
      "definitionId": "wood_plank",
      "metadata": {}
    }
  },
  {
    "index": 10,
    "quantity": 1,
    "item": {
      "instanceId": "inst-phone-1703123456793",
      "definitionId": "phone",
      "currentDurability": 92,
      "metadata": {
        "phoneNumber": "555-0123",
        "batteryLevel": 78,
        "contacts": []
      }
    }
  },
  {
    "index": 11,
    "quantity": 1,
    "item": {
      "instanceId": "inst-tablet-1703123456794",
      "definitionId": "tablet",
      "currentDurability": 88,
      "metadata": {
        "batteryLevel": 65,
        "apps": ["notes", "calculator"]
      }
    }
  },
  {
    "index": 12,
    "quantity": 10,
    "item": {
      "instanceId": "inst-bread-1703123456795",
      "definitionId": "bread",
      "currentDurability": 100,
      "metadata": {
        "expiryDate": "2024-01-20",
        "quality": 100
      }
    }
  },
  {
    "index": 13,
    "quantity": 1,
    "item": {
      "instanceId": "inst-weapon_nightstick-1703123456796",
      "definitionId": "WEAPON_NIGHTSTICK",
      "currentDurability": 100,
      "metadata": {}
    }
  },
  {
    "index": 14,
    "quantity": 1,
    "item": {
      "instanceId": "inst-wooden_sword-1703123456796",
      "definitionId": "WEAPON_KNIFE",
      "currentDurability": 80,
      "metadata": {
        "damage": 15,
        "weaponType": "melee"
      }
    }
  },
  {
    "index": 15,
    "quantity": 15,
    "item": {
      "instanceId": "inst-metal_fragment-1703123456796",
      "definitionId": "metal_fragment",
      "metadata": {}
    }
  },
  {
    "index": 18,
    "quantity": 1,
    "item": {
      "instanceId": "inst-vest-1703123456797",
      "definitionId": "vest",
      "currentDurability": 60,
      "metadata": {
        "armorPoints": 35,
        "repairCount": 1
      }
    }
  },
  {
    "index": 20,
    "quantity": 8,
    "item": {
      "instanceId": "inst-lockpick-1703123456798",
      "definitionId": "lockpick",
      "currentDurability": 45,
      "metadata": {
        "successfulUses": 12,
        "failedAttempts": 3
      }
    }
  }
];

// Equipment slots JSON
export const mockEquipmentJSON = [
  {
    "index": 0,
    "quantity": 1,
    "item": {
      "instanceId": "inst-phone-1703123456799",
      "definitionId": "phone",
      "currentDurability": 95,
      "metadata": {
        "phoneNumber": "555-9876",
        "batteryLevel": 85
      }
    },
    "slotType": "phone"
  },
  {
    "index": 1,
    "quantity": 1,
    "item": {
      "instanceId": "inst-vest-1703123456800",
      "definitionId": "vest",
      "currentDurability": 85,
      "metadata": {
        "armorPoints": 50,
        "repairCount": 0
      }
    },
    "slotType": "armor"
  },
  {
    "index": 2,
    "quantity": 1,
    "item": {
      "instanceId": "inst-weapon_pistol-1703123456801",
      "definitionId": "WEAPON_PISTOL",
      "currentDurability": 90,
      "metadata": {
        "serial": "ABC789",
        "ammoCount": 15,
        "attachments": ["flashlight", "extended_mag"]
      }
    },
    "slotType": "primary_weapon"
  },
  {
    "index": 3,
    "quantity": 1,
    "item": {
      "instanceId": "inst-tablet-1703123456802",
      "definitionId": "tablet",
      "currentDurability": 95,
      "metadata": {
        "batteryLevel": 90,
        "apps": ["gps", "calculator", "notes"]
      }
    },
    "slotType": "tablet"
  },
  {
    "index": 4,
    "quantity": 1,
    "item": {
      "instanceId": "inst-backpack-1703123456803",
      "definitionId": "backpack",
      "currentDurability": 100,
      "metadata": {
        "capacity": 50,
        "color": "black"
      }
    },
    "slotType": "backpack"
  }
];

// Function to convert JSON to ItemSlot with resolved definitions
export function resolveInventoryFromJSON(jsonData: any[]): ItemSlot[] {
  
  if (!Array.isArray(jsonData)) {
    console.error('[resolveInventoryFromJSON] Data is not an array!');
    return [];
  }
  
  return jsonData.map((slot: any) => {
    if (!slot.item) {
      return { index: slot.index };
    }

    // add check for item def and weapon definitions
    let definition = getItemDefinition(slot.item.definitionId);
    if (!definition) {
      definition = getWeaponDefinition(slot.item.definitionId);
    }

    if (!definition) {
      console.error(`Item definition not found: ${slot.item.definitionId}`);
      return { index: slot.index };
    }

    return {
      index: slot.index,
      quantity: slot.quantity,
      item: {
        ...definition, // All static data from definition
        instanceId: slot.item.instanceId,
        definitionId: slot.item.definitionId,
        quantity: slot.quantity || 1,
        currentDurability: slot.item.currentDurability || definition.maxDurability || 100,
        metadata: slot.item.metadata || {}
      },
      ...(slot.slotType && { slotType: slot.slotType })
    };
  });
}

// Updated exports using the JSON approach
export const mockGridItems: ItemSlot[] = (() => {
  const resolvedItems = resolveInventoryFromJSON(mockInventoryJSON);
  
  // Fill empty slots for 25-slot grid
  const fullGrid: ItemSlot[] = [];
  for (let i = 0; i < 25; i++) {
    const existing = resolvedItems.find(slot => slot.index === i);
    fullGrid.push(existing || { index: i });
  }
  return fullGrid;
})();

export const mockActionSlots: ItemSlot[] = resolveInventoryFromJSON(mockEquipmentJSON);

// Secondary inventory JSON
export const mockSecondaryInventoryJSON = [
  {
    "index": 0,
    "quantity": 6,
    "item": {
      "instanceId": "inst-bread-1703123456803",
      "definitionId": "bread",
      "metadata": {
        "quality": 85
      }
    }
  },
  {
    "index": 1,
    "quantity": 1,
    "item": {
      "instanceId": "inst-weapon_pistol-1703123456804",
      "definitionId": "WEAPON_PISTOL",
      "currentDurability": 65,
      "metadata": {
        "serial": "DEF456",
        "ammoCount": 8
      }
    }
  },
  {
    "index": 3,
    "quantity": 30,
    "item": {
      "instanceId": "inst-wood_plank-1703123456805",
      "definitionId": "wood_plank",
      "metadata": {}
    }
  }
];

export const mockSecondaryInventory: ItemSlot[] = resolveInventoryFromJSON(mockSecondaryInventoryJSON);

export default {
  gridItems: mockGridItems,
  secondaryInventory: mockSecondaryInventory,
};