// QuickAccess Preview Configuration Types

/**
 * Defines the source inventory type for quickaccess preview slots
 */
export type QuickAccessSourceType = 'main' | 'action';

/**
 * Configuration for a single quickaccess preview slot
 */
export interface QuickAccessSlotConfig {
    /** The position in the quickaccess bar (0-4) */
    slot: number;
    /** The source inventory type (main inventory or action slots) */
    source: QuickAccessSourceType;
    /** The slot index in the source inventory */
    sourceSlot: number;
    /** Optional key binding for the slot (1-5) */
    keyBinding?: number;
    /** Whether this slot is enabled */
    enabled: boolean;
}

/**
 * Complete quickaccess preview configuration
 */
export interface QuickAccessPreviewConfig {
    /** Array of slot configurations */
    slots: QuickAccessSlotConfig[];
    /** Whether the quickaccess preview is visible */
    visible: boolean;
    /** Position of the quickaccess bar */
    position: 'bottom' | 'top' | 'left' | 'right';
}

/**
 * Default quickaccess configuration
 * Shows first 4 main inventory slots + weapon slot from action slots
 */
export const DEFAULT_QUICKACCESS_CONFIG: QuickAccessPreviewConfig = {
    slots: [
        { slot: 0, source: 'main', sourceSlot: 0, keyBinding: 1, enabled: true }, // Main slot 0
        { slot: 1, source: 'main', sourceSlot: 1, keyBinding: 2, enabled: true }, // Main slot 1
        { slot: 2, source: 'main', sourceSlot: 2, keyBinding: 3, enabled: true }, // Main slot 2
        { slot: 3, source: 'main', sourceSlot: 3, keyBinding: 4, enabled: true }, // Main slot 3
        { slot: 4, source: 'action', sourceSlot: 0, keyBinding: 5, enabled: true } // Weapon slot
    ],
    visible: true,
    position: 'bottom'
};
