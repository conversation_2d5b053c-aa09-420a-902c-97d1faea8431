import { useHudStore } from '../store/hudStore';

// Voice/communication indicator component
// Currently disabled in optimized HUD but kept for future voice system integration
const VoiceIndicator = () => {
  const { hudData, settings } = useHudStore();

  // Voice data is not currently implemented in the optimized HUD
  // This component is kept for future voice system integration
  // Return null until voice data is added to the HUD data structure
  return null;

  // TODO: Uncomment and implement when voice system is integrated
  /*
  // Don't render if voice indicator is disabled
  if (!settings.show.voice) {
    return null;
  }

  // Mock voice data for when voice system is implemented
  const voiceData = {
    talking: false,
    range: 2 // Default to normal range
  };

  const getVoiceRangeInfo = () => {
    switch (voiceData.range) {
      case 1:
        return { label: 'Whisper', color: 'text-yellow-400', bgColor: 'bg-yellow-400/20' };
      case 2:
        return { label: 'Normal', color: 'text-green-400', bgColor: 'bg-green-400/20' };
      case 3:
        return { label: 'Shout', color: 'text-red-400', bgColor: 'bg-red-400/20' };
      default:
        return { label: 'Muted', color: 'text-neutral-400', bgColor: 'bg-neutral-400/20' };
    }
  };

  const voiceInfo = getVoiceRangeInfo();
  
  return (
    <div className="fixed top-4 left-4 animate-fade-in">
      <div className={`
        bg-gradient-to-r from-neutral-900/70 via-neutral-800/80 to-neutral-900/70 
        border border-emerald-500/30 rounded-full px-4 py-2 backdrop-blur-sm shadow-lg
        flex items-center space-x-3
        ${voiceData.talking ? 'border-emerald-400/50' : ''}
        transition-all duration-200
      `}>
        <div className="relative">
          <i className={`fas fa-microphone text-sm ${voiceInfo.color} transition-colors duration-200`}></i>
          {voiceData.talking && (
            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
          )}
        </div>

        <span className={`text-xs font-medium ${voiceInfo.color}`}>
          {voiceInfo.label}
        </span>

        <div className="w-0.5 h-3 bg-neutral-600/50"></div>

        <div className="flex space-x-0.5">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`
                w-0.5 rounded-full transition-all duration-150
                ${i < voiceData.range
                  ? `h-3 ${voiceInfo.color.replace('text-', 'bg-')}` 
                  : 'h-1.5 bg-neutral-600'
                }
                ${voiceData.talking ? 'animate-pulse' : ''}
              `}
            />
          ))}
        </div>

        {voiceData.talking && (
          <>
            <div className="w-0.5 h-3 bg-neutral-600/50"></div>
            <span className="text-xs text-emerald-400 animate-pulse">
              Speaking
            </span>
          </>
        )}
      </div>
    </div>
  );
  */
};

export default VoiceIndicator;
