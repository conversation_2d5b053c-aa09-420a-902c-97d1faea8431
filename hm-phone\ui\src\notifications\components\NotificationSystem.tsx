import React, { useMemo, useEffect, useCallback } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNotificationStore } from '../stores/notificationStore';
import { NotificationRenderer } from './NotificationRenderer';
import { NotificationData, Notification } from '../types/notificationTypes';

const NotificationSystem: React.FC = () => {
  const { notifications, removeNotification, addNotification } = useNotificationStore();

  const popupNotifications = useMemo(() => {
    const popups: NotificationData[] = [];

    Object.entries(notifications).forEach(([appId, appNotifications]) => {
      appNotifications
        .filter(n => !n.movedToTopBar)
        .forEach(notification => {
          popups.push({
            ...notification,
            appId: parseInt(appId),
            movedToTopBar: false,
            duration: notification.duration ?? undefined,
            persistent: notification.persistent ?? false,
            icon: notification.icon ?? undefined,
            metadata: notification.metadata ?? undefined,
            actions: notification.actions
              ? {
                  primary: notification.actions.find(a => a.primary)?.onPress,
                  secondary: notification.actions.find(a => !a.primary)?.onPress
                }
              : undefined
          });
        });
    });

    // Sort by timestamp (newest first)
    return popups.sort((a, b) => b.timestamp - a.timestamp);
  }, [notifications]);

  // Function to move a notification to the topbar
  const moveToTopBar = useCallback(
    (appId: number, notificationId: number) => {
      // Get the current notification
      const appNotifications = notifications[appId] || [];
      const notification = appNotifications.find(n => n.id === notificationId);

      if (notification) {
        // First, completely remove the notification
        removeNotification(appId, notificationId);

        // Then, create a new notification with movedToTopBar set to true
        const updatedNotification: Notification = {
          ...notification,
          id: Date.now(), // Use a new ID to avoid conflicts
          movedToTopBar: true
        };

        // Add the updated notification
        addNotification(updatedNotification);
      }
    },
    [notifications, removeNotification, addNotification]
  );

  // Auto-dismiss popup notifications after a delay (unless they're marked as persistent)
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];

    popupNotifications.forEach(notification => {
      // Skip auto-dismiss for persistent notifications
      if (notification.persistent) return;

      // Default duration is 5 seconds if not specified
      const duration = notification.duration || 5000;

      const timer = setTimeout(() => {
        // Move to topbar for auto-dismiss
        moveToTopBar(notification.appId, notification.id);
      }, duration);

      timers.push(timer);
    });

    // Clean up timers on unmount or when notifications change
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [popupNotifications, removeNotification, addNotification, moveToTopBar]);

  return (
    <div className="fixed top-0 right-0 p-4 space-y-2 max-h-screen overflow-y-auto z-50">
      {/* Popup Notifications - Adjusted to fit phone screen */}
      <div className="fixed top-12 left-0 right-0 px-4 space-y-2 z-50 pointer-events-auto">
        <AnimatePresence>
          {popupNotifications.map(notification => (
            <motion.div
              key={`popup-${notification.appId}-${notification.id}`}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="mb-2 w-full max-w-[280px] mx-auto"
            >
              <NotificationRenderer
                notification={notification}
                onClose={() => removeNotification(notification.appId, notification.id)}
                className="shadow-lg bg-[#2A2A2A] rounded-xl border border-white/10"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* No test panel */}

      {/* We don't need to show notifications in the main phone content anymore */}
    </div>
  );
};

export default NotificationSystem;
