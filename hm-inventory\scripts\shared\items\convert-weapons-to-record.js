const fs = require('fs');
const path = require('path');

// Read the current weapons.ts file
const weaponsFilePath = path.join(__dirname, 'weapons.ts');
let content = fs.readFileSync(weaponsFilePath, 'utf8');

// Replace the export line
content = content.replace(
  'export const weapons: WeaponDefinition[] = ',
  'export const weaponDefinitions: Record<string, WeaponDefinition> = '
);

// Convert array format to object format
// Find the array content between [ and ];
const arrayMatch = content.match(/export const weaponDefinitions: Record<string, WeaponDefinition> = (\[[\s\S]*?\]);/);

if (arrayMatch) {
  const arrayContent = arrayMatch[1];
  
  // Parse the array content to get individual weapon objects
  // This is a simple regex-based approach
  const weaponMatches = arrayContent.match(/\{\s*id: '[^']+',[\s\S]*?\n  \}/g);
  
  if (weaponMatches) {
    // Convert each weapon object to a keyed property
    const objectEntries = weaponMatches.map(weaponStr => {
      // Extract the weapon name/id from the object
      const idMatch = weaponStr.match(/id: '([^']+)'/);
      if (idMatch) {
        const weaponId = idMatch[1];
        // Remove the opening and closing braces and adjust indentation
        const weaponContent = weaponStr.replace(/^\{\s*/, '').replace(/\n  \}$/, '');
        return `  ${weaponId}: {\n    ${weaponContent.replace(/\n  /g, '\n    ')}\n  }`;
      }
      return null;
    }).filter(Boolean);
    
    const objectContent = `{\n${objectEntries.join(',\n')}\n}`;
    
    // Replace the array with the object
    content = content.replace(arrayMatch[0], `export const weaponDefinitions: Record<string, WeaponDefinition> = ${objectContent};`);
  }
}

// Update the getWeaponDefinition function for O(1) lookup
content = content.replace(
  /export function getWeaponDefinition\(weaponName: string\): WeaponDefinition \| undefined \{\s*return weapons\.find\(weapon => weapon\.name === weaponName \|\| weapon\.id === weaponName\);\s*\}/,
  `export function getWeaponDefinition(weaponName: string): WeaponDefinition | undefined {
  return weaponDefinitions[weaponName];
}`
);

// Update the hash lookup function  
content = content.replace(
  /export function getItemDefinitionByHash\(weaponHash: string \| number\): WeaponDefinition \| undefined \{\s*const hashNum = typeof weaponHash === 'string' \? parseInt\(weaponHash\) : weaponHash;\s*return weapons\.find\(weapon => weapon\.hash === hashNum\);\s*\}/,
  `export function getItemDefinitionByHash(weaponHash: string | number): WeaponDefinition | undefined {
  const hashNum = typeof weaponHash === 'string' ? parseInt(weaponHash) : weaponHash;
  return Object.values(weaponDefinitions).find(weapon => weapon.hash === hashNum);
}`
);

// Write the modified content back
fs.writeFileSync(weaponsFilePath, content);
console.log('Successfully converted weapons array to Record/Object format');
console.log('Updated getWeaponDefinition for O(1) lookup performance');
