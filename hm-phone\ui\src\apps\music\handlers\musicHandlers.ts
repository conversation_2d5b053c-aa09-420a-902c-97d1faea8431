/**
 * Music app message handlers
 */

import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useMusicStore } from '../stores/musicStore';
import { Song } from '../types/musicTypes';

// Register handler for songs data
registerEventHandler('music', 'setSongs', data => {
  console.log('[Music] Received songs data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the songs data
    useMusicStore.getState().handlers.onSetSongs(data);
  } else {
    console.error('[Music] Received invalid songs data (not an array):', data);
  }
});

// Register handler for albums data
registerEventHandler('music', 'setAlbums', data => {
  console.log('[Music] Received albums data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the albums data
    useMusicStore.getState().handlers.onSetAlbums(data);
  } else {
    console.error('[Music] Received invalid albums data (not an array):', data);
  }
});

// Register handler for artists data
registerEventHandler('music', 'setArtists', data => {
  console.log('[Music] Received artists data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the artists data
    useMusicStore.getState().handlers.onSetArtists(data);
  } else {
    console.error('[Music] Received invalid artists data (not an array):', data);
  }
});

// Register handler for favorites data
registerEventHandler('music', 'setFavorites', data => {
  console.log('[Music] Received favorites data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the favorites data
    useMusicStore.getState().handlers.onSetFavorites(data);
  } else {
    console.error('[Music] Received invalid favorites data (not an array):', data);
  }
});

// Register handler for current song data
registerEventHandler('music', 'currentSong', data => {
  console.log('[Music] Received current song data:', data);

  // Validate data structure for Song type
  if (
    data &&
    typeof data === 'object' &&
    'id' in data &&
    'title' in data &&
    'albumId' in data &&
    'artistId' in data &&
    'duration' in data &&
    'imageUrl' in data &&
    typeof data.id === 'number' &&
    typeof data.title === 'string' &&
    typeof data.albumId === 'number' &&
    typeof data.artistId === 'number' &&
    typeof data.duration === 'number' &&
    typeof data.imageUrl === 'string'
  ) {
    // Update the store with the current song data
    useMusicStore.getState().setCurrentSong(data as Song);
  } else {
    console.error('[Music] Received invalid current song data:', data);
  }
});

// Register handler for queue data
registerEventHandler('music', 'queue', data => {
  console.log('[Music] Received queue data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the queue data
    useMusicStore.setState({ queue: data });
  } else {
    console.error('[Music] Received invalid queue data (not an array):', data);
  }
});

// Register handler for history data
registerEventHandler('music', 'history', data => {
  console.log('[Music] Received history data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the history data
    useMusicStore.setState({ history: data });
  } else {
    console.error('[Music] Received invalid history data (not an array):', data);
  }
});

// Register handler for volume data
registerEventHandler('music', 'volume', data => {
  console.log('[Music] Received volume data:', data);

  // Validate data
  if (typeof data === 'number') {
    // Update the store with the volume data
    useMusicStore.getState().setVolume(data);
  } else {
    console.error('[Music] Received invalid volume data (not a number):', data);
  }
});

// Register handler for current time data
registerEventHandler('music', 'currentTime', data => {
  console.log('[Music] Received current time data:', data);

  // Validate data
  if (typeof data === 'number') {
    // Update the store with the current time data
    useMusicStore.getState().seekTo(data);
  } else {
    console.error('[Music] Received invalid current time data (not a number):', data);
  }
});

// Register handler for duration data
registerEventHandler('music', 'duration', data => {
  console.log('[Music] Received duration data:', data);

  // Validate data
  if (typeof data === 'number') {
    // Update the store with the duration data
    useMusicStore.setState({ duration: data });
  } else {
    console.error('[Music] Received invalid duration data (not a number):', data);
  }
});

// Register handler for isPlaying data
registerEventHandler('music', 'isPlaying', data => {
  console.log('[Music] Received isPlaying data:', data);

  // Validate data
  if (typeof data === 'boolean') {
    // Update the store with the isPlaying data
    useMusicStore.setState({ isPlaying: data });
  } else {
    console.error('[Music] Received invalid isPlaying data (not a boolean):', data);
  }
});
