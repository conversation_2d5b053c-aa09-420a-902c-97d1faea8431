/**
 * Server Module Exports
 *
 * This file centralizes all exports from the server directory,
 * allowing for simplified imports throughout the codebase.
 */

// Re-export everything from individual modules
// export * from './character.manager';
// export * from './database';
// export * from './events';
// export * from './player';
// export * from './resource.manager';
// export * from './server';

// Export a default object with all modules for convenience
// import * as CharacterManager from './character.manager';
// import * as Database from './database';
// import * as Events from './events';
// import * as Player from './player';
// import * as ResourceManager from './resource.manager';
// import * as Server from './server';
