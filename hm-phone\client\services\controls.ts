/**
 * Controls Service
 *
 * Manages game controls and input handling.
 * This service is responsible for:
 * - Disabling certain controls when phone is open
 * - Registering key bindings
 * - Handling control input
 */

// Import globals to ensure they're initialized
import '../globals';

// Controls tick handler ID
let controlsTickId: number | null = null;

// Controls we disable when the phone is open:
// 1 - Look Left/Right
// 2 - Look Up/Down
// 24 - Attack
// 25 - Aim
// 257 - Attack2
// 68 - Vehicle Aim
// 69 - Vehicle Attack
// 70 - Vehicle Attack2

/**
 * Start the controls tick handler
 * Disables certain controls when phone is open
 */
export function startControlsTickHandler(): void {
    // If already running, do nothing
    if (controlsTickId !== null) {
        return;
    }

    // Set global state
    global.phoneControlsDisabled = true;

    // Create tick handler - ultra optimized version
    // Run at a reduced frequency to save CPU
    // FiveM runs at 60 fps, so running every 3 frames is still 20 updates per second
    let frameCounter = 0;

    // Use a more efficient approach with direct native calls for better performance
    controlsTickId = setTick(() => {
        frameCounter++;

        // Only run every 3 frames (20 times per second instead of 60)
        // This cuts CPU usage by 2/3 while still being responsive enough
        if (frameCounter % 3 === 0) {
            // Only disable the absolute minimum controls needed
            // Each control we disable adds CPU overhead
            DisableControlAction(0, 1, true); // Look Left/Right
            DisableControlAction(0, 2, true); // Look Up/Down
            DisableControlAction(0, 24, true); // Attack
            DisableControlAction(0, 25, true); // Aim
            DisableControlAction(0, 257, true); // Attack2
            DisableControlAction(0, 68, true); // Vehicle Aim
            DisableControlAction(0, 69, true); // Vehicle Attack
            DisableControlAction(0, 70, true); // Vehicle Attack2
        }
    });

    console.log('[Phone] Controls tick handler started');
}

/**
 * Stop the controls tick handler
 */
export function stopControlsTickHandler(): void {
    if (controlsTickId !== null) {
        clearTick(controlsTickId);
        controlsTickId = null;

        // Re-enable controls
        enableAllControlActions();

        // Update global state
        global.phoneControlsDisabled = false;

        console.log('[Phone] Controls tick handler stopped');
    }
}

/**
 * Re-enable all control actions
 * Used when cleaning up resources
 */
export function enableAllControlActions(): void {
    // Re-enable only the controls we disabled
    EnableControlAction(0, 1, true); // Look Left/Right
    EnableControlAction(0, 2, true); // Look Up/Down
    EnableControlAction(0, 24, true); // Attack
    EnableControlAction(0, 25, true); // Aim
    EnableControlAction(0, 257, true); // Attack2
    EnableControlAction(0, 68, true); // Vehicle Aim
    EnableControlAction(0, 69, true); // Vehicle Attack
    EnableControlAction(0, 70, true); // Vehicle Attack2

    // Note: Movement controls are handled by SetNuiFocusKeepInput(true)
    // We only need to handle the camera/look and attack controls here
}

/**
 * Register key bindings for phone actions
 * @param toggleCommand Command to toggle phone
 * @param toggleKey Key to toggle phone
 */
export function registerKeyBindings(toggleCommand = 'phone', toggleKey = 'K'): void {
    // Register key mapping
    RegisterKeyMapping(toggleCommand, 'Toggle Phone', 'keyboard', toggleKey);

    console.log(`[Phone] Registered key binding: ${toggleKey} for command /${toggleCommand}`);
}

/**
 * Clean up controls resources
 */
export function cleanupControlsResources(): void {
    stopControlsTickHandler();
}

// Export controls service
export const controlsService = {
    startControlsTickHandler,
    stopControlsTickHandler,
    enableAllControlActions,
    registerKeyBindings,
    cleanupControlsResources
};

export default controlsService;
