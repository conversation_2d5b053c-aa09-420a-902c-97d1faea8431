/**
 * Framework Detection and API
 *
 * This file handles framework detection and provides a unified API for interacting with the detected framework.
 */

import { IFramework, FrameworkConfig, FrameworkDetectionResult } from './types';
import { HMCoreFramework } from './hmcore';
import { initializeFrameworkGlobals } from './globals';

// Default configuration
const defaultConfig: FrameworkConfig = {
    forceFramework: undefined,
    debug: false
};

// Framework instance
let frameworkInstance: IFramework | null = null;

/**
 * Detect and initialize the framework
 * @param config Framework configuration
 * @returns Framework detection result
 */
export function detectFramework(config: FrameworkConfig = defaultConfig): FrameworkDetectionResult {
    // If framework is already initialized, return it
    if (frameworkInstance) {
        return {
            name: frameworkInstance.name,
            isLoaded: frameworkInstance.isLoaded,
            framework: frameworkInstance
        };
    }

    // Always use hm-core framework
    frameworkInstance = new HMCoreFramework();
    
    // Initialize framework globals
    initializeFrameworkGlobals();

    return {
        name: frameworkInstance.name,
        isLoaded: frameworkInstance.isLoaded,
        framework: frameworkInstance
    };
}

/**
 * Get the current framework instance
 * @returns Framework instance
 */
export function getFramework(): IFramework {
    if (!frameworkInstance) {
        // Auto-detect framework if not already initialized
        const result = detectFramework();
        frameworkInstance = result.framework;
    }

    // This should never be null since we always initialize hm-core
    return frameworkInstance!;
}

/**
 * Get the framework name
 * @returns Framework name
 */
export function getFrameworkName(): string {
    return getFramework().name;
}

/**
 * Check if a framework is loaded
 * @param name Framework name to check
 * @returns True if the specified framework is loaded
 */
export function isFramework(name: string): boolean {
    return getFramework().name === name;
}

/**
 * Reset the framework instance (for testing)
 */
export function resetFramework(): void {
    frameworkInstance = null;
}

// Note: Framework enum is now exported from types.ts
