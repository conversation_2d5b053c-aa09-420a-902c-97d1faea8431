/// <reference types="@citizenfx/server" />

interface WeaponComponentData {
  id: string;
  name: string;
  label: string;
  category: string;
  icon?: string;
  description?: string;
  stats?: {
    accuracy?: number;
    damage?: number;
    range?: number;
    stability?: number;
    concealment?: number;
  };
  rarity?: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  attachBone?: string;
}

/**
 * Check if player has specific item using hm-inventory
 */
function hasItem(source: number, itemName: string, amount: number = 1): boolean {
    try {
        // Check if hm-inventory export exists
        if (!global.exports['hm-inventory']) {
            console.error(`[hm-weapons] hm-inventory exports not found!`);
            return false;
        }
        
        if (!global.exports['hm-inventory'].hasItem) {
            console.error(`[hm-weapons] hasItem export not found in hm-inventory!`);
            return false;
        }
        
        const result = global.exports['hm-inventory'].hasItem(source, itemName, amount);
        return result === true;
    } catch (error) {
        console.error(`[hm-weapons] Error checking item ${itemName}:`, error);
        return false; // Return false on error - player doesn't have the item
    }
}

/**
 * Get weapon definition from hm-inventory using hash
 */
function getWeaponDefinitionByHash(weaponHash: string | number) {
    try {
        // Check if hm-inventory export exists
        if (!global.exports['hm-inventory']) {
            console.error(`[hm-weapons] hm-inventory exports not found!`);
            return null;
        }
        
        if (!global.exports['hm-inventory'].getItemDefinitionByHash) {
            console.error(`[hm-weapons] getItemDefinitionByHash export not found!`);
            return null;
        }
        
        // Use the new getItemDefinitionByHash function from hm-inventory
        const weaponDef = global.exports['hm-inventory'].getItemDefinitionByHash(weaponHash);
          // Log the weapon definition with stringified data for debugging
        console.log(`[hm-weapons] Weapon definition for hash ${weaponHash}:`, JSON.stringify(weaponDef, null, 2));
        
        return weaponDef;
    } catch (error) {
        console.error(`[hm-weapons] Error getting weapon definition for hash ${weaponHash}:`, error);
        return null;
    }
}

/**
 * Get item definition from hm-inventory
 */
function getItemDefinition(itemId: string) {
    try {
        const itemDefinitions = global.exports['hm-inventory'].getItemDefinitions();
        if (!itemDefinitions || typeof itemDefinitions !== 'object') {
            return null;
        }
        
        // itemDefinitions is an object, not an array - access by key
        const item = itemDefinitions[itemId];
        return item || null;
    } catch (error) {
        console.error(`[hm-weapons] Error getting item definition for ${itemId}:`, error);
        return null;
    }
}

/**
 * Get component category based on attach bone or component name
 */
function getComponentCategory(attachBone: string): string {
    const categoryMap: Record<string, string> = {
        'WAPClip': 'magazine',
        'WAPSupp': 'muzzle', 
        'WAPScop': 'optics',
        'WAPGrip': 'grip',
        'WAPScop_2': 'optics',
        'WAPFlshLasr': 'tactical',
        'gun_root': 'misc',
        'gun_gripr': 'misc',
        // Add more mappings based on your component types
        'COMPONENT_AT_SCOPE': 'optics',
        'COMPONENT_AT_AR_SUPP': 'muzzle',
        'COMPONENT_ASSAULTRIFLE_CLIP': 'magazine',
        'COMPONENT_AT_AR_AFGRIP': 'grip'
    };

    // Try to match by attach bone first
    if (categoryMap[attachBone]) {
        return categoryMap[attachBone];
    }

    // Try to match by component name patterns
    for (const [pattern, category] of Object.entries(categoryMap)) {
        if (attachBone.includes(pattern)) {
            return category;
        }
    }

    return 'misc';
}

/**
 * Get available weapon modifications for player based on inventory
 */
async function getAvailableWeaponMods(source: number, weaponHash: string): Promise<Record<string, WeaponComponentData[]>> {
    try {
        // Get weapon definition by hash
        const weaponDef = getWeaponDefinitionByHash(weaponHash);
        
        if (!weaponDef?.components) {
            console.log(`[hm-weapons] No weapon definition or components found for hash ${weaponHash}`);
            return {};
        }

        // Group mods by attachBone instead of category
        const availableMods: Record<string, WeaponComponentData[]> = {};

        // Check each component against player inventory
        for (const component of weaponDef.components) {
            const componentName = component.name || component.definitionId;
            const hasComponent = hasItem(source, componentName);
            
            if (hasComponent) {
                const attachBone = component.attachBone || 'gun_root';
                const category = getComponentCategory(attachBone);
                const itemDef = getItemDefinition(componentName);                const componentData: WeaponComponentData = {
                    id: componentName, // Use component name as unique ID
                    name: componentName,
                    label: component.label || itemDef?.label || componentName,
                    category: category,
                    icon: component.icon || itemDef?.image,
                    description: component.description || itemDef?.description,
                    stats: component.stats,
                    rarity: component.rarity || 'common',
                    attachBone: attachBone
                };// Group by attachBone only (no more dual grouping)
                if (!availableMods[attachBone]) {
                    availableMods[attachBone] = [];
                }
                availableMods[attachBone].push(componentData);
            }
        }        const totalAvailable = Object.keys(availableMods).reduce((sum, key) => {
            return sum + availableMods[key].length;
        }, 0);
        
        // Enhanced debug logging
        console.log(`[hm-weapons] Available mods for ${weaponHash}: ${totalAvailable} total`);
        Object.keys(availableMods).forEach(key => {
            const mods = availableMods[key];
            console.log(`[hm-weapons]   ${key}: ${mods.length} mods - ${mods.map(m => m.name).join(', ')}`);
        });
        
        return availableMods;
    } catch (error) {
        console.error(`[hm-weapons] Error getting available mods for ${weaponHash}:`, error);
        return {};
    }
}

/**
 * Apply weapon component to player's weapon
 */
function applyWeaponComponent(source: number, componentName: string): boolean {
    try {
        console.log(`[hm-weapons] ========== applyWeaponComponent ==========`);
        console.log(`[hm-weapons] Player: ${source}, Component: ${componentName}`);
        
        // Check if player has the component
        if (!hasItem(source, componentName)) {
            console.warn(`[hm-weapons] ✗ Player ${source} doesn't have component ${componentName}`);
            return false;
        }

        // Apply to player's weapon via client
        emitNet('hm-weapons:applyComponent', source, componentName);
        
        console.log(`[hm-weapons] ✓ Applied component ${componentName} to player ${source}`);
        console.log(`[hm-weapons] ============================================`);
        return true;
    } catch (error) {
        console.error(`[hm-weapons] Error applying component ${componentName}:`, error);
        return false;
    }
}

/**
 * Remove weapon component from player's weapon
 */
function removeWeaponComponent(source: number, componentName: string): boolean {
    try {
        console.log(`[hm-weapons] ========== removeWeaponComponent ==========`);
        console.log(`[hm-weapons] Player: ${source}, Component: ${componentName}`);
        
        // Remove from player's weapon via client
        emitNet('hm-weapons:removeComponent', source, componentName);
        
        console.log(`[hm-weapons] ✓ Removed component ${componentName} from player ${source}`);
        console.log(`[hm-weapons] =============================================`);
        return true;
    } catch (error) {
        console.error(`[hm-weapons] Error removing component ${componentName}:`, error);
        return false;
    }
}

// Server events for weapon modding
onNet('hm-weapons:getAvailableMods', async (weaponHash: string) => {
    const source = global.source;
    console.log(`[hm-weapons] ========== getAvailableMods Request ==========`);
    console.log(`[hm-weapons] Player: ${source}, Weapon Hash: ${weaponHash} (type: ${typeof weaponHash})`);

    const availableMods = await getAvailableWeaponMods(source, weaponHash);
    emitNet('hm-weapons:availableModsResponse', source, availableMods);
});

onNet('hm-weapons:applyMod', (componentName: string) => {
    const source = global.source;   
    const success = applyWeaponComponent(source, componentName);
    emitNet('hm-weapons:modApplied', source, { success, componentName });
});

onNet('hm-weapons:removeMod', (componentName: string) => {
    const source = global.source;
    const success = removeWeaponComponent(source, componentName);
    emitNet('hm-weapons:modRemoved', source, { success, componentName });
});

// Test commands to verify integration
RegisterCommand('testhasitem', (source: number, args: string[]) => {
    if (args.length < 1) {
        console.log('[hm-weapons] Usage: /testhasitem <itemName>');
        return;
    }
    
    const itemName = args[0];
    const hasItemResult = hasItem(source, itemName);
    emitNet('chat:addMessage', source, {
        args: ['System', `You ${hasItemResult ? 'have' : 'do not have'} item: ${itemName}`]
    });
}, false);

RegisterCommand('testweaponmods', async (source: number, args: string[]) => {
    const weaponHash = args[0] || 'WEAPON_ASSAULTRIFLE';
    const availableMods = await getAvailableWeaponMods(source, weaponHash);
    console.log(`[hm-weapons] Available mods:`, availableMods);
}, false);
