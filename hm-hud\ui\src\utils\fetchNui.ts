/**
 * Simple wrapper around fetch API for NUI callbacks
 * @param eventName - The callback event name to send
 * @param data - The data to send along with the callback
 * @returns Promise with the response
 */
export async function fetchNui<T = any>(eventName: string, data: any = {}): Promise<T> {
  const resourceName = 'hm-hud'; // This should match your resource name
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: JSON.stringify(data),
  };

  try {
    const resp = await fetch(`https://${resourceName}/${eventName}`, options);
    
    // Check if the response is ok
    if (!resp.ok) {
      throw new Error(`HTTP error! status: ${resp.status}`);
    }
    
    const text = await resp.text();
    if (!text || text.trim() === '') {
      return {} as T;
    }
    
    try {
      return JSON.parse(text);
    } catch (parseError) {
      console.error('Failed to parse JSON response:', text);
      // Return a default success response if parsing fails but request succeeded
      return { status: 'ok' } as T;
    }
  } catch (error) {
    console.error(`fetchNui error for ${eventName}:`, error);
    throw error;
  }
}

/**
 * Mock the NUI environment for development in browser
 */
export const isEnvBrowser = (): boolean => !(window as any).invokeNative;

/**
 * Mock NUI callbacks for browser development
 * @param eventName - The callback event name
 * @param mockData - The mock data to return
 */
export const mockNuiCallback = (eventName: string, mockData: any = {}) => {
  if (isEnvBrowser()) {
    window.addEventListener('message', (event) => {
      if (event.data.type === eventName) {
        window.dispatchEvent(new MessageEvent('message', {
          data: {
            type: eventName,
            data: mockData,
          },
        }));
      }
    });
  }
};
