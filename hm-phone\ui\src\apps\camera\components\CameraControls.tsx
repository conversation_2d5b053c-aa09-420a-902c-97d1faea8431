import React from 'react';
import { useCameraStore } from '../stores/cameraStore';

/**
 * Camera Controls Component
 *
 * This component displays keybind indicators for the camera app.
 * These are not interactive buttons, just visual guides for the player.
 */
const CameraControls: React.FC = () => {
  const { settings } = useCameraStore();
  const { flashEnabled } = settings;

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-[9999]">
      {/* Controls container */}
      <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <div className="flex items-center space-x-4">
          {/* Flash Toggle Indicator */}
          <div className="flex items-center w-28">
            <div className="w-10 h-10 bg-gray-900 border-t border-l border-gray-700 border-b-2 border-r-2 border-b-gray-950 border-r-gray-950 rounded-md flex items-center justify-center text-white">
              <span className="font-bold text-sm">F</span>
            </div>
            <div className="ml-2 flex-1">
              <div className="flex items-center">
                <div className={`w-2 h-2 rounded-full ${flashEnabled ? 'bg-blue-500' : 'bg-gray-500'} mr-1.5`}></div>
                <span className="text-white text-xs whitespace-nowrap">Toggle Flash</span>
              </div>
            </div>
          </div>

          {/* Take Photo Indicator */}
          <div className="flex items-center w-28">
            <div className="w-20 h-10 bg-gray-900 border-t border-l border-gray-700 border-b-2 border-r-2 border-b-gray-950 border-r-gray-950 rounded-md flex items-center justify-center text-white">
              <span className="font-bold text-xs">SPACE</span>
            </div>
            <div className="ml-2 flex-1">
              <span className="text-white text-xs whitespace-nowrap">Take Photo</span>
            </div>
          </div>

          {/* Selfie Mode Toggle Indicator */}
          <div className="flex items-center w-28">
            <div className="w-10 h-10 bg-gray-900 border-t border-l border-gray-700 border-b-2 border-r-2 border-b-gray-950 border-r-gray-950 rounded-md flex items-center justify-center text-white">
              <span className="font-bold text-sm">R</span>
            </div>
            <div className="ml-2 flex-1">
              <span className="text-white text-xs whitespace-nowrap">Flip Camera</span>
            </div>
          </div>

          {/* Mode Switch Indicator */}
          <div className="flex items-center w-28">
            <div className="w-10 h-10 bg-gray-900 border-t border-l border-gray-700 border-b-2 border-r-2 border-b-gray-950 border-r-gray-950 rounded-md flex items-center justify-center text-white">
              <span className="font-bold text-sm">M</span>
            </div>
            <div className="ml-2 flex-1">
              <span className="text-white text-xs whitespace-nowrap">Switch Mode</span>
            </div>
          </div>

          {/* Exit Camera Indicator */}
          <div className="flex items-center w-28">
            <div className="w-10 h-10 bg-gray-900 border-t border-l border-gray-700 border-b-2 border-r-2 border-b-gray-950 border-r-gray-950 rounded-md flex items-center justify-center text-white">
              <span className="font-bold text-xs">ESC</span>
            </div>
            <div className="ml-2 flex-1">
              <span className="text-white text-xs whitespace-nowrap">Exit Camera</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CameraControls;