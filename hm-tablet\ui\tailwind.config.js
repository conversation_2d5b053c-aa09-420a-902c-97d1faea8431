/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Tablet-specific colors following HM theme
        tablet: {
          background: '#0a0a0a',
          surface: '#171717',
          border: '#262626',
          text: {
            primary: '#f5f5f5',
            secondary: '#a3a3a3',
            muted: '#737373',
          },
          accent: {
            primary: '#10b981', // emerald-500
            secondary: '#34d399', // emerald-400
            hover: '#059669', // emerald-600
          }
        }
      },
      animation: {
        'spin-slow': 'spin 8s linear infinite',
        'fade-in': 'fadeIn 0.3s ease-in-out forwards',
        'fade-out': 'fadeOut 0.3s ease-in-out forwards',
        'slide-in-right': 'slideInRight 0.3s ease-in-out forwards',
        'slide-in-left': 'slideInLeft 0.3s ease-in-out forwards',
        'slide-in-up': 'slideInUp 0.3s ease-in-out forwards',
        'slide-in-down': 'slideInDown 0.3s ease-in-out forwards',
        'bounce-in': 'bounceIn 0.5s ease-out forwards',
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        fadeOut: {
          '0%': { opacity: 1 },
          '100%': { opacity: 0 },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInUp: {
          '0%': { transform: 'translateY(100%)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        slideInDown: {
          '0%': { transform: 'translateY(-100%)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: 0 },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px #10b981, 0 0 10px #10b981, 0 0 15px #10b981' },
          '100%': { boxShadow: '0 0 10px #10b981, 0 0 20px #10b981, 0 0 30px #10b981' },
        },
      },
      aspectRatio: {
        'tablet': '4/3',
      },
      backgroundImage: {
        'tablet-gradient': 'linear-gradient(145deg, #0a0a0a 0%, #171717 100%)',
        'app-gradient': 'linear-gradient(145deg, #262626 0%, #404040 100%)',
      }
    },
  },
  corePlugins: {
    backgroundImage: true,
    gradientColorStops: true
  },
  plugins: [],
}
