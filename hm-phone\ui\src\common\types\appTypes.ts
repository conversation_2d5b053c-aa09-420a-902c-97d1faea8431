export interface AppView {
  name: string;
  tabs?: string[];
}

export type BaseApp = {
  id: number;
  name: string;
  icon: string;
  iconColor: string;
  description: string;
  colors: {
    bg: string;
    text: string;
  };
  settings: {
    notificationsEnabled?: boolean;
    sound?: boolean;
    sortBy?: string;
    displayMode?: string;
  };
  component: React.FC;
  path: string;
  views?: Record<string, AppView>;
};

export type SystemApp = BaseApp & {
  type: 'system';
};

export type StoreApp = BaseApp & {
  type: 'store';
  settings: {
    installed: boolean;
    notificationsEnabled?: boolean;
    sound?: boolean;
    sortBy?: string;
    displayMode?: string;
  };
};

export type App = SystemApp | StoreApp;
