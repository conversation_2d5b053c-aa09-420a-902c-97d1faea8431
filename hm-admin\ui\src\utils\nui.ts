/**
 * NUI Event Handler System for HM Admin
 * Handles all communication between the UI and the client script
 */

import { isBrowser } from "./environment";


// Resource name
const RESOURCE_NAME = 'hm-admin';

// NUI Message types
interface NuiMessage {
  type: string;
  [key: string]: any;
}

interface AdminNuiMessage extends NuiMessage {
  type: 'setVisible' | 'setHidden' | 'updateData' | 'notification';
  visible?: boolean;
  hidden?: boolean;
  data?: any;
  message?: string;
  level?: 'info' | 'success' | 'warning' | 'error';
}

// Event listeners registry
type NuiEventHandler = (data: any) => void;
const eventHandlers: Map<string, NuiEventHandler[]> = new Map();

/**
 * Send a message to the client script
 * @param action The action to perform
 * @param data The data to send
 */
export const sendNuiMessage = <T = any>(action: string, data: T = {} as T): void => {  if (isBrowser()) {
    console.log(`[${RESOURCE_NAME}] Would send NUI message:`, JSON.stringify({ action, data }, null, 2));
    return;
  }

  const resourceName = (window as any).GetParentResourceName?.() || RESOURCE_NAME;
  
  fetch(`https://${resourceName}/${action}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: JSON.stringify(data),
  }).catch(error => {
    console.error(`[${RESOURCE_NAME}] Error sending NUI message:`, error);
  });
};

/**
 * Register a callback for specific NUI message types
 * @param type The message type to listen for
 * @param callback The callback to execute
 * @returns Cleanup function to remove the listener
 */
export const onNuiEvent = <T = any>(type: string, callback: (data: T) => void): (() => void) => {
  if (!eventHandlers.has(type)) {
    eventHandlers.set(type, []);
  }
  
  eventHandlers.get(type)!.push(callback);

  // Return cleanup function
  return () => {
    const handlers = eventHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(callback);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  };
};

/**
 * Handle incoming NUI messages from the client script
 * @param event The message event
 */
const handleNuiMessage = (event: MessageEvent<AdminNuiMessage>) => {
  const { data } = event;

  // Validate message format
  if (!data || typeof data !== 'object' || !data.type) {
    console.warn(`[${RESOURCE_NAME}] Invalid NUI message format:`, JSON.stringify(data, null, 2));
    return;
  }
  // Emit to registered handlers
  const handlers = eventHandlers.get(data.type);
  if (handlers) {
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`[${RESOURCE_NAME}] Error in NUI event handler for ${data.type}:`, error);
      }
    });
  }
};

/**
 * Initialize the NUI event system
 * Call this once when the app starts
 */
export const initializeNuiSystem = (): (() => void) => {
  console.log(`[${RESOURCE_NAME}] Initializing NUI event system`);
  
  window.addEventListener('message', handleNuiMessage);

  // Return cleanup function
  return () => {
    console.log(`[${RESOURCE_NAME}] Cleaning up NUI event system`);
    window.removeEventListener('message', handleNuiMessage);
    eventHandlers.clear();
  };
};

/**
 * Common NUI actions
 */
export const NuiActions = {
  // Panel actions
  CLOSE_PANEL: 'hm-admin:closePanel',
  HIDE_PANEL: 'hm-admin:hidePanel',
  SHOW_PANEL: 'hm-admin:showPanel',
  
  // InfoPanel actions
  TOGGLE_INFO_PANEL: 'hm-admin:toggleInfoPanel',
  TELEPORT_TO_COORDS: 'hm-admin:teleportToCoords',
  FREEZE_ENTITY: 'hm-admin:freezeEntity',
  GET_TARGET_INFO: 'hm-admin:getTargetInfo',// Vehicle actions
  SPAWN_VEHICLE: 'hm-admin:spawnVehicle',
  REPAIR_VEHICLE: 'hm-admin:repairVehicle',
  DELETE_VEHICLE: 'hm-admin:deleteVehicle',
  SET_VEHICLE_COLOR: 'hm-admin:setVehicleColor',
  SET_VEHICLE_PLATE: 'hm-admin:setVehiclePlate',
  GET_VEHICLE_COLORS: 'hm-admin:getVehicleColors',
  GET_VEHICLE_LIST: 'hm-admin:getVehicleList',
  // Weather actions
  CHANGE_WEATHER: 'hm-admin:changeWeather',
  SET_WEATHER_INSTANT: 'hm-admin:setWeatherInstant',
  SET_TIME: 'hm-admin:setTime',
  FREEZE_TIME: 'hm-admin:freezeTime',
  SET_TIME_SCALE: 'hm-admin:setTimeScale',
  SET_WIND_SPEED: 'hm-admin:setWindSpeed',
  SET_WIND_DIRECTION: 'hm-admin:setWindDirection',
  RESET_WEATHER: 'hm-admin:resetWeather',
  RESET_TIME: 'hm-admin:resetTime',
  // Debug actions
  SET_DEBUG_MODE: 'hm-admin:setDebugMode',
    // Player management actions
  GET_PLAYER_LIST: 'hm-admin:getPlayerList',
  GET_PLAYER_INFO: 'hm-admin:getPlayerInfo',
  GIVE_ITEM: 'hm-admin:giveItem',
  GET_ITEM_LIST: 'hm-admin:getItemList',
  GIVE_WEAPON: 'hm-admin:giveWeapon',
  GET_WEAPON_LIST: 'hm-admin:getWeaponList',  TELEPORT_PLAYER_TO_PLAYER: 'hm-admin:teleportPlayerToPlayer',
  TELEPORT_PLAYER_TO_COORDS: 'hm-admin:teleportPlayerToCoords',
  KICK_PLAYER: 'hm-admin:kickPlayer',
  BAN_PLAYER: 'hm-admin:banPlayer',
  HEAL_PLAYER: 'hm-admin:healPlayer',
} as const;

/**
 * Common NUI message types
 */
export const NuiMessageTypes = {
  SET_VISIBLE: 'setVisible',
  SET_HIDDEN: 'setHidden', // Hide UI but keep focus
  UPDATE_DATA: 'updateData',
  NOTIFICATION: 'notification',
  
  // InfoPanel message types
  SHOW_INFO_PANEL: 'showInfoPanel',
  HIDE_INFO_PANEL: 'hideInfoPanel',
  UPDATE_TARGET_INFO: 'updateTargetInfo',VEHICLE_COLORS: 'vehicleColors',
  VEHICLE_LIST: 'vehicleList',
  PLAYER_LIST: 'playerList',
  PLAYER_INFO: 'playerInfo',
  ITEM_LIST: 'itemList',
  WEAPON_LIST: 'weaponList',
} as const;

/**
 * Mock NUI event for development
 * @param type The message type to mock
 * @param data The data to send
 */
export const mockNuiEvent = <T = any>(type: string, data: T): void => {
  if (!isBrowser()) {
    console.warn(`[${RESOURCE_NAME}] Cannot mock NUI event in FiveM environment`);
    return;
  }

  window.dispatchEvent(
    new MessageEvent('message', {
      data: {
        type,
        ...data,
      } as AdminNuiMessage,
    })
  );
};

// Development helpers
if (isBrowser()) {
  // Add global helpers for development
  (window as any).hmAdminNui = {
    sendMessage: sendNuiMessage,
    mockEvent: mockNuiEvent,
    show: () => mockNuiEvent(NuiMessageTypes.SET_VISIBLE, { visible: true }),
    hide: () => mockNuiEvent(NuiMessageTypes.SET_VISIBLE, { visible: false }),
  };
  
  console.log(`[${RESOURCE_NAME}] Development helpers added to window.hmAdminNui`);
}
