/**
 * Phone Client - NUI Handlers
 *
 * This file contains handlers for NUI events sent from the UI.
 * It registers handlers for various actions and provides utility functions for sending data to the UI.
 */

/// <reference types="@citizenfx/client" />

import { registerAppHandler, sendToNUI } from './nui';
import { FrameworkPlayer } from '@shared/framework/types';
import { PhoneStateEnum } from './types';

/**
 * Transform FrameworkPlayer data to the PlayerData format expected by the UI
 *
 * @param player FrameworkPlayer object from the framework
 * @returns PlayerData object for the UI
 */
function transformPlayerData(player: FrameworkPlayer | null): any {
    if (!player) return null;

    // Create a properly formatted player data object for the UI
    return {
        // Basic info (required fields)
        name: player.name || `${player.firstName || ''} ${player.lastName || ''}`.trim() || 'Unknown',
        stateid: player.stateid || '',

        // Basic info (optional fields)
        firstName: player.firstName || '',
        lastName: player.lastName || '',
        phoneNumber: player.phoneNumber || '',
        identifier: player.identifier || '',

        // Job info
        job: {
            name: player.job?.name || 'unemployed',
            label: player.job?.label || 'Unemployed',
            grade: player.job?.grade || 0,
            gradeName: player.job?.gradeName || '',
            payment: player.job?.payment || 0,
            onDuty: player.job?.onDuty || false,
            isBoss: player.job?.isBoss || false
        },

        // Gang info (optional)
        gang: player.gang
            ? {
                  name: player.gang.name || 'none',
                  label: player.gang.label || 'No Gang',
                  grade: player.gang.grade || 0,
                  isBoss: player.gang.isBoss || false
              }
            : undefined,

        // Character status (optional)
        status: {
            hunger: player.metadata?.hunger || 100,
            thirst: player.metadata?.thirst || 100,
            stress: player.metadata?.stress || 0,
            isDead: player.metadata?.isDead || false
        },

        // Additional info (optional)
        money: player.money
            ? {
                  cash: player.money.cash || 0,
                  bank: player.money.bank || 0
              }
            : undefined,

        // Profile picture URL (empty by default)
        imageUrl: ''
    };
}

/**
 * Send player data to the UI
 *
 * @param force Force sending data even if it has been sent before
 * @returns True if player data was sent, false otherwise
 */
export function sendPlayerDataToUI(force = false): boolean {
    try {
        // Skip if data has already been sent and force is false
        if (global.playerDataSentToUI && !force) {
            return true;
        }

        // Get player data from the framework
        const player = global.getPlayerData();
        if (!player) {
            console.error('[Phone] Could not get player data');
            return false;
        }

        // Transform player data to the format expected by the UI
        const playerData = transformPlayerData(player);
        if (!playerData) {
            console.error('[Phone] Could not transform player data');
            return false;
        }

        // Send player data to the UI
        sendToNUI('phone', 'playerData', playerData);

        // Mark data as sent
        global.playerDataSentToUI = true;
        return true;
    } catch (error) {
        console.error('[Phone] Error sending player data to UI:', error);
        return false;
    }
}

// Cache for app data
export const appDataCache: Record<string, any> = {};

/**
 * Initialize all NUI handlers
 */
export function initializeHandlers(): void {
    // Register handler for getPlayerData action
    registerAppHandler('phone', 'getPlayerData', () => {
        console.log('[Phone] Received getPlayerData request from UI');
        return { data: transformPlayerData(global.getPlayerData()) };
    });

    // Register handler for getNearbyPlayers
    registerAppHandler('phone', 'getNearbyPlayers', () => {
        return { success: true, data: [] };
    });

    // Register handler for app opening to load app-specific data
    registerAppHandler('phone', 'openApp', (data: { app: string }) => {
        const appName = data.app;
        console.log(`[Phone] App opened: ${appName}`);

        // Trigger app-specific data loading when an app is opened
        // This ensures data is fresh when the app is opened
        switch (appName) {
            case 'messages':
                SendNUIMessage({
                    app: 'messages',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'contacts':
                SendNUIMessage({
                    app: 'contacts',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'photos':
                SendNUIMessage({
                    app: 'photos',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'camera':
                // No need to trigger data loading for camera
                break;

            case 'settings':
                // Settings app already loaded data at startup, but we can refresh it
                SendNUIMessage({
                    app: 'settings',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'services':
                // Trigger services data loading
                SendNUIMessage({
                    app: 'services',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'banking':
                // Trigger banking data loading
                SendNUIMessage({
                    app: 'banking',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'notes':
                // Trigger notes data loading
                SendNUIMessage({
                    app: 'notes',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'yellowPages':
                // Trigger Yellow Pages data loading
                SendNUIMessage({
                    app: 'yellowPages',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'music':
                // Trigger music data loading
                SendNUIMessage({
                    app: 'music',
                    type: 'appOpened',
                    data: {}
                });
                break;

            case 'lifeSnap':
                // Trigger LifeSnap data loading
                SendNUIMessage({
                    app: 'lifeSnap',
                    type: 'appOpened',
                    data: {}
                });
                break;

            // Add more apps as needed
            default:
                console.log(`[Phone] App ${appName} opened - no specific initialization needed`);
                break;
        }

        return { success: true };
    });

    // Register handler for phone closing to clear cache
    registerAppHandler('phone', 'cleanup', (data: { retainApps: string[]; discardApps: string[] }) => {
        // Clear cache for apps that should be discarded
        if (data.discardApps && Array.isArray(data.discardApps)) {
            data.discardApps.forEach(app => {
                delete appDataCache[app];
            });
        }

        return { success: true };
    });

    console.log('[Phone] Core handlers initialized');
}

/**
 * Initialize all app handlers but don't load data yet
 * This registers the handlers for all apps, but data will only be loaded when the app is opened
 *
 * Initialization Strategy:
 * 1. Register Early: All app handlers are registered during resource start
 * 2. Load Late: App-specific data is loaded only when the app is opened
 * 3. Resource Initialization: Resource-intensive apps initialize their resources on demand
 */
export function initializeAppHandlers(): void {
    console.log('[Phone] Initializing app handlers');

    // Import and initialize app handlers
    // These only register the handlers, they don't load data yet

    // Contacts App
    // - Core dependency, used by other apps
    // - Handlers registered at startup
    // - Data loaded when app is opened
    import('./apps/contacts')
        .then(module => {
            module.initializeContactsApp();
        })
        .catch(error => {
            console.error('[Phone] Error initializing contacts app handlers:', error);
        });

    // Messages App
    // - Communication functionality
    // - Handlers registered at startup
    // - Conversation list loaded when app is opened
    // - Messages loaded when conversation is opened
    import('./apps/messages')
        .then(module => {
            module.initializeMessagesApp();
            console.log('[Phone] Messages app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing messages app handlers:', error);
        });

    // Camera App
    // - Resource-intensive functionality
    // - Handlers registered at startup
    // - Camera resources initialized when app is opened
    // - Resources cleaned up when app is closed
    import('./apps/camera')
        .then(module => {
            module.initializeCameraApp();
            console.log('[Phone] Camera app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing camera app handlers:', error);
        });

    // Photos App
    // - Media gallery functionality
    // - Handlers registered at startup
    // - Photo data loaded when app is opened
    // - Full-size images loaded on demand
    import('./apps/photos')
        .then(module => {
            module.initializePhotosApp();
            console.log('[Phone] Photos app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing photos app handlers:', error);
        });

    // Settings App
    // - System-wide impact
    // - Handlers registered at startup
    // - Settings loaded at startup
    // - Applied immediately when changed
    import('./apps/settings')
        .then(module => {
            module.initializeSettingsApp();
            console.log('[Phone] Settings app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing settings app handlers:', error);
        });

    // Initialize Banking app (security sensitive)
    import('./apps/banking')
        .then(module => {
            module.initializeBankingApp();
            console.log('[Phone] Banking app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing banking app handlers:', error);
        });

    // Initialize Yellow Pages app
    import('./apps/yellowPages')
        .then(module => {
            module.initializeYellowPagesApp();
            console.log('[Phone] Yellow Pages app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing Yellow Pages app handlers:', error);
        });

    // Initialize Music app (hybrid initialization)
    // import('./apps/music')
    //     .then(module => {
    //         module.initializeMusicApp();
    //         console.log('[Phone] Music app handlers initialized');
    //     })
    //     .catch(error => {
    //         console.error('[Phone] Error initializing Music app handlers:', error);
    //     });

    // Initialize LifeSnap app (on-demand initialization)
    import('./apps/lifeSnap')
        .then(module => {
            module.initializeLifeSnapApp();
        })
        .catch(error => {
            console.error('[Phone] Error initializing LifeSnap app handlers:', error);
        });

    // Initialize Notes app
    import('./apps/notes')
        .then(module => {
            module.initializeNotesApp();
            console.log('[Phone] Notes app handlers initialized');
        })
        .catch(error => {
            console.error('[Phone] Error initializing Notes app handlers:', error);
        });

    // Future app initializations:
    // - Maps/Navigation app (resource intensive)
    // - Job apps (based on player job)

    console.log('[Phone] App handlers initialization queued');
}

/**
 * Generic function to create a cached data handler
 * This utility function can be used by app-specific handlers to implement caching
 *
 * @param appName Name of the app
 * @param fetchFunction Function to fetch data if not cached
 * @returns Handler function
 *
 * @example
 * // Usage in an app-specific handler:
 * registerAppHandler(
 *   'appName',
 *   'getData',
 *   createCachedDataHandler('appName', () => {
 *     // Fetch and return data
 *     return fetchDataFromServer();
 *   })
 * );
 */
export function createCachedDataHandler(appName: string, fetchFunction: () => any) {
    return () => {
        // Check if we have cached data
        if (appDataCache[appName]) {
            return { success: true, data: appDataCache[appName] };
        }

        // If not, fetch it and cache it
        try {
            const data = fetchFunction();
            appDataCache[appName] = data;
            return { success: true, data };
        } catch (error) {
            console.error(`[Phone] Error fetching ${appName} data:`, error);
            return { success: false, error: 'Failed to fetch data' };
        }
    };
}

/**
 * Note: App-specific handlers are now initialized directly in their respective app files.
 * This ensures better code organization and maintainability.
 *
 * The initialization process is:
 * 1. App handlers are registered during resource start (in initializeAppHandlers)
 * 2. Data is loaded on demand when an app is opened (in the UI)
 *
 * This approach:
 * - Reduces initial load time
 * - Improves memory usage
 * - Ensures data is fresh when needed
 * - Follows the "register early, load late" pattern
 */

/**
 * Update player data if the phone is open
 * This should be called periodically to keep the UI in sync with the game state
 */
export function updatePlayerDataIfPhoneOpen(): void {
    // Only send updates if the phone is open
    if (global.phoneState === PhoneStateEnum.OPEN) {
        sendPlayerDataToUI();
    }
}
