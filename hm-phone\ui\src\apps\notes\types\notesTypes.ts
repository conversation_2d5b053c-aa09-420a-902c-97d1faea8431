export type NoteCategory = {
  id: string;
  name: string;
  color: string;
};

export type NoteAttachment = {
  id: string;
  type: 'image' | 'audio' | 'file';
  url: string;
  name: string;
  size: number;
};

export interface Note {
  id: number;
  title: string;
  content: string;
  timestamp: number;
  color?: string;
  isPinned?: boolean;
  categories: string[];
  attachments: NoteAttachment[];
  isFavorite?: boolean;
  lastEdited: number;
  createdAt: number;
  fontSize?: number;
  backgroundColor?: string;
}
