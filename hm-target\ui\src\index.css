@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Target Overlay Animations */
@keyframes target-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes target-scan {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes target-glow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes target-brackets {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes cyberpunk-glitch {
  0%, 90%, 100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-1px, 1px);
  }
  40% {
    transform: translate(1px, -1px);
  }
  60% {
    transform: translate(-1px, -1px);
  }
  80% {
    transform: translate(1px, 1px);
  }
}

.animate-target-pulse {
  animation: target-pulse 2s ease-in-out infinite;
}

.animate-target-scan {
  animation: target-scan 3s linear infinite;
}

.animate-target-glow {
  animation: target-glow 1.5s ease-in-out infinite;
}

.animate-target-brackets {
  animation: target-brackets 2s ease-in-out infinite;
}

.animate-cyberpunk-glitch {
  animation: cyberpunk-glitch 0.3s ease-in-out infinite;
}

/* Orbital satellite animations */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes spin-reverse {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

@keyframes float-1 {
  0%, 100% { 
    transform: translate(8px, -8px); 
    opacity: 0.6; 
  }
  25% { 
    transform: translate(-6px, -12px); 
    opacity: 1; 
  }
  50% { 
    transform: translate(-8px, 8px); 
    opacity: 0.4; 
  }
  75% { 
    transform: translate(6px, 12px); 
    opacity: 0.8; 
  }
}

@keyframes float-2 {
  0%, 100% { 
    transform: translate(-6px, 10px); 
    opacity: 0.4; 
  }
  33% { 
    transform: translate(8px, -6px); 
    opacity: 0.9; 
  }
  66% { 
    transform: translate(-10px, -8px); 
    opacity: 0.6; 
  }
}

@keyframes float-3 {
  0%, 100% { 
    transform: translate(10px, 6px); 
    opacity: 0.5; 
  }
  50% { 
    transform: translate(-8px, -10px); 
    opacity: 1; 
  }
}

@keyframes micro-orbit-1 {
  0% { 
    transform: translate(12px, 0) rotate(0deg); 
    opacity: 0.8; 
  }
  25% { 
    transform: translate(0, 12px) rotate(90deg); 
    opacity: 0.4; 
  }
  50% { 
    transform: translate(-12px, 0) rotate(180deg); 
    opacity: 0.9; 
  }
  75% { 
    transform: translate(0, -12px) rotate(270deg); 
    opacity: 0.6; 
  }
  100% { 
    transform: translate(12px, 0) rotate(360deg); 
    opacity: 0.8; 
  }
}

@keyframes micro-orbit-2 {
  0% { 
    transform: translate(8px, 8px) rotate(45deg); 
    opacity: 0.6; 
  }
  25% { 
    transform: translate(-8px, 8px) rotate(135deg); 
    opacity: 1; 
  }
  50% { 
    transform: translate(-8px, -8px) rotate(225deg); 
    opacity: 0.4; 
  }
  75% { 
    transform: translate(8px, -8px) rotate(315deg); 
    opacity: 0.8; 
  }
  100% { 
    transform: translate(8px, 8px) rotate(405deg); 
    opacity: 0.6; 
  }
}

/* Animation utilities */
.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 12s linear infinite;
}

.animate-float-1 {
  animation: float-1 3s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 2.5s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 3.5s ease-in-out infinite;
}

.animate-micro-orbit-1 {
  animation: micro-orbit-1 2s linear infinite;
}

.animate-micro-orbit-2 {
  animation: micro-orbit-2 1.8s linear infinite;
}

/* Morphing Energy Web Animations */
@keyframes energy-morph {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    border-radius: 50%;
  }
  25% { 
    transform: scale(1.1) rotate(90deg);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  50% { 
    transform: scale(0.9) rotate(180deg);
    border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
  }
  75% { 
    transform: scale(1.05) rotate(270deg);
    border-radius: 40% 60% 60% 40% / 60% 40% 40% 60%;
  }
}

@keyframes plasma-flow {
  0% { 
    stroke-dashoffset: 0;
    opacity: 0.3;
  }
  50% { 
    stroke-dashoffset: -10;
    opacity: 0.8;
  }
  100% { 
    stroke-dashoffset: -20;
    opacity: 0.3;
  }
}

@keyframes energy-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.5;
  }
  50% { 
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes neural-spark {
  0%, 100% { 
    opacity: 0;
    transform: scale(0.5);
  }
  50% { 
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes quantum-shimmer {
  0%, 100% { 
    background-position: 0% 50%;
  }
  50% { 
    background-position: 100% 50%;
  }
}

.animate-energy-morph {
  animation: energy-morph 4s ease-in-out infinite;
}

.animate-plasma-flow {
  animation: plasma-flow 3s linear infinite;
}

.animate-energy-pulse {
  animation: energy-pulse 2s ease-in-out infinite;
}

.animate-neural-spark {
  animation: neural-spark 1.5s ease-in-out infinite;
}

.animate-quantum-shimmer {
  animation: quantum-shimmer 3s ease-in-out infinite;
  background: linear-gradient(-45deg, transparent, rgba(34, 197, 94, 0.3), transparent);
  background-size: 200% 200%;
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.87);
  background-color: transparent;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Target system specific styles */
.target-overlay {
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
}

.target-indicator {
  position: absolute;
  border: 2px solid #10b981;
  border-radius: 8px;
  background: rgba(16, 185, 129, 0.1);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
}

.target-options {
  position: absolute;
  background: rgba(23, 23, 23, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  min-width: 200px;
  max-width: 300px;
  z-index: 1001;
}

.progress-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(23, 23, 23, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  padding: 24px;
  min-width: 320px;
  z-index: 1002;
}
