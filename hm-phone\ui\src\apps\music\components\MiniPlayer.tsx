import React, { useEffect, useState } from 'react';
import { useMusicStore } from '../stores/musicStore';
import { useNavigation } from '../../../navigation/hooks';
import { MiniPlayerProps } from '../types/musicTypes';

const MiniPlayer: React.FC<MiniPlayerProps> = () => {
  const { currentSong, isPlaying, currentTime, duration, togglePlay, nextSong, clearCurrentSong } =
    useMusicStore();
  const { openView } = useNavigation();

  // Start playback timer when component mounts
  useEffect(() => {
    // This effect will trigger the playback timer in the store
    // We toggle twice to ensure the timer starts (since isPlaying is already true)
    if (isPlaying) {
      togglePlay();
      togglePlay();
    }
    // We only want this to run once on mount, not on every isPlaying/togglePlay change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Force re-render every second to update progress bar
  const [, setForceUpdate] = useState(0);

  useEffect(() => {
    if (isPlaying) {
      const forceUpdateTimer = setInterval(() => {
        // This forces a re-render which will update the progress bar
        setForceUpdate(prev => prev + 1);
      }, 1000);

      return () => clearInterval(forceUpdateTimer);
    }
  }, [isPlaying]);

  if (!currentSong) return null;

  return (
    <div className="bg-black/90 backdrop-blur-md border-t border-gray-800/50 h-14 flex flex-col border-b-0">
      {/* Progress bar */}
      <div className="w-full h-0.5 bg-gray-800 relative">
        <div
          className="absolute top-0 left-0 h-full bg-pink-500"
          style={{ width: `${((currentTime || 0) / (duration || 1)) * 100}%` }}
        ></div>
      </div>

      {/* Content */}
      <div className="px-2 h-14 flex items-center">
        {/* Song info and thumbnail */}
        <div
          className="flex-1 flex items-center cursor-pointer"
          onClick={() => openView('nowPlaying', { songId: currentSong.id })}
        >
          <img
            src={currentSong.imageUrl || 'https://picsum.photos/50'}
            alt={currentSong.title}
            className="w-9 h-9 rounded-md mr-2"
          />
          <div className="truncate">
            <div className="text-sm font-medium text-white truncate">{currentSong.title}</div>
            <div className="text-xs text-gray-400 truncate">
              {currentSong.artist?.name || 'Unknown Artist'}
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-2">
          <button
            className="text-white cursor-pointer"
            onClick={e => {
              e.stopPropagation();
              togglePlay();
            }}
          >
            <i className={`fas ${isPlaying ? 'fa-pause' : 'fa-play'}`}></i>
          </button>

          <button
            className="text-white cursor-pointer"
            onClick={e => {
              e.stopPropagation();
              nextSong();
            }}
          >
            <i className="fas fa-step-forward"></i>
          </button>

          <button
            className="text-white cursor-pointer ml-1"
            onClick={e => {
              e.stopPropagation();
              clearCurrentSong();
            }}
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MiniPlayer;
