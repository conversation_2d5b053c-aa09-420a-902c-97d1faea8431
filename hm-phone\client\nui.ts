/**
 * Phone Client - NUI Communication
 *
 * This file handles communication between the client and the NUI interface.
 * It provides a structured way to register handlers for NUI events and send data to the UI.
 */

/// <reference types="@citizenfx/client" />

// Type definitions for app handlers
type AppHandler = (data: any) => any;
type AppHandlers = Record<string, Record<string, AppHandler>>;

// App handlers registry
const appHandlers: AppHandlers = {};

// Register the main NUI event handler at the module level
RegisterNuiCallback('nuiEvent', (data: { app: string; action: string; data: any }, cb: (data: any) => void) => {
    const { app, action, data: eventData } = data;

    console.log(`[NUI] Received event: ${app}:${action}`);

    if (appHandlers[app] && appHandlers[app][action]) {
        try {
            const result = appHandlers[app][action](eventData);
            cb({ success: true, data: result });
        } catch (error) {
            console.error(`[NUI] Error handling ${app}:${action}:`, error);
            cb({ success: false, error: String(error) });
        }
    } else {
        console.error(`[NUI] No handler for ${app}:${action}`);
        cb({ success: false, error: `No handler for ${app}:${action}` });
    }
});

/**
 * Register an app handler for a specific action
 *
 * @param app The app name
 * @param action The action name
 * @param handler The handler function
 */
export function registerAppHandler(app: string, action: string, handler: AppHandler): void {
    if (!appHandlers[app]) {
        appHandlers[app] = {};
    }

    appHandlers[app][action] = handler;
}

/**
 * Send data to the NUI interface
 *
 * @param app The app name
 * @param type The event type
 * @param data The data to send
 */
export function sendToNUI(app: string, type: string, data: any): void {
    SendNUIMessage({
        app,
        type,
        data
    });
}
