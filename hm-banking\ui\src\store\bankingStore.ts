// e:\Fivem\HMProject\resources\[hm]\hm-banking\ui\src\store\bankingStore.ts
import { create } from 'zustand';
import { BankAccount, BankingUIState, BankingUIActions, Transaction } from '../../../scripts/shared/types';
import { isBrowserEnv } from '../utils/environment'; // Correct import

// Mock data for initial development - only used in browser
const MOCK_ACCOUNTS: BankAccount[] = [
  {
    accountId: 'acc_1_browser',
    accountNumber: '1234-5678 (<PERSON>rows<PERSON>)',
    balance: 15000.75,
    characterId: 'char_1_browser', // This would be the owner identifier
    accountType: 'personal',
    isDefault: true,
  },
  {
    accountId: 'acc_2_browser',
    accountNumber: '9012-3456 (<PERSON>rows<PERSON>)',
    balance: 1500.00,
    characterId: 'char_1_browser',
    accountType: 'savings',
  }
];

const MOCK_TRANSACTIONS: Transaction[] = [
    // Recent transactions
    {
        transactionId: 'txn_1_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 2500,
        timestamp: Date.now() - ******** * 1, // 1 day ago
        description: 'Weekly Salary Payment',
        newBalance: 15000.75,
    },
    {
        transactionId: 'txn_2_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -85.30,
        timestamp: Date.now() - ******** * 2, // 2 days ago
        description: 'Gas Station - Route 68',
        newBalance: 12500.75,
    },
    {
        transactionId: 'txn_3_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -250,
        timestamp: Date.now() - ******** * 3, // 3 days ago
        description: 'Rent Payment to Downtown Apartments',
        counterpartyName: 'Downtown Apartments',
        counterpartyAccountId: 'acc_downtown_apts',
        newBalance: 12586.05,
    },
    {
        transactionId: 'txn_4_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -45.99,
        timestamp: Date.now() - ******** * 4, // 4 days ago
        description: 'Grocery Store - 24/7 Supermarket',
        newBalance: 12836.05,
    },
    {
        transactionId: 'txn_5_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 150,
        timestamp: Date.now() - ******** * 5, // 5 days ago
        description: 'Payment from Jane Smith',
        counterpartyName: 'Jane Smith',
        counterpartyAccountId: 'acc_jane_smith',
        newBalance: 12882.04,
    },
    {
        transactionId: 'txn_6_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -12.50,
        timestamp: Date.now() - ******** * 6, // 6 days ago
        description: 'Coffee Shop - Bean Machine',
        newBalance: 12732.04,
    },
    {
        transactionId: 'txn_7_browser',
        accountId: 'acc_1_browser',
        type: 'business_payout',
        amount: 750,
        timestamp: Date.now() - ******** * 7, // 1 week ago
        description: 'Business Revenue Share - LS Customs',
        newBalance: 12744.54,
    },
    {
        transactionId: 'txn_8_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -200,
        timestamp: Date.now() - ******** * 8, // 8 days ago
        description: 'ATM Withdrawal - Pillbox Medical',
        newBalance: 11994.54,
    },
    {
        transactionId: 'txn_9_browser',
        accountId: 'acc_1_browser',
        type: 'fee',
        amount: -5.00,
        timestamp: Date.now() - ******** * 9, // 9 days ago
        description: 'Monthly Account Maintenance Fee',
        newBalance: 12194.54,
    },
    {
        transactionId: 'txn_10_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 2500,
        timestamp: Date.now() - ******** * 10, // 10 days ago
        description: 'Weekly Salary Payment',
        newBalance: 12199.54,
    },
    // More transactions for better scrolling testing
    {
        transactionId: 'txn_11_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -300,
        timestamp: Date.now() - ******** * 12, // 12 days ago
        description: 'Car Insurance Payment',
        counterpartyName: 'LS Insurance Co.',
        counterpartyAccountId: 'acc_ls_insurance',
        newBalance: 9699.54,
    },
    {
        transactionId: 'txn_12_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -125.75,
        timestamp: Date.now() - ******** * 14, // 2 weeks ago
        description: 'Clothing Store - Suburban',
        newBalance: 9999.54,
    },
    {
        transactionId: 'txn_13_browser',
        accountId: 'acc_1_browser',
        type: 'salary',
        amount: 3000,
        timestamp: Date.now() - ******** * 17, // 17 days ago
        description: 'Bi-weekly Salary - LSPD',
        newBalance: 10125.29,
    },
    {
        transactionId: 'txn_14_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -67.89,
        timestamp: Date.now() - ******** * 18, // 18 days ago
        description: 'Restaurant - Up-n-Atom Burger',
        newBalance: 7125.29,
    },
    {
        transactionId: 'txn_15_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 500,
        timestamp: Date.now() - ******** * 20, // 20 days ago
        description: 'Loan Repayment from Mike Torres',
        counterpartyName: 'Mike Torres',
        counterpartyAccountId: 'acc_mike_torres',
        newBalance: 7193.18,
    },
    {
        transactionId: 'txn_16_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -89.99,
        timestamp: Date.now() - ******** * 22, // 22 days ago
        description: 'Electronics Store - Tech Central',
        newBalance: 6693.18,
    },
    {
        transactionId: 'txn_17_browser',
        accountId: 'acc_1_browser',
        type: 'invoice_payment',
        amount: -450,
        timestamp: Date.now() - ******** * 25, // 25 days ago
        description: 'Medical Bill - Pillbox Medical Center',
        newBalance: 6783.17,
    },
    {
        transactionId: 'txn_18_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 1200,
        timestamp: Date.now() - ******** * 28, // 4 weeks ago
        description: 'Tax Refund - State of San Andreas',
        newBalance: 7233.17,
    },
    // Additional transactions for extensive scrolling test
    {
        transactionId: 'txn_23_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -35.50,
        timestamp: Date.now() - ******** * 30, // 30 days ago
        description: 'Parking Meter - City Hall',
        newBalance: 6033.17,
    },
    {
        transactionId: 'txn_24_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 75,
        timestamp: Date.now() - ******** * 32, // 32 days ago
        description: 'Refund - Ammu-Nation',
        counterpartyName: 'Ammu-Nation',
        counterpartyAccountId: 'acc_ammunation',
        newBalance: 6068.67,
    },
    {
        transactionId: 'txn_25_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -189.99,
        timestamp: Date.now() - ******** * 34, // 34 days ago
        description: 'Mechanic Services - Benny\'s',
        newBalance: 5993.67,
    },
    {
        transactionId: 'txn_26_browser',
        accountId: 'acc_1_browser',
        type: 'business_payout',
        amount: 650,
        timestamp: Date.now() - ******** * 36, // 36 days ago
        description: 'Business Share - Diamond Casino',
        newBalance: 6183.66,
    },
    {
        transactionId: 'txn_27_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -25.00,
        timestamp: Date.now() - ******** * 38, // 38 days ago
        description: 'Vending Machine - LSIA',
        newBalance: 5533.66,
    },
    {
        transactionId: 'txn_28_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -400,
        timestamp: Date.now() - ******** * 40, // 40 days ago
        description: 'Utility Bill Payment',
        counterpartyName: 'LS Water & Power',
        counterpartyAccountId: 'acc_lswp',
        newBalance: 5558.66,
    },
    {
        transactionId: 'txn_29_browser',
        accountId: 'acc_1_browser',
        type: 'salary',
        amount: 2800,
        timestamp: Date.now() - ******** * 42, // 42 days ago
        description: 'Bi-weekly Salary - LSPD',
        newBalance: 5958.66,
    },
    {
        transactionId: 'txn_30_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -156.78,
        timestamp: Date.now() - ******** * 44, // 44 days ago
        description: 'Shopping - Premium Deluxe Motorsport',
        newBalance: 3158.66,
    },
    {
        transactionId: 'txn_31_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 200,
        timestamp: Date.now() - ******** * 46, // 46 days ago
        description: 'Birthday Gift from Family',
        counterpartyName: 'Rodriguez Family',
        counterpartyAccountId: 'acc_rodriguez_fam',
        newBalance: 3315.44,
    },
    {
        transactionId: 'txn_32_browser',
        accountId: 'acc_1_browser',
        type: 'fee',
        amount: -15.00,
        timestamp: Date.now() - ******** * 48, // 48 days ago
        description: 'Wire Transfer Fee',
        newBalance: 3115.44,
    },
    {
        transactionId: 'txn_33_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 5000,
        timestamp: Date.now() - ******** * 50, // 50 days ago
        description: 'Insurance Payout - Vehicle Accident',
        newBalance: 3130.44,
    },
    {
        transactionId: 'txn_34_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -78.90,
        timestamp: Date.now() - ******** * 52, // 52 days ago
        description: 'Pharmacy - Pill Box Pharmacy',
        newBalance: -1869.56,
    },
    {
        transactionId: 'txn_35_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -350,
        timestamp: Date.now() - ******** * 54, // 54 days ago
        description: 'Loan Payment - Maze Bank',
        counterpartyName: 'Maze Bank Loans',
        counterpartyAccountId: 'acc_maze_loans',
        newBalance: -1790.66,
    },
    // Savings account transactions
    {
        transactionId: 'txn_19_browser',
        accountId: 'acc_2_browser',
        type: 'transfer_in',
        amount: 500,
        timestamp: Date.now() - ******** * 7, // 1 week ago
        description: 'Monthly Savings Transfer',
        counterpartyName: 'Personal Account',
        counterpartyAccountId: 'acc_1_browser',
        newBalance: 1500.00,
    },
    {
        transactionId: 'txn_20_browser',
        accountId: 'acc_2_browser',
        type: 'interest_payment',
        amount: 15.75,
        timestamp: Date.now() - ******** * 30, // 1 month ago
        description: 'Monthly Interest Payment - 3.15% APY',
        newBalance: 1000.00,
    },
    {
        transactionId: 'txn_21_browser',
        accountId: 'acc_2_browser',
        type: 'transfer_in',
        amount: 500,
        timestamp: Date.now() - ******** * 37, // 5+ weeks ago
        description: 'Monthly Savings Transfer',
        counterpartyName: 'Personal Account',
        counterpartyAccountId: 'acc_1_browser',
        newBalance: 984.25,
    },
    {
        transactionId: 'txn_22_browser',
        accountId: 'acc_2_browser',
        type: 'account_creation',
        amount: 484.25,
        timestamp: Date.now() - ******** * 45, // Initial deposit
        description: 'Initial Savings Account Deposit',
        newBalance: 484.25,
    },    {
        transactionId: 'txn_36_browser',
        accountId: 'acc_2_browser',
        type: 'transfer_in',
        amount: 300,
        timestamp: Date.now() - ******** * 60, // 2 months ago
        description: 'Emergency Fund Transfer',
        counterpartyName: 'Personal Account',
        counterpartyAccountId: 'acc_1_browser',
        newBalance: 184.25,
    },
    // Add many more transactions for scrolling testing
    {
        transactionId: 'txn_37_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -25.99,
        timestamp: Date.now() - ******** * 56,
        description: 'Fast Food - Cluckin\' Bell',
        newBalance: -1816.65,
    },
    {
        transactionId: 'txn_38_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 1800,
        timestamp: Date.now() - ******** * 58,
        description: 'Freelance Work Payment',
        newBalance: -1841.64,
    },
    {
        transactionId: 'txn_39_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -100,
        timestamp: Date.now() - ******** * 60,
        description: 'Gift to Sarah Johnson',
        counterpartyName: 'Sarah Johnson',
        counterpartyAccountId: 'acc_sarah_j',
        newBalance: -3641.64,
    },
    {
        transactionId: 'txn_40_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -45.00,
        timestamp: Date.now() - ******** * 62,
        description: 'Gas Station - Xero Gas',
        newBalance: -3541.64,
    },
    {
        transactionId: 'txn_41_browser',
        accountId: 'acc_1_browser',
        type: 'salary',
        amount: 2750,
        timestamp: Date.now() - ******** * 64,
        description: 'Bi-weekly Salary - LSPD',
        newBalance: -3496.64,
    },
    {
        transactionId: 'txn_42_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -150.00,
        timestamp: Date.now() - ******** * 66,
        description: 'Barber Shop - Hair on Hawick',
        newBalance: -6246.64,
    },
    {
        transactionId: 'txn_43_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 400,
        timestamp: Date.now() - ******** * 68,
        description: 'Commission from Real Estate Sale',
        counterpartyName: 'Dynasty 8 Real Estate',
        counterpartyAccountId: 'acc_dynasty8',
        newBalance: -6396.64,
    },
    {
        transactionId: 'txn_44_browser',
        accountId: 'acc_1_browser',
        type: 'fee',
        amount: -25.00,
        timestamp: Date.now() - ******** * 70,
        description: 'International Wire Transfer Fee',
        newBalance: -6796.64,
    },
    {
        transactionId: 'txn_45_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -89.50,
        timestamp: Date.now() - ******** * 72,
        description: 'Grocery Shopping - Rob\'s Liquor',
        newBalance: -6821.64,
    },
    {
        transactionId: 'txn_46_browser',
        accountId: 'acc_1_browser',
        type: 'business_payout',
        amount: 1200,
        timestamp: Date.now() - ******** * 74,
        description: 'Business Investment Return',
        newBalance: -6911.14,
    },
    {
        transactionId: 'txn_47_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -300.00,
        timestamp: Date.now() - ******** * 76,
        description: 'Vehicle Repair - LS Customs',
        newBalance: -8111.14,
    },
    {
        transactionId: 'txn_48_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -500,
        timestamp: Date.now() - ******** * 78,
        description: 'Loan to Marcus Williams',
        counterpartyName: 'Marcus Williams',
        counterpartyAccountId: 'acc_marcus_w',
        newBalance: -7811.14,
    },
    {
        transactionId: 'txn_49_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 3500,
        timestamp: Date.now() - ******** * 80,
        description: 'Bonus Payment - LSPD',
        newBalance: -8311.14,
    },
    {
        transactionId: 'txn_50_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -75.25,
        timestamp: Date.now() - ******** * 82,
        description: 'Restaurant - Vanilla Unicorn',
        newBalance: -11811.14,
    },
    {
        transactionId: 'txn_51_browser',
        accountId: 'acc_1_browser',
        type: 'invoice_payment',
        amount: -280,
        timestamp: Date.now() - ******** * 84,
        description: 'Phone Bill - Lifeinvader',
        newBalance: -11886.39,
    },
    {
        transactionId: 'txn_52_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_in',
        amount: 150,
        timestamp: Date.now() - ******** * 86,
        description: 'Refund - Ammunation',
        counterpartyName: 'Ammunation',
        counterpartyAccountId: 'acc_ammunation2',
        newBalance: -12166.39,
    },
    {
        transactionId: 'txn_53_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -120.00,
        timestamp: Date.now() - ******** * 88,
        description: 'Clothing - Ponsonbys',
        newBalance: -12016.39,
    },
    {
        transactionId: 'txn_54_browser',
        accountId: 'acc_1_browser',
        type: 'salary',
        amount: 2600,
        timestamp: Date.now() - ******** * 90,
        description: 'Bi-weekly Salary - LSPD',
        newBalance: -12136.39,
    },
    {
        transactionId: 'txn_55_browser',
        accountId: 'acc_1_browser',
        type: 'fee',
        amount: -10.00,
        timestamp: Date.now() - ******** * 92,
        description: 'ATM Usage Fee',
        newBalance: -14736.39,
    },
    {
        transactionId: 'txn_56_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -60.00,
        timestamp: Date.now() - ******** * 94,
        description: 'Taxi Fare - Downtown Cab Co.',
        newBalance: -14746.39,
    },
    {
        transactionId: 'txn_57_browser',
        accountId: 'acc_1_browser',
        type: 'transfer_out',
        amount: -200,
        timestamp: Date.now() - ******** * 96,
        description: 'Charity Donation - Los Santos Animal Shelter',
        counterpartyName: 'LS Animal Shelter',
        counterpartyAccountId: 'acc_ls_shelter',
        newBalance: -14806.39,
    },
    {
        transactionId: 'txn_58_browser',
        accountId: 'acc_1_browser',
        type: 'business_payout',
        amount: 800,
        timestamp: Date.now() - ******** * 98,
        description: 'Side Business Revenue',
        newBalance: -15006.39,
    },
    {
        transactionId: 'txn_59_browser',
        accountId: 'acc_1_browser',
        type: 'withdrawal',
        amount: -45.75,
        timestamp: Date.now() - ******** * 100,
        description: 'Coffee Shop - Bean Machine Downtown',
        newBalance: -14206.39,
    },
    {
        transactionId: 'txn_60_browser',
        accountId: 'acc_1_browser',
        type: 'deposit',
        amount: 5000,
        timestamp: Date.now() - ******** * 102,
        description: 'Investment Return - Maze Bank Securities',
        newBalance: -14252.14,
    }
];

type BankingStore = BankingUIState & BankingUIActions & {
    // Add explicit actions for setting data from NUI and initializing mock data
    setData: (data: { accounts?: BankAccount[], transactions?: Transaction[] }) => void;
    initializeMockData: () => void;
    // Add getter for current account transactions
    getCurrentTransactions: () => Transaction[];
};

export const useBankingStore = create<BankingStore>((set, get) => ({
  // Initial State
  isOpen: isBrowserEnv(), // Default to open in browser for easier dev
  isLoading: false, // Global loading for initial account fetch
  isLoadingTransactions: false, // Specific loading for transaction fetching
  accounts: [], // Initialize as empty, mock data loaded by action
  selectedAccountId: null,
  transactionsByAccount: {}, // Cache transactions by account ID
  transactions: [], // Current account's transactions (computed)
  error: null,
  currentView: 'dashboard', // Default view
  depositAmount: '',
  withdrawAmount: '',
  transferTargetAccountNumber: '',
  transferAmount: '',
  transferDescription: '',

  // UI Actions
  setOpen: (isOpen: boolean) => set({ isOpen }),
  clearError: () => set({ error: null }),
  setCurrentView: (view: 'dashboard' | 'transactions') => set({ currentView: view }),
  resetForms: () => set({
    depositAmount: '',
    withdrawAmount: '',
    transferTargetAccountNumber: '',
    transferAmount: '',
    transferDescription: '',
    error: null, // Also clear error on form reset
  }),

  // Form Input Handlers
  setDepositAmount: (amount: string) => set({ depositAmount: amount }),
  setWithdrawAmount: (amount: string) => set({ withdrawAmount: amount }),
  setTransferTargetAccountNumber: (accountNumber: string) => set({ transferTargetAccountNumber: accountNumber }),
  setTransferAmount: (amount: string) => set({ transferAmount: amount }),
  setTransferDescription: (description: string) => set({ transferDescription: description }),

  // Data Fetching and Selection
  fetchAccounts: async () => {
    set({ isLoading: true, error: null });
    if (isBrowserEnv()) {
      console.log("BankingStore: Running in browser, fetching mock accounts.");
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate async
      const mockAccounts = MOCK_ACCOUNTS;
      const defaultAccount = mockAccounts.find(acc => acc.isDefault) || mockAccounts[0];
      
      set({ 
        accounts: mockAccounts, 
        selectedAccountId: defaultAccount?.accountId || null, 
        isLoading: false 
      });
      
      if (defaultAccount) {
        get().fetchTransactions(defaultAccount.accountId);
      }
    } else {
      // In FiveM, this would typically involve an NUI callback to the client script
      console.log("BankingStore: Running in FiveM, NUI call to fetch accounts would go here.");
      // For now, set loading to false and perhaps an empty state or error
      set({ isLoading: false, error: "Account fetching not implemented for FiveM yet." });
    }
  },

  selectAccount: (accountId: string) => {
    const currentState = get();
    
    // Don't re-select the same account
    if (currentState.selectedAccountId === accountId) {
      return;
    }
    
    // Instantly set selected account and update current transactions
    const cachedTransactions = currentState.transactionsByAccount[accountId] || [];
    set({ 
      selectedAccountId: accountId, 
      error: null, 
      currentView: 'dashboard',
      transactions: cachedTransactions 
    });
    
    // Only fetch transactions if we don't have them cached
    if (!currentState.transactionsByAccount[accountId]) {
      get().fetchTransactions(accountId);
    }
  },

  fetchTransactions: async (accountId: string) => {
    const currentState = get();
    
    // Use transaction-specific loading state
    set({ isLoadingTransactions: true, error: null });
    
    if (isBrowserEnv()) {
      console.log(`BankingStore: Running in browser, fetching mock transactions for ${accountId}.`);
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate async
      const accountTransactions = MOCK_TRANSACTIONS.filter(tx => tx.accountId === accountId);
      
      // Update both cache and current transactions
      const newTransactionsByAccount = {
        ...currentState.transactionsByAccount,
        [accountId]: accountTransactions
      };
      
      set({ 
        transactionsByAccount: newTransactionsByAccount,
        transactions: accountId === currentState.selectedAccountId ? accountTransactions : currentState.transactions,
        isLoadingTransactions: false 
      });
    } else {
      // In FiveM, NUI callback to client script
      console.log(`BankingStore: Running in FiveM, NUI call to fetch transactions for ${accountId} would go here.`);
      set({ isLoadingTransactions: false, error: "Transaction fetching not implemented for FiveM yet." });
    }
  },

  // Core banking operations (placeholders, will send NUI messages)
  submitDeposit: async () => {
    set({ isLoading: true, error: null });
    const { depositAmount, selectedAccountId } = get();
    console.log(`Submitting deposit of ${depositAmount} to account ${selectedAccountId}`);
    // NUI message to client script
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate async
    set({ isLoading: false, depositAmount: '' /* Clear form */ });
    // Potentially refresh account data
    if (selectedAccountId) get().fetchTransactions(selectedAccountId); // Refresh transactions
    // And potentially fetchAccounts() or a specific account update
  },

  submitWithdrawal: async () => {
    set({ isLoading: true, error: null });
    const { withdrawAmount, selectedAccountId } = get();
    console.log(`Submitting withdrawal of ${withdrawAmount} from account ${selectedAccountId}`);
    // NUI message to client script
    await new Promise(resolve => setTimeout(resolve, 500));
    set({ isLoading: false, withdrawAmount: '' });
    if (selectedAccountId) get().fetchTransactions(selectedAccountId);
  },

  submitTransfer: async () => {
    set({ isLoading: true, error: null });
    const { transferTargetAccountNumber, transferAmount, transferDescription, selectedAccountId } = get();
    console.log(`Submitting transfer of ${transferAmount} from ${selectedAccountId} to ${transferTargetAccountNumber} (${transferDescription})`);
    // NUI message to client script
    await new Promise(resolve => setTimeout(resolve, 500));
    set({ isLoading: false, transferTargetAccountNumber: '', transferAmount: '', transferDescription: '' });
    if (selectedAccountId) get().fetchTransactions(selectedAccountId);
  },
  
  // Action to set data directly (e.g., from NUI messages)
  setData: (data: { accounts?: BankAccount[], transactions?: Transaction[] }) => {
    const updates: Partial<BankingUIState> = {};
    if (data.accounts) {
      updates.accounts = data.accounts;
      // If accounts are updated, also update selectedAccountId if current one is no longer valid or if none is selected
      const currentSelectedId = get().selectedAccountId;
      const newDefaultAccount = data.accounts.find(acc => acc.isDefault) || data.accounts[0];
      if (!currentSelectedId || !data.accounts.find(acc => acc.accountId === currentSelectedId)) {
        updates.selectedAccountId = newDefaultAccount?.accountId || null;
      }
      // If selected account is updated or set, fetch its transactions
      if (updates.selectedAccountId) {
        get().fetchTransactions(updates.selectedAccountId);
      } else if (newDefaultAccount?.accountId) {
         get().fetchTransactions(newDefaultAccount.accountId);
      }
    }
    if (data.transactions) {
      updates.transactions = data.transactions;
    }
    set(updates);
  },

  // Action to initialize mock data, callable from App.tsx
  initializeMockData: () => {
    if (isBrowserEnv()) {
      console.log("BankingStore: Initializing mock data.");
      const mockAccounts = MOCK_ACCOUNTS;
      const defaultAccount = mockAccounts.find(acc => acc.isDefault) || mockAccounts[0];
      const mockTransactions = defaultAccount ? MOCK_TRANSACTIONS.filter(tx => tx.accountId === defaultAccount.accountId) : [];
      
      set({
        accounts: mockAccounts,
        selectedAccountId: defaultAccount?.accountId || null,
        transactionsByAccount: defaultAccount ? { [defaultAccount.accountId]: mockTransactions } : {},
        transactions: mockTransactions,
        isOpen: true, // Ensure UI is open when mock data is init
      });
    }
  },

  // Helper to get current account transactions
  getCurrentTransactions: () => {
    const state = get();
    return state.selectedAccountId ? (state.transactionsByAccount[state.selectedAccountId] || []) : [];
  },
}));

// Optional: Log store changes in development (browser only)
if (isBrowserEnv()) {
  useBankingStore.subscribe(
    (state, prevState) => {
      console.log('BankingStore changed:', { newState: state, prevState });
    },
    // (state) => state // Optional: selector to only log changes to specific parts of the store
  );
}
