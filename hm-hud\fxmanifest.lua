
fx_version 'cerulean'
game 'gta5'
name 'hm-hud'
description 'HUD for HM Framework'
author 'HM'
version '1.0.0'

node_version '22'

-- Dependencies
dependencies {
    'hm-core'
}
-- Compiled TypeScript files
shared_script 'build/shared.js'
client_script 'build/client.js'
server_script 'build/server.js'

-- Client scripts
client_scripts {
    'scripts/client/client.lua',
}

-- UI
ui_page 'ui/build/index.html'

-- Files to include
files {
    'ui/build/index.html',
    'ui/build/assets/**/*',
    'ui/build/*.svg',
    'ui/build/*.jpg',
    'ui/build/*.png'
}

event 'baseevents:leftVehicle'
event 'baseevents:enteredVehicle'