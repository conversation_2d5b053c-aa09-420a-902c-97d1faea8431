/// <reference types="@citizenfx/client" />
/// <reference types="@citizenfx/server" />

// Base handling parameter interface
export interface HandlingParameter {
  key: string;
  value: number | string;
  min?: number;
  max?: number;
  type: 'float' | 'int' | 'string' | 'vector3';
  category: HandlingCategory;
  description?: string;
}

// Vector3 interface for 3D values
export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

// Handling categories for UI organization
export enum HandlingCategory {
  GENERAL = 'General',
  ENGINE = 'Engine',
  BRAKES = 'Brakes',
  STEERING = 'Steering',
  TRACTION = 'Traction',
  SUSPENSION = 'Suspension',  DAMAGE = 'Damage',
  MISC = 'Miscellaneous',
  FLAGS = 'Flags',
  MODEL_FLAGS = 'Model Flags',
  HANDLING_FLAGS = 'Handling Flags',
  DAMAGE_FLAGS = 'Damage Flags',
  // Specialized vehicle types
  BOAT = 'Boat',
  SEAPLANE = 'Seaplane',
  FLYING = 'Flying',
  BIKE = 'Bike',
  SUBMARINE = 'Submarine',
  TRAILER = 'Trailer',
  CAR = 'Car Advanced',
  WEAPON = 'Vehicle Weapons',
  SPECIAL_FLIGHT = 'Special Flight'
}

// Vehicle handling types
export enum HandlingType {
  HANDLING_TYPE_BIKE = 'HANDLING_TYPE_BIKE',
  HANDLING_TYPE_FLYING = 'HANDLING_TYPE_FLYING',
  HANDLING_TYPE_VERTICAL_FLYING = 'HANDLING_TYPE_VERTICAL_FLYING',
  HANDLING_TYPE_BOAT = 'HANDLING_TYPE_BOAT',
  HANDLING_TYPE_SEAPLANE = 'HANDLING_TYPE_SEAPLANE',
  HANDLING_TYPE_SUBMARINE = 'HANDLING_TYPE_SUBMARINE',
  HANDLING_TYPE_TRAIN = 'HANDLING_TYPE_TRAIN',
  HANDLING_TYPE_TRAILER = 'HANDLING_TYPE_TRAILER',
  HANDLING_TYPE_CAR = 'HANDLING_TYPE_CAR',
  HANDLING_TYPE_WEAPON = 'HANDLING_TYPE_WEAPON',
  HANDLING_TYPE_MAX_TYPES = 'HANDLING_TYPE_MAX_TYPES'
}

// Model Flags
export enum ModelFlags {
  MF_IS_VAN = 0x00000001,
  MF_IS_BUS = 0x00000002,
  MF_IS_LOW = 0x00000004,
  MF_IS_BIG = 0x00000008,
  MF_ABS_STD = 0x00000010,
  MF_ABS_OPTION = 0x00000020,
  MF_ABS_ALT_STD = 0x00000040,
  MF_ABS_ALT_OPTION = 0x00000080,
  MF_NO_DOORS = 0x00000100,
  MF_TANDEM_SEATING = 0x00000200,
  MF_SIT_IN_BOAT = 0x00000400,
  MF_HAS_TRACKS = 0x00000800,
  MF_NO_EXHAUST = 0x00001000,
  MF_DOUBLE_EXHAUST = 0x00002000,
  MF_NO_1STPERSON_LOOKBEHIND = 0x00004000,
  MF_CAN_ENTER_IF_NO_DOOR = 0x00008000,
  MF_AXLE_F_TORSION = 0x00010000,
  MF_AXLE_F_SOLID = 0x00020000,
  MF_AXLE_F_MCPHERSON = 0x00040000,
  MF_ATTACH_PED_TO_BODYSHELL = 0x00080000,
  MF_AXLE_R_TORSION = 0x00100000,
  MF_AXLE_R_SOLID = 0x00200000,
  MF_AXLE_R_MCPHERSON = 0x00400000,
  MF_DONT_FORCE_GRND_CLEARANCE = 0x00800000,
  MF_DONT_RENDER_STEER = 0x01000000,
  MF_NO_WHEEL_BURST = 0x02000000,
  MF_INDESTRUCTIBLE = 0x04000000,
  MF_DOUBLE_FRONT_WHEELS = 0x08000000,
  MF_IS_RC = 0x10000000,
  MF_DOUBLE_REAR_WHEELS = 0x20000000,
  MF_NO_WHEEL_BREAK = 0x40000000,
  MF_EXTRA_CAMBER = 0x80000000
}

// Handling Flags
export enum HandlingFlags {
  HF_SMOOTHED_COMPRESSION = 0x00000001,
  HF_REDUCED_MOD_MASS = 0x00000002,
  HF_HAS_KERS = 0x00000004,
  HF_HAS_RALLY_TYRES = 0x00000008,
  HF_NO_HANDBRAKE = 0x00000010,
  HF_STEER_REARWHEELS = 0x00000020,
  HF_HANDBRAKE_REARWHEELSTEER = 0x00000040,
  HF_STEER_ALL_WHEELS = 0x00000080,
  HF_FREEWHEEL_NO_GAS = 0x00000100,
  HF_NO_REVERSE = 0x00000200,
  HF_REDUCED_RIGHTING_FORCE = 0x00000400,
  HF_STEER_NO_WHEELS = 0x00000800,
  HF_CVT = 0x00001000,
  HF_ALT_EXT_WHEEL_BOUNDS_BEH = 0x00002000,
  HF_DONT_RAISE_BOUNDS_AT_SPEED = 0x00004000,
  HF_EXT_WHEEL_BOUNDS_COL = 0x00008000,
  HF_LESS_SNOW_SINK = 0x00010000,
  HF_TYRES_CAN_CLIP = 0x00020000,
  HF_REDUCED_DRIVE_OVER_DAMAGE = 0x00040000,
  HF_ALT_EXT_WHEEL_BOUNDS_SHRINK = 0x00080000,
  HF_OFFROAD_ABILITIES = 0x00100000,
  HF_OFFROAD_ABILITIES_X2 = 0x00200000,
  HF_TYRES_RAISE_SIDE_IMPACT_THRESHOLD = 0x00400000,
  HF_OFFROAD_INCREASED_GRAVITY_NO_FOLIAGE_DRAG = 0x00800000,
  HF_ENABLE_LEAN = 0x01000000,
  HF_FORCE_NO_TC_OR_SC = 0x02000000,
  HF_HEAVYARMOUR = 0x04000000,
  HF_ARMOURED = 0x08000000,
  HF_SELF_RIGHTING_IN_WATER = 0x10000000,
  HF_IMPROVED_RIGHTING_FORCE = 0x20000000,
  HF_LOW_SPEED_WHEELIES = 0x40000000,
  HF_LAST_AVAILABLE_FLAG = 0x80000000
}

// Damage Flags
export enum DamageFlags {
  DF_DRIVER_SIDE_FRONT_DOOR = 0x00000001,
  DF_DRIVER_SIDE_REAR_DOOR = 0x00000002,
  DF_DRIVER_PASSENGER_SIDE_FRONT_DOOR = 0x00000004,
  DF_DRIVER_PASSENGER_SIDE_REAR_DOOR = 0x00000008,
  DF_BONNET = 0x00000010,
  DF_BOOT = 0x00000020
}

// AI Handling Types
export enum AIHandlingType {
  SPORTS_CAR = 'SPORTS_CAR',
  AVERAGE = 'AVERAGE',
  CRAP = 'CRAP',
  TRUCK = 'TRUCK'
}

// Base handling data interface
export interface BaseHandlingData {
  // Handling name identifier
  handlingName: string;
  
  // General Properties
  fMass: number;
  fInitialDragCoeff: number;
  fPercentSubmerged: number;
  vecCentreOfMassOffset: Vector3;
  vecInertiaMultiplier: Vector3;
  
  // Engine Properties
  fDriveBiasFront: number;
  nInitialDriveGears: number;
  fInitialDriveForce: number;
  fDriveInertia: number;
  fClutchChangeRateScaleUpShift: number;
  fClutchChangeRateScaleDownShift: number;
  fInitialDriveMaxFlatVel: number;
  
  // Brake Properties
  fBrakeForce: number;
  fBrakeBiasFront: number;
  fHandBrakeForce: number;
  
  // Steering Properties
  fSteeringLock: number;
  
  // Traction Properties
  fTractionCurveMax: number;
  fTractionCurveMin: number;
  fTractionCurveLateral: number;
  fTractionSpringDeltaMax: number;  fLowSpeedTractionLossMult: number;
  fCamberStiffnesss: number;
  fTractionBiasFront: number;
  fTractionLossMult: number;
  
  // Suspension Properties
  fSuspensionForce: number;
  fSuspensionCompDamp: number;
  fSuspensionReboundDamp: number;
  fSuspensionUpperLimit: number;
  fSuspensionLowerLimit: number;
  fSuspensionRaise: number;
  fSuspensionBiasFront: number;
  fAntiRollBarForce: number;
  fAntiRollBarBiasFront: number;
  fRollCentreHeightFront: number;
  fRollCentreHeightRear: number;
  
  // Damage Properties
  fCollisionDamageMult: number;
  fWeaponDamageMult: number;
  fDeformationDamageMult: number;
  fEngineDamageMult: number;
  fPetrolTankVolume: number;
  fOilVolume: number;
  fPetrolConsumptionRate: number;
  
  // Misc Properties
  fSeatOffsetDistX: number;
  fSeatOffsetDistY: number;
  fSeatOffsetDistZ: number;
  nMonetaryValue: number;
  
  // Flags
  strModelFlags: string;
  strHandlingFlags: string;
  strDamageFlags: string;
  
  // AI and Advanced
  AIHandling: string;
  fWeaponDamageScaledToVehHealthMult: number;
  fDownforceModifier: number;
  fPopUpLightRotation: number;
  fRocketBoostCapacity: number;
  fBoostMaxSpeed: number;
}

// Base interface for sub-handling data
export interface BaseSubHandlingData {
  // Base interface for all sub-handling data types
}

// Specialized handling data interfaces
export interface CBoatHandlingData extends BaseSubHandlingData {
  fBoxFrontMult: number;
  fBoxRearMult: number;
  fBoxSideMult: number;
  fSampleTop: number;
  fSampleBottom: number;
  fSampleBottomTestCorrection: number;
  fAquaplaneForce: number;
  fAquaplanePushWaterMult: number;
  fAquaplanePushWaterCap: number;
  fAquaplanePushWaterApply: number;
  fRudderForce: number;
  fRudderOffsetSubmerge: number;
  fRudderOffsetForce: number;
  fRudderOffsetForceZMult: number;
  fWaveAudioMult: number;
  vecMoveResistance: Vector3;
  vecTurnResistance: Vector3;
  fLook_L_R_CamHeight: number;
  fDragCoefficient: number;
  fKeelSphereSize: number;
  fPropRadius: number;
  fLowLodAngOffset: number;
  fLowLodDraughtOffset: number;
  fImpellerOffset: number;
  fImpellerForceMult: number;
  fDinghySphereBuoyConst: number;
  fProwRaiseMult: number;
  fDeepWaterSampleBuoyancyMult: number;
  fTransmissionMultiplier: number;
  fTractionMultiplier: number;
}

export interface CSeaplaneHandlingData extends BaseSubHandlingData {
  fLeftPontoonComponentId: number;
  fRightPontoonComponentId: number;
  fPontoonBuoyConst: number;
  fPontoonSampleSizeFront: number;
  fPontoonSampleSizeMiddle: number;
  fPontoonSampleSizeRear: number;
  fPontoonLengthFractionForSamples: number;
  fPontoonDragCoefficient: number;
  fPontoonVerticalDampingCoefficientUp: number;
  fPontoonVerticalDampingCoefficientDown: number;
  fKeelSphereSize: number;
}

export interface CBikeHandlingData extends BaseSubHandlingData {
  fLeanFwdCOMMult: number;
  fLeanFwdForceMult: number;
  fLeanBakCOMMult: number;
  fLeanBakForceMult: number;
  fMaxBankAngle: number;
  fFullAnimAngle: number;
  fDesLeanReturnFrac: number;
  fStickLeanMult: number;
  fBrakingStabilityMult: number;
  fInAirSteerMult: number;
  fWheelieBalancePoint: number;
  fStoppieBalancePoint: number;
  fWheelieSteerMult: number;
  fRearBalanceMult: number;
  fFrontBalanceMult: number;
  fBikeGroundSideFrictionMult: number;
  fBikeWheelGroundSideFrictionMult: number;
  fBikeOnStandLeanAngle: number;
  fBikeOnStandSteerAngle: number;
  fJumpForce: number;
}

export interface CVehicleWeaponHandlingDataTurretPitchLimits {
  fForwardAngleMin: number;
  fForwardAngleMax: number;
  fBackwardAngleMin: number;
  fBackwardAngleMid: number;
  fBackwardAngleMax: number;
  fBackwardForcedPitch: number;
}

export interface CVehicleWeaponHandlingData extends BaseSubHandlingData {
  uWeaponHash: string[];
  WeaponSeats: number[];
  WeaponVehicleModType: string[];
  fTurretSpeed: number[];
  fTurretPitchMin: number[];
  fTurretPitchMax: number[];
  fTurretCamPitchMin: number[];
  fTurretCamPitchMax: number[];
  fBulletVelocityForGravity: number[];
  fTurretPitchForwardMin: number[];
  TurretPitchLimitData: CVehicleWeaponHandlingDataTurretPitchLimits[];
  fUvAnimationMult: number;
  fMiscGadgetVar: number;
  fWheelImpactOffset: number;
}

export interface CSubmarineHandlingData extends BaseSubHandlingData {
  fPitchMult: number;
  fPitchAngle: number;
  fYawMult: number;
  fDiveSpeed: number;
  fRollMult: number;
  fRollStab: number;
  vTurnRes: Vector3;
  fMoveResXY: number;
  fMoveResZ: number;
}

export interface CTrailerHandlingData extends BaseSubHandlingData {
  fAttachLimitPitch: number;
  fAttachLimitRoll: number;
  fAttachLimitYaw: number;
  fUprightSpringConstant: number;
  fUprightDampingConstant: number;
  fAttachedMaxDistance: number;
  fAttachedMaxPenetration: number;
  fAttachRaiseZ: number;
  fPosConstraintMassRatio: number;
}

export interface CFlyingHandlingData extends BaseSubHandlingData {
  fThrust: number;
  fInitialThrust: number;
  fThrustFallOff: number;
  fInitialThrustFallOff: number;
  fThrustVectoring: number;
  fYawMult: number;
  fInitialYawMult: number;
  fYawStabilise: number;
  fSideSlipMult: number;
  fRollMult: number;
  fInitialRollMult: number;
  fRollStabilise: number;
  fPitchMult: number;
  fInitialPitchMult: number;
  fPitchStabilise: number;
  fFormLiftMult: number;
  fAttackLiftMult: number;
  fAttackDiveMult: number;
  fGearDownDragV: number;
  fGearDownLiftMult: number;
  fWindMult: number;
  fMoveRes: number;
  vecTurnRes: Vector3;
  vecSpeedRes: Vector3;
  fGearDoorFrontOpen: number;
  fGearDoorRearOpen: number;
  fGearDoorRearOpen2: number;
  fGearDoorRearMOpen: number;
  fTurublenceMagnitudeMax: number;
  fTurublenceForceMulti: number;
  fTurublenceRollTorqueMulti: number;
  fTurublencePitchTorqueMulti: number;
  fBodyDamageControlEffectMult: number;
  fInputSensitivityForDifficulty: number;
  fOnGroundYawBoostSpeedPeak: number;
  fOnGroundYawBoostSpeedCap: number;
  fEngineOffGlideMulti: number;
  fAfterburnerEffectRadius: number;
  fAfterburnerEffectDistance: number;
  fAfterburnerEffectForceMulti: number;
  fSubmergeLevelToPullHeliUnderwater: number;
  fExtraLiftWithRoll: number;
  handlingType: HandlingType;
}

export interface CAdvancedData {
  Slot: number;
  Index: number;
  Value: number;
}

export interface CCarHandlingData extends BaseSubHandlingData {
  fBackEndPopUpCarImpulseMult: number;
  fBackEndPopUpBuildingImpulseMult: number;
  fBackEndPopUpMaxDeltaSpeed: number;
  fToeFront: number;
  fToeRear: number;
  fCamberFront: number;
  fCamberRear: number;
  fCastor: number;
  fEngineResistance: number;
  fMaxDriveBiasTransfer: number;
  fJumpForceScale: number;
  fIncreasedRammingForceScale: number;
  strAdvancedFlags: string;
  AdvancedData: CAdvancedData[];
}

export interface CSpecialFlightHandlingData extends BaseSubHandlingData {
  vecAngularDamping: Vector3;
  vecAngularDampingMin: Vector3;
  vecLinearDamping: Vector3;
  vecLinearDampingMin: Vector3;
  fLiftCoefficient: number;
  fCriticalLiftAngle: number;
  fInitialLiftAngle: number;
  fMaxLiftAngle: number;
  fDragCoefficient: number;
  fBrakingDrag: number;
  fMaxLiftVelocity: number;
  fMinLiftVelocity: number;
  fRollTorqueScale: number;
  fMaxTorqueVelocity: number;
  fMinTorqueVelocity: number;
  fYawTorqueScale: number;
  fSelfLevelingPitchTorqueScale: number;
  fSelfLevelingRollTorqueScale: number;
  fMaxPitchTorque: number;
  fMaxSteeringRollTorque: number;
  fPitchTorqueScale: number;
  fSteeringTorqueScale: number;
  fMaxThrust: number;
  fTransitionDuration: number;
  fHoverVelocityScale: number;
  fStabilityAssist: number;
  fMinSpeedForThrustFalloff: number;
  fBrakingThrustScale: number;
  mode: number;
  strFlags: string;
}

// Vehicle Information Interface
export interface VehicleInfo {
  model: string;
  displayName: string;
  hash: number;
  vehicleClass: number;
  className: string;
  isInVehicle: boolean;
  isDriverSeat: boolean;
  vehicleHandle: number;
}

// Profile Management Interfaces
export interface HandlingProfile {
  id: string;
  name: string;
  description?: string;
  vehicleModel?: string; // If vehicle-specific
  handlingData: Partial<BaseHandlingData>;
  createdBy: string;
  createdAt: Date;
  isPublic: boolean;
  category?: 'drift' | 'racing' | 'offroad' | 'comfort' | 'sport' | 'custom';
  tags?: string[];
}

export interface ProfileSearchFilters {
  category?: string;
  vehicleModel?: string;
  createdBy?: string;
  tags?: string[];
  searchTerm?: string;
}

// Application State Interfaces
export interface HandlingChanges {
  [key: string]: {
    oldValue: number | string | Vector3;
    newValue: number | string | Vector3;
    timestamp: Date;
  };
}

export interface HandlingBackup {
  vehicleHash: number;
  originalHandling: BaseHandlingData;
  timestamp: Date;
}

// Network Event Interfaces for FiveM Communication
export interface HandlingNetworkEvents {
  // Client to Server
  'hm-handling:requestVehicleInfo': () => void;
  'hm-handling:requestStockHandling': (vehicleHash: number) => void;
  'hm-handling:applyHandling': (vehicleHandle: number, handlingData: Partial<BaseHandlingData>) => void;
  'hm-handling:saveProfile': (profile: Omit<HandlingProfile, 'id' | 'createdAt'>) => void;
  'hm-handling:loadProfile': (profileId: string) => void;
  'hm-handling:deleteProfile': (profileId: string) => void;
  'hm-handling:searchProfiles': (filters: ProfileSearchFilters) => void;
  
  // Server to Client
  'hm-handling:vehicleInfoResponse': (vehicleInfo: VehicleInfo | null) => void;
  'hm-handling:stockHandlingResponse': (handlingData: BaseHandlingData) => void;
  'hm-handling:applyHandlingResponse': (success: boolean, error?: string) => void;
  'hm-handling:profileSaved': (profile: HandlingProfile) => void;
  'hm-handling:profileLoaded': (handlingData: Partial<BaseHandlingData>) => void;
  'hm-handling:profileDeleted': (success: boolean) => void;
  'hm-handling:profileSearchResults': (profiles: HandlingProfile[]) => void;
  'hm-handling:error': (message: string) => void;
}

// UI State Interface
export interface HandlingUIState {
  isMenuOpen: boolean;
  currentVehicle: VehicleInfo | null;
  currentHandling: Partial<BaseHandlingData>;
  stockHandling: Partial<BaseHandlingData>;
  originalHandling: Partial<BaseHandlingData>; // Backup for reset
  unsavedChanges: HandlingChanges;
  selectedProfile: HandlingProfile | null;
  availableProfiles: HandlingProfile[];
  isLoading: boolean;
  error: string | null;
}

// Permission levels
export enum HandlingPermission {
  NONE = 0,
  USER = 1,
  MODERATOR = 2,
  ADMIN = 3
}

// Configuration interface
export interface HandlingConfig {
  enabledVehicleTypes: HandlingType[];
  maxProfilesPerPlayer: number;
  allowPublicProfiles: boolean;
  adminOnly: boolean;
  maxValueMultipliers: {
    [key: string]: number;
  };
  restrictedParameters: string[];
  logHandlingChanges: boolean;
}

// Field limit definitions for handling parameters
export interface HandlingFieldLimits {
  min: number;
  max: number;
  step?: number;
  default?: number;
}

// Comprehensive handling field limits based on FiveM handling.meta schema
export const HANDLING_FIELD_LIMITS: Record<keyof BaseHandlingData, HandlingFieldLimits> = {
  // Handling name identifier
  handlingName: { min: 1, max: 255, step: 1 },
  
  // General Properties
  fMass: { min: 0.0, max: 100000.0, step: 1.0 },
  fInitialDragCoeff: { min: 0.0, max: 1000.0, step: 0.1 },
  fPercentSubmerged: { min: 0.0, max: 100.0, step: 0.1 },
  vecCentreOfMassOffset: { min: -1000.0, max: 1000.0, step: 0.01 }, // Vector3 - using reasonable limits
  vecInertiaMultiplier: { min: 0.0, max: 10.0, step: 0.01 }, // Vector3 - using reasonable limits
  
  // Engine Properties
  fDriveBiasFront: { min: 0.0, max: 1000.0, step: 0.01 },
  nInitialDriveGears: { min: 0, max: 1000, step: 1 },
  fInitialDriveForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fDriveInertia: { min: 0.0, max: 1000.0, step: 0.01 },
  fClutchChangeRateScaleUpShift: { min: 0.0, max: 1000.0, step: 0.01 },
  fClutchChangeRateScaleDownShift: { min: 0.0, max: 1000.0, step: 0.01 },
  fInitialDriveMaxFlatVel: { min: 0.0, max: 1000.0, step: 0.1 },
  
  // Brake Properties
  fBrakeForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fBrakeBiasFront: { min: 0.0, max: 1000.0, step: 0.01 },
  fHandBrakeForce: { min: 0.0, max: 1000.0, step: 0.01 },
  
  // Steering Properties
  fSteeringLock: { min: 0.0, max: 1000.0, step: 0.1 },
  
  // Traction Properties
  fTractionCurveMax: { min: 0.0, max: 1000.0, step: 0.01 },
  fTractionCurveMin: { min: 0.0, max: 1000.0, step: 0.01 },
  fTractionCurveLateral: { min: 0.0, max: 1000.0, step: 0.01 },
  fTractionSpringDeltaMax: { min: 0.0, max: 1000.0, step: 0.01 },  fLowSpeedTractionLossMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fCamberStiffnesss: { min: 0.0, max: 1000.0, step: 0.01 },
  fTractionBiasFront: { min: 0.0, max: 1000.0, step: 0.01 },
  fTractionLossMult: { min: 0.0, max: 1000.0, step: 0.01 },
  
  // Suspension Properties
  fSuspensionForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionCompDamp: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionReboundDamp: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionUpperLimit: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionLowerLimit: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionRaise: { min: 0.0, max: 1000.0, step: 0.01 },
  fSuspensionBiasFront: { min: 0.0, max: 1000.0, step: 0.01 },
  fAntiRollBarForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fAntiRollBarBiasFront: { min: 0.0, max: 1000.0, step: 0.01 },
  fRollCentreHeightFront: { min: 0.0, max: 1000.0, step: 0.01 },
  fRollCentreHeightRear: { min: 0.0, max: 1000.0, step: 0.01 },
  
  // Damage Properties
  fCollisionDamageMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fWeaponDamageMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fDeformationDamageMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fEngineDamageMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fPetrolTankVolume: { min: 0.0, max: 1000.0, step: 0.1 },
  fOilVolume: { min: 0.0, max: 1000.0, step: 0.1 },
  fPetrolConsumptionRate: { min: 0.0, max: 1000.0, step: 0.01, default: 0.5 },
  
  // Misc Properties
  fSeatOffsetDistX: { min: 0.0, max: 1000.0, step: 0.01 },
  fSeatOffsetDistY: { min: 0.0, max: 1000.0, step: 0.01 },
  fSeatOffsetDistZ: { min: 0.0, max: 1000.0, step: 0.01 },  nMonetaryValue: { min: 0, max: 1000000, step: 1 },
  
  // Model Flags
  strModelFlags: { min: 0, max: 0 },
  
  // Handling Flags  
  strHandlingFlags: { min: 0, max: 0 },
  
  // Damage Flags
  strDamageFlags: { min: 0, max: 0 },
  
  // AI and Advanced
  AIHandling: { min: 0, max: 0 }, // String field
  fWeaponDamageScaledToVehHealthMult: { min: 0.0, max: 10.0, step: 0.01, default: 0.5 },
  fDownforceModifier: { min: 0.0, max: 100.0, step: 0.1 },
  fPopUpLightRotation: { min: -90.0, max: 90.0, step: 0.1, default: 0.0 },
  fRocketBoostCapacity: { min: 0.0, max: 1000.0, step: 0.01, default: 1.25 },
  fBoostMaxSpeed: { min: 0.0, max: 150.0, step: 0.1, default: 70.0 }
};

// Specialized handling field limits
export const BOAT_HANDLING_LIMITS: Record<keyof CBoatHandlingData, HandlingFieldLimits> = {
  fBoxFrontMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fBoxRearMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fBoxSideMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fSampleTop: { min: 0.0, max: 1000.0, step: 0.01 },
  fSampleBottom: { min: 0.0, max: 1000.0, step: 0.01 },
  fSampleBottomTestCorrection: { min: 0.0, max: 1.0, step: 0.01, default: 0.0 },
  fAquaplaneForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fAquaplanePushWaterMult: { min: 0.0, max: 1000.0, step: 0.01 },
  fAquaplanePushWaterCap: { min: 0.0, max: 1000.0, step: 0.01 },
  fAquaplanePushWaterApply: { min: 0.0, max: 1000.0, step: 0.01 },
  fRudderForce: { min: 0.0, max: 1000.0, step: 0.01 },
  fRudderOffsetSubmerge: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRudderOffsetForce: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRudderOffsetForceZMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fWaveAudioMult: { min: 0.0, max: 1000.0, step: 0.01 },
  vecMoveResistance: { min: -1000.0, max: 1000.0, step: 0.01 },
  vecTurnResistance: { min: -1000.0, max: 1000.0, step: 0.01 },
  fLook_L_R_CamHeight: { min: 0.0, max: 1000.0, step: 0.01 },
  fDragCoefficient: { min: 0.0, max: 1000.0, step: 0.01 },
  fKeelSphereSize: { min: 0.0, max: 1000.0, step: 0.01 },
  fPropRadius: { min: 0.0, max: 1000.0, step: 0.01 },
  fLowLodAngOffset: { min: -1000.0, max: 1000.0, step: 0.01 },
  fLowLodDraughtOffset: { min: -1000.0, max: 1000.0, step: 0.01 },
  fImpellerOffset: { min: -1000.0, max: 1000.0, step: 0.01 },
  fImpellerForceMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fDinghySphereBuoyConst: { min: -1000.0, max: 1000.0, step: 0.01 },
  fProwRaiseMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fDeepWaterSampleBuoyancyMult: { min: 0.0, max: 1000.0, step: 0.01, default: 1.0 },
  fTransmissionMultiplier: { min: 0.0, max: 100.0, step: 0.01, default: 1.0 },
  fTractionMultiplier: { min: 0.0, max: 100.0, step: 0.01, default: 1.0 }
};

export const SEAPLANE_HANDLING_LIMITS: Record<keyof CSeaplaneHandlingData, HandlingFieldLimits> = {
  fLeftPontoonComponentId: { min: 0, max: 65535, step: 1 },
  fRightPontoonComponentId: { min: 0, max: 65535, step: 1 },
  fPontoonBuoyConst: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPontoonSampleSizeFront: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPontoonSampleSizeMiddle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPontoonSampleSizeRear: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPontoonLengthFractionForSamples: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPontoonDragCoefficient: { min: 0.0, max: 1000.0, step: 0.01 },
  fPontoonVerticalDampingCoefficientUp: { min: 0.0, max: 1000.0, step: 0.01 },
  fPontoonVerticalDampingCoefficientDown: { min: 0.0, max: 1000.0, step: 0.01 },
  fKeelSphereSize: { min: 0.0, max: 1000.0, step: 0.01 },
};

export const VEHICLE_WEAPON_HANDLING_LIMITS: Record<keyof CVehicleWeaponHandlingData, HandlingFieldLimits> = {
  uWeaponHash: { min: 0, max: 0 },
  WeaponSeats: { min: 0, max: 0 },
  WeaponVehicleModType: { min: 0, max: 0 },
  fTurretSpeed: { min: 0, max: 0 },
  fTurretPitchMin: { min: 0, max: 0 },
  fTurretPitchMax: { min: 0, max: 0 },
  fTurretCamPitchMin: { min: 0, max: 0 },
  fTurretCamPitchMax: { min: 0, max: 0 },
  fBulletVelocityForGravity: { min: 0, max: 0 },
  fTurretPitchForwardMin: { min: 0, max: 0 },
  TurretPitchLimitData: { min: 0, max: 0 },
  fUvAnimationMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMiscGadgetVar: { min: -1000.0, max: 1000.0, step: 0.01 },
  fWheelImpactOffset: { min: -1000.0, max: 1000.0, step: 0.01 },
};

export const SUBMARINE_HANDLING_LIMITS: Record<keyof CSubmarineHandlingData, HandlingFieldLimits> = {
  fPitchMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPitchAngle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fYawMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fDiveSpeed: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRollMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRollStab: { min: -1000.0, max: 1000.0, step: 0.01 },
  vTurnRes: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMoveResXY: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMoveResZ: { min: -1000.0, max: 1000.0, step: 0.01 },
};

export const SPECIAL_FLIGHT_HANDLING_LIMITS: Record<keyof CSpecialFlightHandlingData, HandlingFieldLimits> = {
    vecAngularDamping: { min: -1000.0, max: 1000.0, step: 0.01 },
    vecAngularDampingMin: { min: -1000.0, max: 1000.0, step: 0.01 },
    vecLinearDamping: { min: -1000.0, max: 1000.0, step: 0.01 },
    vecLinearDampingMin: { min: -1000.0, max: 1000.0, step: 0.01 },
    fLiftCoefficient: { min: -1000.0, max: 1000.0, step: 0.01, default: 150.0 },
    fCriticalLiftAngle: { min: -1000.0, max: 1000.0, step: 0.01, default: 45.0 },
    fInitialLiftAngle: { min: -1000.0, max: 1000.0, step: 0.01, default: 1.5 },
    fMaxLiftAngle: { min: -1000.0, max: 1000.0, step: 0.01, default: 25.0 },
    fDragCoefficient: { min: -1000.0, max: 1000.0, step: 0.01, default: 0.4 },
    fBrakingDrag: { min: -1000.0, max: 1000.0, step: 0.01, default: 10.0 },
    fMaxLiftVelocity: { min: -10000.0, max: 10000.0, step: 0.01, default: 2000.0 },
    fMinLiftVelocity: { min: -10000.0, max: 10000.0, step: 0.01, default: 1.0 },
    fRollTorqueScale: { min: -1000.0, max: 1000.0, step: 0.01, default: 3.0 },
    fMaxTorqueVelocity: { min: -1000.0, max: 1000.0, step: 0.01, default: 100.0 },
    fMinTorqueVelocity: { min: -100000.0, max: 100000.0, step: 0.01, default: 40000.0 },
    fYawTorqueScale: { min: -10000.0, max: 10000.0, step: 0.01, default: -900.0 },
    fSelfLevelingPitchTorqueScale: { min: -1000.0, max: 1000.0, step: 0.01, default: -5.0 },
    fSelfLevelingRollTorqueScale: { min: -1000.0, max: 1000.0, step: 0.01, default: -5.0 },
    fMaxPitchTorque: { min: -10000.0, max: 10000.0, step: 0.01, default: 1500.0 },
    fMaxSteeringRollTorque: { min: -1000.0, max: 1000.0, step: 0.01, default: 250.0 },
    fPitchTorqueScale: { min: -1000.0, max: 1000.0, step: 0.01, default: 400.0 },
    fSteeringTorqueScale: { min: -10000.0, max: 10000.0, step: 0.01, default: 1000.0 },
    fMaxThrust: { min: -1000.0, max: 1000.0, step: 0.01, default: 0.0 },
    fTransitionDuration: { min: 0.0, max: 1000.0, step: 0.01, default: 0.0 },
    fHoverVelocityScale: { min: 0.0, max: 1000.0, step: 0.01, default: 1.0 },
    fStabilityAssist: { min: 0.0, max: 1000.0, step: 0.01, default: 10.0 },
    fMinSpeedForThrustFalloff: { min: 0.0, max: 1.0, step: 0.01, default: 0.0 },
    fBrakingThrustScale: { min: 0.0, max: 1.0, step: 0.01, default: 0.0 },
    mode: { min: 0, max: 10, step: 1, default: 0 },
    strFlags: { min: 0, max: 0 },
};

export const BIKE_HANDLING_LIMITS: Record<keyof CBikeHandlingData, HandlingFieldLimits> = {
  fLeanFwdCOMMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fLeanFwdForceMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fLeanBakCOMMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fLeanBakForceMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMaxBankAngle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fFullAnimAngle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fDesLeanReturnFrac: { min: -1000.0, max: 1000.0, step: 0.01 },
  fStickLeanMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBrakingStabilityMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInAirSteerMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fWheelieBalancePoint: { min: -1000.0, max: 1000.0, step: 0.01 },
  fStoppieBalancePoint: { min: -1000.0, max: 1000.0, step: 0.01 },
  fWheelieSteerMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRearBalanceMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fFrontBalanceMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBikeGroundSideFrictionMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBikeWheelGroundSideFrictionMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBikeOnStandLeanAngle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBikeOnStandSteerAngle: { min: -1000.0, max: 1000.0, step: 0.01 },
  fJumpForce: { min: -1000.0, max: 1000.0, step: 0.01 },
};

export const TRAILER_HANDLING_LIMITS: Record<keyof CTrailerHandlingData, HandlingFieldLimits> = {
  fAttachLimitPitch: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttachLimitRoll: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttachLimitYaw: { min: -1000.0, max: 1000.0, step: 0.01 },
  fUprightSpringConstant: { min: -1000.0, max: 1000.0, step: 0.01 },
  fUprightDampingConstant: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttachedMaxDistance: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttachedMaxPenetration: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttachRaiseZ: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPosConstraintMassRatio: { min: -1000.0, max: 1000.0, step: 0.01 },
};

export const FLYING_HANDLING_LIMITS: Record<keyof CFlyingHandlingData, HandlingFieldLimits> = {  fThrust: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInitialThrust: { min: 0.0, max: 0.0, step: 0.01 },
  fThrustFallOff: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInitialThrustFallOff: { min: 0.0, max: 0.0, step: 0.01 },
  fThrustVectoring: { min: -1000.0, max: 1000.0, step: 0.01 },
  fYawMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInitialYawMult: { min: 0.0, max: 0.0, step: 0.01 },
  fYawStabilise: { min: -1000.0, max: 1000.0, step: 0.01 },
  fSideSlipMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fRollMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInitialRollMult: { min: 0.0, max: 0.0, step: 0.01 },
  fRollStabilise: { min: -1000.0, max: 1000.0, step: 0.01 },
  fPitchMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInitialPitchMult: { min: 0.0, max: 0.0, step: 0.01 },
  fPitchStabilise: { min: -1000.0, max: 1000.0, step: 0.01 },
  fFormLiftMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttackLiftMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAttackDiveMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDownDragV: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDownLiftMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fWindMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMoveRes: { min: -1000.0, max: 1000.0, step: 0.01 },
  vecTurnRes: { min: -1000.0, max: 1000.0, step: 0.01 },
  vecSpeedRes: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDoorFrontOpen: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDoorRearOpen: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDoorRearOpen2: { min: -1000.0, max: 1000.0, step: 0.01 },
  fGearDoorRearMOpen: { min: -1000.0, max: 1000.0, step: 0.01 },
  fTurublenceMagnitudeMax: { min: -1000.0, max: 1000.0, step: 0.01 },
  fTurublenceForceMulti: { min: -1000.0, max: 1000.0, step: 0.01 },
  fTurublenceRollTorqueMulti: { min: -1000.0, max: 1000.0, step: 0.01 },
  fTurublencePitchTorqueMulti: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBodyDamageControlEffectMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fInputSensitivityForDifficulty: { min: -1000.0, max: 1000.0, step: 0.01 },
  fOnGroundYawBoostSpeedPeak: { min: -1000.0, max: 1000.0, step: 0.01 },
  fOnGroundYawBoostSpeedCap: { min: -1000.0, max: 1000.0, step: 0.01 },  fEngineOffGlideMulti: { min: -1000.0, max: 1000.0, step: 0.01 },
  fAfterburnerEffectRadius: { min: 0.0, max: 10.0, step: 0.01 },
  fAfterburnerEffectDistance: { min: 0.0, max: 10.0, step: 0.01 },  fAfterburnerEffectForceMulti: { min: 0.0, max: 10.0, step: 0.01 },
  fSubmergeLevelToPullHeliUnderwater: { min: 0.0, max: 1.0, step: 0.01 },
  fExtraLiftWithRoll: { min: 0.0, max: 100.0, step: 0.01 },
  handlingType: { min: 0, max: 0 },
};

export const CAR_HANDLING_LIMITS: Record<keyof CCarHandlingData, HandlingFieldLimits> = {
  fBackEndPopUpCarImpulseMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBackEndPopUpBuildingImpulseMult: { min: -1000.0, max: 1000.0, step: 0.01 },
  fBackEndPopUpMaxDeltaSpeed: { min: -1000.0, max: 1000.0, step: 0.01 },
  fToeFront: { min: -1000.0, max: 1000.0, step: 0.01 },
  fToeRear: { min: -1000.0, max: 1000.0, step: 0.01 },
  fCamberFront: { min: -1000.0, max: 1000.0, step: 0.01 },
  fCamberRear: { min: -1000.0, max: 1000.0, step: 0.01 },
  fCastor: { min: -1000.0, max: 1000.0, step: 0.01 },
  fEngineResistance: { min: -1000.0, max: 1000.0, step: 0.01 },
  fMaxDriveBiasTransfer: { min: -1000.0, max: 1000.0, step: 0.01 },
  fJumpForceScale: { min: -1000.0, max: 1000.0, step: 0.01 },
  fIncreasedRammingForceScale: { min: -1000.0, max: 1000.0, step: 0.01 },
  strAdvancedFlags: { min: 0, max: 0 },
  AdvancedData: { min: 0, max: 0 },
};

// Enhanced Field State Management
export enum FieldState {
  UNCHANGED = 'unchanged',           // Same as original value
  MODIFIED = 'modified',            // Changed but not applied to vehicle
  APPLIED = 'applied',              // Applied to vehicle and matches current value
  APPLIED_MODIFIED = 'applied_modified', // Applied but then modified again
  OUT_OF_BOUNDS = 'out_of_bounds',  // Value exceeds safe limits
  INVALID = 'invalid',              // Invalid value type or format
  PENDING = 'pending'               // Change pending application
}

export interface FieldStateInfo {
  state: FieldState;
  originalValue: number | string | Vector3;
  currentValue: number | string | Vector3;
  appliedValue?: number | string | Vector3;
  lastModified: number;
  lastApplied?: number;
  isValidValue: boolean;
  isInBounds: boolean;
  performanceImpact?: 'low' | 'medium' | 'high';
}

export interface EnhancedHandlingState {
  // Immutable reference values (never change)
  readonly stockHandling: Readonly<BaseHandlingData>;
  
  // Current working values (can change)
  currentHandling: Partial<BaseHandlingData>;
  
  // Applied values (last successfully applied to vehicle)
  appliedHandling: Partial<BaseHandlingData>;
  
  // Field state tracking
  fieldStates: Record<string, FieldStateInfo>;
  
  // Session metadata
  sessionId: string;
  lastSyncTime: number;
  isDirty: boolean;
}

// Flag category management
export interface FlagCategoryState {
  categoryId: 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags';
  originalFlags: string;
  currentFlags: string;
  appliedFlags: string;
  enabledFlags: string[];
  disabledFlags: string[];
  lastModified: number;
}


