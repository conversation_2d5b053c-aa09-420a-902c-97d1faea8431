import React, { useState, useCallback } from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import { QuickAccessPreviewConfig, QuickAccessSlotConfig, QuickAccessSourceType } from '@shared';

interface QuickAccessConfigPanelProps {
  onClose: () => void;
}

const QuickAccessConfigPanel: React.FC<QuickAccessConfigPanelProps> = ({ onClose }) => {
  const store = useInventoryStore();
  const [localConfig, setLocalConfig] = useState<QuickAccessPreviewConfig>({ ...store.quickAccessConfig });
  const [draggedSlot, setDraggedSlot] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<'slots' | 'appearance'>('slots');

  // Available source slots for configuration
  const mainSlots = Array.from({ length: 20 }, (_, i) => i); // Assuming 20 main slots
  const actionSlots = [0, 1, 2, 3]; // Phone, Armor, Weapon, Tablet

  const handleSlotConfigChange = useCallback((slotIndex: number, updates: Partial<QuickAccessSlotConfig>) => {
    setLocalConfig(prev => ({
      ...prev,
      slots: prev.slots.map(slot => 
        slot.slot === slotIndex ? { ...slot, ...updates } : slot
      )
    }));
  }, []);

  const handleDragStart = (e: React.DragEvent, slotIndex: number) => {
    setDraggedSlot(slotIndex);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetSlot: number) => {
    e.preventDefault();
    if (draggedSlot === null || draggedSlot === targetSlot) return;

    setLocalConfig(prev => {
      const newSlots = [...prev.slots];
      const draggedSlotData = newSlots.find(s => s.slot === draggedSlot);
      const targetSlotData = newSlots.find(s => s.slot === targetSlot);

      if (draggedSlotData && targetSlotData) {
        // Swap the slot configurations
        const tempSource = draggedSlotData.source;
        const tempSourceSlot = draggedSlotData.sourceSlot;
        
        draggedSlotData.source = targetSlotData.source;
        draggedSlotData.sourceSlot = targetSlotData.sourceSlot;
        
        targetSlotData.source = tempSource;
        targetSlotData.sourceSlot = tempSourceSlot;
      }

      return { ...prev, slots: newSlots };
    });
    
    setDraggedSlot(null);
  };

  const applyConfig = () => {
    store.updateQuickAccessConfig(localConfig);
    onClose();
  };

  const resetToDefaults = () => {
    const defaultConfig: QuickAccessPreviewConfig = {
      slots: [
        { slot: 0, source: 'main', sourceSlot: 0, keyBinding: 1, enabled: true },
        { slot: 1, source: 'main', sourceSlot: 1, keyBinding: 2, enabled: true },
        { slot: 2, source: 'main', sourceSlot: 2, keyBinding: 3, enabled: true },
        { slot: 3, source: 'main', sourceSlot: 3, keyBinding: 4, enabled: true },
        { slot: 4, source: 'action', sourceSlot: 2, keyBinding: 5, enabled: true }
      ],
      visible: true,
      position: 'bottom'
    };
    setLocalConfig(defaultConfig);
  };

  const getSlotPreviewItem = (slotConfig: QuickAccessSlotConfig) => {
    if (!slotConfig.enabled) return null;
    
    if (slotConfig.source === 'main') {
      const mainSlot = store.gridItems.find(slot => slot.index === slotConfig.sourceSlot);
      return mainSlot?.item;
    } else {
      const actionSlot = store.actionSlots.find(slot => slot.index === slotConfig.sourceSlot);
      return actionSlot?.item;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[100] flex items-center justify-center">
      <div className="bg-neutral-800 rounded-xl border border-neutral-700 shadow-2xl w-[90%] max-w-4xl max-h-[90%] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-900/20 via-green-800/10 to-green-900/20 border-b border-neutral-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <i className="fas fa-keyboard text-green-400 text-xl" />
              <h2 className="text-xl font-semibold text-white">QuickAccess Configuration</h2>
            </div>            <button
              onClick={onClose}
              tabIndex={-1}
              className="text-neutral-400 hover:text-white transition-colors p-1"
            >
              <i className="fas fa-times text-lg" />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-neutral-700">          <button
            onClick={() => setActiveTab('slots')}
            tabIndex={-1}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'slots'
                ? 'bg-green-900/20 text-green-400 border-b-2 border-green-400'
                : 'text-neutral-400 hover:text-white'
            }`}
          >
            <i className="fas fa-th-large mr-2" />
            Slot Configuration
          </button>
          <button
            onClick={() => setActiveTab('appearance')}
            tabIndex={-1}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'appearance'
                ? 'bg-green-900/20 text-green-400 border-b-2 border-green-400'
                : 'text-neutral-400 hover:text-white'
            }`}
          >
            <i className="fas fa-palette mr-2" />
            Appearance
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'slots' && (
            <div className="space-y-6">
              {/* QuickAccess Preview */}
              <div className="bg-neutral-900/50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-neutral-300 mb-3 flex items-center gap-2">
                  <i className="fas fa-eye text-green-400" />
                  Live Preview
                </h3>
                <div className="flex justify-center">
                  <div className="flex gap-3">
                    {localConfig.slots.map((slotConfig) => (
                      <div
                        key={slotConfig.slot}
                        className="relative flex flex-col items-center"
                        draggable
                        onDragStart={(e) => handleDragStart(e, slotConfig.slot)}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, slotConfig.slot)}
                      >
                        <div className={`w-16 h-16 rounded-lg border-2 transition-all cursor-move ${
                          slotConfig.enabled 
                            ? 'bg-neutral-700 border-green-500/30 hover:border-green-400/50' 
                            : 'bg-neutral-800 border-neutral-600'
                        }`}>
                          {slotConfig.enabled && (
                            <div className="w-full h-full flex items-center justify-center">
                              {getSlotPreviewItem(slotConfig) ? (
                                <i className={`fas ${getSlotPreviewItem(slotConfig)?.icon || 'fa-cube'} text-xl text-neutral-300`} />
                              ) : (
                                <i className="fas fa-plus text-neutral-500" />
                              )}
                            </div>
                          )}
                        </div>
                        <span className="mt-1 px-1.5 py-0.5 rounded bg-green-900/60 border border-green-700/30 text-green-300 text-xs font-medium">
                          {slotConfig.keyBinding}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Slot Configuration List */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-neutral-300 flex items-center gap-2">
                  <i className="fas fa-cog text-green-400" />
                  Slot Settings
                </h3>
                {localConfig.slots.map((slotConfig) => (
                  <div
                    key={slotConfig.slot}
                    className="bg-neutral-900/30 rounded-lg p-4 border border-neutral-700"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <span className="px-2 py-1 rounded bg-green-900/40 text-green-300 text-sm font-medium">
                          Slot {slotConfig.slot + 1}
                        </span>
                        <span className="px-2 py-1 rounded bg-blue-900/40 text-blue-300 text-sm">
                          Key {slotConfig.keyBinding}
                        </span>
                      </div>
                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={slotConfig.enabled}
                          onChange={(e) => handleSlotConfigChange(slotConfig.slot, { enabled: e.target.checked })}
                          className="rounded border-neutral-600 bg-neutral-700 text-green-500 focus:ring-green-500 focus:ring-offset-0"
                        />
                        <span className="text-sm text-neutral-300">Enabled</span>
                      </label>
                    </div>

                    {slotConfig.enabled && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Source Type */}
                        <div>
                          <label className="block text-xs text-neutral-400 mb-1">Source</label>                          <select
                            value={slotConfig.source}
                            onChange={(e) => handleSlotConfigChange(slotConfig.slot, { 
                              source: e.target.value as QuickAccessSourceType,
                              sourceSlot: 0 // Reset source slot when changing source type
                            })}
                            tabIndex={-1}
                            className="w-full bg-neutral-700 border border-neutral-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            <option value="main">Main Inventory</option>
                            <option value="action">Action Slots</option>
                          </select>
                        </div>

                        {/* Source Slot */}
                        <div>
                          <label className="block text-xs text-neutral-400 mb-1">Source Slot</label>
                          <select
                            value={slotConfig.sourceSlot}
                            onChange={(e) => handleSlotConfigChange(slotConfig.slot, { sourceSlot: parseInt(e.target.value) })}
                            className="w-full bg-neutral-700 border border-neutral-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            {slotConfig.source === 'main' 
                              ? mainSlots.map(slot => (
                                  <option key={slot} value={slot}>Slot {slot + 1}</option>
                                ))
                              : actionSlots.map(slot => (
                                  <option key={slot} value={slot}>
                                    {slot === 0 ? 'Phone' : slot === 1 ? 'Armor' : slot === 2 ? 'Weapon' : 'Tablet'}
                                  </option>
                                ))
                            }
                          </select>
                        </div>

                        {/* Key Binding */}
                        <div>
                          <label className="block text-xs text-neutral-400 mb-1">Key Binding</label>
                          <select
                            value={slotConfig.keyBinding}
                            onChange={(e) => handleSlotConfigChange(slotConfig.slot, { keyBinding: parseInt(e.target.value) })}
                            className="w-full bg-neutral-700 border border-neutral-600 rounded px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(key => (
                              <option key={key} value={key}>{key}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'appearance' && (
            <div className="space-y-6">
              {/* Position Settings */}
              <div className="bg-neutral-900/30 rounded-lg p-4 border border-neutral-700">
                <h3 className="text-sm font-medium text-neutral-300 mb-3 flex items-center gap-2">
                  <i className="fas fa-arrows-alt text-green-400" />
                  Position
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {(['bottom', 'top', 'left', 'right'] as const).map(position => (
                    <button
                      key={position}
                      onClick={() => setLocalConfig(prev => ({ ...prev, position }))}
                      className={`p-3 rounded-lg border-2 transition-all text-center ${
                        localConfig.position === position
                          ? 'border-green-500 bg-green-900/20 text-green-400'
                          : 'border-neutral-600 bg-neutral-700 text-neutral-400 hover:border-neutral-500'
                      }`}
                    >
                      <i className={`fas ${
                        position === 'bottom' ? 'fa-arrow-down' :
                        position === 'top' ? 'fa-arrow-up' :
                        position === 'left' ? 'fa-arrow-left' :
                        'fa-arrow-right'
                      } text-lg mb-1`} />
                      <div className="text-xs capitalize">{position}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Visibility Settings */}
              <div className="bg-neutral-900/30 rounded-lg p-4 border border-neutral-700">
                <h3 className="text-sm font-medium text-neutral-300 mb-3 flex items-center gap-2">
                  <i className="fas fa-eye text-green-400" />
                  Visibility
                </h3>
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localConfig.visible}
                    onChange={(e) => setLocalConfig(prev => ({ ...prev, visible: e.target.checked }))}
                    className="rounded border-neutral-600 bg-neutral-700 text-green-500 focus:ring-green-500 focus:ring-offset-0"
                  />
                  <div>
                    <div className="text-sm text-white">Show QuickAccess Preview</div>
                    <div className="text-xs text-neutral-400">Display the quickaccess preview overlay</div>
                  </div>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-neutral-700 p-4 bg-neutral-900/20">
          <div className="flex justify-between">
            <button
              onClick={resetToDefaults}
              className="px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <i className="fas fa-undo" />
              Reset to Defaults
            </button>            <div className="flex gap-3">
              <button
                onClick={onClose}
                tabIndex={-1}
                className="px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={applyConfig}
                tabIndex={-1}
                className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors flex items-center gap-2"
              >
                <i className="fas fa-check" />
                Apply Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickAccessConfigPanel;
