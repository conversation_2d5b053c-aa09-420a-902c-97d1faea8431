import React from 'react';
import { VehicleInfo as VehicleInfoType } from '../../../scripts/shared/types';

interface VehicleInfoProps {
  vehicleInfo: VehicleInfoType | null;
  onRefresh: () => void;
}

const VehicleInfo: React.FC<VehicleInfoProps> = ({ vehicleInfo, onRefresh }) => {
  if (!vehicleInfo || !vehicleInfo.isInVehicle) {
    return (
      <div className="vehicle-info no-vehicle">
        <div className="vehicle-status">
          <span className="status-icon">🚫</span>
          <span className="status-text">Not in vehicle</span>
          <button className="refresh-btn" onClick={onRefresh} title="Refresh vehicle info">
            🔄
          </button>
        </div>
        <div className="no-vehicle-message">
          Enter a vehicle to modify handling parameters
        </div>
      </div>
    );
  }

  return (
    <div className="vehicle-info">
      <div className="vehicle-header">
        <div className="vehicle-details">
          <div className="vehicle-name">{vehicleInfo.displayName}</div>
          <div className="vehicle-model">{vehicleInfo.model}</div>
          <div className="vehicle-meta">
            <span className="vehicle-hash">Hash: {vehicleInfo.hash.toString(16).toUpperCase()}</span>
            <span className="vehicle-class">{vehicleInfo.className}</span>
          </div>
        </div>
        <button className="refresh-btn" onClick={onRefresh} title="Refresh vehicle info">
          🔄
        </button>
      </div>
    </div>
  );
};

export default VehicleInfo;
