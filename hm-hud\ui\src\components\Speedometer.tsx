import React from 'react';
import { useHudStore } from '../store/hudStore';

interface SpeedometerProps {
  speed?: number;
  fuel?: number;
  engineHealth?: number;
  bodyHealth?: number;
  gear?: number;
  nitrous?: number;
  seatbelt?: boolean;
}

const Speedometer: React.FC<SpeedometerProps> = ({ 
  speed: propSpeed, 
  fuel: propFuel,
  engineHealth: propEngineHealth,
  gear: propGear,
  nitrous: propNitrous,
  seatbelt: propSeatbelt,
}) => {
  const { hudData } = useHudStore();
    // Use props if provided, otherwise fall back to store data with safe defaults
  const speed = Math.max(0, propSpeed ?? hudData.vehicle.speed ?? 0);
  const fuel = Math.max(0, Math.min(100, propFuel ?? hudData.vehicle.fuel ?? 100));
  const engineHealth = Math.max(0, Math.min(100, propEngineHealth ?? (hudData.vehicle.engineHealth ? hudData.vehicle.engineHealth / 10 : 100))); // Convert 0-1000 to 0-100
  const gear = propGear ?? hudData.vehicle.gear ?? 1;
  const nitrous = Math.max(0, Math.min(100, propNitrous ?? hudData.vehicle.nitrous ?? 0)); // Fixed: was nitro, now nitrous
  const seatbelt = propSeatbelt ?? hudData.vehicle.seatbelt ?? false;// Porsche 911 Sports Car Style Speedometer
  const maxSpeed = 300;
  // Calculate needle angle to match speed markings positioning
  // Speed markings start at -240° for 0 MPH and sweep 300° to 60° for 300 MPH
  const speedPercentage = Math.min(Math.max(0, speed) / maxSpeed, 1); // Ensure valid speed and clamp to max 300 MPH
  const needleAngle = -150 + (speedPercentage * 300); // Start at -150°, add proportional angle
  // For browser testing, always show the speedometer
  const isBrowser = typeof window !== 'undefined' && !(window as unknown as { invokeNative?: unknown }).invokeNative;
  
  // Don't render if not in a vehicle or engine is off (except in browser)
  if (!isBrowser && (!hudData.vehicle.isInVehicle || !hudData.vehicle.showSpeedometer)) {
    return null;
  }
  return (    <div className="space-y-2">
      {/* Fuel ring container - positioned outside speedometer */}
      <div className="relative w-72 h-72 mx-auto">
        {/* Outer fuel ring */}
        <div className="absolute inset-0">
          <svg className="w-full h-full" viewBox="0 0 288 288">
            {(() => {              const fuelPercentage = Math.max(0, Math.min(100, fuel)) / 100;
              // Fuel ring: 50 MPH range (0-50) = 50° of the 300° speedometer sweep
              // 0 MPH is at -240°, 50 MPH is at -190° (50° sweep)
              const fuelStartAngle = -240;
              const fuelEndAngle = -190;
              const fuelSweepAngle = 50;
              const actualFuelAngle = fuelPercentage * fuelSweepAngle;
              const fuelCurrentEndAngle = fuelStartAngle + actualFuelAngle;              // Create fuel arc path
              const fuelRadius = 139; // 10% smaller than 154
              const fuelLargeArcFlag = actualFuelAngle > 180 ? 1 : 0;
              
              const fuelStartRadian = (fuelStartAngle * Math.PI) / 180;
              const fuelEndRadian = (fuelCurrentEndAngle * Math.PI) / 180;
              const fuelBackgroundEndRadian = (fuelEndAngle * Math.PI) / 180;
              
              const fuelStartX = 144 + fuelRadius * Math.cos(fuelStartRadian); // Center at 144 (288/2)
              const fuelStartY = 144 + fuelRadius * Math.sin(fuelStartRadian);
              const fuelEndX = 144 + fuelRadius * Math.cos(fuelEndRadian);
              const fuelEndY = 144 + fuelRadius * Math.sin(fuelEndRadian);
              const fuelBackgroundEndX = 144 + fuelRadius * Math.cos(fuelBackgroundEndRadian);
              const fuelBackgroundEndY = 144 + fuelRadius * Math.sin(fuelBackgroundEndRadian);
              
              const fuelPathData = `M ${fuelStartX} ${fuelStartY} A ${fuelRadius} ${fuelRadius} 0 ${fuelLargeArcFlag} 1 ${fuelEndX} ${fuelEndY}`;
              const fuelBackgroundPathData = `M ${fuelStartX} ${fuelStartY} A ${fuelRadius} ${fuelRadius} 0 0 1 ${fuelBackgroundEndX} ${fuelBackgroundEndY}`;
                // Engine health ring: 50 MPH range (300-250) = 50° of the 300° speedometer sweep  
              // 300 MPH is at 60°, 250 MPH is at 10° (50° sweep clockwise)
              const enginePercentage = Math.max(0, Math.min(100, engineHealth)) / 100;
              const engineStartAngle = 60;
              const engineEndAngle = 10;
              const engineSweepAngle = 50;
              const actualEngineAngle = enginePercentage * engineSweepAngle;
              const engineCurrentEndAngle = engineStartAngle - actualEngineAngle;              const engineRadius = 139; // 10% smaller than 154
              const engineLargeArcFlag = actualEngineAngle > 180 ? 1 : 0;
              
              const engineStartRadian = (engineStartAngle * Math.PI) / 180;
              const engineEndRadian = (engineCurrentEndAngle * Math.PI) / 180;
              const engineBackgroundEndRadian = (engineEndAngle * Math.PI) / 180;
              
              const engineStartX = 144 + engineRadius * Math.cos(engineStartRadian); // Center at 144
              const engineStartY = 144 + engineRadius * Math.sin(engineStartRadian);
              const engineEndX = 144 + engineRadius * Math.cos(engineEndRadian);
              const engineEndY = 144 + engineRadius * Math.sin(engineEndRadian);
              const engineBackgroundEndX = 144 + engineRadius * Math.cos(engineBackgroundEndRadian);
              const engineBackgroundEndY = 144 + engineRadius * Math.sin(engineBackgroundEndRadian);
                const enginePathData = `M ${engineStartX} ${engineStartY} A ${engineRadius} ${engineRadius} 0 ${engineLargeArcFlag} 0 ${engineEndX} ${engineEndY}`;
              const engineBackgroundPathData = `M ${engineStartX} ${engineStartY} A ${engineRadius} ${engineRadius} 0 0 0 ${engineBackgroundEndX} ${engineBackgroundEndY}`;              // Nitrous ring: 150 MPH range (75-225) aligned with speedometer markings
              // Use the EXACT same angle calculation as speedometer markings for perfect alignment
              // Speedometer formula: angle = (i * 300 / 60) - 240 where i = speed/5
              // For 75 MPH: i = 15, angle = (15 * 300/60) - 240 = -165°
              // For 225 MPH: i = 45, angle = (45 * 300/60) - 240 = -15°
              const nitrousPercentage = Math.max(0, Math.min(100, nitrous)) / 100;              const nitrousStartAngle = (15 * 300 / 60) - 240; // 75 MPH marking angle
              const nitrousEndAngle = (45 * 300 / 60) - 240;   // 225 MPH marking angle
              const nitrousSweepAngle = nitrousEndAngle - nitrousStartAngle; // Calculate actual sweep
              const actualNitrousAngle = nitrousPercentage * nitrousSweepAngle;              const nitrousCurrentEndAngle = nitrousStartAngle + actualNitrousAngle;              // Use same radius as fuel and engine rings for perfect alignment
              const nitrousRadius = 139; // 10% smaller than 154 to match fuel/engine rings
              const nitrousLargeArcFlag = actualNitrousAngle > 180 ? 1 : 0;
              
              const nitrousStartRadian = (nitrousStartAngle * Math.PI) / 180;
              const nitrousEndRadian = (nitrousCurrentEndAngle * Math.PI) / 180;
              const nitrousBackgroundEndRadian = (nitrousEndAngle * Math.PI) / 180;
                const nitrousStartX = 144 + nitrousRadius * Math.cos(nitrousStartRadian); // Center at 144
              const nitrousStartY = 144 + nitrousRadius * Math.sin(nitrousStartRadian);
              const nitrousEndX = 144 + nitrousRadius * Math.cos(nitrousEndRadian);
              const nitrousEndY = 144 + nitrousRadius * Math.sin(nitrousEndRadian);              const nitrousBackgroundEndX = 144 + nitrousRadius * Math.cos(nitrousBackgroundEndRadian);
              const nitrousBackgroundEndY = 144 + nitrousRadius * Math.sin(nitrousBackgroundEndRadian);
                // Use identical arc parameters for perfect alignment
              // Both arcs start from the same point, use same radius and center
              // For 150° sweep: large arc flag = 0 (since 150° < 180°)
              const nitrousPathData = `M ${nitrousStartX} ${nitrousStartY} A ${nitrousRadius} ${nitrousRadius} 0 ${nitrousLargeArcFlag} 1 ${nitrousEndX} ${nitrousEndY}`;
              const nitrousBackgroundPathData = `M ${nitrousStartX} ${nitrousStartY} A ${nitrousRadius} ${nitrousRadius} 0 0 1 ${nitrousBackgroundEndX} ${nitrousBackgroundEndY}`;
              
              // Check if vehicle has nitrous (for browser testing, set to true)
              const hasNitrous = true; // In FiveM, this would check if the vehicle has nitrous installed
              
              return (
                <g>                  {/* Gradient definitions */}
                  <defs>                    <linearGradient id="fuelBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="rgb(35, 35, 35)" stopOpacity="0.9" />
                      <stop offset="50%" stopColor="rgb(25, 25, 25)" stopOpacity="1" />
                      <stop offset="100%" stopColor="rgb(18, 18, 18)" stopOpacity="0.8" />
                    </linearGradient>
                    <linearGradient id="engineBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="rgb(35, 35, 35)" stopOpacity="0.9" />
                      <stop offset="50%" stopColor="rgb(25, 25, 25)" stopOpacity="1" />
                      <stop offset="100%" stopColor="rgb(18, 18, 18)" stopOpacity="0.8" />
                    </linearGradient><filter id="smoothGlow">
                      <feGaussianBlur stdDeviation="0.5" result="coloredBlur"/>
                      <feMerge> 
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                      </feMerge>
                    </filter>                    <filter id="darkShadow">
                      <feDropShadow dx="0" dy="0" stdDeviation="1" floodColor="rgb(0, 0, 0)" floodOpacity="0.5"/>
                    </filter>
                  </defs>                  {/* Fuel ring background with dark fill and emerald border */}
                  <path
                    d={fuelBackgroundPathData}
                    fill="none"
                    stroke="rgb(25, 25, 25)"
                    strokeWidth="8.5"
                    opacity="0.8"
                    strokeLinecap="round"
                  />
                  <path
                    d={fuelBackgroundPathData}
                    fill="none"
                    stroke="rgb(16, 185, 129)"
                    strokeWidth="8"
                    opacity="0.4"
                    strokeLinecap="round"
                  />
                  {/* Active fuel ring - only render if fuel > 0 */}
                  {fuel > 0 && (
                    <path
                      d={fuelPathData}
                      fill="none"
                      stroke={fuel > 25 ? "rgb(16, 185, 129)" : fuel > 10 ? "rgb(251, 191, 36)" : "rgb(239, 68, 68)"}
                      strokeWidth="8"
                      strokeLinecap="round"
                    />                  )}                  {/* Engine health ring background with dark fill and emerald border */}
                  <path
                    d={engineBackgroundPathData}
                    fill="none"
                    stroke="rgb(25, 25, 25)"
                    strokeWidth="8.5"
                    opacity="0.8"
                    strokeLinecap="round"
                  />
                  <path
                    d={engineBackgroundPathData}
                    fill="none"
                    stroke="rgb(16, 185, 129)"
                    strokeWidth="8"
                    opacity="0.4"
                    strokeLinecap="round"
                  />
                  {/* Active engine health ring - only render if engineHealth > 0 */}
                  {engineHealth > 0 && (
                    <path
                      d={enginePathData}
                      fill="none"
                      stroke={engineHealth > 75 ? "rgb(16, 185, 129)" : engineHealth > 50 ? "rgb(251, 191, 36)" : engineHealth > 25 ? "rgb(255, 165, 0)" : "rgb(239, 68, 68)"}
                      strokeWidth="8"
                      strokeLinecap="round"
                    />
                  )}
                    {/* Nitrous ring - only render if vehicle has nitrous */}
                  {hasNitrous && (
                    <>
                      {/* Nitrous ring background with enhanced visibility */}
                      <path
                        d={nitrousBackgroundPathData}
                        fill="none"
                        stroke="rgb(20, 20, 20)"
                        strokeWidth="9"
                        opacity="1"
                        strokeLinecap="round"
                      />
                      <path
                        d={nitrousBackgroundPathData}
                        fill="none"
                        stroke="rgb(0, 162, 255)"
                        strokeWidth="7"
                        opacity="0.3"
                        strokeLinecap="round"
                      />
                      {/* Active nitrous ring - only render if nitrous > 0 */}
                      {nitrous > 0 && (
                        <path
                          d={nitrousPathData}
                          fill="none"
                          stroke="rgb(0, 162, 255)"
                          strokeWidth="7"
                          strokeLinecap="round"
                          style={{
                            filter: "drop-shadow(0 0 4px rgba(0, 162, 255, 0.6))"
                          }}
                        />                      )}
                    </>
                  )}                  {/* Nitrous range indicators - small markers at 75 and 225 MPH */}
                  {hasNitrous && (
                    <>                      {/* 75 MPH marker */}
                      <circle
                        cx={144 + nitrousRadius * Math.cos(nitrousStartRadian)}
                        cy={144 + nitrousRadius * Math.sin(nitrousStartRadian)}
                        r="2"
                        fill="rgb(0, 162, 255)"
                        opacity="0.8"
                      />
                      {/* 225 MPH marker */}
                      <circle
                        cx={144 + nitrousRadius * Math.cos(nitrousBackgroundEndRadian)}
                        cy={144 + nitrousRadius * Math.sin(nitrousBackgroundEndRadian)}
                        r="2"
                        fill="rgb(0, 162, 255)"
                        opacity="0.8"
                      />
                    </>
                  )}                  {/* Fuel icon positioned before fuel ring start with spacing */}
                  <g transform={`translate(${144 + 117 * Math.cos(-245 * Math.PI / 180)}, ${144 + 144 * Math.sin(-245 * Math.PI / 180)})`}>
                    {/* Gas pump FontAwesome icon */}
                    <foreignObject x="-8" y="-8" width="16" height="16">
                      <i className="fas fa-gas-pump" style={{
                        color: fuel > 25 ? "rgb(16, 185, 129)" : fuel > 10 ? "rgb(251, 191, 36)" : "rgb(239, 68, 68)",
                        fontSize: "12px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "16px",
                        height: "16px"
                      }}></i>
                    </foreignObject>
                  </g>                  {/* Engine icon positioned before engine ring start with spacing */}
                  <g transform={`translate(${144 + 117 * Math.cos(65 * Math.PI / 180)}, ${144 + 144 * Math.sin(65 * Math.PI / 180)})`}>
                    {/* Engine/gear FontAwesome icon */}
                    <foreignObject x="-8" y="-8" width="16" height="16">
                      <i className="fas fa-cog" style={{
                        color: engineHealth > 75 ? "rgb(16, 185, 129)" : engineHealth > 50 ? "rgb(251, 191, 36)" : engineHealth > 25 ? "rgb(255, 165, 0)" : "rgb(239, 68, 68)",
                        fontSize: "12px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "16px",
                        height: "16px"
                      }}></i>
                    </foreignObject>                  </g>                  {/* Nitrous icon - only render if vehicle has nitrous */}
                  {hasNitrous && (
                    <g transform={`translate(${144 + 117 * Math.cos(-175 * Math.PI / 180)}, ${144 + 144 * Math.sin(-175 * Math.PI / 180)})`}>
                      {/* Nitrous bottle FontAwesome icon */}
                      <foreignObject x="-8" y="-8" width="16" height="16">
                        <i className="fas fa-tint" style={{
                          color: nitrous > 75 ? "rgb(0, 162, 255)" : nitrous > 50 ? "rgb(16, 185, 129)" : nitrous > 25 ? "rgb(251, 191, 36)" : "rgb(239, 68, 68)",
                          fontSize: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: "16px",
                          height: "16px"
                        }}></i>
                      </foreignObject>
                    </g>
                  )}                  {/* Seatbelt indicator positioned on the left side of speedometer */}
                  <g transform={`translate(${144 + 117 * Math.cos(180 * Math.PI / 180)}, ${144 + 117 * Math.sin(180 * Math.PI / 180)})`}>
                    {/* Seatbelt FontAwesome icon */}
                    <foreignObject x="-12" y="-12" width="24" height="24">
                      <div style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "24px",
                        height: "24px",
                        backgroundColor: "rgba(0, 0, 0, 0.8)",
                        borderRadius: "4px",
                        border: `1px solid ${seatbelt ? "rgb(16, 185, 129)" : "rgb(239, 68, 68)"}`,
                        padding: "2px"
                      }}>
                        <i className="fas fa-user-shield" style={{
                          color: seatbelt ? "rgb(16, 185, 129)" : "rgb(239, 68, 68)",
                          fontSize: "10px"
                        }}></i>
                        <span style={{
                          color: seatbelt ? "rgb(16, 185, 129)" : "rgb(239, 68, 68)",
                          fontSize: "6px",
                          fontWeight: "bold",
                          marginTop: "1px"
                        }}>
                          {seatbelt ? 'ON' : 'OFF'}
                        </span>
                      </div>
                    </foreignObject>
                  </g>
                </g>
              );
            })()}
          </svg>
        </div>        {/* Speedometer - centered within fuel ring */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-black rounded-full border-2 border-emerald-500 shadow-2xl shadow-emerald-500/20">
        <div className="absolute inset-1 bg-gradient-to-br from-gray-900 to-black rounded-full">
          <svg className="w-full h-full" viewBox="0 0 256 256">            {/* Outer green ring */}
            <circle cx="128" cy="128" r="120" fill="none" stroke="rgb(16, 185, 129)" strokeWidth="2" />
            
            {/* Speed markings - sports car style with consistent subdivisions */}
            {Array.from({length: 61}, (_, i) => i * 5).map((num, i) => {
              const angle = (i * 300 / 60) - 240; // 60 total markings for consistent spacing
              const radian = (angle * Math.PI) / 180;
              const x1 = 128 + 110 * Math.cos(radian);
              const y1 = 128 + 110 * Math.sin(radian);
              const x2 = 128 + (num % 50 === 0 ? 85 : num % 25 === 0 ? 92 : 100) * Math.cos(radian);
              const y2 = 128 + (num % 50 === 0 ? 85 : num % 25 === 0 ? 92 : 100) * Math.sin(radian);
              const textX = 128 + 70 * Math.cos(radian);
              const textY = 128 + 70 * Math.sin(radian);
              
              return (
                <g key={i}>
                  <line
                    x1={x1} y1={y1} x2={x2} y2={y2}
                    stroke={num > 250 ? "rgb(239, 68, 68)" : "rgb(16, 185, 129)"}
                    strokeWidth={num % 50 === 0 ? "3" : num % 25 === 0 ? "2" : "1"}
                  />                  {num % 50 === 0 && (
                    <text
                      x={textX} y={textY}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize="14"
                      fill={num > 250 ? "rgb(239, 68, 68)" : "rgb(16, 185, 129)"}
                      fontFamily="Arial, sans-serif"
                      fontWeight="bold"
                    >
                      {num}
                    </text>
                  )}
                </g>
              );            })}            {/* Speed needle - precise sports car style */}
            <g transform={`rotate(${needleAngle} 128 128)`}>
              <line
                x1="128" y1="128" x2="128" y2="60"
                stroke="rgb(16, 185, 129)"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <polygon
                points="128,60 125,66 131,66"
                fill="rgb(16, 185, 129)"
              />
            </g>
              {/* Center hub */}
            <circle cx="128" cy="128" r="8" fill="rgb(55, 65, 81)" stroke="rgb(16, 185, 129)" strokeWidth="2" />
          </svg>          {/* Speed and gear display positioned below center */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 translate-y-1 text-center">
            <div className="text-white text-sm font-bold tabular-nums">{speed}</div>
            <div className="text-emerald-300 text-xs font-bold">MPH</div>            {/* Gear display - number only, positioned under MPH with gradient square background */}
            <div className="mt-1 inline-flex items-center justify-center w-8 h-8 bg-gradient-to-br from-emerald-900/40 via-black/90 to-gray-900/80 border border-emerald-500/60 rounded-sm shadow-lg shadow-emerald-500/20">
              <span className="text-white text-lg font-bold drop-shadow-lg">
                {gear === 0 ? 'R' : gear}
              </span>
            </div>
          </div>
        </div>        </div>
      </div>
    </div>
  );
};

export default Speedometer;
