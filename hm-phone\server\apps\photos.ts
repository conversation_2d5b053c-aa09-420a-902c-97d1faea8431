/**
 * Photos App - Server Side
 *
 * This file handles server-side functionality for the Photos app.
 */

import config from '@shared/config';

/**
 * Initialize the photos app
 */
export function initializePhotosApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
    
    // Listen for player loaded event to ensure photo data is available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Photos] Player ${source} loaded, ensuring photo data is available`);
        
        // Initialize player data if needed
        await ensurePlayerPhotoData(source);
    });
}

/**
 * Ensure player has photo data initialized
 */
async function ensurePlayerPhotoData(source: number): Promise<void> {
    try {
        const player = global.getServerPlayerData(source);
        if (!player) return;

        const identifier = player.stateid || player.identifier;
        if (!identifier) return;

        // Check if player has any photos, this ensures they're properly initialized
        const photos = await getPhotos(identifier);
        console.log(`[Photos] Player ${source} has ${photos.length} photos`);
    } catch (error) {
        console.error(`[Photos] Error ensuring player photo data:`, error);
    }
}

/**
 * Register server events for the photos app
 */
function registerServerEvents(): void {
    // Note: The 'hm-phone:savePhoto' event handler has been removed to prevent duplicate handlers
    // This event is now handled exclusively in the camera.ts module

    // Register event for getting photos
    onNet('hm-phone:getPhotos', async () => {
        const source = global.source;
        console.log(`[Photos] Received getPhotos event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Photos] Player ${source} not found`);
                emitNet('hm-phone:photosError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Photos] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:photosError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get photos for the player
            const photos = await getPhotos(playerIdentifier);

            // Send the photos back to the client
            emitNet('hm-phone:photos', source, photos);
        } catch (error) {
            console.error('[Photos] Error getting photos:', error);
            emitNet('hm-phone:photosError', source, 'Failed to get photos');
        }
    });

    // Register event for deleting photos
    onNet('hm-phone:deletePhoto', async (photoId: number) => {
        const source = global.source;
        console.log(`[Photos] Received deletePhoto event from player ${source} for photo ${photoId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Photos] Player ${source} not found`);
                emitNet('hm-phone:photoDeleteError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Photos] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:photoDeleteError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Delete the photo
            await deletePhoto(photoId, playerIdentifier);

            // Send the result back to the client
            emitNet('hm-phone:photoDeleted', source, photoId);
        } catch (error) {
            console.error('[Photos] Error deleting photo:', error);
            emitNet('hm-phone:photoDeleteError', source, 'Failed to delete photo');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[Photos] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[Photos] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_photos table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_photos (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    imageUrl TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata JSON,
                    INDEX idx_identifier (identifier)
                )
            `,
                [],
                () => {
                    console.log('[Photos] Database tables initialized');
                }
            );
        } else {
            console.error('[Photos] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[Photos] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get photos for a player
 * @param identifier Player identifier
 * @returns Array of photos
 */
async function getPhotos(identifier: string): Promise<any[]> {
    console.log(`[Photos] Getting photos for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let photos = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            photos = await global.exports.oxmysql.query_async(
                'SELECT * FROM phone_photos WHERE identifier = ? ORDER BY timestamp DESC',
                [identifier]
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            photos = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    'SELECT * FROM phone_photos WHERE identifier = ? ORDER BY timestamp DESC',
                    [identifier],
                    (result: any) => {
                        if (result && Array.isArray(result)) {
                            resolve(result);
                        } else {
                            resolve([]);
                        }
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // Format the photos
        return photos.map((photo: any) => ({
            id: photo.id,
            identifier: photo.identifier,
            imageUrl: photo.imageUrl,
            timestamp: new Date(photo.timestamp).getTime(),
            metadata: JSON.parse(photo.metadata || '{}')
        }));
    } catch (error: any) {
        console.error('[Photos] Error getting photos from database:', error);
        throw new Error(`Database error: ${error?.message || 'Unknown database error'}`);
    }
}

/**
 * Delete a photo
 * @param photoId Photo ID
 * @param identifier Player identifier (for security)
 */
async function deletePhoto(photoId: number, identifier: string): Promise<void> {
    console.log(`[Photos] Deleting photo ${photoId} for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            await global.exports.oxmysql.query_async('DELETE FROM phone_photos WHERE id = ? AND identifier = ?', [
                photoId,
                identifier
            ]);
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.query(
                    'DELETE FROM phone_photos WHERE id = ? AND identifier = ?',
                    [photoId, identifier],
                    () => {
                        resolve();
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }
    } catch (error: any) {
        console.error('[Photos] Error deleting photo from database:', error);
        throw new Error(`Database error: ${error?.message || 'Unknown database error'}`);
    }
}
