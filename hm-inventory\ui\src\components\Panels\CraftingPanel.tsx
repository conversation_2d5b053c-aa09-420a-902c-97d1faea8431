import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import { useCraftingStore } from '../../stores/craftingStore';
import { getItemDefinition } from '../../../../scripts/shared/items/items';
import { CraftingRecipe, CraftingQueueItem, CraftingPanelConfig } from '@shared';
import { craftingRecipes } from '../../../../scripts/shared/items/recipes';
import InventoryGrid from '../InventoryGrid/InventoryGrid';

// Helper functions for handling recipe properties
const getRecipeResultItemId = (recipe: CraftingRecipe): string => {
  // Now the recipe ID is the same as the item ID it produces
  return recipe.id;
};

const getRecipeQuantity = (recipe: CraftingRecipe): number => {
  return recipe.quantity;
};

const getRecipeName = (recipe: CraftingRecipe): string => {
  const resultItem = getItemDefinition(getRecipeResultItemId(recipe));
  return resultItem?.label || getRecipeResultItemId(recipe);
};

const getRecipeDescription = (recipe: CraftingRecipe): string => {
  const resultItem = getItemDefinition(getRecipeResultItemId(recipe));
  return resultItem?.description || '';
};

// Function to get the icon for a recipe (from the result item)
// This is used in the component rendering for consistency
const getRecipeIcon = (recipe: CraftingRecipe): string | undefined => {
  // Get icon from the item definition using recipe.id
  const resultItem = getItemDefinition(recipe.id);
  return resultItem?.icon;
};

// Default configuration for the crafting panel
const DEFAULT_CONFIG: CraftingPanelConfig = {
  inventoryColumns: 4,
  inventoryMaxVisibleRows: 2,
  maxQueueSlots: 3,
};

// Recipe Card Component
const RecipeCard: React.FC<{ 
  recipe: CraftingRecipe & { canCraft: boolean }; 
  selectedRecipe: CraftingRecipe | null;
  onSelectRecipe: (recipe: CraftingRecipe) => void;
}> = ({ recipe, selectedRecipe, onSelectRecipe }) => {
  const canCraft = recipe.canCraft;
  const recipeName = getRecipeName(recipe);
  const recipeQuantity = getRecipeQuantity(recipe);
  const icon = getRecipeIcon(recipe);
  return (
    <div
      className={`w-[5.5rem] h-[5.5rem] bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg flex flex-col items-center justify-center relative cursor-pointer transition-all duration-200 ease-in-out overflow-hidden shadow-md ${selectedRecipe?.id === recipe.id ? 'ring-2 ring-green-400 border-green-500' : ''} ${!canCraft ? 'opacity-60' : ''}`}
      onClick={() => onSelectRecipe(recipe)}
    >
      {/* accent overlay */}
      <div className="absolute inset-0 pointer-events-none z-10 rounded-lg">
        <div
          className="absolute left-0 top-0 w-full h-full rounded-lg"
          style={{
            background: 'linear-gradient(135deg, rgba(34,197,94,0.13) 0%, rgba(34,197,94,0.07) 40%, rgba(0,0,0,0) 80%)',
            borderRadius: 'inherit',
          }}
        />      </div>
      <div className="w-18 h-16 flex items-center justify-center z-10 mt-1">
        {icon ? (
          icon.startsWith('fa-') ? (
            <i className={`fas ${icon} text-3xl text-neutral-200 drop-shadow-md`} />
          ) : (
            <img src={icon} alt={recipeName} className="max-w-full max-h-full object-contain drop-shadow-md" />
          )
        ) : (
          <div className="w-6 h-6 bg-neutral-500 rounded"></div>
        )}
      </div>
      
      {recipeQuantity > 1 && (
        <div className="absolute top-0.5 right-0.5 bg-green-500/90 text-white text-[0.6rem] font-bold px-1.5 py-0.5 rounded-full shadow-sm">
          {recipeQuantity}
        </div>
      )}      {!canCraft && (
        <div className="absolute inset-0 bg-red-900 bg-opacity-50 rounded-lg"></div>
      )}
      
      {/* Item Label with Enhanced Gradient Overlay - Compact within slot */}
      <div className="absolute bottom-0 left-0 right-0 z-25 slot-label-container">
        {/* Accent gradient underline */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 slot-label-accent" />
        
        {/* Gradient fade background */}
        <div className="absolute inset-0 slot-label-gradient" />
        
        {/* Label text - compact with padding */}
        <div className="relative px-2 py-1">
          <p className="slot-label-text text-neutral-200 text-[10px] font-medium text-center truncate leading-tight">
            {recipeName}
          </p>
        </div>
      </div>
    </div>
  );
};

// Crafting Queue Slot Component
const QueueSlot: React.FC<{ item?: CraftingQueueItem }> = React.memo(({ item }) => {  if (!item) {
    return (
      <div className="w-[5.5rem] h-[5.5rem] bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg flex flex-col items-center justify-center relative cursor-pointer transition-all duration-200 ease-in-out overflow-hidden shadow-md">
        <div className="text-neutral-500 text-xs opacity-0"></div>
      </div>
    );
  }
  
  // Get information about the recipe's result using helper functions
  const resultItemId = getRecipeResultItemId(item.recipe);
  const icon = getRecipeIcon(item.recipe);
  const progressPercentage = item.progress * 100;
  const timeLeft = Math.max(0, (item.duration - (Date.now() - item.startTime)) / 1000);

  // Format time display
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.ceil(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.ceil(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`w-[5.5rem] h-[5.5rem] bg-neutral-800/90 border border-neutral-700/50 rounded-xl flex items-center justify-center relative overflow-hidden shadow-md transition-all duration-200 hover:shadow-lg hover:scale-[1.02] ${progressPercentage >= 100 ? 'cursor-pointer' : ''}`}>
      {/* Card background - base layer */}
      <div className="absolute inset-0 bg-gradient-to-b from-neutral-800/40 to-neutral-900/60 rounded-xl"></div>
      
      {/* Linear progress indicator - visible overlay */}
      <div className="absolute inset-0 rounded-xl overflow-hidden">
        <div 
          className={`h-full transition-all duration-500 border-r border-white/10 ${
            progressPercentage >= 100 ? 'bg-green-500/70' : 'bg-blue-500/60'
          }`}
          style={{ 
            width: `${progressPercentage}%`,
            boxShadow: progressPercentage >= 100 
              ? '0 0 8px rgba(34, 197, 94, 0.4)' 
              : '0 0 6px rgba(59, 130, 246, 0.3)'
          }}
        ></div>
      </div>

      {/* Content container */}
      <div className="relative z-10 w-full h-full flex flex-col">
        {/* Top row - Timer/Collection and Quantity */}
        <div className="flex items-start justify-between p-1">
          {/* Timer or Collection Button */}
          {progressPercentage >= 100 ? (
            <button 
              className="px-2 py-0.5 bg-green-500 hover:bg-green-400 text-white text-[0.55rem] font-medium rounded shadow-sm transition-all duration-200 pulse-animation"
              onClick={() => {
                // TODO: Implement collection logic
                console.log('Collecting item:', resultItemId);
              }}
            >
              <i className="fas fa-hand-paper mr-1"></i>
              Collect
            </button>
          ) : (
            <div className="px-1.5 py-0.5 bg-blue-600/80 text-white text-[0.55rem] font-medium rounded shadow-sm">
              {formatTime(timeLeft)}
            </div>
          )}
          
          {/* Quantity */}
          {item.quantity > 1 && (
            <div className="px-1.5 py-0.5 bg-orange-500/90 text-white text-[0.55rem] font-bold rounded shadow-sm">
              {item.quantity}
            </div>
          )}
        </div>        {/* Center - Item Icon */}        
        <div className="flex-1 flex items-center justify-center -mt-2">
          <div className="w-18 h-16 flex items-center justify-center">
            {icon ? (
              icon.startsWith('fa-') ? (
                <i className={`fas ${icon} text-3xl text-white filter drop-shadow-md`} />
              ) : (
                <img src={icon} alt={resultItemId} className="max-w-full max-h-full object-contain filter drop-shadow-md" />
              )
            ) : (
              <div className="w-6 h-6 bg-neutral-500 rounded-lg"></div>
            )}
          </div>
        </div>
        
        {/* Bottom - Empty space for balance */}
        <div className="p-1"></div>
      </div>
      
      {/* Completion effect */}
      {progressPercentage >= 100 && (
        <div className="absolute inset-0 bg-green-500/10 rounded-xl animate-pulse"></div>
      )}
      
      {/* Ready for collection glow effect */}
      {progressPercentage >= 100 && (
        <div className="absolute inset-0 rounded-xl border-2 border-green-400/50 animate-pulse"></div>
      )}
    </div>
  );
});

const CraftingPanel: React.FC<{ config?: Partial<CraftingPanelConfig> }> = ({ config = {} }) => {
  const { gridItems } = useInventoryStore();
  
  // State for quantity selector
  const [selectedQuantity, setSelectedQuantity] = useState(1);
  
  // Use the dedicated crafting store
  const { 
    stationInventory,
    craftingQueue, 
    selectedRecipe, 
    searchTerm,
    setSelectedRecipe,
    setSearchTerm,
    addToQueue,
    getAvailableQuantity,
    canCraftRecipe,
    initializeCrafting,
    updateQueueProgress
  } = useCraftingStore();
  
  // Merge default config with provided config
  const panelConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  
  // Memoized recipe calculations
  const allRecipes = useMemo(() => Object.values(craftingRecipes), []);

  // Memoized can craft checker using crafting store - includes gridItems as dependency for reactivity
  const canPlayerCraftRecipe = useCallback((recipe: CraftingRecipe): boolean => {
    return canCraftRecipe(recipe, gridItems);
  }, [canCraftRecipe, gridItems]);

  // Check if player can craft the selected quantity
  const canPlayerCraftSelectedQuantity = useCallback((recipe: CraftingRecipe, quantity: number): boolean => {
    return recipe.ingredients.every(ingredient => {
      const availableQty = getAvailableQuantity(ingredient.itemId, gridItems);
      return availableQty >= (ingredient.quantity * quantity);
    });
  }, [getAvailableQuantity, gridItems]);

  // Memoized filtered recipes with can-craft status for better reactivity
  const filteredRecipes = useMemo(() => 
    allRecipes
      .filter(recipe => {
        const recipeName = getRecipeName(recipe);
        const recipeDescription = getRecipeDescription(recipe);
        return (
          recipeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          recipeDescription.toLowerCase().includes(searchTerm.toLowerCase())
        );
      })
      .map(recipe => ({
        ...recipe,
        canCraft: canPlayerCraftRecipe(recipe) // Pre-calculate can craft status
      })), [allRecipes, searchTerm, canPlayerCraftRecipe]
  );

  // Handle recipe crafting using crafting store
  const handleCraftRecipe = (recipe: CraftingRecipe) => {
    if (!canPlayerCraftSelectedQuantity(recipe, selectedQuantity)) return;
    addToQueue(recipe, selectedQuantity);
  };

  // Calculate maximum craftable quantity based on available materials
  const getMaxCraftableQuantity = useCallback((recipe: CraftingRecipe): number => {
    if (!recipe) return 0;
    
    let maxQuantity = 999; // Start with a high number
    
    // Check each ingredient
    for (const ingredient of recipe.ingredients) {
      const availableQty = getAvailableQuantity(ingredient.itemId, gridItems);
      const possibleCrafts = Math.floor(availableQty / ingredient.quantity);
      maxQuantity = Math.min(maxQuantity, possibleCrafts);
    }
    
    // Also consider result item maxStack if it exists
    const resultItem = getItemDefinition(getRecipeResultItemId(recipe));
    if (resultItem?.maxStack) {
      maxQuantity = Math.min(maxQuantity, resultItem.maxStack);
    }
    
    return Math.max(0, maxQuantity);
  }, [getAvailableQuantity, gridItems]);

  // Reset quantity when recipe changes
  useEffect(() => {
    if (selectedRecipe) {
      const maxQuantity = getMaxCraftableQuantity(selectedRecipe);
      setSelectedQuantity(Math.min(selectedQuantity, Math.max(1, maxQuantity)));
    }
  }, [selectedRecipe, getMaxCraftableQuantity, selectedQuantity, setSelectedQuantity]);

  // Initialize crafting data on mount
  useEffect(() => {
    initializeCrafting();
  }, [initializeCrafting, ]);

  // Update queue progress periodically
  useEffect(() => {
    const interval = setInterval(() => {
      updateQueueProgress();
    }, 1000);
    
    return () => clearInterval(interval);
  }, [updateQueueProgress]);

  return (
    <div className="relative p-4 bg-neutral-800 rounded-2xl shadow-xl flex flex-col gap-2 overflow-hidden border-l-4 border-green-500/10 transition-all duration-700 ease-in-out group">
      <div className="flex gap-4">
        {/* Left Column: Recipes, Details & Inventory */}
        <div className="flex-1 flex flex-col gap-4 overflow-hidden">
          {/* Orange gradient bar at the top */}
          <div className="w-full h-1 bg-gradient-to-r from-green-400/10 via-green-500/10 to-green-600/10 opacity-30 rounded-t-2xl" />
          
          {/* Header with Search */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-bold text-neutral-100 tracking-wide flex items-center gap-2 mb-4">
              <i className="fas fa-hammer text-green-400/80" /> Crafting
            </h2>
            <div className="flex-1 max-w-[200px] mx-4">
              <input
                type="text"
                placeholder="Search recipes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-1 bg-neutral-700 border border-neutral-600 rounded-lg text-white text-sm placeholder-neutral-400 focus:border-green-500 focus:outline-none"
              />
            </div>
          </div>
          
          {/* Recipes Section */}
          <div className="flex-1 flex flex-col space-y-4 overflow-y-auto">
            <div>
              <h3 className="text-sm font-semibold text-neutral-100 mb-2">Recipes</h3>
              <div className="grid grid-cols-4 gap-2 max-h-48 overflow-y-auto">
                {filteredRecipes.map((recipe) => (
                  <RecipeCard 
                    key={recipe.id} 
                    recipe={recipe}
                    selectedRecipe={selectedRecipe}
                    onSelectRecipe={setSelectedRecipe}
                  />
                ))}
              </div>
            </div>            {/* Selected Recipe Details */}
            {selectedRecipe && (
              <div className="bg-neutral-900/60 border border-neutral-700/50 rounded-xl p-3 transition-all duration-300">                {/* Header row */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {/* Recipe icon */}
                    <div className="w-10 h-10 bg-neutral-800/80 rounded-lg flex items-center justify-center">
                      {(() => {
                        const icon = getRecipeIcon(selectedRecipe);
                        return icon ? (
                          icon.startsWith('fa-') ? (
                            <i className={`fas ${icon} text-lg text-neutral-200`} />
                          ) : (
                            <img src={icon} alt={getRecipeName(selectedRecipe)} className="w-6 h-6 object-contain" />
                          )
                        ) : (
                          <div className="w-4 h-4 bg-neutral-500 rounded"></div>
                        );
                      })()}
                    </div>
                    
                    {/* Recipe info */}
                    <div>
                      <h3 className="text-sm font-semibold text-white">{getRecipeName(selectedRecipe)}</h3>
                      <div className="flex items-center gap-3 text-xs text-neutral-400">
                        <span className="flex items-center gap-1">
                          <i className="fas fa-clock text-xs"></i>
                          {selectedRecipe.craftingTime}s
                        </span>
                        <span className="flex items-center gap-1">
                          <i className="fas fa-cube text-xs"></i>
                          x{getRecipeQuantity(selectedRecipe)}
                        </span>
                      </div>
                    </div>
                  </div>
                    {/* Quantity controls and craft button */}
                  <div className="flex items-center gap-1.5">
                    <div className="flex items-center gap-0.5">
                      <button
                        onClick={() => setSelectedQuantity(Math.max(1, selectedQuantity - 1))}
                        className="w-5 h-5 bg-neutral-700 hover:bg-neutral-600 text-white text-xs rounded flex items-center justify-center transition-colors"
                      >
                        -
                      </button>
                      <span className="text-white text-xs font-medium min-w-[1.5rem] text-center">{selectedQuantity}</span>
                      <button
                        onClick={() => setSelectedQuantity(Math.min(getMaxCraftableQuantity(selectedRecipe), selectedQuantity + 1))}
                        className="w-5 h-5 bg-neutral-700 hover:bg-neutral-600 text-white text-xs rounded flex items-center justify-center transition-colors"
                      >
                        +
                      </button>
                    </div>
                    <button
                      onClick={() => handleCraftRecipe(selectedRecipe)}
                      disabled={!canPlayerCraftSelectedQuantity(selectedRecipe, selectedQuantity)}
                      className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 disabled:bg-neutral-600 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                      Craft
                    </button>
                  </div>
                </div>
                  {/* Quantity preset buttons */}
                <div className="flex justify-end mb-2">
                  <div className="flex gap-0.5">                    {[1, 5, 10].map((qty) => (
                      <button
                        key={qty}
                        tabIndex={-1}
                        onClick={() => setSelectedQuantity(Math.min(getMaxCraftableQuantity(selectedRecipe), qty))}
                        className={`px-1.5 py-0.5 text-[10px] rounded transition-colors ${
                          selectedQuantity === qty
                            ? 'bg-green-600 text-white'
                            : 'bg-neutral-700 text-neutral-300 hover:bg-neutral-600'
                        }`}
                      >
                        {qty}
                      </button>
                    ))}
                  </div>
                </div>                {/* Materials section */}
                <div>
                  <h5 className="text-white font-medium text-[10px] mb-2 flex items-center gap-1">
                    <i className="fas fa-th-large text-[10px] text-neutral-400"></i>
                    Required Materials
                  </h5>                  <div className="grid grid-cols-2 gap-1.5">
                    {selectedRecipe.ingredients.map((ingredient, i) => {
                      const item = getItemDefinition(ingredient.itemId);
                      const playerQty = getAvailableQuantity(ingredient.itemId, gridItems);
                      const requiredQty = ingredient.quantity * selectedQuantity;
                      const hasEnough = playerQty >= requiredQty;
                      
                      return (
                        <div
                          key={i}
                          className={`relative bg-neutral-800/40 border rounded p-1.5 transition-all duration-200 hover:scale-[1.02] ${
                            hasEnough 
                              ? 'border-green-500/40 hover:border-green-400/60' 
                              : 'border-red-500/40 hover:border-red-400/60'
                          }`}
                        >
                          {/* Status indicator */}
                          <div className={`absolute top-0.5 right-0.5 w-1.5 h-1.5 rounded-full ${
                            hasEnough ? 'bg-green-400' : 'bg-red-400'
                          }`} />
                          
                          {/* Icon and item info */}
                          <div className="flex items-center gap-1.5 mb-1">
                            <div className="w-4 h-4 bg-neutral-700/50 rounded flex items-center justify-center">
                              {item?.icon ? (
                                item.icon.startsWith('fa-') ? (
                                  <i className={`fas ${item.icon} text-[8px] ${hasEnough ? 'text-green-300' : 'text-red-300'}`} />
                                ) : (
                                  <img src={item.icon} alt={item.label} className="w-3 h-3 object-contain" />
                                )
                              ) : (
                                <div className="w-2 h-2 bg-neutral-500 rounded"></div>
                              )}
                            </div>
                            <span className="text-[10px] text-neutral-200 font-medium truncate flex-1">
                              {item?.label || ingredient.itemId}
                            </span>
                          </div>
                          
                          {/* Quantity and progress */}
                          <div className="space-y-0.5">
                            <div className="flex justify-between items-center">
                              <span className={`text-[9px] font-bold ${hasEnough ? 'text-green-300' : 'text-red-300'}`}>
                                {playerQty}/{requiredQty}
                              </span>
                              <span className={`text-[8px] font-medium ${hasEnough ? 'text-green-400' : 'text-red-400'}`}>
                                {hasEnough ? '✓' : '✗'}
                              </span>
                            </div>
                            
                            {/* Progress bar */}
                            <div className="w-full h-0.5 bg-neutral-700 rounded-full overflow-hidden">
                              <div 
                                className={`h-full transition-all duration-300 ${
                                  hasEnough ? 'bg-green-500' : 'bg-red-500'
                                }`}
                                style={{ 
                                  width: `${Math.min(100, (playerQty / requiredQty) * 100)}%` 
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
            
            {/* Station Inventory Section */}
            <div className="mt-4">
              <h3 className="text-sm font-semibold text-neutral-100 mb-2 flex items-center gap-2">
                <i className="fas fa-boxes text-green-400/80" /> Station Inventory
              </h3>
              <InventoryGrid
                PanelType="crafting"
                instanceId="crafting-station-1"
                columns={panelConfig.inventoryColumns}
                visibleRows={panelConfig.inventoryMaxVisibleRows}
                slotCount={panelConfig.inventoryColumns * panelConfig.inventoryMaxVisibleRows}
                items={stationInventory}
                highlightRequiredItems={selectedRecipe?.ingredients || []}
              />
            </div>
          </div>
        </div>
        
        {/* Divider */}
        <div className="w-px bg-neutral-600" />
        
        {/* Right Column: Queue */}
        <div className="flex flex-col space-y-2 overflow-y-auto transition-all duration-700 ease-in-out">
          <h2 className="text-lg font-bold text-neutral-100 tracking-wide flex items-center gap-2 mb-4">
            <i className="fas fa-stream text-green-400/80" /> Queue
          </h2>          
          <div className="flex flex-col space-y-2">
            {Array.from({ length: panelConfig.maxQueueSlots }, (_, index) => (
              <QueueSlot key={index} item={craftingQueue[index]} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CraftingPanel;
