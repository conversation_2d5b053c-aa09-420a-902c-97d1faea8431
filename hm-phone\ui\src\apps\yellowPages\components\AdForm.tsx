import React, { useState } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { AdCategory } from '../types/yellowPagesTypes';
import { getCategoryInfo } from '../utils/categoryUtils';
import { useYellowPagesStore } from '../stores/yellowPagesStore';
import LoadingSpinner from '../../../common/components/LoadingSpinner';

interface AdFormProps {
  onClose: () => void;
}

const AdForm: React.FC<AdFormProps> = ({ onClose }) => {
  const userProfile = usePhoneStore(state => state.userProfile);
  const { loading, error } = useYellowPagesStore();
  const { createAd } = useYellowPagesStore().actions;

  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [categories, setCategories] = useState<AdCategory[]>(['SELL']);
  const [formError, setFormError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form
    if (!description) {
      setFormError('Please enter a description');
      return;
    }

    if (!userProfile?.name) {
      setFormError('User profile not available');
      return;
    }

    if (categories.length === 0) {
      setFormError('Please select at least one category');
      return;
    }

    try {
      // Create ad data object
      const adData = {
        description,
        price: price ? parseInt(price) : undefined,
        imageUrl: selectedImage || undefined,
        category: categories.length === 1 ? categories[0] : categories
      };

      // Send to server
      await createAd(adData);

      // Close form on success
      onClose();
    } catch (error) {
      console.error('Error creating ad:', error);
      setFormError('An error occurred. Please try again.');
    }
  };

  const categoryOptions: { value: AdCategory; label: string }[] = [
    { value: 'SELL', label: 'Sell' },
    { value: 'BUY', label: 'Buy' },
    { value: 'SERVICES', label: 'Services' },
    { value: 'HIRING', label: 'Hiring' },
    { value: 'EVENTS', label: 'Events' }
  ];

  const handleCategoryToggle = (category: AdCategory) => {
    setCategories(prev => {
      if (prev.includes(category)) {
        // Remove category if it's already selected
        return prev.filter(c => c !== category);
      } else {
        // Add category if it's not selected
        return [...prev, category];
      }
    });
  };

  return (
    <div className="flex-1 p-4 bg-black overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Image Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Image <span className="text-gray-500">(Optional)</span>
          </label>
          {selectedImage ? (
            <div className="relative w-full aspect-[16/9] mb-2">
              <img
                src={selectedImage}
                alt="Selected"
                className="w-full h-full object-cover rounded-lg"
              />
              <button
                type="button"
                onClick={() => setSelectedImage(null)}
                className="absolute top-2 right-2 bg-black/50 text-white/80 hover:text-white p-2 rounded-full"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          ) : (
            <button
              type="button"
              // onClick={() => setShowPhotoPicker(true)}
              className="w-full h-32 border-2 border-dashed border-gray-800 rounded-lg flex items-center justify-center hover:border-gray-700 transition-colors"
            >
              <div className="text-center">
                <i className="fas fa-camera text-2xl text-gray-500 mb-2"></i>
                <div className="text-sm text-gray-500">Click to select an image</div>
              </div>
            </button>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            value={description}
            onChange={e => setDescription(e.target.value)}
            className="w-full px-4 py-2 bg-[#1a1a1a] rounded-lg text-white border border-gray-800 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
            rows={4}
            placeholder="What are you advertising?"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Price ($) <span className="text-gray-500">(Optional)</span>
          </label>
          <input
            type="number"
            value={price}
            onChange={e => setPrice(e.target.value)}
            className="w-full px-4 py-2 bg-[#1a1a1a] rounded-lg text-white border border-gray-800 focus:outline-none focus:ring-2 focus:ring-yellow-500/50"
            placeholder="Enter price in dollars"
            min="0"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Categories <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-3 gap-2">
            {categoryOptions.map(({ value, label }) => {
              const info = getCategoryInfo(value);
              const isSelected = categories.includes(value);
              return (
                <button
                  key={value}
                  type="button"
                  onClick={() => handleCategoryToggle(value)}
                  className={`inline-flex items-center h-8 gap-1.5 px-3 rounded-full transition-all cursor-pointer
                    ${
                      isSelected
                        ? `${info.colorClasses.bg} border ${info.colorClasses.border}`
                        : 'bg-gray-900 border border-gray-800 grayscale brightness-50 hover:brightness-75'
                    }`}
                >
                  <i className={`fas fa-${info.icon} text-xs ${info.colorClasses.text}`}></i>
                  <span className={`text-sm font-medium ${info.colorClasses.text}`}>{label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {formError && (
          <div className="text-red-500 text-sm text-center">
            <i className="fas fa-exclamation-circle mr-1"></i> {formError}
          </div>
        )}

        {error && (
          <div className="text-red-500 text-sm text-center">
            <i className="fas fa-exclamation-circle mr-1"></i> {error}
          </div>
        )}

        <div className="flex justify-center gap-4 pt-4">
          <button
            type="button"
            onClick={onClose}
            disabled={loading}
            className="px-6 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-yellow-500/20 text-yellow-500 rounded-lg hover:bg-yellow-500/30 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[100px]"
          >
            {loading ? <LoadingSpinner /> : 'Post Ad'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdForm;
