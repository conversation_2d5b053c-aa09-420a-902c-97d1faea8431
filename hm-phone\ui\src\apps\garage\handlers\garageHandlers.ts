/**
 * Garage app message handlers
 */
import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useGarageStore } from '../stores/garageStore';
import { Vehicle } from '../types/garageTypes';

// Register handler for vehicles data
registerEventHandler('garage', 'vehicles', data => {
  console.log('[Garage] Received vehicles data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Update the store with the vehicles data
    useGarageStore.getState().setVehicles(data as Vehicle[]);
  } else {
    console.error('[Garage] Received invalid vehicles data (not an array):', data);
  }
});
