// navigation/store.ts
import { create } from 'zustand';
import { parseDeepLink, isValidApp, isValidView, isValidTab } from './navigationUtils';
import { NavigationState, NavigationStateData } from './navigationTypes';
import { clientRequests } from '../fivem/clientRequestSender';
import { usePhoneStore } from '../common/stores/phoneStateStore';

export const useNavigationStore = create<NavigationState>((set, get) => ({
  currentApp: null,
  currentView: 'home',
  currentTab: undefined,
  history: [],

  openApp: (appPath: string, data = {}, options = {}) => {
    if (!isValidApp(appPath)) {
      console.error(`Navigation error: App "${appPath}" does not exist or is not installed`);
      return false;
    }

    // Load app-specific data when opening an app
    // This ensures data is loaded only when needed
    console.log(`[Navigation] Loading data for app: ${appPath}`);
    usePhoneStore.getState().actions.loadAppData(appPath);

    set(state => {
      // Create new history entry
      const newEntry = {
        app: appPath,
        view: 'main', // Default view
        data,
        mode: options.mode
      };

      // Handle clearing history if requested
      const newHistory = options.clearHistory ? [newEntry] : [...state.history, newEntry];

      return {
        currentApp: appPath,
        currentView: 'main',
        currentTab: undefined, // Reset tab when changing apps
        history: newHistory
      };
    });

    return true;
  },

  openView: (view: string, data = {}, options = {}) => {
    const { currentApp } = get();

    // Validate app and view
    if (!currentApp) {
      // Silently return false without error when there's no active app
      // This prevents errors when components try to open views after returning to home
      return false;
    }

    if (!isValidView(currentApp, view)) {
      console.error(`Navigation error: View "${view}" does not exist in app "${currentApp}"`);
      return false;
    }

    set(state => {
      // Create new history entry
      const newEntry = {
        app: currentApp,
        view,
        tab: options.tab,
        data
      };

      // Handle replacing current view if requested
      const newHistory = [...state.history];
      if (options.replace && newHistory.length > 0) {
        newHistory.pop(); // Remove current view
      }
      newHistory.push(newEntry);

      return {
        currentView: view,
        currentTab: options.tab,
        history: newHistory
      };
    });

    return true;
  },

  openAppView: (appPath, view, data = {}, options = {}) => {
    // Validate app exists and is installed
    if (!isValidApp(appPath)) {
      console.error(`Navigation error: App "${appPath}" does not exist or is not installed`);
      return false;
    }

    // Validate view exists
    if (!isValidView(appPath, view)) {
      console.error(`Navigation error: View "${view}" does not exist in app "${appPath}"`);
      return false;
    }

    // Load app-specific data when opening an app view
    // This ensures data is loaded only when needed
    console.log(`[Navigation] Loading data for app: ${appPath}`);
    usePhoneStore.getState().actions.loadAppData(appPath);

    set(state => {
      // Create new history entry
      const newEntry = {
        app: appPath,
        view,
        tab: options.tab,
        data,
        mode: options.mode
      };

      // Handle clearing history if requested
      const newHistory = options.clearHistory ? [] : [...state.history];

      // Handle replacing current view if requested
      if (options.replace && newHistory.length > 0) {
        newHistory.pop(); // Remove current view
      }

      newHistory.push(newEntry);

      return {
        currentApp: appPath,
        currentView: view,
        currentTab: options.tab,
        history: newHistory
      };
    });

    return true;
  },

  switchTab: (tab, data = {}) => {
    const { currentApp, currentView } = get();

    // Validate app and tab
    if (!currentApp) {
      // Silently return false without error when there's no active app
      // This prevents errors when components try to switch tabs after returning to home
      return false;
    }

    if (!isValidTab(currentApp, currentView, tab)) {
      console.error(
        `Navigation error: Tab "${tab}" does not exist in view "${currentView}" of app "${currentApp}"`
      );
      return false;
    }

    set(state => ({
      currentTab: tab,
      history: state.history.map((entry, index) =>
        index === state.history.length - 1
          ? { ...entry, tab, data: { ...entry.data, ...data } }
          : entry
      )
    }));

    return true;
  },

  goBack: () => {
    set(state => {
      if (state.history.length <= 1) {
        return state;
      }
      const newHistory = [...state.history];
      newHistory.pop();
      const prevEntry = newHistory[newHistory.length - 1];
      return {
        currentApp: prevEntry.app,
        currentView: prevEntry.view,
        currentTab: prevEntry.tab,
        history: newHistory
      };
    });
  },

  goHome: () => {
    // Reset navigation state to home screen
    // This intentionally sets currentApp to null, which means we're not in any app
    // Components should check if currentApp exists before trying to use app-specific navigation
    set({
      currentApp: null,
      currentView: 'home',
      currentTab: undefined,
      history: []
    });
  },
  openAppAndReturn: (
    fromApp: string,
    toApp: string,
    data = {},
    mode?: string,
    onReturn?: (result: unknown) => void
  ) => {
    console.log('[Navigation] openAppAndReturn called:', {
      fromApp,
      toApp,
      hasCallback: !!onReturn
    });

    if (!isValidApp(fromApp)) {
      console.error(`Navigation error: Source app "${fromApp}" does not exist or is not installed`);
      return false;
    }
    if (!isValidApp(toApp)) {
      console.error(`Navigation error: Target app "${toApp}" does not exist or is not installed`);
      return false;
    }

    const callbackId = `${fromApp}_${toApp}_${Date.now()}`;
    console.log('[Navigation] Generated callbackId:', callbackId);

    if (onReturn) {
      console.log('[Navigation] Registering callback for ID:', callbackId);
    } else {
      console.log('[Navigation] No callback provided, using selection store callback');
    }

    return get().openApp(
      toApp,
      {
        ...data,
        _returnTo: fromApp,
        _callbackId: callbackId
      },
      { mode }
    );
  },

  goBackWithResult: result => {
    console.log(
      '[Navigation] goBackWithResult called with result:',
      Array.isArray(result) ? `Array of ${result.length} items` : result
    );

    const state = get();
    const currentEntry = state.history[state.history.length - 1];

    console.log('[Navigation] Current entry:', currentEntry);

    // Check if we have return information
    if (!currentEntry?.data?._returnTo || !currentEntry?.data?._callbackId) {
      console.log('[Navigation] No return info found, doing normal back');
      // Just do a normal back if no return info
      get().goBack();
      return;
    }

    const { _returnTo, _callbackId } = currentEntry.data;
    console.log('[Navigation] Return info found:', {
      returnTo: _returnTo,
      callbackId: _callbackId
    });

    // Go back
    get().goBack();
  },

  openDeepLink: link => {
    // Parse deep link
    const parsedLink = parseDeepLink(link);

    if (!parsedLink) {
      console.error(`Navigation error: Invalid deep link format "${link}"`);
      return false;
    }

    const { appPath, view, data } = parsedLink;

    // Open app and view directly
    if (view) {
      return get().openAppView(appPath, view, data);
    } else {
      return get().openApp(appPath, data);
    }
  },

  // Persistence methods
  saveNavigationState: () => {
    const state = get();
    const stateData: NavigationStateData = {
      currentApp: state.currentApp,
      currentView: state.currentView,
      currentTab: state.currentTab,
      history: state.history
    };

    // Save to localStorage for browser mode
    try {
      localStorage.setItem('phone_navigation_state', JSON.stringify(stateData));
    } catch (error) {
      console.error('[Navigation] Error saving state to localStorage:', error);
    }

    // Send to client for FiveM mode
    try {
      clientRequests.send(
        'phone',
        'saveNavigationState',
        stateData as unknown as Record<string, unknown>
      );
    } catch (error) {
      console.error('[Navigation] Error sending state to client:', error);
    }

    console.log('[Navigation] State saved:', stateData);
    return stateData;
  },

  restoreNavigationState: stateData => {
    if (!stateData) return;

    console.log('[Navigation] Restoring state:', stateData);

    // Validate the state data
    if (stateData.currentApp && !isValidApp(stateData.currentApp)) {
      console.error(
        `[Navigation] Cannot restore state: App "${stateData.currentApp}" is not valid`
      );
      return;
    }

    // Set the state
    set({
      currentApp: stateData.currentApp,
      currentView: stateData.currentView || 'home',
      currentTab: stateData.currentTab,
      history: stateData.history || []
    });

    console.log('[Navigation] State restored');
  }
}));
