/**
 * Consolidated mock notifications data for browser testing
 */
import { Notification } from '../../../notifications/types/notificationTypes';
import { contactsMockData, messagesMockData } from '../../../fivem/mockData';

// Using hardcoded values to avoid circular dependencies with appConfigurations
// These match the IDs in appConfigurations.ts
const CONTACTS_APP_ID = 1;
const SETTINGS_APP_ID = 3;
const MESSAGES_APP_ID = 5;
const YELLOW_PAGES_APP_ID = 6;
const DARK_MARKET_APP_ID = 7;
const GARAGE_APP_ID = 8;
const LIFESNAP_APP_ID = 9;
const BANKING_APP_ID = 10;

// App names and icons for notifications
const APP_NAMES = {
  [CONTACTS_APP_ID]: 'Contacts',
  [SETTINGS_APP_ID]: 'Settings',
  [MESSAGES_APP_ID]: 'Messages',
  [YELLOW_PAGES_APP_ID]: 'Yellow Pages',
  [DARK_MARKET_APP_ID]: 'Dark Market',
  [GARAGE_APP_ID]: 'Garage',
  [LIFESNAP_APP_ID]: 'LifeSnap',
  [BANKING_APP_ID]: 'Banking'
};

const APP_ICONS = {
  [CONTACTS_APP_ID]: 'user',
  [SETTINGS_APP_ID]: 'cogs',
  [MESSAGES_APP_ID]: 'envelope',
  [YELLOW_PAGES_APP_ID]: 'book',
  [DARK_MARKET_APP_ID]: 'mask',
  [GARAGE_APP_ID]: 'car',
  [LIFESNAP_APP_ID]: 'camera',
  [BANKING_APP_ID]: 'money-bill-wave'
};

// Helper function to get contact avatar or generate initials if not available
const getContactAvatarOrInitials = (phoneNumber: string): string => {
  // Find contact in mock data
  const contact = contactsMockData.contacts.find(c => c.number === phoneNumber);

  if (contact?.avatar) {
    return contact.avatar;
  }

  // If no contact or no avatar, generate initials
  const displayName = contact?.name || phoneNumber;
  const initials = displayName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);

  return initials;
};

/**
 * Generate a mock notification
 */
export const generateMockNotification = (
  appId: number,
  title: string,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error' | 'message' = 'info',
  metadata: any = {},
  deepLink?: {
    app: string;
    view: string;
    params?: Record<string, any>;
  }
): Notification => {
  return {
    id: Date.now() + Math.floor(Math.random() * 1000),
    appId,
    title,
    message,
    type,
    timestamp: Date.now(),
    read: false,
    movedToTopBar: true, // Changed to true so all notifications go to topbar
    metadata,
    deepLink
  };
};

/**
 * Create mock notifications for browser testing
 */
export const createMockNotifications = (): Notification[] => {
  // Create a timestamp for each notification to ensure proper ordering
  const now = Date.now();

  // Get conversations from mock data
  const johnDoeConversation = messagesMockData.conversations.find(c => c.id === 1);
  const janeSmithConversation = messagesMockData.conversations.find(c => c.id === 2);
  const friendsGroupConversation = messagesMockData.conversations.find(c => c.id === 3);

  // Get contacts from mock data
  const johnDoeContact = contactsMockData.contacts.find(c => c.number === '555-1234');
  const janeSmithContact = contactsMockData.contacts.find(c => c.number === '555-5678');
  const bobJohnsonContact = contactsMockData.contacts.find(c => c.number === '555-9012');

  return [
    // Message notification - Conversation 1 (John Doe)
    generateMockNotification(
      MESSAGES_APP_ID,
      johnDoeContact?.name || 'John Doe',
      'Hi! How are you doing?',
      'message',
      {
        sender: johnDoeContact?.name || 'John Doe',
        senderPhone: johnDoeContact?.number || '555-1234',
        conversationId: johnDoeConversation?.id || 1,
        messageType: 'text',
        avatar: johnDoeContact?.avatar || getContactAvatarOrInitials('555-1234'),
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: johnDoeConversation?.id || 1 }
      }
    ),

    // Another message from John Doe (for grouping test)
    generateMockNotification(
      MESSAGES_APP_ID,
      johnDoeContact?.name || 'John Doe',
      'Are you available for that job?',
      'message',
      {
        sender: johnDoeContact?.name || 'John Doe',
        senderPhone: johnDoeContact?.number || '555-1234',
        conversationId: johnDoeConversation?.id || 1,
        messageType: 'text',
        avatar: johnDoeContact?.avatar || getContactAvatarOrInitials('555-1234'),
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: johnDoeConversation?.id || 1 }
      }
    ),

    // Third message from John Doe (for grouping test)
    generateMockNotification(
      MESSAGES_APP_ID,
      johnDoeContact?.name || 'John Doe',
      'I need your help with something',
      'message',
      {
        sender: johnDoeContact?.name || 'John Doe',
        senderPhone: johnDoeContact?.number || '555-1234',
        conversationId: johnDoeConversation?.id || 1,
        messageType: 'text',
        avatar: johnDoeContact?.avatar || getContactAvatarOrInitials('555-1234'),
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: johnDoeConversation?.id || 1 }
      }
    ),

    // Message from Jane Smith (different conversation)
    generateMockNotification(
      MESSAGES_APP_ID,
      janeSmithContact?.name || 'Jane Smith',
      'Hey, got a minute?',
      'message',
      {
        sender: janeSmithContact?.name || 'Jane Smith',
        senderPhone: janeSmithContact?.number || '555-5678',
        conversationId: janeSmithConversation?.id || 2,
        messageType: 'text',
        avatar: janeSmithContact?.avatar || getContactAvatarOrInitials('555-5678'),
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: janeSmithConversation?.id || 2 }
      }
    ),

    // Message from Friends Group (for testing group conversations)
    generateMockNotification(
      MESSAGES_APP_ID,
      friendsGroupConversation?.name || 'Friends Group',
      'Let\'s meet tomorrow at 10am',
      'message',
      {
        sender: 'You',
        senderPhone: '555-0000',
        conversationId: friendsGroupConversation?.id || 3,
        messageType: 'text',
        isGroup: true,
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: friendsGroupConversation?.id || 3 }
      }
    ),

    // Another message from Friends Group (different sender)
    generateMockNotification(
      MESSAGES_APP_ID,
      friendsGroupConversation?.name || 'Friends Group',
      'I\'ll be there!',
      'message',
      {
        sender: bobJohnsonContact?.name || 'Bob Johnson',
        senderPhone: bobJohnsonContact?.number || '555-9012',
        conversationId: friendsGroupConversation?.id || 3,
        messageType: 'text',
        avatar: bobJohnsonContact?.avatar || getContactAvatarOrInitials('555-9012'),
        isGroup: true,
        isOnline: true
      },
      {
        app: 'messages',
        view: 'conversation',
        params: { id: friendsGroupConversation?.id || 3 }
      }
    ),

    // Banking notification
    generateMockNotification(
      BANKING_APP_ID,
      'Banking',
      'You received a payment of $1,500',
      'success',
      {
        amount: 1500,
        sender: janeSmithContact?.name || 'Jane Smith',
        accountNumber: '****1234',
        transactionId: 'tx_123456',
        timestamp: now - 120000 // 2 minutes ago
      },
      {
        app: 'banking',
        view: 'transactions',
        params: { id: 'tx_123456' }
      }
    ),

    // Another banking notification (for grouping test)
    generateMockNotification(
      BANKING_APP_ID,
      'Banking',
      'You received a payment of $750',
      'success',
      {
        amount: 750,
        sender: bobJohnsonContact?.name || 'Bob Johnson',
        accountNumber: '****1234',
        transactionId: 'tx_123457',
        timestamp: now - 60000 // 1 minute ago
      },
      {
        app: 'banking',
        view: 'transactions',
        params: { id: 'tx_123457' }
      }
    ),

    // Contacts notification
    generateMockNotification(
      CONTACTS_APP_ID,
      'Contacts',
      'Contact request from Mike Wilson',
      'info',
      {
        contactId: 4,
        contactName: 'Mike Wilson',
        contactNumber: '555-4321',
        avatar: contactsMockData.contacts.find(c => c.id === 4)?.avatar || getContactAvatarOrInitials('555-4321')
      },
      {
        app: 'contacts',
        view: 'detail',
        params: { id: 4 }
      }
    ),

    // Yellow Pages notification
    generateMockNotification(
      YELLOW_PAGES_APP_ID,
      'Yellow Pages',
      'New listing: "Mechanic services available 24/7"',
      'info',
      {
        listingId: 3,
        author: 'Tony Martinez',
        category: 'SERVICES',
        timestamp: now - 300000 // 5 minutes ago
      },
      {
        app: 'yellow-pages',
        view: 'detail',
        params: { id: 3 }
      }
    ),

    // LifeSnap notification
    generateMockNotification(
      LIFESNAP_APP_ID,
      'LifeSnap',
      'Your post received 15 likes',
      'info',
      {
        postId: 1,
        likes: 15,
        comments: 3,
        image: 'https://picsum.photos/500/500',
        timestamp: now - 600000 // 10 minutes ago
      },
      {
        app: 'life-snap',
        view: 'post',
        params: { id: 1 }
      }
    ),

    // Garage notification
    generateMockNotification(
      GARAGE_APP_ID,
      'Garage',
      'Your vehicle is ready for pickup',
      'info',
      {
        vehicleId: 101,
        vehicleName: 'Sultan RS',
        garage: 'Downtown',
        timestamp: now - 900000 // 15 minutes ago
      },
      {
        app: 'garage',
        view: 'detail',
        params: { id: 101 }
      }
    ),

    // Dark Market notification
    generateMockNotification(
      DARK_MARKET_APP_ID,
      'Dark Market',
      'New item available: Advanced Lockpick',
      'info',
      {
        itemId: 202,
        seller: 'ShadowDealer',
        price: 1200,
        timestamp: now - 1200000 // 20 minutes ago
      },
      {
        app: 'dark-market',
        view: 'detail',
        params: { id: 202 }
      }
    )
  ];
};

// Mock notifications data with proper metadata usage for FiveM phone apps
export const mockNotifications: Notification[] = [
  // First notification for topbar (movedToTopBar: true) - persistent
  {
    id: 1,
    appId: MESSAGES_APP_ID,
    title: APP_NAMES[MESSAGES_APP_ID],
    message: "Hey there! How's it going?",
    timestamp: Date.now(),
    read: false,
    movedToTopBar: true, // This will show in the topbar
    type: 'message',
    icon: APP_ICONS[MESSAGES_APP_ID],
    persistent: true, // This notification won't auto-dismiss
    metadata: {
      sender: 'John Doe',
      senderPhone: '555-1234',
      conversationId: 1,
      messageType: 'text',
      avatar: 'https://i.pravatar.cc/150?img=1',
      isOnline: true
    }
  },

  // Second notification for topbar (movedToTopBar: true) - with custom duration
  {
    id: 2,
    appId: YELLOW_PAGES_APP_ID,
    title: APP_NAMES[YELLOW_PAGES_APP_ID],
    message: 'Your ad is getting popular! 15 new views',
    timestamp: Date.now() - 7300000,
    read: false,
    movedToTopBar: true, // This will show in the topbar
    type: 'info',
    icon: APP_ICONS[YELLOW_PAGES_APP_ID],
    duration: 8000, // This notification will auto-dismiss after 8 seconds
    metadata: {
      adId: 101,
      title: 'Mechanic Services - 24/7',
      views: 15
    }
  },

  // Third notification for topbar (movedToTopBar: true)
  {
    id: 3,
    appId: SETTINGS_APP_ID,
    title: APP_NAMES[SETTINGS_APP_ID],
    message: 'System update available',
    timestamp: Date.now() - 3600000,
    read: false,
    movedToTopBar: true, // This will show in the topbar
    type: 'info',
    icon: APP_ICONS[SETTINGS_APP_ID],
    metadata: {
      version: '2.0.1',
      size: '15MB',
      releaseNotes: 'Bug fixes and performance improvements'
    }
  }
];

export default mockNotifications;