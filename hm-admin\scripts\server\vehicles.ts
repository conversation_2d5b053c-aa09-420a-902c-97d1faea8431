/// <reference types="@citizenfx/server" />

import { NotificationUtils } from "./utilities";


onNet('hm-admin:setVehiclePlate', (targetVehicle: number, plateText: string) => {
    const playerPed = PlayerPedId();
    

    if (!targetVehicle || targetVehicle === 0) {
        console.error('[VehicleManager] No vehicle found for the player.');
        return;
    }
    // Set the vehicle plate text using the FiveM native
    SetVehicleNumberPlateText(targetVehicle, plateText);
    NotificationUtils.show(`Vehicle plate changed to "${plateText}" successfully!`, 'success');
    console.log(`[VehicleManager] Vehicle plate changed to: ${plateText}`);
});

