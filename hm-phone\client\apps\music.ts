// /**
//  * Music App - Client Side
//  *
//  * This file handles client-side functionality for the Music app.
//  * The Music app uses a hybrid initialization strategy:
//  * - Player initialized on start for background playback
//  * - Library loaded on demand when app is opened
//  */

// import { registerAppHandler } from '../nui';

// // Track the current playback state
// let isPlaying = false;
// let currentTrack: any = null;
// let currentPlaylist: any = null;
// let volume = 0.5; // Default volume (0.0 to 1.0)
// let repeat = false;
// let shuffle = false;

// /**
//  * Initialize the music app
//  */
// export function initializeMusicApp(): void {
//     console.log('[Music] Initializing client-side music app');

//     // Register client events
//     registerClientEvents();

//     // Register NUI handlers
//     registerNUIHandlers();

//     // Initialize the music player
//     initializeMusicPlayer();
// }

// /**
//  * Register client events for the music app
//  */
// function registerClientEvents(): void {
//     // Register event for music library data
//     onNet('hm-phone:musicLibrary', (libraryData: any) => {
//         console.log('[Music] Music library data received:', libraryData);

//         // Send the library data to the UI
//         sendToUI('setLibrary', libraryData);
//     });

//     // Register event for playlists data
//     onNet('hm-phone:musicPlaylists', (playlistsData: any) => {
//         console.log('[Music] Music playlists data received:', playlistsData);

//         // Send the playlists data to the UI
//         sendToUI('setPlaylists', playlistsData);
//     });

//     // Register event for playback error
//     onNet('hm-phone:musicError', (errorMessage: string) => {
//         console.error('[Music] Music error:', errorMessage);

//         // Send the error to the UI
//         sendToUI('error', errorMessage);
//     });
// }

// /**
//  * Register NUI handlers for the music app
//  */
// function registerNUIHandlers(): void {
//     // Register handler for getting music library
//     registerAppHandler('music', 'getLibrary', async () => {
//         console.log('[Music] Received getLibrary request from UI');

//         try {
//             // Get player data
//             const identifier = global.playerIdentifier;
//             const citizenId = global.playerCitizenId;

//             console.log(`[Music] Getting music library for player: ${citizenId || identifier}`);

//             // Request music library from the server
//             emitNet('hm-phone:getMusicLibrary');

//             // Return success to the UI
//             // The actual library data will be sent via the hm-phone:musicLibrary event
//             return { success: true };
//         } catch (error) {
//             console.error('[Music] Error getting music library:', error);
//             return { success: false, error: 'Failed to get music library' };
//         }
//     });

//     // Register handler for getting playlists
//     registerAppHandler('music', 'getPlaylists', async () => {
//         console.log('[Music] Received getPlaylists request from UI');

//         try {
//             // Get player data
//             const identifier = global.playerIdentifier;
//             const citizenId = global.playerCitizenId;

//             console.log(`[Music] Getting playlists for player: ${citizenId || identifier}`);

//             // Request playlists from the server
//             emitNet('hm-phone:getMusicPlaylists');

//             // Return success to the UI
//             // The actual playlists data will be sent via the hm-phone:musicPlaylists event
//             return { success: true };
//         } catch (error) {
//             console.error('[Music] Error getting playlists:', error);
//             return { success: false, error: 'Failed to get playlists' };
//         }
//     });

//     // Register handler for creating a playlist
//     registerAppHandler('music', 'createPlaylist', async (data: any) => {
//         console.log('[Music] Received createPlaylist request from UI:', data);

//         try {
//             // Extract the playlist data
//             const { name, description } = data;

//             // Get player data
//             const identifier = global.playerIdentifier;
//             const citizenId = global.playerCitizenId;

//             console.log(`[Music] Creating playlist for player: ${citizenId || identifier}`);

//             // Send the playlist data to the server
//             emitNet('hm-phone:createMusicPlaylist', name, description);

//             // Return success to the UI
//             return { success: true };
//         } catch (error) {
//             console.error('[Music] Error creating playlist:', error);
//             return { success: false, error: 'Failed to create playlist' };
//         }
//     });

//     // Register handler for adding a track to a playlist
//     registerAppHandler('music', 'addToPlaylist', async (data: any) => {
//         console.log('[Music] Received addToPlaylist request from UI:', data);

//         try {
//             // Extract the data
//             const { playlistId, trackId } = data;

//             // Get player data
//             const identifier = global.playerIdentifier;
//             const citizenId = global.playerCitizenId;

//             console.log(`[Music] Adding track to playlist for player: ${citizenId || identifier}`);

//             // Send the data to the server
//             emitNet('hm-phone:addTrackToPlaylist', playlistId, trackId);

//             // Return success to the UI
//             return { success: true };
//         } catch (error) {
//             console.error('[Music] Error adding track to playlist:', error);
//             return { success: false, error: 'Failed to add track to playlist' };
//         }
//     });

//     // Register handler for removing a track from a playlist
//     registerAppHandler('music', 'removeFromPlaylist', async (data: any) => {
//         console.log('[Music] Received removeFromPlaylist request from UI:', data);

//         try {
//             // Extract the data
//             const { playlistId, trackId } = data;

//             // Get player data
//             const identifier = global.playerIdentifier;
//             const citizenId = global.playerCitizenId;

//             console.log(`[Music] Removing track from playlist for player: ${citizenId || identifier}`);

//             // Send the data to the server
//             emitNet('hm-phone:removeTrackFromPlaylist', playlistId, trackId);

//             // Return success to the UI
//             return { success: true };
//         } catch (error) {
//             console.error('[Music] Error removing track from playlist:', error);
//             return { success: false, error: 'Failed to remove track from playlist' };
//         }
//     });

//     // Register handler for playing a track
//     registerAppHandler('music', 'playTrack', async (data: any) => {
//         console.log('[Music] Received playTrack request from UI:', data);

//         try {
//             // Extract the track data
//             const { track, playlist } = data;

//             // Update current track and playlist
//             currentTrack = track;
//             currentPlaylist = playlist;

//             // Play the track
//             playTrack(track);

//             // Return success to the UI
//             return { success: true, isPlaying: true };
//         } catch (error) {
//             console.error('[Music] Error playing track:', error);
//             return { success: false, error: 'Failed to play track' };
//         }
//     });

//     // Register handler for pausing playback
//     registerAppHandler('music', 'pausePlayback', async () => {
//         console.log('[Music] Received pausePlayback request from UI');

//         try {
//             // Pause the playback
//             pausePlayback();

//             // Return success to the UI
//             return { success: true, isPlaying: false };
//         } catch (error) {
//             console.error('[Music] Error pausing playback:', error);
//             return { success: false, error: 'Failed to pause playback' };
//         }
//     });

//     // Register handler for resuming playback
//     registerAppHandler('music', 'resumePlayback', async () => {
//         console.log('[Music] Received resumePlayback request from UI');

//         try {
//             // Resume the playback
//             resumePlayback();

//             // Return success to the UI
//             return { success: true, isPlaying: true };
//         } catch (error) {
//             console.error('[Music] Error resuming playback:', error);
//             return { success: false, error: 'Failed to resume playback' };
//         }
//     });

//     // Register handler for skipping to the next track
//     registerAppHandler('music', 'nextTrack', async () => {
//         console.log('[Music] Received nextTrack request from UI');

//         try {
//             // Skip to the next track
//             playNextTrack();

//             // Return success to the UI
//             return { success: true, currentTrack, isPlaying: true };
//         } catch (error) {
//             console.error('[Music] Error skipping to next track:', error);
//             return { success: false, error: 'Failed to skip to next track' };
//         }
//     });

//     // Register handler for skipping to the previous track
//     registerAppHandler('music', 'previousTrack', async () => {
//         console.log('[Music] Received previousTrack request from UI');

//         try {
//             // Skip to the previous track
//             playPreviousTrack();

//             // Return success to the UI
//             return { success: true, currentTrack, isPlaying: true };
//         } catch (error) {
//             console.error('[Music] Error skipping to previous track:', error);
//             return { success: false, error: 'Failed to skip to previous track' };
//         }
//     });

//     // Register handler for setting volume
//     registerAppHandler('music', 'setVolume', async (data: any) => {
//         console.log('[Music] Received setVolume request from UI:', data);

//         try {
//             // Extract the volume data
//             const { volume: newVolume } = data;

//             // Set the volume
//             setVolume(newVolume);

//             // Return success to the UI
//             return { success: true, volume };
//         } catch (error) {
//             console.error('[Music] Error setting volume:', error);
//             return { success: false, error: 'Failed to set volume' };
//         }
//     });

//     // Register handler for toggling repeat
//     registerAppHandler('music', 'toggleRepeat', async () => {
//         console.log('[Music] Received toggleRepeat request from UI');

//         try {
//             // Toggle repeat
//             repeat = !repeat;

//             // Return success to the UI
//             return { success: true, repeat };
//         } catch (error) {
//             console.error('[Music] Error toggling repeat:', error);
//             return { success: false, error: 'Failed to toggle repeat' };
//         }
//     });

//     // Register handler for toggling shuffle
//     registerAppHandler('music', 'toggleShuffle', async () => {
//         console.log('[Music] Received toggleShuffle request from UI');

//         try {
//             // Toggle shuffle
//             shuffle = !shuffle;

//             // Return success to the UI
//             return { success: true, shuffle };
//         } catch (error) {
//             console.error('[Music] Error toggling shuffle:', error);
//             return { success: false, error: 'Failed to toggle shuffle' };
//         }
//     });

//     // Register handler for getting playback state
//     registerAppHandler('music', 'getPlaybackState', async () => {
//         console.log('[Music] Received getPlaybackState request from UI');

//         try {
//             // Return the current playback state
//             return {
//                 success: true,
//                 isPlaying,
//                 currentTrack,
//                 currentPlaylist,
//                 volume,
//                 repeat,
//                 shuffle
//             };
//         } catch (error) {
//             console.error('[Music] Error getting playback state:', error);
//             return { success: false, error: 'Failed to get playback state' };
//         }
//     });
// }

// /**
//  * Initialize the music player
//  */
// function initializeMusicPlayer(): void {
//     console.log('[Music] Initializing music player');

//     // Set up event listeners for media keys
//     if (IsDuplicityVersion()) {
//         // Server-side code
//         return;
//     }

//     // Client-side code
//     // Register key mappings for media controls
//     RegisterCommand(
//         '+music_play_pause',
//         () => {
//             if (isPlaying) {
//                 pausePlayback();
//             } else {
//                 resumePlayback();
//             }
//         },
//         false
//     );

//     RegisterCommand(
//         '+music_next',
//         () => {
//             playNextTrack();
//         },
//         false
//     );

//     RegisterCommand(
//         '+music_previous',
//         () => {
//             playPreviousTrack();
//         },
//         false
//     );

//     // Register key bindings
//     RegisterKeyMapping('+music_play_pause', 'Play/Pause Music', 'keyboard', 'k');
//     RegisterKeyMapping('+music_next', 'Next Track', 'keyboard', 'l');
//     RegisterKeyMapping('+music_previous', 'Previous Track', 'keyboard', 'j');
// }

// /**
//  * Play a track
//  * @param track Track to play
//  */
// function playTrack(track: any): void {
//     if (!track) return;

//     console.log('[Music] Playing track:', track.title);

//     // Update state
//     isPlaying = true;
//     currentTrack = track;

//     // Send playback state to UI
//     sendToUI('playbackState', {
//         isPlaying,
//         currentTrack,
//         currentPlaylist,
//         volume,
//         repeat,
//         shuffle
//     });

//     // Play the track using the native audio API
//     if (track.stream_url) {
//         // Stop any currently playing audio
//         CancelMusicEvent();

//         // Prepare the audio
//         PrepareMusicEvent(track.stream_url);

//         // Set the volume
//         SetMusicVolume(volume);

//         // Start the audio
//         TriggerMusicEvent();

//         // Show notification
//         SendNUIMessage({
//             app: 'notification',
//             type: 'add',
//             data: {
//                 id: `music_${Date.now()}`,
//                 title: 'Now Playing',
//                 content: `${track.title} - ${track.artist}`,
//                 icon: 'music',
//                 timeout: 3000
//             }
//         });
//     }
// }

// /**
//  * Pause playback
//  */
// function pausePlayback(): void {
//     if (!isPlaying) return;

//     console.log('[Music] Pausing playback');

//     // Update state
//     isPlaying = false;

//     // Send playback state to UI
//     sendToUI('playbackState', {
//         isPlaying,
//         currentTrack,
//         currentPlaylist,
//         volume,
//         repeat,
//         shuffle
//     });

//     // Pause the audio
//     PauseMusicEvent();
// }

// /**
//  * Resume playback
//  */
// function resumePlayback(): void {
//     if (isPlaying || !currentTrack) return;

//     console.log('[Music] Resuming playback');

//     // Update state
//     isPlaying = true;

//     // Send playback state to UI
//     sendToUI('playbackState', {
//         isPlaying,
//         currentTrack,
//         currentPlaylist,
//         volume,
//         repeat,
//         shuffle
//     });

//     // Resume the audio
//     ResumeMusicEvent();
// }

// /**
//  * Play the next track
//  */
// function playNextTrack(): void {
//     if (!currentPlaylist || !currentTrack) return;

//     console.log('[Music] Playing next track');

//     // Find the current track index
//     const tracks = currentPlaylist.tracks || [];
//     const currentIndex = tracks.findIndex((t: any) => t.id === currentTrack.id);

//     // Get the next track
//     let nextIndex = currentIndex + 1;
//     if (nextIndex >= tracks.length) {
//         if (repeat) {
//             // If repeat is enabled, go back to the first track
//             nextIndex = 0;
//         } else {
//             // Otherwise, stop playback
//             pausePlayback();
//             return;
//         }
//     }

//     // Play the next track
//     playTrack(tracks[nextIndex]);
// }

// /**
//  * Play the previous track
//  */
// function playPreviousTrack(): void {
//     if (!currentPlaylist || !currentTrack) return;

//     console.log('[Music] Playing previous track');

//     // Find the current track index
//     const tracks = currentPlaylist.tracks || [];
//     const currentIndex = tracks.findIndex((t: any) => t.id === currentTrack.id);

//     // Get the previous track
//     let prevIndex = currentIndex - 1;
//     if (prevIndex < 0) {
//         if (repeat) {
//             // If repeat is enabled, go to the last track
//             prevIndex = tracks.length - 1;
//         } else {
//             // Otherwise, go to the beginning of the current track
//             // For now, we'll just restart the current track
//             playTrack(tracks[currentIndex]);
//             return;
//         }
//     }

//     // Play the previous track
//     playTrack(tracks[prevIndex]);
// }

// /**
//  * Set the volume
//  * @param newVolume Volume to set (0.0 to 1.0)
//  */
// function setVolume(newVolume: number): void {
//     // Clamp volume between 0 and 1
//     volume = Math.max(0, Math.min(1, newVolume));

//     console.log(`[Music] Setting volume to ${volume}`);

//     // Set the volume
//     SetMusicVolume(volume);

//     // Send playback state to UI
//     sendToUI('playbackState', {
//         isPlaying,
//         currentTrack,
//         currentPlaylist,
//         volume,
//         repeat,
//         shuffle
//     });
// }

// /**
//  * Send data to the UI
//  * @param type Event type
//  * @param data Event data
//  */
// function sendToUI(type: string, data: any): void {
//     SendNUIMessage({
//         app: 'music',
//         type,
//         data
//     });
// }
