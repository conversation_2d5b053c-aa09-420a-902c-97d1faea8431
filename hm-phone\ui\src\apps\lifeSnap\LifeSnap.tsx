import React from 'react';
import { useLifeSnapStore } from './stores/lifeSnapStore';
import { useNavigation } from '../../navigation/hooks';
// Custom hooks for navigation
import useStoryNavigation from './hooks/useStoryNavigation';
import usePostNavigation from './hooks/usePostNavigation';
// Components
import Stories from './components/Stories';
import Feed from './components/Feed';
import CreatePost from './components/CreatePost';
import Profile from './components/Profile';
import StoryViewer from './components/StoryViewer';
import { useNavigationStore } from '../../navigation/navigationStore';

const LifeSnap: React.FC = () => {
  const store = useLifeSnapStore();
  const { openView } = useNavigation();
  const { currentView, currentApp, history } = useNavigationStore();
  // Get the current navigation entry and its data
  const currentEntry = history.length > 0 ? history[history.length - 1] : null;
  const navigationData = currentEntry?.data || {};

  // Extract navigation parameters
  const selectedStoryId = navigationData.storyId ? Number(navigationData.storyId) : null;
  const isPostDetailView = navigationData.postDetail === true;

  // For debugging
  console.log('[LifeSnap] Current navigation state:', {
    currentApp,
    currentView,
    navigationData,
    selectedStoryId,
    isPostDetailView
  });

  // LifeSnap data is now loaded during phone initialization
  // No need to load it again when the component mounts

  // Use custom hooks for navigation
  const { handleStoryNavigation, handleStoryClose, handleStoryClick } = useStoryNavigation({
    selectedStoryId
  });

  const { handlePostDetailView } = usePostNavigation();

  if (store.loading) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center bg-gray-900 pt-8 pb-3">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col bg-gray-900 pt-8 pb-3">
      {/* Main Content Area */}
      <div className="flex-1 scrollable">
        {/* Stories are only shown in the main view */}
        {currentView === 'main' && <Stories onStoryClick={handleStoryClick} />}

        <div className="flex-1">
          {/* Main feed view */}
          {currentView === 'main' && <Feed onPostDetailView={handlePostDetailView} />}

          {/* Create post view */}
          {currentView === 'create' && <CreatePost onClose={() => openView('main')} />}

          {/* Profile view */}
          {currentView === 'profile' && <Profile />}

          {/* Post detail view - using the isPostDetailView flag for backward compatibility */}
          {(currentView === 'post' || isPostDetailView) && (
            <Feed
              onPostDetailView={handlePostDetailView}
              isDetailView={true}
              postId={navigationData.postId ? Number(navigationData.postId) : undefined}
            />
          )}
        </div>
      </div>

      {/* Story Viewer - only shown in story view or when selectedStoryId is present */}
      {(currentView === 'story' || selectedStoryId !== null) && (
        <StoryViewer
          storyId={selectedStoryId}
          onClose={handleStoryClose}
          onNext={() => handleStoryNavigation('next')}
          onPrevious={() => handleStoryNavigation('previous')}
        />
      )}

      {/* Bottom Navigation */}
      <div className="flex items-center justify-around py-1 px-2 border-t border-white/10 bg-black/90 backdrop-blur-lg">
        <button
          onClick={() => openView('main')}
          className={`text-sm p-1 ${
            currentView === 'main' || !currentView ? 'text-white' : 'text-white/60'
          }`}
        >
          <i className="fas fa-home"></i>
        </button>
        <button
          onClick={() => openView('create')}
          className={`text-sm p-1 ${currentView === 'create' ? 'text-white' : 'text-white/60'}`}
        >
          <i className="fas fa-plus-square"></i>
        </button>
        <button
          onClick={() => openView('profile')}
          className={`text-sm p-1 ${currentView === 'profile' ? 'text-white' : 'text-white/60'}`}
        >
          <i className="fas fa-user"></i>
        </button>
      </div>
    </div>
  );
};

export default LifeSnap;
