/**
 * Type definitions for the Music app
 */

// Basic data types
export interface Artist {
  id: number;
  name: string;
  imageUrl?: string;
  bio?: string;
  isUserProfile?: boolean; // Flag to identify the user's profile artist
  isActive?: boolean; // Whether the artist is actively publishing music
  followers?: number;
  following?: number;
  playlists?: number;
  identifier?: string; // Player's identifier for the user profile artist
}

export interface Album {
  id: number;
  title: string;
  artistId: number;
  artist?: Artist; // For joined data
  imageUrl: string;
}

export interface Song {
  id: number;
  title: string;
  albumId: number;
  album?: Album; // For joined data
  artistId: number;
  artist?: Artist; // For joined data
  duration: number; // in seconds
  imageUrl: string;
}

// Player state interfaces
export interface PlayerState {
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isRepeatEnabled: boolean;
  isShuffleEnabled: boolean;
}

// Music store interface
export interface MusicStore extends PlayerState {
  // Data state
  songs: Song[];
  albums: Album[];
  artists: Artist[];
  favorites: number[]; // Song IDs
  queue: Song[];
  history: Song[];

  // UI state
  loading: boolean;
  currentArtistId: number | null;

  // Actions - methods to fetch data from server
  actions: {
    getSongs: () => Promise<void>; // This loads all music data (songs, albums, artists, user profile, favorites)
    getFavorites: () => Promise<void>; // Keep this for individual favorites updates

    toggleFavorite: (songId: number) => Promise<void>;
    updateUserProfile: (profileData: Partial<Artist>) => Promise<void>;
  };

  // Handlers - methods to update state based on server responses
  handlers: {
    onSetSongs: (songs: Song[]) => void;
    onSetAlbums: (albums: Album[]) => void;
    onSetArtists: (artists: Artist[]) => void;
    onSetFavorites: (favorites: number[]) => void;
    onSetUserProfile: (artist: Artist) => void;
    onClearSongData: () => void; // Added for cleanup
  };

  // Player controls
  setCurrentSong: (song: Song) => void;
  togglePlay: () => void;
  nextSong: () => void;
  previousSong: () => void;
  seekTo: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  toggleRepeat: () => void;
  toggleShuffle: () => void;

  // Queue management
  addToQueue: (song: Song) => void;
  removeFromQueue: (songId: number) => void;
  clearQueue: () => void;
  clearCurrentSong: () => void;

  // Helper methods
  getSongsByAlbum: (albumId: number) => Song[];
  getSongsByArtist: (artistId: number) => Song[];
  isFavorite: (songId: number) => boolean;

  // Artist navigation
  setCurrentArtistId: (artistId: number | null) => void;
  getCurrentArtist: () => Artist | undefined;

  // User profile methods
  getUserProfileArtist: (identifier?: string) => Artist | undefined;
  getUserProfileAlbums: () => Album[];
  getUserProfileSongs: () => Song[];
}

// Component props interfaces
export interface MiniPlayerProps {
  onOpen: () => void;
}

export interface SearchBarProps {
  onSearch: (query: string) => void;
}

export interface AlbumCardProps {
  album: Album;
  onClick: () => void;
}

export interface SongCardProps {
  song: {
    id: number;
    title: string;
    artist?: { id: number; name: string } | null;
    artistName?: string;
    imageUrl: string;
    duration?: string;
    plays?: string;
  };
  index?: number;
  showIndex?: boolean;
  showDuration?: boolean;
  showPlays?: boolean;
  compact?: boolean;
  onClick?: () => void;
}
