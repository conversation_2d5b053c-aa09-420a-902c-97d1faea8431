// Add FiveM-specific types
declare global {
  interface Window {
    invokeNative?: (eventName: string, data: string) => void;
  }
}

export interface GameViewOptions {
  cropVertical?: number;   // Percentage to crop from both top and bottom (0-0.5)
  cropHorizontal?: number; // Percentage to crop from both left and right (0-0.5)
  enableCameraControls?: boolean;
}

export class GameView {
  private vertexShaderSrc: string;
  private fragmentShaderSrc: string;
  private canvas: HTMLCanvasElement | null = null;
  private gl: WebGLRenderingContext | null = null;
  private program: WebGLProgram | null = null;
  private vertexBuff: WebGLBuffer | null = null;
  private texBuff: WebGLBuffer | null = null;
  private tex: WebGLTexture | null = null;
  private animationFrameId: number | null = null;
  private options: GameViewOptions = {
    cropVertical: 0.05,   // 5% crop from both top and bottom by default
    cropHorizontal: 0.0,  // 0% crop from left and right by default
    enableCameraControls: true // Enable camera controls by default
  };

  constructor(options?: GameViewOptions) {
    // Apply options if provided
    if (options) {
      this.options = { ...this.options, ...options };
    }

    this.vertexShaderSrc = `
        attribute vec2 a_position;
        attribute vec2 a_texcoord;
        uniform mat3 u_matrix;
        varying vec2 textureCoordinate;
        void main() {
            gl_Position = vec4(a_position, 0.0, 1.0);
            textureCoordinate = a_texcoord;
        }
    `;

    this.fragmentShaderSrc = `
        varying highp vec2 textureCoordinate;
        uniform sampler2D external_texture;
        void main()
        {
        gl_FragColor = texture2D(external_texture, textureCoordinate);
        }
    `;

    // Removed interval property as it's not used
  }

  makeShader = (gl: WebGLRenderingContext, type: number, src: string): WebGLShader => {
      const shader = gl.createShader(type);
      gl.shaderSource(shader!, src);
      gl.compileShader(shader!);
      return shader!;
  }

  createTexture(gl: WebGLRenderingContext): WebGLTexture {
      this.tex = gl.createTexture(); // Store texture

      const texPixels = new Uint8Array([0, 0, 255, 255]);

      gl.bindTexture(gl.TEXTURE_2D, this.tex!);
      gl.texImage2D(
          gl.TEXTURE_2D,
          0,
          gl.RGBA,
          1,
          1,
          0,
          gl.RGBA,
          gl.UNSIGNED_BYTE,
          texPixels,
      );

      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);

      // Magic hook sequence
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.MIRRORED_REPEAT);
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.REPEAT);

      // Reset
      gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

      return this.tex!;
  }

  createBuffers = (gl: WebGLRenderingContext) => {
      // Standard vertex positions for a full-screen quad
      this.vertexBuff = gl.createBuffer(); // Store vertex buffer
      gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuff!);
      gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]),
          gl.STATIC_DRAW,
      );

      // Apply the crop to texture coordinates
      const cropVertical = this.options.cropVertical || 0;
      const cropHorizontal = this.options.cropHorizontal || 0;

      // Ensure crop values are within valid range (0-0.5)
      const safeVerticalCrop = Math.min(0.5, Math.max(0, cropVertical));
      const safeHorizontalCrop = Math.min(0.5, Math.max(0, cropHorizontal));

      // Adjust texture coordinates to apply cropping
      // texCoord values: (0,0) = bottom left, (1,1) = top right
      this.texBuff = gl.createBuffer(); // Store texture buffer
      gl.bindBuffer(gl.ARRAY_BUFFER, this.texBuff!);
      gl.bufferData(
          gl.ARRAY_BUFFER,
          new Float32Array([
              safeHorizontalCrop, safeVerticalCrop,                 // bottom left
              1 - safeHorizontalCrop, safeVerticalCrop,             // bottom right
              safeHorizontalCrop, 1 - safeVerticalCrop,             // top left
              1 - safeHorizontalCrop, 1 - safeVerticalCrop          // top right
          ]),
          gl.STATIC_DRAW,
      );

      return { vertexBuff: this.vertexBuff, texBuff: this.texBuff };
  }

  createProgram = (gl: WebGLRenderingContext) => {
      const vertexShader = this.makeShader(gl, gl.VERTEX_SHADER, this.vertexShaderSrc);
      const fragmentShader = this.makeShader(gl, gl.FRAGMENT_SHADER, this.fragmentShaderSrc);

      this.program = gl.createProgram(); // Store program

      gl.attachShader(this.program!, vertexShader);
      gl.attachShader(this.program!, fragmentShader);
      gl.linkProgram(this.program!);
      gl.useProgram(this.program!);

      const vloc = gl.getAttribLocation(this.program!, 'a_position');
      const tloc = gl.getAttribLocation(this.program!, 'a_texcoord');

      // Clean up shaders after linking
      gl.detachShader(this.program!, vertexShader);
      gl.detachShader(this.program!, fragmentShader);
      gl.deleteShader(vertexShader);
      gl.deleteShader(fragmentShader);

      return { program: this.program, vloc, tloc };
  }

  createStuff(gl: WebGLRenderingContext): void {
      this.createTexture(gl);
      const { vloc, tloc } = this.createProgram(gl);
      this.createBuffers(gl);

      gl.useProgram(this.program!);

      gl.bindTexture(gl.TEXTURE_2D, this.tex!);

      gl.uniform1i(gl.getUniformLocation(this.program!, 'external_texture')!, 0);

      gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuff!);
      gl.vertexAttribPointer(vloc, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(vloc);

      gl.bindBuffer(gl.ARRAY_BUFFER, this.texBuff!);
      gl.vertexAttribPointer(tloc, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(tloc);

      gl.viewport(0, 0, gl.canvas!.width, gl.canvas!.height);
  }

  render(): void { // Removed gl and gameView parameters as they are class members or not needed here
      if (!this.gl) return;
      this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
      // Removed gl.finish() for potential performance improvement
      // Removed redundant requestAnimationFrame call
  }

  createGameView = (canvas: HTMLCanvasElement, options?: GameViewOptions) => {
      if (options) {
          this.options = { ...this.options, ...options };
      }
      this.canvas = canvas;
      this.gl = this.canvas.getContext('webgl', {
          antialias: false,
          depth: false,
          stencil: false,
          alpha: false,
          desynchronized: true,
          failIfMajorPerformanceCaveat: false,
      });

      if (!this.gl) {
          console.error('[GameView] Unable to initialize WebGL. Your browser may not support it.');
          return null;
      }

      const gameView = {
          canvas,
          gl: this.gl,
          // animationFrame is now a class property (this.animationFrameId)
          resize: (width: number, height: number) => {
              if (!this.gl) return;
              this.gl.viewport(0, 0, width, height);
              this.gl.canvas.width = width;
              this.gl.canvas.height = height;
          },
      };

      this.createStuff(this.gl);

      if (this.options.enableCameraControls) {
          this.enableCameraControls();
      }

      const renderLoop = () => {
          this.render();
          this.animationFrameId = requestAnimationFrame(renderLoop);
      };
      renderLoop(); // Start the main render loop

      return gameView;
  }

  /**
   * Enables first-person camera and looking controls
   * This allows the player to look around while using the camera
   */
  private enableCameraControls(): void {
      if (!window.invokeNative) return; // Only run in FiveM environment

      try {
          // Enable camera controls
          window.invokeNative('sendNUIMessage', JSON.stringify({
              type: 'enableCameraControls',
              data: {
                  enabled: true
              }
          }));

          console.log('[GameView] First-person camera and looking controls enabled');
      } catch (error) {
          console.error('[GameView] Error enabling camera controls:', error);
      }
  }

  stop(): void {
      // Cancel the animation frame
      if (this.animationFrameId !== null) {
          cancelAnimationFrame(this.animationFrameId);
          this.animationFrameId = null;
      }

      // Disable camera controls if they were enabled
      if (this.options.enableCameraControls) {
          this.disableCameraControls();
      }

      // Explicitly clean up WebGL resources
      if (this.gl) {
          if (this.vertexBuff) this.gl.deleteBuffer(this.vertexBuff);
          if (this.texBuff) this.gl.deleteBuffer(this.texBuff);
          if (this.tex) this.gl.deleteTexture(this.tex);
          if (this.program) this.gl.deleteProgram(this.program);

          // Lose the WebGL context
          const loseContextExt = this.gl.getExtension('WEBGL_lose_context');
          if (loseContextExt) {
              loseContextExt.loseContext();
          }
      }

      // Hide the canvas
      if (this.canvas && this.canvas.style.display !== "none") {
          this.canvas.style.display = "none";
      }

      // Reset class properties
      this.gl = null;
      this.canvas = null;
      this.program = null;
      this.vertexBuff = null;
      this.texBuff = null;
      this.tex = null;
  }

  /**
   * Disables first-person camera and restores original camera view
   */
  private disableCameraControls(): void {
      if (!window.invokeNative) return; // Only run in FiveM environment

      try {
          // Disable camera controls
          window.invokeNative('sendNUIMessage', JSON.stringify({
              type: 'enableCameraControls',
              data: {
                  enabled: false
              }
          }));

          console.log('[GameView] First-person camera disabled and original view restored');
      } catch (error) {
          console.error('[GameView] Error disabling camera controls:', error);
      }
  }

  /**
   * Captures the current canvas as a screenshot (returns a data URL)
   */
  public captureScreenshot(type: string = 'image/png', quality?: number): string | null {
    if (!this.canvas) return null;
    try {
      return this.canvas.toDataURL(type, quality);
    } catch (e) {
      console.error('[GameView] Screenshot capture failed:', e);
      return null;
    }
  }

  /**
   * Starts recording the canvas using MediaRecorder (if supported)
   * Returns a controller with stop() and getBlob() methods
   */
  public startRecording(options?: MediaRecorderOptions) {
    if (!this.canvas) return null;
    const stream = (this.canvas as any).captureStream ? (this.canvas as any).captureStream() : null;
    if (!stream) {
      console.error('[GameView] Canvas captureStream not supported.');
      return null;
    }
    let recorder: MediaRecorder;
    try {
      recorder = new MediaRecorder(stream, options);
    } catch (e) {
      console.error('[GameView] MediaRecorder init failed:', e);
      return null;
    }
    const chunks: BlobPart[] = [];
    recorder.ondataavailable = (e: BlobEvent) => {
      if (e.data && e.data.size > 0) chunks.push(e.data);
    };
    recorder.start();
    // Optionally, notify UI that recording started
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('gameview-recording-started'));
    }
    return {
      stop: () => {
        recorder.stop();
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('gameview-recording-stopped'));
        }
      },
      getBlob: () => new Blob(chunks, { type: recorder.mimeType }),
      getUrl: () => URL.createObjectURL(new Blob(chunks, { type: recorder.mimeType }))
    };
  }
  /**
   * Stops the recording and returns a Promise that resolves with the video Blob
   */
  public async stopRecording(): Promise<Blob | null> {
    // Check if a recording controller exists
    if (!this.canvas) return null;
    // Find the recording controller (if any)
    // We'll assume the controller is stored on the canvas for now
    const controller = (this.canvas as any)._gameViewRecordingController;
    if (!controller || typeof controller.stop !== 'function' || typeof controller.getBlob !== 'function') {
      console.error('[GameView] No recording controller found on canvas.');
      return null;
    }
    return new Promise<Blob | null>((resolve, reject) => {
      let resolved = false;
      // Listen for the stopped event
      const onStopped = () => {
        try {
          const blob = controller.getBlob();
          resolved = true;
          resolve(blob);
        } catch (e) {
          reject(e);
        }
      };
      // Patch the controller to call onStopped after stop
      const origStop = controller.stop;
      controller.stop = () => {
        origStop();
        setTimeout(onStopped, 100); // Give MediaRecorder time to fire dataavailable
      };
      // Actually stop recording
      controller.stop();
      // Fallback in case onStopped is never called
      setTimeout(() => {
        if (!resolved) {
          try {
            const blob = controller.getBlob();
            resolve(blob);
          } catch (e) {
            reject(e);
          }
        }
      }, 1000);
    });
  }
}