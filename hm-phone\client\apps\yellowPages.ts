/**
 * Yellow Pages App - Client Side
 *
 * This file handles client-side functionality for the Yellow Pages app.
 * The Yellow Pages app is initialized on demand.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the Yellow Pages app
 */
export function initializeYellowPagesApp(): void {
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();
}

/**
 * Register client events for the Yellow Pages app
 */
function registerClientEvents(): void {
    // Register event for Yellow Pages ads data
    onNet('hm-phone:yellowPagesAds', (adsData: any) => {
        sendToUI('ads', adsData);
    });

    // Register event for ad created confirmation
    onNet('hm-phone:adCreated', (result: any) => {
        sendToUI('adCreated', result);
    });

    // Register event for ad updated confirmation
    onNet('hm-phone:adUpdated', (result: any) => {
        sendToUI('adUpdated', result);
    });

    // Register event for ad deleted confirmation
    onNet('hm-phone:adDeleted', (result: any) => {
        sendToUI('adDeleted', result);
    });

    // Register event for Yellow Pages error
    onNet('hm-phone:yellowPagesError', (errorMessage: string) => {
        sendToUI('error', errorMessage);
    });
}

/**
 * Register NUI handlers for the Yellow Pages app
 */
function registerNUIHandlers(): void {
    // Register handler for getting all ads
    registerAppHandler('yellowPages', 'getAllAds', async () => {
        console.log('[YellowPages] Received getAllAds request from UI');

        try {
            emitNet('hm-phone:getYellowPagesAds');
            // The actual ads will be sent via the hm-phone:yellowPagesAds event
            return { success: true };
        } catch (error) {
            console.error('[YellowPages] Error getting Yellow Pages ads:', error);
            return { success: false, error: 'Failed to get Yellow Pages ads' };
        }
    });

    // Register handler for creating an ad
    registerAppHandler('yellowPages', 'createAd', async (data: any) => {
        try {
            // Send the ad data to the server
            emitNet('hm-phone:createAd', data);

            // Return success to the UI
            // The actual result will be sent via the hm-phone:adCreated event
            return { success: true };
        } catch (error) {
            console.error('[YellowPages] Error creating ad:', error);
            return { success: false, error: 'Failed to create ad' };
        }
    });

    // Register handler for updating an ad
    registerAppHandler('yellowPages', 'updateAd', async (data: any) => {
        try {
            // Send the ad data to the server
            emitNet('hm-phone:updateAd', data);
            // The actual result will be sent via the hm-phone:adUpdated event
            return { success: true };
        } catch (error) {
            console.error('[YellowPages] Error updating ad:', error);
            return { success: false, error: 'Failed to update ad' };
        }
    });

    // Register handler for deleting an ad
    registerAppHandler('yellowPages', 'deleteAd', async (data: any) => {

        try {
            // Extract the ad ID
            const { id } = data;
            // Send the delete request to the server
            emitNet('hm-phone:deleteAd', id);

            // Return success to the UI
            // The actual result will be sent via the hm-phone:adDeleted event
            return { success: true };
        } catch (error) {
            console.error('[YellowPages] Error deleting ad:', error);
            return { success: false, error: 'Failed to delete ad' };
        }
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'yellowPages',
        type,
        data
    });
}
