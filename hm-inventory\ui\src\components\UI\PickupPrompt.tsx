import React from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';

const PickupPrompt: React.FC = () => {
  const pickupPrompt = useInventoryStore((state) => state.pickupPrompt);

  if (!pickupPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-1/4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-neutral-900/90 backdrop-blur-sm text-white px-6 py-3 rounded-lg shadow-xl border border-neutral-700">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">{pickupPrompt}</span>
        </div>
      </div>
    </div>
  );
};

export default PickupPrompt;
