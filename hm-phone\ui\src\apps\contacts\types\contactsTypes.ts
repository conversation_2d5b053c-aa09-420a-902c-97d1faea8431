// This file contains the types for the contacts app
import { Contact } from '@shared/types';

// For backward compatibility with existing code
export interface LegacyContact {
  id?: number;
  name: string;
  phone: string;
  favorite?: boolean;
  photo?: string;
}

export interface ContactShareRequest {
  contactId: number;
  contact: Contact;
  senderId: number;
  senderName: string;
}

export interface ContactShareStatus {
  success: boolean;
  contactId: number;
  targetPlayerId: number;
  status: 'sent' | 'accepted' | 'declined' | 'error';
}
