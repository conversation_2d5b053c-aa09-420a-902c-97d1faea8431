import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Call } from '../types/dialerTypes';
import { useContactsStore } from '../stores/contactsStore';

interface CompactCallHistoryProps {
  calls: Call[];
  onCallPress: (number: string, name?: string, photo?: string) => void;
  onContactPress?: (number: string, name?: string) => void;
}

const CompactCallHistory: React.FC<CompactCallHistoryProps> = ({
  calls,
  onCallPress,
  onContactPress
}) => {
  // Get contacts to display names and photos
  const { contacts } = useContactsStore();

  // State for active filter
  const [activeFilter, setActiveFilter] = useState<'all' | 'missed' | 'outgoing' | 'answered'>(
    'all'
  );

  // Filter calls based on active filter
  const filteredCalls =
    activeFilter === 'all' ? calls : calls.filter(call => call.status === activeFilter);

  // Sort calls by timestamp (newest first)
  const sortedCalls = [...filteredCalls].sort((a, b) => b.timestamp - a.timestamp);

  // Helper function to get contact info
  const getContactInfo = (phoneNumber: string) => {
    const contact = contacts.find(c => c.number === phoneNumber);
    return {
      name: contact?.name,
      photo: contact?.avatar
    };
  };

  // Format time
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Helper function to check if a timestamp is from today
  const isToday = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  // Helper function to check if a timestamp is from yesterday
  const isYesterday = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return date.toDateString() === yesterday.toDateString();
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    if (seconds === 0) return '';

    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;

    if (mins === 0) {
      return `${secs}s`;
    }

    return `${mins}m ${secs}s`;
  };

  // Get status icon and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'missed':
        return {
          icon: <i className="fas fa-phone-slash"></i>,
          color: 'text-red-500',
          bgColor: 'bg-red-500/20',
          label: 'Missed'
        };
      case 'outgoing':
        return {
          icon: <i className="fas fa-phone rotate-90"></i>,
          color: 'text-green-500',
          bgColor: 'bg-green-500/20',
          label: 'Outgoing'
        };
      case 'answered':
        return {
          icon: <i className="fas fa-phone"></i>,
          color: 'text-blue-500',
          bgColor: 'bg-blue-500/20',
          label: 'Incoming'
        };
      default:
        return {
          icon: <i className="fas fa-phone"></i>,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/20',
          label: 'Unknown'
        };
    }
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden h-full">
      {/* Filter tabs with consistent padding */}
      <div className="px-4 pt-2 pb-3 bg-[#0a0f1a] z-30 border-b border-white/10 shadow-md flex-shrink-0">
        <div className="flex justify-between items-center mb-2.5">
          <h2 className="text-white text-lg font-medium">Call History</h2>
          <button className="text-white/60 hover:text-white w-8 h-8 flex items-center justify-center rounded-full hover:bg-white/5">
            <i className="fas fa-ellipsis-v"></i>
          </button>
        </div>
        <div className="grid grid-cols-4 gap-1.5">
          {['all', 'missed', 'outgoing', 'answered'].map(filter => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter as 'all' | 'missed' | 'outgoing' | 'answered')}
              className={`px-1 py-1.5 rounded-full text-xs font-medium transition-colors ${
                activeFilter === filter
                  ? 'bg-green-600 text-white shadow-lg shadow-green-600/20'
                  : 'bg-white/10 text-white/70 hover:bg-white/15'
              }`}
            >
              {filter === 'all'
                ? 'All'
                : filter === 'missed'
                ? 'Missed'
                : filter === 'outgoing'
                ? 'Outgoing'
                : 'Incoming'}
            </button>
          ))}
        </div>
      </div>

      {/* Call list with consistent spacing */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-[#0a0f1a] to-[#0a0f1a]/95 flex flex-col min-h-0">
        <AnimatePresence>
          <div className="flex-1 pt-1 pb-2">
            {sortedCalls.map((call, index) => {
              const statusInfo = getStatusInfo(call.status);
              const contactInfo = getContactInfo(call.contact_number);
              const name = contactInfo.name || call.contact_number;
              const photo = contactInfo.photo;

              // Group calls by date
              const callDate = new Date(call.timestamp * 1000);
              const dateString = callDate.toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric' });

              // Check if this is the first call of a new date group
              const prevCall = index > 0 ? sortedCalls[index - 1] : null;
              const prevCallDate = prevCall ? new Date(prevCall.timestamp * 1000) : null;
              const isNewDateGroup = !prevCallDate ||
                prevCallDate.toLocaleDateString() !== callDate.toLocaleDateString();

              return (
                <React.Fragment key={call.id}>
                  {/* Date header if this is a new date group */}
                  {isNewDateGroup && (
                    <div className="px-4 py-1.5 sticky top-0 bg-[#0a0f1a]/90 backdrop-blur-sm z-10 w-full">
                      <p className="text-white/50 text-xs font-medium">
                        {isToday(call.timestamp)
                          ? 'Today'
                          : isYesterday(call.timestamp)
                            ? 'Yesterday'
                            : dateString}
                      </p>
                    </div>
                  )}

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    whileTap={{ scale: 0.98 }}
                    className="mx-4 mb-1.5 py-1.5 px-2.5 border-b border-white/5 flex items-center hover:bg-[#0f1525]/50 transition-colors"
                    onClick={() => {
                      if (onContactPress) onContactPress(call.contact_number, name);
                    }}
                  >
                    {/* Status icon with avatar */}
                    <div className="relative">
                      {/* Contact avatar with status overlay */}
                      {photo ? (
                        <div className="relative">
                          <div
                            className="w-9 h-9 rounded-full bg-cover bg-center"
                            style={{ backgroundImage: `url(${photo})` }}
                          />
                          <div className={`absolute inset-0 rounded-full ${statusInfo.bgColor} mix-blend-overlay opacity-20`}></div>
                        </div>
                      ) : (
                        <div className="relative">
                          <div className="w-9 h-9 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              {name[0].toUpperCase()}
                            </span>
                          </div>
                          <div className={`absolute inset-0 rounded-full ${statusInfo.bgColor} mix-blend-overlay opacity-20`}></div>
                        </div>
                      )}


                    </div>

                    {/* Call info */}
                    <div className="ml-2.5 flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <div className="text-white font-medium text-sm truncate pr-2">
                          {name}
                        </div>
                        <div className="text-white/50 text-xs flex-shrink-0">
                          {formatTime(call.timestamp)}
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-0.5">
                        <div className="text-xs text-white/50 flex items-center">
                          <span className={`${statusInfo.color} mr-1.5 text-xs`}>
                            {statusInfo.icon}
                          </span>
                          <span className="text-white/60">
                            {statusInfo.label}
                          </span>

                          {/* Duration if available */}
                          {call.duration > 0 && (
                            <>
                              <span className="mx-1.5 text-white/30">•</span>
                              <span className="text-white/40 flex items-center">
                                <i className="fas fa-clock text-[8px] mr-1"></i>
                                {formatDuration(call.duration)}
                              </span>
                            </>
                          )}
                        </div>

                        {/* Call button */}
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          onClick={e => {
                            e.stopPropagation();
                            onCallPress(call.contact_number, name, photo || undefined);
                          }}
                          className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center text-green-500 hover:bg-green-500/30 transition-colors"
                        >
                          <i className="fas fa-phone text-xs"></i>
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </React.Fragment>
              );
            })}
          </div>
        </AnimatePresence>

        {/* Empty state */}
        {sortedCalls.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex-1 flex flex-col items-center justify-center text-white/60 px-6 py-8"
          >
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#1a2234] to-[#0d1423] flex items-center justify-center mb-4 shadow-inner border border-white/5">
              <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                <i className="fas fa-phone-slash text-xl text-white/40"></i>
              </div>
            </div>
            <p className="text-lg font-medium text-center text-white/80">
              No {activeFilter !== 'all' ? activeFilter : ''} calls
            </p>
            <p className="text-sm text-white/40 text-center mt-2.5 max-w-xs leading-relaxed">
              {activeFilter === 'all'
                ? 'Your call history will appear here when you make or receive calls'
                : `You don't have any ${activeFilter} calls in your history yet`}
            </p>

            {activeFilter === 'all' && (
              <button className="mt-5 px-5 py-2 bg-green-600 hover:bg-green-700 text-white rounded-full text-sm font-medium transition-colors flex items-center shadow-lg shadow-green-600/20">
                <i className="fas fa-phone-alt mr-2"></i>
                Make a call
              </button>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CompactCallHistory;
