/*
 * LifeSnap Color System
 * This file contains explicit RGB/RGBA color definitions for the LifeSnap app
 */

:root {
  /* Base colors with explicit RGB values */
  --lifesnap-bg-dark: rgb(17, 24, 39);        /* #111827 - Main app background */
  --lifesnap-bg-darker: rgb(10, 15, 26);      /* #0a0f1a - Post detail background */
  --lifesnap-black: rgb(0, 0, 0);             /* #000000 */
  --lifesnap-white: rgb(255, 255, 255);       /* #ffffff */
  
  /* Brand colors */
  --lifesnap-blue: rgb(59, 130, 246);         /* #3b82f6 - Verified badge, buttons */
  --lifesnap-yellow: rgb(234, 179, 8);        /* #eab308 - Yellow-500 */
  --lifesnap-pink: rgb(236, 72, 153);         /* #ec4899 - Pink-500 */
  --lifesnap-red: rgb(239, 68, 68);           /* #ef4444 - Red-500 for likes */
}

/* ===== BACKGROUNDS ===== */
.lifesnap-bg-main {
  background-color: var(--lifesnap-bg-dark);
}

.lifesnap-bg-detail {
  background-color: var(--lifesnap-bg-darker);
}

.lifesnap-bg-black-20 {
  background-color: rgba(0, 0, 0, 0.2);
}

.lifesnap-bg-black-90 {
  background-color: rgba(0, 0, 0, 0.9);
}

/* ===== TEXT COLORS ===== */
.lifesnap-text-white {
  color: var(--lifesnap-white);
}

.lifesnap-text-white-90 {
  color: rgba(255, 255, 255, 0.9);
}

.lifesnap-text-white-80 {
  color: rgba(255, 255, 255, 0.8);
}

.lifesnap-text-white-60 {
  color: rgba(255, 255, 255, 0.6);
}

.lifesnap-text-white-40 {
  color: rgba(255, 255, 255, 0.4);
}

.lifesnap-text-blue {
  color: var(--lifesnap-blue);
}

.lifesnap-text-red {
  color: var(--lifesnap-red);
}

.lifesnap-text-yellow {
  color: var(--lifesnap-yellow);
}

/* ===== BORDERS ===== */
.lifesnap-border-white-10 {
  border-color: rgba(255, 255, 255, 0.1);
}

.lifesnap-border-black {
  border-color: var(--lifesnap-black);
}

/* ===== STORY RING ===== */
.lifesnap-story-ring-gradient {
  background-image: linear-gradient(to top right, var(--lifesnap-yellow), var(--lifesnap-pink));
}

/* ===== HOVER STATES ===== */
.hover\:lifesnap-text-white:hover {
  color: var(--lifesnap-white);
}

.hover\:lifesnap-text-blue-400:hover {
  color: rgba(96, 165, 250, 1); /* lighter blue */
}

/* ===== DISABLED STATES ===== */
.disabled\:lifesnap-opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:lifesnap-cursor-not-allowed:disabled {
  cursor: not-allowed;
}
