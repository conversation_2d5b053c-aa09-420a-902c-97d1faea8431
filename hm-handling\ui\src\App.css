body {
  background-image: url('/bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.handling-menu {
  position: fixed;
  right: 0;
  top: 0;
  width: 25%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  border-left: 2px solid #333;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.handling-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  background: rgba(0, 0, 0, 0.8);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.handling-menu-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.handling-menu-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.reset-all-btn {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.2s;
}

.reset-all-btn:hover:not(:disabled) {
  background: #b71c1c;
}

.reset-all-btn:disabled {
  background: #555;
  cursor: not-allowed;
}

.close-btn {
  background: transparent;
  color: #ccc;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.handling-panel {
  flex: 1;
  overflow-y: auto;  padding: 8px;
}

.handling-fields {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.main-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.main-fields > * {
  margin-bottom: 4px;
}

.main-fields > *:last-child {
  margin-bottom: 0;
}

/* Compact inline field style */
.handling-field-compact {
  display: grid;
  grid-template-columns: auto 1fr auto auto auto;
  gap: 6px;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid #333;
  border-radius: 4px;
  padding: 6px 8px;
  transition: all 0.2s;
  font-size: 11px;
}

.handling-field-compact.changed {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.08);
}

.handling-field-compact .field-name {
  font-weight: bold;
  color: #fff;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  max-width: 140px;
}

.handling-field-compact .stock-value {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #aaa;
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 4px;
  border-radius: 2px;
  border: 1px solid #444;
  min-width: 50px;
  text-align: center;
}

.handling-field-compact .current-value-input {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  border-radius: 2px;
  padding: 2px 4px;
  color: white;
  width: 60px;
  text-align: center;
  appearance: textfield; /* Standard property */
  -moz-appearance: textfield; /* Firefox */
}

/* Hide spinner arrows in Chrome, Safari, Edge */
.handling-field-compact .current-value-input::-webkit-outer-spin-button,
.handling-field-compact .current-value-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.handling-field-compact .current-value-input:focus {
  outline: none;
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.handling-field-compact .reset-btn {
  background: transparent;
  color: #666;
  border: 1px solid #444;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.handling-field-compact .reset-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
  border-color: #666;
}

.handling-field-compact .reset-btn.active {
  color: #f44336;
  border-color: #f44336;
}

.handling-field-compact .reset-btn.active:hover {
  background: rgba(244, 67, 54, 0.1);
  color: #ff5722;
}

.handling-field-compact .reset-btn:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}

/* Confirm button styling */
.handling-field-compact .confirm-btn {
  background: transparent;
  border: 1px solid #444;
  color: #888;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.handling-field-compact .confirm-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
  border-color: #666;
}

.handling-field-compact .confirm-btn.active {
  color: #4CAF50;
  border-color: #4CAF50;
}

.handling-field-compact .confirm-btn.active:hover {
  background: rgba(76, 175, 80, 0.1);
  color: #66BB6A;
}

.handling-field-compact .confirm-btn:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}

/* Out-of-bounds styling */
.handling-field-compact.out-of-bounds {
  background: rgba(255, 152, 0, 0.1);
  border-left: 3px solid #ff9800;
}

.handling-field-compact.out-of-bounds .current-value-input {
  background: rgba(255, 152, 0, 0.15);
  border-color: #ff9800;
  color: #ff9800;
}

.handling-field-compact.out-of-bounds .field-name {
  color: #ff9800;
}

/* Vector3 field styling */
.vector3-field {
  grid-template-columns: auto 1fr auto auto auto;
  gap: 6px;
}

.vector3-field .stock-value.vector3-stock {
  font-size: 10px;
  color: #888;
  white-space: nowrap;
}

.vector3-field .vector3-inputs {
  display: flex;
  gap: 2px;
}

.vector3-field .vector3-inputs .current-value-input {
  flex: 1;
  min-width: 0;
  font-size: 10px;
  padding: 2px 4px;
  width: 50px;
}

/* Custom scrollbar */
.handling-panel::-webkit-scrollbar {
  width: 4px;
}

.handling-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.handling-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.handling-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Flag Categories */
.flag-categories {
  margin-top: 16px;
  border-top: 1px solid #333;
  padding-top: 12px;
}

.flag-category {
  margin-bottom: 12px;
  border: 1px solid #444;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.3);
}

.flag-category-title {
  margin: 0;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid #444;
  color: #fff;
  text-transform: none;
}

.flag-category .handling-field-compact {
  margin-bottom: 0;
  border: none;
  border-radius: 0;
}

/* Category Selector */
.category-selector {
  display: flex;
  border-bottom: 1px solid #333;
  background: rgba(0, 0, 0, 0.8);
  margin-bottom: 12px;
}

.category-btn {
  flex: 1;
  padding: 10px 12px;
  background: transparent;
  border: none;
  border-right: 1px solid #333;
  color: #ccc;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-btn:last-child {
  border-right: none;
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.category-btn.active {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-weight: 600;
}

.flag-category-content {
  padding: 8px 0;
}

/* Update select styling for AIHandling dropdown */
.current-value-input select,
select.current-value-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #444;
  color: #fff;
  padding: 4px 8px;
  border-radius: 2px;
  font-size: 11px;
  min-width: 80px;
}

select.current-value-input option {
  background: #222;
  color: #fff;
}

/* Flag Field Styles */
.flag-field {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #444;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.flag-header {
  display: grid;
  grid-template-columns: auto 1fr auto auto auto;
  gap: 8px;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  margin-bottom: 12px;
  border: 1px solid #555;
}

.flag-header .field-name {
  font-weight: 600;
  color: #fff;
  font-size: 12px;
}

.flag-header .stock-value,
.flag-header .current-value {
  font-size: 11px;
  color: #ccc;
  font-family: 'Courier New', monospace;
}

.flag-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 6px;
  max-height: 400px;
  overflow-y: auto;
}

.flag-checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid #333;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
}

.flag-checkbox-label:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: #555;
}

.flag-checkbox {
  width: 14px;
  height: 14px;
  margin: 0;
  cursor: pointer;
}

.flag-name {
  flex: 1;
  color: #fff;
  font-weight: 500;
}

.flag-value {
  color: #888;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  min-width: 70px;
  text-align: right;
}

.flag-checkbox-label.checked {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
}

.flag-checkbox-label.checked .flag-name {
  color: #4caf50;
}

.flag-checkbox-label.checked .flag-value {
  color: #4caf50;
}

/* Scrollbar styling for flag checkboxes */
.flag-checkboxes::-webkit-scrollbar {
  width: 6px;
}

.flag-checkboxes::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.flag-checkboxes::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.flag-checkboxes::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Enhanced header controls */
.profile-manager-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.profile-manager-btn:hover {
  background: #1565c0;
}

.apply-btn {
  background: #388e3c;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
}

.apply-btn:hover:not(:disabled) {
  background: #2e7d32;
}

.apply-btn:disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.apply-btn.has-changes {
  background: #ff9800;
  animation: pulse 2s infinite;
}

.apply-btn.has-changes:hover {
  background: #f57c00;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Vehicle Info Styles */
.vehicle-info {
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  background: rgba(0, 0, 0, 0.8);
  flex-shrink: 0;
}

.vehicle-info.no-vehicle {
  text-align: center;
}

/* No vehicle message */
.no-vehicle-message {
  display: flex;
  align-items: center;
  padding: 20px;
  margin: 10px 0;
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
  color: #ffab00;
}

.no-vehicle-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.7;
}

.no-vehicle-text h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.no-vehicle-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.vehicle-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-weight: 600;
  color: #f44336;
}

.refresh-btn {
  background: transparent;
  border: none;
  color: #ccc;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 3px;
  font-size: 12px;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: rotate(90deg);
}

.no-vehicle-message {
  font-size: 12px;
  color: #888;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vehicle-details {
  flex: 1;
}

.vehicle-name {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
}

.vehicle-model {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 4px;
}

.vehicle-meta {
  display: flex;
  gap: 12px;
  font-size: 10px;
  color: #888;
}

.vehicle-hash {
  font-family: monospace;
}

.vehicle-class {
  background: #333;
  padding: 1px 4px;
  border-radius: 2px;
}

/* Separate Vehicle Info Line */
.vehicle-info-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid #333;
  flex-shrink: 0;
}

.vehicle-info-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #ccc;
  flex-wrap: wrap;
}

.vehicle-name {
  color: #4CAF50;
  font-weight: 500;
}

.vehicle-model {
  color: #2196F3;
  font-family: monospace;
}

.vehicle-class {
  color: #FF9800;
  font-size: 11px;
  background: rgba(255, 152, 0, 0.15);
  padding: 2px 6px;
  border-radius: 3px;
}

/* Unsaved Changes Bar */
.unsaved-changes-bar {
  background: #ff9800;
  color: #000;
  padding: 6px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  border-bottom: 1px solid #333;
}

.changes-icon {
  color: #f57c00;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Profile Manager Section */
.profile-manager-section {
  border-bottom: 1px solid #333;
  max-height: 300px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.9);
}

/* Profile Manager Styles */
.profile-manager {
  padding: 12px 16px;
}

.profile-header {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;
}

.profile-search-input {
  flex: 1;
  background: #333;
  border: 1px solid #555;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.profile-search-input:focus {
  outline: none;
  border-color: #1976d2;
}

.create-profile-btn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  white-space: nowrap;
}

.create-profile-btn:hover:not(:disabled) {
  background: #45a049;
}

.create-profile-btn:disabled {
  background: #666;
  cursor: not-allowed;
}

/* Profile Creator */
.profile-creator {
  background: #222;
  border: 1px solid #444;
  border-radius: 4px;
  margin-bottom: 12px;
}

.profile-creator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #444;
  background: #1a1a1a;
}

.profile-creator-header h3 {
  margin: 0;
  font-size: 12px;
  color: #fff;
}

.close-btn {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.close-btn:hover {
  color: #fff;
}

.profile-form {
  padding: 12px;
}

.form-row {
  margin-bottom: 10px;
}

.form-row label {
  display: block;
  font-size: 10px;
  color: #ccc;
  margin-bottom: 4px;
  font-weight: 600;
}

.form-row input,
.form-row textarea,
.form-row select {
  width: 100%;
  background: #333;
  border: 1px solid #555;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
  box-sizing: border-box;
}

.form-row textarea {
  resize: vertical;
  min-height: 60px;
}

.form-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #ccc;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.cancel-btn {
  background: #666;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
}

.cancel-btn:hover {
  background: #555;
}

.save-btn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
}

.save-btn:hover:not(:disabled) {
  background: #45a049;
}

.save-btn:disabled {
  background: #666;
  cursor: not-allowed;
}

/* Profile List */
.profile-list {
  max-height: 200px;
  overflow-y: auto;
}

.profile-item {
  border: 1px solid #444;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #1a1a1a;
}

.profile-info {
  padding: 8px 10px;
}

.profile-main {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 6px;
}

.profile-icon {
  font-size: 14px;
  margin-top: 1px;
}

.profile-details {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-size: 12px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
}

.profile-description {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 4px;
  line-height: 1.3;
}

.profile-meta {
  font-size: 9px;
  color: #888;
}

.profile-category {
  color: #1976d2;
  font-weight: 600;
}

.profile-vehicle {
  color: #ff9800;
}

.profile-actions {
  display: flex;
  gap: 6px;
  margin-top: 6px;
}

.load-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
}

.load-btn:hover {
  background: #1565c0;
}

.delete-btn {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
}

.delete-btn:hover {
  background: #c62828;
}

.no-profiles {
  text-align: center;
  color: #888;
  font-size: 11px;
  padding: 20px;
  font-style: italic;
}

/* Main Content Area */
.handling-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.vehicle-info-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #ccc;
  flex-wrap: wrap;
}

.vehicle-name {
  color: #4CAF50;
  font-weight: 500;
}

.vehicle-model {
  color: #2196F3;
  font-family: monospace;
}

.vehicle-class {
  color: #FF9800;
  font-size: 11px;
  background: rgba(255, 152, 0, 0.15);
  padding: 2px 6px;
  border-radius: 3px;
}

.vehicle-info-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-dropdown {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #444;
  color: white;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  min-width: 120px;
}

.profile-dropdown:hover {
  border-color: #666;
}

.profile-dropdown option {
  background: #222;
  color: white;
}

.profile-dropdown option:disabled {
  color: #666;
  font-style: italic;
}

/* Save Profile Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.save-profile-modal {
  background: rgba(0, 0, 0, 0.95);
  border: 2px solid #333;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  background: rgba(0, 0, 0, 0.8);
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.modal-content {
  padding: 16px;
}

.form-row {
  margin-bottom: 16px;
}

.form-row label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #ccc;
  font-weight: 500;
}

.form-row input,
.form-row textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #444;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-family: inherit;
  box-sizing: border-box;
}

.form-row input:focus,
.form-row textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.form-row textarea {
  resize: vertical;
  min-height: 60px;
}

.form-checkbox {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #ccc;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #333;
  justify-content: flex-end;
}

.cancel-btn {
  background: transparent;
  color: #ccc;
  border: 1px solid #444;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #666;
  color: white;
}

.save-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-btn:hover:not(:disabled) {
  background: #45a049;
}

.save-btn:disabled {
  background: #555;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Development mode indicator */
.dev-mode-indicator {
  background: #ff6b35;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  margin-right: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
