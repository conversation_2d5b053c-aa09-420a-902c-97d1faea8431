import React from 'react';
import { Job, JobGroup } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';

interface MyGroupsProps {
  groups: JobGroup[];
  jobs: Job[];
  onBack: () => void;
  onViewGroup: (groupId: number) => void;
}

const MyGroups: React.FC<MyGroupsProps> = ({ groups, jobs, onBack, onViewGroup }) => {
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'RECRUITING':
        return 'text-blue-400 bg-blue-400/20';
      case 'IN_PROGRESS':
        return 'text-yellow-400 bg-yellow-400/20';
      case 'COMPLETED':
        return 'text-green-400 bg-green-400/20';
      case 'FAILED':
        return 'text-red-400 bg-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/20';
    }
  };

  const formatTimeAgo = (timestamp: number): string => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);

    if (seconds < 60) return `${seconds}s ago`;

    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;

    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  const getJobById = (jobId: number): Job | undefined => {
    return jobs.find(job => job.id === jobId);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex items-center">
        <button
          onClick={onBack}
          className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <h2 className="text-white font-medium ml-2">My Groups</h2>
      </div>

      {/* Groups list */}
      <div className="flex-1 overflow-y-auto p-4">
        {groups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-white/60 p-4">
            <i className="fas fa-users text-3xl mb-2"></i>
            <p className="text-center">You haven't joined any groups yet.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {groups
              .sort((a, b) => {
                // Sort by status: RECRUITING and IN_PROGRESS first, then by creation date
                if (
                  (a.status === 'RECRUITING' || a.status === 'IN_PROGRESS') &&
                  b.status !== 'RECRUITING' &&
                  b.status !== 'IN_PROGRESS'
                ) {
                  return -1;
                }
                if (
                  (b.status === 'RECRUITING' || b.status === 'IN_PROGRESS') &&
                  a.status !== 'RECRUITING' &&
                  a.status !== 'IN_PROGRESS'
                ) {
                  return 1;
                }
                // If both are active or both are inactive, sort by creation date (newest first)
                return b.createdAt - a.createdAt;
              })
              .map(group => {
                const job = getJobById(group.jobId);
                if (!job) return null;

                return (
                  <div
                    key={group.id}
                    className="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10 cursor-pointer hover:bg-white/15 transition-colors"
                    onClick={() => onViewGroup(group.id)}
                  >
                    <div className="p-2.5">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-white font-medium text-base">{group.name}</h3>
                          <p className="text-white/70 text-xs mt-0.5">
                            {job.title} - {job.company}
                          </p>
                        </div>

                        <span
                          className={`text-[10px] px-1.5 py-0.5 rounded-full ${getStatusColor(
                            group.status
                          )}`}
                        >
                          {group.status.replace('_', ' ')}
                        </span>
                      </div>

                      <div className="mt-1.5 flex flex-wrap gap-x-3 gap-y-1 text-[10px] text-white/60">
                        <div className="flex items-center gap-1">
                          <i className="fas fa-users"></i>
                          <span>
                            {group.members.length}/{job.maxPlayers || '∞'} members
                          </span>
                        </div>

                        {group.status === 'RECRUITING' && (
                          <div className="flex items-center gap-1">
                            <i className="fas fa-clock"></i>
                            <span>Created {formatTimeAgo(group.createdAt)}</span>
                          </div>
                        )}

                        {group.status === 'IN_PROGRESS' && group.startedAt && (
                          <div className="flex items-center gap-1">
                            <i className="fas fa-play"></i>
                            <span>Started {formatTimeAgo(group.startedAt)}</span>
                          </div>
                        )}

                        {(group.status === 'COMPLETED' || group.status === 'FAILED') &&
                          group.completedAt && (
                            <div className="flex items-center gap-1">
                              <i className="fas fa-flag-checkered"></i>
                              <span>Completed {formatTimeAgo(group.completedAt)}</span>
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default MyGroups;
