export enum DrivingFlags {
    StopForCars = 1,
    StopForPeds = 2,
    SwerveAroundAllCars = 4,
    SteerAroundStationaryCars = 8,
    SteerAroundPeds = 16,
    SteerAroundObjects = 32,
    DontSteerAroundPlayerPed = 64,
    StopAtLights = 128,
    GoOffRoadWhenAvoiding = 256,
    DriveIntoOncomingTraffic = 512,
    DriveInReverse = 1024,
    UseWanderFallbackInsteadOfStraightLine = 2048,
    AvoidRestrictedAreas = 4096,
    PreventBackgroundPathfinding = 8192,
    AdjustCruiseSpeedBasedOnRoadSpeed = 16384,
    UseShortCutLinks = 262144,
    ChangeLanesAroundObstructions = 524288,
    UseSwitchedOffNodes = 2097152,
    PreferNavmeshRoute = 4194304,
    PlaneTaxiMode = 8388608,
    ForceStraightLine = 16777216,
    UseStringPullingAtJunctions = 33554432,
    AvoidHighways = *********,
    ForceJoinInRoadDirection = 1073741824,

}

// These are combinations of the above flags to represent different driving modes
export enum DrivingMode {
    StopForCars =   786603, // DF_StopForCars | DF_StopForPeds | DF_SteerAroundObjects | DF_SteerAroundStationaryCars | DF_StopAtLights | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions
    StopForCarsStrict = 262275, // DF_StopForCars | DF_StopForPeds | DF_StopAtLights | DF_UseShortCutLinks
    AvoidCars = 786469, // DF_SwerveAroundAllCars | DF_SteerAroundObjects | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions | DF_StopForCars
    AvoidCarsReckless = 786468, // DF_SwerveAroundAllCars | DF_SteerAroundObjects | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions
    PloughThrough = 262144, // DF_UseShortCutLinks
    StopForCarsIgnoreLights = 786475, // DF_StopForCars | DF_SteerAroundStationaryCars | DF_StopForPeds | DF_SteerAroundObjects | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions
    AvoidCarsObeyLights = 786597, // DF_SwerveAroundAllCars | DF_StopAtLights | DF_SteerAroundObjects | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions | DF_StopForCars
    AvoidCarsStopForPedsObeyLights = 786599 // DF_SwerveAroundAllCars | DF_StopAtLights | DF_StopForPeds | DF_SteerAroundObjects | DF_UseShortCutLinks | DF_ChangeLanesAroundObstructions | DF_StopForCars
}

export enum DrivingStyle {
    Normal = 0,
    Racing = 1,
    Reversing = 2,
}


//INFO: 
//PARAM NOTES:
//PURPOSE:
// NATIVE PROC FREEZE_RADIO_STATION(STRING RadioStationName) = "0x8b7cd2568e24fe82"

//INFO: 
//PARAM NOTES:
//PURPOSE: Retunes a named static emitter to the specified station
// NATIVE PROC SET_EMITTER_RADIO_STATION(STRING EmitterName, STRING RadioStationName) = "0xece7b7efd96cebf4"

//INFO: 
//PARAM NOTES:
//PURPOSE: Forces a specific track on a radio station.  The station must be frozen, and the track will be queued for immediate playback
// NATIVE PROC SET_RADIO_TRACK(STRING RadioStationName, STRING TrackName) = "0x93cb338c8902831c"

//INFO: 
//PARAM NOTES:
//PURPOSE: Forces a specific track on a radio station with a given start offset.  The station must be frozen, and the track will be queued for immediate playback
// NATIVE PROC SET_RADIO_TRACK_WITH_START_OFFSET(STRING RadioStationName, STRING TrackName, INT TimeOffsetMs) = "0x5b1db18d92a04434"

//INFO: 
//PARAM NOTES:
//PURPOSE: Forces the next track on a radio station. The track will played following the currently playing track
// NATIVE PROC SET_NEXT_RADIO_TRACK(STRING RadioStationName, STRING category, STRING contextName, STRING trackIndex) = "0x3d330982888f05e5"

//INFO: 
//PARAM NOTES:
//PURPOSE: Gets the current play time (in milliseconds) into the active music playlist for the given station
// NATIVE INT GET_MUSIC_TRACK_LIST_PLAY_TIME_OFFSET_MS(STRING RadioStationName) = "0xc9a6f1669345b14e"

//INFO: 
//PARAM NOTES:
//PURPOSE: Gets the total duration (in milliseconds) of the active music playlist for the given station
// NATIVE INT GET_MUSIC_TRACK_LIST_DURATION_MS(STRING RadioStationName) = "0x62ec19ec897e956f"

//INFO:
//PARAM NOTES: If ForceNow = TRUE then the current track will be interrupted by the tracklist
//PURPOSE: Queues up a custom track list on the specified radio station.  The content in the track list will be played as soon as possible.  
//			The station does not have to be frozen.
// NATIVE PROC SET_CUSTOM_RADIO_TRACK_LIST(STRING RadioStationName, STRING TrackListName, BOOL ForceNow = FALSE) = "0xba4e8f889faa1728"

//INFO:
//PURPOSE: Get the time in seconds that the current track has been playing on the given radio station
// NATIVE FUNC INT GET_CURRENT_TRACK_PLAY_TIME(STRING radioStationName) = "0x48c9930f53211812"

//INFO:
//PURPOSE: Get the sound name for the track currently playing on the given station
// NATIVE FUNC INT GET_CURRENT_TRACK_SOUND_NAME(STRING radioStationName) = "0x07e9d9bdd31f68bd"

//INFO: 
//PARAM NOTES:
//PURPOSE: 
// NATIVE PROC SET_VEHICLE_RADIO_LOUD(VEHICLE_INDEX vehIndex, BOOL loud) = "0x32cac81ec8c5ba86"

//INFO: 
//PARAM NOTES:
//PURPOSE: Enables/disables a positioned emitter for the player vehicle radio
// NATIVE PROC SET_POSITIONED_PLAYER_VEHICLE_RADIO_EMITTER_ENABLED(BOOL enabled) = "0xe9669168376aa182"