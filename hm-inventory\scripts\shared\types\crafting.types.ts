// Defines types related to crafting system

import { ItemSlot } from './inventory.types';

/**
 * Represents a crafting recipe ingredient.
 */
export interface RecipeIngredient {
    itemId: string; // References ItemDefinition.id
    quantity: number;
}

/**
 * Represents a complete crafting recipe.
 */
export interface CraftingRecipe {
    id: string; // Unique identifier for the recipe (must match the itemId it produces)
    craftingTime: number; // in seconds
    quantity: number; // How many of the item are produced
    ingredients: RecipeIngredient[];
    requiresWorkbench?: boolean;
    skillRequired?: string;
    skillLevel?: number;
}

/**
 * Represents an item in the crafting queue
 */
export interface CraftingQueueItem {
    id: string;
    recipe: CraftingRecipe;
    quantity: number;
    startTime: number;
    duration: number;
    progress: number;
}

/**
 * Crafting station data
 */
export interface CraftingStationData {
    stationId: string;
    stationType: CraftingStationType;
    position?: { x: number; y: number; z: number }; // For proximity checks
    availableRecipes: string[]; // Array of recipe IDs
    currentQueue: CraftingQueueItem[];
    maxQueueSize: number;
    inventory?: ItemSlot[]; // Station shared inventory slots
}

/**
 * Crafting station types
 */
export type CraftingStationType =
    | 'GENERAL_WORKBENCH'
    | 'WEAPON_BENCH'
    | 'CHEMISTRY_STATION'
    | 'ELECTRONICS_WORKBENCH'
    | 'VEHICLE_GARAGE'
    | 'COOKING_STATION'
    | 'MEDICAL_STATION';

/**
 * Crafting panel configuration
 */
export interface CraftingPanelConfig {
    inventoryColumns: number;
    inventoryMaxVisibleRows: number;
    maxQueueSlots: number;
}
