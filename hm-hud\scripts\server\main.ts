/// <reference types="@citizenfx/server" />

const RESOURCE_NAME = GetCurrentResourceName();

// Initialize the server-side functionality with baseevents integration
function initialize(): void {
    console.log(`[${RESOURCE_NAME}] Server initialized with baseevents integration`);
    
    setupBaseEvents();
}

function setupBaseEvents(): void {
    // Vehicle entry event - notify client to start vehicle tracking
    onNet('baseevents:enteredVehicle', (currentVehicle: number, currentSeat: number, vehicleDisplayName: string, vehicleNetId: number) => {
        const source = global.source;
        console.log(`[${RESOURCE_NAME}] Player ${source} entered vehicle ${currentVehicle} (netId: ${vehicleNetId})`);
        
        // Emit to client to start vehicle tracking
        emitNet('hm-hud:client:enteredVehicle', source, currentVehicle, vehicleNetId);
    });

    // Vehicle exit event - notify client to stop vehicle tracking
    onNet('baseevents:leftVehicle', (currentVehicle: number, currentSeat: number, vehicleDisplayName: string, vehicleNetId: number) => {
        const source = global.source;
        console.log(`[${RESOURCE_NAME}] Player ${source} left vehicle ${currentVehicle} (netId: ${vehicleNetId})`);
        
        // Emit to client to stop vehicle tracking
        emitNet('hm-hud:client:leftVehicle', source, currentVehicle, vehicleNetId);
    });
}

// Handle player disconnect - state bags are automatically cleaned up
on('playerDropped', (reason: string) => {
    const source = global.source;
    console.log(`[${RESOURCE_NAME}] Player ${source} disconnected: ${reason}`);
});

// Export functions for other resources to use
global.exports('isHudLoaded', () => {
    return true;
});

// Start initialization when the resource starts
on('onResourceStart', (resourceName: string) => {
    if (resourceName === RESOURCE_NAME) {
        initialize();
    }
});
