import React, { useState, useRef } from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';
import { parseDate } from '../../../utils/timeUtils';
import { Story } from '../types/lifeSnapTypes';

interface StoriesProps {
  onStoryClick: (id: number) => void;
  showAddStory?: boolean;
}

const Stories: React.FC<StoriesProps> = ({ onStoryClick, showAddStory = true }) => {
  const store = useLifeSnapStore();
  const { stories, profiles, viewedStories } = store;

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('[LifeSnap] Stories component - stories:', stories?.length || 0);
    if (stories && stories.length > 0) {
      console.log('[LifeSnap] First story sample:', stories[0]);
    }
  }
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.pageX - (containerRef.current?.offsetLeft || 0));
    setScrollLeft(containerRef.current?.scrollLeft || 0);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const x = e.pageX - (containerRef.current?.offsetLeft || 0);
    const walk = (x - startX) * 2;

    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollLeft - walk;
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  if (stories && stories.length > 0) {
    console.log('[LifeSnap] First story sample:', stories[0]);
    if (stories.length > 1) {
      console.log('[LifeSnap] Second story sample:', stories[1]);
    }
  }

  const activeStoriesByUser =
    stories && stories.length > 0
      ? stories
          .filter(story => {
            console.log(`[LifeSnap] Filtering story ${story.id} for user ${story.userId}`);
            const isActive = true;
            return isActive;
          })
          .reduce((acc, story) => {
            const userIdStr = String(story.userId);
            if (!acc[userIdStr]) {
              console.log(`[LifeSnap] Creating new array for user ${userIdStr}`);
              acc[userIdStr] = [];
            }
            acc[userIdStr].push(story);
            return acc;
          }, {} as Record<string, typeof stories>)
      : {};

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('[LifeSnap] Active stories by user:', Object.keys(activeStoriesByUser).length);
    if (Object.keys(activeStoriesByUser).length > 0) {
      console.log(
        '[LifeSnap] Active stories by user sample:',
        Object.entries(activeStoriesByUser).map(([userId, stories]) => ({
          userId,
          storiesCount: Array.isArray(stories) ? stories.length : 0,
          firstStory: Array.isArray(stories) && stories.length > 0 ? stories[0] : null
        }))
      );
    }
  }

  // Sort stories for each user by timestamp (newest first)
  if (Object.keys(activeStoriesByUser).length > 0) {
    Object.values(activeStoriesByUser).forEach(userStories => {
      if (Array.isArray(userStories)) {
        userStories.sort((a: Story, b: Story) => {
          // Safely get timestamp values with fallbacks
          const getTimeValue = (timestamp: Date | number | string | undefined) => {
            const date = parseDate(timestamp);
            return date ? date.getTime() : 0;
          };

          const aTime = getTimeValue(a.timestamp);
          const bTime = getTimeValue(b.timestamp);
          return bTime - aTime; // Newest first
        });
      }
    });
  }

  // Sort users based on their most recent story and viewed status
  const sortedUsers =
    Object.keys(activeStoriesByUser).length > 0
      ? Object.entries(activeStoriesByUser).sort((a, b) => {
          // Only log in development mode
          if (process.env.NODE_ENV === 'development') {
            console.log(`[LifeSnap] Sorting users ${a[0]} and ${b[0]}`);
          }
          const [, userStoriesA] = a;
          const [, userStoriesB] = b;

          // Ensure we have stories for both users
          if (!userStoriesA || !userStoriesA.length) return 1;
          if (!userStoriesB || !userStoriesB.length) return -1;

          // First prioritize users with unviewed stories
          const hasUnviewedA = userStoriesA.some(story => !viewedStories.includes(story.id));
          const hasUnviewedB = userStoriesB.some(story => !viewedStories.includes(story.id));

          if (hasUnviewedA !== hasUnviewedB) {
            return hasUnviewedA ? -1 : 1;
          }

          // Then sort by most recent story (newest first)
          // Find the most recent story for each user
          const getMostRecentStory = (stories: Story[]) => {
            return stories.reduce((latest, current) => {
              const latestTime = parseDate(latest.timestamp)?.getTime() || 0;
              const currentTime = parseDate(current.timestamp)?.getTime() || 0;
              return currentTime > latestTime ? current : latest;
            }, stories[0]);
          };

          const latestStoryA = getMostRecentStory(userStoriesA);
          const latestStoryB = getMostRecentStory(userStoriesB);

          const aTime = parseDate(latestStoryA.timestamp)?.getTime() || 0;
          const bTime = parseDate(latestStoryB.timestamp)?.getTime() || 0;

          return bTime - aTime; // Newest first
        })
      : [];

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('[LifeSnap] Stories component - sortedUsers:', sortedUsers.length);
    if (sortedUsers.length > 0) {
      console.log(
        '[LifeSnap] Sorted users sample:',
        sortedUsers.map(([userId, stories]) => ({
          userId,
          storiesCount: stories.length
        }))
      );
    }
  }

  // If there are no stories and no add story button, don't render anything
  if (sortedUsers.length === 0 && !showAddStory) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="flex gap-4 p-4 overflow-x-auto scrollbar-none"
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onMouseMove={handleMouseMove}
      style={{
        overflowX: sortedUsers.length <= 3 ? 'hidden' : 'auto'
      }}
    >
      {showAddStory && (
        <div
          onClick={() => onStoryClick(-1)}
          className="flex flex-col items-center gap-1 flex-shrink-0 cursor-pointer"
        >
          <div className="w-14 h-14 rounded-full border-2 border-white/20 flex items-center justify-center">
            <i className="fas fa-plus text-white/80"></i>
          </div>
          <span className="text-[10px] text-white/60">Your Story</span>
        </div>
      )}

      {sortedUsers.map(([userId, userStories]) => {
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log(`[LifeSnap] Rendering story for user ${userId}`);
        }

        // Get the first story for this user to extract profile information
        const firstStory = userStories[0];
        if (!firstStory) {
          // Only log error in development mode
          if (process.env.NODE_ENV === 'development') {
            console.error(`[LifeSnap] No stories found for user ${userId}`);
          }
          return null;
        }

        // Use embedded profile information from the story or find in profiles array
        let username = firstStory.username || `User-${userId}`;
        let profilePicture =
          firstStory.profilePicture || `https://picsum.photos/200/200?random=${userId}`;
        // If we don't have embedded profile info, try to find it in the profiles array
        if ((!username || !profilePicture) && profiles && profiles.length > 0) {
          const profile = profiles?.find(p => String(p?.id) === userId);
          if (profile) {
            username = profile.username;
            profilePicture = profile.profilePicture;
          } else {
            // Only log error in development mode
            console.log(
              `[LifeSnap] No profile found in profiles array for user ${userId}, using fallback`
            );
          }
        }

        // Check if all stories have been viewed
        const allStoriesViewed = userStories?.every(story => viewedStories?.includes(story.id));

        const gradientClass = allStoriesViewed
          ? 'bg-gradient-to-tr from-gray-500 to-gray-600'
          : 'bg-gradient-to-tr from-yellow-500 to-pink-500';

        // Find the oldest unviewed story or use the last story
        const oldestUnviewedStory =
          userStories && userStories.length > 0
            ? [...userStories].reverse().find(story => !viewedStories?.includes(story.id))
            : null;
        const storyToShow =
          oldestUnviewedStory ||
          (userStories && userStories.length > 0 ? userStories[userStories.length - 1] : null);

        if (!storyToShow) {
          // Only log error in development mode
          if (process.env.NODE_ENV === 'development') {
            console.error(`[LifeSnap] No story to show for user ${userId}`);
          }
          return null;
        }

        return (
          <div
            key={userId}
            onClick={() => onStoryClick(storyToShow.id)}
            className="flex flex-col items-center gap-1 flex-shrink-0 cursor-pointer"
          >
            <div className={`w-14 h-14 rounded-full p-[2px] ${gradientClass}`}>
              <div className="relative w-full h-full">
                <img
                  src={profilePicture || 'https://picsum.photos/200/200?random=default'}
                  alt={username || 'User'}
                  className="w-full h-full rounded-full object-cover border-2 border-black"
                />
              </div>
            </div>
            <span className="text-[10px] text-white/60 truncate w-14 text-center">
              {username || 'User'}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default Stories;
