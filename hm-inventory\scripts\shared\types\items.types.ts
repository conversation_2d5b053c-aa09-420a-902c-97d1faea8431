// Defines the structure of an item and related types

import { SlotType } from './inventory.types';

/**
 * Represents the different types an item can be.
 */
export enum ItemType {
    GENERAL = 'general',
    WEAPON = 'weapon',
    CONSUMABLE = 'consumable',
    GADGET = 'gadget',
    AMMO = 'ammo',
    KEY = 'key'
}

/**
 * Defines how an item behaves when used
 */
export interface ItemUsage {
    consumable?: boolean; // Whether the item is consumed on use (default: true for CONSUMABLE, false for others)
    cooldown?: number; // Cooldown in milliseconds between uses
    category?: string; // Usage category for grouped handling (e.g., 'food', 'medical', 'tool')
    effects?: {
        health?: number; // Health to restore/damage
        hunger?: number; // Hunger to restore
        thirst?: number; // Thirst to restore
        stress?: number; // Stress to reduce/add
        armor?: number; // Armor to restore
    };
    animation?: {
        dict: string; // Animation dictionary
        name: string; // Animation name
        duration?: number; // Duration in ms
        flag?: number; // Animation flags
    };
    progressBar?: {
        label: string; // Progress bar label
        duration: number; // Duration in ms
        color?: 'green' | 'blue' | 'orange' | 'red';
    };
    customHandler?: string; // Custom event name for specific handling
    requirements?: {
        items?: Array<{ item: string; quantity: number }>; // Required items to use this item
        location?: string; // Required location/zone
        job?: string; // Required job
    };
}

/**
 * Defines the static blueprint for an item type.
 * This is the single source of truth for the properties of an item category.
 */
export interface ItemDefinition {
    id: string; // Unique identifier for this item TYPE (e.g., "weapon_pistol", "medkit_small")
    name: string; // Internal/programmatic name (often same as id for simplicity)
    label: string; // Default display name shown to the player
    description: string; // Default description
    type: ItemType; // Category of the item
    weight: number; // Weight per unit (grams)
    stackable: boolean;
    maxStack?: number; // If stackable
    icon: string; // Default icon (e.g., filename or path)
    model?: number | string; // 3D model hash/name for spawning on ground when dropped
    usable?: boolean;
    destroyable?: boolean;
    validSlotTypes?: SlotType[]; // Which equipment slots it can go into (for equippable items)
    durability?: number;
    maxDurability?: number; // Maximum durability if the item type has it
    usage?: ItemUsage; // How this item behaves when used
    metadata?: Record<string, any>; // For any other static properties (e.g., base damage, consumable effects, default attachments, weapon category)
}
