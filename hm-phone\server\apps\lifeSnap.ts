/**
 * LifeSnap App - Server Side
 *
 * This file handles server-side functionality for the LifeSnap app.
 */

import config from '@shared/config';

/**
 * Initialize the LifeSnap app
 */
export function initializeLifeSnapApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
}

/**
 * Register server events for the LifeSnap app
 */
function registerServerEvents(): void {
    // Register event for getting feed
    onNet('hm-phone:getLifeSnapFeed', async () => {
        const source = global.source;
        console.log(`[LifeSnap] Received getLifeSnapFeed event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support, fallback to identifier
            const playerIdentifier = stateid || identifier;

            // Get feed for the player
            const feed = await getLifeSnapFeed(playerIdentifier);

            // Send the feed back to the client
            emitNet('hm-phone:lifeSnapFeed', source, feed);
        } catch (error) {
            console.error('[LifeSnap] Error getting feed:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to get feed');
        }
    });

    // Register event for getting stories
    onNet('hm-phone:getLifeSnapStories', async () => {
        const source = global.source;
        console.log(`[LifeSnap] Received getLifeSnapStories event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get stories for the player
            const stories = await getLifeSnapStories(playerIdentifier);

            // Send the stories back to the client
            emitNet('hm-phone:lifeSnapStories', source, stories);
        } catch (error) {
            console.error('[LifeSnap] Error getting stories:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to get stories');
        }
    });

    // Register event for getting profile
    onNet('hm-phone:getLifeSnapProfile', async (profileId: string) => {
        const source = global.source;
        console.log(`[LifeSnap] Received getLifeSnapProfile event from player ${source} for profile ${profileId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get profile for the player
            const profile = await getLifeSnapProfile(profileId || playerIdentifier);

            // Send the profile back to the client
            emitNet('hm-phone:lifeSnapProfile', source, profile);
        } catch (error) {
            console.error('[LifeSnap] Error getting profile:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to get profile');
        }
    });

    // Register event for creating a post
    onNet('hm-phone:createLifeSnapPost', async (content: string, imageUrl: string, location: any) => {
        const source = global.source;
        console.log(`[LifeSnap] Received createLifeSnapPost event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Create the post
            const post = await createLifeSnapPost(
                playerIdentifier,
                content,
                imageUrl,
                location,
                player.name || 'Unknown'
            );

            // Send the post back to the client
            emitNet('hm-phone:lifeSnapPostCreated', source, post);

            // Broadcast the post to all players
            broadcastNewPost(post);
        } catch (error) {
            console.error('[LifeSnap] Error creating post:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to create post');
        }
    });

    // Register event for creating a story
    onNet('hm-phone:createLifeSnapStory', async (content: string, imageUrl: string, location: any) => {
        const source = global.source;
        console.log(`[LifeSnap] Received createLifeSnapStory event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Create the story
            const story = await createLifeSnapStory(
                playerIdentifier,
                content,
                imageUrl,
                location,
                player.name || 'Unknown'
            );

            // Send the story back to the client
            emitNet('hm-phone:lifeSnapStoryCreated', source, story);

            // Broadcast the story to all players
            broadcastNewStory(story);
        } catch (error) {
            console.error('[LifeSnap] Error creating story:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to create story');
        }
    });

    // Register event for liking a post
    onNet('hm-phone:likeLifeSnapPost', async (postId: number) => {
        const source = global.source;
        console.log(`[LifeSnap] Received likeLifeSnapPost event from player ${source} for post ${postId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Like the post
            const likes = await likeLifeSnapPost(playerIdentifier, postId);

            // Send the like data back to the client
            emitNet('hm-phone:lifeSnapPostLiked', source, postId, likes);

            // Broadcast the like to all players
            broadcastPostLiked(postId, likes);
        } catch (error) {
            console.error('[LifeSnap] Error liking post:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to like post');
        }
    });

    // Register event for adding a comment
    onNet('hm-phone:addLifeSnapComment', async (postId: number, content: string) => {
        const source = global.source;
        console.log(`[LifeSnap] Received addLifeSnapComment event from player ${source} for post ${postId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[LifeSnap] Player ${source} not found`);
                emitNet('hm-phone:lifeSnapError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[LifeSnap] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:lifeSnapError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Add the comment
            const comment = await addLifeSnapComment(playerIdentifier, postId, content, player.name || 'Unknown');

            // Send the comment data back to the client
            emitNet('hm-phone:lifeSnapCommentAdded', source, postId, comment);

            // Broadcast the comment to all players
            broadcastCommentAdded(postId, comment);
        } catch (error) {
            console.error('[LifeSnap] Error adding comment:', error);
            emitNet('hm-phone:lifeSnapError', source, 'Failed to add comment');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[LifeSnap] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[LifeSnap] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_lifesnap_posts table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_lifesnap_posts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    stateid VARCHAR(255) NULL,
                    content TEXT NOT NULL,
                    image_url TEXT NULL,
                    likes INT DEFAULT 0,
                    comments INT DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    location VARCHAR(255) NULL,
                    is_story BOOLEAN DEFAULT FALSE,
                    expires_at TIMESTAMP NULL,
                    INDEX idx_identifier (identifier),
                    INDEX idx_stateid (stateid),
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_is_story (is_story)
                )
            `,
                [],
                () => {
                    console.log('[LifeSnap] phone_lifesnap_posts table initialized');
                }
            );

            // Create the phone_lifesnap_comments table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_lifesnap_comments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    post_id INT NOT NULL,
                    identifier VARCHAR(255) NOT NULL,
                    stateid VARCHAR(255) NULL,
                    content TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    likes INT DEFAULT 0,
                    INDEX idx_post_id (post_id),
                    INDEX idx_identifier (identifier),
                    INDEX idx_stateid (stateid),
                    INDEX idx_timestamp (timestamp),
                    FOREIGN KEY (post_id) REFERENCES phone_lifesnap_posts(id) ON DELETE CASCADE
                )
            `,
                [],
                () => {
                    console.log('[LifeSnap] phone_lifesnap_comments table initialized');
                }
            );

            // Create the phone_lifesnap_likes table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_lifesnap_likes (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    post_id INT NULL,
                    comment_id INT NULL,
                    identifier VARCHAR(255) NOT NULL,
                    stateid VARCHAR(255) NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_post_id (post_id),
                    INDEX idx_comment_id (comment_id),
                    INDEX idx_identifier (identifier),
                    INDEX idx_stateid (stateid),
                    FOREIGN KEY (post_id) REFERENCES phone_lifesnap_posts(id) ON DELETE CASCADE,
                    FOREIGN KEY (comment_id) REFERENCES phone_lifesnap_comments(id) ON DELETE CASCADE,
                    CONSTRAINT chk_target CHECK (post_id IS NOT NULL OR comment_id IS NOT NULL)
                )
            `,
                [],
                () => {
                    console.log('[LifeSnap] phone_lifesnap_likes table initialized');
                }
            );
        } else {
            console.error('[LifeSnap] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[LifeSnap] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get LifeSnap feed for a player
 * @param identifier Player identifier
 * @returns Array of posts
 */
async function getLifeSnapFeed(identifier: string): Promise<any[]> {
    console.log(`[LifeSnap] Getting feed for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let posts = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            posts = await global.exports.oxmysql.query_async(
                `
                SELECT p.*,
                    (SELECT COUNT(*) FROM phone_lifesnap_likes WHERE post_id = p.id) as like_count,
                    (SELECT COUNT(*) FROM phone_lifesnap_comments WHERE post_id = p.id) as comment_count
                FROM phone_lifesnap_posts p
                WHERE p.is_story = 0
                ORDER BY p.timestamp DESC
                LIMIT 50
            `,
                []
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            posts = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT p.*,
                        (SELECT COUNT(*) FROM phone_lifesnap_likes WHERE post_id = p.id) as like_count,
                        (SELECT COUNT(*) FROM phone_lifesnap_comments WHERE post_id = p.id) as comment_count
                    FROM phone_lifesnap_posts p
                    WHERE p.is_story = 0
                    ORDER BY p.timestamp DESC
                    LIMIT 50
                `,
                    [],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // Format posts for the UI
        return posts.map((post: any) => ({
            id: post.id,
            identifier: post.identifier,
            content: post.content,
            imageUrl: post.image_url,
            likes: post.like_count || 0,
            comments: post.comment_count || 0,
            timestamp: new Date(post.timestamp).getTime(),
            location: post.location,
            isStory: false
        }));
    } catch (error: any) {
        console.error('[LifeSnap] Error getting feed:', error);
        throw new Error(`Failed to get feed: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Get LifeSnap stories for a player
 * @param identifier Player identifier
 * @returns Array of stories
 */
async function getLifeSnapStories(identifier: string): Promise<any[]> {
    console.log(`[LifeSnap] Getting stories for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let stories = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            stories = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_lifesnap_posts
                WHERE is_story = 1 AND (expires_at IS NULL OR expires_at > NOW())
                ORDER BY timestamp DESC
                LIMIT 50
            `,
                []
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            stories = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_lifesnap_posts
                    WHERE is_story = 1 AND (expires_at IS NULL OR expires_at > NOW())
                    ORDER BY timestamp DESC
                    LIMIT 50
                `,
                    [],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // Format stories for the UI
        return stories.map((story: any) => ({
            id: story.id,
            identifier: story.identifier,
            content: story.content,
            imageUrl: story.image_url,
            timestamp: new Date(story.timestamp).getTime(),
            location: story.location,
            isStory: true,
            expiresAt: story.expires_at ? new Date(story.expires_at).getTime() : null
        }));
    } catch (error: any) {
        console.error('[LifeSnap] Error getting stories:', error);
        throw new Error(`Failed to get stories: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Get LifeSnap profile for a player
 * @param identifier Player identifier
 * @returns Profile data
 */
async function getLifeSnapProfile(identifier: string): Promise<any> {
    console.log(`[LifeSnap] Getting profile for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Get player posts
        let posts = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            posts = await global.exports.oxmysql.query_async(
                `
                SELECT p.*,
                    (SELECT COUNT(*) FROM phone_lifesnap_likes WHERE post_id = p.id) as like_count,
                    (SELECT COUNT(*) FROM phone_lifesnap_comments WHERE post_id = p.id) as comment_count
                FROM phone_lifesnap_posts p
                WHERE p.identifier = ? AND p.is_story = 0
                ORDER BY p.timestamp DESC
                LIMIT 20
            `,
                [identifier]
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            posts = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT p.*,
                        (SELECT COUNT(*) FROM phone_lifesnap_likes WHERE post_id = p.id) as like_count,
                        (SELECT COUNT(*) FROM phone_lifesnap_comments WHERE post_id = p.id) as comment_count
                    FROM phone_lifesnap_posts p
                    WHERE p.identifier = ? AND p.is_story = 0
                    ORDER BY p.timestamp DESC
                    LIMIT 20
                `,
                    [identifier],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // Format posts for the UI
        const formattedPosts = posts.map((post: any) => ({
            id: post.id,
            identifier: post.identifier,
            content: post.content,
            imageUrl: post.image_url,
            likes: post.like_count || 0,
            comments: post.comment_count || 0,
            timestamp: new Date(post.timestamp).getTime(),
            location: post.location,
            isStory: false
        }));

        // Get player data
        const player = getPlayerByIdentifier(identifier);

        // Return profile data
        return {
            identifier,
            name: player?.name || 'Unknown',
            phoneNumber: player?.phoneNumber || '',
            posts: formattedPosts,
            postCount: formattedPosts.length,
            followerCount: 0, // Not implemented yet
            followingCount: 0 // Not implemented yet
        };
    } catch (error: any) {
        console.error('[LifeSnap] Error getting profile:', error);
        throw new Error(`Failed to get profile: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create a LifeSnap post
 * @param identifier Player identifier
 * @param content Post content
 * @param imageUrl Post image URL
 * @param location Post location
 * @param playerName Player name
 * @returns Created post
 */
async function createLifeSnapPost(
    identifier: string,
    content: string,
    imageUrl: string,
    location: any,
    playerName: string
): Promise<any> {
    console.log(`[LifeSnap] Creating post for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Format location
        const locationString = location ? JSON.stringify(location) : null;

        // Insert the post
        let postId = 0;

        // Check if insert_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            postId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_lifesnap_posts (identifier, stateid, content, image_url, location, is_story)
                VALUES (?, ?, ?, ?, ?, 0)
            `,
                [identifier, identifier, content, imageUrl, locationString]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            postId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_lifesnap_posts (identifier, stateid, content, image_url, location, is_story)
                    VALUES (?, ?, ?, ?, ?, 0)
                `,
                    [identifier, identifier, content, imageUrl, locationString],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        // Return the created post
        return {
            id: postId,
            identifier,
            content,
            imageUrl,
            likes: 0,
            comments: 0,
            timestamp: Date.now(),
            location: locationString,
            isStory: false,
            playerName
        };
    } catch (error: any) {
        console.error('[LifeSnap] Error creating post:', error);
        throw new Error(`Failed to create post: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create a LifeSnap story
 * @param identifier Player identifier
 * @param content Story content
 * @param imageUrl Story image URL
 * @param location Story location
 * @param playerName Player name
 * @returns Created story
 */
async function createLifeSnapStory(
    identifier: string,
    content: string,
    imageUrl: string,
    location: any,
    playerName: string
): Promise<any> {
    console.log(`[LifeSnap] Creating story for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Format location
        const locationString = location ? JSON.stringify(location) : null;

        // Calculate expiration time (24 hours from now)
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24);

        // Insert the story
        let storyId = 0;

        // Check if insert_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            storyId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_lifesnap_posts (identifier, stateid, content, image_url, location, is_story, expires_at)
                VALUES (?, ?, ?, ?, ?, 1, ?)
            `,
                [identifier, identifier, content, imageUrl, locationString, expiresAt]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            storyId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_lifesnap_posts (identifier, stateid, content, image_url, location, is_story, expires_at)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                `,
                    [identifier, identifier, content, imageUrl, locationString, expiresAt],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        // Return the created story
        return {
            id: storyId,
            identifier,
            content,
            imageUrl,
            timestamp: Date.now(),
            location: locationString,
            isStory: true,
            expiresAt: expiresAt.getTime(),
            playerName
        };
    } catch (error: any) {
        console.error('[LifeSnap] Error creating story:', error);
        throw new Error(`Failed to create story: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Like a LifeSnap post
 * @param identifier Player identifier
 * @param postId Post ID
 * @returns Number of likes
 */
async function likeLifeSnapPost(identifier: string, postId: number): Promise<number> {
    console.log(`[LifeSnap] Liking post ${postId} for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Check if the player has already liked the post
        let hasLiked = false;

        if (typeof global.exports.oxmysql.scalar_async === 'function') {
            // Use scalar_async if available
            hasLiked = await global.exports.oxmysql.scalar_async(
                `
                SELECT COUNT(*) FROM phone_lifesnap_likes
                WHERE identifier = ? AND post_id = ?
            `,
                [identifier, postId]
            );
        } else if (typeof global.exports.oxmysql.scalar === 'function') {
            // Fall back to scalar if scalar_async is not available
            hasLiked = await new Promise<boolean>(resolve => {
                global.exports.oxmysql.scalar(
                    `
                    SELECT COUNT(*) FROM phone_lifesnap_likes
                    WHERE identifier = ? AND post_id = ?
                `,
                    [identifier, postId],
                    (result: any) => {
                        resolve(result > 0);
                    }
                );
            });
        }

        // If the player has already liked the post, unlike it
        if (hasLiked) {
            if (typeof global.exports.oxmysql.execute_async === 'function') {
                // Use execute_async if available
                await global.exports.oxmysql.execute_async(
                    `
                    DELETE FROM phone_lifesnap_likes
                    WHERE identifier = ? AND post_id = ?
                `,
                    [identifier, postId]
                );
            } else if (typeof global.exports.oxmysql.execute === 'function') {
                // Fall back to execute if execute_async is not available
                await new Promise<void>(resolve => {
                    global.exports.oxmysql.execute(
                        `
                        DELETE FROM phone_lifesnap_likes
                        WHERE identifier = ? AND post_id = ?
                    `,
                        [identifier, postId],
                        () => {
                            resolve();
                        }
                    );
                });
            }
        } else {
            // Otherwise, like the post
            if (typeof global.exports.oxmysql.insert_async === 'function') {
                // Use insert_async if available
                await global.exports.oxmysql.insert_async(
                    `
                    INSERT INTO phone_lifesnap_likes (post_id, identifier, stateid)
                    VALUES (?, ?, ?)
                `,
                    [postId, identifier, identifier]
                );
            } else if (typeof global.exports.oxmysql.insert === 'function') {
                // Fall back to insert if insert_async is not available
                await new Promise<void>(resolve => {
                    global.exports.oxmysql.insert(
                        `
                        INSERT INTO phone_lifesnap_likes (post_id, identifier, stateid)
                        VALUES (?, ?, ?)
                    `,
                        [postId, identifier, identifier],
                        () => {
                            resolve();
                        }
                    );
                });
            }
        }

        // Get the updated like count
        let likeCount = 0;

        if (typeof global.exports.oxmysql.scalar_async === 'function') {
            // Use scalar_async if available
            likeCount = await global.exports.oxmysql.scalar_async(
                `
                SELECT COUNT(*) FROM phone_lifesnap_likes
                WHERE post_id = ?
            `,
                [postId]
            );
        } else if (typeof global.exports.oxmysql.scalar === 'function') {
            // Fall back to scalar if scalar_async is not available
            likeCount = await new Promise<number>(resolve => {
                global.exports.oxmysql.scalar(
                    `
                    SELECT COUNT(*) FROM phone_lifesnap_likes
                    WHERE post_id = ?
                `,
                    [postId],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        }

        // Update the post's like count
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                UPDATE phone_lifesnap_posts
                SET likes = ?
                WHERE id = ?
            `,
                [likeCount, postId]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    UPDATE phone_lifesnap_posts
                    SET likes = ?
                    WHERE id = ?
                `,
                    [likeCount, postId],
                    () => {
                        resolve();
                    }
                );
            });
        }

        return likeCount;
    } catch (error: any) {
        console.error('[LifeSnap] Error liking post:', error);
        throw new Error(`Failed to like post: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Add a comment to a LifeSnap post
 * @param identifier Player identifier
 * @param postId Post ID
 * @param content Comment content
 * @param playerName Player name
 * @returns Created comment
 */
async function addLifeSnapComment(
    identifier: string,
    postId: number,
    content: string,
    playerName: string
): Promise<any> {
    console.log(`[LifeSnap] Adding comment to post ${postId} for player ${identifier}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        // Insert the comment
        let commentId = 0;

        // Check if insert_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            commentId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_lifesnap_comments (post_id, identifier, stateid, content)
                VALUES (?, ?, ?, ?)
            `,
                [postId, identifier, identifier, content]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            commentId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_lifesnap_comments (post_id, identifier, stateid, content)
                    VALUES (?, ?, ?, ?)
                `,
                    [postId, identifier, identifier, content],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        // Get the updated comment count
        let commentCount = 0;

        if (typeof global.exports.oxmysql.scalar_async === 'function') {
            // Use scalar_async if available
            commentCount = await global.exports.oxmysql.scalar_async(
                `
                SELECT COUNT(*) FROM phone_lifesnap_comments
                WHERE post_id = ?
            `,
                [postId]
            );
        } else if (typeof global.exports.oxmysql.scalar === 'function') {
            // Fall back to scalar if scalar_async is not available
            commentCount = await new Promise<number>(resolve => {
                global.exports.oxmysql.scalar(
                    `
                    SELECT COUNT(*) FROM phone_lifesnap_comments
                    WHERE post_id = ?
                `,
                    [postId],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        }

        // Update the post's comment count
        if (typeof global.exports.oxmysql.execute_async === 'function') {
            // Use execute_async if available
            await global.exports.oxmysql.execute_async(
                `
                UPDATE phone_lifesnap_posts
                SET comments = ?
                WHERE id = ?
            `,
                [commentCount, postId]
            );
        } else if (typeof global.exports.oxmysql.execute === 'function') {
            // Fall back to execute if execute_async is not available
            await new Promise<void>(resolve => {
                global.exports.oxmysql.execute(
                    `
                    UPDATE phone_lifesnap_posts
                    SET comments = ?
                    WHERE id = ?
                `,
                    [commentCount, postId],
                    () => {
                        resolve();
                    }
                );
            });
        }

        // Return the created comment
        return {
            id: commentId,
            postId,
            identifier,
            content,
            timestamp: Date.now(),
            likes: 0,
            playerName
        };
    } catch (error: any) {
        console.error('[LifeSnap] Error adding comment:', error);
        throw new Error(`Failed to add comment: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Broadcast a new post to all players
 * @param post Post data
 */
function broadcastNewPost(post: any): void {
    // Use the framework's broadcast function if available
    if (typeof (global as any).broadcastToAllPlayers === 'function') {
        (global as any).broadcastToAllPlayers('hm-phone:lifeSnapNewPost', post);
        return;
    }

    // Fallback: Get all players from the framework
    const players = (global as any).getAllPlayers ? (global as any).getAllPlayers() : [];

    // Broadcast the post to all players
    for (const player of players) {
        emitNet('hm-phone:lifeSnapNewPost', player.source || player, post);
    }
}

/**
 * Broadcast a new story to all players
 * @param story Story data
 */
function broadcastNewStory(story: any): void {
    // Use the framework's broadcast function if available
    if (typeof (global as any).broadcastToAllPlayers === 'function') {
        (global as any).broadcastToAllPlayers('hm-phone:lifeSnapNewStory', story);
        return;
    }

    // Fallback: Get all players from the framework
    const players = (global as any).getAllPlayers ? (global as any).getAllPlayers() : [];

    // Broadcast the story to all players
    for (const player of players) {
        emitNet('hm-phone:lifeSnapNewStory', player.source || player, story);
    }
}

/**
 * Broadcast a post like to all players
 * @param postId Post ID
 * @param likes Number of likes
 */
function broadcastPostLiked(postId: number, likes: number): void {
    // Use the framework's broadcast function if available
    if (typeof (global as any).broadcastToAllPlayers === 'function') {
        (global as any).broadcastToAllPlayers('hm-phone:lifeSnapPostLiked', postId, likes);
        return;
    }

    // Fallback: Get all players from the framework
    const players = (global as any).getAllPlayers ? (global as any).getAllPlayers() : [];

    // Broadcast the like to all players
    for (const player of players) {
        emitNet('hm-phone:lifeSnapPostLiked', player.source || player, postId, likes);
    }
}

/**
 * Broadcast a comment to all players
 * @param postId Post ID
 * @param comment Comment data
 */
function broadcastCommentAdded(postId: number, comment: any): void {
    // Use the framework's broadcast function if available
    if (typeof (global as any).broadcastToAllPlayers === 'function') {
        (global as any).broadcastToAllPlayers('hm-phone:lifeSnapCommentAdded', postId, comment);
        return;
    }

    // Fallback: Get all players from the framework
    const players = (global as any).getAllPlayers ? (global as any).getAllPlayers() : [];

    // Broadcast the comment to all players
    for (const player of players) {
        emitNet('hm-phone:lifeSnapCommentAdded', player.source || player, postId, comment);
    }
}

/**
 * Get player by identifier
 * @param identifier Player identifier
 * @returns Player data
 */
function getPlayerByIdentifier(identifier: string): any {
    // Use the framework's getPlayerByIdentifier function
    if (typeof (global as any).getPlayerByIdentifier === 'function') {
        return (global as any).getPlayerByIdentifier(identifier);
    }

    // Fallback: Get all players from the framework
    const players = (global as any).getAllPlayers ? (global as any).getAllPlayers() : [];

    // Find the player with the matching identifier
    for (const player of players) {
        const playerIdentifier = (global as any).getServerPlayerIdentifier
            ? (global as any).getServerPlayerIdentifier(player.source || player)
            : player.identifier;

        if (playerIdentifier === identifier) {
            return (global as any).getServerPlayerData
                ? (global as any).getServerPlayerData(player.source || player)
                : player;
        }
    }

    return null;
}
