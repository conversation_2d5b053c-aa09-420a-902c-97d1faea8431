@tailwind base;
@tailwind components;
@tailwind utilities;

::-webkit-scrollbar {
  display: none;
}

/* For Firefox */
* {
  scrollbar-width: none;
}

/* For IE and Edge */
* {
  -ms-overflow-style: none;
}

/* Make sure all scrollable areas still work */
.overflow-y-auto, .overflow-x-auto, .overflow-auto {
  -webkit-overflow-scrolling: touch;
}

/* Custom scrollable class */
.scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  height: 100%;
  width: 100%;
  overscroll-behavior: contain;
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Pseudo-element utilities */
.before\:absolute::before {
  position: absolute;
  content: "";
}

.before\:inset-0::before {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: "";
}

.before\:rounded-2xl::before {
  border-radius: 1rem;
  content: "";
}

.before\:bg-white::before {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  content: "";
}

.before\:opacity-0::before {
  opacity: 0;
  content: "";
}

.before\:transition-opacity::before {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  content: "";
}


