import React from 'react';
import { InventoryItem } from '@shared';

interface ItemTooltipProps {
  item: InventoryItem;
  quantity: number;
  isVisible: boolean;
  position: { x: number; y: number };
}

const ItemTooltip: React.FC<ItemTooltipProps> = ({ 
  item, 
  quantity, 
  isVisible, 
  position 
}) => {
  if (!isVisible || !item) return null;

  // Calculate durability percentage
  const durabilityPercentage = item.currentDurability && item.maxDurability 
    ? Math.round((item.currentDurability / item.maxDurability) * 100)
    : null;

  // Get durability color based on percentage
  const getDurabilityColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-400';
    if (percentage >= 50) return 'text-yellow-400';
    if (percentage >= 25) return 'text-orange-400';
    return 'text-red-400';
  };

  // Get item type color
//   const getItemTypeColor = (type: ItemType) => {
//     switch (type) {
//       case ItemType.WEAPON: return 'text-red-400';
//       case ItemType.CONSUMABLE: return 'text-green-400';
//       case ItemType.GADGET: return 'text-blue-400';
//       case ItemType.AMMO: return 'text-yellow-400';
//       case ItemType.KEY: return 'text-purple-400';
//       default: return 'text-neutral-400';
//     }
//   };

  // Format weight display
  const formatWeight = (weight: number) => {
    if (weight >= 1000) {
      return `${(weight / 1000).toFixed(1)}kg`;
    }
    return `${weight}g`;
  };

  // Calculate total weight
  const totalWeight = item.weight * quantity;

  return (
    <div 
      className="fixed bg-neutral-800/70 border border-neutral-700/80 border-t-green-400/20 rounded-lg shadow-xl backdrop-blur-sm z-[9999] pointer-events-none overflow-hidden"
      style={{ 
        left: position.x + 10, 
        top: position.y - 10,
        width: '170px'
      }}
    >
      {/* Subtle green gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-green-400/5 via-transparent to-transparent pointer-events-none rounded-lg z-0" />
      
      {/* Header with Description */}
      <div className="p-2 border-b border-neutral-700/80 relative z-10">
        {item.description && (
          <p className="text-xs text-neutral-200 leading-tight break-words">{item.description}</p>
        )}
      </div>

      {/* Content */}
      <div className="p-2 space-y-2 relative z-10">
        {/* Properties Grid */}
        <div className="space-y-1">
          {/* Weight */}
          <div className="flex justify-between text-xs">
            <span className="text-neutral-400">Weight:</span>
            <span className="text-neutral-200">
              {formatWeight(item.weight)}{quantity > 1 && ` (${formatWeight(totalWeight)})`}
            </span>
          </div>

          {/* Durability */}
          {durabilityPercentage !== null && (
            <div className="space-y-0.5">
              <div className="flex justify-between text-xs">
                <span className="text-neutral-400">Durability:</span>
                <span className={`font-medium ${getDurabilityColor(durabilityPercentage)}`}>
                  {durabilityPercentage}%
                </span>
              </div>
              <div className="w-full h-1 bg-neutral-700/50 rounded-full overflow-hidden">
                <div 
                  className={`h-full transition-all duration-300 ease-out shadow-sm ${
                    durabilityPercentage >= 80 ? 'bg-green-500/90 shadow-green-400/20' :
                    durabilityPercentage >= 50 ? 'bg-yellow-500/90 shadow-yellow-400/20' :
                    durabilityPercentage >= 25 ? 'bg-orange-500/90 shadow-orange-400/20' : 'bg-red-500/90 shadow-red-400/20'
                  }`}
                  style={{ width: `${durabilityPercentage}%` }}
                />
              </div>
            </div>
          )}

          {/* Valid Slot Types */}
          {item.validSlotTypes && item.validSlotTypes.length > 0 && (
            <div className="flex justify-between text-xs">
              <span className="text-neutral-400">Valid Slots:</span>
              <span className="text-neutral-200 text-right max-w-[60%]">
                {item.validSlotTypes.map(slot => 
                  slot.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
                ).join(', ')}
              </span>
            </div>
          )}
        </div>

        {/* Metadata */}
        {item.metadata && Object.keys(item.metadata).length > 0 && (
          <div className="border-t border-neutral-700/80 pt-1">
            <div className="space-y-0.5">
              {Object.entries(item.metadata).map(([key, value]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="text-neutral-500 capitalize truncate">
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
                  </span>
                  <span className="text-neutral-300 truncate">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Item IDs */}
        <div className="border-t border-neutral-700/80 pt-1">
          <div className="text-xs text-neutral-500 space-y-0.5">
            <div className="flex justify-between">
              <span className="font-mono truncate">{item.instanceId}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemTooltip;
