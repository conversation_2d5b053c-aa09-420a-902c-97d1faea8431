// Defines types related to inventory structure, slots, and events.

import { ItemDefinition } from './items.types';

/**
 * Optimized storage format for database - contains only instance-specific data.
 * Definition data is retrieved separately using definitionId to avoid redundancy.
 */
export interface InventoryItemInstance {
    instanceId: string; // Unique ID for THIS SPECIFIC item instance
    definitionId: string; // ID of the ItemDefinition this instance is based on
    quantity: number; // Current number of items in this stack
    currentDurability?: number; // Current durability if applicable
    metadata?: Record<string, any>; // Any custom instance-specific data
}

/**
 * Optimized slot format for database storage - minimal data only.
 */
export interface ItemSlotStorage {
    index: number; // Slot index
    item?: InventoryItemInstance; // Only instance data, no definition data
    slotType?: SlotType;
}

/**
 * Represents an actual item instance in an inventory slot or in the world.
 * It holds a reference to its definition and any instance-specific data.
 */
export interface InventoryItem extends ItemDefinition {
    instanceId: string; // Unique ID for THIS SPECIFIC item instance (e.g., generated by the server)
    definitionId: string; // ID of the ItemDefinition this instance is based on (links to ItemDefinition.id)
    quantity: number; // Current number of items in this stack.
    currentDurability?: number;
}

/**
 * Represents a single slot in an inventory, which may or may not contain an item.
 */
export interface ItemSlot {
    index: number; // Simple slot index
    quantity?: number;
    item?: InventoryItem;
    slotType?: SlotType;
    displayStyle?: {
        backgroundIcon: string;
        accentColor: string;
    };
}

export enum SlotType {
    PHONE = 'phone',
    ARMOR = 'armor',
    PRIMARY_WEAPON = 'primary_weapon',
    TABLET = 'tablet',
    BACKPACK = 'backpack'
}

/**
 * Represents a toast message for UI notifications.
 */
export interface Toast {
    id: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    duration?: number; // in milliseconds
    icon?: string; // FontAwesome icon class
    itemIcon?: string; // Item icon for item-related toasts
    quantity?: number; // For item quantity display
    amount?: number; // For purchase amounts
}

/**
 * Represents a progress bar for item usage (eating, drinking, etc.)
 */
export interface ProgressBar {
    id: string;
    label: string;
    progress: number; // 0-100
    icon?: string;
    color?: 'green' | 'blue' | 'orange' | 'red';
}
