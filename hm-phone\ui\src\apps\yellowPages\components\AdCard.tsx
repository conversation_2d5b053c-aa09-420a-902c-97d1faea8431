import React, { useState } from 'react';
import { Ad } from '../types/yellowPagesTypes';
import { getTimeAgo } from '../../../utils/timeUtils';
import { getCategoryInfo } from '../utils/categoryUtils';
import { useYellowPagesStore } from '../stores/yellowPagesStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { useNavigation } from '../../../navigation/hooks';
import { useDialerStore } from '../../contacts/stores/dialerStore';
import MessageModal from '../../messages/components/MessageModal';

interface AdCardProps {
  ad: Ad;
}

const AdCard: React.FC<AdCardProps> = ({ ad }) => {
  const { deleteAd } = useYellowPagesStore().actions;
  const { loading } = useYellowPagesStore();
  const userProfile = usePhoneStore(state => state.userProfile);
  const { openAppView } = useNavigation();
  const { actions: dialerActions } = useDialerStore();

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);

  const categoryInfo = getCategoryInfo(Array.isArray(ad.category) ? ad.category[0] : ad.category);

  // Check if this ad belongs to the current user
  const isOwnAd = userProfile?.phoneNumber === ad.contactNumber;

  return (
    <div className="bg-[#1a1a1a] rounded-lg overflow-hidden border border-gray-800 mb-3">
      {ad.imageUrl && (
        <div className="aspect-[16/9] overflow-hidden">
          <img
            src={ad.imageUrl}
            alt={ad.characterName || 'Ad image'}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <div className="p-3">
        <div className="space-y-1">
          <p className="text-xs text-gray-300">{ad.description}</p>
          {ad.price !== undefined && (
            <p className="text-sm font-medium text-green-400">${ad.price.toLocaleString()}</p>
          )}
        </div>

        {/* Tags line */}
        <div className="flex flex-wrap items-center gap-2 mt-3 mb-3">
          {Array.isArray(ad.category) ? (
            Array.isArray(ad.category) ? (
              ad.category.map(cat => {
                const info = getCategoryInfo(cat);
                return (
                  <div
                    key={cat}
                    className={`inline-flex items-center h-6 gap-1.5 px-2 rounded-full
                    ${info.colorClasses.bg} border ${info.colorClasses.border}`}
                  >
                    <i className={`fas fa-${info.icon} ${info.colorClasses.text} text-xs`}></i>
                    <span className={`text-xs font-medium ${info.colorClasses.text} leading-none`}>
                      {info.label}
                    </span>
                  </div>
                );
              })
            ) : (
              <span className="px-2 py-1 text-xs rounded-full bg-gray-800">{ad.category}</span>
            )
          ) : (
            <div
              className={`inline-flex items-center h-6 gap-1.5 px-2 rounded-full
                ${categoryInfo.colorClasses.bg} border ${categoryInfo.colorClasses.border}`}
            >
              <i
                className={`fas fa-${categoryInfo.icon} ${categoryInfo.colorClasses.text} text-xs`}
              ></i>
              <span
                className={`text-xs font-medium ${categoryInfo.colorClasses.text} leading-none`}
              >
                {categoryInfo.label}
              </span>
            </div>
          )}
        </div>

        {/* Divider */}
        <div className="h-[1px] bg-gray-800 mb-3"></div>

        {/* Bottom row with character name, timestamp, and action buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold text-gray-300">{ad.characterName}</span>
            {ad.timestamp && (
              <span className="text-xs text-gray-500">• {getTimeAgo(ad.timestamp)}</span>
            )}
          </div>

          {ad.contactNumber && (
            <div className="flex items-center gap-2">
              {/* Call button */}
              <button
                onClick={() => {
                  dialerActions.makeCall(ad.contactNumber, ad.characterName);
                  openAppView('contacts', 'main', { tab: 'dialer' });
                }}
                disabled={loading || isOwnAd}
                className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center hover:bg-green-500/20 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="fas fa-phone text-green-500/60 text-sm"></i>
              </button>

              {/* Message button */}
              <button
                onClick={() => setShowMessageModal(true)}
                disabled={loading || isOwnAd}
                className="w-8 h-8 bg-yellow-500/10 rounded-lg flex items-center justify-center hover:bg-yellow-500/20 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="fas fa-comment text-yellow-500/60 text-sm"></i>
              </button>

              {/* Message modal */}
              {showMessageModal && (
                <MessageModal
                  phoneNumber={ad.contactNumber}
                  name={ad.characterName}
                  onClose={() => setShowMessageModal(false)}
                />
              )}

              {/* Delete button (only shown for own ads) */}
              {isOwnAd && (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={loading || isDeleting}
                  className="w-8 h-8 bg-red-500/10 rounded-lg flex items-center justify-center hover:bg-red-500/20 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i className="fas fa-trash text-red-500/60 text-sm"></i>
                </button>
              )}

              {/* Delete confirmation */}
              {showDeleteConfirm && (
                <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-10 p-4">
                  <div className="bg-[#1a1a1a] p-4 rounded-lg max-w-xs w-full">
                    <h3 className="text-lg font-bold text-white mb-2">Delete Ad</h3>
                    <p className="text-gray-300 mb-4">
                      Are you sure you want to delete this ad? This action cannot be undone.
                    </p>
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => setShowDeleteConfirm(false)}
                        disabled={isDeleting}
                        className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={async () => {
                          setIsDeleting(true);
                          try {
                            await deleteAd(ad.id);
                          } catch (error) {
                            console.error('Error deleting ad:', error);
                          } finally {
                            setIsDeleting(false);
                            setShowDeleteConfirm(false);
                          }
                        }}
                        disabled={isDeleting}
                        className="px-4 py-2 bg-red-500/20 text-red-500 rounded-lg hover:bg-red-500/30 disabled:opacity-50 flex items-center justify-center min-w-[80px]"
                      >
                        {isDeleting ? <span className="animate-pulse">...</span> : 'Delete'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdCard;
