import React, { useState, useMemo } from 'react';
import { useMessagesStore } from '../stores/messagesStore';
// We're using the Conversation type directly
import { useContactsStore } from '../../contacts/stores/contactsStore';
import { useSelectionStore } from '../../../common/stores/selectionStore';
import { useNavigation } from '../../../navigation/hooks';
import { useNavigationStore } from '../../../navigation/navigationStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { normalizePhone } from '../../../utils/phoneUtils';

// Define an enhanced member type that includes UI properties
interface EnhancedMember {
  id?: number;
  phone: string;
  name: string;
  displayName: string;
  isAdmin: boolean;
  avatar?: string;
  hasContact?: boolean; // Flag to indicate if this is a saved contact
}

// We no longer need the ParticipantObject interface as we're using ConversationMember

const GroupInfoScreen: React.FC = () => {
  const { activeChat, conversations, actions } = useMessagesStore();
  const { openView, openAppAndReturn } = useNavigation();
  const { openAppView } = useNavigationStore();
  const { contacts } = useContactsStore();
  const { startSelection } = useSelectionStore();
  const { userProfile } = usePhoneStore();

  const [showConfirmLeave, setShowConfirmLeave] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');

  // Ensure conversations is an array before using find
  const conversation = Array.isArray(conversations)
    ? conversations.find(c => c.id === activeChat)
    : undefined;

  // Get members and enhance with contact information if available
  const members = useMemo(() => {
    // Get the members from the conversation
    const memberPhones = conversation?.members ? Object.keys(conversation.members) : [];
    const enhancedMembers: EnhancedMember[] = [];

    // Process all members
    for (const phoneNumber of memberPhones) {
      if (!phoneNumber) continue;

      // Get the member data
      const memberData = conversation?.members?.[phoneNumber];
      if (!memberData) continue;

      // Normalize the phone number for comparison
      const normalizedPhone = normalizePhone(phoneNumber);

      // Try to find a matching contact using normalized phone number
      const contact = contacts?.find(c => normalizePhone(c.number) === normalizedPhone);
      const hasContact = !!contact;

      // Create an enhanced member object
      const enhancedMember: EnhancedMember = {
        phone: phoneNumber,
        name: hasContact ? contact.name : phoneNumber,
        displayName: hasContact ? contact.name : phoneNumber,
        isAdmin: memberData.is_admin || false, // Use member data
        avatar: hasContact && contact.avatar ? contact.avatar : undefined,
        hasContact // Track if we have this contact saved
      };

      enhancedMembers.push(enhancedMember);
    }

    return enhancedMembers;
  }, [conversation?.members, contacts]);

  // Check if the current user is an admin of the group
  const isAdmin = useMemo(() => {
    if (!conversation?.members || !userProfile?.phoneNumber) return false;

    // Check if the current user is in the members list
    const memberData = conversation.members[userProfile.phoneNumber];
    if (!memberData) return false;

    // Return whether the user is an admin
    return memberData.is_admin || false;
  }, [conversation?.members, userProfile?.phoneNumber]);

  if (!conversation?.type || conversation.type !== 'group' || !activeChat) return null;

  // Handle changing the group image
  const handleChangeGroupImage = () => {
    if (!activeChat || !isAdmin) return;

    console.log('[GroupInfo] Opening Photos app for image selection');

    // Use the selection store to start selection mode for photos
    startSelection({
      mode: 'single', // Only select one photo
      maxSelection: 1,
      initialSelected: [],
      purpose: 'share',
      targetApp: 'messages',
      returnPath: 'messages',
      callback: selectedPhotos => {
        console.log('[GroupInfo] Photo selection callback with:', selectedPhotos);

        // Ensure we have a valid photo
        if (!selectedPhotos || !Array.isArray(selectedPhotos) || selectedPhotos.length === 0) {
          console.log('[GroupInfo] No photo selected, skipping');
          return;
        }

        // Get the first photo (we only support one in single mode)
        const selectedPhoto = selectedPhotos[0];

        // Ensure the selected photo has an id property and it's a string
        if (!selectedPhoto || typeof selectedPhoto.id !== 'string') {
          console.log('[GroupInfo] Invalid photo data:', selectedPhoto);
          return;
        }

        // Update the group image using the store action
        actions.updateGroupImage(activeChat, selectedPhoto.id);
      }
    });

    // Navigate to the photos app
    openAppAndReturn('messages', 'photos', { returnTo: 'messages' });
  };

  // Handle toggling mute status
  const handleToggleMute = () => {
    if (!activeChat) return;

    // Toggle the mute status using the store action
    actions.toggleMute(activeChat);
  };

  // Handle updating the group name
  const handleUpdateGroupName = () => {
    if (!activeChat || !isAdmin || !newGroupName.trim()) return;

    // Update the group name using the store action
    actions.updateGroupName(activeChat, newGroupName.trim());

    // Exit editing mode
    setIsEditingName(false);
  };

  // Handle clicking on a member
  const handleMemberClick = (member: EnhancedMember) => {
    // Don't do anything if it's the current user
    if (member.phone === userProfile?.phoneNumber) return;

    // Normalize the phone number
    const memberPhone = normalizePhone(member.phone);

    // Find the contact by phone number, using normalized comparison
    const contact = contacts?.find(c => normalizePhone(c.number) === memberPhone);

    if (contact) {
      // If contact exists, navigate to contact detail view
      console.log('[GroupInfo] Contact found, navigating to detail view:', contact.id);
      openAppView('contacts', 'detail', { id: contact.id });
    } else {
      // If contact doesn't exist, navigate to add contact view with pre-filled data
      console.log(
        '[GroupInfo] Contact not found, navigating to add view with phone:',
        member.phone
      );
      openAppView('contacts', 'add', { display_name: member.name, contact_number: member.phone });
    }
  };

  // Handle removing a member from the group
  const handleRemoveMember = (phoneNumber: string) => {
    if (!activeChat || !isAdmin) return;

    // Validate that we're not trying to remove ourselves or another admin
    if (phoneNumber === userProfile?.phoneNumber) {
      console.error('[GroupInfo] Cannot remove yourself from the group');
      return;
    }

    // Check if the member exists in the conversation
    if (!conversation?.members || !conversation.members[phoneNumber]) {
      console.error('[GroupInfo] Member not found:', phoneNumber);
      return;
    }

    // Get the member data
    const memberData = conversation.members[phoneNumber];

    // Check if the member is an admin
    if (memberData.is_admin) {
      console.error('[GroupInfo] Cannot remove an admin from the group');
      return;
    }

    console.log('[GroupInfo] Removing member from group:', phoneNumber);

    // Use the store action to remove the member
    actions.removeGroupMember(activeChat, phoneNumber);
  };

  // Handle adding members to the group
  const handleAddMembers = () => {
    if (!activeChat || !isAdmin) return;

    // Use the selection store to start selection mode
    startSelection({
      mode: 'multiple',
      maxSelection: 10,
      initialSelected: [],
      purpose: 'addToGroup',
      targetApp: 'messages',
      returnPath: 'messages',
      callback: selectedContacts => {
        console.log('[GroupInfo] Contact selection callback with:', selectedContacts);

        // Ensure we have valid contacts
        if (
          !selectedContacts ||
          !Array.isArray(selectedContacts) ||
          selectedContacts.length === 0
        ) {
          console.log('[GroupInfo] No contacts selected, skipping');
          return;
        }

        // Add each selected contact to the group
        selectedContacts.forEach(contact => {
          // Get the phone number from the contact
          const phoneNumber = typeof contact.contact_number === 'string' ? contact.contact_number : '';

          // Use the store action to add the member by phone number
          if (phoneNumber) {
            actions.addGroupMember(activeChat, phoneNumber);
          }
        });
      }
    });

    // Navigate to the contacts app
    openAppAndReturn('messages', 'contacts', {});
  };

  return (
    <div className="relative flex-1 flex flex-col bg-[#0a0f1a] overflow-hidden">
      {/* Header - Updated to match conversation header */}
      <div className="flex items-center gap-2 px-3 py-2 bg-[#0a0f1a] border-b border-white/10">
        <button
          onClick={() => openView('conversation')}
          className="text-white/80 hover:text-white flex-shrink-0 cursor-pointer"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <div className="text-white font-medium">Group Info</div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Group Avatar and Name Section */}
        <div className="relative w-20 h-20 mx-auto mt-4 mb-2">
          <div className="w-full h-full rounded-full overflow-hidden bg-white/10">
            {conversation.avatar ? (
              <img
                src={conversation.avatar}
                alt={conversation.name || 'Group'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <i className="fas fa-users text-3xl text-white/60"></i>
              </div>
            )}
          </div>
          {isAdmin && (
            <>
              <button
                onClick={() => handleChangeGroupImage()}
                className="absolute bottom-0 right-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer hover:bg-blue-600"
              >
                <i className="fas fa-camera text-xs"></i>
              </button>
            </>
          )}
        </div>

        {/* Group Name Edit Section */}
        <div className="px-4 mb-3">
          {isEditingName ? (
            <div className="flex items-center justify-center gap-2">
              <input
                type="text"
                value={newGroupName}
                onChange={e => setNewGroupName(e.target.value)}
                className="bg-white/10 text-white px-3 py-1 rounded text-sm"
                placeholder={conversation.name || ''}
              />
              <button
                onClick={handleUpdateGroupName}
                className="text-blue-500 hover:text-blue-400 cursor-pointer"
              >
                <i className="fas fa-check"></i>
              </button>
              <button
                onClick={() => setIsEditingName(false)}
                className="text-red-500 hover:text-red-400 cursor-pointer"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2">
              <h2 className="text-white text-lg font-semibold text-center">{conversation.name}</h2>
              {isAdmin && (
                <button
                  onClick={() => {
                    setNewGroupName(conversation.name || '');
                    setIsEditingName(true);
                  }}
                  className="text-white/60 hover:text-white cursor-pointer"
                >
                  <i className="fas fa-pen text-xs"></i>
                </button>
              )}
            </div>
          )}
        </div>

        {/* Quick Actions Row - More compact */}
        <div className="px-4 py-1 flex justify-around items-center gap-2 mb-2">
          <div className="flex-1 bg-white/5 rounded-lg">
            <button
              onClick={() => handleToggleMute()}
              className="w-full flex items-center justify-center gap-2 text-white/80 hover:text-white py-1.5 cursor-pointer"
            >
              <i className={`fas ${userProfile?.phoneNumber && conversation.members[userProfile.phoneNumber]?.is_muted ? 'fa-bell-slash' : 'fa-bell'} text-sm`}></i>
              <span className="text-xs">{userProfile?.phoneNumber && conversation.members[userProfile.phoneNumber]?.is_muted ? 'Unmute' : 'Mute'}</span>
            </button>
          </div>

          <div className="flex-1 bg-white/5 rounded-lg">
            <button
              onClick={() => setShowConfirmLeave(true)}
              className="w-full flex items-center justify-center gap-2 text-white/80 hover:text-white py-1.5 cursor-pointer"
            >
              <i className="fas fa-sign-out-alt text-sm"></i>
              <span className="text-xs">Leave</span>
            </button>
          </div>
        </div>

        {/* Members Header */}
        <div className="px-3 py-1.5 border-b border-white/10 mb-1">
          <div className="text-white font-medium flex items-center justify-between">
            <span className="text-sm">Members ({members.length})</span>
            {isAdmin && (
              <button
                onClick={handleAddMembers}
                className="text-blue-400 text-xs flex items-center gap-1 hover:text-blue-300 transition-colors"
              >
                <i className="fas fa-user-plus text-[10px]"></i>
                <span>Add</span>
              </button>
            )}
          </div>
        </div>
        {/* Sort members to show admins first */}
        {[...members]
          .sort((a, b) => {
            // Sort by admin status first (admins come first)
            if (a.isAdmin && !b.isAdmin) return -1;
            if (!a.isAdmin && b.isAdmin) return 1;
            // Then sort by name
            return a.name.localeCompare(b.name);
          })
          .map(member => (
            <div
              key={member.phone}
              className="flex items-center justify-between py-1.5 px-2 border-b border-white/10"
            >
              <div
                className="flex items-center gap-1.5 flex-1 hover:bg-white/5 transition-colors cursor-pointer py-1 px-1 rounded-md"
                onClick={() => handleMemberClick(member)}
              >
                <div className="relative">
                  <div className={`w-8 h-8 rounded-full ${member.hasContact ? 'ring-2 ring-blue-500' : 'bg-white/10'} overflow-hidden`}>
                    {member.avatar ? (
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500/30 to-blue-600/30">
                        <span className="text-white text-sm font-medium">
                          {member.name?.[0]?.toUpperCase() || '?'}
                        </span>
                      </div>
                    )}
                  </div>
                  {/* Admin badge */}
                  {member.isAdmin && (
                    <div className="absolute -bottom-0.5 -right-0.5 bg-blue-500 rounded-full w-4 h-4 flex items-center justify-center">
                      <i className="fas fa-crown text-[8px] text-white"></i>
                    </div>
                  )}
                  {/* Contact badge */}
                  {member.hasContact && !member.isAdmin && (
                    <div className="absolute -top-0.5 -right-0.5 bg-green-500 rounded-full w-4 h-4 flex items-center justify-center">
                      <i className="fas fa-address-book text-[8px] text-white"></i>
                    </div>
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-1">
                    <span className={`text-sm truncate ${member.hasContact ? 'text-blue-400' : 'text-white'}`}>
                      {member.name}
                    </span>
                    {member.phone === userProfile?.phoneNumber && (
                      <span className="text-white/40 text-xs">(You)</span>
                    )}
                    {member.hasContact && (
                      <span className="text-green-400 text-[10px]">
                        <i className="fas fa-check"></i>
                      </span>
                    )}
                  </div>
                  <div className="text-white/60 text-xs truncate">{member.phone}</div>
                </div>
              </div>
              <div className="flex items-center gap-1 ml-1 flex-shrink-0">
                {member.isAdmin && (
                  <span className="text-blue-400 text-[10px] px-1.5 py-0.5 bg-blue-500/10 rounded-full">
                    Admin
                  </span>
                )}
                {isAdmin &&
                  // Can't remove yourself or other admins
                  member.phone !== userProfile?.phoneNumber &&
                  !member.isAdmin && (
                    <button
                      onClick={e => {
                        e.stopPropagation(); // Prevent triggering the member click
                        handleRemoveMember(member.phone);
                      }}
                      className="text-red-500 hover:text-red-400 p-1.5 cursor-pointer rounded-full hover:bg-red-500/10"
                    >
                      <i className="fas fa-user-minus text-xs"></i>
                    </button>
                  )}
              </div>
            </div>
          ))}
      </div>

      {/* We no longer need the Add Members Modal as we're using the selection store */}

      {/* Confirm Leave Modal */}
      {showConfirmLeave && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-[#0a0f1a] w-11/12 rounded-lg p-4">
            <h3 className="text-white text-lg font-medium mb-4">Leave Group?</h3>
            <div className="flex justify-end gap-2 mt-4">
              <button
                onClick={() => setShowConfirmLeave(false)}
                className="px-4 py-2 text-white/60 hover:text-white cursor-pointer"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (activeChat) {
                    // Here you would implement the actual leave group functionality
                    console.log('[GroupInfo] Leaving group:', activeChat);

                    // Navigate back to the main messages view
                    openView('main');
                  }
                }}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 rounded text-white cursor-pointer"
              >
                Leave
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupInfoScreen;
