/**
 * Event parameter definitions for game events
 *
 * This file contains a simple mapping of event names to parameter descriptions.
 * Add new parameter definitions as you discover them.
 */

/**
 * Simple mapping of event names to parameter descriptions
 * Key: Event name
 * Value: Array of parameter names or descriptions
 */
export const EventParameters: Record<string, string[]> = {
  // === MOVEMENT EVENTS ===
  CEventClimbLadderOnRoute: [
    'ladderBottom',
    'ladderTop',
    'direction',
    'ladderHeading',
    'moveBlendRatio',
  ],

  CEventClimbNavMeshOnRoute: [
    'climbPosition',
    'climbHeading',
    'climbTarget',
    'moveBlendRatio',
    'wptFlags',
    'entityWeAreOn',
    'warpTimer',
    'warpTarget',
    'forceJump',
  ],

  CEventPotentialBeWalkedInto: [
    'otherPed',
    'target',
    'moveBlendRatio',
  ],

  CEventPotentialGetRunOver: [
    'threatVehicle',
  ],

  CEventPotentialBlast: [
    'target',
    'radius',
    'timeOfExplosion',
  ],

  CEventPotentialWalkIntoFire: [
    'firePos',
    'fireRadius',
    'moveBlendRatio',
  ],

  CEventPotentialWalkIntoObject: [
    'object',
    'moveBlendRatio',
  ],

  CEventPotentialWalkIntoVehicle: [
    'threatVehicle',
    'moveBlendRatio',
    'target',
  ],
  // === NETWORK EVENTS ===
  CEventNetworkEntityDamage: [
    'victimId',
    'damagerId',
    'damage',
    'enduranceDamage',
    'victimIncapacitated',
    'victimDestroyed',
    'weaponHash',
    'victimSpeed',
    'damagerSpeed',
    'isResponsibleForCollision',
    'isHeadShot',
    'withMeleeWeapon',
    'hitMaterial',
  ],

  CEventNetworkPlayerEnteredVehicle: [
    'playerIndex',
    'vehicleIndex',
  ],

  CEventNetworkPlayerActivatedSpecialAbility: [
    'playerIndex',
    'specialAbility',
  ],

  CEventNetworkPlayerDeactivatedSpecialAbility: [
    'playerIndex',
    'specialAbility',
  ],

  CEventNetworkPlayerSpecialAbilityFailedActivation: [
    'playerIndex',
    'specialAbility',
  ],

  CEventNetworkPlayerDied: [
    'playerName',
    'playerIndex',
    'playerTeam',
    'playerWantedLevel',
    'killerName',
    'killerIndex',
    'killerTeam',
    'killedByPlayer',
    'killerWeaponType',
  ],

  CEventNetworkPlayerJoinSession: [
    'playerName',
    'playerIndex',
    'playerTeam',
  ],

  CEventNetworkPlayerLeftSession: [
    'playerName',
    'playerIndex',
    'playerTeam',
  ],

  CEventNetworkPlayerCollectedPickup: [
    'placementReference',
    'playerIndex',
    'pickupHash',
    'pickupAmount',
    'pickupModel',
    'pickupCollected',
  ],

  CEventNetworkPlayerCollectedAmbientPickup: [
    'pickupHash',
    'pickupAmount',
    'playerIndex',
    'pickupModel',
    'playerGift',
    'droppedByPed',
    'pickupCollected',
    'pickupIndex',
  ],

  CEventNetworkPlayerCollectedPortablePickup: [
    'pickupID',
    'pickupNetID',
    'playerIndex',
    'pickupModel',
  ],

  CEventNetworkPlayerArrest: [
    'arresterIndex',
    'arresteeIndex',
    'arrestType',
  ],

  CEventNetworkVehicleUndrivable: [
    'vehicleId',
    'damagerId',
    'weaponUsed',
  ],

  CEventNetworkTimedExplosion: [
    'vehicleIndex',
    'culpritIndex',
  ],

  CEventNetworkFiredDummyProjectile: [
    'firingPedIndex',
    'firedProjectileIndex',
    'weaponType',
  ],

  CEventNetworkFiredVehicleProjectile: [
    'firingVehicleIndex',
    'firingPedIndex',
    'firedProjectileIndex',
    'weaponType',
  ],

  // === COMBAT EVENTS ===
  CEventGunShot: [
    'shooterId',
    'weaponHash',
  ],

  CEventExplosion: [
    'position',
    'owner',
    'radius',
    'isMinor',
  ],

  // === PLAYER EVENTS ===
  CEventPlayerDeath: [
    'playerId',
    'killerId',
    'weaponHash',
  ],

  CEventRevived: [
    'treatingMedic',
    'ambulance',
    'revived',
  ],

  CEventPedToChase: [
    'pedToChase',
  ],

  CEventPedToFlee: [
    'pedToFlee',
  ],

  CEventDamage: [
    'inflictor',
    'time',
    'weaponUsedHash',
  ],

  CEventDeath: [
    'hasDrowned',
    'useRagdoll',
  ],

  CEventWrithe: [
    'inflictor',
    'fromGetUp',
  ],

  CEventHurtTransition: [
    'target',
  ],

  // === VEHICLE EVENTS ===
  CEventPedEnteredMyVehicle: [
    'pedThatEnteredVehicle',
    'vehicle',
  ],

  CEventVehicleOnFire: [
    'vehicleOnFire',
  ],

  // === ACQUAINTANCE EVENTS ===
  CEventAcquaintancePed: [
    'acquaintancePed',
  ],

  // === OTHER EVENTS ===
  CEventCrimeCryForHelp: [
    'assaultedPed',
    'assaultingPed',
  ],

  CEventShoutBlockingLos: [
    'pedShouting',
    'target',
  ],

  // === SCRIPT EVENTS ===
  CEventCrimeReported: [
    'bChangedWantedLevel',
    'crimeType',
  ],

  CEventEntityDamaged: [
    'entityId',
  ],

  CEventEntityDestroyed: [
    'entityId',
  ],

  CEventPlayerUnableToEnterVehicle: [
    'vehicleId',
  ],

  CEventPedSeenDeadPed: [
    'deadPedId',
    'findingPedId',
  ],

  CEventStatChangedValue: [
    'statHash',
  ],

  // === SHOCKING EVENTS ===
  CEventShockingBrokenGlass: [
    'smasher',
    'position',
  ],

  CEventShockingCarChase: [
    'chaser',
  ],

  CEventShockingCarCrash: [
    'crashedEntity',
    'inflictor',
  ],

  CEventShockingBicycleCrash: [
    'crashedEntity',
    'inflictor',
  ],

  CEventShockingCarPileUp: [
    'vehicle',
  ],

  CEventShockingCarOnCar: [
    'driver',
    'vehicle',
  ],

  CEventShockingDeadBody: [
    'deadBody',
  ],

  CEventShockingDrivingOnPavement: [
    'vehicle',
  ],

  CEventShockingBicycleOnPavement: [
    'vehicle',
  ],

  CEventShockingExplosion: [
    'explosionOwner',
    'explosionPos',
  ],

  CEventShockingFire: [
    'entityOnFire',
    'firePos',
  ],

  CEventShockingGunFight: [],

  CEventShockingGunshotFired: [
    'firingEntity',
  ],

  CEventShockingHelicopterOverhead: [
    'heli',
  ],

  CEventShockingParachuterOverhead: [
    'parachuter',
  ],

  CEventShockingDangerousAnimal: [
    'dangerousAnimal',
  ],

  CEventShockingEngineRevved: [
    'vehicle',
  ],

  CEventShockingHornSounded: [
    'vehicle',
  ],

  CEventShockingInDangerousVehicle: [
    'dangerousVehicle',
  ],

  CEventShockingInjuredPed: [
    'pedInjured',
    'inflictor',
  ],

  CEventShockingMadDriver: [
    'madDriver',
  ],

  CEventShockingMadDriverExtreme: [
    'madDriver',
  ],

  CEventShockingMadDriverBicycle: [
    'madDriver',
  ],

  CEventShockingMugging: [
    'mugger',
  ],

  CEventShockingNonViolentWeaponAimedAt: [
    'aimer',
  ],

  CEventShockingPedKnockedIntoByPlayer: [
    'knockingPed',
    'pedKnockedInto',
  ],

  CEventShockingPedRunOver: [
    'pedRunOver',
    'inflictor',
  ],

  CEventShockingPedShot: [
    'shooter',
    'victim',
  ],

  CEventShockingPlaneFlyby: [
    'plane',
  ],

  CEventShockingPropertyDamage: [
    'culprit',
    'property',
  ],

  CEventShockingRunningPed: [
    'runner',
  ],

  CEventShockingRunningStampede: [],

  CEventShockingSeenCarStolen: [
    'thief',
    'vehicle',
  ],

  CEventShockingSeenConfrontation: [
    'confronter',
    'victim',
  ],

  CEventShockingSeenGangFight: [],

  CEventShockingSeenInsult: [
    'insulter',
    'victim',
  ],

  CEventShockingSeenMeleeAction: [
    'attacker',
    'victim',
  ],

  CEventShockingSeenNiceCar: [
    'vehicle',
  ],

  CEventShockingSeenPedKilled: [
    'attacker',
    'victim',
  ],

  CEventShockingSiren: [
    'source',
  ],

  CEventShockingCarAlarm: [
    'source',
  ],

  // === DAMAGE EVENTS ===
  CEventIncapacitated: [],

  // === VEHICLE EVENTS ===
  CEventVehicleCollision: [
    'vehicle',
    'collidedWith',
    'collisionForce',
    'damageToVehicle',
  ],

  CEventVehicleDamage: [
    'vehicle',
    'damager',
    'damageAmount',
    'weaponHash',
  ],

  CEventVehicleDestroyed: [
    'vehicle',
  ],

  CEventVehicleEmpty: [
    'vehicle',
  ],

  CEventVehicleUndrivable: [
    'vehicle',
    'damager',
    'weaponHash',
  ],

  // === WEAPON EVENTS ===
  CEventWeaponFired: [
    'shooter',
    'weaponHash',
    'ammoCount',
  ],

  CEventWeaponDamage: [
    'victim',
    'attacker',
    'weaponHash',
    'damage',
  ],

  // === AUDIO EVENTS ===
  CEventAudioFootstep: [
    'ped',
    'surface',
    'intensity',
  ],

  CEventAudioVehicleHorn: [
    'vehicle',
    'duration',
  ],

  CEventAudioAlarm: [
    'source',
    'duration',
  ],

  // === MISC EVENTS ===
  CEventAreaTrigger: [
    'areaId',
    'entityEntering',
  ],

  CEventObjectCollision: [
    'object',
    'collidedWith',
    'collisionForce',
  ],

  CEventWeatherChange: [
    'oldWeather',
    'newWeather',
  ],

  CEventTimeChange: [
    'oldTime',
    'newTime',
  ],

  // === ACQUAINTANCE EVENTS ===
  CEventAcquaintancePedDislike: [
    'acquaintancePed',
  ],

  CEventAcquaintancePedHate: [
    'acquaintancePed',
  ],

  CEventAcquaintancePedLike: [
    'acquaintancePed',
  ],

  CEventAcquaintancePedRespect: [
    'acquaintancePed',
  ],

  CEventAcquaintancePedWanted: [
    'acquaintancePed',
  ],

  CEventAcquaintancePedDead: [
    'acquaintancePed',
  ],

  // === AGITATION EVENTS ===
  CEventAgitated: [
    'source',
    'level',
  ],

  CEventAgitatedAction: [
    'source',
    'action',
  ],

  // === ENCROACHMENT EVENTS ===
  CEventEncroachingPed: [
    'encroachingPed',
  ],

  // === CALL EVENTS ===
  CEventCallForCover: [
    'caller',
    'position',
  ],

  // === VEHICLE EVENTS ===
  CEventCarUndriveable: [
    'vehicle',
    'reason',
  ],

  CEventCopCarBeingStolen: [
    'vehicle',
    'thief',
  ],

  // === COMBAT EVENTS ===
  CEventCombatTaunt: [
    'taunter',
    'target',
  ],

  // === COMMUNICATION EVENTS ===
  CEventCommunicateEvent: [
    'sender',
    'message',
    'recipient',
  ],

  // === INJURY EVENTS ===
  CEventInjuredCryForHelp: [
    'injuredPed',
    'position',
  ],

  // === WEAPON EVENTS ===
  CEventGunAimedAt: [
    'aggressorPed',
  ],

  CEventGunShotWhizzedBy: [
    'firingEntity',
    'shotOrigin',
    'shotTarget',
    'isSilent',
    'weaponHash',
  ],

  CEventGunShotBulletImpact: [
    'firingEntity',
    'shotOrigin',
    'shotTarget',
    'isSilent',
    'weaponHash',
    'hitPed',
  ],

  CEventMeleeAction: [
    'actingEntity',
    'hitEntity',
    'isOffensive',
  ],

  CEventFriendlyAimedAt: [
    'aggressorPed',
  ],

  CEventFriendlyFireNearMiss: [
    'firingEntity',
    'shotOrigin',
    'shotTarget',
    'isSilent',
    'weaponHash',
    'type',
  ],

  // === ENVIRONMENTAL EVENTS ===

  CEventExplosionHeard: [
    'position',
    'owner',
    'radius',
  ],

  CEventFireNearby: [
    'position',
    'radius',
  ],

  // === MOVEMENT EVENTS ===
  CEventFootStepHeard: [
    'ped',
    'position',
    'intensity',
  ],

  CEventGetOutOfWater: [
    'ped',
    'position',
  ],

  CEventInAir: [
    'ped',
    'height',
  ],

  CEventInWater: [
    'ped',
    'depth',
  ],

  // === LEADER EVENTS ===
  CEventLeaderEnteredCarAsDriver: [
    'leader',
    'vehicle',
  ],

  CEventLeaderEnteredCover: [
    'leader',
    'coverPosition',
  ],

  CEventLeaderExitedCarAsDriver: [
    'leader',
    'vehicle',
  ],

  CEventLeaderHolsteredWeapon: [
    'leader',
    'weaponHash',
  ],

  CEventLeaderLeftCover: [
    'leader',
    'coverPosition',
  ],

  CEventLeaderUnholsteredWeapon: [
    'leader',
    'weaponHash',
  ],

  // === OBJECT EVENTS ===
  CEventOpenDoor: [
    'ped',
    'door',
  ],

  CEventShovePed: [
    'shover',
    'victim',
  ],

  // === COLLISION EVENTS ===
  CEventPedCollisionWithPed: [
    'ped1',
    'ped2',
    'force',
  ],

  CEventPedCollisionWithPlayer: [
    'ped',
    'player',
    'force',
  ],

  CEventPlayerCollisionWithPed: [
    'player',
    'ped',
    'force',
  ],

  // === VEHICLE INTERACTION EVENTS ===
  CEventPedJackingMyVehicle: [
    'jacker',
    'vehicle',
  ],

  CEventPedOnCarRoof: [
    'ped',
    'vehicle',
  ],

  // === PLAYER EVENTS ===
  CEventPlayerLockOnTarget: [
    'player',
    'target',
  ],

  // === RADIO EVENTS ===
  CEventRadioTargetPosition: [
    'sender',
    'position',
  ],

  // === REACTION EVENTS ===
  CEventReactionCombatVictory: [
    'victor',
    'defeated',
  ],

  CEventReactionEnemyPed: [
    'ped',
    'enemy',
  ],

  CEventReactionInvestigateDeadPed: [
    'investigator',
    'deadPed',
  ],

  CEventReactionInvestigateThreat: [
    'investigator',
    'threat',
  ],

  // === HELP EVENTS ===
  CEventRequestHelpWithConfrontation: [
    'requester',
    'target',
  ],

  // === RESPONSE EVENTS ===
  CEventRespondedToThreat: [
    'responder',
    'threat',
  ],

  // === SCRIPT EVENTS ===
  CEventScriptCommand: [
    'command',
    'params',
  ],

  // === SUSPICIOUS EVENTS ===
  CEventSuspiciousActivity: [
    'ped',
    'activity',
  ],

  // === UNIDENTIFIED EVENTS ===
  CEventUnidentifiedPed: [
    'ped',
    'position',
  ],

  // === WHISTLING EVENTS ===
  CEventWhistlingHeard: [
    'whistler',
    'position',
  ],

  // === DISTURBANCE EVENTS ===
  CEventDisturbance: [
    'source',
    'position',
  ],

  // === NETWORK EVENTS ===
  CEventNetworkPlayerJoinScript: [],

  CEventNetworkPlayerLeftScript: [],

  CEventNetworkStartSession: [],

  CEventNetworkEndSession: [],

  CEventNetworkPickupRespawned: [],

  CEventNetworkPlayerDroppedPortablePickup: [],

  CEventNetworkInviteAccepted: [],

  CEventNetworkInviteConfirmed: [],

  CEventNetworkInviteRejected: [],

  CEventNetworkPedLeftBehind: [],

  CEventNetworkSpectateLocal: [],

  CEventNetworkScriptEvent: [],

  CEventNetworkVoiceSessionStarted: [],

  CEventNetworkVoiceSessionEnded: [],

  CEventNetworkVoiceConnectionRequested: [],

  CEventNetworkVoiceConnectionResponse: [],

  CEventNetworkVoiceConnectionTerminated: [],

  CEventNetworkClanInviteReceived: [],

  CEventNetworkClanJoined: [],

  CEventNetworkClanLeft: [],

  CEventNetworkClanKicked: [],

  CEventNetworkClanRankChanged: [],

  CEventNetworkPrimaryClanChanged: [],

  // === SYSTEM EVENTS ===
  CEventStaticCountReachedMax: [],

  CEventScriptWithData: [],

  CEventDataDecisionMaker: [],

  CEventDataFileMounter: [],

  CEventDecisionMakerResponse: [],

  CEventEditableResponse: [],

  CEventInfo: [],

  CEventInfoBase: [],

  CEventSwitch2NM: [],

  CEventSoundBase: [],

  // === RESPONSE EVENTS ===
  CEventDataResponseAggressiveRubberneck: [],

  CEventDataResponseDeferToScenarioPointFlags: [],

  CEventDataResponseFriendlyAimedAt: [],

  CEventDataResponseFriendlyNearMiss: [],

  CEventDataResponsePlayerDeath: [],

  CEventDataResponsePoliceTaskWanted: [],

  CEventDataResponseSwatTaskWanted: [],

  CEventDataResponseTask: [],

  CEventDataResponseTaskAgitated: [],

  CEventDataResponseTaskCombat: [],

  CEventDataResponseTaskCower: [],

  CEventDataResponseTaskCrouch: [],

  CEventDataResponseTaskDuckAndCover: [],

  CEventDataResponseTaskEscapeBlast: [],

  CEventDataResponseTaskEvasiveStep: [],

  CEventDataResponseTaskExhaustedFlee: [],

  CEventDataResponseTaskExplosion: [],

  CEventDataResponseTaskFlee: [],

  CEventDataResponseTaskFlyAway: [],

  CEventDataResponseTaskGrowlAndFlee: [],

  CEventDataResponseTaskGunAimedAt: [],

  CEventDataResponseTaskHandsUp: [],

  CEventDataResponseTaskHeadTrack: [],

  CEventDataResponseTaskLeaveCarAndFlee: [],

  CEventDataResponseTaskScenarioFlee: [],

  CEventDataResponseTaskSharkAttack: [],

  CEventDataResponseTaskShockingEventBackAway: [],

  CEventDataResponseTaskShockingEventGoto: [],

  CEventDataResponseTaskShockingEventHurryAway: [],

  CEventDataResponseTaskShockingEventReact: [],

  CEventDataResponseTaskShockingEventReactToAircraft: [],

  CEventDataResponseTaskShockingEventStopAndStare: [],

  CEventDataResponseTaskShockingEventThreatResponse: [],

  CEventDataResponseTaskShockingEventWatch: [],

  CEventDataResponseTaskShockingNiceCar: [],

  CEventDataResponseTaskShockingPoliceInvestigate: [],

  CEventDataResponseTaskThreat: [],

  CEventDataResponseTaskTurnToFace: [],

  CEventDataResponseTaskWalkAway: [],

  CEventDataResponseTaskWalkRoundEntity: [],

  CEventDataResponseTaskWalkRoundFire: [],

  // === OTHER EVENTS ===
  CEventDeadPedFound: [],

  CEventDraggedOutCar: [],

  CEventGivePedTask: [],

  CEventGroupScriptAI: [],

  CEventGroupScriptNetwork: [],

  CEventHelpAmbientFriend: [],

  CEventNewTask: [],

  CEventProvidingCover: [],

  CEventRequestHelp: [],

  CEventScanner: [],

  CEventScenarioForceAction: [],

  CEventShoutTargetPosition: [],

  // === SHOCKING EVENTS ===
  CEventShocking: [],

  CEventShockingStudioBomb: [],

  CEventShockingVehicleTowed: [],

  CEventShockingVisibleWeapon: [],

  CEventShockingWeaponThreat: [],

  CEventShockingWeirdPed: [],

  CEventShockingWeirdPedApproaching: [],

  // === VEHICLE EVENTS ===
  CEventVehicleDamageWeapon: [],

  // === PLAYER EVENTS ===
  CEventRanOverPed: [],

  CEventStuckInAir: [],

  // === COMBAT EVENTS ===
  CEventEntityDamage: [],

  // Add more event parameter definitions as you discover them
};

/**
 * Format event arguments with parameter names if available
 * @param eventName The name of the event
 * @param args The event arguments
 * @returns A formatted string representation of the arguments
 */
export function formatEventArgs(eventName: string, args: any[]): string {
  const paramNames = EventParameters[eventName];

  if (!paramNames || paramNames.length === 0) {
    // No parameter definitions found, return a generic format
    return formatGenericArgs(args);
  }

  // Format arguments with parameter names
  const formattedParams: string[] = [];

  for (let i = 0; i < Math.min(args.length, paramNames.length); i++) {
    const paramName = paramNames[i];
    const value = args[i];

    // Format special types
    let formattedValue: string;

    // Entity IDs (peds, players)
    if (paramName.includes('Id') || paramName.includes('Ped') ||
        paramName.includes('Player') || paramName.includes('Attacker') ||
        paramName.includes('Victim') || paramName.includes('Shooter') ||
        paramName.includes('Killer') || paramName.includes('Owner') ||
        paramName.includes('Driver') || paramName.includes('Medic') ||
        paramName === 'source' || paramName === 'target' ||
        paramName === 'inflictor' || paramName === 'entityEntering') {
      formattedValue = formatEntity(value);
    }
    // Vehicle IDs
    else if (paramName.includes('Vehicle') || paramName === 'ambulance' ||
             paramName === 'heli' || paramName === 'plane') {
      formattedValue = `Vehicle(${value})`;
    }
    // Weapon hashes
    else if (paramName.includes('weapon') || paramName.includes('Weapon')) {
      formattedValue = formatWeapon(value);
    }
    // Boolean values (0/1)
    else if (paramName.startsWith('is') || paramName.startsWith('has') ||
             paramName.startsWith('can') || paramName.startsWith('should') ||
             paramName === 'victimIncapacitated' || paramName === 'victimDestroyed' ||
             paramName === 'withMeleeWeapon' || paramName === 'revived' ||
             paramName === 'isResponsibleForCollision') {
      formattedValue = value === 1 || value === true ? 'Yes' : 'No';
    }
    // Damage values
    else if (paramName.includes('damage') || paramName.includes('Damage') ||
             paramName.includes('health') || paramName.includes('Health') ||
             paramName.includes('armour') || paramName.includes('Armour')) {
      formattedValue = `${value}`;
    }
    // Speed and distance values
    else if (paramName.includes('speed') || paramName.includes('Speed') ||
             paramName.includes('radius') || paramName.includes('Radius') ||
             paramName.includes('distance') || paramName.includes('Distance')) {
      formattedValue = `${value} units`;
    }
    // Time values
    else if (paramName.includes('time') || paramName.includes('Time') ||
             paramName.includes('duration') || paramName.includes('Duration')) {
      formattedValue = `${value} ms`;
    }
    // Position vectors
    else if (paramName.includes('position') || paramName.includes('Position') ||
             paramName.includes('location') || paramName.includes('Location') ||
             paramName.includes('coord') || paramName.includes('Coord')) {
      if (Array.isArray(value)) {
        formattedValue = `[${value.join(', ')}]`;
      } else {
        formattedValue = `Vector3(${value})`;
      }
    }
    // Hash values
    else if (paramName.includes('hash') || paramName.includes('Hash') ||
             paramName.includes('material') || paramName.includes('Material')) {
      formattedValue = `${paramName}(${value})`;
    }
    // Special ability
    else if (paramName.includes('ability') || paramName.includes('Ability')) {
      formattedValue = `Ability(${value})`;
    }
    // Object IDs
    else if (paramName.includes('object') || paramName.includes('Object') ||
             paramName.includes('prop') || paramName.includes('Prop')) {
      formattedValue = `Object(${value})`;
    }
    // Regular value
    else {
      formattedValue = formatValue(value);
    }

    formattedParams.push(`${paramName}: ${formattedValue}`);
  }

  // If there are more args than defined parameters, add them as unnamed
  if (args.length > paramNames.length) {
    const remaining = args.length - paramNames.length;
    formattedParams.push(`+${remaining} more args`);
  }

  return formattedParams.join(', ');
}

/**
 * Format generic arguments without parameter definitions
 * @param args The event arguments
 * @returns A formatted string representation of the arguments
 */
function formatGenericArgs(args: any[]): string {
  const maxArgsToShow = 4;
  const shownArgs = args.slice(0, maxArgsToShow);
  const argsStr = shownArgs.map((arg, index) => {
    return `arg${index}: ${formatValue(arg)}`;
  }).join(', ');

  const remaining = args.length > maxArgsToShow ? ` (+${args.length - maxArgsToShow} more)` : '';
  return `${argsStr}${remaining}`;
}

/**
 * Format a value based on its type
 * @param value The value to format
 * @returns A formatted string representation of the value
 */
function formatValue(value: any): string {
  if (typeof value === 'number' || typeof value === 'boolean') {
    return value.toString();
  } else if (typeof value === 'string') {
    return `"${value}"`;
  } else if (value === null) {
    return 'null';
  } else if (value === undefined) {
    return 'undefined';
  } else {
    return 'object';
  }
}

/**
 * Format an entity ID with type information if available
 * @param entityId The entity ID
 * @returns A formatted string representation of the entity
 */
function formatEntity(entityId: number): string {
  if (!entityId) return 'None';

  let entityType = 'Entity';

  // Check entity type if we're on the client side
  if (typeof DoesEntityExist === 'function' && DoesEntityExist(entityId)) {
    if (typeof IsEntityAPed === 'function' && IsEntityAPed(entityId)) {
      entityType = typeof IsPedAPlayer === 'function' && IsPedAPlayer(entityId) ? 'Player' : 'Ped';
    } else if (typeof IsEntityAVehicle === 'function' && IsEntityAVehicle(entityId)) {
      entityType = 'Vehicle';
    } else if (typeof IsEntityAnObject === 'function' && IsEntityAnObject(entityId)) {
      entityType = 'Object';
    }
  }

  return `${entityType}(${entityId})`;
}

/**
 * Format a weapon hash with name if available
 * @param weaponHash The weapon hash
 * @returns A formatted string representation of the weapon
 */
function formatWeapon(weaponHash: number): string {
  // This could be expanded with a mapping of weapon hashes to names
  return `Weapon(${weaponHash})`;
}