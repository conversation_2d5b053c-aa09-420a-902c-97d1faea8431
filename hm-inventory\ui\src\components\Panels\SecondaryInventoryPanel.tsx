import React, {useState, useRef } from 'react';
import InventoryGrid from '../InventoryGrid/InventoryGrid'; // Assuming InventoryGrid can be reused
import * as SharedTypes from '@shared';
import { PanelType } from '@/stores/inventoryStore';

interface SecondaryInventoryPanelProps {
  title: string;
  rows: number;
  columns: number;
  instanceId: string; // Unique ID for this instance of secondary inventory
  panelType?: PanelType; // Optional, if you want to specify the type of panel
  initiallyOpen?: boolean;
  className?: string;
  items?: SharedTypes.ItemSlot[];
}

const SecondaryInventoryPanel: React.FC<SecondaryInventoryPanelProps> = ({
  title = 'title',
  rows = 1,
  columns = 4,
  instanceId = 'ground',
  initiallyOpen = true,
  className = '',
  items = [],
}) => {  const [expanded, setExpanded] = useState(initiallyOpen);
  const [contentHeight] = useState<number | undefined>(undefined);
  const contentRef = useRef<HTMLDivElement>(null);

  if (!initiallyOpen) {
    return null;
  }

  // if (!currentInventory) {
  //   return <div className={`p-4 bg-neutral-800 rounded-2xl shadow-xl border-l-4 border-green-400/10 ${className}`}>Loading {title}...</div>;
  // }
  return (
    <div
      className={`relative p-4 bg-neutral-800 rounded-2xl shadow-xl flex flex-col gap-2 ${className} overflow-hidden border-l-4 border-green-400/10 transition-all duration-700 ${expanded ? 'shadow-green-400/10' : ''} group`}
    >
      {/* Green gradient bar at the top */}
      <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-green-400/10 via-green-500/10 to-green-600/10 opacity-30 z-10 rounded-t-2xl pointer-events-none" />
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-bold text-neutral-100 tracking-wide flex items-center gap-2">
          <i className="fas fa-box-open text-green-400/80" />
          {title}
        </h3>
        <button
          onClick={() => setExpanded(e => !e)}
          className="text-neutral-400 hover:text-green-400 transition-colors focus:outline-none focus:ring-2 focus:ring-green-400 rounded-full p-1"
          title={expanded ? 'Collapse' : 'Expand'}
        >
          <i className={`fas fa-chevron-${expanded ? 'up' : 'down'} transition-transform duration-500 ${expanded ? 'rotate-180 text-green-400' : ''}`}></i>
        </button>
      </div>
      <div
        ref={contentRef}
        className="transition-all duration-700 ease-[cubic-bezier(0.77,0,0.18,1)] overflow-hidden"
        style={{
          maxHeight: expanded ? contentHeight : 0,
          opacity: expanded ? 1 : 0,
          transform: expanded ? 'scaleY(1)' : 'scaleY(0.97)',
          transitionProperty: 'max-height, opacity, transform',
        }}
      >
        {expanded && (          <div className="pt-2">
            <InventoryGrid
              columns={columns}
              PanelType={'main'}
              instanceId={instanceId}
              items={items}
              slotCount={rows * columns} // Corrected slotCount
              visibleRows={rows} // Add visible rows prop
            />
          </div>
        )}
      </div>
      {/* Add other elements like weight/capacity display if needed */}
    </div>
  );
};

export default SecondaryInventoryPanel;