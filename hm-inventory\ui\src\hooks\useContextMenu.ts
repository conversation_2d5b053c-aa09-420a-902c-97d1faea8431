import { useMemo } from 'react';
import { InventoryItem } from '../../../scripts/shared/types/inventory.types';
import { 
  ContextMenuConfig, 
  DEFAULT_CONTEXT_MENU_CONFIG, 
  generateContextMenuOptions,
  executeContextMenuAction
} from '../config/contextMenuConfig';

interface UseContextMenuOptions {
  customConfig?: Partial<ContextMenuConfig>;
  onActionExecuted?: (action: string, item: InventoryItem) => void;
}

export const useContextMenu = (options: UseContextMenuOptions = {}) => {
  const { customConfig, onActionExecuted } = options;

  // Merge custom config with default
  const config = useMemo(() => {
    if (!customConfig) return DEFAULT_CONTEXT_MENU_CONFIG;

    return {
      universal: customConfig.universal || DEFAULT_CONTEXT_MENU_CONFIG.universal,
      typeSpecific: { 
        ...DEFAULT_CONTEXT_MENU_CONFIG.typeSpecific, 
        ...customConfig.typeSpecific 
      },
      itemSpecific: { 
        ...DEFAULT_CONTEXT_MENU_CONFIG.itemSpecific, 
        ...customConfig.itemSpecific 
      },
      conditional: customConfig.conditional || DEFAULT_CONTEXT_MENU_CONFIG.conditional
    };
  }, [customConfig]);

  const getMenuOptions = (item: InventoryItem, quantity: number) => {
    return generateContextMenuOptions(item, quantity, config);
  };

  const executeAction = (
    action: string,
    item: InventoryItem,
    quantity: number,
    slotIndex: number,
    panelType?: string,
    panelId?: string
  ) => {
    executeContextMenuAction(action, item, quantity, slotIndex, panelType, panelId);
    onActionExecuted?.(action, item);
  };

  return {
    getMenuOptions,
    executeAction,
    config
  };
};

// Convenience hook for specific use cases
export const useWeaponContextMenu = () => {
  return useContextMenu({
    customConfig: {
      universal: [
        {
          id: 'inspect',
          label: 'Inspect Weapon',
          action: 'inspect',
          className: 'text-blue-400'
        },
        {
          id: 'give',
          label: 'Give',
          action: 'give'
        },
        {
          id: 'drop',
          label: 'Drop',
          action: 'drop',
          className: 'text-red-400',
          dividerAfter: true
        }
      ]
    }
  });
};

export const useConsumableContextMenu = () => {
  return useContextMenu({
    customConfig: {
      universal: [
        {
          id: 'consume',
          label: 'Consume',
          action: 'use',
          condition: (item) => item.usable === true,
          className: 'text-green-400'
        },
        {
          id: 'give',
          label: 'Give',
          action: 'give'
        },
        {
          id: 'drop',
          label: 'Drop',
          action: 'drop',
          className: 'text-red-400'
        }
      ]
    }
  });
};
