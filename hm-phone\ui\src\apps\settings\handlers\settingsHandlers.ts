/**
 * Settings app message handlers
 */

import { registerEventHandler } from '../../../fivem/clientEventReceiver';
import { useSettingsStore } from '../stores/settingsStore';
import { PhoneSettings } from '../types/settingsTypes';

// Register handler for phone settings data
registerEventHandler('settings', 'phoneSettings', data => {
  console.log('[Settings] Received phone settings data:', data);

  // Validate data
  if (typeof data === 'object' && data !== null) {
    // Update the store with the settings data
    if (
      'wallpaper' in data &&
      'ringtone' in data &&
      'notificationSound' in data &&
      'theme' in data
    ) {
      useSettingsStore.getState().setPhoneSettings(data as PhoneSettings);
    }
  } else {
    console.error('[Settings] Received invalid phone settings data:', data);
  }
});
