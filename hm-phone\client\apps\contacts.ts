/**
 * Contacts app client-side logic
 */

import { registerApp<PERSON><PERSON><PERSON> } from '../nui';
import { Contact, ContactShareRequest, ContactShareStatus, Call } from '@shared/types';

/**
 * Initialize the contacts app
 */
export function initializeContactsApp(): void {
    console.log('[Contacts] Initializing client-side contacts app');
    // Register NUI handlers
    registerNUIHandlers();
    // Register client events
    registerClientEvents();
}

/**
 * Register NUI handlers for the contacts app
 */
function registerNUIHandlers(): void {
    // Register handler for getting contacts
    registerAppHandler('contacts', 'getContacts', async () => {
        console.log('[Contacts] Received getContacts request from UI');
        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId || global.playerStateId;

            console.log(`[Contacts] Getting contacts for player: ${stateid || identifier}`);

            // Get contacts from the server
            emitNet('hm-phone:getContacts');
            // The actual contacts will be sent via the hm-phone:contacts event
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error getting contacts:', error);
            return { success: false, error: 'Failed to get contacts' };
        }
    });
    // Register handler for adding a contact
    registerAppHandler('contacts', 'addContact', async (data: Contact) => {
        console.log('[Contacts] Received addContact request from UI:', data);

        try {
            // Add the contact to the server
            emitNet('hm-phone:addContact', data);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error adding contact:', error);
            return { success: false, error: 'Failed to add contact' };
        }
    });
    // Register handler for deleting a contact
    registerAppHandler('contacts', 'deleteContact', async (data: { id: number }) => {
        console.log('[Contacts] Received deleteContact request from UI:', data);

        try {
            // Delete the contact from the server
            emitNet('hm-phone:deleteContact', data.id);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error deleting contact:', error);
            return { success: false, error: 'Failed to delete contact' };
        }
    });
    // Register handler for updating a contact
    registerAppHandler('contacts', 'updateContact', async (data: Contact) => {
        console.log('[Contacts] Received updateContact request from UI:', data);

        try {
            // Update the contact on the server
            emitNet('hm-phone:updateContact', data);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error updating contact:', error);
            return { success: false, error: 'Failed to update contact' };
        }
    });
    // Register handler for toggling favorite status of a contact
    registerAppHandler('contacts', 'toggleFavorite', async (data: { id: number }) => {
        console.log('[Contacts] Received toggleFavorite request from UI:', data);

        try {
            // Toggle the favorite status of the contact on the server
            emitNet('hm-phone:toggleFavorite', data.id);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error toggling favorite status:', error);
            return { success: false, error: 'Failed to toggle favorite status' };
        }
    });
    // Register handler for sharing a contact
    registerAppHandler('contacts', 'shareContact', async (data: { contactId: number; targetPlayerId: number }) => {
        console.log('[Contacts] Received shareContact request from UI:', data);

        try {
            // Share the contact with the target player on the server
            emitNet('hm-phone:shareContact', data.contactId, data.targetPlayerId);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error sharing contact:', error);
            return { success: false, error: 'Failed to share contact' };
        }
    });
    // Register handler for accepting a contact share request
    registerAppHandler('contacts', 'acceptContactShare', async (data: ContactShareRequest) => {
        console.log('[Contacts] Received acceptContactShare request from UI:', data);
        try {
            // Accept the contact share request on the server
            emitNet('hm-phone:acceptContactShare', data);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error accepting contact share:', error);
            return { success: false, error: 'Failed to accept contact share' };
        }
    });
    registerAppHandler('contacts', 'declineContactShare', async (data: ContactShareRequest) => {
        console.log('[Contacts] Received declineContactShare request from UI:', data);
        try {
            // Decline the contact share request on the server
            emitNet('hm-phone:declineContactShare', data);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error declining contact share:', error);
            return { success: false, error: 'Failed to decline contact share' };
        }
    });

    // Register handler for getting call history
    registerAppHandler('dialer', 'getCalls', async () => {
        console.log('[Contacts] Received getCalls request from UI');
        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Contacts] Getting call history for player: ${stateid || identifier}`);

            // Get call history from the server
            emitNet('hm-phone:getCallHistory');

            // Return success to the UI
            // The actual call history will be sent via the hm-phone:callHistory event
            return { success: true };
        } catch (error) {
            console.error('[Contacts] Error getting call history:', error);
            return { success: false, error: 'Failed to get call history' };
        }
    });
}

/**
 * Register client events for the contacts app
 */
function registerClientEvents(): void {
    // Register event for contacts data
    onNet('hm-phone:setContacts', (contactsData: Contact[]) => {
        console.log('[Contacts] Contacts data received:', contactsData.length);

        // Send the contacts data to the UI
        sendToUI('setContacts', contactsData);
    });
    // Register event for contact added
    onNet('hm-phone:contactAdded', (contactData: Contact) => {
        console.log('[Contacts] Contact added event received:', contactData);

        // Send the contact data to the UI
        sendToUI('contactAdded', contactData);
    });
    // Register event for contact updated
    onNet('hm-phone:contactUpdated', (contactData: Contact) => {
        console.log('[Contacts] Contact updated event received:', contactData);

        // Send the contact data to the UI
        sendToUI('contactUpdated', contactData);
    });
    // Register event for contact deleted
    onNet('hm-phone:contactDeleted', (contactId: number) => {
        console.log('[Contacts] Contact deleted event received:', contactId);

        // Send the contact ID to the UI
        sendToUI('contactDeleted', contactId);
    });
    // Register event for contact share request
    onNet('hm-phone:contactShareRequest', (contactData: ContactShareRequest) => {
        console.log('[Contacts] Contact share request received:', contactData);

        // Send the contact data to the UI
        sendToUI('contactShareRequest', contactData);
    });
    // Register event for contact share sent confirmation
    onNet('hm-phone:contactShareSent', (status: ContactShareStatus) => {
        console.log('[Contacts] Contact share sent confirmation received:', status);

        // Send the status to the UI
        sendToUI('contactShareSent', status);
    });
    // Register event for contact share status update
    onNet('hm-phone:contactShareUpdated', (status: ContactShareStatus) => {
        console.log('[Contacts] Contact share status update received:', status);

        // Send the status to the UI
        sendToUI('contactShareUpdated', status);
    });
    // Register event for contact share accepted
    onNet('hm-phone:contactShareAccepted', (contactId: number) => {
        console.log('[Contacts] Contact share accepted received:', contactId);

        // Send the contact ID to the UI
        sendToUI('contactShareAccepted', contactId);
    });

    // Register event for call history
    onNet('hm-phone:callHistory', (callHistory: Call[]) => {
        console.log('[Contacts] Call history received:', callHistory.length);

        // Send the call history to the UI
        // Note: We need to send it to the contacts app, not the dialer app
        SendNUIMessage({
            app: 'contacts',
            type: 'calls',
            data: callHistory
        });
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI<T>(type: string, data: T): void {
    SendNUIMessage({
        app: 'contacts',
        type,
        data
    });
}
