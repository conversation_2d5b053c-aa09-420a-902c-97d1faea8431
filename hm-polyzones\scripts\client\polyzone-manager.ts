import { Vector2, Vector3, CellPoint, Zone, BoxZoneOptions, CircleZoneOptions, PolygonZoneOptions, ZoneOptions } from '@shared/types';

interface ZoneDrawColors {
    r: number;
    g: number;
    b: number;
    outlineAlpha: number;
    fillAlpha: number;
}

const GRID_CELL_SIZE = 400;
const GRID_DIMENSION = 5; // 5x5 grid
const GRID_WORLD_SIZE = GRID_CELL_SIZE * GRID_DIMENSION; // 2000x2000
const GRID_OFFSET_X = GRID_WORLD_SIZE / 2;
const GRID_OFFSET_Y = GRID_WORLD_SIZE / 2;
const PLAYER_POSITION_UPDATE_INTERVAL = 500; // ms
const DEBUG_DRAW_HEIGHT = 100.0; // Define the upward extension height

export class PolyZoneManager {
    private zones: Map<string, Zone> = new Map();
    private playerCurrentCell: CellPoint | null = null;
    private playerCurrentZones: Set<string> = new Set();
    private debugEnabled = false;
    private tickInterval: number = 0;

    constructor() {
        console.log('PolyZoneManager Initialized');
        this.startPlayerTracking();
        this.startDebugDrawingTick(); // Add this call
        RegisterCommand('polydebug', () => this.toggleDebug(), false);
    }

    public toggleDebug(): void {
        this.debugEnabled = !this.debugEnabled;
        console.log(`PolyZone Debug: ${this.debugEnabled ? 'Enabled' : 'Disabled'}`);
    }

    private getCellKey(cell: CellPoint): string {
        return `${cell.cellX}_${cell.cellY}`;
    }

    public getCellIndex(x: number, y: number): CellPoint {
        const cellX = Math.floor((x + GRID_OFFSET_X) / GRID_CELL_SIZE);
        const cellY = Math.floor((y + GRID_OFFSET_Y) / GRID_CELL_SIZE);
        return { cellX, cellY };
    }

    private startPlayerTracking(): void {
        if (this.tickInterval) {
            clearInterval(this.tickInterval);
        }
        this.tickInterval = setInterval(() => {
            const ped = PlayerPedId();
            const coords = GetEntityCoords(ped, true) as [number, number, number];
            const currentPosition: Vector2 = { x: coords[0], y: coords[1] };
            const newCell = this.getCellIndex(currentPosition.x, currentPosition.y);

            if (!this.playerCurrentCell || newCell.cellX !== this.playerCurrentCell.cellX || newCell.cellY !== this.playerCurrentCell.cellY) {
                const oldCell = this.playerCurrentCell;
                this.playerCurrentCell = newCell;
                emit('hm-polyzones:cellChanged', oldCell, newCell);
                console.log(`Player moved to cell: ${newCell.cellX}, ${newCell.cellY}`);
                this.updatePlayerZoneStatus(currentPosition, {x: coords[0], y: coords[1], z: coords[2]});
            } else {
                // Still update zone status even if cell hasn't changed, for dynamic zones or precise entry/exit
                 this.updatePlayerZoneStatus(currentPosition, {x: coords[0], y: coords[1], z: coords[2]});
            }
        }, PLAYER_POSITION_UPDATE_INTERVAL) as any as number; // Cast to any then number
    }

    private startDebugDrawingTick(): void {
        setTick(() => {
            if (this.debugEnabled) {
                this.drawDebugGrid();
                this.drawDebugZones();
            }
        });
    }

    private updatePlayerZoneStatus(playerPosition: Vector2, playerFullPosition: Vector3): void {
        if (!this.playerCurrentCell) {
            // console.log('[PolyZoneManager] updatePlayerZoneStatus: No current player cell.'); // Optional: Uncomment if needed
            return;
        }
        // const relevantCells = this.getAdjacentCells(this.playerCurrentCell, true); // OLD CODE
        const relevantCells = this.playerCurrentCell ? [this.playerCurrentCell] : []; // NEW CODE: Only consider the current cell

        const zonesToCheck = new Set<Zone>();

        relevantCells.forEach(cell => {
            if (!cell) return; // Add a null check for cell
            const cellKey = this.getCellKey(cell);
            this.zones.forEach(zone => {
                if (zone.cells.has(cellKey)) {
                    zonesToCheck.add(zone);
                }
            });
        });

        // console.log(`[PolyZoneManager] updatePlayerZoneStatus: Found ${zonesToCheck.size} relevant zones to check.`);
        
        const newlyEnteredZones = new Set<string>();

        zonesToCheck.forEach(zone => {
            const isInside = this.isPlayerInsideZone(playerPosition, playerFullPosition, zone);
            console.log(`${zone.name}, ${isInside}, ${playerFullPosition.z}`);
            if (isInside) {
                newlyEnteredZones.add(zone.name);
                if (!this.playerCurrentZones.has(zone.name)) {
                    this.playerCurrentZones.add(zone.name);
                    emit('hm-polyzones:enterZone', zone.name);
                    console.log(`Entered zone: ${zone.name}`);
                }
            }
        });
        
        const exitedZones = new Set<string>();
        this.playerCurrentZones.forEach(zoneName => {
            if (!newlyEnteredZones.has(zoneName)) {
                exitedZones.add(zoneName);
            }
        });

        exitedZones.forEach(zoneName => {
            this.playerCurrentZones.delete(zoneName);
            emit('hm-polyzones:exitZone', zoneName);
            console.log(`Exited zone: ${zoneName}`);
        });
    }
    
    private isPlayerInsideZone(playerPosition: Vector2, playerFullPosition: Vector3, zone: Zone): boolean {
        const { minZ, maxZ } = zone.options;
        if ((minZ !== undefined && playerFullPosition.z < minZ) || (maxZ !== undefined && playerFullPosition.z > maxZ)) {
            return false;
        }

        switch (zone.type) {
            case 'box':
                return this.isInsideBox(playerPosition, zone as Zone<BoxZoneOptions>);
            case 'circle':
                return this.isInsideCircle(playerPosition, zone as Zone<CircleZoneOptions>);
            case 'polygon':
                return this.isInsidePolygon(playerPosition, zone as Zone<PolygonZoneOptions>);
            default:
                return false;
        }
    }

    private isInsideBox(point: Vector2, zone: Zone<BoxZoneOptions>): boolean {
        const { center, width, height, options } = zone;
        if (!center || width === undefined || height === undefined) return false;
        
        const heading = options.heading; // Radians
        let translatedX = point.x - center.x;
        let translatedY = point.y - center.y;

        if (heading) {
            const cosH = Math.cos(-heading); // Use negative heading for point rotation
            const sinH = Math.sin(-heading);
            const rotatedX = translatedX * cosH - translatedY * sinH;
            const rotatedY = translatedX * sinH + translatedY * cosH;
            translatedX = rotatedX;
            translatedY = rotatedY;
        }
        
        return Math.abs(translatedX) <= width / 2 && Math.abs(translatedY) <= height / 2;
    }

    private isInsideCircle(point: Vector2, zone: Zone<CircleZoneOptions>): boolean {
        const { center, radius } = zone;
        if (!center || radius === undefined) return false;
        const distanceSq = (point.x - center.x) ** 2 + (point.y - center.y) ** 2;
        return distanceSq <= radius ** 2;
    }

    private isInsidePolygon(point: Vector2, zone: Zone<PolygonZoneOptions>): boolean {
        const { vertices } = zone;
        if (!vertices || vertices.length < 3) return false;

        let inside = false;
        for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
            const xi = vertices[i].x, yi = vertices[i].y;
            const xj = vertices[j].x, yj = vertices[j].y;

            const intersect = ((yi > point.y) !== (yj > point.y))
                && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);
            if (intersect) inside = !inside;
        }
        return inside;
    }


    private getAdjacentCells(cell: CellPoint, includeSelf: boolean = false): CellPoint[] {
        const adjacent: CellPoint[] = [];
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                if (!includeSelf && dx === 0 && dy === 0) continue;
                adjacent.push({ cellX: cell.cellX + dx, cellY: cell.cellY + dy });
            }
        }
        return adjacent;
    }

    private calculateBoundingBox(zone: Omit<Zone, 'boundingBox' | 'cells'>): Zone['boundingBox'] {
        switch (zone.type) {
            case 'box': {
                if (!zone.center || zone.width === undefined || zone.height === undefined) throw new Error('Box zone missing params');
                // For simplicity, using Axis-Aligned Bounding Box (AABB) even for rotated boxes.
                // For more accuracy with rotated boxes, one would calculate the AABB of the rotated corners.
                const halfWidth = zone.width / 2;
                const halfHeight = zone.height / 2;
                if (zone.options && (zone.options as BoxZoneOptions).heading) {
                    // Calculate AABB of rotated box corners
                    const angle = (zone.options as BoxZoneOptions).heading!; // Radians
                    const cosA = Math.cos(angle);
                    const sinA = Math.sin(angle);
                    const corners = [
                        { x: -halfWidth, y: -halfHeight },
                        { x:  halfWidth, y: -halfHeight },
                        { x:  halfWidth, y:  halfHeight },
                        { x: -halfWidth, y:  halfHeight },
                    ];
                    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                    corners.forEach(corner => {
                        const rotatedX = corner.x * cosA - corner.y * sinA + zone.center!.x;
                        const rotatedY = corner.x * sinA + corner.y * cosA + zone.center!.y;
                        minX = Math.min(minX, rotatedX);
                        minY = Math.min(minY, rotatedY);
                        maxX = Math.max(maxX, rotatedX);
                        maxY = Math.max(maxY, rotatedY);
                    });
                    return { minX, minY, maxX, maxY };

                } else {
                     return {
                        minX: zone.center.x - halfWidth,
                        minY: zone.center.y - halfHeight,
                        maxX: zone.center.x + halfWidth,
                        maxY: zone.center.y + halfHeight,
                    };
                }
            }
            case 'circle': {
                if (!zone.center || zone.radius === undefined) throw new Error('Circle zone missing params');
                return {
                    minX: zone.center.x - zone.radius,
                    minY: zone.center.y - zone.radius,
                    maxX: zone.center.x + zone.radius,
                    maxY: zone.center.y + zone.radius,
                };
            }
            case 'polygon': {
                if (!zone.vertices || zone.vertices.length === 0) throw new Error('Polygon zone missing params');
                let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                zone.vertices.forEach(v => {
                    minX = Math.min(minX, v.x);
                    minY = Math.min(minY, v.y);
                    maxX = Math.max(maxX, v.x);
                    maxY = Math.max(maxY, v.y);
                });
                return { minX, minY, maxX, maxY };
            }
            default:
                throw new Error('Unknown zone type');
        }
    }

    private assignZoneToCells(zone: Zone): void {
        const { minX, minY, maxX, maxY } = zone.boundingBox;
        const startCell = this.getCellIndex(minX, minY);
        const endCell = this.getCellIndex(maxX, maxY);

        for (let x = startCell.cellX; x <= endCell.cellX; x++) {
            for (let y = startCell.cellY; y <= endCell.cellY; y++) {
                zone.cells.add(this.getCellKey({ cellX: x, cellY: y }));
            }
        }
    }
    
    private addZone(zoneData: Omit<Zone, 'boundingBox' | 'cells'>): void {
        if (this.zones.has(zoneData.name)) {
            console.warn(`Zone with name "${zoneData.name}" already exists. Skipping.`);
            return;
        }
        const boundingBox = this.calculateBoundingBox(zoneData);
        const newZone: Zone = {
            ...zoneData,
            boundingBox,
            cells: new Set<string>(),
        };
        this.assignZoneToCells(newZone);
        this.zones.set(newZone.name, newZone);
        console.log(`Zone "${newZone.name}" created and assigned to cells:`, Array.from(newZone.cells));
    }

    public createBoxZone(name: string, center: Vector2, width: number, height: number, options: BoxZoneOptions = {}): void {
        this.addZone({ name, type: 'box', center, width, height, options });
    }

    public createCircleZone(name: string, center: Vector2, radius: number, options: CircleZoneOptions = {}): void {
        this.addZone({ name, type: 'circle', center, radius, options });
    }

    public createPolygonZone(name: string, vertices: Vector2[], options: PolygonZoneOptions = {}): void {
        if (vertices.length < 3) {
            console.error("Polygon zones require at least 3 vertices.");
            return;
        }
        this.addZone({ name, type: 'polygon', vertices, options });
    }

    public removeZone(zoneName: string): void {
        if (this.zones.has(zoneName)) {
            this.zones.delete(zoneName);
            // If the player was in this zone, remove it from their current zones
            if (this.playerCurrentZones.has(zoneName)) {
                this.playerCurrentZones.delete(zoneName);
                // Optionally, emit an exit event here if immediate feedback is needed,
                // though updatePlayerZoneStatus will handle it on the next tick.
                // emit('hm-polyzones:exitZone', zoneName); 
                console.log(`Player exited zone \"${zoneName}\" due to removal.`);
            }
            console.log(`Zone \"${zoneName}\" removed.`);
        } else {
            console.warn(`Zone with name \"${zoneName}\" not found. Cannot remove.`);
        }
    }

    // --- Debug Drawing ---
    private drawDebugGrid(): void {
        const linesToDraw = GRID_DIMENSION + 1;
        const gridStartOffset = -GRID_WORLD_SIZE / 2;
        const gridLineColor = { r: 255, g: 0, b: 0 }; // Define grid line color

        // Draw cell labels and vertical lines at cell corners
        for (let i = 0; i < GRID_DIMENSION; i++) {
            for (let j = 0; j < GRID_DIMENSION; j++) {
                const cellMinX = gridStartOffset + i * GRID_CELL_SIZE;
                const cellMinY = gridStartOffset + j * GRID_CELL_SIZE;
                const cellMaxX = cellMinX + GRID_CELL_SIZE;
                const cellMaxY = cellMinY + GRID_CELL_SIZE;

                const cellCenterX = cellMinX + GRID_CELL_SIZE / 2;
                const cellCenterY = cellMinY + GRID_CELL_SIZE / 2;
                
                const [foundGroundCenter, groundZCenter] = GetGroundZFor_3dCoord(cellCenterX, cellCenterY, 1000.0, false);

                if (foundGroundCenter) {
                    this.drawText3D(cellCenterX, cellCenterY, groundZCenter + DEBUG_DRAW_HEIGHT / 2, `[${i},${j}]`, 255, 255, 255, 200, 0.4);
                }

                const corners = [
                    { x: cellMinX, y: cellMinY },
                    { x: cellMaxX, y: cellMinY },
                    { x: cellMaxX, y: cellMaxY },
                    { x: cellMinX, y: cellMaxY },
                ];

                const groundCorners: (Vector3 | null)[] = corners.map(corner => {
                    const [found, z] = GetGroundZFor_3dCoord(corner.x, corner.y, 1000.0, false);
                    return found ? { ...corner, z } : null;
                });

                // Draw vertical lines at corners
                groundCorners.forEach(corner => {
                    if (corner !== null) { 
                        DrawLine(corner.x, corner.y, corner.z, corner.x, corner.y, corner.z + DEBUG_DRAW_HEIGHT, gridLineColor.r, gridLineColor.g, gridLineColor.b, 100);
                    }
                });

                // Draw horizontal lines on the ground
                for (let k = 0; k < groundCorners.length; k++) {
                    const startNode = groundCorners[k];
                    const endNode = groundCorners[(k + 1) % groundCorners.length]; 
                    if (startNode && endNode) { 
                        DrawLine(startNode.x, startNode.y, startNode.z, endNode.x, endNode.y, endNode.z, gridLineColor.r, gridLineColor.g, gridLineColor.b, 150);
                    }
                }
            }
        }
    }

    private drawText3D(x: number, y: number, z: number, text: string, r: number, g: number, b: number, a: number, scale: number, font: number = 0, center: boolean = true) {
        SetTextFont(font);
        SetTextScale(0.0, scale);
        SetTextColour(r, g, b, a);
        SetTextDropshadow(0, 0, 0, 0, 255);
        SetTextDropShadow();
        SetTextOutline();
        if (center) {
            SetTextCentre(true);
        }
        SetDrawOrigin(x,y,z,0); // Set the origin for the text
        BeginTextCommandDisplayText("STRING");
        AddTextComponentSubstringPlayerName(text);
        EndTextCommandDisplayText(0,0); // Draw relative to the origin set by SetDrawOrigin
        ClearDrawOrigin(); // Clear the draw origin
    }

    private getZoneDebugColor(zoneName: string): { r: number, g: number, b: number } {
        // Simple hash function to get a somewhat consistent color
        let hash = 0;
        for (let i = 0; i < zoneName.length; i++) {
            hash = zoneName.charCodeAt(i) + ((hash << 5) - hash);
            hash = hash & hash; // Convert to 32bit integer
        }
        const r = (hash & 0xFF0000) >> 16;
        const g = (hash & 0x00FF00) >> 8;
        const b = hash & 0x0000FF;
        return { r: (r % 200) + 55, g: (g % 200) + 55, b: (b % 200) + 55 }; // Ensure colors are not too dark
    }

    private drawDebugZones(): void {
        this.zones.forEach(zone => {
            let r, g, b, outlineAlpha, fillAlpha;

            // Determine base color and alpha values
            if (zone.options && zone.options.debugColor && Array.isArray(zone.options.debugColor)) {
                r = zone.options.debugColor[0];
                g = zone.options.debugColor[1];
                b = zone.options.debugColor[2];
                // Use provided alpha for outline, make fill more transparent or use a default
                outlineAlpha = zone.options.debugColor.length > 3 ? zone.options.debugColor[3] : 220;
                fillAlpha = zone.options.debugColor.length > 3 ? Math.min(zone.options.debugColor[3], 120) : 100; // Ensure fill is not too opaque
            } else {
                const generatedColor = this.getZoneDebugColor(zone.name);
                r = generatedColor.r;
                g = generatedColor.g;
                b = generatedColor.b;
                outlineAlpha = 200; // Default outline alpha
                fillAlpha = 80;    // Default fill alpha, more transparent
            }
            const drawColors = { r, g, b, outlineAlpha, fillAlpha };

            const centerForLabel = zone.type === 'polygon' ? (zone.vertices && zone.vertices.length > 0 ? zone.vertices[0] : {x:0, y:0}) : zone.center || {x:0,y:0};
            const [foundGroundLabel, groundZLabel] = GetGroundZFor_3dCoord(centerForLabel.x, centerForLabel.y, 1000.0, false);
            if (foundGroundLabel) {
                 this.drawText3D(centerForLabel.x, centerForLabel.y, groundZLabel + DEBUG_DRAW_HEIGHT / 2, zone.name, drawColors.r, drawColors.g, drawColors.b, 255, 0.5);
            }

            switch (zone.type) {
                case 'box':
                    this.drawDebugBoxZone(zone as Zone<BoxZoneOptions>, drawColors);
                    break;
                case 'circle':
                    this.drawDebugCircleZone(zone as Zone<CircleZoneOptions>, drawColors);
                    break;
                case 'polygon':
                    this.drawDebugPolygonZone(zone as Zone<PolygonZoneOptions>, drawColors);
                    break;
            }
        });
    }

    private drawDebugBoxZone(zone: Zone<BoxZoneOptions>, colors: { r: number, g: number, b: number, outlineAlpha: number, fillAlpha: number }): void {
        if (!zone.center || zone.width === undefined || zone.height === undefined) return;
        
        const { center, width, height, options } = zone;
        const heading = options.heading || 0; 
        const cosH = Math.cos(heading);
        const sinH = Math.sin(heading);

        const halfW = width / 2;
        const halfH = height / 2;

        const localCorners = [
            { x: -halfW, y: -halfH }, { x: halfW, y: -halfH },
            { x: halfW, y: halfH },   { x: -halfW, y: halfH }
        ];

        const worldCornersXY = localCorners.map(lc => ({
            x: center.x + (lc.x * cosH - lc.y * sinH),
            y: center.y + (lc.x * sinH + lc.y * cosH)
        }));

        const worldCornersGround: (Vector3 | null)[] = worldCornersXY.map(wc => {
            const [found, z] = GetGroundZFor_3dCoord(wc.x, wc.y, 1000.0, false);
            return found ? { ...wc, z } : null;
        }).filter(v => v !== null) as Vector3[];

        if (worldCornersGround.length < 4) return; // Need all 4 corners for a box

        // Vertical Walls
        worldCornersGround.forEach(corner  => {
            if (!corner) return; // Skip if corner is null
            DrawLine(corner.x, corner.y, corner.z, corner.x, corner.y, corner.z + DEBUG_DRAW_HEIGHT, colors.r, colors.g, colors.b, colors.outlineAlpha);
        });

        // Ground Outline
        for (let i = 0; i < worldCornersGround.length; i++) {
            const startNode = worldCornersGround[i] as Vector3;
            const endNode = worldCornersGround[(i + 1) % worldCornersGround.length] as Vector3;
            DrawLine(startNode.x, startNode.y, startNode.z, endNode.x, endNode.y, endNode.z, colors.r, colors.g, colors.b, colors.outlineAlpha);
        }

        // Filled Base (2 triangles)
        const v0 = worldCornersGround[0] as Vector3;
        const v1 = worldCornersGround[1] as Vector3;
        const v2 = worldCornersGround[2] as Vector3;
        const v3 = worldCornersGround[3] as Vector3;
        DrawPoly(v0.x, v0.y, v0.z, v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, colors.r, colors.g, colors.b, colors.fillAlpha);
        DrawPoly(v0.x, v0.y, v0.z, v2.x, v2.y, v2.z, v3.x, v3.y, v3.z, colors.r, colors.g, colors.b, colors.fillAlpha);
    }

    private drawDebugCircleZone(zone: Zone<CircleZoneOptions>, colors: { r: number, g: number, b: number, outlineAlpha: number, fillAlpha: number }): void {
        if (!zone.center || zone.radius === undefined) return;
        const { center, radius } = zone;
        const segments = 32;

        const groundPoints: Vector3[] = [];
        const [foundCenter, centerZ] = GetGroundZFor_3dCoord(center.x, center.y, 1000.0, false);
        if (!foundCenter) return; // Cannot draw if ground for center isn't found

        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * 2 * Math.PI;
            const x = center.x + radius * Math.cos(angle);
            const y = center.y + radius * Math.sin(angle);

            const [foundGround, groundZ] = GetGroundZFor_3dCoord(x, y, 1000.0, false);
            if (foundGround) {
                groundPoints.push({ x, y, z: groundZ });
                DrawLine(x, y, groundZ, x, y, groundZ + DEBUG_DRAW_HEIGHT, colors.r, colors.g, colors.b, colors.outlineAlpha);
            }
        }
        
        if (groundPoints.length < 2) return;

        // Draw Ground Outline
        for (let i = 0; i < groundPoints.length -1; i++) { // Iterate to length - 1 as points are already ordered
             const startNode = groundPoints[i];
             const endNode = groundPoints[i+1];
             DrawLine(startNode.x, startNode.y, startNode.z, endNode.x, endNode.y, endNode.z, colors.r, colors.g, colors.b, colors.outlineAlpha);
        }
         // Connect last to first if it's a closed circle (which it is due to <= segments)
        if (groundPoints.length > 1) {
            const first = groundPoints[0];
            const last = groundPoints[groundPoints.length - 1];
            DrawLine(last.x, last.y, last.z, first.x, first.y, first.z, colors.r, colors.g, colors.b, colors.outlineAlpha);
        }


        // Filled Base (Triangle Fan from center)
        const centerPointGround = { x: center.x, y: center.y, z: centerZ };
        for (let i = 0; i < groundPoints.length -1; i++) { // Iterate to length - 1
            const v1 = groundPoints[i];
            const v2 = groundPoints[i+1];
            DrawPoly(centerPointGround.x, centerPointGround.y, centerPointGround.z, v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, colors.r, colors.g, colors.b, colors.fillAlpha);
        }
         if (groundPoints.length > 1) { // Connect last segment back to the first with the center
            const first = groundPoints[0];
            const last = groundPoints[groundPoints.length - 1];
            DrawPoly(centerPointGround.x, centerPointGround.y, centerPointGround.z, last.x, last.y, last.z, first.x, first.y, first.z, colors.r, colors.g, colors.b, colors.fillAlpha);
        }
    }

    private drawDebugPolygonZone(zone: Zone<PolygonZoneOptions>, colors: { r: number, g: number, b: number, outlineAlpha: number, fillAlpha: number }): void {
        if (!zone.vertices || zone.vertices.length < 3) return; // Need at least 3 for a polygon

        const worldVerticesGround: Vector3[] = zone.vertices.map(v => {
            const [found, z] = GetGroundZFor_3dCoord(v.x, v.y, 1000.0, false);
            return found ? { ...v, z } : null;
        }).filter(v => v !== null) as Vector3[]; 
        
        if (worldVerticesGround.length < 3) return; // Still need 3 valid ground points to form a closed shape for walls

        // Draw Filled Wall Panels
        for (let i = 0; i < worldVerticesGround.length; i++) {
            const p1_bottom = worldVerticesGround[i];
            const p2_bottom = worldVerticesGround[(i + 1) % worldVerticesGround.length]; // Wraps around for the last segment

            const p1_top: Vector3 = { x: p1_bottom.x, y: p1_bottom.y, z: p1_bottom.z + DEBUG_DRAW_HEIGHT };
            const p2_top: Vector3 = { x: p2_bottom.x, y: p2_bottom.y, z: p2_bottom.z + DEBUG_DRAW_HEIGHT };

            // Draw the quad (wall panel) using two triangles
            // Triangle 1: (p1_bottom, p2_bottom, p2_top)
            DrawPoly(p1_bottom.x, p1_bottom.y, p1_bottom.z, 
                     p2_bottom.x, p2_bottom.y, p2_bottom.z, 
                     p2_top.x,    p2_top.y,    p2_top.z, 
                     colors.r, colors.g, colors.b, colors.fillAlpha);
            
            // Triangle 2: (p1_bottom, p2_top, p1_top)
            DrawPoly(p1_bottom.x, p1_bottom.y, p1_bottom.z, 
                     p2_top.x,    p2_top.y,    p2_top.z, 
                     p1_top.x,    p1_top.y,    p1_top.z, 
                     colors.r, colors.g, colors.b, colors.fillAlpha);
        }
    }

    private drawFilledBoxBase(groundVertices: (Vector3 | null)[], colors: ZoneDrawColors) {
        if (groundVertices.length !== 4) return;
        const v0 = groundVertices[0];
        const v1 = groundVertices[1];
        const v2 = groundVertices[2];
        const v3 = groundVertices[3];

        if (v0 && v1 && v2 && v3) { // Ensure all vertices are non-null
            DrawPoly(v0.x, v0.y, v0.z, v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, colors.r, colors.g, colors.b, colors.fillAlpha);
            DrawPoly(v0.x, v0.y, v0.z, v2.x, v2.y, v2.z, v3.x, v3.y, v3.z, colors.r, colors.g, colors.b, colors.fillAlpha);
        }
    }

    private drawFilledCircleBase(center: Vector3 | null, radius: number, groundVertices: (Vector3 | null)[], colors: ZoneDrawColors) {
        if (!center) return; // Check if center is null

        const nonNullVertices = groundVertices.filter(v => v !== null) as Vector3[];
        if (nonNullVertices.length < 2) return; // Need at least 2 vertices to form a triangle with the center

        for (let i = 0; i < nonNullVertices.length; i++) {
            const v1 = nonNullVertices[i];
            const v2 = nonNullVertices[(i + 1) % nonNullVertices.length]; // Loop back for the last segment
            // All v1, v2, and center are guaranteed non-null here due to prior checks and filtering
            DrawPoly(center.x, center.y, center.z, v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, colors.r, colors.g, colors.b, colors.fillAlpha);
        }
    }

    private drawFilledPolygonBaseWalls(groundVertices: (Vector3 | null)[], colors: ZoneDrawColors, wallHeight: number) {
        if (groundVertices.length < 2) return;

        for (let i = 0; i < groundVertices.length; i++) {
            const p1_base = groundVertices[i];
            const p2_base = groundVertices[(i + 1) % groundVertices.length];

            if (p1_base && p2_base) { // Ensure both base points of the wall segment are non-null
                const p1_top = { x: p1_base.x, y: p1_base.y, z: p1_base.z + wallHeight };
                const p2_top = { x: p2_base.x, y: p2_base.y, z: p2_base.z + wallHeight };

                // Wall panel (two triangles)
                DrawPoly(p1_base.x, p1_base.y, p1_base.z, p2_base.x, p2_base.y, p2_base.z, p1_top.x, p1_top.y, p1_top.z, colors.r, colors.g, colors.b, colors.fillAlpha);
                DrawPoly(p1_top.x, p1_top.y, p1_top.z, p2_base.x, p2_base.y, p2_base.z, p2_top.x, p2_top.y, p2_top.z, colors.r, colors.g, colors.b, colors.fillAlpha);
            }
        }
    }
}
