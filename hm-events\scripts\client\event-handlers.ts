/// <reference types="@citizenfx/client" />

import { Config, EventCategory, GameEvents } from '@shared';
import { log } from 'console';

// Define our own logger for consistent logging
const RESOURCE_NAME = GetCurrentResourceName();
const logger = {
  info: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.log(`^2[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.log(data);
  },
  debug: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.debug(`^5[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.debug(data);
  },
  warn: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.warn(`^3[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.warn(data);
  },
  error: (message: string, context?: string, data?: any) => {
    const contextStr = context ? ` ^7[^6${context}^7]^0` : '';
    console.error(`^1[${RESOURCE_NAME}]^7 ${message}${contextStr}`);
    if (data) console.error(data);
  },
};

// Throttling counters
const eventCounters: Record<string, number> = {};

// Event tracking for debugging
const triggeredEvents: Record<string, {
  count: number,
  lastTriggered: number,
  examples: Array<{args: any[], time: number}>,
}> = {};

/**
 * Initialize all event handlers
 */
export function setupEventHandlers() {
  // Set up handlers for all event categories
  setupCombatEvents();
  setupVehicleEvents();
  setupNetworkEvents();
  setupPlayerEvents();
  setupEnvironmentEvents();
  setupAcquaintanceEvents();
  setupResponseEvents();
  setupShockingEvents();
  setupSystemEvents();
  setupOtherEvents();
  setupGameEventTriggeredHandler();

}

/**
 * Set up combat-related event handlers
 */
function setupCombatEvents() {
  // Direct event handlers for combat events
  on('CEventDamage', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDamage', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventEntityDamage', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventEntityDamage', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventEntityDestroyed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventEntityDestroyed', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventGunShot', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventGunShot', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventGunShotBulletImpact', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventGunShotBulletImpact', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventGunShotWhizzedBy', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventGunShotWhizzedBy', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventGunAimedAt', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventGunAimedAt', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventExplosion', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventExplosion', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventMeleeAction', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventMeleeAction', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  // on('CEventFriendlyFireNearMiss', (entities: number[], eventEntity: number, data: any[]) => {
  //   logEvent('CEventFriendlyFireNearMiss', [entities, eventEntity, data], EventCategory.COMBAT);
  // });

  on('CEventFriendlyAimedAt', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventFriendlyAimedAt', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventOnFire', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventOnFire', [entities, eventEntity, data], EventCategory.COMBAT);
  });

  on('CEventWrithe', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventWrithe', [entities, eventEntity, data], EventCategory.COMBAT);
  });
}

/**
 * Set up vehicle-related event handlers
 */
function setupVehicleEvents() {
  // Direct event handlers for vehicle events
  on('CEventCarUndriveable', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventCarUndriveable', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventVehicleCollision', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventVehicleCollision', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventVehicleDamage', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventVehicleDamage', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventVehicleDamageWeapon', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventVehicleDamageWeapon', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventVehicleOnFire', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventVehicleOnFire', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventPedJackingMyVehicle', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedJackingMyVehicle', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventPedEnteredMyVehicle', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedEnteredMyVehicle', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventPlayerUnableToEnterVehicle', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPlayerUnableToEnterVehicle', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  // on('CEventCopCarBeingStolen', (entities: number[], eventEntity: number, data: any[]) => {
  //   logEvent('CEventCopCarBeingStolen', [entities, eventEntity, data], EventCategory.VEHICLE);
  // });

  on('CEventDraggedOutCar', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDraggedOutCar', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventMustLeaveBoat', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventMustLeaveBoat', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventPedOnCarRoof', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedOnCarRoof', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

  on('CEventShockingSiren', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingSiren', [entities, eventEntity, data], EventCategory.VEHICLE);
  });

}

/**
 * Set up network-related event handlers
 */
function setupNetworkEvents() {
  // Direct event handlers for network events
  on('CEventNetworkEntityDamage', (entities: number[], eventEntity: number, data: any[]) => {
    // This is one of the most important events - it triggers when any networked entity takes damage
    logEvent('CEventNetworkEntityDamage', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerJoinScript', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerJoinScript', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerLeftScript', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerLeftScript', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerSpawn', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerSpawn', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkStartSession', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkStartSession', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkEndSession', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkEndSession', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkVehicleUndrivable', (entities: number[], eventEntity: number, data: any[]) => {
    // This is an important event - it triggers when a vehicle becomes undrivable
    logEvent('CEventNetworkVehicleUndrivable', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerCollectedPickup', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerCollectedPickup', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerCollectedAmbientPickup', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerCollectedAmbientPickup', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPickupRespawned', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPickupRespawned', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerCollectedPortablePickup', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerCollectedPortablePickup', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerDroppedPortablePickup', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerDroppedPortablePickup', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkInviteAccepted', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkInviteAccepted', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerArrest', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerArrest', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerEnteredVehicle', (entities: number[], eventEntity: number, data: any[]) => {
    // This is an important event - it triggers when a player enters a vehicle
    logEvent('CEventNetworkPlayerEnteredVehicle', [entities, eventEntity, data], EventCategory.NETWORK);
  });

  on('CEventNetworkPlayerPedLeftBehind', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventNetworkPlayerPedLeftBehind', [entities, eventEntity, data], EventCategory.NETWORK);
  });
}

/**
 * Set up player-related event handlers
 */
function setupPlayerEvents() {
  // Direct event handlers for player events
  on('CEventPlayerDeath', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPlayerDeath', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventDeath', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDeath', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventDeadPedFound', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDeadPedFound', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventPedSeenDeadPed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedSeenDeadPed', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventPlayerCollisionWithPed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPlayerCollisionWithPed', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventPedCollisionWithPlayer', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedCollisionWithPlayer', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventPedCollisionWithPed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedCollisionWithPed', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventInAir', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventInAir', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventStuckInAir', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventStuckInAir', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventGetOutOfWater', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventGetOutOfWater', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventHurtTransition', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventHurtTransition', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventRanOverPed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventRanOverPed', [entities, eventEntity, data], EventCategory.PLAYER);
  });

  on('CEventShovePed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShovePed', [entities, eventEntity, data], EventCategory.PLAYER);
  });
}

/**
 * Set up environment-related event handlers
 */
function setupEnvironmentEvents() {
  // Fire events
  on('CEventFireNearby', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventFireNearby', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // Explosion events
  on('CEventExplosionHeard', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventExplosionHeard', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // Sound events
  // on('CEventFootStepHeard', (entities: number[], eventEntity: number, data: any[]) => {
  //   logEvent('CEventFootStepHeard', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  // });

  // Collision events
  on('CEventObjectCollision', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventObjectCollision', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // Door events
  on('CEventOpenDoor', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventOpenDoor', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // Weather events
  on('CEventLightningStrike', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventLightningStrike', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // Water events
  on('CEventSplashWater', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventSplashWater', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  // World events
  on('CEventCarAlarm', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventCarAlarm', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });

  on('CEventCarHorn', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventCarHorn', [entities, eventEntity, data], EventCategory.ENVIRONMENT);
  });
}

/**
 * Set up acquaintance-related event handlers
 */
function setupAcquaintanceEvents() {
  // Acquaintance events
  on('CEventAcquaintancePed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePed', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });

  on('CEventAcquaintancePedDead', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePedDead', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });

  on('CEventAcquaintancePedDislike', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePedDislike', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });

  on('CEventAcquaintancePedHate', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePedHate', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });

  on('CEventAcquaintancePedLike', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePedLike', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });

  on('CEventAcquaintancePedRespect', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAcquaintancePedRespect', [entities, eventEntity, data], EventCategory.ACQUAINTANCE);
  });
}

/**
 * Set up response-related event handlers
 */
function setupResponseEvents() {
  // Response task events
  on('CEventDataResponseTask', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTask', [entities, eventEntity, data], EventCategory.RESPONSE);
  });

  on('CEventDataResponseTaskCombatPed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTaskCombatPed', [entities, eventEntity, data], EventCategory.RESPONSE);
  });

  on('CEventDataResponseTaskEvasiveStep', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTaskEvasiveStep', [entities, eventEntity, data], EventCategory.RESPONSE);
  });

  on('CEventDataResponseTaskFleePed', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTaskFleePed', [entities, eventEntity, data], EventCategory.RESPONSE);
  });

  on('CEventDataResponseTaskPause', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTaskPause', [entities, eventEntity, data], EventCategory.RESPONSE);
  });

  on('CEventDataResponseTaskStandStill', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDataResponseTaskStandStill', [entities, eventEntity, data], EventCategory.RESPONSE);
  });
}

/**
 * Set up shocking-related event handlers
 */
function setupShockingEvents() {
  // Shocking vehicle events
  on('CEventShockingCarCrash', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingCarCrash', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  // Shocking combat events
  on('CEventShockingGunFight', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingGunFight', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  on('CEventShockingGunshotFired', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingGunshotFired', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  // Other shocking events
  on('CEventShockingDeadBody', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingDeadBody', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  on('CEventShockingExplosion', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingExplosion', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  on('CEventShockingMadDriver', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingMadDriver', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  on('CEventShockingMugger', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingMugger', [entities, eventEntity, data], EventCategory.SHOCKING);
  });

  on('CEventShockingPlaneFlying', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventShockingPlaneFlying', [entities, eventEntity, data], EventCategory.SHOCKING);
  });
}

/**
 * Set up system-related event handlers
 */
function setupSystemEvents() {
  // System events
  on('CEventScriptCommand', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventScriptCommand', [entities, eventEntity, data], EventCategory.SYSTEM);
  });

  on('CEventSystemEntityAttached', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventSystemEntityAttached', [entities, eventEntity, data], EventCategory.SYSTEM);
  });

  on('CEventSystemEntityDetached', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventSystemEntityDetached', [entities, eventEntity, data], EventCategory.SYSTEM);
  });

  on('CEventSystemMissionFlagSet', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventSystemMissionFlagSet', [entities, eventEntity, data], EventCategory.SYSTEM);
  });

  on('CEventSystemResetFlag', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventSystemResetFlag', [entities, eventEntity, data], EventCategory.SYSTEM);
  });
}

/**
 * Set up other miscellaneous event handlers
 */
function setupOtherEvents() {
  // Miscellaneous events
  on('CEventAgitated', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventAgitated', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventCombatTaunt', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventCombatTaunt', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventDamage', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventDamage', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventIncident', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventIncident', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventPedJacking', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedJacking', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventPedSeen', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedSeen', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventPedSeenMeleeAction', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventPedSeenMeleeAction', [entities, eventEntity, data], EventCategory.OTHER);
  });

  on('CEventRequestHelp', (entities: number[], eventEntity: number, data: any[]) => {
    logEvent('CEventRequestHelp', [entities, eventEntity, data], EventCategory.OTHER);
  });

  // on('CEventRespondedToThreat', (entities: number[], eventEntity: number, data: any[]) => {
  //   logEvent('CEventRespondedToThreat', [entities, eventEntity, data], EventCategory.OTHER);
  // });

  // on('CEventSuspiciousActivity', (entities: number[], eventEntity: number, data: any[]) => {
  //   logEvent('CEventSuspiciousActivity', [entities, eventEntity, data], EventCategory.OTHER);
  // });
}

/**
 * Set up the generic gameEventTriggered handler for any other events
 */
function setupGameEventTriggeredHandler() {
  on('gameEventTriggered', (name: string, args: any[]) => {
    // Track this event for debugging
    if (!triggeredEvents[name]) {
      triggeredEvents[name] = {
        count: 0,
        lastTriggered: Date.now(),
        examples: []
      };

      // Log first occurrence of each event
      logger.debug(`First occurrence of event: ${name}`, 'debug');
    }
    logEvent(name, args, EventCategory.OTHER);

    triggeredEvents[name].count++;
    triggeredEvents[name].lastTriggered = Date.now();

    // Store example args (keep only the last 3 examples)
    if (triggeredEvents[name].examples.length >= 3) {
      triggeredEvents[name].examples.shift(); // Remove oldest
    }
    triggeredEvents[name].examples.push({
      args: [...args], // Clone the args array
      time: Date.now()
    });

    // Check if we have a direct handler for this event
    // If we do, we don't need to handle it here
    const eventInfo = GameEvents[name];
    if (!eventInfo) {
      // This is an unknown event
      if (Config.logUnknownEvents) {
        // Log unknown events
        if (args.length > 0) {
          // For unknown events, just log the raw args as JSON
          logger.info(`${name}`, 'unknown', args);
        } else {
          logger.info(`${name}`, 'unknown');
        }
      }
    }
  });

  // Add debug commands
  RegisterCommand('hm:events', (_source: number, args: string[]) => {
    const triggeredCount = Object.keys(triggeredEvents).length;
    logger.info(`Total triggered events: ${triggeredCount}`, 'debug');

    if (triggeredCount === 0) {
      logger.info('No events have been triggered yet', 'debug');
      return;
    }

    // Sort events by count
    const sortedEvents = Object.entries(triggeredEvents)
      .sort((a, b) => b[1].count - a[1].count);

    // Show the top events
    logger.info('Top triggered events:', 'debug');

    const showAll = args[0] === 'all';
    const limit = showAll ? sortedEvents.length : Math.min(10, sortedEvents.length);

    for (let i = 0; i < limit; i++) {
      const [name, data] = sortedEvents[i];
      const secondsAgo = Math.floor((Date.now() - data.lastTriggered) / 1000);
      logger.info(`  ${name}: ${data.count} times (last: ${secondsAgo}s ago)`, 'debug');

      // Show example args for the first few events if requested
      if (args[0] === 'examples' && i < 3 && data.examples.length > 0) {
        const example = data.examples[data.examples.length - 1]; // Get the most recent
        logger.info(`    Example args: ${JSON.stringify(example.args)}`, 'debug');
      }
    }

    if (!showAll && sortedEvents.length > 10) {
      logger.info(`  ... and ${sortedEvents.length - 10} more. Use 'hm:events all' to see all.`, 'debug');
    }
  }, false);
}

/**
 * Log an event with simple JSON formatting
 */
function logEvent(name: string, args: any[], category: EventCategory) {
  // Apply throttling if needed
  if (Config.throttling.enabled && Config.throttling.rates[name]) {
    if (!shouldLogThrottledEvent(name)) {
      return;
    }
  }

  // Log the event with proper context and JSON data
  if (args.length > 0) {
    // Extract the standard event parameters
    const [entities, eventEntity, data] = args;

    // Create a simple JSON object with the event data
    const eventData = {
      entities,
      entity: eventEntity,
      data
    };

    // Log the event with JSON data
    logger.info(
      `${name}`,
      `${category.toLowerCase()}`,
      eventData
    );
  } else {
    logger.info(
      `${name}`,
      `${category.toLowerCase()}`
    );
  }
}



/**
 * Throttling logic
 */
function shouldLogThrottledEvent(eventName: string): boolean {
  // Initialize counter if it doesn't exist
  if (eventCounters[eventName] === undefined) {
    eventCounters[eventName] = 0;
  }

  // Increment counter
  eventCounters[eventName]++;

  // Check if we should log this occurrence
  const rate = Config.throttling.rates[eventName] || 1;
  return eventCounters[eventName] % rate === 0;
}

/**
 * Reset throttling counters
 */
export function resetThrottlingCounters() {
  for (const key in eventCounters) {
    eventCounters[key] = 0;
  }
}

/**
 * Get triggered events for debugging
 */
export function getTriggeredEvents() {
  return triggeredEvents;
}
