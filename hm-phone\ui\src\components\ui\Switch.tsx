import React from 'react';
import { motion } from 'framer-motion';

interface SwitchProps {
  checked: boolean;
  onChange: () => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  disabled = false,
  size = 'md',
  className = ''
}) => {
  // Size mappings
  const sizeClasses = {
    sm: {
      container: 'w-8 h-4',
      circle: 'w-3 h-3',
      translateX: 16
    },
    md: {
      container: 'w-11 h-6',
      circle: 'w-5 h-5',
      translateX: 20
    },
    lg: {
      container: 'w-14 h-7',
      circle: 'w-6 h-6',
      translateX: 28
    }
  };

  const { container, circle, translateX } = sizeClasses[size];

  return (
    <motion.button
      type="button"
      role="switch"
      aria-checked={checked}
      className={`
        relative inline-flex shrink-0 cursor-pointer rounded-full transition-colors 
        ${container}
        ${checked ? 'bg-blue-600' : 'bg-gray-700'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      onClick={disabled ? undefined : onChange}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
    >
      <motion.span
        className={`
          ${circle}
          pointer-events-none inline-block rounded-full bg-white shadow-lg transform ring-0
          transition-transform
        `}
        animate={{
          translateX: checked ? translateX : 0
        }}
        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
      />
    </motion.button>
  );
};