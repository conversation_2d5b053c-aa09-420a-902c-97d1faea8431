/**
 * Utility functions for notifications
 */

import { appConfigurations } from '../../common/stores/appConfigurations';

// App icon mapping for common app IDs
// This helps avoid circular dependencies by providing defaults
const APP_ICON_MAPPING: Record<number, { icon: string; color: string }> = {
  1: { icon: 'user', color: 'green' },        // Contacts
  2: { icon: 'image', color: 'purple' },      // Photos
  3: { icon: 'cogs', color: 'gray' },         // Settings
  4: { icon: 'store', color: 'orange' },      // AppStore
  5: { icon: 'envelope', color: 'indigo' },   // Messages
  6: { icon: 'book', color: 'yellow' },       // Yellow Pages
  7: { icon: 'mask', color: 'red' },          // Dark Market
  8: { icon: 'car', color: 'blue' },          // Garage
  9: { icon: 'camera', color: 'yellow' },     // LifeSnap
  10: { icon: 'money-bill-wave', color: 'green' }, // Banking
  11: { icon: 'music', color: 'purple' },     // Music
  12: { icon: 'heart', color: 'pink' },       // LoveLink
  13: { icon: 'sticky-note', color: 'orange' }, // Notes
  14: { icon: 'briefcase', color: 'teal' },   // Job Center
  15: { icon: 'camera', color: 'cyan' },      // Camera
  16: { icon: 'concierge-bell', color: 'blue' } // Services
};

/**
 * Get app icon and color from app ID
 * @param appId App ID
 * @returns Object with icon and color
 */
export const getAppIconAndColor = (appId: number): { icon: string; color: string } => {
  // First check the mapping for quick access
  if (APP_ICON_MAPPING[appId]) {
    return APP_ICON_MAPPING[appId];
  }

  // If not in mapping, try to find in app configurations
  try {
    const app = appConfigurations.find(app => app.id === appId);

    if (app) {
      return {
        icon: app.icon,
        color: app.iconColor
      };
    }
  } catch (error) {
    console.warn('Error accessing app configurations:', error);
  }

  // Default values if app not found
  return {
    icon: 'bell',
    color: 'gray'
  };
};

/**
 * Format time ago from timestamp
 * @param timestamp Timestamp in milliseconds
 * @returns Formatted time string (e.g., "5m", "2h", "3d")
 */
export const formatTimeAgo = (timestamp: number): string => {
  const diff = Date.now() - timestamp;
  const minutes = Math.floor(diff / 60000);

  if (minutes < 60) return `${minutes}m`;
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h`;
  return `${Math.floor(hours / 24)}d`;
};