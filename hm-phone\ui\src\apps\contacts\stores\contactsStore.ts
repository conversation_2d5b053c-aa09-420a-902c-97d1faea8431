import { create } from 'zustand';
import { Call } from '../types/dialerTypes';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { contactsMockData, dialerMockData } from '../../../fivem';
import { useMessagesStore } from '../../messages/stores/messagesStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { useNavigationStore } from '../../../navigation/navigationStore';
import { Contact } from '@shared/types';

// Define interfaces for contact sharing
interface ContactShareRequest {
  contactId: number;
  contact: Contact;
  senderId: number;
  senderName: string;
}

interface ContactShareStatus {
  success: boolean;
  contactId: number;
  targetPlayerId: number;
  status: 'sent' | 'accepted' | 'declined' | 'error';
}

// Define interfaces for each category
interface ContactsStore {
  contacts: Contact[];
  calls: Call[];
  loading: boolean;
  error: null | string;
  searchTerm: string;
  selectedContacts: number[];
  sharingStatus: Record<string, ContactShareStatus>; // Track sharing status by targetPlayerId
  pendingShareRequest: ContactShareRequest | null; // For the recipient
  lastContactsUpdate: number; // Timestamp of last contacts update

  actions: {
    getContacts: () => Promise<void>;
    getCalls: () => Promise<void>;
    addContact: (contact: Contact) => Promise<void>;
    updateContact: (contact: Contact) => Promise<void>;
    deleteContact: (contactId: number) => Promise<void>;
    messageContact: (contact: Contact) => Promise<void>;
    toggleFavorite: (contactId: number) => Promise<void>;
    shareContact: (contact: Contact, targetPlayerId: number) => Promise<void>;
    acceptContactShare: (request: ContactShareRequest) => Promise<void>;
    declineContactShare: (request: ContactShareRequest) => Promise<void>;
  };

  handlers: {
    onSetContacts: (contacts: Contact[]) => void;
    onSetCalls: (calls: Call[]) => void;
    onContactAdded: (contact: Contact) => void;
    onContactUpdated: (contact: Contact) => void;
    onContactDeleted: (contactId: number) => void;
    onContactShareRequest: (request: ContactShareRequest) => void;
    onContactShareSent: (status: ContactShareStatus) => void;
    onContactShareUpdated: (status: ContactShareStatus) => void;
    onContactShareAccepted: (contactId: number) => void;
  };

  ui: {
    searchContacts: (query: string) => Contact[];
    setSearchTerm: (term: string) => void;
    toggleContactSelection: (id: number) => void;
    clearSelectedContacts: () => void;
    clearPendingShareRequest: () => void;
  };
}

export const useContactsStore = create<ContactsStore>((set, get) => ({
  contacts: [],
  calls: [],
  loading: false,
  error: null,
  searchTerm: '',
  selectedContacts: [],
  sharingStatus: {},
  pendingShareRequest: null,
  lastContactsUpdate: 0,

  actions: {
    getContacts: async () => {
      const state = get();
      if (state.loading) {
        return;
      }
      if (state.contacts.length > 0) {
        return;
      }
      set({ loading: true });
      try {
        await clientRequests.send(
          'contacts',
          'getContacts',
          {},
          contactsMockData.contacts,
          state.handlers.onSetContacts as (data: unknown) => void
        );
      } catch (error) {
        console.error('[ContactsStore] Error loading contacts:', error);
        set({ loading: false });
      } finally {
        setTimeout(() => {
          set({ loading: false });
        }, 500);
      }
    },

    toggleFavorite: async (contactId: number) => {
      console.log('[ContactsStore] toggleFavorite called for contact:', contactId);
      const state = get();

      // Find the contact
      const contact = state.contacts.find(c => c.id === contactId);
      if (!contact) {
        console.error('[ContactsStore] Contact not found:', contactId);
        return;
      }

      // Create updated contact with toggled favorite status
      const updatedContact: Contact = {
        ...contact,
        favorite: contact.favorite === 1 ? 0 : 1
      };

      // Update the contact
      await state.actions.updateContact(updatedContact);
    },

    shareContact: async (contact: Contact, targetPlayerId: number) => {
      console.log(
        '[ContactsStore] shareContact called for contact:',
        contact.name,
        'to player:',
        targetPlayerId
      );

      // Make sure contact.id is a number (not undefined)
      const contactId = contact.id || 0;

      // Immediately update the UI to show "sent" status
      const key = `${contactId}_${targetPlayerId}`;
      const sentStatus: ContactShareStatus = {
        success: true,
        contactId: contactId,
        targetPlayerId: targetPlayerId,
        status: 'sent'
      };

      // Update the sharing status
      set(state => ({
        sharingStatus: {
          ...state.sharingStatus,
          [key]: sentStatus
        }
      }));

      // Set a timeout to clear the "sent" status after 5 seconds
      // This allows the user to see the status briefly but then be able to reshare
      const clearStatusTimeout = setTimeout(() => {
        const currentState = get();
        const currentStatus = currentState.sharingStatus[key];

        // Only clear if the status is still "sent" (not accepted/declined/error)
        if (currentStatus && currentStatus.status === 'sent') {
          console.log(
            `[ContactsStore] Clearing sent status for contact ${contactId} to player ${targetPlayerId}`
          );

          // Remove the status
          set(state => {
            const newSharingStatus = { ...state.sharingStatus };
            delete newSharingStatus[key];
            return { sharingStatus: newSharingStatus };
          });
        }
      }, 5000); // 5 seconds

      try {
        // Send the contact to the server
        await clientRequests.send('contacts', 'shareContact', {
          contactId: contactId,
          targetPlayerId: targetPlayerId
        });

        console.log(`Contact ${contact.name} shared with player ${targetPlayerId}`);

        // The server will send back a contactShareSent event which will update the status
        // If we don't receive that event, we already have the UI showing "sent"
      } catch (error) {
        console.error('[ContactsStore] Error sharing contact:', error);

        // Clear the timeout since we're updating the status
        clearTimeout(clearStatusTimeout);

        // Update status to error
        const errorStatus: ContactShareStatus = {
          success: false,
          contactId: contactId,
          targetPlayerId: targetPlayerId,
          status: 'error'
        };

        set(state => ({
          sharingStatus: {
            ...state.sharingStatus,
            [key]: errorStatus
          }
        }));

        // Send a notification about the error
        clientRequests.send('notifications', 'addNotification', {
          id: Date.now(),
          appId: 3, // Contacts app ID
          title: 'Contacts',
          message: 'Failed to share contact',
          type: 'error',
          timestamp: Date.now(),
          read: false,
          icon: 'exclamation-triangle'
        });
      }
    },
    messageContact: async (contact: Contact) => {
      console.log('[ContactsStore] messageContact called for contact:', contact);
      const state = get();
      if (state.loading) {
        console.log('[ContactsStore] Already loading, skipping request');
        return;
      }
      const playerPhone = usePhoneStore.getState().userProfile?.phoneNumber;
      if (!playerPhone) {
        console.error(
          '[ContactsStore] Cannot create conversation: Player phone number not available'
        );
        return;
      }
      const { conversations } = useMessagesStore.getState();
      const { openAppView } = useNavigationStore.getState();
      let existingConversation = null;

      for (const conversation of conversations) {
        if (conversation.type !== 'group' && Object.keys(conversation.members).includes(contact.number)) {
          existingConversation = conversation;
          break;
        }
      }

      if (existingConversation) {
        console.log(
          '[ContactsStore] Found existing conversation with contact:',
          existingConversation.id
        );
        openAppView('messages', 'conversation', { id: existingConversation.id });
      } else {
        // Instead of creating an empty conversation, we'll show the message modal
        // The modal will be shown by the component that called this function
        // This is just a placeholder function now that will be handled by the MessageModal component
        console.log(
          '[ContactsStore] Will show message modal for contact:',
          contact.name,
          contact.number
        );
      }
    },
    getCalls: async () => {
      console.log('[ContactsStore] getCalls called');
      const state = get();
      if (state.loading) {
        console.log('[ContactsStore] Already loading, skipping request');
        return;
      }
      set({ loading: true });
      try {
        // Send request with mock data and callback
        await clientRequests.send(
          'dialer',
          'getCalls',
          {},
          dialerMockData.calls,
          state.handlers.onSetCalls as (data: unknown) => void
        );
      } catch (error) {
        console.error('[ContactsStore] Error getting call history:', error);
        set({ loading: false });
      } finally {
        setTimeout(() => {
          set({ loading: false });
        }, 500);
      }
    },
    addContact: async (contact: Contact) => {
      const state = get();
      if (state.loading) return;
      set({ loading: true });
      try {
        await clientRequests.create(
          'contacts',
          'addContact',
          contact as unknown as Record<string, unknown>,
          state.contacts as unknown as Record<string, unknown>[],
          state.handlers.onContactAdded as (data: unknown) => void
        );
      } catch (error) {
        console.error('[ContactsStore] Error adding contact:', error);
        set({ loading: false });
      } finally {
        setTimeout(() => set({ loading: false }), 500);
      }
    },
    updateContact: async (contact: Contact) => {
      const state = get();
      if (state.loading) return;
      set({ loading: true });
      try {
        // Ensure contact.id is defined
        if (contact.id === undefined) {
          throw new Error('Contact ID is undefined');
        }
        await clientRequests.update(
          'contacts',
          'Contact',
          contact.id,
          contact as unknown as Record<string, unknown>,
          state.handlers.onContactUpdated as (data: unknown) => void
        );
      } catch (error) {
        console.error('[ContactsStore] Error updating contact:', error);
        set({ loading: false });
      } finally {
        setTimeout(() => set({ loading: false }), 500);
      }
    },
    deleteContact: async (contactId: number) => {
      const state = get();
      if (state.loading) return;
      set({ loading: true });
      try {
        await clientRequests.delete(
          'contacts',
          'deleteContact',
          contactId,
          state.handlers.onContactDeleted
        );
      } catch (error) {
        console.error('[ContactsStore] Error deleting contact:', error);
        set({ loading: false });
      } finally {
        setTimeout(() => set({ loading: false }), 500);
      }
    },
    acceptContactShare: async (request: ContactShareRequest) => {
      console.log('[ContactsStore] Accepting contact share request:', request);
      try {
        // Send accept request to server
        await clientRequests.send(
          'contacts',
          'acceptContactShare',
          request as unknown as Record<string, unknown>
        );

        // Clear the pending request
        get().ui.clearPendingShareRequest();
      } catch (error) {
        console.error('[ContactsStore] Error accepting contact share:', error);
      }
    },

    declineContactShare: async (request: ContactShareRequest) => {
      console.log('[ContactsStore] Declining contact share request:', request);
      try {
        // Send decline request to server
        await clientRequests.send(
          'contacts',
          'declineContactShare',
          request as unknown as Record<string, unknown>
        );

        // Clear the pending request
        get().ui.clearPendingShareRequest();
      } catch (error) {
        console.error('[ContactsStore] Error declining contact share:', error);
      }
    }
  },

  handlers: {
    onSetContacts: (contacts: Contact[]) => {
      // Convert SharedContact to UI Contact type
      const uiContacts: Contact[] = contacts.map(contact => ({
        ...contact,
        // Add UI-specific properties
        name: contact.name,
        phone: contact.number,
        favorite: contact.favorite ? 1 : 0,
        photo: contact.avatar || undefined
      }));

      set({
        contacts: uiContacts,
        loading: false,
        lastContactsUpdate: Date.now() // Update the timestamp when contacts are refreshed
      });
    },
    onSetCalls: (calls: Call[]) => {
      console.log('[ContactsStore] Setting calls:', calls);
      set({ calls, loading: false });
    },
    onContactAdded: (contact: Contact) => {
      const uiContact: Contact = {
        ...contact,
      };

      set(state => ({ contacts: [...state.contacts, uiContact] }));
    },
    onContactUpdated: (contact: Contact) => {
      const uiContact: Contact = {
        ...contact,
      };

      set(state => ({
        contacts: state.contacts.map(c => (c.id === contact.id ? uiContact : c))
      }));
    },
    onContactDeleted: (contactId: number) => {
      set(state => ({
        contacts: state.contacts.filter(c => c.id !== contactId)
      }));
    },
    onContactShareRequest: (request: ContactShareRequest) => {
      // Store the request and show a modal
      set({ pendingShareRequest: request });

      // Show a notification instead of using the modal store
      console.log('[ContactsStore] Contact share request received:', request);

      // Send a notification about the contact share request
      clientRequests.send('notifications', 'addNotification', {
        id: Date.now(),
        appId: 3, // Contacts app ID
        title: 'Contact Share Request',
        message: `${request.senderName} wants to share a contact with you`,
        type: 'info',
        timestamp: Date.now(),
        read: false,
        icon: 'address-card'
      });
    },
    onContactShareSent: (status: ContactShareStatus) => {
      // Update the sharing status for this target
      const key = `${status.contactId}_${status.targetPlayerId}`;
      set(state => ({
        sharingStatus: {
          ...state.sharingStatus,
          [key]: status
        }
      }));
    },
    onContactShareUpdated: (status: ContactShareStatus) => {
      // Update the sharing status for this target
      const key = `${status.contactId}_${status.targetPlayerId}`;
      set(state => ({
        sharingStatus: {
          ...state.sharingStatus,
          [key]: status
        }
      }));
    },
    onContactShareAccepted: (_contactId: number) => {
      // Clear the pending request
      console.log('[ContactsStore] Contact share accepted', _contactId);
      get().ui.clearPendingShareRequest();
    }

  },

  ui: {
    searchContacts: (query: string) => {
      const state = get();
      const searchTerm = query.toLowerCase();
      return state.contacts.filter(
        contact =>
          contact.name.toLowerCase().includes(searchTerm) || contact.number.includes(searchTerm)
      );
    },
    setSearchTerm: (term: string) => set({ searchTerm: term }),
    toggleContactSelection: (id: number) =>
      set(state => ({
        selectedContacts: state.selectedContacts.includes(id)
          ? state.selectedContacts.filter(contactId => contactId !== id)
          : [...state.selectedContacts, id]
      })),
    clearSelectedContacts: () => set({ selectedContacts: [] }),
    clearPendingShareRequest: () => set({ pendingShareRequest: null })
  }
}));
