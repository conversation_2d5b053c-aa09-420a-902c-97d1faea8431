/**
 * Dialer message handlers for the Contacts app
 */
import { useDialerStore } from '../stores/dialerStore';
import { useContactsStore } from '../stores/contactsStore';
import { CurrentCall } from '../types/dialerTypes';
import { registerEventHandler } from '../../../fivem/clientEventReceiver';

// Define the expected call data structure
interface CallData {
  id: number;
  number: string;
  name?: string;
  photo?: string;
  timestamp: number;
  duration: number;
  status: string;
}

// Register handler for call history
registerEventHandler('dialer', 'calls', (data: unknown) => {
  console.log('[Dialer] Received call history data:', data);

  // Validate data
  if (Array.isArray(data)) {
    // Convert to proper Call type with CallStatus
    const typedCalls = data.map((call: CallData) => ({
      id: call.id,
      owner_number: '', // Will be set by the server
      contact_number: call.number,
      status: call.status as 'missed' | 'answered' | 'outgoing',
      timestamp: call.timestamp,
      duration: call.duration,
      // Add UI-specific properties
      name: call.name || call.number,
      phone: call.number,
      photo: call.photo
    }));

    // Update the store with the call history
    useContactsStore.getState().handlers.onSetCalls(typedCalls);
  } else {
    console.error('[Dialer] Received invalid call history data:', data);
  }
});

// Register handler for call state changes
registerEventHandler('dialer', 'callState', (data: unknown) => {
  console.log('[Dialer] Received call state data:', data);

  // Validate data
  if (typeof data === 'object' && data !== null) {
    // Update the store with the call state
    useDialerStore.getState().handlers.onCallStateChanged(data as Partial<CurrentCall>);
  } else {
    console.error('[Dialer] Received invalid call state data:', data);
  }
});

// Define the expected call ended data structure
interface CallEndedData {
  reason: 'ended' | 'missed' | 'rejected';
}

// Register handler for call ended
registerEventHandler('dialer', 'callEnded', (data: unknown) => {
  console.log('[Dialer] Received call ended data:', data);

  // Validate data
  if (typeof data === 'object' && data !== null && 'reason' in (data as CallEndedData)) {
    // Update the store with the call ended reason
    useDialerStore.getState().handlers.onCallEnded((data as CallEndedData).reason);
  } else {
    console.error('[Dialer] Received invalid call ended data:', data);
    // Default to 'ended' if no reason is provided
    useDialerStore.getState().handlers.onCallEnded('ended');
  }
});

// Define the expected incoming call data structure
interface IncomingCallData {
  number: string;
  name?: string;
  photo?: string;
}

// Register handler for incoming call
registerEventHandler('dialer', 'incomingCall', (data: unknown) => {
  console.log('[Dialer] Received incoming call data:', data);

  // Validate data
  if (typeof data === 'object' && data !== null && 'number' in (data as IncomingCallData)) {
    const callData = data as IncomingCallData;
    // Create a new current call object
    const newCall: CurrentCall = {
      number: callData.number,
      name: callData.name,
      photo: callData.photo,
      state: 'calling',
      startTime: Date.now(),
      duration: 0,
      muted: false,
      speakerOn: false
    };

    // Update the store with the incoming call
    useDialerStore.setState({ currentCall: newCall });
  } else {
    console.error('[Dialer] Received invalid incoming call data:', data);
  }
});
