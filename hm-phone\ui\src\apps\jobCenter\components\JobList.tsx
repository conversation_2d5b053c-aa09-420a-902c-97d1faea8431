import React from 'react';
import { Job } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';

interface JobListProps {
  jobs: Job[];
  onSelectJob: (jobId: number) => void;
  activeGroupCounts: Record<number, number>;
}

const JobList: React.FC<JobListProps> = ({ jobs, onSelectJob, activeGroupCounts }) => {
  // Use a static object instead of a function that returns dynamic class names
  const difficultyColorMap: Record<string, string> = {
    EASY: 'text-green-400',
    MEDIUM: 'text-yellow-400',
    HARD: 'text-orange-400',
    EXPERT: 'text-red-400'
  };

  const formatPayout = (job: Job): string => {
    const { amount, type } = job.payout;

    switch (type) {
      case 'PER_PERSON':
        return `$${amount} per person`;
      case 'SPLIT':
        return `$${amount} split`;
      case 'FIXED':
        return `$${amount} fixed`;
      default:
        return `$${amount}`;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Job listings */}
      <div className="flex-1 overflow-y-auto">
        {jobs.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-white/60 p-4">
            <i className="fas fa-search text-3xl mb-2"></i>
            <p className="text-center">No jobs available.</p>
          </div>
        ) : (
          <div className="space-y-3 p-3">
            {jobs.map(job => (
              <motion.div
                key={job.id}
                layoutId={`job-${job.id}`}
                onClick={() => onSelectJob(job.id)}
                className="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10 hover:bg-white/15 transition-colors p-3"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-white font-medium text-lg">{job.title}</h3>
                    <p className="text-white/70 text-sm">{job.company}</p>
                  </div>

                  <span
                    className={`${
                      difficultyColorMap[job.difficulty] || 'text-white/70'
                    } text-xs px-2 py-1 rounded-full bg-black/30`}
                  >
                    {job.difficulty}
                  </span>
                </div>

                <div className="mt-2 flex justify-between items-center">
                  <div className="flex items-center gap-1 text-white/70 text-sm">
                    <i className="fas fa-money-bill-wave text-white/50"></i>
                    <span>{formatPayout(job)}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1 text-white/70 text-sm">
                      <i className="fas fa-users text-white/50"></i>
                      <span>
                        {job.minPlayers || 1}-{job.maxPlayers || '∞'}
                      </span>
                    </div>

                    <div className="bg-teal-500/20 text-teal-400 text-xs px-2 py-1 rounded-full flex items-center gap-1">
                      <i className="fas fa-user-friends"></i>
                      <span>{activeGroupCounts[job.id] || 0} groups</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default JobList;
