const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// ANSI color codes for colored output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

// Configuration for each resource
const resourceConfigs = [
    {
        name: 'hm-admin',
        path: 'hm-admin',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Admin system for HM Framework'
    },
    {
        name: 'hm-banking',
        path: 'hm-banking',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Banking resource'
    },
    {
        name: 'hm-core',
        path: 'hm-core',
        buildCommand: 'npm run build:game',
        hasUI: false,
        description: 'Core resource'
    },
    {
        name: 'hm-doorlock',
        path: 'hm-doorlock',
        buildCommand: 'npm run build:game',
        hasUI: false,
        description: 'Door lock system'
    },
    {
        name: 'hm-events',
        path: 'hm-events',
        buildCommand: 'npm run build:game',
        hasUI: false,
        description: 'Game event logging'
    },
    {
        name: 'hm-garages',
        path: 'hm-garages',
        buildCommand: 'npm run build',
        hasUI: false,
        description: 'Garage system'
    },
    {
        name: 'hm-hud',
        path: 'hm-hud',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Modern HUD system'
    },
    {
        name: 'hm-inventory',
        path: 'hm-inventory',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Inventory resource'
    },
    {
        name: 'hm-multicharacter',
        path: 'hm-multicharacter',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Multicharacter system'
    },
    {
        name: 'hm-phone',
        path: 'hm-phone',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Phone resource'
    },
    {
        name: 'hm-polyzones',
        path: 'hm-polyzones',
        buildCommand: 'npm run build',
        hasUI: false,
        description: 'PolyZone and grid management'
    },
    {
        name: 'hm-target',
        path: 'hm-target',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Professional targeting system'
    },
    {
        name: 'hm-weapons',
        path: 'hm-weapons',
        buildCommand: 'npm run build:all',
        hasUI: true,
        description: 'Weapons resource'
    }
];

// Resources that don't need building (Lua only)
const luaOnlyResources = [
    'hm-carbomb',
    'hm-radio',
    'hm-rendertargets'
];

class BuildManager {
    constructor() {
        this.startTime = performance.now();
        this.results = {
            success: [],
            failed: [],
            skipped: [],
            total: 0
        };
        this.parallel = process.argv.includes('--parallel');
        this.verbose = process.argv.includes('--verbose');
        this.skipInstall = process.argv.includes('--skip-install');
        this.onlyResource = process.argv.find(arg => arg.startsWith('--only='))?.replace('--only=', '');
    }

    log(message, color = 'white') {
        console.log(`${colors[color]}${message}${colors.reset}`);
    }

    logSuccess(message) {
        this.log(`✅ ${message}`, 'green');
    }

    logError(message) {
        this.log(`❌ ${message}`, 'red');
    }

    logWarning(message) {
        this.log(`⚠️  ${message}`, 'yellow');
    }

    logInfo(message) {
        this.log(`ℹ️  ${message}`, 'blue');
    }

    resourceExists(resourcePath) {
        return fs.existsSync(resourcePath) && fs.existsSync(path.join(resourcePath, 'package.json'));
    }

    async installDependencies(resourcePath, resourceName) {
        if (this.skipInstall) {
            this.logInfo(`Skipping dependency installation for ${resourceName}`);
            return true;
        }

        try {
            this.logInfo(`Installing dependencies for ${resourceName}...`);
            const startTime = performance.now();
            
            execSync('npm ci --silent', {
                cwd: resourcePath,
                stdio: this.verbose ? 'inherit' : 'pipe'
            });

            // Install UI dependencies if they exist
            const uiPath = path.join(resourcePath, 'ui');
            if (fs.existsSync(uiPath) && fs.existsSync(path.join(uiPath, 'package.json'))) {
                execSync('npm ci --silent', {
                    cwd: uiPath,
                    stdio: this.verbose ? 'inherit' : 'pipe'
                });
            }

            const duration = ((performance.now() - startTime) / 1000).toFixed(2);
            this.logSuccess(`Dependencies installed for ${resourceName} (${duration}s)`);
            return true;
        } catch (error) {
            this.logError(`Failed to install dependencies for ${resourceName}: ${error.message}`);
            return false;
        }
    }

    async buildResource(config) {
        const resourcePath = path.resolve(__dirname, config.path);
        
        if (!this.resourceExists(resourcePath)) {
            this.logWarning(`Resource ${config.name} not found, skipping...`);
            this.results.skipped.push(config.name);
            return { success: false, skipped: true };
        }

        try {
            this.logInfo(`Building ${config.name} - ${config.description}`);
            const startTime = performance.now();

            // Install dependencies first
            const installSuccess = await this.installDependencies(resourcePath, config.name);
            if (!installSuccess) {
                throw new Error('Dependency installation failed');
            }

            // Run build command
            execSync(config.buildCommand, {
                cwd: resourcePath,
                stdio: this.verbose ? 'inherit' : 'pipe'
            });

            const duration = ((performance.now() - startTime) / 1000).toFixed(2);
            this.logSuccess(`${config.name} built successfully! (${duration}s)`);
            this.results.success.push(config.name);
            return { success: true, duration };

        } catch (error) {
            this.logError(`Failed to build ${config.name}: ${error.message}`);
            this.results.failed.push({ name: config.name, error: error.message });
            return { success: false, error: error.message };
        }
    }

    async buildAll() {
        this.log('\n🚀 Starting HM Framework Production Build', 'cyan');
        this.log('==========================================', 'cyan');

        // Filter resources if --only flag is used
        let resourcesToBuild = resourceConfigs;
        if (this.onlyResource) {
            resourcesToBuild = resourceConfigs.filter(config => 
                config.name === this.onlyResource || config.name === `hm-${this.onlyResource}`
            );
            if (resourcesToBuild.length === 0) {
                this.logError(`Resource "${this.onlyResource}" not found!`);
                process.exit(1);
            }
            this.logInfo(`Building only: ${resourcesToBuild[0].name}`);
        }

        this.results.total = resourcesToBuild.length + luaOnlyResources.length;

        if (this.parallel && !this.onlyResource) {
            this.logInfo('Building resources in parallel...');
            const promises = resourcesToBuild.map(config => this.buildResource(config));
            await Promise.allSettled(promises);
        } else {
            this.logInfo('Building resources sequentially...');
            for (const config of resourcesToBuild) {
                await this.buildResource(config);
            }
        }

        // Handle Lua-only resources
        if (!this.onlyResource) {
            this.logInfo('Checking Lua-only resources...');
            for (const resourceName of luaOnlyResources) {
                const resourcePath = path.resolve(__dirname, resourceName);
                if (fs.existsSync(resourcePath)) {
                    this.logSuccess(`${resourceName} - Lua only, no build required`);
                    this.results.success.push(resourceName);
                } else {
                    this.logWarning(`${resourceName} not found, skipping...`);
                    this.results.skipped.push(resourceName);
                }
            }
        }

        this.printSummary();
    }

    printSummary() {
        const totalTime = ((performance.now() - this.startTime) / 1000).toFixed(2);
        
        this.log('\n📊 Build Summary', 'cyan');
        this.log('================', 'cyan');
        this.log(`Total time: ${totalTime}s`, 'white');
        this.log(`Total resources: ${this.results.total}`, 'white');
        this.logSuccess(`Successful: ${this.results.success.length}`);
        this.logError(`Failed: ${this.results.failed.length}`);
        this.logWarning(`Skipped: ${this.results.skipped.length}`);

        if (this.results.success.length > 0) {
            this.log('\n✅ Successfully built:', 'green');
            this.results.success.forEach(name => this.log(`  • ${name}`, 'green'));
        }

        if (this.results.failed.length > 0) {
            this.log('\n❌ Failed to build:', 'red');
            this.results.failed.forEach(item => {
                this.log(`  • ${item.name}: ${item.error}`, 'red');
            });
        }

        if (this.results.skipped.length > 0) {
            this.log('\n⚠️  Skipped:', 'yellow');
            this.results.skipped.forEach(name => this.log(`  • ${name}`, 'yellow'));
        }

        if (this.results.failed.length > 0) {
            this.log('\n💥 Build completed with errors!', 'red');
            process.exit(1);
        } else {
            this.log('\n🎉 All builds completed successfully!', 'green');
        }
    }

    showHelp() {
        this.log('\n🔧 HM Framework Build Tool', 'cyan');
        this.log('==========================', 'cyan');
        this.log('Usage: node build-all.js [options]', 'white');
        this.log('\nOptions:', 'yellow');
        this.log('  --parallel      Build resources in parallel (faster but less readable output)', 'white');
        this.log('  --verbose       Show detailed build output', 'white');
        this.log('  --skip-install  Skip npm dependency installation', 'white');
        this.log('  --only=<name>   Build only specific resource (e.g., --only=admin)', 'white');
        this.log('  --help         Show this help message', 'white');
        this.log('\nExamples:', 'yellow');
        this.log('  node build-all.js                    Build all resources', 'white');
        this.log('  node build-all.js --parallel          Build all resources in parallel', 'white');
        this.log('  node build-all.js --only=admin        Build only hm-admin', 'white');
        this.log('  node build-all.js --verbose --parallel   Parallel build with detailed output', 'white');
        this.log('\nSupported Resources:', 'yellow');
        resourceConfigs.forEach(config => {
            const uiTag = config.hasUI ? ' (with UI)' : '';
            this.log(`  • ${config.name}${uiTag} - ${config.description}`, 'white');
        });
        luaOnlyResources.forEach(name => {
            this.log(`  • ${name} (Lua only)`, 'white');
        });
    }
}

// Main execution
async function main() {
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
        const builder = new BuildManager();
        builder.showHelp();
        return;
    }

    const builder = new BuildManager();
    
    try {
        await builder.buildAll();
    } catch (error) {
        builder.logError(`Build process failed: ${error.message}`);
        process.exit(1);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n⚠️  Build process interrupted by user');
    process.exit(1);
});

process.on('SIGTERM', () => {
    console.log('\n\n⚠️  Build process terminated');
    process.exit(1);
});

// Run the build
main().catch(error => {
    console.error('Fatal error:', error.message);
    process.exit(1);
});
