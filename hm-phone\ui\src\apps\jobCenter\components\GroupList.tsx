import React, { useState } from 'react';
import { Job, JobGroup } from '../types/jobCenterTypes';
import { motion } from 'framer-motion';

interface GroupListProps {
  job: Job;
  groups: JobGroup[];
  onBack: () => void;
  onJoinGroup: (groupId: number, password?: string) => void;
  onViewGroup: (groupId: number) => void;
}

const GroupList: React.FC<GroupListProps> = ({ job, groups, onBack, onJoinGroup, onViewGroup }) => {
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const formatTimeAgo = (timestamp: number): string => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);

    if (seconds < 60) return `${seconds}s ago`;

    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;

    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  const handleJoinGroup = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    if (!group) return;

    if (group.isPrivate) {
      setSelectedGroupId(groupId);
    } else {
      onJoinGroup(groupId);
    }
  };

  const handlePasswordSubmit = () => {
    if (!selectedGroupId) return;

    if (!password.trim()) {
      setPasswordError('Password is required');
      return;
    }

    onJoinGroup(selectedGroupId, password);
    setSelectedGroupId(null);
    setPassword('');
    setPasswordError(null);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10 flex items-center">
        <button
          onClick={onBack}
          className="w-8 h-8 flex items-center justify-center text-white/70 hover:text-white"
        >
          <i className="fas fa-arrow-left"></i>
        </button>
        <h2 className="text-white font-medium ml-2">Groups for {job.title}</h2>
      </div>

      {/* Group list */}
      <div className="flex-1 overflow-y-auto p-4">
        {groups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-white/60 p-4">
            <i className="fas fa-users text-3xl mb-2"></i>
            <p className="text-center">No active groups for this job.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {groups.map(group => (
              <div
                key={group.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden border border-white/10"
              >
                <div className="p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3
                        className="text-white font-medium text-lg cursor-pointer hover:text-teal-400"
                        onClick={() => onViewGroup(group.id)}
                      >
                        {group.name}
                      </h3>
                      <p className="text-white/70 text-sm">Leader: {group.leader.name}</p>
                    </div>

                    <div className="flex items-center">
                      {group.isPrivate && (
                        <span className="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-1 rounded-full mr-2">
                          <i className="fas fa-lock mr-1"></i>
                          Private
                        </span>
                      )}
                      <span className="bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded-full">
                        {group.members.length}/{job.maxPlayers || '∞'}
                      </span>
                    </div>
                  </div>

                  <div className="mt-2 flex flex-wrap gap-x-4 gap-y-1 text-xs text-white/60">
                    <div className="flex items-center gap-1">
                      <i className="fas fa-users"></i>
                      <span>
                        {group.members.length} member{group.members.length !== 1 ? 's' : ''}
                      </span>
                    </div>

                    <div className="flex items-center gap-1">
                      <i className="fas fa-clock"></i>
                      <span>Created {formatTimeAgo(group.createdAt)}</span>
                    </div>

                    {group.meetupLocation && (
                      <div className="flex items-center gap-1">
                        <i className="fas fa-map-marker-alt"></i>
                        <span>{group.meetupLocation}</span>
                      </div>
                    )}
                  </div>

                  <div className="mt-3 flex justify-end">
                    <button
                      onClick={() => handleJoinGroup(group.id)}
                      className="text-xs px-3 py-1.5 bg-teal-500/20 text-teal-400 rounded-full hover:bg-teal-500/30 transition-colors"
                    >
                      Join Group
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Password modal */}
      {selectedGroupId && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center p-4">
          <div className="bg-[#0a0f1a] rounded-lg p-4 w-full max-w-xs border border-white/10">
            <h3 className="text-white font-medium mb-3">Enter Group Password</h3>
            <input
              type="password"
              value={password}
              onChange={e => {
                setPassword(e.target.value);
                setPasswordError(null);
              }}
              placeholder="Password"
              className={`w-full p-3 rounded-lg bg-white/10 text-white border ${
                passwordError ? 'border-red-500' : 'border-white/20'
              } focus:outline-none focus:border-teal-500 mb-2`}
            />
            {passwordError && <p className="text-red-500 text-xs mb-2">{passwordError}</p>}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setSelectedGroupId(null);
                  setPassword('');
                  setPasswordError(null);
                }}
                className="flex-1 py-2 rounded-lg bg-white/10 text-white/70 font-medium hover:bg-white/20 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handlePasswordSubmit}
                className="flex-1 py-2 rounded-lg bg-teal-500 text-white font-medium hover:bg-teal-600 transition-colors text-sm"
              >
                Join
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default GroupList;
