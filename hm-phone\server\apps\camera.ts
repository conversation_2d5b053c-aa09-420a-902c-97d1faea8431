/**
 * Camera App - Server Side
 *
 * This file handles server-side functionality for the Camera app.
 */

import { webhookService } from '../services/webhook';
import config from '@shared/config';

// Extract webhook config for easier access
const { webhook: webhookConfig } = config;

/**
 * Initialize the camera app
 */
export function initializeCameraApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
    
    // Listen for player loaded event to ensure camera data is available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Camera] Player ${source} loaded, camera functionality ready`);
    });
}

/**
 * Register server events for the camera app
 */
function registerServerEvents(): void {
    // Register event for saving photos
    onNet('hm-phone:savePhoto', async (imageData: string, metadata: any) => {
        const source = global.source;
        console.log(`[Camera] Received savePhoto event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Camera] Player ${source} not found`);
                emitNet('hm-phone:photoSaveError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Camera] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:photoSaveError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support, fallback to identifier
            const playerIdentifier = stateid || identifier;

            // Add player information to metadata
            const enhancedMetadata = {
                ...metadata,
                playerName: player.name || 'Unknown',
                identifier: playerIdentifier
            };

            // Save the photo
            const result = await savePhoto(imageData, enhancedMetadata);

            // Send the result back to the client
            emitNet('hm-phone:photoSaved', source, result);
        } catch (error) {
            console.error('[Camera] Error saving photo:', error);
            emitNet('hm-phone:photoSaveError', source, 'Failed to save photo');
        }
    });

    // Register event for saving videos
    onNet('hm-phone:saveVideo', async (videoData: string, metadata: any) => {
        const source = global.source;
        console.log(`[Camera] Received saveVideo event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Camera] Player ${source} not found`);
                emitNet('hm-phone:videoSaveError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Camera] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:videoSaveError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Add player information to metadata
            const enhancedMetadata = {
                ...metadata,
                playerName: player.name || 'Unknown',
                identifier: playerIdentifier,
                mode: 'video' // Ensure mode is set to video
            };

            // Save the video (using the same function as photos)
            const result = await savePhoto(videoData, enhancedMetadata);

            // Send the result back to the client
            emitNet('hm-phone:videoSaved', source, result);
        } catch (error) {
            console.error('[Camera] Error saving video:', error);
            emitNet('hm-phone:videoSaveError', source, 'Failed to save video');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[Camera] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[Camera] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_photos table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_photos (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    imageUrl TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata JSON,
                    INDEX idx_identifier (identifier)
                )
            `,
                [],
                () => {
                    console.log('[Camera] Database tables initialized');
                }
            );
        } else {
            console.error('[Camera] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[Camera] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Save a photo or video to the database
 * @param imageData Base64-encoded image/video data
 * @param metadata Photo/video metadata
 * @returns The saved photo/video data
 */
async function savePhoto(imageData: string, metadata: any): Promise<any> {
    console.log('[Camera] Saving media with metadata:', metadata);

    try {
        let imageUrl = '';

        // If webhooks are enabled, send the media to the webhook
        if (webhookConfig.general.enabled) {
            try {
                console.log('[Camera] Sending media to webhook');
                // Use different webhook methods based on media type
                if (metadata.mode === 'video') {
                    // For videos
                    imageUrl = await webhookService.sendMediaToWebhook('video', imageData, metadata);
                } else {
                    // For photos
                    imageUrl = await webhookService.sendPhoto(imageData, metadata);
                }
                console.log('[Camera] Media sent to webhook, URL:', imageUrl);
            } catch (error) {
                console.error('[Camera] Error sending media to webhook:', error);
                // If webhook fails, we'll use the base64 data as fallback
                imageUrl = imageData;
            }
        } else {
            // If webhooks are disabled, use the base64 data
            imageUrl = imageData;
        }

        // Save the media to the database
        let mediaId;

        try {
            // Check if oxmysql is available
            if (!global.global.exports.oxmysql) {
                throw new Error('oxmysql is not available');
            }

            // Check if insert_async method exists
            if (typeof global.exports.oxmysql.insert_async === 'function') {
                // Use insert_async if available
                const result = await global.exports.oxmysql.insert_async(
                    'INSERT INTO phone_photos (identifier, imageUrl, metadata) VALUES (?, ?, ?)',
                    [
                        metadata.identifier,
                        imageUrl,
                        JSON.stringify({
                            mode: metadata.mode || 'photo',
                            flash: metadata.flash || false,
                            location: metadata.location || null
                        })
                    ]
                );

                // Get the inserted ID
                mediaId = result;
            } else if (typeof global.exports.oxmysql.insert === 'function') {
                // Fall back to insert if insert_async is not available
                mediaId = await new Promise((resolve, reject) => {
                    global.exports.oxmysql.insert(
                        'INSERT INTO phone_photos (identifier, imageUrl, metadata) VALUES (?, ?, ?)',
                        [
                            metadata.identifier,
                            imageUrl,
                            JSON.stringify({
                                mode: metadata.mode || 'photo',
                                flash: metadata.flash || false,
                                location: metadata.location || null
                            })
                        ],
                        (result: any) => {
                            if (result) {
                                resolve(result);
                            } else {
                                reject(new Error('Failed to insert media'));
                            }
                        }
                    );
                });
            } else {
                // If neither method is available, throw an error
                throw new Error('No suitable database insert method found');
            }
        } catch (error: any) {
            console.error('[Camera] Database error when saving media:', error);
            throw new Error(`Database error: ${error?.message || 'Unknown database error'}`);
        }

        // Return the media data
        return {
            id: mediaId,
            identifier: metadata.identifier,
            imageUrl: imageUrl,
            timestamp: Date.now(),
            metadata: {
                mode: metadata.mode || 'photo',
                flash: metadata.flash || false,
                location: metadata.location || null
            }
        };
    } catch (error) {
        console.error('[Camera] Error saving media to database:', error);
        throw error;
    }
}
