import React from 'react';
import { useInventoryStore } from '../../stores/inventoryStore';
import { ProgressBar } from '@shared/inventory.types';

interface ProgressBarItemProps {
  progressBar: ProgressBar;
}

const ProgressBarItem: React.FC<ProgressBarItemProps> = ({ progressBar }) => {
  const colorStyles = {
    green: {
      bg: 'bg-green-500',
      border: 'border-green-500/20',
      borderAccent: 'border-l-green-400/80',
      gradient: 'from-green-400/10 via-green-500/10 to-green-600/10',
      icon: 'text-green-400/80',
      shadow: '0 0 8px rgba(34, 197, 94, 0.4)'
    },
    blue: {
      bg: 'bg-blue-500',
      border: 'border-blue-500/20',
      borderAccent: 'border-l-blue-400/80',
      gradient: 'from-blue-400/10 via-blue-500/10 to-blue-600/10',
      icon: 'text-blue-400/80',
      shadow: '0 0 8px rgba(59, 130, 246, 0.4)'
    },
    orange: {
      bg: 'bg-orange-500',
      border: 'border-orange-500/20',
      borderAccent: 'border-l-orange-400/80',
      gradient: 'from-orange-400/10 via-orange-500/10 to-orange-600/10',
      icon: 'text-orange-400/80',
      shadow: '0 0 8px rgba(249, 115, 22, 0.4)'
    },
    red: {
      bg: 'bg-red-500',
      border: 'border-red-500/20',
      borderAccent: 'border-l-red-400/80',
      gradient: 'from-red-400/10 via-red-500/10 to-red-600/10',
      icon: 'text-red-400/80',
      shadow: '0 0 8px rgba(239, 68, 68, 0.4)'
    }
  };

  const style = colorStyles[progressBar.color || 'green'];
  return (
    <div className={`bg-neutral-800 ${style.border} border border-neutral-700/50 ${style.borderAccent} border-l-4 rounded-lg p-3 shadow-xl relative overflow-hidden`}>
      {/* Top accent gradient */}
      <div className={`absolute left-0 top-0 w-full h-1 bg-gradient-to-r ${style.gradient} opacity-30 z-10 rounded-t-lg`} />
      
      {/* Header - Icon and text on the same line */}
      <div className="flex items-center gap-3 mb-2 relative z-20">
        {progressBar.icon && (
          <div className="flex-shrink-0">
            <i className={`fas ${progressBar.icon} ${style.icon} text-lg`} />
          </div>
        )}
        <div className="flex-1 text-neutral-200 font-medium text-sm">
          {progressBar.label}
        </div>
      </div>
      
      {/* Progress bar and percentage on the same line */}
      <div className="flex items-center gap-2 relative z-20">
        <div className="flex-1 relative h-2 bg-neutral-700/80 rounded-full overflow-hidden">
          {/* Progress fill */}
          <div 
            className={`h-full bg-gradient-to-r ${style.bg} transition-all duration-300 ease-out relative overflow-hidden`}
            style={{ 
              width: `${progressBar.progress}%`,
              boxShadow: style.shadow
            }}
          >
            {/* Animated shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine" />
          </div>
        </div>
        <div className="flex-shrink-0 text-neutral-400 text-xs w-9 text-right">
          {Math.round(progressBar.progress)}%
        </div>
      </div>
    </div>
  );
};

interface ProgressBarContainerProps {
  isQuickAccessVisible: boolean;
}

const ProgressBarContainer: React.FC<ProgressBarContainerProps> = ({ isQuickAccessVisible }) => {
  const progressBars = useInventoryStore(state => state.progressBars);

  if (progressBars.length === 0) return null;
  // Position above the quick access bar
  const bottomOffset = isQuickAccessVisible ? 'bottom-24' : 'bottom-8';

  return (
    <div className={`fixed ${bottomOffset} left-1/2 -translate-x-1/2 z-[90] pointer-events-none`}>
      <div className="flex flex-col gap-2 pointer-events-auto filter drop-shadow-xl">
        {progressBars.map((progressBar) => (
          <ProgressBarItem 
            key={progressBar.id} 
            progressBar={progressBar}
          />
        ))}
      </div>
    </div>
  );
};

export default ProgressBarContainer;
