/**
 * Camera Controls Service
 *
 * This service handles enabling and disabling first-person camera controls
 * while using the camera app.
 */

// Track the state of camera controls
let cameraControlsEnabled = false;
let cameraControlsTickId: number | null = null;
let originalCameraView = 0;

/**
 * Initialize camera controls service
 */
export function initializeCameraControlsService(): void {
    console.log('[CameraControls] Initializing camera controls service');

    // Register NUI callback for enabling/disabling camera controls
    RegisterNuiCallbackType('enableCameraControls');
    on('__cfx_nui:enableCameraControls', (data: { enabled: boolean }, cb: (response: any) => void) => {
        if (data.enabled) {
            enableFirstPersonCamera();
        } else {
            disableFirstPersonCamera();
        }

        // Send response back to NUI
        cb({ success: true });
    });
}

/**
 * Enable first-person camera and looking controls
 * This allows the player to look around while using the camera
 */
function enableFirstPersonCamera(): void {
    if (cameraControlsEnabled) return;

    console.log('[CameraControls] Enabling first-person camera and looking controls');

    // Store the original camera view
    originalCameraView = GetFollowPedCamViewMode();

    // Set first-person camera view
    // View mode values:
    // 0 = THIRD_PERSON_NEAR
    // 1 = THIRD_PERSON_MEDIUM
    // 2 = THIRD_PERSON_FAR
    // 3 = CINEMATIC
    // 4 = FIRST_PERSON
    SetFollowPedCamViewMode(4); // 4 = FIRST_PERSON

    // Enable camera controls
    cameraControlsEnabled = true;

    // Temporarily modify NUI focus settings to allow camera controls
    // This is critical - we need to override the phone's NUI focus settings
    // to allow looking around while in camera mode
    SetNuiFocus(false, true); // Keep mouse focus for UI but disable keyboard focus
    SetNuiFocusKeepInput(true); // Allow game input while NUI is focused

    // Start a tick handler to enable camera controls
    if (cameraControlsTickId === null) {
        cameraControlsTickId = setTick(() => {
            // Enable camera controls (look left/right, up/down)
            // Control group 0 is for general controls
            EnableControlAction(0, 1, true); // Look Left/Right
            EnableControlAction(0, 2, true); // Look Up/Down

            // Enable movement controls too
            EnableControlAction(0, 30, true); // Move Left/Right
            EnableControlAction(0, 31, true); // Move Up/Down
            EnableControlAction(0, 21, true); // Sprint
            EnableControlAction(0, 22, true); // Jump

            // Disable attack controls to prevent accidental shooting
            DisableControlAction(0, 24, true); // Attack (LMB)
            DisableControlAction(0, 25, true); // Aim (RMB)
            DisableControlAction(0, 140, true); // Melee Attack Light
            DisableControlAction(0, 141, true); // Melee Attack Heavy
            DisableControlAction(0, 142, true); // Melee Attack Alternate
            DisableControlAction(0, 257, true); // Attack 2
            DisableControlAction(0, 263, true); // Melee Attack 1

            // Keep first-person camera view in case something tries to change it
            if (GetFollowPedCamViewMode() != 4) {
                SetFollowPedCamViewMode(4);
            }

            // Force first-person view in vehicles too
            if (IsPedInAnyVehicle(PlayerPedId(), false)) {
                SetFollowVehicleCamViewMode(4);
            }
        });
    }
}

/**
 * Disable first-person camera and restore original camera view
 */
function disableFirstPersonCamera(): void {
    if (!cameraControlsEnabled) return;

    console.log('[CameraControls] Disabling first-person camera and restoring original view');

    // Disable camera controls
    cameraControlsEnabled = false;

    // Restore original camera view
    SetFollowPedCamViewMode(originalCameraView);

    // Also restore vehicle camera view if in a vehicle
    if (IsPedInAnyVehicle(PlayerPedId(), false)) {
        SetFollowVehicleCamViewMode(originalCameraView);
    }

    // Stop the tick handler
    if (cameraControlsTickId !== null) {
        clearTick(cameraControlsTickId);
        cameraControlsTickId = null;
    }

    // Re-enable all controls that might have been disabled
    EnableControlAction(0, 24, true); // Attack (LMB)
    EnableControlAction(0, 25, true); // Aim (RMB)
    EnableControlAction(0, 140, true); // Melee Attack Light
    EnableControlAction(0, 141, true); // Melee Attack Heavy
    EnableControlAction(0, 142, true); // Melee Attack Alternate
    EnableControlAction(0, 257, true); // Attack 2
    EnableControlAction(0, 263, true); // Melee Attack 1

    // Restore the phone's original NUI focus settings
    // This is critical - we need to restore the phone's NUI focus settings
    // when exiting camera mode
    SetNuiFocus(true, true); // Restore full NUI focus for the phone UI
    SetNuiFocusKeepInput(true); // Keep allowing game input while NUI is focused
}

/**
 * Clean up camera controls resources
 */
export function cleanupCameraControlsResources(): void {
    disableFirstPersonCamera();
}

// Export camera controls service
export const cameraControlsService = {
    initializeCameraControlsService,
    enableFirstPersonCamera,
    disableFirstPersonCamera,
    cleanupCameraControlsResources
};

export default cameraControlsService;
