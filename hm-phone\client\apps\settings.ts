/**
 * Settings App - Client Side
 *
 * This file handles client-side functionality for the Settings app.
 * The Settings app is initialized on start since it has system-wide impact.
 */

import { registerAppHandler } from '../nui';
import config from '@shared/config';

// Default settings
const defaultSettings = {
    wallpaper: config.ui.defaultSettings.wallpaper || '1',
    ringtone: config.ui.defaultSettings.ringtone || 'default',
    notificationSound: config.ui.defaultSettings.notificationSound || 'default',
    theme: config.ui.defaultSettings.theme || 'dark',
    fontSize: config.ui.defaultSettings.fontSize || 'medium',
    language: config.ui.defaultSettings.language || 'en',
    doNotDisturb: config.ui.defaultSettings.doNotDisturb !== undefined ? config.ui.defaultSettings.doNotDisturb : false,
    airplaneMode: config.ui.defaultSettings.airplaneMode !== undefined ? config.ui.defaultSettings.airplaneMode : false,
    showNotificationsOnLockScreen:
        config.ui.defaultSettings.showNotificationsOnLockScreen !== undefined
            ? config.ui.defaultSettings.showNotificationsOnLockScreen
            : true,
    vibrate: config.ui.defaultSettings.vibrate !== undefined ? config.ui.defaultSettings.vibrate : true,
    volume: config.ui.defaultSettings.volume !== undefined ? config.ui.defaultSettings.volume : 80,
    brightness: config.ui.defaultSettings.brightness !== undefined ? config.ui.defaultSettings.brightness : 70,
    autoLock: config.ui.defaultSettings.autoLock !== undefined ? config.ui.defaultSettings.autoLock : 30,
    lockScreenType: config.ui.defaultSettings.lockScreenType || 'none'
};

// Settings cache
let settingsCache = { ...defaultSettings };

/**
 * Initialize the settings app
 * This only registers handlers but doesn't load data yet
 */
export function initializeSettingsApp(): void {
    registerNUIHandlers();
}

/**
 * Load settings data from server
 * This should only be called after a player is loaded
 */
export function loadSettingsData(): void {
    loadSettings();
}

/**
 * Register NUI handlers for the settings app
 */
function registerNUIHandlers(): void {
    // Register handler for getting phone settings
    registerAppHandler('settings', 'getPhoneSettings', async () => {
        try {
            return { success: true, data: settingsCache };
        } catch (error) {
            console.error('[Settings] Error getting phone settings:', error);
            return { success: false, error: 'Failed to get phone settings' };
        }
    });

    // Register handler for updating phone settings
    registerAppHandler('settings', 'updatePhoneSettings', async (data: any) => {
        try {
            const { settings } = data;
            // Update the cache
            settingsCache = { ...settingsCache, ...settings };
            // Save the settings to the server
            emitNet('hm-phone:saveSettings', settingsCache);

            // Apply the settings
            applySettings(settingsCache);

            // Return success to the UI
            return { success: true, data: settingsCache };
        } catch (error) {
            console.error('[Settings] Error updating phone settings:', error);
            return { success: false, error: 'Failed to update phone settings' };
        }
    });

    // Register handler for resetting phone settings
    registerAppHandler('settings', 'resetPhoneSettings', async () => {
        try {
            settingsCache = { ...defaultSettings };
            emitNet('hm-phone:saveSettings', settingsCache);
            applySettings(settingsCache);

            return { success: true, data: settingsCache };
        } catch (error) {
            console.error('[Settings] Error resetting phone settings:', error);
            return { success: false, error: 'Failed to reset phone settings' };
        }
    });

    // Register event for settings loaded
    onNet('hm-phone:settingsLoaded', (settings: any) => {
        // Update the cache
        settingsCache = { ...defaultSettings, ...settings };
        applySettings(settingsCache);
        sendToUI('settingsLoaded', settingsCache);
    });

    // Register event for settings error
    onNet('hm-phone:settingsError', (errorMessage: string) => {
        settingsCache = { ...defaultSettings };
        applySettings(settingsCache);
        sendToUI('settingsLoaded', settingsCache);
        // Also send the error to the UI
        sendToUI('error', errorMessage);
    });
}

/**
 * Load settings from the server
 */
function loadSettings(): void {

    try {
        // Get player data
        const identifier = global.playerIdentifier;
        const stateid = global.playerStateId;

        // Check if player data is available
        if (!stateid && !identifier) {
            // Apply default settings
            settingsCache = { ...defaultSettings };
            applySettings(settingsCache);

            // Send the settings to the UI
            sendToUI('settingsLoaded', settingsCache);
            return;
        }

        console.log(`[Settings] Loading settings for player: ${stateid || identifier}`);

        // Request settings from the server
        emitNet('hm-phone:getSettings');
    } catch (error) {
        console.error('[Settings] Error loading settings:', error);

        settingsCache = { ...defaultSettings };
        applySettings(settingsCache);

        // Send the settings to the UI
        sendToUI('settingsLoaded', settingsCache);
    }
}

/**
 * Apply settings to the phone
 * @param settings Settings to apply
 */
function applySettings(settings: any): void {
    // Apply theme
    if (settings.theme) {
        console.log(`[Settings] Applied theme`);
    }
    // Apply font size
    if (settings.fontSize) {
        // This would normally update the font size
    }
    if (settings.doNotDisturb !== undefined) {
        // This would normally update the do not disturb mode
    }

    // Apply airplane mode
    if (settings.airplaneMode !== undefined) {
        // This would normally update the airplane mode
    }
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'settings',
        type,
        data
    });
}
