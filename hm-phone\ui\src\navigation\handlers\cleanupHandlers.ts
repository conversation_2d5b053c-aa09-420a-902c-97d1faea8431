/**
 * Cleanup message handlers
 *
 * This file handles cleanup events from the client to optimize memory usage
 * when the phone is closed.
 */
import { registerEventHandler } from '../../fivem/clientEventReceiver';
import { useMessagesStore } from '../../apps/messages/stores/messagesStore';
import { usePhotosStore } from '../../apps/photos/stores/photosStore';
import { useLifeSnapStore } from '../../apps/lifeSnap/stores/lifeSnapStore';
import { useMusicStore } from '../../apps/music/stores/musicStore';
import { useYellowPagesStore } from '../../apps/yellowPages/stores/yellowPagesStore';

interface CleanupData {
  retainApps: string[];
  discardApps: string[];
}

// Register handler for cleanup events
registerEventHandler('phone', 'cleanup', (data) => {
  console.log('[Cleanup] Received cleanup request:', data);

  // Validate data
  if (data && typeof data === 'object' && 'discardApps' in data) {
    const cleanupData = data as CleanupData;

    // Process apps that should be cleaned up
    cleanupData.discardApps.forEach(appId => {
      switch (appId) {
        case 'messages':
          { console.log('[Cleanup] Cleaning up messages data');
          // Clear message content but keep conversation list
          const messagesStore = useMessagesStore.getState();
          // Keep conversation list but clear message content
          messagesStore.conversations.forEach(conversation => {
            messagesStore.handlers.onSetMessages(conversation.id, []);
          });
          break; }

        case 'photos':
          console.log('[Cleanup] Cleaning up photos data');
          // Clear full-resolution photos but keep thumbnails
          usePhotosStore.getState().handlers.onClearFullResolutionPhotos();
          break;

        case 'lifeSnap':
          console.log('[Cleanup] Cleaning up LifeSnap data');
          // Clear posts content but keep basic data
          useLifeSnapStore.getState().handlers.onClearPostContent();
          break;

        case 'music':
          { console.log('[Cleanup] Cleaning up music data');
          // Stop any playing music and clear song data
          const musicStore = useMusicStore.getState();
          if (musicStore.isPlaying) {
            musicStore.togglePlay(); // Use togglePlay to stop playback
          }
          // Keep favorites and playlists but clear full song list
          musicStore.handlers.onClearSongData();
          break; }

        case 'yellowPages':
          console.log('[Cleanup] Cleaning up Yellow Pages data');
          // Clear ads content but keep basic data
          useYellowPagesStore.getState().handlers.onClearAdsContent();
          break;

        default:
          console.log(`[Cleanup] No cleanup handler for app: ${appId}`);
          break;
      }
    });

    // Force garbage collection if possible
    if (typeof window.gc === 'function') {
      console.log('[Cleanup] Forcing garbage collection');
      window.gc();
    }

    console.log('[Cleanup] Cleanup complete');
  } else {
    console.error('[Cleanup] Received invalid cleanup data:', data);
  }
});