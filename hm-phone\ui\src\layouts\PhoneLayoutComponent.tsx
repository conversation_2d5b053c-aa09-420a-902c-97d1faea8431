import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import TopBar from '../common/components/TopBar';
import PullIndicator from '../common/components/PullIndicator';
import { useNavigation } from '../navigation/hooks';
import { usePhone } from '../hooks/usePhone';
import { useNotificationBadges } from '../notifications/hooks/useNotificationBadges';
import NotificationSystem from '../notifications/components/NotificationSystem';
import { PhoneStateEnum } from '../common/stores/phoneStateStore';
import { isBrowser } from '../utils/environment';
import { useNavigationStore } from '../navigation/navigationStore';
import CameraControls from '../apps/camera/components/CameraControls';

const PhoneLayoutComponent: React.FC = () => {
  // const { closeApp } = usePhoneStore();
  const { closePhone, phoneState } = usePhone(); // Use state from PhoneProvider
  const location = useLocation();
  const isHome = location.pathname === '/';
  const notificationBadges = useNotificationBadges();
  const { openApp, goHome } = useNavigation();

  // Get the current app from the navigation store
  const currentApp = useNavigationStore(state => state.currentApp);

  // Check if the camera app is open
  const isCameraOpen = currentApp === 'camera';

  // Handle home button click
  const handleHomeClick = () => {
    goHome();
    if (isHome) {
      closePhone();
    }
  };

  // No need for useEffect since we're not changing the visiblePercentage anymore

  return (
    <div className="relative">
      {/* Page background - transparent */}
      <div className="fixed inset-0 bg-transparent pointer-events-none"></div>

      {/* Root level background - gameview.png (only in browser mode) */}
      {isBrowser && (
        <div className="fixed inset-0 z-0 pointer-events-none">
          <img
            src="/gameview.png"
            alt="Game View"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Camera controls - only shown when camera app is open */}
      {isCameraOpen && <CameraControls />}

      {/* Phone container - both content and frame will move together */}
      <div style={{
        position: 'fixed',
        right: '1rem',
        bottom: phoneState === PhoneStateEnum.CLOSED
          ? '-640px'
          : phoneState === PhoneStateEnum.PEEK
            ? `${Math.round((18 / 100) * 640) - 640}px`
            : '1rem',
        width: '20rem',
        height: '640px',
        transition: 'all 0.3s ease',
        opacity: phoneState === PhoneStateEnum.CLOSED ? 0 : 1,
        pointerEvents: phoneState === PhoneStateEnum.CLOSED ? 'none' : 'auto',
        zIndex: 50
      }}>
        {/* Phone content */}
        <div
          className={`absolute inset-0 max-h-[calc(100vh-2rem)]
            bg-black/90 overflow-hidden shadow-[0_10px_25px_rgba(0,0,0,0.5)]
            rounded-[1.125rem] border border-zinc-800`}
        >
          {/* Screen content */}
          <div className="relative flex flex-col h-full overflow-hidden" style={{ zIndex: 20 }}>
            <TopBar />
            <main className="flex-1 overflow-auto phone-main-content" style={{ zIndex: 30 }}>
              <div className="outlet-container h-full" style={{ zIndex: 35 }}>
                <Outlet />
              </div>
            </main>

            {/* Notification system positioned on top of content */}
            <div className="absolute top-8 left-0 right-0 z-50 pointer-events-auto px-2">
              <NotificationSystem />
            </div>

            {!isHome && <PullIndicator />}

            {isHome && (
              <div className="bg-transparent relative">
                <div className="flex justify-around items-center px-2 py-3">
                  <button
                    onClick={() => openApp('dialer')}
                    className="flex flex-col items-center p-2 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <i className="fas fa-phone text-white text-lg drop-shadow-sm"></i>
                  </button>

                  <button
                    onClick={() => openApp('contacts')}
                    className="flex flex-col items-center p-2 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <i className="fas fa-address-book text-white text-lg drop-shadow-sm"></i>
                  </button>

                  <button onClick={handleHomeClick} className="flex flex-col items-center">
                    <div
                      className="w-12 h-12 bg-white/20 border-white/20 hover:bg-white/30 rounded-full flex items-center justify-center
                      border transition-colors shadow-sm"
                    >
                      <i className="fas fa-home text-white text-xl drop-shadow-sm"></i>
                    </div>
                  </button>

                  <button
                    onClick={() => openApp('messages')}
                    className="flex flex-col items-center p-2 rounded-lg hover:bg-white/10 transition-colors relative"
                  >
                    <i className="fas fa-comments text-white text-lg drop-shadow-sm"></i>
                    {/* Messages notification badge */}
                    {notificationBadges[1] > 0 && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-4 h-4 flex items-center justify-center px-1 border border-black/10 shadow-sm">
                        {notificationBadges[1] > 99 ? '99+' : notificationBadges[1]}
                      </div>
                    )}
                  </button>

                  <button
                    onClick={() => openApp('settings')}
                    className="flex flex-col items-center p-2 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <i className="fas fa-cog text-white text-lg drop-shadow-sm"></i>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Phone frame overlay */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Outer shadow for depth */}
          <div className="absolute -inset-4 bg-white/10 blur-md rounded-[3rem] -z-10"></div>
          {/* Frame border with proper inner and outer rounding */}
          <div className="absolute -inset-[10px] border-[10px] border-zinc-700/80 rounded-[1.75rem] z-10 box-content"></div>

          {/* No notch or speaker - completely removed as requested */}

          {/* Side buttons - positioned behind the frame */}
          <div className="absolute -left-[12px] top-[160px] w-3 h-12 bg-white/80 rounded-l-md z-0"></div>
          <div className="absolute -left-[12px] top-[240px] w-3 h-14 bg-white/80 rounded-l-md z-0"></div>
          <div className="absolute -right-[12px] top-[200px] w-3 h-16 bg-white/80 rounded-r-md z-0"></div>
        </div>
      </div>
    </div>
  );
};

export default PhoneLayoutComponent;
