/**
 * HM Weapons - Weapon Benches Configuration
 * 
 * Configuration for weapon crafting/modding benches throughout the map.
 * These benches will be registered as targets using hm-target.
 */

export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

export interface WeaponBench {
  /** Unique identifier for the bench */
  id: string;
  
  /** Display name for the bench */
  name: string;
  
  /** World coordinates of the bench */
  coords: Vector3;
  
  /** Heading/rotation of the bench (in degrees) */
  heading: number;
    /** Model hash or name for the bench prop */
  model: string;
  
  /** Required job to access this bench (optional) */
  requiredJob?: string;
  
  /** Required gang to access this bench (optional) */
  requiredGang?: string;
  
  /** Required item to access this bench (optional) */
  requiredItem?: string;
  
  /** Interaction distance for the target */
  interactionDistance: number;
  
  /** Whether to spawn the prop automatically */
  autoSpawn: boolean;
  
  /** Whether the bench is currently active/enabled */
  enabled: boolean;
  
  /** Custom icon for the target interaction */
  icon?: string;
  
  /** Custom label for the target interaction */
  targetLabel?: string;
}

/**
 * Weapon Benches Configuration
 * Add new benches here to have them automatically registered as targets
 */
export const WEAPON_BENCHES: WeaponBench[] = [  {
    id: 'ammunation_downtown',
    name: 'Ammunation Workshop',
    coords: { x: -1575.89, y: -235.15, z: 60.23 }, // Updated Z to match actual ground level
    heading: 70.0,    model: 'gr_prop_gr_bench_02b',
    requiredJob: 'police', // Example: only police can use this one
    interactionDistance: 2.0,
    autoSpawn: true,
    enabled: true,
    icon: 'fas fa-wrench',
    targetLabel: 'Access Weapon Bench'
  },
  
  {
    id: 'underground_workshop',
    name: 'Underground Weapons Workshop',
    coords: { x: 1009.46, y: -3196.65, z: -39.0 },
    heading: 180.0,    model: 'gr_prop_gr_bench_02b',
    requiredGang: 'families', // Example: gang-restricted
    interactionDistance: 2.0,
    autoSpawn: true,
    enabled: true,
    icon: 'fas fa-industry',
    targetLabel: 'Access Underground Workshop'
  },
  
  {
    id: 'military_base',
    name: 'Military Armory Bench',
    coords: { x: -2358.132, y: 3249.754, z: 92.903 },
    heading: 330.0,    model: 'gr_prop_gr_bench_02b',
    requiredJob: 'military',
    requiredItem: 'military_keycard',
    interactionDistance: 1.5,
    autoSpawn: true,
    enabled: true,
    icon: 'fas fa-shield-alt',
    targetLabel: 'Access Military Workbench'
  },
  
  {
    id: 'garage_workshop',
    name: 'Garage Weapon Bench',
    coords: { x: 155.23, y: -3011.45, z: 7.04 },
    heading: 90.0,    model: 'gr_prop_gr_bench_02b',
    interactionDistance: 2.0,
    autoSpawn: true,
    enabled: true,
    icon: 'fas fa-hammer',
    targetLabel: 'Use Weapon Crafting Bench'
  }
];

/**
 * Configuration for weapon bench targeting system
 */
export const WEAPON_BENCH_CONFIG = {
  /** Default interaction distance if not specified per bench */
  defaultInteractionDistance: 2.0,
  
  /** Default target icon */
  defaultIcon: 'fas fa-tools',
  
  /** Default target label */
  defaultLabel: 'Access Weapon Bench',
    /** Whether to show debug information */
  debug: true,
  
  /** Default prop model if not specified */
  defaultModel: 'gr_prop_gr_bench_02b',
  
  /** Target options that will be added to each bench */
  targetOptions: [
    {
      id: 'weapon_modding',
      label: 'Modify Weapon',
      icon: 'fas fa-wrench'
    },
    {
      id: 'weapon_crafting',
      label: 'Craft Weapon',
      icon: 'fas fa-hammer'
    },
    {
      id: 'weapon_repair',
      label: 'Repair Weapon',
      icon: 'fas fa-tools'
    }
  ]
};

/**
 * Get weapon bench by ID
 */
export function getWeaponBench(id: string): WeaponBench | undefined {
  return WEAPON_BENCHES.find(bench => bench.id === id);
}

/**
 * Get all enabled weapon benches
 */
export function getEnabledWeaponBenches(): WeaponBench[] {
  return WEAPON_BENCHES.filter(bench => bench.enabled);
}

/**
 * Check if a player can access a weapon bench
 */
export function canAccessWeaponBench(bench: WeaponBench, playerJob?: string, playerGang?: string, hasRequiredItem?: boolean): boolean {
  // Check job requirement
  if (bench.requiredJob && bench.requiredJob !== playerJob) {
    return false;
  }
  
  // Check gang requirement
  if (bench.requiredGang && bench.requiredGang !== playerGang) {
    return false;
  }
  
  // Check required item
  if (bench.requiredItem && !hasRequiredItem) {
    return false;
  }
  
  return bench.enabled;
}
