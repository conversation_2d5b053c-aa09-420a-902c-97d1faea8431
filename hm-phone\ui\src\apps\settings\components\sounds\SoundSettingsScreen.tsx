import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useNotificationStore } from '../../../../notifications/stores/notificationStore';
import { useAppStore } from '../../../../common/stores/appStore';
import { slideTransition, staggeredAnimation, fadeTransition } from '../../../../utils/ui';
import { playRingtone, getRingtones } from '../../../../notifications/sounds/notificationSoundSystem';

interface SoundSettingsScreenProps {
  onBack: () => void;
}

interface AppType {
  id: number;
  name: string;
  icon: string;
  type: string;
  colors: {
    bg: string;
    text: string;
  };
}

interface Ringtone {
  id: string;
  name: string;
  path: string;
}

const SoundSettingsScreen: React.FC<SoundSettingsScreenProps> = ({ onBack }) => {
  const {
    notificationSettings,
    updateNotificationSettings,
    updateAppNotificationSettings
  } = useNotificationStore();

  const { apps } = useAppStore();
  const [currentRingtone, setCurrentRingtone] = useState<string>('default');
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [muteAll, setMuteAll] = useState<boolean>(notificationSettings.notificationSoundVolume === 0);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Get available ringtones
  const availableRingtones = getRingtones();
  const ringtones: Ringtone[] = Object.entries(availableRingtones).map(([id, path]) => ({
    id,
    name: id.charAt(0).toUpperCase() + id.slice(1),
    path
  }));

  // Initialize current ringtone based on settings
  useEffect(() => {
    // Find the ringtone ID that matches the stored ringtone path
    const storedRingtonePath = notificationSettings.ringtone;
    const matchingRingtone = ringtones.find(r => r.path === storedRingtonePath);

    if (matchingRingtone) {
      setCurrentRingtone(matchingRingtone.id);
    }
  }, [notificationSettings.ringtone]);

  // Filter apps that should have sound settings
  const soundApps = apps.filter((app: AppType) =>
    [
      // System apps
      'Messages',
      'Contacts',
      'Phone',

      // Store apps
      'Banking',
      'Yellow Pages',
      'Dark Market',
      'LifeSnap',
      'LoveLink',
      'Job Center'
    ].includes(app.name)
  );

  // Group apps by type
  const systemApps = soundApps.filter((app: AppType) => app.type === 'system');
  const storeApps = soundApps.filter((app: AppType) => app.type === 'store');

  // Play ringtone preview
  const playRingtonePreview = (ringtoneId: string) => {
    // If muted, don't play anything
    if (muteAll) {
      return;
    }

    // Stop any currently playing sound
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    // If we're clicking the same ringtone that's already playing, just stop it
    if (isPlaying === ringtoneId) {
      setIsPlaying(null);
      return;
    }

    // Play the selected ringtone with default volume (80%)
    const audio = playRingtone(ringtoneId, 80);
    if (audio) {
      audioRef.current = audio;
      setIsPlaying(ringtoneId);

      // Stop playing after the sound finishes
      audio.onended = () => {
        setIsPlaying(null);
      };
    }
  };

  // Toggle sound for an app
  const toggleAppSound = (appId: number) => {
    // If muted, don't allow toggling
    if (muteAll) {
      return;
    }

    const currentSettings = notificationSettings.appSettings[appId] || {
      enabled: true,
      sound: true,
      priority: 'normal' as const,
      grouping: {
        enabled: true,
        collapseThreshold: notificationSettings.collapseThreshold
      }
    };

    updateAppNotificationSettings(appId, {
      ...currentSettings,
      sound: !currentSettings.sound
    });
  };

  // Toggle mute all sounds
  const toggleMuteAll = () => {
    const newMuteState = !muteAll;
    setMuteAll(newMuteState);

    // If unmuting, set to default volume (80), otherwise set to 0
    updateNotificationSettings({
      notificationSoundVolume: newMuteState ? 0 : 80
    });

    // If muting, stop any playing ringtone
    if (newMuteState && isPlaying) {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(null);
    }
  };

  // Select a new ringtone
  const selectRingtone = (ringtoneId: string) => {
    setCurrentRingtone(ringtoneId);

    // Get the ringtone path
    const ringtonePath = ringtones.find(r => r.id === ringtoneId)?.path || '/sounds/notification-call.mp3';

    // Update both the ringtone setting and the default sound
    updateNotificationSettings({
      ringtone: ringtonePath
    });

    // Play a preview of the selected ringtone
    playRingtonePreview(ringtoneId);
  };

  // Render app sound toggle item
  const renderAppSoundToggle = (app: AppType, index: number) => {
    const appSettings = notificationSettings.appSettings[app.id] || {
      enabled: true,
      sound: true
    };

    return (
      <motion.div
        key={app.id}
        className={`w-full p-2.5 border-b border-white/10 flex items-center justify-between ${muteAll ? 'text-gray-500 opacity-70' : 'text-white'}`}
        variants={staggeredAnimation().child}
        custom={index}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{ delay: index * 0.05 }}
      >
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-lg ${muteAll ? 'bg-gray-700/30' : app.colors.bg} flex items-center justify-center`}>
            <i className={`fas fa-${app.icon} ${muteAll ? 'text-gray-500' : 'text-white'} text-sm`}></i>
          </div>
          <span className="text-sm">{app.name}</span>
        </div>
        <label className={`relative inline-flex items-center ${muteAll ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
          <input
            type="checkbox"
            className="sr-only peer"
            checked={muteAll ? false : appSettings.sound}
            onChange={() => toggleAppSound(app.id)}
            disabled={muteAll}
          />
          <div className={`w-10 h-5 ${
            muteAll
              ? 'bg-gray-700/30'
              : 'bg-gray-700 peer-focus:ring-blue-800 peer-focus:outline-none peer-focus:ring-2 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600'
          } rounded-full`}></div>
        </label>
      </motion.div>
    );
  };

  // Main container with pt-8 for topbar padding and scrolling
  return (
    <motion.div
      className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8"
      {...slideTransition('right')}
    >
      {/* Header */}
      <motion.div
        className="flex items-center gap-2 px-3 py-2 bg-[#0a0f1a] border-b border-white/10 sticky top-0 z-10"
        {...fadeTransition(0.2)}
      >
        <motion.button
          onClick={onBack}
          className="flex items-center text-white/80 hover:text-white"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <i className="fas fa-arrow-left text-lg"></i>
        </motion.button>
        <span className="text-white">Sound Settings</span>
      </motion.div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Global Settings */}
        <motion.div
          className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03]"
          {...fadeTransition(0.3)}
        >
          GLOBAL SETTINGS
        </motion.div>

        {/* Mute All Toggle */}
        <motion.div
          className="w-full p-2.5 border-b border-white/10 flex items-center justify-between text-white"
          variants={staggeredAnimation().child}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-red-500/20 flex items-center justify-center">
              <i className="fas fa-volume-mute text-white text-sm"></i>
            </div>
            <div>
              <span className="text-sm">Mute All Sounds</span>
              <p className="text-xs text-white/50">Silence all phone sounds</p>
            </div>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={muteAll}
              onChange={toggleMuteAll}
            />
            <div className="w-10 h-5 bg-gray-700 peer-focus:ring-blue-800 peer-focus:outline-none peer-focus:ring-2 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </motion.div>

        {/* Ringtone Section */}
        <motion.div
          className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03] mt-2"
          {...fadeTransition(0.3)}
        >
          RINGTONES
        </motion.div>

        {/* Compact Ringtone Grid - 5 columns (one row) */}
        <div className="flex justify-between px-2 py-3">
          {ringtones.map((ringtone, index) => (
            <motion.div
              key={ringtone.id}
              className={`w-[18%] aspect-square rounded-lg flex flex-col items-center justify-center ${
                currentRingtone === ringtone.id
                  ? 'bg-blue-500/20 border border-blue-500/40'
                  : muteAll
                    ? 'bg-gray-700/20 border border-gray-700/20 opacity-50'
                    : 'bg-white/[0.06] border border-white/[0.08] hover:bg-white/[0.08]'
              }`}
              variants={staggeredAnimation().child}
              custom={index}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{ delay: index * 0.05 }}
              onClick={() => !muteAll && selectRingtone(ringtone.id)}
            >
              <div className="flex flex-col items-center justify-between h-full w-full p-1.5">
                <div className="flex items-center justify-center w-full">
                  <span className={`text-xs font-medium text-center truncate ${
                    muteAll
                      ? 'text-gray-500'
                      : currentRingtone === ringtone.id
                        ? 'text-blue-400'
                        : 'text-white'
                  }`}>
                    {ringtone.name}
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!muteAll) playRingtonePreview(ringtone.id);
                    }}
                    className={`w-7 h-7 rounded-full ${
                      muteAll
                        ? 'bg-gray-700/30 cursor-not-allowed'
                        : 'bg-white/[0.1] hover:bg-white/[0.15]'
                    } flex items-center justify-center mb-1`}
                    disabled={muteAll}
                  >
                    <i className={`fas fa-${isPlaying === ringtone.id ? 'stop' : 'play'} ${
                      muteAll ? 'text-gray-500' : 'text-white/80'
                    } text-xs`}></i>
                  </button>

                  {currentRingtone === ringtone.id && (
                    <span className={`text-[0.6rem] ${muteAll ? 'text-gray-500' : 'text-blue-400'} flex items-center`}>
                      <i className="fas fa-check mr-0.5 text-[0.6rem]"></i> Current
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        {/* App Sound Settings */}
        <motion.div
          className="mt-2"
          variants={staggeredAnimation().parent}
          initial="initial"
          animate="animate"
        >
          {/* System Apps Section */}
          <motion.div
            className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03] mt-2"
            {...fadeTransition(0.3)}
          >
            SYSTEM APPS
          </motion.div>
          {systemApps.map((app: AppType, index: number) => renderAppSoundToggle(app, index))}

          {/* Store Apps Section */}
          {storeApps.length > 0 && (
            <>
              <motion.div
                className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03] mt-2"
                {...fadeTransition(0.4)}
              >
                INSTALLED APPS
              </motion.div>
              {storeApps.map((app: AppType, index: number) => renderAppSoundToggle(app, index + systemApps.length))}
            </>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SoundSettingsScreen;
