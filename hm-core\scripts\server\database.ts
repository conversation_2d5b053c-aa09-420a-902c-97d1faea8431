// /// <reference types="@citizenfx/server" />

import { CharacterData, PlayerData, Vector4 } from './types/types';

export class Database {
    private static instance: Database;

    private constructor() {
        // Private constructor to enforce singleton pattern
        console.log('Database instance created');
    }

    public static getInstance(): Database {
        if (!Database.instance) {
            Database.instance = new Database();
        }
        return Database.instance;
    }

    async getPlayerByLicense(license: string): Promise<PlayerData | null> {
        try {
            const results = await global.exports.oxmysql.query_async(
                'SELECT * FROM players WHERE license = ?',
                [license]
            );
            if (!results || results.length === 0) return null;

            const result = results[0];
            console.log(`Fetched player by license: ${license}`, result);

            return {
                id: result.id,
                license: result.license,
                name: result.name,
                discord: result.discord,
                steam: result.steam,
                created_at: new Date(result.created_at),
                last_seen: new Date(result.last_seen),
                play_time: result.play_time,
                is_online: result.is_online,
            };
        } catch (error) {
            console.error('Error fetching player by license:', error);
            return null;
        }
    }

    async createPlayer(playerData: Partial<PlayerData>): Promise<PlayerData | null> {
        try {
            const result = await global.exports.oxmysql.insert_async(
                `INSERT INTO players (license, name, discord, steam)
             VALUES (?, ?, ?, ?)
             ON DUPLICATE KEY UPDATE name = ?, discord = ?, steam = ?`,
                [
                    playerData.license,
                    playerData.name,
                    playerData.discord || null,
                    playerData.steam || null,
                    playerData.name,
                    playerData.discord || null,
                    playerData.steam || null,
                ]
            );
            if (result === false || result === -1) {
                console.log(`Failed to insert/update player: ${result}`);
                return null;
            }

            return await this.getPlayerByLicense(playerData.license!);
        } catch (error) {
            console.error('Error creating player:', error);
            return null;
        }
    }

    async updatePlayer(license: string, isOnline: boolean): Promise<void> {
        try {
            await global.exports.oxmysql.execute(
                'UPDATE players SET is_online = ?, last_seen = NOW() WHERE license = ?',
                [isOnline, license]
            );
        } catch (error) {
            console.error('Error updating player:', error);
        }
    }

    async getCharactersByPlayerId(playerId: number): Promise<CharacterData[]> {
        try {
            const results = await exports.oxmysql.query(
                'SELECT * FROM characters WHERE player_id = ? AND is_active = TRUE ORDER BY last_played DESC',
                [playerId]
            );

            return results.map((row: any) => ({
                id: row.id,
                playerId: row.player_id,
                firstName: row.first_name,
                lastName: row.last_name,
                dateOfBirth: row.date_of_birth,
                gender: row.gender,
                position: {
                    x: row.position_x,
                    y: row.position_y,
                    z: row.position_z,
                },
                heading: row.heading,
                money: {
                    cash: row.cash,
                    bank: row.bank,
                },
                job: row.job,
                grade: row.grade,
                created_at: new Date(row.created_at),
                updated_at: new Date(row.updated_at),
                isActive: row.is_active,
            }));
        } catch (error) {
            console.error('Error fetching characters by player ID:', error);
            return [];
        }
    }

    async createCharacter(characterData: Partial<CharacterData>): Promise<CharacterData | null> {
        try {
            const result = await global.exports.oxmysql.insert(
                `INSERT INTO characters (stateid, identifier, first_name, last_name, birthdate, gender, state, money, position, appearance, tattoos, metadata, created_at, updated_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                 ON DUPLICATE KEY UPDATE first_name = ?, last_name = ?, birthdate = ?, gender = ?, state = ?, money = ?, position = ?`,
                [
                    characterData.stateid,
                    characterData.identifier,
                    characterData.first_name,
                    characterData.last_name,
                    characterData.birthdate,
                    characterData.gender,
                    characterData.state || 'alive',
                    characterData.money,
                    characterData.position,
                    characterData.appearance || [],
                    characterData.tattoos || [],
                    characterData.metadata || [],
                    characterData.created_at || new Date(),
                    characterData.updated_at || new Date(),
                    characterData.first_name,
                    characterData.last_name,
                    characterData.birthdate,
                    characterData.gender,
                    characterData.state || 'alive',
                    characterData.money,
                    characterData.position || { x: 0, y: 0, z: 0, heading: 0 },
                ]
            );

            if (result) {
                const characters = await this.getCharactersByPlayerId(result.insertId);
                return characters.find(c => c.id === result) || null;
            }
            return null;
        } catch (error) {
            console.error('[server database]Error creating character:', error);
            return null;
        }
    }

    async updateCharacterPosition(identifier: string, stateid: string, position: Vector4): Promise<void> {
        try {
            // Ensure position is a valid Vector4 but as json like the database expects
            const positionJson = JSON.stringify({
                x: position.x,
                y: position.y,
                z: position.z,
                heading: position.heading || 0,
            });
            await global.exports.oxmysql.execute(
                'UPDATE characters SET position = ? WHERE license = ? AND stateid = ?',
                [positionJson, identifier, stateid]
            );
        } catch (error) {
            console.error('Error updating character position:', error);
        }
    }
}

// make global exports available for other resources
if (IsDuplicityVersion()) {
    global.exports('getDatabase', () => Database.getInstance());
}
