// Default configuration for the tablet
export const DEFAULT_TABLET_CONFIG = {
  brightness: 80,
  volume: 50,
  theme: 'dark' as const,  autoLock: true,
  lockTimeout: 5, // minutes
  installedApps: [],
  homeScreenApps: ['racing', 'settings'],
  recentApps: []
};

// Built-in apps configuration
export const BUILTIN_APPS = [  {
    id: 'racing',
    name: 'Racing',
    icon: 'flag-checkered',
    description: 'Street racing underground network',
    category: 'entertainment' as const,
    isInstalled: true,
    isVisible: true,
    version: '1.0.0',
    size: 15.2
  },
  {
    id: 'settings',
    name: 'Settings',
    icon: 'gear',
    description: 'Tablet settings and configuration',
    category: 'system' as const,
    isInstalled: true,
    isVisible: true,
    version: '1.0.0',
    size: 5.1
  }
];

// UI Constants
export const UI_CONSTANTS = {
  APP_GRID_COLS: 4,
  APP_GRID_ROWS: 5,
  MAX_HOME_SCREEN_APPS: 16,
  MAX_RECENT_APPS: 10,
  NOTIFICATION_TIMEOUT: 5000, // 5 seconds
  SCREEN_TIMEOUT: 300000, // 5 minutes
};

// Animation durations (in milliseconds)
export const ANIMATIONS = {
  APP_OPEN: 300,
  APP_CLOSE: 250,
  PAGE_TRANSITION: 200,
  NOTIFICATION_SLIDE: 400,
  SCREEN_FADE: 150
};
