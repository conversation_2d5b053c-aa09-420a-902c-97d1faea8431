/**
 * Messages app message handlers
 */
import { register<PERSON>ventHandler } from '../../../fivem/clientEventReceiver';
import { useMessagesStore } from '../stores/messagesStore';
import { useNavigationStore } from '../../../navigation/navigationStore';
import { Conversation, Message } from '../../../../../shared/types';

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    _pendingInitialMessage?: Message;
  }
}

// Define interfaces for the message data types
interface MessageData {
  id?: number;
  conversationId?: number;
  conversation_id?: number;
  type?: string;
  metadata?: string | Record<string, unknown>;
  message?: string;
}

interface ErrorResponse {
  message?: string;
}

interface ConversationCreatedResponse {
  success?: boolean;
  data?: Conversation;
  id?: number;
}

// Register handler for conversations data
registerEventHandler('messages', 'conversations', data => {
  console.log('[Messages] Received conversations data:', data);
  console.log('[Messages] Data type:', typeof data);
  console.log('[Messages] Is array:', Array.isArray(data));

  // Add detailed logging of the data structure
  if (data) {
    console.log('[Messages] UI handler - Data structure details:');
    console.log('[Messages] UI handler - Keys:', Object.keys(data));

    try {
      console.log('[Messages] UI handler - JSON representation:', JSON.stringify(data, null, 2));
    } catch (e) {
      console.log('[Messages] UI handler - Could not stringify data:', e);
    }

    if (Array.isArray(data)) {
      console.log('[Messages] UI handler - Array length:', data.length);
      if (data.length > 0) {
        console.log('[Messages] UI handler - First item keys:', Object.keys(data[0]));
      }
    } else if (typeof data === 'object') {
      console.log('[Messages] UI handler - Object properties:');
      for (const key in data) {
        console.log(`[Messages] UI handler - Property ${key}:`, typeof (data as Record<string, unknown>)[key], (data as Record<string, unknown>)[key]);
      }
    }
  }

  if (data === null || data === undefined) {
    console.error('[Messages] Received null or undefined conversations data');
    useMessagesStore.getState().handlers.onSetConversations([]);
    return;
  }

  // Validate data
  if (Array.isArray(data)) {
    console.log('[Messages] Processing array of conversations, length:', data.length);

    // Ensure each conversation has valid array properties
    const validatedConversations = data.map((conversation: Partial<Conversation>) => {
      console.log('[Messages] Processing conversation:', conversation);

      // Ensure members is an object
      if (!conversation.members || typeof conversation.members !== 'object') {
        console.log(`[Messages] Fixing members for conversation ${conversation.id}`);
        conversation.members = {};
      }

      // Ensure messages is an array
      if (!conversation.messages || !Array.isArray(conversation.messages)) {
        console.log(`[Messages] Fixing messages for conversation ${conversation.id}`);
        conversation.messages = [];
      }

      return conversation;
    });

    // Update the store with the validated conversations data
    console.log('[Messages] Setting validated conversations:', validatedConversations);
    useMessagesStore.getState().handlers.onSetConversations(validatedConversations as Conversation[]);
  } else if (typeof data === 'object') {
    // Handle case where a single conversation object is received instead of an array
    console.log('[Messages] Received single conversation object instead of array');

    const conversation = data as Partial<Conversation>;

    // Ensure members is an object
    if (!conversation.members || typeof conversation.members !== 'object') {
      console.log(`[Messages] Fixing members for single conversation ${conversation.id}`);
      conversation.members = {};
    }

    // Ensure messages is an array
    if (!conversation.messages || !Array.isArray(conversation.messages)) {
      console.log(`[Messages] Fixing messages for single conversation ${conversation.id}`);
      conversation.messages = [];
    }

    // Update the store with an array containing the single conversation
    console.log('[Messages] Setting single conversation as array:', [conversation]);
    useMessagesStore.getState().handlers.onSetConversations([conversation] as Conversation[]);
  } else {
    console.error('[Messages] Received invalid conversations data (not an array or object):', data);
    // Send an empty array to prevent errors in the UI
    useMessagesStore.getState().handlers.onSetConversations([]);
  }
});

// Register handler for conversation updates
registerEventHandler('messages', 'conversationUpdated', (data: unknown) => {
  console.log('[Messages] Received conversation update:', data);

  // Validate data
  const conversationData = data as Partial<Conversation>;
  if (data && typeof data === 'object' && conversationData.id) {
    // Update the store with the updated conversation
    useMessagesStore.getState().handlers.onUpdateConversation(data as Conversation);
  } else {
    console.error('[Messages] Received invalid conversation update data:', data);
  }
});

// Register handler for message sent confirmation
registerEventHandler('messages', 'messageSent', (data: unknown) => {
  console.log('[Messages] Message sent confirmation:', data);

  // Check if data is an array (server might be returning conversations instead of a message)
  if (Array.isArray(data)) {
    console.log('[Messages] Received array instead of message object, handling as conversations');
    // Handle as conversations data
    useMessagesStore.getState().handlers.onSetConversations(data as Conversation[]);
    return;
  }

  // Validate data
  const messageData = data as Partial<MessageData>;
  if (data && typeof data === 'object') {
    // Log the conversation ID for debugging
    console.log('[Messages] Message sent with conversationId:', messageData.conversationId);

    // Ensure we have a valid conversationId
    if (!messageData.conversationId) {
      console.error('[Messages] Missing conversationId in message response:', messageData);

      // Try to extract it from other properties
      if (messageData.conversation_id) {
        messageData.conversationId = messageData.conversation_id;
        console.log('[Messages] Using conversation_id instead:', messageData.conversationId);
      } else if (messageData.id && typeof messageData.id === 'number') {
        // Last resort - use the message ID (not ideal but better than nothing)
        messageData.conversationId = messageData.id;
        console.log('[Messages] Using message id as fallback:', messageData.conversationId);
      }
    }

    // Update the store with the sent message confirmation
    // Cast to unknown and then to Message to bypass type checking since we know the server returns a valid message
    useMessagesStore.getState().handlers.onMessageSent(messageData as unknown as Message);

    // Also update the UI to show the message was sent successfully
    // This is especially important for image messages
    if (messageData.type === 'image' && messageData.metadata) {
      try {
        // If metadata is a string, parse it
        const metadata =
          typeof messageData.metadata === 'string'
            ? JSON.parse(messageData.metadata)
            : messageData.metadata;

        console.log('[Messages] Image message metadata:', metadata);
      } catch (error) {
        console.error('[Messages] Error parsing image message metadata:', error);
      }
    }
  } else {
    console.error('[Messages] Received invalid message sent data:', data);
  }
});

// Register handler for message error
registerEventHandler('messages', 'messageError', data => {
  console.error('[Messages] Message error:', data);
  // Could implement error handling here
});

// Register handler for general errors
registerEventHandler('messages', 'error', (data: unknown) => {
  console.error('[Messages] Error:', data);

  // Show error notification
  const errorData = data as Partial<ErrorResponse>;
  if (data && typeof data === 'object' && errorData.message) {
    // TODO: Show error notification
    console.error('[Messages] Error message:', errorData.message);
  }
});

// Register handler for messages data
registerEventHandler('messages', 'messages', (data: unknown) => {
  console.log('[Messages] Received messages data:', data);

  // Validate data
  if (data && typeof data === 'object') {
    // Check if data has the expected structure
    const messagesData = data as { conversationId?: number | string, messages: Message[] };

    if (messagesData.messages && Array.isArray(messagesData.messages)) {
      // We have a valid structure with conversationId and messages array
      if (messagesData.conversationId) {
        console.log(`[Messages] Processing ${messagesData.messages.length} messages for conversation ${messagesData.conversationId}`);
        useMessagesStore.getState().handlers.onSetMessages(messagesData.conversationId, messagesData.messages);
      } else if (messagesData.messages.length > 0 && messagesData.messages[0].conversation_id) {
        // Extract conversation_id from the first message
        const conversationId = messagesData.messages[0].conversation_id;
        console.log(`[Messages] Extracted conversation ID ${conversationId} from first message`);
        useMessagesStore.getState().handlers.onSetMessages(conversationId, messagesData.messages);
      } else {
        console.error('[Messages] Cannot determine conversation ID for messages:', messagesData);
      }
    } else if (Array.isArray(data)) {
      // If we just received an array of messages without a conversationId wrapper
      if (data.length > 0 && 'conversation_id' in data[0]) {
        const conversationId = data[0].conversation_id;
        console.log(`[Messages] Processing ${data.length} messages for conversation ${conversationId} (array format)`);
        useMessagesStore.getState().handlers.onSetMessages(conversationId, data as Message[]);
      } else {
        console.error('[Messages] Received messages array without conversation_id:', data);
      }
    } else {
      console.error('[Messages] Received invalid messages data structure:', data);
    }
  } else {
    console.error('[Messages] Received invalid messages data (not an object):', data);
  }
});

// Register handler for conversation created
registerEventHandler('messages', 'conversationCreated', (data: unknown) => {
  console.log('[Messages] Conversation created:', data);

  // Validate data
  if (data && typeof data === 'object') {
    const responseData = data as Partial<ConversationCreatedResponse>;
    // Get the conversation data - handle both formats (with or without success/data wrapper)
    const conversation =
      responseData.success && responseData.data ? responseData.data : (data as Conversation);
    const conversationId = conversation.id;

    if (!conversationId) {
      console.error('[Messages] Missing conversation ID in response:', data);
      return;
    }

    console.log('[Messages] Setting active chat to new conversation:', conversationId);

    // Import navigation functions
    const { openView } = useNavigationStore.getState();

    // Add the conversation to the store if it's not already there
    const { conversations } = useMessagesStore.getState();

    // Convert ID to number to ensure consistent format
    const numericId = Number(conversationId);

    // Look for the conversation with the numeric ID
    const existingConversation = conversations.find(c => c.id === numericId);

    if (!existingConversation) {
      console.log('[Messages] Adding new conversation to store:', conversation);
      useMessagesStore.getState().handlers.onUpdateConversation(conversation as Conversation);
    } else {
      console.log('[Messages] Found existing conversation with ID:', existingConversation.id);
    }

    // Set the active chat immediately
    useMessagesStore.getState().ui.setActiveChat(numericId);

    // Navigate to the conversation view
    openView('conversation', { chatId: numericId });

    console.log('[Messages] Navigation complete to conversation:', numericId);
  } else {
    console.error('[Messages] Invalid conversation created data:', data);
  }
});
