/**
 * Game Service
 *
 * Provides general game-related utilities for the phone system.
 * This service is responsible for:
 * - Getting game time
 * - Loading models and animations
 * - Handling resource cleanup
 * - Providing utility functions for other services
 */

// Import globals to ensure they're initialized
import '../globals';

// Animation loading state
let animLoadTickId: number | null = null;
let modelLoadTickId: number | null = null;

/**
 * Get the current game time in HH:MM format
 * @returns Current game time as string
 */
export function getCurrentGameTime(): string {
    let hour: string | number = GetClockHours();
    let minute: string | number = GetClockMinutes();

    // Format time if needed
    if (hour < 10) hour = `0${hour}`;
    if (minute < 10) minute = `0${minute}`;

    return `${hour}:${minute}`;
}

/**
 * Check if an entity is dead
 * @param entity Entity to check
 * @returns True if entity is dead
 */
export function isEntityDead(entity: number): boolean {
    return IsEntityDead(entity);
}

/**
 * Get player coordinates
 * @returns Player coordinates
 */
export function getPlayerCoords(): number[] {
    const playerPed = PlayerPedId();
    return GetEntityCoords(playerPed, false);
}

/**
 * Play a sound
 * @param soundName Name of the sound to play
 * @param soundSet Sound set the sound belongs to
 */
export function playSound(soundName: string, soundSet = 'HUD_FRONTEND_DEFAULT_SOUNDSET'): void {
    PlaySoundFrontend(-1, soundName, soundSet, false);
}

/**
 * Load animation dictionary with a tick handler
 * @param animDict Animation dictionary to load
 * @returns Promise that resolves when animation is loaded or rejects on timeout
 */
export async function loadAnimDict(animDict: string): Promise<void> {
    return new Promise((resolve, reject) => {
        // If already loaded, resolve immediately
        if (HasAnimDictLoaded(animDict)) {
            resolve();
            return;
        }

        // Request the animation dictionary
        RequestAnimDict(animDict);

        // Set timeout
        const startTime = GetGameTimer();
        const timeout = 5000; // 5 seconds timeout

        // Create tick handler
        animLoadTickId = setTick(() => {
            // Check if loaded
            if (HasAnimDictLoaded(animDict)) {
                if (animLoadTickId !== null) {
                    clearTick(animLoadTickId);
                    animLoadTickId = null;
                }
                resolve();
                return;
            }

            // Check timeout
            if (GetGameTimer() - startTime > timeout) {
                if (animLoadTickId !== null) {
                    clearTick(animLoadTickId);
                    animLoadTickId = null;
                }
                reject(new Error(`Failed to load animation dictionary ${animDict} after ${timeout}ms`));
                return;
            }
        });
    });
}

/**
 * Load model with a tick handler
 * @param modelHash Model hash to load
 * @returns Promise that resolves when model is loaded or rejects on timeout
 */
export async function loadModel(modelHash: number): Promise<void> {
    return new Promise((resolve, reject) => {
        // If already loaded, resolve immediately
        if (HasModelLoaded(modelHash)) {
            resolve();
            return;
        }

        // Request the model
        RequestModel(modelHash);

        // Set timeout
        const startTime = GetGameTimer();
        const timeout = 5000; // 5 seconds timeout

        // Create tick handler
        modelLoadTickId = setTick(() => {
            // Check if loaded
            if (HasModelLoaded(modelHash)) {
                if (modelLoadTickId !== null) {
                    clearTick(modelLoadTickId);
                    modelLoadTickId = null;
                }
                resolve();
                return;
            }

            // Check timeout
            if (GetGameTimer() - startTime > timeout) {
                if (modelLoadTickId !== null) {
                    clearTick(modelLoadTickId);
                    modelLoadTickId = null;
                }
                reject(new Error(`Failed to load model ${modelHash} after ${timeout}ms`));
                return;
            }
        });
    });
}

/**
 * Clean up resources when resource stops
 */
export function cleanupResources(): void {
    // Clean up animation loading resources
    if (animLoadTickId !== null) {
        clearTick(animLoadTickId);
        animLoadTickId = null;
    }

    if (modelLoadTickId !== null) {
        clearTick(modelLoadTickId);
        modelLoadTickId = null;
    }
}

// Export game service
export const gameService = {
    // Time functions
    getCurrentGameTime,

    // Entity functions
    isEntityDead,
    getPlayerCoords,

    // Sound functions
    playSound,

    // Resource management
    loadAnimDict,
    loadModel,
    cleanupResources
};

export default gameService;
