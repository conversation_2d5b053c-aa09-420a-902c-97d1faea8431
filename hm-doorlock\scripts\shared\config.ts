/**
 * HM Door System - Configuration
 */

import {
  DoorDefinition,
  DoorState,
  DoorLockType,
  DoorType,
} from "./types/door.types";

// ==================== DOOR DEFINITIONS ====================

/**
 * List of all doors to be registered on server start
 */
export const DOOR_DEFINITIONS: DoorDefinition[] = [
  // Single door example
  {
    type: DoorType.SINGLE,
    id: "mrpd_front_door",
    hash: "HM_DOOR_MRPD_FRONT",
    name: "MRPD Front Door",
    defaultState: DoorState.LOCKED,
    lockType: DoorLockType.MANUAL,
    accessControl: {
      allowedJobs: ["police"],
      minimumJobGrade: 0,
      emergencyOverride: true,
    },
    door: {
      coords: { x: 434.7, y: -981.9, z: 30.7 },
      model: "v_ilev_ph_door01",
      heading: 90.0,
    },
  },

  // Double door example
  {
    type: DoorType.DOUBLE,
    id: "bank_vault_double_door",
    hash: "HM_DOOR_BANK_VAULT",
    name: "Bank Vault Double Door",
    defaultState: DoorState.LOCKED,
    lockType: DoorLockType.KEYCARD,
    accessControl: {
      allowedJobs: ["police", "admin"],
      minimumJobGrade: 3,
      requiredItems: ["keycard_vault"],
      emergencyOverride: false,
    },
    doors: [
      {
        coords: { x: 255.2, y: 220.1, z: 106.3 },
        model: "hei_prop_heist_vault_door",
        heading: 0.0,
      },
      {
        coords: { x: 256.5, y: 220.1, z: 106.3 },
        model: "hei_prop_heist_vault_door",
        heading: 180.0,
      },
    ],
  },

  // Gate example (multiple parts)
  {
    type: DoorType.GATE,
    id: "prison_main_gate",
    hash: "HM_GATE_PRISON_MAIN",
    name: "Prison Main Gate",
    defaultState: DoorState.LOCKED,
    lockType: DoorLockType.BIOMETRIC,
    accessControl: {
      allowedJobs: ["police", "corrections"],
      minimumJobGrade: 1,
      emergencyOverride: true,
    },
    parts: [
      {
        coords: { x: 1844.998, y: 2604.81, z: 44.638 },
        model: "prop_gate_prison_01",
      },
      {
        coords: { x: 1818.542, y: 2604.812, z: 44.611 },
        model: "prop_gate_prison_01",
      },
    ],
  },
];

// ==================== SYSTEM CONFIG ====================

/**
 * Global door system settings
 */
export const DOOR_SYSTEM_CONFIG = {
  enabled: true,
  defaultMaxDistance: 2.0,
  logInteractions: false,
  updateFrequency: 500,

  /** Debug settings */
  debug: {
    enabled: true,
    showDoorMarkers: false,
    logLevel: "info" as "none" | "error" | "warn" | "info" | "debug",
  },
};
