/**
 * Framework Integration Types
 *
 * This file defines the interfaces for the framework integration system.
 * It provides a common interface that all framework adapters must implement.
 */

/**
 * Player data interface
 * Contains common player data across all frameworks
 */
export interface FrameworkPlayer {
    // Basic identification
    source: number;
    identifier: string;
    stateid: string; // Character identifier for multi-character support

    // Character info
    firstName?: string;
    lastName?: string;
    name?: string;
    phoneNumber?: string;

    // Job info
    job?: {
        name: string;
        label: string;
        grade: number;
        gradeName?: string;
        payment?: number;
        onDuty?: boolean;
        isBoss?: boolean;
    };

    // Gang info
    gang?: {
        name: string;
        label: string;
        grade: number;
        isBoss?: boolean;
    };

    // Money info
    money?: {
        cash: number;
        bank: number;
        crypto?: number;
    };

    // Metadata/status
    metadata?: Record<string, any>;
}

/**
 * Item interface
 * Represents an inventory item
 */
export interface FrameworkItem {
    name: string;
    label: string;
    weight?: number;
    count?: number;
    amount?: number;
    info?: Record<string, any>;
    type?: string;
    unique?: boolean;
    useable?: boolean;
    image?: string;
    slot?: number;
}

/**
 * Framework interface
 * Defines the common methods that all framework adapters must implement
 */
export interface IFramework {
    // Framework info
    name: string;
    isLoaded: boolean;

    // Player methods
    getPlayer(source: number): FrameworkPlayer | null;
    getPlayerByIdentifier(identifier: string): FrameworkPlayer | null;
    getPlayerByPhoneNumber(phoneNumber: string): FrameworkPlayer | null;
    getPlayerIdentifier(source: number): string | null;
    getPlayerPhoneNumber(source: number): string | null;

    // Permission methods
    hasPermission(source: number, permission: string): boolean;

    // Money methods
    addMoney(source: number, amount: number, type: string): boolean;
    removeMoney(source: number, amount: number, type: string): boolean;
    getMoney(source: number, type: string): number;

    // Inventory methods
    getInventoryItem(source: number, item: string): FrameworkItem | null;
    addInventoryItem(source: number, item: string, amount: number, metadata?: any): boolean;
    removeInventoryItem(source: number, item: string, amount: number, metadata?: any): boolean;

    // Notification methods
    notify(source: number, message: string, type?: string, duration?: number): void;

    // Utility methods
    getCore(): any; // Returns the raw framework core object
}

/**
 * Framework detection result
 */
export interface FrameworkDetectionResult {
    name: string;
    isLoaded: boolean;
    framework: IFramework | null;
}

/**
 * Framework configuration
 */
export interface FrameworkConfig {
    // If set, forces a specific framework instead of auto-detection
    forceFramework?: 'hm-core';

    // Debug mode
    debug?: boolean;
}

/**
 * Framework enum
 */
export enum Framework {
    HMCORE = 'hm-core'
}
