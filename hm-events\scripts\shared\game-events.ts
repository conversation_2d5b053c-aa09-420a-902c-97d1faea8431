/**
 * Comprehensive catalog of game events
 * Organized by categories for easy reference
 */

// Event categories
export enum EventCategory {
  COMBAT = 'combat',
  VEHICLE = 'vehicle',
  PLAYER = 'player',
  ENVIRONMENT = 'environment',
  NETWORK = 'network',
  ACQUAINTANCE = 'acquaintance',
  RESPONSE = 'response',
  SHOCKING = 'shocking',
  SYSTEM = 'system',
  OTHER = 'other',
}

// Event information
export interface GameEventInfo {
  category: EventCategory;
  description: string;
}

// Define events by category for better organization
const eventsByCategory: Record<EventCategory, Record<string, string>> = {
  // === ACQUAINTANCE EVENTS ===
  [EventCategory.ACQUAINTANCE]: {
    CEventAcquaintancePed: 'Acquaintance ped event',
    CEventAcquaintancePedDead: 'Acquaintance ped dead',
    CEventAcquaintancePedDislike: 'Acquaintance ped dislike',
    CEventAcquaintancePedHate: 'Acquaintance ped hate',
    CEventAcquaintancePedLike: 'Acquaintance ped like',
    CEventAcquaintancePedWanted: 'Acquaintance ped wanted',
  },

  // === COMBAT EVENTS ===
  [EventCategory.COMBAT]: {
    CEventDamage: 'Damage event',
    CEventEntityDamage: 'Entity damaged',
    CEventEntityDestroyed: 'Entity destroyed',
    CEventGunShot: 'Gun shot fired',
    CEventGunShotBulletImpact: 'Bullet impact',
    CEventGunShotWhizzedBy: 'Bullet whizzed by',
    CEventGunAimedAt: 'Gun aimed at entity',
    CEventExplosion: 'Explosion occurred',
    CEventMeleeAction: 'Melee action performed',
    CEventFriendlyFireNearMiss: 'Friendly fire near miss',
    CEventFriendlyAimedAt: 'Friendly aimed at',
    CEventOnFire: 'Entity on fire',
    CEventWrithe: 'Entity writhing (injured)',
  },

  // === VEHICLE EVENTS ===
  [EventCategory.VEHICLE]: {
    CEventCarUndriveable: 'Vehicle became undriveable',
    CEventVehicleCollision: 'Vehicle collision',
    CEventVehicleDamage: 'Vehicle damaged',
    CEventVehicleDamageWeapon: 'Vehicle damaged by weapon',
    CEventVehicleOnFire: 'Vehicle on fire',
    CEventPedJackingMyVehicle: 'Ped jacking player vehicle',
    CEventPedEnteredMyVehicle: 'Ped entered player vehicle',
    CEventPlayerUnableToEnterVehicle: 'Player unable to enter vehicle',
    CEventCopCarBeingStolen: 'Police car being stolen',
    CEventDraggedOutCar: 'Entity dragged out of car',
    CEventMustLeaveBoat: 'Entity must leave boat',
    CEventPedOnCarRoof: 'Ped on car roof',
  },

  // === PLAYER EVENTS ===
  [EventCategory.PLAYER]: {
    CEventPlayerDeath: 'Player died',
    CEventDeath: 'Entity death',
    CEventDeadPedFound: 'Dead ped found',
    CEventPedSeenDeadPed: 'Ped saw dead ped',
    CEventPlayerCollisionWithPed: 'Player collided with ped',
    CEventPedCollisionWithPlayer: 'Ped collided with player',
    CEventPedCollisionWithPed: 'Ped collided with ped',
    CEventInAir: 'Entity in air',
    CEventStuckInAir: 'Entity stuck in air',
    CEventGetOutOfWater: 'Entity getting out of water',
    CEventHurtTransition: 'Entity hurt transition',
    CEventRanOverPed: 'Ran over ped',
    CEventShovePed: 'Shoved ped',
  },

  // === ENVIRONMENT EVENTS ===
  [EventCategory.ENVIRONMENT]: {
    CEventFireNearby: 'Fire detected nearby',
    CEventExplosionHeard: 'Explosion heard',
    CEventFootStepHeard: 'Footstep heard',
    CEventClimbLadderOnRoute: 'Climbing ladder on route',
    CEventClimbNavMeshOnRoute: 'Climbing navmesh on route',
    CEventObjectCollision: 'Object collision',
    CEventOpenDoor: 'Door opened',
    CEventPotentialBlast: 'Potential blast',
    CEventPotentialWalkIntoVehicle: 'Potential walk into vehicle',
    CEventPotentialGetRunOver: 'Potential get run over',
    CEventPotentialBeWalkedInto: 'Potential be walked into',
  },

  // === NETWORK EVENTS ===
  [EventCategory.NETWORK]: {
    CEventNetworkPlayerDeactivatedSpecialAbility: 'Player deactivated special ability',
    CEventNetworkEntityDamage: 'Network entity damage',
    CEventNetworkPlayerJoinScript: 'Player joined script',
    CEventNetworkPlayerLeftScript: 'Player left script',
    CEventNetworkPlayerSpawn: 'Player spawned',
    CEventNetworkStartSession: 'Network session started',
    CEventNetworkEndSession: 'Network session ended',
    CEventNetworkVehicleUndrivable: 'Network vehicle undrivable',
    CEventNetworkPlayerCollectedPickup: 'Player collected pickup',
    CEventNetworkPlayerCollectedAmbientPickup: 'Player collected ambient pickup',
    CEventNetworkPickupRespawned: 'Pickup respawned',
    CEventNetworkPlayerCollectedPortablePickup: 'Player collected portable pickup',
    CEventNetworkPlayerDroppedPortablePickup: 'Player dropped portable pickup',
    CEventNetworkInviteAccepted: 'Network invite accepted',
    CEventNetworkInviteConfirmed: 'Network invite confirmed',
    CEventNetworkInviteRejected: 'Network invite rejected',
    CEventNetworkPlayerArrest: 'Player arrested',
    CEventNetworkPlayerEnteredVehicle: 'Player entered vehicle',
    CEventNetworkPlayerPedLeftBehind: 'Player ped left behind',
    CEventNetworkPlayerSpectateLocal: 'Player spectating local',
    CEventNetworkScriptEvent: 'Network script event',
    CEventNetworkVoiceSessionStarted: 'Voice session started',
    CEventNetworkVoiceSessionEnded: 'Voice session ended',
    CEventNetworkVoiceConnectionRequested: 'Voice connection requested',
    CEventNetworkVoiceConnectionResponse: 'Voice connection response',
    CEventNetworkVoiceConnectionTerminated: 'Voice connection terminated',
    CEventNetworkClanInviteReceived: 'Clan invite received',
    CEventNetworkClanJoined: 'Clan joined',
    CEventNetworkClanLeft: 'Clan left',
    CEventNetworkClanKicked: 'Clan kicked',
    CEventNetworkClanRankChanged: 'Clan rank changed',
    CEventNetworkPrimaryClanChanged: 'Primary clan changed',
    CEventNetworkAdminInvited: 'Admin invited',
    CEventNetworkAttemptHostMigration: 'Attempt host migration',
    CEventNetworkBail: 'Network bail',
    CEventNetworkCashTransactionLog: 'Cash transaction log',
    CEventNetworkCheatTriggered: 'Cheat triggered',
    CEventNetworkCloudEvent: 'Cloud event',
    CEventNetworkCloudFileResponse: 'Cloud file response',
    CEventNetworkEmailReceivedEvent: 'Email received',
    CEventNetworkEndMatch: 'End match',
    CEventNetworkFindSession: 'Find session',
    CEventNetworkFollowInviteReceived: 'Follow invite received',
    CEventNetworkHostMigration: 'Host migration',
    CEventNetworkHostSession: 'Host session',
    CEventNetworkIncrementStat: 'Increment stat',
    CEventNetworkJoinSession: 'Join session',
    CEventNetworkJoinSessionResponse: 'Join session response',
    CEventNetworkOnlinePermissionsUpdated: 'Online permissions updated',
    CEventNetworkPresenceInvite: 'Presence invite',
    CEventNetworkPresenceInviteRemoved: 'Presence invite removed',
    CEventNetworkPresenceInviteReply: 'Presence invite reply',
    CEventNetworkPresenceTriggerEvent: 'Presence trigger event',
    CEventNetworkPresence_StatUpdate: 'Presence stat update',
    CEventNetworkRequestDelay: 'Request delay',
    CEventNetworkRosChanged: 'ROS changed',
    CEventNetworkScAdminPlayerUpdated: 'SC admin player updated',
    CEventNetworkScAdminReceivedCash: 'SC admin received cash',
    CEventNetworkSessionEvent: 'Session event',
    CEventNetworkShopTransaction: 'Shop transaction',
    CEventNetworkSignInStateChanged: 'Sign in state changed',
    CEventNetworkSocialClubAccountLinked: 'Social Club account linked',
    CEventNetworkSummon: 'Summon',
    CEventNetworkSystemServiceEvent: 'System service event',
    CEventNetworkTextMessageReceived: 'Text message received',
    CEventNetworkTimedExplosion: 'Timed explosion',
    CEventNetworkTransitionEvent: 'Transition event',
    CEventNetworkTransitionGamerInstruction: 'Transition gamer instruction',
    CEventNetworkTransitionMemberJoined: 'Transition member joined',
    CEventNetworkTransitionMemberLeft: 'Transition member left',
    CEventNetworkTransitionParameterChanged: 'Transition parameter changed',
    CEventNetworkTransitionStarted: 'Transition started',
    CEventNetworkTransitionStringChanged: 'Transition string changed',
    CEventNetworkWithData: 'Network with data',
    CEventNetwork_InboxMsgReceived: 'Inbox message received',
  },

  // === SHOCKING EVENTS ===
  [EventCategory.SHOCKING]: {
    CEventShocking: 'Shocking event',
    CEventShockingBicycleCrash: 'Shocking bicycle crash',
    CEventShockingBicycleOnPavement: 'Shocking bicycle on pavement',
    CEventShockingCarAlarm: 'Shocking car alarm',
    CEventShockingCarChase: 'Shocking car chase',
    CEventShockingCarCrash: 'Shocking car crash',
    CEventShockingCarOnCar: 'Shocking car on car',
    CEventShockingCarPileUp: 'Shocking car pile up',
    CEventShockingDangerousAnimal: 'Shocking dangerous animal',
    CEventShockingDeadBody: 'Shocking dead body',
    CEventShockingDrivingOnPavement: 'Shocking driving on pavement',
    CEventShockingEngineRevved: 'Shocking engine revved',
    CEventShockingExplosion: 'Shocking explosion',
    CEventShockingFire: 'Shocking fire',
    CEventShockingGunFight: 'Shocking gun fight',
    CEventShockingGunshotFired: 'Shocking gunshot fired',
    CEventShockingHelicopterOverhead: 'Shocking helicopter overhead',
    CEventShockingHornSounded: 'Shocking horn sounded',
    CEventShockingInDangerousVehicle: 'Shocking in dangerous vehicle',
    CEventShockingInjuredPed: 'Shocking injured ped',
    CEventShockingMadDriver: 'Shocking mad driver',
    CEventShockingMadDriverBicycle: 'Shocking mad driver bicycle',
    CEventShockingMadDriverExtreme: 'Shocking mad driver extreme',
    CEventShockingMugging: 'Shocking mugging',
    CEventShockingNonViolentWeaponAimedAt: 'Shocking non-violent weapon aimed at',
    CEventShockingParachuterOverhead: 'Shocking parachuter overhead',
    CEventShockingPedKnockedIntoByPlayer: 'Shocking ped knocked into by player',
    CEventShockingPedRunOver: 'Shocking ped run over',
    CEventShockingPedShot: 'Shocking ped shot',
    CEventShockingPlaneFlyby: 'Shocking plane flyby',
    CEventShockingPotentialBlast: 'Shocking potential blast',
    CEventShockingPropertyDamage: 'Shocking property damage',
    CEventShockingRunningPed: 'Shocking running ped',
    CEventShockingRunningStampede: 'Shocking running stampede',
    CEventShockingSeenCarStolen: 'Shocking seen car stolen',
    CEventShockingSeenConfrontation: 'Shocking seen confrontation',
    CEventShockingSeenGangFight: 'Shocking seen gang fight',
    CEventShockingSeenInsult: 'Shocking seen insult',
    CEventShockingSeenMeleeAction: 'Shocking seen melee action',
    CEventShockingSeenNiceCar: 'Shocking seen nice car',
    CEventShockingSeenPedKilled: 'Shocking seen ped killed',
    CEventShockingSiren: 'Shocking siren',
    CEventShockingStudioBomb: 'Shocking studio bomb',
    CEventShockingVehicleTowed: 'Shocking vehicle towed',
    CEventShockingVisibleWeapon: 'Shocking visible weapon',
    CEventShockingWeaponThreat: 'Shocking weapon threat',
    CEventShockingWeirdPed: 'Shocking weird ped',
    CEventShockingWeirdPedApproaching: 'Shocking weird ped approaching',
  },

  // === RESPONSE EVENTS ===
  [EventCategory.RESPONSE]: {
    CEventDataResponseAggressiveRubberneck: 'Response aggressive rubberneck',
    CEventDataResponseDeferToScenarioPointFlags: 'Response defer to scenario point flags',
    CEventDataResponseFriendlyAimedAt: 'Response friendly aimed at',
    CEventDataResponseFriendlyNearMiss: 'Response friendly near miss',
    CEventDataResponsePlayerDeath: 'Response player death',
    CEventDataResponsePoliceTaskWanted: 'Response police task wanted',
    CEventDataResponseSwatTaskWanted: 'Response swat task wanted',
    CEventDataResponseTask: 'Response task',
    CEventDataResponseTaskAgitated: 'Response task agitated',
    CEventDataResponseTaskCombat: 'Response task combat',
    CEventDataResponseTaskCower: 'Response task cower',
    CEventDataResponseTaskCrouch: 'Response task crouch',
    CEventDataResponseTaskDuckAndCover: 'Response task duck and cover',
    CEventDataResponseTaskEscapeBlast: 'Response task escape blast',
    CEventDataResponseTaskEvasiveStep: 'Response task evasive step',
    CEventDataResponseTaskExhaustedFlee: 'Response task exhausted flee',
    CEventDataResponseTaskExplosion: 'Response task explosion',
    CEventDataResponseTaskFlee: 'Response task flee',
    CEventDataResponseTaskFlyAway: 'Response task fly away',
    CEventDataResponseTaskGrowlAndFlee: 'Response task growl and flee',
    CEventDataResponseTaskGunAimedAt: 'Response task gun aimed at',
    CEventDataResponseTaskHandsUp: 'Response task hands up',
    CEventDataResponseTaskHeadTrack: 'Response task head track',
    CEventDataResponseTaskLeaveCarAndFlee: 'Response task leave car and flee',
    CEventDataResponseTaskScenarioFlee: 'Response task scenario flee',
    CEventDataResponseTaskSharkAttack: 'Response task shark attack',
    CEventDataResponseTaskShockingEventBackAway: 'Response task shocking event back away',
    CEventDataResponseTaskShockingEventGoto: 'Response task shocking event goto',
    CEventDataResponseTaskShockingEventHurryAway: 'Response task shocking event hurry away',
    CEventDataResponseTaskShockingEventReact: 'Response task shocking event react',
    CEventDataResponseTaskShockingEventReactToAircraft: 'Response task shocking event react to aircraft',
    CEventDataResponseTaskShockingEventStopAndStare: 'Response task shocking event stop and stare',
    CEventDataResponseTaskShockingEventThreatResponse: 'Response task shocking event threat response',
    CEventDataResponseTaskShockingEventWatch: 'Response task shocking event watch',
    CEventDataResponseTaskShockingNiceCar: 'Response task shocking nice car',
    CEventDataResponseTaskShockingPoliceInvestigate: 'Response task shocking police investigate',
    CEventDataResponseTaskThreat: 'Response task threat',
    CEventDataResponseTaskTurnToFace: 'Response task turn to face',
    CEventDataResponseTaskWalkAway: 'Response task walk away',
    CEventDataResponseTaskWalkRoundEntity: 'Response task walk round entity',
    CEventDataResponseTaskWalkRoundFire: 'Response task walk round fire',
  },

  // === SYSTEM EVENTS ===
  [EventCategory.SYSTEM]: {
    CEventStaticCountReachedMax: 'Static count reached max',
    CEventStatChangedValue: 'Stat changed value',
    CEventScriptCommand: 'Script command',
    CEventScriptWithData: 'Script with data',
    CEventDataDecisionMaker: 'Data decision maker',
    CEventDataFileMounter: 'Data file mounter',
    CEventDecisionMakerResponse: 'Decision maker response',
    CEventEditableResponse: 'Editable response',
    CEventInfo: 'Info event',
    CEventInfoBase: 'Info base event',
    CEventSwitch2NM: 'Switch to NM',
    CEventSoundBase: 'Sound base event',
  },

  // === OTHER EVENTS ===
  [EventCategory.OTHER]: {
    CEventAgitated: 'Agitated event',
    CEventAgitatedAction: 'Agitated action',
    CEventCallForCover: 'Call for cover',
    CEventCombatTaunt: 'Combat taunt',
    CEventCommunicateEvent: 'Communicate event',
    CEventCrimeCryForHelp: 'Crime cry for help',
    CEventCrimeReported: 'Crime reported',
    CEventDisturbance: 'Disturbance',
    CEventEncroachingPed: 'Encroaching ped',
    CEventGivePedTask: 'Give ped task',
    CEventGroupScriptAI: 'Group script AI',
    CEventGroupScriptNetwork: 'Group script network',
    CEventHelpAmbientFriend: 'Help ambient friend',
    CEventInjuredCryForHelp: 'Injured cry for help',
    CEventLeaderEnteredCarAsDriver: 'Leader entered car as driver',
    CEventLeaderExitedCarAsDriver: 'Leader exited car as driver',
    CEventLeaderHolsteredWeapon: 'Leader holstered weapon',
    CEventLeaderLeftCover: 'Leader left cover',
    CEventLeaderUnholsteredWeapon: 'Leader unholstered weapon',
    CEventNewTask: 'New task',
    CEventProvidingCover: 'Providing cover',
    CEventReactionEnemyPed: 'Reaction enemy ped',
    CEventReactionInvestigateDeadPed: 'Reaction investigate dead ped',
    CEventReactionInvestigateThreat: 'Reaction investigate threat',
    CEventRequestHelp: 'Request help',
    CEventRequestHelpWithConfrontation: 'Request help with confrontation',
    CEventRespondedToThreat: 'Responded to threat',
    CEventScanner: 'Scanner event',
    CEventScenarioForceAction: 'Scenario force action',
    CEventShoutBlockingLos: 'Shout blocking LOS',
    CEventShoutTargetPosition: 'Shout target position',
    CEventSuspiciousActivity: 'Suspicious activity',
    CEventUnidentifiedPed: 'Unidentified ped',
  },
};



// Function to generate GameEvents map from the category-based structure
function generateGameEvents(): Record<string, GameEventInfo> {
  const result: Record<string, GameEventInfo> = {};

  for (const [category, events] of Object.entries(eventsByCategory)) {
    for (const [eventName, description] of Object.entries(events)) {
      result[eventName] = {
        category: category as EventCategory,
        description,
      };
    }
  }

  return result;
}

// Generate and export the GameEvents map
export const GameEvents = generateGameEvents();

// Helper function to check if an event is in a specific category
export function isEventInCategory(eventName: string, category: EventCategory): boolean {
  return GameEvents[eventName]?.category === category;
}

// Helper function to get event info
export function getEventInfo(eventName: string): GameEventInfo | undefined {
  return GameEvents[eventName];
}