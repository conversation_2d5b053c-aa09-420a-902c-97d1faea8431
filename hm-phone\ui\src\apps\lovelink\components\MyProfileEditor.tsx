import React, { useState } from 'react';
import { lovelinkMockData } from '../../../fivem';

interface MyProfileEditorProps {
  onBack: () => void;
}

const MyProfileEditor: React.FC<MyProfileEditorProps> = ({ onBack }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newInterest, setNewInterest] = useState('');
  const [editedProfile, setEditedProfile] = useState(lovelinkMockData.profiles[0]);

  if (!isEditing) {
    return (
      <div className="flex flex-col h-full bg-[#0a0f1a] text-white max-w-full">
        {/* Header */}
        <div className="flex items-center gap-2 px-3 py-2 border-b border-white/10">
          <button onClick={onBack} className="text-white/80 hover:text-white">
            <i className="fas fa-arrow-left text-base"></i>
          </button>
          <h1 className="text-white font-medium text-base flex-1">Profile</h1>
          <button onClick={() => setIsEditing(true)} className="text-blue-500 hover:text-blue-400">
            <i className="fas fa-edit"></i>
          </button>
        </div>

        {/* Profile View */}
        <div className="flex-1 overflow-y-auto p-4">
          {/* Photos */}
          <div className="flex gap-2 overflow-x-auto pb-4">
            {editedProfile.photos.map((photo, index) => (
              <div key={index} className="w-24 h-24 flex-shrink-0">
                <img
                  src={photo}
                  className="w-full h-full object-cover rounded-lg"
                  alt={`Profile photo ${index + 1}`}
                />
              </div>
            ))}
          </div>

          {/* Basic Info */}
          <div className="space-y-4 mt-4">
            <div>
              <h2 className="text-xl font-semibold">
                {editedProfile.name}, {editedProfile.age}
              </h2>
              <div className="flex items-center gap-2 text-white/60 mt-1">
                <i className="fas fa-briefcase text-sm"></i>
                <p>{editedProfile.occupation}</p>
              </div>
              <div className="flex items-center gap-2 text-white/60">
                <i className="fas fa-location-dot text-sm"></i>
                <p>{editedProfile.location}</p>
              </div>
            </div>

            {editedProfile.bio && (
              <div>
                <div className="flex items-center gap-2 text-white/60 text-sm mb-1">
                  <i className="fas fa-user text-sm"></i>
                  <h3>About</h3>
                </div>
                <p>{editedProfile.bio}</p>
              </div>
            )}

            {editedProfile.interests.length > 0 && (
              <div>
                <div className="flex items-center gap-2 text-white/60 text-sm mb-2">
                  <i className="fas fa-heart text-sm"></i>
                  <h3>Interests</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {editedProfile.interests.map((interest, index) => (
                    <span key={index} className="bg-white/10 rounded-full px-3 py-1 text-sm">
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Original editor view
  return (
    <div className="flex flex-col h-full bg-[#0a0f1a] text-white max-w-full">
      <div className="flex items-center gap-2 px-3 py-2 border-b border-white/10">
        <button onClick={() => setIsEditing(false)} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left text-base"></i>
        </button>
        <h1 className="text-white font-medium text-base flex-1">Edit Profile</h1>
        <button
          onClick={() => setIsEditing(false)}
          className="text-blue-500 hover:text-blue-400 text-sm font-medium"
        >
          Save
        </button>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Photos */}
        <div className="space-y-2 max-w-full">
          <label className="text-sm text-white/60">Photos</label>
          <div className="flex gap-2 overflow-x-auto pb-2 max-w-full">
            {editedProfile.photos.map((photo, index) => (
              <div key={index} className="relative w-24 h-24 flex-shrink-0">
                <img
                  src={photo}
                  className="w-full h-full object-cover rounded-lg"
                  alt={`Profile photo ${index + 1}`}
                />
                <button
                  onClick={() =>
                    setEditedProfile({
                      ...editedProfile,
                      photos: editedProfile.photos.filter((_, i) => i !== index)
                    })
                  }
                  className="absolute top-1 right-1 w-6 h-6 rounded-full bg-black/50 flex items-center justify-center"
                >
                  <i className="fas fa-times text-xs"></i>
                </button>
              </div>
            ))}
            <button
              onClick={() => {
                // TODO: Implement photo upload
                const newPhoto = 'https://picsum.photos/400/400';
                setEditedProfile({
                  ...editedProfile,
                  photos: [...editedProfile.photos, newPhoto]
                });
              }}
              className="w-24 h-24 flex-shrink-0 border-2 border-dashed border-white/20 rounded-lg flex items-center justify-center"
            >
              <i className="fas fa-plus text-white/40"></i>
            </button>
          </div>
        </div>

        {/* Basic Info */}
        <div className="space-y-4">
          <div>
            <label className="text-sm text-white/60">Name</label>
            <input
              type="text"
              value={editedProfile.name}
              onChange={e => setEditedProfile({ ...editedProfile, name: e.target.value })}
              className="w-full bg-white/5 rounded-lg px-3 py-2 mt-1"
            />
          </div>

          <div>
            <label className="text-sm text-white/60">Age</label>
            <input
              type="number"
              value={editedProfile.age}
              onChange={e => setEditedProfile({ ...editedProfile, age: parseInt(e.target.value) })}
              className="w-full bg-white/5 rounded-lg px-3 py-2 mt-1"
            />
          </div>

          <div>
            <div className="flex items-center gap-2 mb-1">
              <i className="fas fa-briefcase text-white/60 text-sm"></i>
              <label className="text-sm text-white/60">Occupation</label>
            </div>
            <input
              type="text"
              value={editedProfile.occupation}
              onChange={e => setEditedProfile({ ...editedProfile, occupation: e.target.value })}
              className="w-full bg-white/5 rounded-lg px-3 py-2"
            />
          </div>

          <div>
            <div className="flex items-center gap-2 mb-1">
              <i className="fas fa-location-dot text-white/60 text-sm"></i>
              <label className="text-sm text-white/60">Location</label>
            </div>
            <input
              type="text"
              value={editedProfile.location}
              onChange={e => setEditedProfile({ ...editedProfile, location: e.target.value })}
              className="w-full bg-white/5 rounded-lg px-3 py-2"
            />
          </div>

          <div>
            <div className="flex items-center gap-2 mb-1">
              <i className="fas fa-user text-white/60 text-sm"></i>
              <label className="text-sm text-white/60">Bio</label>
            </div>
            <textarea
              value={editedProfile.bio}
              onChange={e => setEditedProfile({ ...editedProfile, bio: e.target.value })}
              className="w-full bg-white/5 rounded-lg px-3 py-2 h-24 resize-none"
            />
          </div>

          {/* Interests */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <i className="fas fa-heart text-white/60 text-sm"></i>
              <label className="text-sm text-white/60">Interests</label>
            </div>
            <div className="flex flex-wrap gap-2 mt-2 max-w-full">
              {editedProfile.interests.map((interest, index) => (
                <div
                  key={index}
                  className="bg-white/10 rounded-full px-3 py-1 text-sm flex items-center gap-2"
                >
                  <span className="truncate">{interest}</span>
                  <button onClick={() => {}}>
                    <i className="fas fa-times text-xs"></i>
                  </button>
                </div>
              ))}
            </div>
            <div className="flex gap-2 mt-2 max-w-full">
              <input
                type="text"
                value={newInterest}
                onChange={e => setNewInterest(e.target.value)}
                placeholder="Add interest..."
                className="flex-1 bg-white/5 rounded-lg px-3 py-2 min-w-0"
              />
              <button
                onClick={() => {}}
                className="bg-blue-500 hover:bg-blue-600 rounded-lg px-4 py-2 flex-shrink-0"
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyProfileEditor;
