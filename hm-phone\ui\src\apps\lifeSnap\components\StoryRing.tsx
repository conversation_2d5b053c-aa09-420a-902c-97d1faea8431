import React from 'react';
import { useLifeSnapStore } from '../stores/lifeSnapStore';

interface StoryRingProps {
  userId: string | number; // Can be string (identifier) or number for backward compatibility
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  children: React.ReactNode;
}

const StoryRing: React.FC<StoryRingProps> = ({ userId, size = 'md', onClick, children }) => {
  const { stories } = useLifeSnapStore();

  const hasActiveStory = stories.some(
    story => story.userId === userId && new Date(story.expiresAt) > new Date()
  );

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-14 h-14',
    lg: 'w-20 h-20'
  };

  if (!hasActiveStory) {
    return <div className={sizeClasses[size]}>{children}</div>;
  }

  return (
    <div
      onClick={onClick}
      className={`p-[2px] rounded-full bg-gradient-to-tr from-yellow-500 to-pink-500 cursor-pointer ${
        onClick ? 'cursor-pointer' : ''
      }`}
    >
      {children}
    </div>
  );
};

export default StoryRing;
