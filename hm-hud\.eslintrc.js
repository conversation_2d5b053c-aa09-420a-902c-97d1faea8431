module.exports = {
    parser: '@typescript-eslint/parser',
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:prettier/recommended'
    ],
    plugins: ['@typescript-eslint', 'prettier'],
    env: {
        node: true,
        es6: true,
        browser: true // For FiveM client-side code
    },
    globals: {
        // FiveM globals
        GetCurrentResourceName: 'readonly',
        on: 'readonly',
        onNet: 'readonly',
        emit: 'readonly',
        emitNet: 'readonly',
        exports: 'writable',
        RegisterCommand: 'readonly',
        // Add any other FiveM globals you use frequently
    },
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module'
    },
    rules: {
        'prettier/prettier': 'warn',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
        '@typescript-eslint/no-non-null-assertion': 'off',
        '@typescript-eslint/no-empty-function': 'warn',
        '@typescript-eslint/ban-ts-comment': 'off',
        'no-console': 'off',
        'no-var': 'warn', // Changed to warn instead of off to encourage let/const
        'prefer-const': 'warn',
        'no-undef': 'off' // FiveM natives will trigger this, so we turn it off
    },
    ignorePatterns: ['build/', 'node_modules/', 'ui/']
};
