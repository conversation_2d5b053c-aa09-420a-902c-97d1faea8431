export interface PhoneSettings {
  wallpaper: string;
  ringtone: string;
  notificationSound: string;
  theme: 'light' | 'dark' | 'system';
  fontSize: 'small' | 'medium' | 'large';
  language: string;
  doNotDisturb: boolean;
  airplaneMode: boolean;
  showNotificationsOnLockScreen: boolean;
  vibrate: boolean;
  volume: number;
  brightness: number;
  autoLock: number; // seconds
  lockScreenPassword?: string;
  lockScreenType: 'none' | 'pin' | 'pattern' | 'password';
  emergencyContactIds?: number[];
  customColors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
}
