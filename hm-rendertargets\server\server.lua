-- RegisterServerEvent('hm_carbomb:armbomb')
-- AddEventHandler('hm_carbomb:armbomb', function(vehicle, plate, bombType)
--     local sourcePlayerId = source
--     -- local plate = GetVehicleNumberPlateText(vehicle)
--     local success = false  -- Assume arming is not successful initially
--     -- For example, you might use a database or some other conditions
--     success = true
--     -- Trigger the corresponding client-side event with the arming result
--     if success then -- Add additional logic for a successful arming, such as removing the bomb item from the player's inventory
--         TriggerClientEvent('hm_carbomb:armed', sourcePlayerId, success, vehicle, plate, bombType)
--         print("TriggerClientEvent : hm_carbomb:armed".."_"..vehicle.."_"..plate.."_"..bombType)
--     end
--     Citizen.Wait(5000)
--     TriggerEvent('hm_carbomb:countdown', vehicle, bombType)
-- end)

-- RegisterServerEvent('hm_carbomb:disarmbomb')
-- AddEventHandler('hm_carbomb:disarmbomb', function(vehicle, plate, bombType)
--     local sourcePlayerId = source
--     local success = false  -- Assume disarming is not successful initially
--     -- Add your custom logic here to check if the player can disarm the bomb
--     -- For example, you might use a database or some other conditions
--     -- For now, let's assume the disarming is always successful
--     success = true
--     -- Trigger the corresponding client-side event with the disarming result
--     TriggerClientEvent('hm_carbomb:disarmed', sourcePlayerId, success, vehicle, plate, bombType)
--     if success then
--         print("TriggerClientEvent : hm_carbomb:disarmed")
--     end
-- end)

-- RegisterServerEvent('hm_rendertargets_sv:GetCurrentPedWeaponWeapon')
-- AddEventHandler('hm_rendertargets_sv:GetCurrentPedWeaponWeapon', function()
--     local sourcePlayerId = source
--     weapon = GetCurrentPedWeapon(sourcePlayerId)
--     TriggerEvent('hm_rendertargets_cl:GetCurrentPedWeaponWeapon', weapon)
-- end)

