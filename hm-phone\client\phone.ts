/**
 * Phone Client - Core Phone Functionality
 *
 * This file handles the core phone functionality including:
 * - Opening and closing the phone
 * - Managing phone visibility state (CLOSED, PEEK, OPEN)
 * - NUI communication for phone actions
 */
/// <reference types="@citizenfx/client" />

// Import services and globals
import { animationService, controlsService, playerService } from './services';
import { PhoneStateEnum } from './types';
import './globals'; // Import globals to ensure they're initialized

// Track previous state (not needed as a global)
let previousState: PhoneStateEnum = PhoneStateEnum.CLOSED;

/**
 * Set the phone state
 * @param newState The new state to set
 */
function setPhoneState(newState: PhoneStateEnum): void {
    try {
        // Store previous state
        previousState = global.phoneState;

        // Update current state
        global.phoneState = newState;

        // Send NUI message with new state
        SendNUIMessage({
            app: 'phone',
            type: 'setState',
            data: {
                state: newState
            }
        });

        // Handle NUI focus based on state
        if (newState === PhoneStateEnum.OPEN) {
            // Set focus to true, true to enable UI interaction
            SetNuiFocus(true, true);

            // Allow movement while NUI is focused
            // This is the key to allowing movement while the phone UI is open
            SetNuiFocusKeepInput(true);

            // Start controls tick handler - disables camera movement but allows player movement
            // Player can move, jump, and sprint but cannot look around while phone is open
            controlsService.startControlsTickHandler();
        } else {
            SetNuiFocus(false, false);
            SetNuiFocusKeepInput(false);

            // Remove tick handler if phone is closed
            if (newState === PhoneStateEnum.CLOSED) {
                controlsService.stopControlsTickHandler();
            }
        }

        // Log state change
        console.log(`[Phone] State changed: ${PhoneStateEnum[previousState]} -> ${PhoneStateEnum[newState]}`);
    } catch (error) {
        console.error('[Phone] Error setting phone state:', error);
    }
}

/**
 * Open the phone fully
 * Transitions from any state to OPEN
 */
export async function openPhone(): Promise<void> {
    try {
        // Prevent opening if already open
        if (global.phoneState === PhoneStateEnum.OPEN) {
            return;
        }

        // Check if player can use phone
        if (!playerService.canUsePhone()) {
            return;
        }

        // Send player data to the UI only if it hasn't been sent before
        // or if it has changed (handled by event listeners)
        const { sendPlayerDataToUI } = await import('./handlers');
        sendPlayerDataToUI(false); // false = don't force send if already sent

        // Play phone animation
        // Ensure prop is created before playing open animation sequence
        if (global.phoneState === PhoneStateEnum.CLOSED) {
            await animationService.createPhoneProp(); // Ensure prop exists
            await animationService.openPhone();
        } else if (global.phoneState === PhoneStateEnum.PEEK) {
            // If peeking, just transition to full open (prop should exist)
            await animationService.openPhone();
        }

        // Update state
        setPhoneState(PhoneStateEnum.OPEN);
    } catch (error) {
        console.error('[Phone] Error opening phone:', error);
    }
}

/**
 * Close the phone completely
 * Transitions from any state to CLOSED
 * @param _force Force close without animation (unused while animations are disabled)
 */
export async function closePhone(_force = false): Promise<void> {
    try {
        // Prevent closing if already closed
        if (global.phoneState === PhoneStateEnum.CLOSED) {
            return;
        }
        // Play close animation
        if (!_force && (global.phoneState === PhoneStateEnum.OPEN || global.phoneState === PhoneStateEnum.PEEK)) {
            await animationService.closePhone(_force);
        } else if (_force) {
            animationService.removePhoneProp(); // Just remove prop if forcing
        }

        // Update state
        setPhoneState(PhoneStateEnum.CLOSED);

        // Notify UI to clean up large datasets
        // This tells the UI to retain only essential data (contacts, settings)
        // and clear large datasets (messages content, photos, etc.)
        SendNUIMessage({
            app: 'phone',
            type: 'cleanup',
            data: {
                retainApps: ['contacts', 'settings'],
                discardApps: ['messages', 'photos', 'lifeSnap', 'music', 'yellowPages']
            }
        });

        // Also clean up our local cache
        // We'll directly call our cleanup handler
        const { appDataCache } = await import('./handlers');
        if (appDataCache) {
            ['messages', 'photos', 'lifeSnap', 'music', 'yellowPages'].forEach(app => {
                if (appDataCache[app]) {
                    delete appDataCache[app];
                    console.log(`[Phone] Cleared cache for app: ${app}`);
                }
            });
        }
    } catch (error) {
        console.error('[Phone] Error closing phone:', error);
        // Reset state on error
        setPhoneState(PhoneStateEnum.CLOSED);
    }
}

/**
 * Show the phone in peek mode
 * Transitions from CLOSED to PEEK
 */
export async function peekPhone(): Promise<void> {
    try {
        // Only allow peeking from closed state
        if (global.phoneState !== PhoneStateEnum.CLOSED) {
            return;
        }
        // Ensure prop is created for peeking
        await animationService.createPhoneProp();
        // Here, you might want a specific "peek" animation if it's different from just creating the prop
        // For now, creating the prop makes it visible for a "peek"

        // Update state
        setPhoneState(PhoneStateEnum.PEEK);
    } catch (error) {
        console.error('[Phone] Error peeking phone:', error);
    }
}

/**
 * Check if player can use phone
 * Re-export from Player service
 * @returns True if player can use phone
 */
export function canUsePhone(): boolean {
    return playerService.canUsePhone();
}

/**
 * Toggle phone between open and closed states
 */
export async function togglePhone(): Promise<void> {
    try {
        // Check if player can use phone
        if (!playerService.canUsePhone()) {
            return;
        }
        if (global.phoneState === PhoneStateEnum.CLOSED) {
            await openPhone();
        } else if (global.phoneState === PhoneStateEnum.OPEN || global.phoneState === PhoneStateEnum.PEEK) {
            await closePhone();
        }
    } catch (error) {
        console.error('[Phone] Error toggling phone:', error);
    }
}

// These functions have been moved to utility files

/**
 * Helper function to check if phone is fully open
 * @returns True if phone is in OPEN state
 */
export function isPhoneOpen(): boolean {
    return global.phoneState === PhoneStateEnum.OPEN;
}

/**
 * Helper function to check if phone is visible (either fully or peeking)
 * @returns True if phone is in OPEN or PEEK state
 */
export function isPhoneVisible(): boolean {
    return global.phoneState !== PhoneStateEnum.CLOSED;
}

/**
 * Helper function to check if phone is in peek mode
 * @returns True if phone is in PEEK state
 */
export function isPhonePeeking(): boolean {
    return global.phoneState === PhoneStateEnum.PEEK;
}

// Register NUI callbacks at the module level
RegisterNuiCallback('closePhone', (_data: any, cb: (data: any) => void) => {
    closePhone();
    cb({});
});

RegisterNuiCallback('openPhone', (_data: any, cb: (data: any) => void) => {
    openPhone();
    cb({});
});

// Register force close event handler at the module level
onNet('phone:forceClose', () => {
    closePhone(true);
});

// No need for auto-close tick handler - we'll use events instead

/**
 * Initialize phone state management
 */
export function initializePhone(): void {
    // Native FiveM event
    on('playerDied', () => {
        if (isPhoneVisible()) {
            closePhone(true);
        }
    });

    // QBCore specific event
    onNet('QBCore:Client:OnPlayerDeath', () => {
        if (isPhoneVisible()) {
            closePhone(true);
        }
    });
    // Job update
    onNet('QBCore:Client:OnJobUpdate', async () => {
        const { updatePlayerDataGlobals } = await import('@shared/framework/globals');
        updatePlayerDataGlobals();
        if (isPhoneVisible()) {
            const { sendPlayerDataToUI } = await import('./handlers');
            sendPlayerDataToUI(true); // true = force send even if already sent
        }
    });

    // Gang update
    onNet('QBCore:Client:OnGangUpdate', async () => {
        const { updatePlayerDataGlobals } = await import('@shared/framework/globals');
        updatePlayerDataGlobals();
        if (isPhoneVisible()) {
            const { sendPlayerDataToUI } = await import('./handlers');
            sendPlayerDataToUI(true); // true = force send even if already sent
        }
    });

    // Player loaded
    onNet('QBCore:Client:OnPlayerLoaded', async () => {
        const { updatePlayerDataGlobals } = await import('@shared/framework/globals');
        updatePlayerDataGlobals();
    });

    // HM Core specific event
    onNet('hm-core:playerLoaded', async () => {
        const { updatePlayerDataGlobals } = await import('@shared/framework/globals');
        updatePlayerDataGlobals();
    });
}

/**
 * Clean up phone resources
 */
export function cleanupPhone(): void {
    // Force close the phone if open
    if (global.phoneState !== PhoneStateEnum.CLOSED) {
        closePhone(true); // Ensure closePhone logic (including animationService.removePhoneProp) is called
    } else {
        // If already closed, ensure prop is removed if it somehow still exists
        animationService.removePhoneProp();
    }
    controlsService.stopControlsTickHandler();
    // animationService.removePhoneProp(); // This is now handled within closePhone or directly above

    console.log('[Phone] Phone resources cleaned up');
}

/**
 * Reset phone state
 * Used when player reconnects to the server
 */
export function resetPhoneState(): void {
    closePhone(true); // This will set state to CLOSED and remove prop
    // global.phoneState = PhoneStateEnum.CLOSED; // set by closePhone
    previousState = PhoneStateEnum.CLOSED; // Reset previous state as well
    // animationService.removePhoneProp(); // Handled by closePhone(true)
}

// Note: Resource start/stop events are handled in client/index.ts

// Handle player reconnection
on('playerSpawned', () => {
    resetPhoneState();
});
