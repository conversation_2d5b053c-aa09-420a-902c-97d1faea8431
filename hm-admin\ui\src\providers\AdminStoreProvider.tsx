import { ReactNode, useEffect } from 'react';
import { initializeAdminStore, useAdminStore } from '../stores/adminStore';
import { onNuiEvent } from '../utils/nui';

interface AdminStoreProviderProps {
  children: ReactNode;
}

/**
 * Provider component for initializing the Admin Store
 * This sets up NUI event listeners and fetches initial data
 */
export const AdminStoreProvider: React.FC<AdminStoreProviderProps> = ({ children }) => {  useEffect(() => {
    // Initialize store and get cleanup function
    const cleanup = initializeAdminStore();
    
    // Only set up listeners, but don't fetch data yet
    // Wait for UI to become visible before fetching data
    
    // Listen for UI visibility changes
    const visibilityCleanup = onNuiEvent('setVisible', (data: { visible: boolean }) => {
      if (!data.visible) {
        // UI is being hidden, clean up resources
        useAdminStore.getState().cleanup();
      } else {
        // UI is being shown, fetch fresh data
        useAdminStore.getState().fetchAllData();
      }
    });
    
    // Clean up on unmount
    return () => {
      cleanup();
      visibilityCleanup();
      useAdminStore.getState().reset();
    };
  }, []);
  
  return <>{children}</>;
};
