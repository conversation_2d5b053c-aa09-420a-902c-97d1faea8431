// Starter items configuration for new characters
export interface StarterLoadout {
    name: string;
    description: string;
    items: Array<{
        definitionId: string;
        quantity: number;
        slot?: number; // Optional: force specific slot
        metadata?: any;
    }>;
}

export const starterLoadouts: Record<string, StarterLoadout> = {
    default: {
        name: 'Basic Starter Kit',
        description: 'Basic items for new players',
        items: [
            {
                definitionId: 'phone',
                quantity: 1,
                slot: 100, // Equipment slot
                metadata: {
                    phoneNumber: '555-0000',
                    batteryLevel: 100
                }
            },
            {
                definitionId: 'bread',
                quantity: 3,
                metadata: {
                    expiryDate: '2024-02-01',
                    quality: 100
                }
            },
            {
                definitionId: 'bandage',
                quantity: 2,
                metadata: {}
            }
        ]
    },

    vip: {
        name: 'VIP Starter Kit',
        description: 'Enhanced starter kit for VIP players',
        items: [
            {
                definitionId: 'phone',
                quantity: 1,
                slot: 100,
                metadata: {
                    phoneNumber: '555-VIP1',
                    batteryLevel: 100
                }
            },
            {
                definitionId: 'tablet',
                quantity: 1,
                slot: 103,
                metadata: {
                    batteryLevel: 100,
                    apps: ['gps', 'calculator', 'notes']
                }
            },
            {
                definitionId: 'bread',
                quantity: 10,
                metadata: {
                    expiryDate: '2024-02-01',
                    quality: 100
                }
            },
            {
                definitionId: 'bandage',
                quantity: 5,
                metadata: {}
            }
        ]
    }
};
