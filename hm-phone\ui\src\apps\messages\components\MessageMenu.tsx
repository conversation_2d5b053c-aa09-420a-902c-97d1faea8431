import React from 'react';

interface MessageMenuProps {
  showMenu: boolean;
  menuPosition: 'top' | 'bottom';
  menuRef: React.RefObject<HTMLDivElement>;
  buttonRef: React.RefObject<HTMLButtonElement>;
  canDelete: boolean;
  isDeleted: boolean;
  handleMenuToggle: () => void;
  handleEdit: () => void;
  handleDeleteClick: () => void;
  handleReplyClick: () => void;
}

const MessageMenu: React.FC<MessageMenuProps> = ({
  showMenu,
  menuPosition,
  menuRef,
  buttonRef,
  canDelete,
  isDeleted,
  handleMenuToggle,
  handleEdit,
  handleDeleteClick,
  handleReplyClick
}) => {
  return (
    <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity relative">
      <button
        ref={buttonRef}
        onClick={handleMenuToggle}
        className="p-1 text-white/60 hover:text-white cursor-pointer"
      >
        <i className="fas fa-ellipsis-v text-xs"></i>
      </button>

      {showMenu && (
        <div
          ref={menuRef}
          className={`absolute ${
            menuPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
          } left-0 w-32 bg-[#1a1f2a] rounded-lg shadow-lg py-1 z-[100]`}
        >
          {!isDeleted && (
            <>
              <button
                onClick={handleEdit}
                className="w-full text-left px-3 py-1.5 text-sm text-white hover:bg-white/10 flex items-center gap-2"
              >
                <i className="fas fa-pen text-xs text-white/60"></i>
                Edit
              </button>
              <button
                onClick={handleReplyClick}
                className="w-full text-left px-3 py-1.5 text-sm text-white hover:bg-white/10 flex items-center gap-2"
              >
                <i className="fas fa-reply text-xs text-white/60"></i>
                Reply
              </button>
              {canDelete && (
                <button
                  onClick={handleDeleteClick}
                  className="w-full text-left px-3 py-1.5 text-sm hover:bg-white/10 flex items-center gap-2 text-red-400"
                >
                  <i className="fas fa-trash text-xs text-red-400/60"></i>
                  Delete
                </button>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default MessageMenu;
