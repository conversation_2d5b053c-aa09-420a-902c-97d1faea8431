import React from 'react';

interface MessageAvatarProps {
  showAvatar?: boolean;
  avatar?: string | null;
  sender?: string;
  displayName?: string;
  isContact?: boolean; // New prop to indicate if this is a contact's avatar
}

const MessageAvatar: React.FC<MessageAvatarProps> = ({
  showAvatar,
  avatar,
  sender,
  displayName,
  isContact = false // Default to false
}) => {
  if (!showAvatar) return null;

  // Determine if we should show the contact indicator
  const showContactIndicator = isContact && avatar;

  return (
    <div className={`w-6 h-6 rounded-full ${showContactIndicator ? 'ring-1 ring-blue-500' : 'bg-gradient-to-br from-zinc-700 to-zinc-800'} flex-shrink-0 overflow-hidden mt-1 relative`}>
      <div className="w-full h-full flex items-center justify-center">
        {avatar ? (
          <img src={avatar} alt={displayName || sender} className="w-full h-full object-cover" />
        ) : (
          <span className="text-white text-xs font-medium">
            {displayName?.charAt(0).toUpperCase() || sender?.charAt(0).toUpperCase() || '?'}
          </span>
        )}
      </div>
    </div>
  );
};

export default MessageAvatar;
