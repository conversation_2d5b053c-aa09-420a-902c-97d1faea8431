/**
 * UI Utilities Index
 *
 * This file exports all UI utility functions for easy importing.
 */

// Export all utilities
export * from './formatUtils';
export * from './componentUtils';
export * from './formUtils';
export * from './animationUtils';
export * from './modalUtils';
export * from './imageUtils';
export * from './errorUtils';

// Export individual utilities with more descriptive names
import {
  formatPhoneNumber,
  formatCurrency,
  formatFileSize,
  truncateText,
  formatNameToInitials,
  getFirstLetter
} from './formatUtils';
import {
  renderLoadingState,
  renderErrorState,
  renderEmptyState,
  renderActionButtons,
  renderAvatar,
  renderBadge,
  renderListItem,
  renderSectionHeader
} from './componentUtils';
import {
  validateEmail,
  validatePhone,
  validateUrl,
  validateInput,
  formatInputValue,
  createFormState
} from './formUtils';
import {
  fadeTransition,
  slideTransition,
  scaleTransition,
  staggeredAnimation,
  pulseAnimation,
  bounceAnimation,
  tabIndicatorAnimation,
  buttonTapAnimation,
  listItemAnimation
} from './animationUtils';
import { useConfirmDialog, useImagePreview, useSelectionModal } from './modalUtils';
import {
  loadImage,
  getImageDimensions,
  resizeImage,
  dataUrlToBlob,
  getDataUrlFileSize,
  applyImageFilter,
  createThumbnail
} from './imageUtils';
import {
  handleApiError,
  logError,
  createUserFriendlyError,
  isNetworkError,
  createRetryFunction
} from './errorUtils';

// Format utilities
export {
  formatPhoneNumber,
  formatCurrency,
  formatFileSize,
  truncateText,
  formatNameToInitials,
  getFirstLetter
};

// Component utilities
export {
  renderLoadingState,
  renderErrorState,
  renderEmptyState,
  renderActionButtons,
  renderAvatar,
  renderBadge,
  renderListItem,
  renderSectionHeader
};

// Form utilities
export {
  validateEmail,
  validatePhone,
  validateUrl,
  validateInput,
  formatInputValue,
  createFormState
};

// Animation utilities
export {
  fadeTransition,
  slideTransition,
  scaleTransition,
  staggeredAnimation,
  pulseAnimation,
  bounceAnimation,
  tabIndicatorAnimation,
  buttonTapAnimation,
  listItemAnimation
};

// Modal utilities
export { useConfirmDialog, useImagePreview, useSelectionModal };

// Image utilities
export {
  loadImage,
  getImageDimensions,
  resizeImage,
  dataUrlToBlob,
  getDataUrlFileSize,
  applyImageFilter,
  createThumbnail
};

// Error utilities
export { handleApiError, logError, createUserFriendlyError, isNetworkError, createRetryFunction };
