# Temporarily ignore files with type issues
src/apps/camera/types/cameraTypes.ts
src/apps/contacts/components/CompactCallHistory.tsx
src/apps/contacts/components/NearbyPanel.tsx
src/apps/contacts/handlers/dialerHandlers.ts
src/apps/lifeSnap/components/Stories.tsx
src/apps/lifeSnap/components/StoryViewer.tsx
src/apps/settings/components/NotificationsSettings.tsx
src/common/stores/modalStore.ts
src/common/stores/selectionStore.ts
src/fivem/clientEventReceiver.ts
src/fivem/clientRequestSender.ts
src/navigation/handlers/navigationHandlers.ts
src/navigation/navigationStore.ts
src/navigation/navigationTypes.ts
src/notifications/components/TopBarNotificationSection.tsx
src/notifications/types/notificationTypes.ts
src/utils/environment.ts
src/utils/ui/errorUtils.ts
src/utils/ui/formUtils.ts
src/utils/ui/imageUtils.ts
src/utils/ui/modalUtils.tsx

# Node modules
node_modules/