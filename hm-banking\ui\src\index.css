@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  /* Ensure root elements fill viewport */
  html, body, #root {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
  }

  /* Disable tab navigation for all elements */
  * {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Remove tab focus from all interactive elements */
  button, input, select, textarea, [tabindex] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

@layer components {
  /* Slot label gradient animations - seamless blending with slot background */
  .slot-label-gradient {
    background: linear-gradient(
    );
    transition: all 0.3s ease-in-out;
  }

  .slot-label-gradient:hover {
    background: linear-gradient(
    );
  }

  /* Label container with smooth entrance - compact */
  .slot-label-container {
    height: 18px; /* Slightly increased for padding */
    transform: translateY(1px);
    opacity: 0.9;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slot-label-container:hover {
    transform: translateY(0);
    opacity: 1;
  }

  /* Accent gradient under the label */
  .slot-label-accent {
    background: linear-gradient(
    );
    transition: all 0.3s ease-in-out;
  }

  .slot-label-accent:hover {
    background: linear-gradient(
    );
  }

  /* Label text glow effect on hover - compact */
  .slot-label-text {
    transition: all 0.3s ease-in-out;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
  }

  .slot-label-text:hover {
    color: rgb(255, 255, 255);
    text-shadow: 
  }

  /* Toast and ProgressBar animations */
  @keyframes slideInRight {
    from {transform: translateX(100%); opacity: 0;}
    to {transform: translateX(0); opacity: 1;}
  }

  @keyframes shine {
    from {transform: translateX(-100%);}
    to {transform: translateX(100%);}
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out forwards;
  }

  .animate-shine {
    animation: shine 2s infinite;
  }

  /* Enhanced drag animations */
  @keyframes trail {
    0% { transform: translateX(-100%); opacity: 0; }
  }

  @keyframes dragBounce { /* Add keyframes if not defined elsewhere */
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }

  @keyframes rarityPulse { /* Add keyframes if not defined elsewhere */
    0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
    50% { box-shadow: 0 0 15px rgba(255, 255, 255, 1); }
  }

  .animate-trail {animation: trail 0.5s ease-out forwards;}

  .animate-drag-bounce {animation: dragBounce 0.5s ease-in-out;}

  .animate-rarity-pulse {animation: rarityPulse 1.5s infinite;}
}
