const esbuild = require('esbuild');
const { resolve } = require('path');

// Check if we're in production mode
const production = process.argv.includes('--production');
// Check if we're in watch mode
const watch = process.argv.includes('--watch');

// Path aliases (matching your tsconfig.json)
const alias = {
  '@shared': resolve(__dirname, 'scripts/shared'),
  '@client': resolve(__dirname, 'scripts/client'),
  '@server': resolve(__dirname, 'scripts/server')
};

// Common build options
const commonOptions = {
  bundle: true,
  minify: production,
  format: 'iife',
  target: ['es2020'],
  logLevel: 'info',
  define: {
    'process.env.NODE_ENV': production ? '"production"' : '"development"'
  },
  plugins: [
    // Custom plugin to handle path aliases
    {
      name: 'alias-resolver',
      setup(build) {
        // Handle path aliases
        Object.entries(alias).forEach(([aliasName, aliasPath]) => {
          const regex = new RegExp(`^${aliasName}(/.*)?$`);

          build.onResolve({ filter: regex }, args => {
            const importPath = args.path.replace(aliasName, aliasPath);
            return { path: importPath };
          });
        });
      }
    }
  ],
  external: [
    '@citizenfx/client',
    '@citizenfx/server',
    'node:*'
  ]
};

// Build configurations for each entry point
const buildConfigs = [
  {
    entryPoints: ['scripts/client/main.ts'],
    outfile: 'build/client.js',
    platform: 'browser',
    ...commonOptions
  },
  {
    entryPoints: ['scripts/server/main.ts'],
    outfile: 'build/server.js',
    platform: 'node',
    format: 'cjs', // Use CommonJS format for server
    minify: false, // Disable minification for server to help with debugging
    ...commonOptions
  },
  {
    entryPoints: ['scripts/shared/main.ts'],
    outfile: 'build/shared.js',
    platform: 'browser',
    ...commonOptions
  }
];

// Build function
async function runBuild() {
  try {
    if (watch) {
      // Watch mode - create context for each config and watch
      const contexts = await Promise.all(
        buildConfigs.map(config => esbuild.context(config))
      );

      await Promise.all(contexts.map(context => context.watch()));
      console.log('Watching for changes...');
    } else {
      // Build once
      for (const config of buildConfigs) {
        await esbuild.build(config);
        console.log(`Built ${config.outfile} successfully`);
      }
      console.log('Build complete!');
    }
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

runBuild();