import React, { useEffect, useState, useCallback } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNotificationStore } from '../stores/notificationStore';
import { Notification, NotificationData } from '../types/notificationTypes';
import { NotificationRenderer } from './NotificationRenderer';
import { useNavigationStore } from '../../navigation/navigationStore';
import { usePhoneStore, PhoneStateEnum } from '../../common/stores/phoneStateStore';

const PopupNotifications: React.FC = () => {
  const { notifications, moveToTopBar } = useNotificationStore();
  const { openAppView } = useNavigationStore();
  const phoneStore = usePhoneStore();
  const [activeNotifications, setActiveNotifications] = useState<Notification[]>([]);

  // Handle notification dismiss - using useCallback to memoize the function
  const handleDismiss = useCallback(
    (notification: Notification) => {
      // Move to top bar instead of removing completely
      moveToTopBar(notification.appId, notification.id);
    },
    [moveToTopBar]
  );

  // Handle notification click with deep linking
  const handleNotificationClick = useCallback(
    (notification: Notification) => {
      // If there's a deep link, navigate to the specified app and view
      if (notification.deepLink) {
        const { app, view, params } = notification.deepLink;

        // Make sure the phone is visible
        phoneStore.actions.setPhoneState(PhoneStateEnum.OPEN);

        // Navigate to the app and view
        openAppView(app, view, params || {});

        // Move notification to top bar
        moveToTopBar(notification.appId, notification.id);
      }
    },
    [openAppView, phoneStore, moveToTopBar]
  );

  // Filter notifications that should be shown as popups (not moved to top bar)
  useEffect(() => {
    const popupNotifs: Notification[] = [];

    // Collect all notifications that haven't been moved to the top bar
    Object.entries(notifications).forEach(([appId, appNotifications]) => {
      appNotifications.forEach(notification => {
        if (!notification.movedToTopBar) {
          popupNotifs.push({
            ...notification,
            appId: parseInt(appId)
          });
        }
      });
    });

    // Sort by timestamp (newest first)
    popupNotifs.sort((a, b) => b.timestamp - a.timestamp);

    // Limit to 3 most recent notifications
    setActiveNotifications(popupNotifs.slice(0, 3));
  }, [notifications]);

  // Auto-dismiss notifications after a delay
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];

    activeNotifications.forEach(notification => {
      // Default duration is 5 seconds if not specified
      const duration = notification.duration || 5000;

      // Skip auto-dismiss for persistent notifications
      if (notification.persistent) return;

      const timer = setTimeout(() => {
        handleDismiss(notification);
      }, duration);

      timers.push(timer);
    });

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [activeNotifications, handleDismiss]);

  return (
    <div className="fixed top-16 right-4 space-y-2 z-50 max-w-xs">
      <AnimatePresence>
        {activeNotifications.map(notification => (
          <motion.div
            key={`${notification.appId}-${notification.id}`}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 50 }}
            transition={{ duration: 0.3 }}
            className="mb-2"
          >
            <NotificationRenderer
              notification={notification as NotificationData}
              onClose={() => handleDismiss(notification)}
              onClick={() => handleNotificationClick(notification)}
              className="shadow-lg"
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default PopupNotifications;
