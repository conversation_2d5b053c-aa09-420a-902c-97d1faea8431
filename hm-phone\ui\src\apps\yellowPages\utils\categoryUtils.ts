import { AdCategory } from '../types/yellowPagesTypes';

export const getCategoryInfo = (category: AdCategory) => {
  switch (category) {
    case 'BUY':
      return {
        colorClasses: {
          bg: 'bg-blue-500/10',
          border: 'border-blue-500/20',
          text: 'text-blue-500/80'
        },
        icon: 'shopping-cart',
        label: 'Buy'
      };
    case 'SELL':
      return {
        colorClasses: {
          bg: 'bg-green-500/10',
          border: 'border-green-500/20',
          text: 'text-green-500/80'
        },
        icon: 'tag',
        label: 'Sell'
      };
    case 'SERVICES':
      return {
        colorClasses: {
          bg: 'bg-purple-500/10',
          border: 'border-purple-500/20',
          text: 'text-purple-500/80'
        },
        icon: 'tools',
        label: 'Services'
      };
    case 'HIRING':
      return {
        colorClasses: {
          bg: 'bg-orange-500/10',
          border: 'border-orange-500/20',
          text: 'text-orange-500/80'
        },
        icon: 'briefcase',
        label: 'Hiring'
      };
    case 'EVENTS':
      return {
        colorClasses: {
          bg: 'bg-pink-500/10',
          border: 'border-pink-500/20',
          text: 'text-pink-500/80'
        },
        icon: 'calendar',
        label: 'Events'
      };
    default:
      return {
        colorClasses: {
          bg: 'bg-yellow-500/10',
          border: 'border-yellow-500/20',
          text: 'text-yellow-500/80'
        },
        icon: 'circle',
        label: category
      };
  }
};
