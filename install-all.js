const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// ANSI color codes
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    red: '\x1b[31m'
};

// Resources with package.json files
const resourcesWithPackages = [
    'hm-admin',
    'hm-banking',
    'hm-core',
    'hm-doorlock',
    'hm-events',
    'hm-garages',
    'hm-hud',
    'hm-inventory',
    'hm-multicharacter',
    'hm-phone',
    'hm-polyzones',
    'hm-target',
    'hm-weapons'
];

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function installDependencies() {
    const startTime = performance.now();
    log('\n🔧 Installing dependencies for all HM Framework resources', 'cyan');
    log('=========================================================', 'cyan');

    let successful = 0;
    let failed = 0;

    for (const resourceName of resourcesWithPackages) {
        const resourcePath = path.resolve(__dirname, resourceName);
        
        if (!fs.existsSync(resourcePath) || !fs.existsSync(path.join(resourcePath, 'package.json'))) {
            log(`⚠️  ${resourceName} not found, skipping...`, 'yellow');
            continue;
        }

        try {
            log(`📦 Installing dependencies for ${resourceName}...`, 'blue');
            
            // Install main dependencies
            execSync('npm ci --silent', {
                cwd: resourcePath,
                stdio: 'pipe'
            });

            // Install UI dependencies if they exist
            const uiPath = path.join(resourcePath, 'ui');
            if (fs.existsSync(uiPath) && fs.existsSync(path.join(uiPath, 'package.json'))) {
                execSync('npm ci --silent', {
                    cwd: uiPath,
                    stdio: 'pipe'
                });
            }

            log(`✅ ${resourceName} dependencies installed`, 'green');
            successful++;
        } catch (error) {
            log(`❌ Failed to install dependencies for ${resourceName}: ${error.message}`, 'red');
            failed++;
        }
    }

    const totalTime = ((performance.now() - startTime) / 1000).toFixed(2);
    
    log('\n📊 Installation Summary', 'cyan');
    log('=======================', 'cyan');
    log(`Total time: ${totalTime}s`);
    log(`Successful: ${successful}`, 'green');
    log(`Failed: ${failed}`, failed > 0 ? 'red' : 'reset');

    if (failed > 0) {
        log('\n💥 Some installations failed!', 'red');
        process.exit(1);
    } else {
        log('\n🎉 All dependencies installed successfully!', 'green');
    }
}

installDependencies().catch(error => {
    log(`Fatal error: ${error.message}`, 'red');
    process.exit(1);
});
