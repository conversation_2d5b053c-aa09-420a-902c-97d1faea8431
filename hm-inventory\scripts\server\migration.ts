// Migration utility for converting existing inventory data to optimized format

import { ItemSlot, ItemSlotStorage, InventoryItemInstance } from '@shared';

/**
 * Converts a full ItemSlot with definition data to optimized storage format
 */
export function convertToStorageFormat(slot: ItemSlot): ItemSlotStorage {
    if (!slot.item) {
        return {
            index: slot.index,
            slotType: slot.slotType
        };
    }

    const storageItem: InventoryItemInstance = {
        instanceId: slot.item.instanceId,
        definitionId: slot.item.definitionId,
        quantity: slot.item.quantity,
        currentDurability: slot.item.currentDurability,
        metadata: slot.item.metadata || {}
    };

    return {
        index: slot.index,
        item: storageItem,
        slotType: slot.slotType
    };
}

/**
 * Migration script to convert all existing player inventories to optimized format
 */
export async function migrateAllInventories(): Promise<void> {
    try {
        console.log('[hm-inventory] Starting inventory data migration...');
        
        // Get all player inventories
        const result = await global.exports.oxmysql.query_async(
            'SELECT identifier, stateid, items FROM player_inventories'
        );

        if (!result || result.length === 0) {
            console.log('[hm-inventory] No inventories found to migrate');
            return;
        }

        let migratedCount = 0;
        let alreadyOptimized = 0;

        for (const row of result) {
            try {
                const items = JSON.parse(row.items);
                
                // Check if already in optimized format
                if (items.length > 0 && items[0].item && 
                    !Object.prototype.hasOwnProperty.call(items[0].item, 'name') &&
                    Object.prototype.hasOwnProperty.call(items[0].item, 'instanceId')) {
                    alreadyOptimized++;
                    continue;
                }

                // Convert to optimized format
                const optimizedItems = items.map((slot: ItemSlot) => convertToStorageFormat(slot));
                
                // Update database
                await global.exports.oxmysql.query_async(
                    'UPDATE player_inventories SET items = ? WHERE identifier = ? AND stateid = ?',
                    [JSON.stringify(optimizedItems), row.identifier, row.stateid]
                );

                migratedCount++;
                console.log(`[hm-inventory] Migrated inventory for ${row.identifier} (${row.stateid})`);
                
            } catch (error) {
                console.error(`[hm-inventory] Error migrating inventory for ${row.identifier}: ${error}`);
            }
        }

        console.log(`[hm-inventory] Migration complete: ${migratedCount} inventories migrated, ${alreadyOptimized} already optimized`);
        
    } catch (error) {
        console.error(`[hm-inventory] Migration failed: ${error}`);
    }
}

// Command to trigger migration
RegisterCommand('hm-inventory:migrate', async () => {
    await migrateAllInventories();
}, true); // true = restricted to console/admin
