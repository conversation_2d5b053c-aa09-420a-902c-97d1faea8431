import { useNavigation } from '../../navigation/hooks';
import { useNotificationBadges } from '../../notifications/hooks/notificationHooks';
import { useAppStore } from '../stores/appStore';
import { App } from '../types/appTypes';

const AppGrid = () => {
  const { openApp } = useNavigation();
  const { installedApps } = useAppStore();
  const notificationBadges = useNotificationBadges();

  const handleAppClick = (app: App) => {
    // Use the path directly without any processing
    openApp(app.path);
  };

  return (
    <div className="flex flex-col h-full w-full app-grid-container" style={{ zIndex: 50 }}>
      <div
        className="grid grid-cols-4 gap-y-4 gap-x-1 p-2"
        style={{ zIndex: 50, position: 'relative' }}
      >
        {installedApps.length === 0 && (
          <div className="col-span-4 text-center p-4 bg-red-500 text-white rounded-lg">
            No apps found!
          </div>
        )}
        {installedApps.map(app => (
          <button
            key={app.id}
            onClick={() => handleAppClick(app)}
            className="flex flex-col items-center gap-2 group relative w-16"
          >
            <div
              className={`w-14 h-14
                ${app.colors.bg}
                rounded-2xl flex items-center justify-center
                shadow-lg backdrop-blur-sm
                group-hover:scale-105 group-hover:shadow-xl
                transition-all duration-200 relative
                before:absolute before:inset-0 before:rounded-2xl
                before:bg-white before:opacity-0
                group-hover:before:opacity-100 before:transition-opacity
                border border-white/20`}
            >
              {/* Use a predefined set of icon classes for Tailwind v4 compatibility */}
              <i className={`fas fa-${app.icon} text-white text-xl relative z-10`}></i>

              {/* Notification Badge */}
              {notificationBadges[app.id] > 0 && (
                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-5 h-5 flex items-center justify-center px-1 border border-black/10 shadow-sm z-20">
                  {notificationBadges[app.id] > 99 ? '99+' : notificationBadges[app.id]}
                </div>
              )}
            </div>
            <span className="text-[11px] text-white font-medium drop-shadow-sm">{app.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default AppGrid;
