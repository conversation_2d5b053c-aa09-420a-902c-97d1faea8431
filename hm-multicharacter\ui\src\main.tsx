import { createRoot } from 'react-dom/client';
import { useState, useEffect } from 'react';
import { useMulticharacterNui } from './hooks/useMulticharacterNui';
import { CharacterSelectionData, CharacterCreationData } from '../../scripts/shared/character.types';
import { CharacterCard } from './components/CharacterCard';
import { CharacterCreationForm } from './components/CharacterCreationForm';
import './index.css';

// Main App component using NUI hook
const App = () => {
  // State for UI components
  const [showNewCharacterForm, setShowNewCharacterForm] = useState(false);
  const [activeCharacter, setActiveCharacter] = useState<string | null>(null);
  const [uiError, setUiError] = useState<string | null>(null);

  // Get all NUI state and handlers from our custom hook
  const { characters, error, visible, select<PERSON>haracter, deleteCharacter, createCharacter, playCharacter, hoverCharacter, hoverCharacterEnd } = useMulticharacterNui();

  // We don't need a separate NUI message handler for visibility
  // since useMulticharacterNui already handles all messages

  useEffect(() => {
    if (characters.length > 0) {
      setUiError(null);
    }
  }, [characters]);

  // Debug render state
  console.log(`UI: Render called, visible=${visible}, characters=${characters.length}`);

  return (
    <div
      className="w-full h-full"
      style={{
        backgroundColor: 'transparent',
        background: 'none',
        display: visible ? 'block' : 'none',
        position: 'absolute',
        top: 0,
        left: 0,
        zIndex: visible ? 1 : -1,
        pointerEvents: visible ? 'auto' : 'none'
      }}
    >
      {/* Character Creation Form - Centered */}
      {showNewCharacterForm && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="w-[500px] bg-[#111827]/95 rounded-3xl shadow-2xl overflow-hidden">
            <CharacterCreationForm
              onCancel={() => setShowNewCharacterForm(false)}
              onSubmit={(data: CharacterCreationData) => {
                // Clear any previous errors
                setUiError(null);

                // Validate date of birth is not empty
                if (!data.birthdate || isNaN(new Date(data.birthdate).getTime())) {
                  setUiError("Date of birth is required");
                  return;
                }

                // Create character and close form
                createCharacter(data);
                setShowNewCharacterForm(false);
              }}
            />
          </div>
        </div>
      )}

      {/* Right-side Character Selection Panel */}
      <div className="absolute right-8 top-0 bottom-0 w-[350px] flex flex-col justify-center">
        {/* Header Card */}
        <div className="mb-6 bg-[#111827]/90 rounded-2xl p-5 shadow-lg flex justify-between">
          <h2 className="text-xl font-bold text-white">Select Character</h2>
          <span className="px-3 py-1 rounded-full bg-[#1f2937]/90 text-white text-sm">
            {characters.length}/5
          </span>
        </div>

        {/* Character List */}
        <div className="space-y-4 pr-2 max-h-[70vh] overflow-y-auto scrollbar-thumb-[#3b82f6]/20">
          {characters.map((char: CharacterSelectionData, i: number) => (
            <div key={char.stateid} style={{ animationDelay: `${i * 100}ms` }} className="animate-fadeIn">
              <CharacterCard
                character={char}
                spawnPointIndex={i}
                isActive={activeCharacter === char.stateid}
                onSelect={(stateid: string) => { setActiveCharacter(stateid); selectCharacter(stateid); }}
                onDelete={(stateid: string) => deleteCharacter(stateid)}
                onHover={(stateid: string, spawnPointIndex: number) => hoverCharacter(stateid, spawnPointIndex)}
                onHoverEnd={() => hoverCharacterEnd()}
              />
            </div>
          ))}
        </div>

        {/* Create New Character Button */}
        {characters.length < 5 && (
          <button
            onClick={() => setShowNewCharacterForm(true)}
            className="mt-6 w-full py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl"
          >Create New Character</button>
        )}

        {/* Play Button */}
        <button
          onClick={() => { if (activeCharacter) playCharacter(activeCharacter); }}
          disabled={!activeCharacter}
          className={`mt-6 w-full py-4 rounded-2xl text-lg font-bold shadow-lg ${activeCharacter ? 'bg-gradient-to-r from-blue-500 to-purple-500' : 'bg-gray-700 text-gray-400 cursor-not-allowed'}`}
        >Play</button>

        {/* Error Messages */}
        {error && <div className="mt-4 text-red-500">Server Error: {error}</div>}
        {uiError && <div className="mt-4 text-red-500">Error: {uiError}</div>}
      </div>
    </div>
  );
};

// Initialize the application
const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Failed to find the root element');
}

try {
  const root = createRoot(rootElement);
  root.render(<App />);
} catch (error) {
  console.error('Error rendering the application:', error);
}