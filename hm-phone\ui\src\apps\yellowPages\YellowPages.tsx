import React, { useState } from 'react';
import AdCard from './components/AdCard';
import AdForm from './components/AdForm';
import { useYellowPagesStore } from './stores/yellowPagesStore';
import { useNavigation } from '../../navigation/hooks';
import { useNavigationStore } from '../../navigation/navigationStore';
import LoadingSpinner from '../../common/components/LoadingSpinner';

const YellowPages: React.FC = () => {
  const { goBack, openView } = useNavigation();
  const { currentView } = useNavigationStore.getState();
  const { ads, loading, error } = useYellowPagesStore();
  const { searchAds } = useYellowPagesStore().actions;
  const [searchQuery, setSearchQuery] = useState('');

  const filteredAds = searchQuery ? searchAds(searchQuery) : ads;

  return (
    <div className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8 pb-3">
      {(currentView === 'main' || !currentView) && (
        <div className="flex items-center gap-4 p-4 bg-[#1a1a1a] border-b border-gray-800">
          <button onClick={goBack} className="text-white/80 hover:text-white cursor-pointer">
            <i className="fas fa-arrow-left text-lg"></i>
          </button>
          <div className="flex-1">
            <input
              type="search"
              placeholder="Search listings..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 bg-[#2a2a2a] rounded-full text-sm text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 cursor-text"
            />
          </div>
          <button
            onClick={() => openView('form')}
            className="w-8 h-8 bg-yellow-500/10 rounded-full flex items-center justify-center hover:bg-yellow-500/20 transition-colors cursor-pointer"
          >
            <i className="fas fa-plus text-yellow-500"></i>
          </button>
        </div>
      )}

      {currentView === 'form' ? (
        <AdForm onClose={() => openView('main')} />
      ) : (
        <div className="flex-1 overflow-y-auto px-4 py-2 bg-black">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full text-red-500 text-center p-4">
              <div>
                <i className="fas fa-exclamation-circle text-2xl mb-2"></i>
                <p>{error}</p>
                <button
                  onClick={() => useYellowPagesStore.getState().actions.loadAds()}
                  className="mt-4 px-4 py-2 bg-yellow-500/20 text-yellow-500 rounded-lg hover:bg-yellow-500/30"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : filteredAds.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500 text-center p-4">
              <div>
                {searchQuery ? (
                  <>
                    <i className="fas fa-search text-2xl mb-2"></i>
                    <p>No ads found matching "{searchQuery}"</p>
                  </>
                ) : (
                  <>
                    <i className="fas fa-newspaper text-2xl mb-2"></i>
                    <p>No ads available</p>
                    <p className="text-sm mt-1">Be the first to post an ad!</p>
                  </>
                )}
              </div>
            </div>
          ) : (
            filteredAds.map(ad => <AdCard key={ad.id} ad={ad} />)
          )}
        </div>
      )}
    </div>
  );
};

export default YellowPages;
