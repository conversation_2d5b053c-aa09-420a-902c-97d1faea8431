import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';

interface ImageModalProps {
  isVisible: boolean;
  onClose: () => void;
  imageUrl: string;
  onDelete?: () => void;
  metadata?: {
    size?: string;
    timestamp?: string;
    dimensions?: string;
  };
}

const ImageModal: React.FC<ImageModalProps> = ({
  isVisible,
  onClose,
  imageUrl,
  onDelete,
  metadata
}) => {
  const [isZoomed, setIsZoomed] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => setLoading(false);
  }, [imageUrl]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, [isVisible, onClose]);

  const modalContent = (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/95"
          onClick={e => e.target === e.currentTarget && onClose()}
        >
          {/* Main Content */}
          <div className="relative w-full h-full flex flex-col items-center justify-center p-4">
            {/* Loading Indicator */}
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-10 h-10 border-3 border-white/20 border-t-white/90 rounded-full animate-spin"></div>
              </div>
            )}

            {/* Image Container with Controls */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: 'spring', duration: 0.5 }}
              className="relative max-w-[90vw] max-h-[90vh] group"
            >
              {/* Image */}
              <img
                src={imageUrl}
                alt="Full size"
                className={`max-w-full max-h-[90vh] object-contain ${
                  isZoomed ? 'cursor-zoom-out scale-150' : 'cursor-zoom-in scale-100'
                }`}
                onClick={() => setIsZoomed(!isZoomed)}
                style={{
                  transition: 'transform 0.3s ease',
                  // Show the full image without any cropping in the modal view
                  clipPath: 'none'
                }}
              />

              {/* Controls Overlay */}
              <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button
                  onClick={e => {
                    e.stopPropagation();
                    setIsZoomed(!isZoomed);
                  }}
                  className="w-10 h-10 rounded-full bg-black/50 hover:bg-black/70 transition-colors flex items-center justify-center backdrop-blur-sm"
                >
                  <i
                    className={`fas fa-${isZoomed ? 'search-minus' : 'search-plus'} text-white/90`}
                  ></i>
                </button>
                {onDelete && (
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      onDelete();
                    }}
                    className="w-10 h-10 rounded-full bg-red-500/50 hover:bg-red-500/70 transition-colors flex items-center justify-center backdrop-blur-sm"
                  >
                    <i className="fas fa-trash text-white/90"></i>
                  </button>
                )}
                <button
                  onClick={e => {
                    e.stopPropagation();
                    onClose();
                  }}
                  className="w-10 h-10 rounded-full bg-black/50 hover:bg-black/70 transition-colors flex items-center justify-center backdrop-blur-sm"
                >
                  <i className="fas fa-times text-white/90"></i>
                </button>
              </div>

              {/* Image Info */}
              <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                {metadata?.size && (
                  <div className="text-sm text-white/80 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full">
                    {metadata.size}
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return createPortal(modalContent, document.body);
};

export default ImageModal;
