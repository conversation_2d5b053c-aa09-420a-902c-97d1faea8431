import React, { useState } from 'react';
import { useNavigation } from '../../navigation/hooks';
import { useNavigationStore } from '../../navigation/navigationStore';
import { useDarkMarketStore } from './stores/darkMarketStore';
import { motion } from 'framer-motion';

const DarkMarket: React.FC = () => {
  const { goBack, switchTab } = useNavigation();
  const { currentTab } = useNavigationStore();
  const { listings, searchListings } = useDarkMarketStore();
  const [searchQuery, setSearchQuery] = useState('');
  const selectedCategory = currentTab || 'all';

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'tech', label: 'Tech' },
    { id: 'data', label: 'Data' },
    { id: 'cyber', label: 'Cyber' }
  ];

  const filteredListings = searchQuery ? searchListings(searchQuery) : listings;

  return (
    <div className="h-full w-full flex flex-col bg-zinc-900 pt-8 pb-3">
      {/* Header */}
      <div className="px-4 py-3 border-b border-zinc-800">
        <div className="flex items-center justify-between">
          <button onClick={goBack} className="text-zinc-400 hover:text-zinc-200">
            <i className="fas fa-arrow-left text-lg"></i>
          </button>
          <div className="text-zinc-200 font-medium text-lg">Dark Market</div>
          <div className="w-8" /> {/* Spacer for alignment */}
        </div>
      </div>

      {/* Search */}
      <div className="px-4 py-3">
        <div className="relative">
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-zinc-400">
            <i className="fas fa-search"></i>
          </div>
          <input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="w-full bg-zinc-800 text-zinc-200
								 pl-10 pr-4 py-2 rounded-lg
								 placeholder-zinc-500
								 focus:outline-none focus:ring-2 focus:ring-zinc-700"
          />
        </div>
      </div>

      {/* Category Tabs */}
      <div className="px-4 py-2 flex gap-2 overflow-x-auto scrollbar-hide">
        {categories.map(cat => (
          <button
            key={cat.id}
            onClick={() => switchTab(cat.id)}
            className={`px-4 py-1.5 rounded-full text-sm
								 ${
                   selectedCategory === cat.id
                     ? 'bg-zinc-700 text-zinc-200'
                     : 'text-zinc-400 hover:text-zinc-300'
                 }`}
          >
            {cat.label}
          </button>
        ))}
      </div>

      {/* Listings Grid */}
      <div className="flex-1 overflow-y-auto px-4 py-2">
        <div className="space-y-4">
          {filteredListings.map(listing => (
            <motion.div
              key={listing.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-zinc-800 rounded-lg overflow-hidden"
            >
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-zinc-200 font-medium">{listing.title}</div>
                    <div className="text-zinc-400 text-sm mt-1">{listing.description}</div>
                  </div>
                  <div className="bg-zinc-700 px-3 py-1 rounded-full text-zinc-200">
                    {listing.price} ¢
                  </div>
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-zinc-400 text-sm">{listing.seller}</span>
                  </div>
                  <button className="text-zinc-200 hover:text-zinc-400 text-sm bg-zinc-700 px-3 py-1 rounded-full">
                    Connect
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Status Bar */}
      <div className="px-4 py-2 border-t border-zinc-800">
        <div className="flex justify-between text-xs text-zinc-500">
          <span>Connected</span>
          <span>Encrypted</span>
        </div>
      </div>
    </div>
  );
};

export default DarkMarket;
