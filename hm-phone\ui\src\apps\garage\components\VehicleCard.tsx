import React from 'react';
import { motion } from 'framer-motion';
import { Vehicle } from '../types/garageTypes';
import { getCategoryIcon, getStatusColorClass } from '../utils/garageUtils';

interface VehicleCardProps {
  vehicle: Vehicle;
  isSelected: boolean;
  onSelect: (id: number) => void;
  onTrack: (id: number) => void;
  onToggleEngine: (id: number) => void;
  onToggleLock: (id: number) => void;
}

const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  // isSelected is not used in this component but is part of the props interface
  // for potential future use
  onSelect,
  onTrack
}) => {
  // Determine if vehicle is financed
  const isFinanced = vehicle.balance && vehicle.balance > 0;

  return (
    <motion.div
      layoutId={`vehicle-${vehicle.id}`}
      onClick={() => onSelect(vehicle.id)}
      className="relative"
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2 }}
    >
      {/* Glassmorphic card with improved layout */}
      <div className="rounded-xl overflow-hidden backdrop-blur-md bg-white/10 border border-white/20">
        {/* Two-column layout for better space utilization */}
        <div className="flex p-4">
          {/* Left column with vehicle info */}
          <div className="flex-1 pr-3">
            {/* Vehicle name and brand */}
            <div className="mb-3">
              <h3 className="text-white font-medium text-lg leading-tight mb-1">{vehicle.name}</h3>
              <p className="text-white/70 text-sm">{vehicle.brand}</p>
            </div>

            {/* Vehicle details */}
            <div className="space-y-2">
              {/* Location */}
              <div className="flex items-center">
                <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                  <i className="fas fa-map-marker-alt text-red-400 text-sm"></i>
                </div>
                <div className="text-white/80 text-sm line-clamp-1">
                  {vehicle.location || vehicle.garage}
                </div>
              </div>

              {/* Category */}
              <div className="flex items-center">
                <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                  <i className={`fas ${getCategoryIcon(vehicle.category || '')} text-sm`}></i>
                </div>
                <div className="text-white/80 text-sm capitalize">
                  {vehicle.category}
                </div>
              </div>

              {/* License plate */}
              <div className="flex items-center">
                <div className="w-7 h-7 rounded-full bg-white/10 flex items-center justify-center mr-2 flex-shrink-0">
                  <i className="fas fa-id-card text-yellow-400 text-sm"></i>
                </div>
                <div className="bg-white/10 px-2 py-0.5 rounded text-white/90 text-sm font-mono">
                  {vehicle.licensePlate || vehicle.plate}
                </div>
              </div>
            </div>
          </div>

          {/* Right column with status and actions */}
          <div className="flex flex-col justify-between items-end ml-2 w-24">
            {/* Status badge */}
            <div
              className={`px-3 py-1 rounded-full text-xs text-center w-full ${getStatusColorClass(vehicle.status || '')}`}
            >
              {vehicle.status}
            </div>

            {/* Fuel indicator */}
            <div className="flex flex-col items-center w-full bg-white/10 rounded-lg p-2 my-2">
              <i className="fas fa-gas-pump text-green-400 mb-1"></i>
              <div className="text-white/80 text-sm font-medium">{vehicle.fuel}%</div>
              <div className="w-full bg-white/10 rounded-full h-1 mt-1">
                <div
                  className="bg-green-500 h-1 rounded-full"
                  style={{ width: `${vehicle.fuel}%` }}
                ></div>
              </div>
            </div>

            {/* Finance or track button */}
            {isFinanced ? (
              <div className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-lg text-xs flex items-center w-full justify-center">
                <i className="fas fa-money-bill-wave mr-1"></i>
                <span>${vehicle.balance?.toLocaleString()}</span>
              </div>
            ) : (
              <button
                className="bg-white/10 hover:bg-white/20 text-white text-xs px-2 py-1 rounded-lg transition-colors w-full"
                onClick={e => {
                  e.stopPropagation();
                  onTrack(vehicle.id);
                }}
              >
                <i className="fas fa-location-arrow mr-1"></i>
                Track
              </button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default VehicleCard;
