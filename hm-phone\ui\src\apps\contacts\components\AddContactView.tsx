import React, { useState } from 'react';
import { motion } from 'framer-motion';
// import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { Contact } from '@shared/types';

interface AddContactViewProps {
  onAddContact: (contact: Contact) => void;
  isSubmitting?: boolean;
  onCancel: () => void;
  initialData?: {
    phone?: string;
    name?: string;
  };
}

const AddContactView: React.FC<AddContactViewProps> = ({
  onAddContact,
  isSubmitting: externalSubmitting,
  onCancel,
  initialData
}) => {
  const [name, setName] = useState(initialData?.name || '');
  const [phone, setPhone] = useState(initialData?.phone || '');
  const [favorite, setFavorite] = useState(false);
  const [photo, setPhoto] = useState('');
  const [internalSubmitting, setInternalSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Use either external or internal submitting state
  const isSubmitting = externalSubmitting || internalSubmitting;
  // const userPhoneNumber = usePhoneStore.getState().userProfile?.phoneNumber || '';

  const validatePhone = (phone: string) => {
    // Basic phone validation - can be adjusted based on your needs
    const phoneRegex = /^\+?[\d\s-]{6,}$/;
    return phoneRegex.test(phone.trim());
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    // Validate form
    if (!name.trim()) {
      setError('Please enter a name');
      return;
    }

    if (!phone.trim()) {
      setError('Please enter a phone number');
      return;
    }

    if (!validatePhone(phone)) {
      setError('Please enter a valid phone number');
      return;
    }

    // Set submitting state
    setInternalSubmitting(true);

    // Get the user's phone number from the store
    // Create new contact with proper fields
    const newContact: Contact = {
      id: Date.now(),
      identifier: '', // Will be set by the server
      stateid: '', // Will be set by the server
      number: phone.trim(),
      name: name.trim(),
      favorite: favorite ? 1 : 0,
      avatar: photo.trim() || null,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
    };

    try {
      // Call the onAddContact callback
      onAddContact(newContact);

      // Reset form
      setName('');
      setPhone('');
      setFavorite(false);
      setPhoto('');
      setError('');
    } catch (error) {
      console.error('Error adding contact:', error);
      setError('Failed to add contact. Please try again.');
    } finally {
      // Reset submitting state after a delay
      setTimeout(() => {
        setInternalSubmitting(false);
      }, 1000);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-white/10">
        <button onClick={onCancel} className="text-white/80 hover:text-white">
          <i className="fas fa-arrow-left" />
        </button>
        <h2 className="text-white font-medium">New Contact</h2>
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="text-green-500 hover:text-green-400 disabled:opacity-50"
        >
          {isSubmitting ? 'Adding...' : 'Add'}
        </button>
      </div>

      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {error && <div className="bg-red-500/20 text-red-100 px-4 py-2 rounded">{error}</div>}

        {/* Profile Photo */}
        <div className="flex flex-col items-center mb-6">
          <div className="relative w-24 h-24 rounded-full overflow-hidden mb-2">
            {photo ? (
              <div
                className="w-full h-full bg-cover bg-center"
                style={{ backgroundImage: `url(${photo})` }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-2xl font-medium">
                  {name ? name.charAt(0).toUpperCase() : '?'}
                </span>
              </div>
            )}
            <button
              className="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-green-500 flex items-center justify-center shadow-lg"
              onClick={() => {
                // In a real implementation, this would open a photo picker
                const randomId = Math.floor(Math.random() * 70);
                setPhoto(`https://i.pravatar.cc/300?img=${randomId}`);
              }}
            >
              <i className="fas fa-camera text-white text-sm"></i>
            </button>
          </div>
        </div>

        {/* Name Field */}
        <div className="space-y-2">
          <label className="text-white/60 text-sm">Name</label>
          <input
            type="text"
            value={name}
            onChange={e => setName(e.target.value)}
            className="w-full bg-white/10 text-white rounded-lg px-4 py-2
                     focus:outline-none focus:ring-2 focus:ring-white/20"
            placeholder="Enter name"
          />
        </div>

        {/* Phone Field */}
        <div className="space-y-2">
          <label className="text-white/60 text-sm">Phone</label>
          <input
            type="tel"
            value={phone}
            onChange={e => setPhone(e.target.value)}
            className="w-full bg-white/10 text-white rounded-lg px-4 py-2
                     focus:outline-none focus:ring-2 focus:ring-white/20"
            placeholder="Enter phone number"
          />
        </div>

        {/* Favorite Toggle */}
        <div className="flex items-center justify-between py-2">
          <span className="text-white/60">Add to Favorites</span>
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setFavorite(!favorite)}
            className="text-2xl"
          >
            <i className={`fas fa-star ${favorite ? 'text-yellow-400' : 'text-white/20'}`} />
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default AddContactView;
