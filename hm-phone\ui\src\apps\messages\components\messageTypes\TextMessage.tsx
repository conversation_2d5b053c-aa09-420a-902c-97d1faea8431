import React from 'react';
import { TextMessageType } from '../../../../../../shared/types';

// Extended TextMessageType with UI-specific properties
interface UITextMessage extends Omit<TextMessageType, 'content'> {
  message: string;
  isEdited?: boolean;
  timestamp?: string;
}

interface TextMessageProps {
  message: UITextMessage;
  isEditing: boolean;
  editedContent: string;
  editInputRef: React.RefObject<HTMLInputElement>;
  setEditedContent: (content: string) => void;
  handleEditSubmit: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

export const TextMessage: React.FC<TextMessageProps> = ({
  message,
  isEditing,
  editedContent,
  editInputRef,
  setEditedContent,
  handleEditSubmit
}) => (
  <div className="px-2 py-2">
    {isEditing ? (
      <input
        ref={editInputRef}
        type="text"
        value={editedContent}
        onChange={e => setEditedContent(e.target.value)}
        onKeyDown={handleEditSubmit}
        className="w-full bg-transparent text-[14px] focus:outline-none"
      />
    ) : (
      <div className="break-words overflow-hidden overflow-wrap-anywhere whitespace-pre-wrap text-[14px]">
        {message.message}
        {message.isEdited && (
          <span className="inline-flex items-center group relative">
            <span className="ml-1 text-[11px] text-white/60 hover:text-white/80 transition-colors cursor-help">
              (edited)
            </span>
            <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 bg-black/80 text-xs text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Message was edited
            </span>
          </span>
        )}
      </div>
    )}
    <div className="flex justify-end items-center gap-1 mt-1">
      <span className="text-[9px] text-white/50">
        {new Date(
          message.timestamp ||
          (message.metadata && typeof message.metadata === 'object' && 'timestamp' in message.metadata
            ? (message.metadata as { timestamp: number }).timestamp
            : Date.now())
        ).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })}
      </span>
    </div>
  </div>
);
