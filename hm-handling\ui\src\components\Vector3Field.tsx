import React from 'react';
import { Vector3, HANDLING_FIELD_LIMITS } from '../../../scripts/shared/types';
import { isVector3InBounds } from '../utils/fieldUtils';

interface Vector3FieldProps {
  field: string;
  currentValue: Vector3;
  stockValue: Vector3;
  onValueChange: (field: string, value: Vector3) => void;
  onReset: (field: string) => void;
}

const Vector3Field: React.FC<Vector3FieldProps> = ({
  field,
  currentValue,
  stockValue,
  onValueChange,
  onReset
}) => {
  const formatFieldName = (fieldName: string): string => {
    return fieldName;
  };

  // Get field limits (Vector3 fields use the same limits for x, y, z)
  const limits = HANDLING_FIELD_LIMITS[field as keyof typeof HANDLING_FIELD_LIMITS];
  const { min, max, step = 0.01 } = limits || {};

  // Provide default values to prevent undefined errors
  const safeCurrentValue = currentValue || { x: 0, y: 0, z: 0 };
  const safeStockValue = stockValue || { x: 0, y: 0, z: 0 };

  const isChanged = safeCurrentValue.x !== safeStockValue.x || 
                   safeCurrentValue.y !== safeStockValue.y || 
                   safeCurrentValue.z !== safeStockValue.z;

  const isValidValue = (value: number) => value !== undefined && value !== null && !isNaN(value);
  
  // Check if any component is out of bounds
  const isOutOfBounds = currentValue && !isVector3InBounds(field, currentValue);
  const handleInputChange = (component: 'x' | 'y' | 'z', value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      onValueChange(field, {
        ...safeCurrentValue,
        [component]: numValue
      });
    }
  };

  const handleResetClick = () => {
    onReset(field);
  };

  const handleConfirmClick = () => {
    // For now, this could be used to apply changes or trigger validation
    // In the future, this could trigger server sync or other confirmation actions
    console.log(`Confirmed changes for ${field}:`, safeCurrentValue);
  };  return (
    <div className={`handling-field-compact vector3-field ${isChanged ? 'changed' : ''} ${isOutOfBounds ? 'out-of-bounds' : ''}`}>
      <button
        className={`reset-btn ${isChanged ? 'active' : ''}`}
        onClick={handleResetClick}
        disabled={!isChanged}
        title="Reset to stock value"
      >
        ↻
      </button>
      
      <span className="field-name">{formatFieldName(field)}</span>
      
      <span className="stock-value vector3-stock">
        {isValidValue(safeStockValue.x) ? safeStockValue.x.toFixed(3) : 'N/A'} | {isValidValue(safeStockValue.y) ? safeStockValue.y.toFixed(3) : 'N/A'} | {isValidValue(safeStockValue.z) ? safeStockValue.z.toFixed(3) : 'N/A'}
      </span>

      <div className="vector3-inputs">
        <input
          type="number"
          step={step}
          min={min}
          max={max}
          className="current-value-input vector3-x"
          value={isValidValue(safeCurrentValue.x) ? safeCurrentValue.x : ''}
          onChange={(e) => handleInputChange('x', e.target.value)}
          placeholder="X"
          title={`X component - ${limits ? `Min: ${min ?? 'none'}, Max: ${max ?? 'none'}` : 'No limits'}`}
        />
        <input
          type="number"
          step={step}
          min={min}
          max={max}
          className="current-value-input vector3-y"
          value={isValidValue(safeCurrentValue.y) ? safeCurrentValue.y : ''}
          onChange={(e) => handleInputChange('y', e.target.value)}
          placeholder="Y"
          title={`Y component - ${limits ? `Min: ${min ?? 'none'}, Max: ${max ?? 'none'}` : 'No limits'}`}
        />
        <input
          type="number"
          step={step}
          min={min}
          max={max}
          className="current-value-input vector3-z"
          value={isValidValue(safeCurrentValue.z) ? safeCurrentValue.z : ''}
          onChange={(e) => handleInputChange('z', e.target.value)}
          placeholder="Z"
          title={`Z component - ${limits ? `Min: ${min ?? 'none'}, Max: ${max ?? 'none'}` : 'No limits'}`}
        />
      </div>

      <button
        className={`confirm-btn ${isChanged ? 'active' : ''}`}
        onClick={handleConfirmClick}
        disabled={!isChanged}
        title="Confirm changes"
      >
        ✓
      </button>
    </div>
  );
};

export default Vector3Field;
