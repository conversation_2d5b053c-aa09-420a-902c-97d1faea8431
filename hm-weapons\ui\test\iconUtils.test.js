// Test icon utilities
import { getModIconPath, isFontAwesomeIcon } from '../src/utils/iconUtils';

// Test cases
const testCases = [
  { input: 'fa-cog', expected: 'fa-cog', description: 'FontAwesome icon' },
  { input: 'W_AR_AssaultRifle_Flash.png', expected: './W_AR_AssaultRifle_Flash.png', description: 'Weapon PNG file' },
  { input: 'https://example.com/icon.png', expected: 'https://example.com/icon.png', description: 'External URL' },
  { input: '/absolute/path/icon.png', expected: '/absolute/path/icon.png', description: 'Absolute path' },
  { input: undefined, expected: '', description: 'Undefined icon' },
  { input: 'weapon_pistol', expected: 'weapon_pistol', description: 'Non-PNG string' }
];

console.log('Testing icon utility functions:');
testCases.forEach((test, index) => {
  const result = getModIconPath(test.input);
  const isFA = isFontAwesomeIcon(test.input);
  const passed = result === test.expected;
  
  console.log(`Test ${index + 1}: ${test.description}`);
  console.log(`  Input: "${test.input}"`);
  console.log(`  Expected: "${test.expected}"`);
  console.log(`  Got: "${result}"`);
  console.log(`  FontAwesome: ${isFA}`);
  console.log(`  Status: ${passed ? 'PASS' : 'FAIL'}`);
  console.log('');
});
