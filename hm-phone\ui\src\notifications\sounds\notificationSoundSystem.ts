/**
 * Notification sound system
 * Handles playing notification sounds based on notification type and app settings
 */

import { Notification } from '../types/notificationTypes';

// Define sound file paths
const NOTIFICATION_SOUNDS = {
  default: '/sounds/notification-default.mp3',
  message: '/sounds/notification-message.mp3',
  call: '/sounds/notification-call.mp3',
  success: '/sounds/notification-success.mp3',
  warning: '/sounds/notification-warning.mp3',
  error: '/sounds/notification-error.mp3'
};

// Define ringtone file paths
const RINGTONES = {
  default: '/sounds/notification-call.mp3',
  classic: '/sounds/notification-default.mp3',
  digital: '/sounds/notification-message.mp3',
  retro: '/sounds/notification-success.mp3',
  minimal: '/sounds/notification-warning.mp3'
};

// Cache audio elements to avoid creating new ones for each notification
const audioCache: Record<string, HTMLAudioElement> = {};

/**
 * Get the appropriate sound for a notification
 * @param notification Notification to get sound for
 * @param settings Optional notification settings
 * @returns Path to sound file
 */
export const getNotificationSound = (
  notification: Notification,
  settings?: {
    defaultSound?: string;
    appSettings?: Record<number, { sound?: string }>;
  }
): string => {
  // Check if app has a custom sound
  const appSound = settings?.appSettings?.[notification.appId]?.sound;
  if (appSound) return appSound;

  // Use notification type-specific sound or default
  switch (notification.type) {
    case 'message':
      return NOTIFICATION_SOUNDS.message;
    case 'success':
      return NOTIFICATION_SOUNDS.success;
    case 'warning':
      return NOTIFICATION_SOUNDS.warning;
    case 'error':
      return NOTIFICATION_SOUNDS.error;
    default:
      return settings?.defaultSound || NOTIFICATION_SOUNDS.default;
  }
};

/**
 * Play a notification sound
 * @param notification Notification to play sound for
 * @param settings Optional notification settings
 */
export const playNotificationSound = (
  notification: Notification,
  settings?: {
    defaultSound?: string;
    notificationSoundVolume?: number;
    doNotDisturb?: boolean;
    appSettings?: Record<number, { sound?: string; enabled?: boolean }>;
  }
): void => {
  // Skip if notification is silent or do not disturb is enabled
  if (notification.silent || settings?.doNotDisturb) return;

  // Skip if app notifications are disabled
  if (settings?.appSettings?.[notification.appId]?.enabled === false) return;

  // Get the appropriate sound
  const soundPath = getNotificationSound(notification, settings);

  // Create or get cached audio element
  if (!audioCache[soundPath]) {
    audioCache[soundPath] = new Audio(soundPath);
  }

  const audio = audioCache[soundPath];

  // Set volume (0-1 scale)
  audio.volume = (settings?.notificationSoundVolume ?? 100) / 100;

  // Play the sound
  audio.currentTime = 0; // Reset to start
  audio.play().catch(error => {
    console.error(`[NotificationSound] Error playing sound: ${error}`);
  });
};

/**
 * Play a ringtone
 * @param ringtoneId ID of the ringtone to play
 * @param volume Volume level (0-100)
 * @returns The audio element playing the ringtone
 */
export const playRingtone = (
  ringtoneId: string = 'default',
  volume: number = 80
): HTMLAudioElement | null => {
  const ringtone = RINGTONES[ringtoneId as keyof typeof RINGTONES] || RINGTONES.default;

  // Create or get cached audio element
  if (!audioCache[ringtone]) {
    audioCache[ringtone] = new Audio(ringtone);
  }

  const audio = audioCache[ringtone];

  // Set volume (0-1 scale)
  audio.volume = volume / 100;

  // Play the ringtone
  audio.currentTime = 0; // Reset to start
  audio.play().catch(error => {
    console.error(`[RingtoneSound] Error playing ringtone: ${error}`);
    return null;
  });

  return audio;
};

/**
 * Get all available ringtones
 * @returns Object containing all ringtones
 */
export const getRingtones = () => {
  return RINGTONES;
};

/**
 * Preload notification sounds
 */
export const preloadNotificationSounds = (): void => {
  // Preload notification sounds
  Object.values(NOTIFICATION_SOUNDS).forEach(soundPath => {
    if (!audioCache[soundPath]) {
      const audio = new Audio(soundPath);
      audio.preload = 'auto';
      audioCache[soundPath] = audio;
    }
  });

  // Preload ringtones
  Object.values(RINGTONES).forEach(soundPath => {
    if (!audioCache[soundPath]) {
      const audio = new Audio(soundPath);
      audio.preload = 'auto';
      audioCache[soundPath] = audio;
    }
  });
};