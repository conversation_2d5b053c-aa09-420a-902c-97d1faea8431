{"name": "hm-core", "version": "1.0.0", "description": "Core resource for FiveM", "main": "index.js", "engines": {"node": "22.16.0"}, "scripts": {"watch:all": "concurrently -n \"GAME,UI\" -c \"blue,green\" \"npm run watch:game\" \"npm run watch:ui\"", "build:all": "node build.js --production && cd ui && npm run build", "watch:game": "node build.js --watch", "watch:ui": "cd ui && npm run dev", "build:game": "node build.js --production", "build:ui": "cd ui && npm run build", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\""}, "keywords": ["fivem", "phone", "typescript"], "author": "HM", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "concurrently": "^8.2.2", "esbuild": "^0.25.5", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.9.0"}}