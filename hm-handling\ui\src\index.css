@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-image: url('/bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

#root {
  width: 100vw;
  height: 100vh;
}

::-webkit-scrollbar {
  display: none;
}

/* For Firefox */
* {
  scrollbar-width: none;
}

/* For IE and Edge */
* {
  -ms-overflow-style: none;
}

/* Make sure all scrollable areas still work */
.overflow-y-auto, .overflow-x-auto, .overflow-auto {
  -webkit-overflow-scrolling: touch;
}

/* Custom scrollable class */
.scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  height: 100%;
  width: 100%;
  overscroll-behavior: contain;
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

@layer components {
  /* HUD component gradients inspired by inventory design */
  .hud-panel-gradient {
    background: linear-gradient(
      135deg,
      rgba(23, 23, 23, 0.95) 0%,
      rgba(38, 38, 38, 0.9) 50%,
      rgba(23, 23, 23, 0.95) 100%
    );
    border: 1px solid rgba(64, 64, 64, 0.3);
    border-top: 1px solid rgba(16, 185, 129, 0.2);
    transition: all 0.3s ease-in-out;
  }

  .hud-panel-gradient:hover {
    background: linear-gradient(
      135deg,
      rgba(23, 23, 23, 0.98) 0%,
      rgba(38, 38, 38, 0.95) 50%,
      rgba(23, 23, 23, 0.98) 100%
    );
    border-top: 1px solid rgba(16, 185, 129, 0.4);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
  }

  /* Status bar styling matching inventory aesthetic */
  .status-bar-container {
    background: linear-gradient(
      90deg,
      rgba(23, 23, 23, 0.8) 0%,
      rgba(38, 38, 38, 0.6) 50%,
      rgba(23, 23, 23, 0.8) 100%
    );
    border: 1px solid rgba(64, 64, 64, 0.4);
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
  }

  .status-bar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.3) 20%,
      rgba(16, 185, 129, 0.6) 50%,
      rgba(16, 185, 129, 0.3) 80%,
      transparent 100%
    );
  }

  /* Enhanced status block styling */
  .status-block {
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(64, 64, 64, 0.3);
    transition: all 0.2s ease-in-out;
  }

  .status-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
    pointer-events: none;
  }

  /* Status bar fill animations */
  .status-fill {
    position: relative;
    height: 100%;
    transition: width 0.3s ease-in-out;
    overflow: hidden;
  }

  .status-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
    animation: shine 3s ease-in-out infinite;
  }

  /* Vehicle HUD styling */
  .vehicle-hud-panel {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(23, 23, 23, 0.8) 50%,
      rgba(0, 0, 0, 0.9) 100%
    );
    border: 1px solid rgba(64, 64, 64, 0.5);
    border-top: 2px solid rgba(251, 191, 36, 0.6); /* Amber accent like inventory */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  }

  /* Player info panel */
  .player-info-panel {
    background: linear-gradient(
      135deg,
      rgba(23, 23, 23, 0.95) 0%,
      rgba(38, 38, 38, 0.9) 100%
    );
    border: 1px solid rgba(64, 64, 64, 0.4);
    border-left: 3px solid rgba(16, 185, 129, 0.6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  /* Info item styling */
  .info-item {
    transition: all 0.2s ease-in-out;
    padding: 0.5rem;
    border-radius: 0.375rem;
    position: relative;
    overflow: hidden;
  }

  .info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.05) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .info-item:hover::before {
    opacity: 1;
  }

  .info-item:hover {
    background: rgba(64, 64, 64, 0.2);
    transform: translateX(2px);
  }

  /* Voice indicator styling */
  .voice-indicator {
    position: relative;
    overflow: hidden;
  }

  .voice-indicator.talking {
    animation: pulse-green 1s ease-in-out infinite;
  }

  .voice-indicator.talking::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.3) 50%,
      transparent 100%
    );
    animation: shine 1.5s ease-in-out infinite;
  }

  /* Custom animations for vehicle HUD */
  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shine {
    animation: shine 2s infinite;
  }

  /* Utility animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out forwards;
  }
}

/* Pseudo-element utilities */
.before\:absolute::before {
  position: absolute;
  content: "";
}

.before\:inset-0::before {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: "";
}

.before\:rounded-2xl::before {
  border-radius: 1rem;
  content: "";
}

.before\:bg-white::before {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  content: "";
}

.before\:opacity-0::before {
  opacity: 0;
  content: "";
}

.before\:transition-opacity::before {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  content: "";
}


