/**
 * Consolidated Music Store
 * Handles both player state and music data management
 */

import { create } from 'zustand';
import { musicMockData } from '../../../fivem/mockData';
import { clientRequests } from '../../../fivem/clientRequestSender';
import { Album, Artist, MusicStore, Song } from '../types/musicTypes';
import {
  startPlayback,
  stopPlayback,
  getNextSongFromQueue,
  normalizeVolume,
  toggleMuteState
} from '../utils/playbackUtils';

export const useMusicStore = create<MusicStore>((set, get) => {
  return {
    // Player state
    currentSong: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 0.7,
    isMuted: false,
    isRepeatEnabled: false,
    isShuffleEnabled: false,
    queue: [],
    history: [],

    // Data state
    songs: [],
    albums: [],
    artists: [],
    favorites: [],
    loading: false,
    currentArtistId: null,

    // Player controls implementation
    setCurrentSong: (song: Song) => {
      const current = get().currentSong;
      const wasPlaying = get().isPlaying;

      // Stop current playback
      if (wasPlaying) {
        stopPlayback();
      }

      if (current) {
        set(state => ({
          history: [...state.history, current]
        }));
      }

      set({
        currentSong: song,
        isPlaying: true, // Auto-play when a song is selected
        currentTime: 0,
        duration: song.duration
      });

      // Start playback of new song
      startPlayback(
        // Get current state
        () => ({
          currentSong: get().currentSong,
          isPlaying: get().isPlaying,
          currentTime: get().currentTime,
          duration: get().duration,
          isRepeatEnabled: get().isRepeatEnabled
        }),
        // Update state
        updates => set(updates),
        // On song end
        () => get().nextSong()
      );
      console.log('[MusicStore] New song set and playback started');
    },

    togglePlay: () => {
      const state = get();
      const newIsPlaying = !state.isPlaying;

      // Only toggle play if there's a song
      if (!state.currentSong) {
        console.log('[MusicStore] Cannot toggle play - no song selected');
        return;
      }

      console.log(`[MusicStore] Toggling play: ${state.isPlaying} -> ${newIsPlaying}`);
      set({ isPlaying: newIsPlaying });

      // Start or stop playback timer
      if (newIsPlaying) {
        startPlayback(
          // Get current state
          () => ({
            currentSong: get().currentSong,
            isPlaying: get().isPlaying,
            currentTime: get().currentTime,
            duration: get().duration,
            isRepeatEnabled: get().isRepeatEnabled
          }),
          // Update state
          updates => set(updates),
          // On song end
          () => get().nextSong()
        );
      } else {
        stopPlayback();
      }
    },

    nextSong: () => {
      const { queue, currentSong, isRepeatEnabled, isShuffleEnabled } = get();

      if (isRepeatEnabled && currentSong) {
        // Just restart the current song
        set({ currentTime: 0 });
        return;
      }

      if (queue.length > 0) {
        const { nextSong, newQueue } = getNextSongFromQueue(queue, isShuffleEnabled);

        if (nextSong) {
          set(state => ({
            currentSong: nextSong,
            queue: newQueue,
            history: state.currentSong ? [...state.history, state.currentSong] : state.history,
            currentTime: 0,
            duration: nextSong.duration
          }));
        }
      }
    },

    previousSong: () => {
      const { history, currentTime } = get();

      // If current song has played for more than 3 seconds, restart it
      if (currentTime > 3) {
        set({ currentTime: 0 });
        return;
      }

      if (history.length > 0) {
        const previousSong = history[history.length - 1];
        const newHistory = history.slice(0, -1);

        set(state => ({
          currentSong: previousSong,
          history: newHistory,
          queue: state.currentSong ? [state.currentSong, ...state.queue] : state.queue,
          currentTime: 0,
          duration: previousSong.duration
        }));
      }
    },

    seekTo: (time: number) => set({ currentTime: time }),

    setVolume: (volume: number) => {
      const { volume: normalizedVolume, isMuted } = normalizeVolume(volume);
      set({ volume: normalizedVolume, isMuted });
    },

    toggleMute: () => {
      const { isMuted, volume } = get();
      const newState = toggleMuteState(isMuted, volume);
      set(newState);
    },

    toggleRepeat: () =>
      set(state => ({
        isRepeatEnabled: !state.isRepeatEnabled
      })),

    toggleShuffle: () =>
      set(state => ({
        isShuffleEnabled: !state.isShuffleEnabled
      })),

    // Queue management implementation
    addToQueue: (song: Song) =>
      set(state => ({
        queue: [...state.queue, song]
      })),

    removeFromQueue: (songId: number) =>
      set(state => ({
        queue: state.queue.filter(song => song.id !== songId)
      })),

    clearQueue: () => set({ queue: [] }),

    clearCurrentSong: () => {
      // console.log('[MusicStore] Clearing current song');
      stopPlayback();
      set({
        currentSong: null,
        isPlaying: false,
        currentTime: 0,
        duration: 0
      });
    },

    // Data actions implementation
    actions: {
      getSongs: async () => {
        try {
          // Set loading state
          set({ loading: true });

          // Load user profile
          await clientRequests.send(
            'music',
            'getUserProfile',
            {},
            musicMockData.artists.find(artist => artist.isUserProfile),
            get().handlers.onSetUserProfile as (data: unknown) => void
          );

          // Load songs
          await clientRequests.send(
            'music',
            'getSongs',
            {},
            musicMockData.songs,
            get().handlers.onSetSongs as (data: unknown) => void
          );

          // Load albums
          await clientRequests.send(
            'music',
            'getAlbums',
            {},
            musicMockData.albums,
            get().handlers.onSetAlbums as (data: unknown) => void
          );

          // Now load artists, but filter them to only include those with content
          await clientRequests.send('music', 'getArtists', {}, musicMockData.artists, ((
            artists: unknown
          ) => {
            // Filter artists to only include those with content and the user profile
            const { songs, albums } = get();

            // Create sets of artist IDs with content for faster lookup
            const artistsWithSongs = new Set(songs.map(song => song.artistId));
            const artistsWithAlbums = new Set(albums.map(album => album.artistId));

            // Filter artists to only include those with content (including user profile)
            const filteredArtists = (artists as Artist[]).filter(
              artist => artistsWithSongs.has(artist.id) || artistsWithAlbums.has(artist.id)
            );

            get().handlers.onSetArtists(filteredArtists);
          }) as (data: unknown) => void);

          // Finally, load favorites
          set({ favorites: musicMockData.favorites });

          // Set loading state to false
          set({ loading: false });
        } catch (error) {
          console.error('[MusicStore] Error loading music data:', error);
          set({ loading: false });
        }
      },

      getFavorites: async () => {
        try {
          // Just use mock data for favorites since there's no server endpoint
          set({ favorites: musicMockData.favorites });
        } catch (error) {
          console.error('[MusicStore] Error loading favorites:', error);
        }
      },

      toggleFavorite: async (songId: number) => {
        try {
          // Use clientRequests.send instead of musicApi
          await clientRequests.send('music', 'toggleFavorite', { songId }, null, () => {
            // Update local state optimistically
            set(state => ({
              favorites: state.favorites.includes(songId)
                ? state.favorites.filter(id => id !== songId)
                : [...state.favorites, songId]
            }));
          });
        } catch (error) {
          console.error('[MusicStore] Error toggling favorite:', error);
        }
      },

      updateUserProfile: async (profileData: Partial<Artist>) => {
        try {
          // Use clientRequests.send instead of musicApi
          await clientRequests.send('music', 'updateUserProfile', { profileData }, null, () => {
            // Update local state optimistically
            set(state => ({
              artists: state.artists.map(artist =>
                artist.isUserProfile ? { ...artist, ...profileData } : artist
              )
            }));
          });
        } catch (error) {
          console.error('[MusicStore] Error updating user profile:', error);
        }
      },

      // Load all music data in a single request
      loadMusicData: async () => {
        try {
          // Set loading state
          set({ loading: true });
          // Then load songs and albums
          const songsPromise = clientRequests.send(
            'music',
            'getSongs',
            {},
            musicMockData.songs,
            get().handlers.onSetSongs as (data: unknown) => void
          );

          const albumsPromise = clientRequests.send(
            'music',
            'getAlbums',
            {},
            musicMockData.albums,
            get().handlers.onSetAlbums as (data: unknown) => void
          );

          // Wait for songs and albums to load
          await Promise.all([songsPromise, albumsPromise]);

          // Now load artists, but filter them to only include those with content
          await clientRequests.send('music', 'getArtists', {}, musicMockData.artists, ((
            artists: unknown
          ) => {
            // Filter artists to only include those with content and the user profile
            const { songs, albums } = get();

            // Create sets of artist IDs with content for faster lookup
            const artistsWithSongs = new Set(songs.map(song => song.artistId));
            const artistsWithAlbums = new Set(albums.map(album => album.artistId));

            // Filter artists to only include those with content (including user profile)
            const filteredArtists = (artists as Artist[]).filter(
              artist => artistsWithSongs.has(artist.id) || artistsWithAlbums.has(artist.id)
            );

            get().handlers.onSetArtists(filteredArtists);
          }) as (data: unknown) => void);

          // Finally, load favorites
          await get().actions.getFavorites();

          // Set loading state to false
          set({ loading: false });
        } catch (error) {
          console.error('[MusicStore] Error loading music data:', error);
          set({ loading: false });
        }
      }
    },

    // Data handlers implementation
    handlers: {
      onSetSongs: (songs: Song[]) => {
        set({ songs });
      },

      onSetAlbums: (albums: Album[]) => {
        set({ albums });
      },

      onSetArtists: (artists: Artist[]) => {
        set({ artists });
      },

      onSetFavorites: (favorites: number[]) => {
        set({ favorites });
      },

      onSetUserProfile: (artist: Artist) => {
        set(state => ({
          artists: state.artists.map(a => (a.isUserProfile ? artist : a))
        }));
      },

      onClearSongData: () => {
        console.log('[MusicStore] Clearing song data to save memory');

        // Stop any playing music
        if (get().isPlaying) {
          stopPlayback();
        }

        // Clear current song and playback state
        set({
          currentSong: null,
          isPlaying: false,
          currentTime: 0,
          duration: 0,
          queue: [],
          history: []
        });

        // Keep favorites and basic artist/album data, but clear full song list
        // This significantly reduces memory usage while preserving essential data
        set(state => {
          // Keep only minimal song data for favorites
          const minimalSongs = state.songs
            .filter(song => state.favorites.includes(song.id))
            .map(song => ({
              id: song.id,
              title: song.title,
              albumId: song.albumId,
              artistId: song.artistId,
              duration: song.duration,
              imageUrl: '' // Clear image URLs to save memory
            }));

          return {
            songs: minimalSongs
          };
        });

        console.log('[MusicStore] Song data cleared, keeping only favorites');
      }
    },

    // Helper methods implementation
    getSongsByAlbum: (albumId: number) => {
      return get().songs.filter(song => song.albumId === albumId);
    },

    getSongsByArtist: (artistId: number) => {
      return get().songs.filter(song => song.artistId === artistId);
    },

    isFavorite: (songId: number) => {
      return get().favorites.includes(songId);
    },

    // Artist navigation implementation
    setCurrentArtistId: (artistId: number | null) => {
      set({ currentArtistId: artistId });
      console.log(`[MusicStore] Current artist ID set to: ${artistId}`);
    },

    getCurrentArtist: () => {
      const { currentArtistId, artists } = get();
      if (currentArtistId === null) return undefined;
      return artists.find(artist => artist.id === currentArtistId);
    },

    // User profile methods implementation
    getUserProfileArtist: (identifier?: string) => {
      if (identifier) {
        // If identifier is provided, find artist with matching identifier and isUserProfile=true
        return get().artists.find(
          artist => artist.isUserProfile && artist.identifier === identifier
        );
      }
      // Otherwise, just find the first artist with isUserProfile=true
      return get().artists.find(artist => artist.isUserProfile);
    },

    getUserProfileAlbums: () => {
      const userProfile = get().getUserProfileArtist();
      if (!userProfile) return [];
      return get().albums.filter(album => album.artistId === userProfile.id);
    },

    getUserProfileSongs: () => {
      const userProfile = get().getUserProfileArtist();
      if (!userProfile) return [];
      return get().songs.filter(song => song.artistId === userProfile.id);
    }
  };
});
