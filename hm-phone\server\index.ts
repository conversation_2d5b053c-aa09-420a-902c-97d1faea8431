/**
 * Phone Server - Main Entry Point
 *
 * This file serves as the entry point for the server-side phone functionality.
 * It imports and initializes the core phone functionality when the resource starts.
 */

/// <reference types="@citizenfx/server" />

// Import framework detection
import config from '@shared/config';

// Import server-side apps
import { initializePhotosApp } from './apps/photos';
import { initializeServicesApp } from './apps/services';
import { initializeContactsApp } from './apps/contacts';
import { initializeMessagesApp } from './apps/messages';
import { initializeSettingsApp } from './apps/settings';
import { initializeBankingApp } from './apps/banking';
import { initializeYellowPagesApp } from './apps/yellowPages';
import { initializeLifeSnapApp } from './apps/lifeSnap';
import { initializeMusicApp } from './apps/music';
import { initializeCameraApp } from './apps/camera';
import { initializeNotesApp } from './apps/notes';

// Register resource start/stop events
on('onResourceStart', (resourceName: string) => {
    if (GetCurrentResourceName() !== resourceName) return;
    console.log(`[Phone] Server resource ${resourceName} started`);

    console.log(`[Phone] Using hm-core framework directly`);

    // Initialize server-side functionality
    initializeServer();
    console.log('[Phone] Server initialized');
});

on('onResourceStop', (resourceName: string) => {
    if (GetCurrentResourceName() !== resourceName) return;
    console.log(`[Phone] Server resource ${resourceName} stopped`);

    // Clean up resources
    cleanupServer();
});

/**
 * Initialize server-side functionality
 */
function initializeServer(): void {
    // Database initialization removed; use init_database.sql at resource root

    // Initialize server-side apps
    initializeContactsApp(); // Core dependency - initialize first
    initializeMessagesApp(); // Messages functionality
    initializePhotosApp(); // Media functionality
    initializeCameraApp(); // Camera functionality
    initializeServicesApp(); // Service-related functionality
    initializeSettingsApp(); // System-wide settings
    initializeBankingApp(); // Banking functionality
    initializeYellowPagesApp(); // Yellow Pages functionality
    initializeMusicApp(); // Music functionality
    initializeLifeSnapApp(); // Social media functionality
    initializeNotesApp(); // Notes functionality

    // Register server events
    console.log('[Phone] Server initialized');
}

/**
 * Clean up server-side resources
 */
function cleanupServer(): void {
    // Clean up resources
    console.log('[Phone] Server cleanup complete');
}
