import { create } from 'zustand';
import { HudData, DEFAULT_HUD_SETTINGS, HudSettings } from '../../../scripts/shared/types';

interface HudStore {
  // Visibility state
  visible: boolean;
  setVisible: (visible: boolean) => void;

  // HUD data
  hudData: HudData;
  updateHudData: (data: Partial<HudData>) => void;

  // Settings
  settings: HudSettings;
  updateSettings: (settings: Partial<HudSettings>) => void;
}

// Initial HUD data - Set for testing all elements visible in browser
const initialHudData: HudData = {
  health: 75,
  armor: 60, // Will show 3 segments filled
  oxygen: 75, // Will show oxygen bar (below 100)
  hunger: 65,
  thirst: 40,
  stress: 25,
  vehicle: {
    isInVehicle: true, // Make vehicle elements visible
    showSpeedometer: true,
    speed: 120,
    fuel: 78,
    engineHealth: 850,
    bodyHealth: 920,
    gear: 4,
    seatbelt: true,
    nitrous: 50
  },
  location: {
    streetName: "Test Street",
    crossingName: "Test Avenue",
    zoneName: "Test Zone"
  }
};

export const useHudStore = create<HudStore>((set) => ({
  // Visibility state
  visible: false,
  setVisible: (visible) => set({ visible }),

  // HUD data
  hudData: initialHudData,
  updateHudData: (data) => set((state) => ({
    hudData: {
      ...state.hudData,
      ...data,
      // Handle nested vehicle object updates properly
      vehicle: data.vehicle ? {
        ...state.hudData.vehicle,
        ...data.vehicle
      } : state.hudData.vehicle
    }
  })),

  // Settings
  settings: DEFAULT_HUD_SETTINGS,
  updateSettings: (settings) => set((state) => ({
    settings: { ...state.settings, ...settings }
  }))
}));
