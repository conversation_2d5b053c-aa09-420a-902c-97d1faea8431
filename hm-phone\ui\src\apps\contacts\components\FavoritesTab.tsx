import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Contact } from '@shared/types';

interface FavoritesTabProps {
  contacts: Contact[];
  onContactSelect: (contact: Contact) => void;
  onCallClick: (contact: Contact) => void;
  onMessageClick: (contact: Contact) => void;
}

const FavoritesTab: React.FC<FavoritesTabProps> = ({
  contacts,
  onContactSelect,
  onCallClick,
  onMessageClick
}) => {
  // State for search query
  const [searchQuery, setSearchQuery] = useState('');

  // Filter favorite contacts
  const favoriteContacts = contacts.filter(contact => contact.favorite === 1);

  // Filter by search query
  const filteredContacts = searchQuery.trim() === ''
    ? favoriteContacts
    : favoriteContacts.filter(contact =>
        contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.number.includes(searchQuery)
      );

  return (
    <div className="flex-1 flex flex-col overflow-hidden h-full">
      {/* Header with search bar */}
      <div className="px-4 pt-2 pb-3 bg-[#0a0f1a] z-30 border-b border-white/10 shadow-md flex-shrink-0">
        <div className="relative">
          <input
            type="text"
            placeholder="Search favorites..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#131b2e] text-white rounded-lg py-2 pl-9 pr-3 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
            <i className="fas fa-search text-xs"></i>
          </div>
          {searchQuery && (
            <button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/80"
              onClick={() => setSearchQuery('')}
            >
              <i className="fas fa-times text-xs"></i>
            </button>
          )}
        </div>
      </div>

      {/* Favorites list with consistent spacing */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-[#0a0f1a] to-[#0a0f1a]/95 flex flex-col min-h-0">
        <AnimatePresence>
          <div className="flex-1 pt-1 pb-2">
            {filteredContacts.length > 0 ? (
              filteredContacts.map(contact => (
                <motion.div
                  key={contact.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  whileTap={{ scale: 0.98 }}
                  className="mx-4 mb-1.5 py-1.5 px-2.5 border-b border-white/5 flex items-center hover:bg-[#0f1525]/50 transition-colors"
                  onClick={() => onContactSelect(contact)}
                >
                  {/* Contact avatar */}
                  <div className="relative">
                    {contact.avatar ? (
                      <div className="relative">
                        <div
                          className="w-9 h-9 rounded-full bg-cover bg-center"
                          style={{ backgroundImage: `url(${contact.avatar})` }}
                        />
                      </div>
                    ) : (
                      <div className="relative">
                        <div className="w-9 h-9 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {contact.name ? contact.name[0].toUpperCase() : '#'}
                          </span>
                        </div>
                      </div>
                    )}


                  </div>

                  {/* Contact info */}
                  <div className="ml-2.5 flex-1 min-w-0">
                    <div className="flex justify-between items-center">
                      <div className="text-white font-medium text-sm truncate pr-2">
                        {contact.name}
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-0.5">
                      <div className="text-xs text-white/50 truncate">
                        {contact.number}
                      </div>

                      {/* Action buttons */}
                      <div className="flex space-x-2">
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          onClick={e => {
                            e.stopPropagation();
                            onCallClick(contact);
                          }}
                          className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center text-green-500 hover:bg-green-500/30 transition-colors"
                        >
                          <i className="fas fa-phone text-xs"></i>
                        </motion.button>
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          onClick={e => {
                            e.stopPropagation();
                            onMessageClick(contact);
                          }}
                          className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-500 hover:bg-blue-500/30 transition-colors"
                        >
                          <i className="fas fa-comment text-xs"></i>
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex-1 flex flex-col items-center justify-center text-white/60 px-6 py-8"
              >
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#1a2234] to-[#0d1423] flex items-center justify-center mb-4 shadow-inner border border-white/5">
                  <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center">
                    <i className="fas fa-star text-yellow-400/50 text-xl"></i>
                  </div>
                </div>
                <p className="text-lg font-medium text-center text-white/80">
                  No favorite contacts
                </p>
                <p className="text-sm text-white/40 text-center mt-2.5 max-w-xs leading-relaxed">
                  Mark contacts as favorites to see them here
                </p>
              </motion.div>
            )}
          </div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default FavoritesTab;
