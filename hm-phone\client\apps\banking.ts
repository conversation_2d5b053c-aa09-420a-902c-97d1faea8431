/**
 * Banking App - Client Side
 *
 * This file handles client-side functionality for the Banking app.
 * The Banking app is initialized on demand due to security sensitivity.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the banking app
 */
export function initializeBankingApp(): void {
    console.log('[Banking] Initializing client-side banking app');

    // Register client events
    registerClientEvents();

    // Register NUI handlers
    registerNUIHandlers();
}

/**
 * Register client events for the banking app
 */
function registerClientEvents(): void {
    // Register event for accounts data
    onNet('hm-phone:bankAccounts', (accountsData: any) => {
        console.log('[Banking] Bank accounts data received:', accountsData.length);

        // Send the accounts data to the UI
        sendToUI('setAccounts', accountsData);
    });

    // Register event for transactions data
    onNet('hm-phone:bankTransactions', (transactionsData: any) => {
        console.log('[Banking] Bank transactions data received:', transactionsData.length);

        // Send the transactions data to the UI
        sendToUI('setTransactions', transactionsData);
    });

    // Register event for transaction result
    onNet('hm-phone:transactionResult', (result: any) => {
        console.log('[Banking] Transaction result received:', result);

        // Send the result to the UI
        sendToUI('transactionResult', result);
    });

    // Register event for banking error
    onNet('hm-phone:bankingError', (errorMessage: string) => {
        console.error('[Banking] Banking error:', errorMessage);

        // Send the error to the UI
        sendToUI('error', errorMessage);
    });
}

/**
 * Register NUI handlers for the banking app
 */
function registerNUIHandlers(): void {
    // Register handler for getting bank accounts
    registerAppHandler('banking', 'getAccounts', async () => {
        console.log('[Banking] Received getAccounts request from UI');

        try {
            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Banking] Getting accounts for player: ${stateid || identifier}`);

            // Request accounts from the server
            emitNet('hm-phone:getBankAccounts');

            // Return success to the UI
            // The actual accounts will be sent via the hm-phone:bankAccounts event
            return { success: true };
        } catch (error) {
            console.error('[Banking] Error getting bank accounts:', error);
            return { success: false, error: 'Failed to get bank accounts' };
        }
    });

    // Register handler for getting transactions
    registerAppHandler('banking', 'getTransactions', async (data: any) => {
        console.log('[Banking] Received getTransactions request from UI:', data);

        try {
            // Extract the account ID
            const { accountId } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Banking] Getting transactions for player: ${stateid || identifier}, account: ${accountId}`);

            // Request transactions from the server
            emitNet('hm-phone:getBankTransactions', accountId);

            // Return success to the UI
            // The actual transactions will be sent via the hm-phone:bankTransactions event
            return { success: true };
        } catch (error) {
            console.error('[Banking] Error getting transactions:', error);
            return { success: false, error: 'Failed to get transactions' };
        }
    });

    // Register handler for making a transfer
    registerAppHandler('banking', 'makeTransfer', async (data: any) => {
        console.log('[Banking] Received makeTransfer request from UI:', data);

        try {
            // Extract the transfer data
            const { fromAccountId, toAccountId, amount, description } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Banking] Making transfer for player: ${stateid || identifier}`);

            // Send the transfer request to the server
            emitNet('hm-phone:makeTransfer', fromAccountId, toAccountId, amount, description);

            // Return success to the UI
            // The actual result will be sent via the hm-phone:transactionResult event
            return { success: true };
        } catch (error) {
            console.error('[Banking] Error making transfer:', error);
            return { success: false, error: 'Failed to make transfer' };
        }
    });

    // Register handler for making a payment
    registerAppHandler('banking', 'makePayment', async (data: any) => {
        console.log('[Banking] Received makePayment request from UI:', data);

        try {
            // Extract the payment data
            const { fromAccountId, toPhone, amount, description } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Banking] Making payment for player: ${stateid || identifier}`);

            // Send the payment request to the server
            emitNet('hm-phone:makePayment', fromAccountId, toPhone, amount, description);

            // Return success to the UI
            // The actual result will be sent via the hm-phone:transactionResult event
            return { success: true };
        } catch (error) {
            console.error('[Banking] Error making payment:', error);
            return { success: false, error: 'Failed to make payment' };
        }
    });

    // Register handler for refreshing account balance
    registerAppHandler('banking', 'refreshBalance', async (data: any) => {
        console.log('[Banking] Received refreshBalance request from UI:', data);

        try {
            // Extract the account ID
            const { accountId } = data;

            // Get player data
            const identifier = global.playerIdentifier;
            const stateid = global.playerStateId;

            console.log(`[Banking] Refreshing balance for player: ${stateid || identifier}, account: ${accountId}`);

            // Request account refresh from the server
            emitNet('hm-phone:refreshBankAccount', accountId);

            // Return success to the UI
            return { success: true };
        } catch (error) {
            console.error('[Banking] Error refreshing balance:', error);
            return { success: false, error: 'Failed to refresh balance' };
        }
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'banking',
        type,
        data
    });
}
