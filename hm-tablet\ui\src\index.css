@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  /* Ensure root elements fill viewport */
  html, body, #root {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  /* Disable tab navigation for all elements */
  * {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Remove tab focus from all interactive elements */
  button, input, select, textarea, [tabindex] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Tablet-specific font settings */
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    background: transparent;
    color: #f5f5f5;
  }
}

@layer components {  /* Tablet screen base styles with realistic edges */
  .tablet-screen {
    background: linear-gradient(145deg, #0a0a0a 0%, #171717 100%);
    border: 2px solid #262626;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    z-index: 3;
    /* Enhanced tablet edge simulation */
    box-shadow: 
      /* Main depth shadow */
      0 8px 32px rgba(0, 0, 0, 0.6),
      /* Inner bevel highlight */
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      /* Inner bevel shadow */
      inset 0 -1px 0 rgba(0, 0, 0, 0.3),
      /* Side edges */
      inset 1px 0 0 rgba(255, 255, 255, 0.05),
      inset -1px 0 0 rgba(0, 0, 0, 0.2),
      /* Outer glow for premium feel */
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }  /* Tablet frame container for hardware elements */
  .tablet-frame {
    /* Smaller padding for more reasonable frame size */
    padding: 1.5rem;
    position: relative;
    /* Create the outer metal frame */
    background: linear-gradient(145deg, #3a3a3a, #1f1f1f, #2d2d2d);
    border-radius: 28px;
    box-shadow: 
      /* Deep outer shadow */
      0 20px 60px rgba(0, 0, 0, 0.8),
      /* Metal frame highlight */
      inset 0 2px 0 rgba(255, 255, 255, 0.2),
      /* Metal frame shadow */
      inset 0 -2px 0 rgba(0, 0, 0, 0.6),
      /* Side highlights */
      inset 2px 0 0 rgba(255, 255, 255, 0.1),
      inset -2px 0 0 rgba(0, 0, 0, 0.4);
  }

  /* Add metal texture overlay */
  .tablet-frame::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 28px;
    background: 
      /* Metal texture pattern */
      radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
      /* Brushed metal effect */
      linear-gradient(45deg, 
        transparent 40%, 
        rgba(255, 255, 255, 0.03) 50%, 
        transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
  /* Enhanced tablet edge visual depth */
  .tablet-frame::after {
    content: '';
    position: absolute;
    inset: 1rem;
    border-radius: 24px;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
  }

  /* Hardware element base styles */
  .tablet-hardware {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  }
  /* App icon base styles */
  .app-icon {
    @apply w-16 h-16 rounded-xl flex items-center justify-center text-3xl transition-all duration-200;
    background: linear-gradient(145deg, #262626 0%, #404040 100%);
    border: 1px solid #404040;
  }

  .app-icon:hover {
    @apply scale-105;
    background: linear-gradient(145deg, #404040 0%, #525252 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .app-icon:active {
    @apply scale-95;
  }

  /* Status bar styles */
  .status-bar {
    @apply flex items-center justify-between px-4 py-2 text-sm bg-gradient-to-r from-gray-900/50 to-gray-800/50;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #262626;
  }

  /* Navigation bar styles */
  .nav-bar {
    @apply flex items-center justify-center gap-6 py-3 bg-gradient-to-r from-gray-900/80 to-gray-800/80;
    backdrop-filter: blur(15px);
    border-top: 1px solid #262626;
  }

  /* App container styles */
  .app-container {
    @apply absolute inset-0 bg-tablet-background;
    border-radius: 18px;
    overflow: hidden;
  }

  /* Scrollbar customization */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(38, 38, 38, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(16, 185, 129, 0.5);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(16, 185, 129, 0.7);
  }
  /* Button variants */
  .btn-primary {
    @apply px-6 py-2 bg-tablet-accent-primary hover:bg-tablet-accent-hover text-white font-medium rounded-lg transition-all duration-200 shadow-lg;
    width: auto;
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  }

  .btn-secondary {
    @apply px-6 py-2 bg-tablet-surface hover:bg-gray-700 text-tablet-text-primary font-medium rounded-lg transition-all duration-200 border border-tablet-border;
    width: auto;
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  /* Input styles */
  .input-primary {
    @apply px-3 py-2 bg-tablet-surface border border-tablet-border rounded-lg text-tablet-text-primary placeholder-tablet-text-muted focus:border-tablet-accent-primary focus:ring-1 focus:ring-tablet-accent-primary transition-all duration-200;
    width: auto;
    min-width: 200px;
  }

  /* Card styles */
  .card {
    @apply bg-tablet-surface border border-tablet-border rounded-xl p-4 shadow-lg;
  }

  .card-hover {
    @apply hover:border-tablet-accent-primary/50 hover:shadow-xl transition-all duration-200;
  }

  /* Loading spinner */
  .loading-spinner {
    @apply w-6 h-6 border-2 border-tablet-accent-primary/30 border-t-tablet-accent-primary rounded-full animate-spin;
  }
}
