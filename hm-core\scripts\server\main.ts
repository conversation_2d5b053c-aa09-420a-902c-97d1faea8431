/// <reference types="@citizenfx/server" />
import { PlayerManager } from './player.manager';
import { Database } from './database';

class hmCore {
    private static instance: hmCore;
    private playerManager: PlayerManager | undefined;
    private database: Database | undefined;
    private isInitialized = false;

    private constructor() {
        this.initialize();
    }

    public static getInstance(): hmCore {
        if (!hmCore.instance) {
            hmCore.instance = new hmCore();
        }
        return hmCore.instance;
    }

    private async initialize(): Promise<void> {
        try {
            // Initialize database first
            this.database = Database.getInstance();
            console.info('✓ Database initialized');

            // Wait for database to be ready
            await this.waitForDatabase();

            // Initialize player manager
            this.playerManager = PlayerManager.getInstance();
            console.info('✓ Player Manager initialized');

            // Register global commands
            this.registerCommands();

            // Register exports for other resources
            this.registerExports();

            // Setup cleanup handlers
            this.setupCleanupHandlers();

            // Setup event handlers
            this.setupEventHandlers();

            this.isInitialized = true;
            console.info('=== SERVER CORE READY ===');

            // Notify other resources that core is ready
            emit('core:ready');
        } catch (error) {
            console.error(`Failed to initialize server core: ${error}`);
        }
    }

    private async waitForDatabase(): Promise<void> {
        return new Promise(resolve => {
            const checkDatabase = setInterval(async () => {
                try {
                    // Simple connectivity test
                    await exports.oxmysql.scalar('SELECT 1');
                    clearInterval(checkDatabase);
                    console.info('✓ Database connection verified');
                    resolve();
                } catch (error) {
                    console.warn('Waiting for database connection...');
                }
            }, 1000);
        });
    }

    private registerCommands(): void {
        // Admin commands
        RegisterCommand(
            'players',
            (source: number, _args: string[]) => {
                if (source === 0) {
                    // Console only
                    const onlinePlayers = getPlayers();
                    console.info(`Online players: ${onlinePlayers.length}`);
                    onlinePlayers.forEach(playerId => {
                        const name = GetPlayerName(playerId);
                        console.info(`  - ${name} (ID: ${playerId})`);
                    });
                }
            },
            true
        );

        // Player commands
        RegisterCommand(
            'char',
            (source: number, _args: string[]) => {
                const activeChar = this.playerManager?.getActiveCharacter(source);
                if (activeChar) {
                    emitNet(
                        'core:notify',
                        source,
                        'info',
                        `Current character: ${activeChar.first_name} ${activeChar.last_name}`
                    );
                } else {
                    emitNet('core:notify', source, 'error', 'No active character');
                }
            },
            false
        );

        console.info('✓ Commands registered');
    }

    private registerExports(): void {
        // Export player manager functions for other resources
        global.exports('getPlayerManager', () => this.playerManager);
        global.exports('getDatabase', () => this.database);

        // Export specific functions
        global.exports('getActiveCharacter', (source: number) => {
            return this.playerManager?.getActiveCharacter(source);
        });

        global.exports('getPlayer', (license: string) => {
            return this.playerManager?.getPlayer(license);
        });

        global.exports('isPlayerOnline', (license: string) => {
            return this.playerManager?.getPlayer(license) !== undefined;
        });

        // Export stateid function for inventory and other systems
        global.exports('stateid', (source: number) => {
            const activeCharacter = this.playerManager?.getActiveCharacter(source);
            return activeCharacter?.stateid || null;
        });

        console.info('✓ Exports registered');
    }

    private setupCleanupHandlers(): void {
        // Cleanup on resource stop
        on('onResourceStop', (resourceName: string) => {
            if (resourceName === GetCurrentResourceName()) {
                console.info('=== SERVER CORE SHUTDOWN ===');
                this.cleanup();
            }
        });

        // Cleanup orphaned data periodically
        setInterval(async () => {
            try {
                // Clean up offline players that have been offline for too long
                await exports.oxmysql.execute(
                    'UPDATE players SET is_online = FALSE WHERE last_seen < DATE_SUB(NOW(), INTERVAL 1 HOUR) AND is_online = TRUE'
                );
            } catch (error) {
                console.error(`Cleanup error: ${error}`);
            }
        }, 300000); // Every 5 minutes

        console.info('✓ Cleanup handlers registered');
    }

    private setupEventHandlers(): void {
        // Listen for character selection from multicharacter system
        onNet(
            'hm-core:playerLoaded',
            (data: { characterId: number; stateid: string; characterData: any }) => {
                const source = global.source;
                console.info(
                    `hm-core:playerLoaded event received from player ${source} for character stateid: ${data.stateid}`
                );

                try {
                    // Convert the character data to match our CharacterData interface
                    const characterData = {
                        id: data.characterId,
                        identifier: data.characterData.identifier || '',
                        stateid: data.stateid,
                        first_name: data.characterData.first_name,
                        last_name: data.characterData.last_name,
                        birthdate: new Date(data.characterData.birthdate),
                        gender: data.characterData.gender,
                        state: data.characterData.state || 'alive',
                        money: data.characterData.money || { cash: 500, bank: 5000, crypto: 0 },
                        position: data.characterData.position || { x: 0, y: 0, z: 100, heading: 0 },
                        appearance: data.characterData.appearance || {},
                        tattoos: data.characterData.tattoos || [],
                        metadata: data.characterData.metadata || {},
                        created_at: new Date(data.characterData.created_at || Date.now()),
                        updated_at: new Date(data.characterData.updated_at || Date.now()),
                    };

                    // Set the active character in the player manager
                    if (this.playerManager) {
                        this.playerManager.setActiveCharacter(source, characterData);
                        console.info(
                            `✓ Active character set for player ${source}: ${characterData.first_name} ${characterData.last_name}`
                        );
                        
                        // Emit the event back to the client so other resources can listen for it
                        emitNet('hm-core:playerLoaded', source, {
                            characterId: data.characterId,
                            stateid: data.stateid,
                            characterData: data.characterData
                        });
                        console.info(`✓ hm-core:playerLoaded event sent back to client ${source}`);
                    } else {
                        console.error(
                            'Player manager not initialized when trying to set active character'
                        );
                    }
                } catch (error) {
                    console.error(`Error handling hm-core:playerLoaded event: ${error}`);
                }
            }
        );

        console.info('✓ Event handlers registered');
    }

    private cleanup(): void {
        // Perform cleanup operations
        console.info('Performing cleanup...');
        // Set all players as offline
        exports.oxmysql.execute('UPDATE players SET is_online = FALSE WHERE is_online = TRUE');
        console.info('Cleanup completed');
    }

    public isReady(): boolean {
        return this.isInitialized;
    }

    public getPlayerManager(): PlayerManager {
        return this.playerManager!;
    }

    public getDatabase(): Database {
        return this.database!;
    }
}

// Initialize server core
const serverCore = hmCore.getInstance();

// Global access
global.hmCore = serverCore;
