import React, { useState } from 'react';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { useContactsStore } from '../stores/contactsStore';
import NearbyPlayersPanel from '../../../common/components/NearbyPlayersPanel';
import { Contact } from '@shared/types';

const ProfileTab: React.FC = () => {
  const { userProfile } = usePhoneStore();
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState(userProfile.name);
  const [showAirdrop, setShowAirdrop] = useState(false);
  const { shareContact } = useContactsStore().actions;

  // Track sharing status for each player
  const [sharingStatus, setSharingStatus] = useState<
    Record<string, { status: 'sent' | 'accepted' | 'declined' | 'error' }>
  >({});

  const handleEditName = () => {
    setEditedName(userProfile.name);
    setIsEditingName(true);
  };

  const handleSaveName = () => {
    // Handle saving name logic here
    setIsEditingName(false);
  };

  const handleCopyNumber = () => {
    navigator.clipboard.writeText(userProfile.phoneNumber || '');
  };

  const handleShareNumber = () => {
    setShowAirdrop(true);
  };

  const handlePlayerClick = (playerId: number) => {
    // Create a contact object from the user profile
    const myContact: Contact = {
      id: 0, // This will be assigned by the server
      identifier: userProfile.identifier || '',
      stateid: userProfile.stateid || '',
      number: userProfile.phoneNumber || '',
      name: userProfile.name || 'My Profile',
      favorite: 1,
      avatar: userProfile.imageUrl || null,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
    };

    // Update sharing status to 'sent'
    const key = `0_${playerId}`;
    setSharingStatus(prev => ({
      ...prev,
      [key]: { status: 'sent' }
    }));

    // Share the contact
    shareContact(myContact, playerId)
      .then(() => {
        // Update status to 'accepted' after successful sharing
        setSharingStatus(prev => ({
          ...prev,
          [key]: { status: 'accepted' }
        }));

        // Reset status after 5 seconds
        setTimeout(() => {
          setSharingStatus(prev => {
            const newStatus = { ...prev };
            delete newStatus[key];
            return newStatus;
          });
        }, 5000);
      })
      .catch(() => {
        // Update status to 'error' if sharing fails
        setSharingStatus(prev => ({
          ...prev,
          [key]: { status: 'error' }
        }));
      });
  };

  return (
    <div className="h-full flex flex-col bg-[#0a0f1a] text-white relative">
      <div className="p-4 flex flex-col items-center">
        {/* Profile Image */}
        <div className="relative w-24 h-24 rounded-full bg-white/20 overflow-hidden mb-4">
          {userProfile.imageUrl ? (
            <img
              src={userProfile.imageUrl}
              alt={userProfile.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <i className="fas fa-user text-4xl text-white/40"></i>
            </div>
          )}
          <button
            className="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-green-500 flex items-center justify-center shadow-lg"
            onClick={() => {
              /* Handle avatar change */
            }}
          >
            <i className="fas fa-camera text-white text-sm"></i>
          </button>
        </div>

        {/* Name */}
        {isEditingName ? (
          <div className="flex items-center gap-2 mb-2">
            <input
              type="text"
              value={editedName}
              onChange={e => setEditedName(e.target.value)}
              className="bg-white/10 text-white px-3 py-1.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/50"
              autoFocus
            />
            <button
              onClick={handleSaveName}
              className="bg-green-500 text-white px-3 py-1.5 rounded-lg"
            >
              Save
            </button>
          </div>
        ) : (
          <div className="flex items-center gap-2 mb-2">
            <h2 className="text-xl font-semibold">{userProfile.name}</h2>
            <button onClick={handleEditName} className="text-white/60 hover:text-white">
              <i className="fas fa-pencil-alt text-sm"></i>
            </button>
          </div>
        )}

        {/* Phone Number */}
        <div className="flex items-center gap-2 mt-2">
          <p className="text-white/60 text-sm">{userProfile.phoneNumber}</p>
          <button onClick={handleCopyNumber} className="text-white/60 hover:text-white">
            <i className="fas fa-copy text-sm"></i>
          </button>
        </div>

        {/* Share Number Button - Icon Only */}
        <button
          onClick={handleShareNumber}
          className="mt-4 w-12 h-12 flex items-center justify-center bg-gradient-to-r from-blue-500/20 to-blue-500/20 hover:from-blue-500/30 hover:to-blue-500/30 border border-blue-500/20 rounded-xl"
        >
          <i className="fas fa-qrcode text-blue-400 text-2xl"></i>
        </button>
      </div>

      {/* Additional Profile Info */}
      <div className="flex-1 px-4 py-2">
        <div className="bg-white/5 rounded-xl p-4 mb-4">
          <h3 className="text-white/80 text-sm font-medium mb-2">Personal Information</h3>

          <div className="space-y-3">
            <div>
              <p className="text-white/60 text-xs">Job</p>
              <p className="text-white text-sm">{userProfile.job?.label || 'Unemployed'}</p>
            </div>

            <div>
              <p className="text-white/60 text-xs">Character ID</p>
              <p className="text-white text-sm">{userProfile.stateid}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Nearby Players Panel */}
      <NearbyPlayersPanel
        show={showAirdrop}
        onClose={() => setShowAirdrop(false)}
        title="Share My Contact"
        onPlayerSelect={handlePlayerClick}
        getPlayerStatus={playerId => {
          // Get sharing status for a specific player
          const key = `0_${playerId}`; // Using 0 as the ID for user's own contact
          const status = sharingStatus[key]?.status || null;

          switch (status) {
            case 'sent':
              return {
                status,
                text: 'Sent',
                color: 'text-blue-400',
                icon: 'fa-paper-plane'
              };
            case 'accepted':
              return {
                status,
                text: 'Accepted',
                color: 'text-green-400',
                icon: 'fa-check'
              };
            case 'declined':
              return {
                status,
                text: 'Declined',
                color: 'text-red-400',
                icon: 'fa-times'
              };
            case 'error':
              return {
                status,
                text: 'Failed',
                color: 'text-red-400',
                icon: 'fa-exclamation'
              };
            default:
              return {
                status: null,
                text: 'Share',
                color: 'text-white',
                icon: 'fa-share-alt'
              };
          }
        }}
        contentAbove={
          <div className="bg-white/5 rounded-lg p-3 mb-2 flex items-center">
            {userProfile.imageUrl ? (
              <div
                className="w-10 h-10 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${userProfile.imageUrl})` }}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                <span className="text-white text-lg font-medium">
                  {userProfile.name ? userProfile.name[0].toUpperCase() : '#'}
                </span>
              </div>
            )}
            <div className="ml-3">
              <div className="text-white font-medium">{userProfile.name}</div>
              <div className="text-white/60 text-sm">{userProfile.phoneNumber}</div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ProfileTab;
