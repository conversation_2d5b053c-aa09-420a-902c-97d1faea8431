/**
 * Notes App - Client Side
 *
 * This file handles client-side functionality for the Notes app.
 */

import { registerAppHandler } from '../nui';

/**
 * Initialize the notes app
 */
export function initializeNotesApp(): void {
    registerNUIHandlers();
    registerClientEvents();
}

/**
 * Register NUI handlers for the notes app
 */
function registerNUIHandlers(): void {
    // Register handler for getting notes
    registerAppHandler('notes', 'getNotes', async () => {
        console.log('[Notes] Received getNotes request from UI');
        try {
            emitNet('hm-phone:getNotes');
            return { success: true };
        } catch (error) {
            console.error('[Notes] Error getting notes:', error);
            return { success: false, error: 'Failed to get notes' };
        }
    });

    // Register handler for creating a note
    registerAppHandler('notes', 'createNote', async (data: any) => {
        console.log('[Notes] Received createNote request from UI:', data);
        try {
            emitNet('hm-phone:createNote', data);
            return { success: true };
        } catch (error) {
            console.error('[Notes] Error creating note:', error);
            return { success: false, error: 'Failed to create note' };
        }
    });

    // Register handler for updating a note
    registerAppHandler('notes', 'updateNote', async (data: any) => {
        console.log('[Notes] Received updateNote request from UI:', data);
        try {
            emitNet('hm-phone:updateNote', data);
            return { success: true };
        } catch (error) {
            console.error('[Notes] Error updating note:', error);
            return { success: false, error: 'Failed to update note' };
        }
    });

    // Register handler for deleting a note
    registerAppHandler('notes', 'deleteNote', async (data: any) => {
        console.log('[Notes] Received deleteNote request from UI:', data);
        try {
            emitNet('hm-phone:deleteNote', data.id);
            return { success: true };
        } catch (error) {
            console.error('[Notes] Error deleting note:', error);
            return { success: false, error: 'Failed to delete note' };
        }
    });

    // Register handler for creating a category
    registerAppHandler('notes', 'createCategory', async (data: any) => {
        console.log('[Notes] Received createCategory request from UI:', data);
        try {
            emitNet('hm-phone:createNoteCategory', data);
            return { success: true };
        } catch (error) {
            console.error('[Notes] Error creating category:', error);
            return { success: false, error: 'Failed to create category' };
        }
    });
}

/**
 * Register client events for the notes app
 */
function registerClientEvents(): void {
    // Register event for notes data
    onNet('hm-phone:notes', (data: any) => {
        console.log('[Notes] Received notes data from server:', data);
        sendToUI('notes', data);
    });

    // Register event for note created
    onNet('hm-phone:noteCreated', (note: any) => {
        console.log('[Notes] Note created:', note);
        sendToUI('noteCreated', note);
    });

    // Register event for note updated
    onNet('hm-phone:noteUpdated', (note: any) => {
        console.log('[Notes] Note updated:', note);
        sendToUI('noteUpdated', note);
    });

    // Register event for note deleted
    onNet('hm-phone:noteDeleted', (noteId: number) => {
        console.log('[Notes] Note deleted:', noteId);
        sendToUI('noteDeleted', noteId);
    });

    // Register event for category created
    onNet('hm-phone:noteCategoryCreated', (category: any) => {
        console.log('[Notes] Category created:', category);
        sendToUI('categoryCreated', category);
    });

    // Register event for notes error
    onNet('hm-phone:notesError', (errorMessage: string) => {
        console.error('[Notes] Error:', errorMessage);
        sendToUI('error', errorMessage);
    });
}

/**
 * Send data to the UI
 * @param type Event type
 * @param data Event data
 */
function sendToUI(type: string, data: any): void {
    SendNUIMessage({
        app: 'notes',
        type,
        data
    });
}
