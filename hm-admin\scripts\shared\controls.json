[{"controlIndex": 0, "name": "INPUT NEXT CAMERA", "qwerty": "V", "controller": "BACK"}, {"controlIndex": 1, "name": "INPUT LOOK LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 2, "name": "INPUT LOOK UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 3, "name": "INPUT LOOK UP ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 4, "name": "INPUT LOOK DOWN ONLY", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 5, "name": "INPUT LOOK LEFT ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 6, "name": "INPUT LOOK RIGHT ONLY", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 7, "name": "INPUT CINEMATIC SLOWMO", "qwerty": "(NONE)", "controller": "R3"}, {"controlIndex": 8, "name": "INPUT SCRIPTED FLY UD", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 9, "name": "INPUT SCRIPTED FLY LR", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 10, "name": "INPUT SCRIPTED FLY ZUP", "qwerty": "PAGEUP", "controller": "LT"}, {"controlIndex": 11, "name": "INPUT SCRIPTED FLY ZDOWN", "qwerty": "PAGEDOWN", "controller": "RT"}, {"controlIndex": 12, "name": "INPUT WEAPON WHEEL UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 13, "name": "INPUT WEAPON WHEEL LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 14, "name": "INPUT WEAPON WHEEL NEXT", "qwerty": "SCROLLWHEEL DOWN", "controller": "DPAD RIGHT"}, {"controlIndex": 15, "name": "INPUT WEAPON WHEEL PREV", "qwerty": "SCROLLWHEEL UP", "controller": "DPAD LEFT"}, {"controlIndex": 16, "name": "INPUT SELECT NEXT WEAPON", "qwerty": "SCROLLWHEEL DOWN", "controller": "(NONE)"}, {"controlIndex": 17, "name": "INPUT SELECT PREV WEAPON", "qwerty": "SCROLLWHEEL UP", "controller": "(NONE)"}, {"controlIndex": 18, "name": "INPUT SKIP CUTSCENE", "qwerty": "ENTER / LEFT MOUSE BUTTON / SPACEBAR", "controller": "A"}, {"controlIndex": 19, "name": "INPUT CHARACTER WHEEL", "qwerty": "LEFT ALT", "controller": "DPAD DOWN"}, {"controlIndex": 20, "name": "INPUT MULTIPLAYER INFO", "qwerty": "Z", "controller": "DPAD DOWN"}, {"controlIndex": 21, "name": "INPUT SPRINT", "qwerty": "LEFT SHIFT", "controller": "A"}, {"controlIndex": 22, "name": "INPUT JUMP", "qwerty": "SPACEBAR", "controller": "X"}, {"controlIndex": 23, "name": "INPUT ENTER", "qwerty": "F", "controller": "Y"}, {"controlIndex": 24, "name": "INPUT ATTACK", "qwerty": "LEFT MOUSE BUTTON", "controller": "RT"}, {"controlIndex": 25, "name": "INPUT AIM", "qwerty": "RIGHT MOUSE BUTTON", "controller": "LT"}, {"controlIndex": 26, "name": "INPUT LOOK BEHIND", "qwerty": "C", "controller": "R3"}, {"controlIndex": 27, "name": "INPUT PHONE", "qwerty": "ARROW UP / SCROLLWHEEL BUTTON (PRESS)", "controller": "DPAD UP"}, {"controlIndex": 28, "name": "INPUT SPECIAL ABILITY", "qwerty": "(NONE)", "controller": "L3"}, {"controlIndex": 29, "name": "INPUT SPECIAL ABILITY SECONDARY", "qwerty": "B", "controller": "R3"}, {"controlIndex": 30, "name": "INPUT MOVE LR", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 31, "name": "INPUT MOVE UD", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 32, "name": "INPUT MOVE UP ONLY", "qwerty": "W", "controller": "LEFT STICK"}, {"controlIndex": 33, "name": "INPUT MOVE DOWN ONLY", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 34, "name": "INPUT MOVE LEFT ONLY", "qwerty": "A", "controller": "LEFT STICK"}, {"controlIndex": 35, "name": "INPUT MOVE RIGHT ONLY", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 36, "name": "INPUT DUCK", "qwerty": "LEFT CTRL", "controller": "L3"}, {"controlIndex": 37, "name": "INPUT SELECT WEAPON", "qwerty": "TAB", "controller": "LB"}, {"controlIndex": 38, "name": "INPUT PICKUP", "qwerty": "E", "controller": "LB"}, {"controlIndex": 39, "name": "INPUT SNIPER ZOOM", "qwerty": "[", "controller": "LEFT STICK"}, {"controlIndex": 40, "name": "INPUT SNIPER ZOOM IN ONLY", "qwerty": "]", "controller": "LEFT STICK"}, {"controlIndex": 41, "name": "INPUT SNIPER ZOOM OUT ONLY", "qwerty": "[", "controller": "LEFT STICK"}, {"controlIndex": 42, "name": "INPUT SNIPER ZOOM IN SECONDARY", "qwerty": "]", "controller": "DPAD UP"}, {"controlIndex": 43, "name": "INPUT SNIPER ZOOM OUT SECONDARY", "qwerty": "[", "controller": "DPAD DOWN"}, {"controlIndex": 44, "name": "INPUT COVER", "qwerty": "Q", "controller": "RB"}, {"controlIndex": 45, "name": "INPUT RELOAD", "qwerty": "R", "controller": "B"}, {"controlIndex": 46, "name": "INPUT TALK", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 47, "name": "INPUT DETONATE", "qwerty": "G", "controller": "DPAD LEFT"}, {"controlIndex": 48, "name": "INPUT HUD SPECIAL", "qwerty": "Z", "controller": "DPAD DOWN"}, {"controlIndex": 49, "name": "INPUT ARREST", "qwerty": "F", "controller": "Y"}, {"controlIndex": 50, "name": "INPUT ACCURATE AIM", "qwerty": "SCROLLWHEEL DOWN", "controller": "R3"}, {"controlIndex": 51, "name": "INPUT CONTEXT", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 52, "name": "INPUT CONTEXT SECONDARY", "qwerty": "Q", "controller": "DPAD LEFT"}, {"controlIndex": 53, "name": "INPUT WEAPON SPECIAL", "qwerty": "(NONE)", "controller": "Y"}, {"controlIndex": 54, "name": "INPUT WEAPON SPECIAL TWO", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 55, "name": "INPUT DIVE", "qwerty": "SPACEBAR", "controller": "RB"}, {"controlIndex": 56, "name": "INPUT DROP WEAPON", "qwerty": "F9", "controller": "Y"}, {"controlIndex": 57, "name": "INPUT DROP AMMO", "qwerty": "F10", "controller": "B"}, {"controlIndex": 58, "name": "INPUT THROW GRENADE", "qwerty": "G", "controller": "DPAD LEFT"}, {"controlIndex": 59, "name": "INPUT VEH MOVE LR", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 60, "name": "INPUT VEH MOVE UD", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 61, "name": "INPUT VEH MOVE UP ONLY", "qwerty": "LEFT SHIFT", "controller": "LEFT STICK"}, {"controlIndex": 62, "name": "INPUT VEH MOVE DOWN ONLY", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 63, "name": "INPUT VEH MOVE LEFT ONLY", "qwerty": "A", "controller": "LEFT STICK"}, {"controlIndex": 64, "name": "INPUT VEH MOVE RIGHT ONLY", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 65, "name": "INPUT VEH SPECIAL", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 66, "name": "INPUT VEH GUN LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 67, "name": "INPUT VEH GUN UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 68, "name": "INPUT VEH AIM", "qwerty": "RIGHT MOUSE BUTTON", "controller": "LB"}, {"controlIndex": 69, "name": "INPUT VEH ATTACK", "qwerty": "LEFT MOUSE BUTTON", "controller": "RB"}, {"controlIndex": 70, "name": "INPUT VEH ATTACK2", "qwerty": "RIGHT MOUSE BUTTON", "controller": "A"}, {"controlIndex": 71, "name": "INPUT VEH ACCELERATE", "qwerty": "W", "controller": "RT"}, {"controlIndex": 72, "name": "INPUT VEH BRAKE", "qwerty": "S", "controller": "LT"}, {"controlIndex": 73, "name": "INPUT VEH DUCK", "qwerty": "X", "controller": "A"}, {"controlIndex": 74, "name": "INPUT VEH HEADLIGHT", "qwerty": "H", "controller": "DPAD RIGHT"}, {"controlIndex": 75, "name": "INPUT VEH EXIT", "qwerty": "F", "controller": "Y"}, {"controlIndex": 76, "name": "INPUT VEH HANDBRAKE", "qwerty": "SPACEBAR", "controller": "RB"}, {"controlIndex": 77, "name": "INPUT VEH HOTWIRE LEFT", "qwerty": "W", "controller": "LT"}, {"controlIndex": 78, "name": "INPUT VEH HOTWIRE RIGHT", "qwerty": "S", "controller": "RT"}, {"controlIndex": 79, "name": "INPUT VEH LOOK BEHIND", "qwerty": "C", "controller": "R3"}, {"controlIndex": 80, "name": "INPUT VEH CIN CAM", "qwerty": "R", "controller": "B"}, {"controlIndex": 81, "name": "INPUT VEH NEXT RADIO", "qwerty": ".", "controller": "(NONE)"}, {"controlIndex": 82, "name": "INPUT VEH PREV RADIO", "qwerty": ",", "controller": "(NONE)"}, {"controlIndex": 83, "name": "INPUT VEH NEXT RADIO TRACK", "qwerty": "=", "controller": "(NONE)"}, {"controlIndex": 84, "name": "INPUT VEH PREV RADIO TRACK", "qwerty": "-", "controller": "(NONE)"}, {"controlIndex": 85, "name": "INPUT VEH RADIO WHEEL", "qwerty": "Q", "controller": "DPAD LEFT"}, {"controlIndex": 86, "name": "INPUT VEH HORN", "qwerty": "E", "controller": "L3"}, {"controlIndex": 87, "name": "INPUT VEH FLY THROTTLE UP", "qwerty": "W", "controller": "RT"}, {"controlIndex": 88, "name": "INPUT VEH FLY THROTTLE DOWN", "qwerty": "S", "controller": "LT"}, {"controlIndex": 89, "name": "INPUT VEH FLY YAW LEFT", "qwerty": "A", "controller": "LB"}, {"controlIndex": 90, "name": "INPUT VEH FLY YAW RIGHT", "qwerty": "D", "controller": "RB"}, {"controlIndex": 91, "name": "INPUT VEH PASSENGER AIM", "qwerty": "RIGHT MOUSE BUTTON", "controller": "LT"}, {"controlIndex": 92, "name": "INPUT VEH PASSENGER ATTACK", "qwerty": "LEFT MOUSE BUTTON", "controller": "RT"}, {"controlIndex": 93, "name": "INPUT VEH SPECIAL ABILITY FRANKLIN", "qwerty": "(NONE)", "controller": "R3"}, {"controlIndex": 94, "name": "INPUT VEH STUNT UD", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 95, "name": "INPUT VEH CINEMATIC UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 96, "name": "INPUT VEH CINEMATIC UP ONLY", "qwerty": "NUMPAD- / SCROLLWHEEL UP", "controller": "(NONE)"}, {"controlIndex": 97, "name": "INPUT VEH CINEMATIC DOWN ONLY", "qwerty": "NUMPAD+ / SCROLLWHEEL DOWN", "controller": "(NONE)"}, {"controlIndex": 98, "name": "INPUT VEH CINEMATIC LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 99, "name": "INPUT VEH SELECT NEXT WEAPON", "qwerty": "SCROLLWHEEL UP", "controller": "X"}, {"controlIndex": 100, "name": "INPUT VEH SELECT PREV WEAPON", "qwerty": "[", "controller": "(NONE)"}, {"controlIndex": 101, "name": "INPUT VEH ROOF", "qwerty": "H", "controller": "DPAD RIGHT"}, {"controlIndex": 102, "name": "INPUT VEH JUMP", "qwerty": "SPACEBAR", "controller": "RB"}, {"controlIndex": 103, "name": "INPUT VEH GRAPPLING HOOK", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 104, "name": "INPUT VEH SHUFFLE", "qwerty": "H", "controller": "DPAD RIGHT"}, {"controlIndex": 105, "name": "INPUT VEH DROP PROJECTILE", "qwerty": "X", "controller": "A"}, {"controlIndex": 106, "name": "INPUT VEH MOUSE CONTROL OVERRIDE", "qwerty": "LEFT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 107, "name": "INPUT VEH FLY ROLL LR", "qwerty": "NUMPAD 6", "controller": "LEFT STICK"}, {"controlIndex": 108, "name": "INPUT VEH FLY ROLL LEFT ONLY", "qwerty": "NUMPAD 4", "controller": "LEFT STICK"}, {"controlIndex": 109, "name": "INPUT VEH FLY ROLL RIGHT ONLY", "qwerty": "NUMPAD 6", "controller": "LEFT STICK"}, {"controlIndex": 110, "name": "INPUT VEH FLY PITCH UD", "qwerty": "NUMPAD 5", "controller": "LEFT STICK"}, {"controlIndex": 111, "name": "INPUT VEH FLY PITCH UP ONLY", "qwerty": "NUMPAD 8", "controller": "LEFT STICK"}, {"controlIndex": 112, "name": "INPUT VEH FLY PITCH DOWN ONLY", "qwerty": "NUMPAD 5", "controller": "LEFT STICK"}, {"controlIndex": 113, "name": "INPUT VEH FLY UNDERCARRIAGE", "qwerty": "G", "controller": "L3"}, {"controlIndex": 114, "name": "INPUT VEH FLY ATTACK", "qwerty": "RIGHT MOUSE BUTTON", "controller": "A"}, {"controlIndex": 115, "name": "INPUT VEH FLY SELECT NEXT WEAPON", "qwerty": "SCROLLWHEEL UP", "controller": "DPAD LEFT"}, {"controlIndex": 116, "name": "INPUT VEH FLY SELECT PREV WEAPON", "qwerty": "[", "controller": "(NONE)"}, {"controlIndex": 117, "name": "INPUT VEH FLY SELECT TARGET LEFT", "qwerty": "NUMPAD 7", "controller": "LB"}, {"controlIndex": 118, "name": "INPUT VEH FLY SELECT TARGET RIGHT", "qwerty": "NUMPAD 9", "controller": "RB"}, {"controlIndex": 119, "name": "INPUT VEH FLY VERTICAL FLIGHT MODE", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 120, "name": "INPUT VEH FLY DUCK", "qwerty": "X", "controller": "A"}, {"controlIndex": 121, "name": "INPUT VEH FLY ATTACK CAMERA", "qwerty": "INSERT", "controller": "R3"}, {"controlIndex": 122, "name": "INPUT VEH FLY MOUSE CONTROL OVERRIDE", "qwerty": "LEFT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 123, "name": "INPUT VEH SUB TURN LR", "qwerty": "NUMPAD 6", "controller": "LEFT STICK"}, {"controlIndex": 124, "name": "INPUT VEH SUB TURN LEFT ONLY", "qwerty": "NUMPAD 4", "controller": "LEFT STICK"}, {"controlIndex": 125, "name": "INPUT VEH SUB TURN RIGHT ONLY", "qwerty": "NUMPAD 6", "controller": "LEFT STICK"}, {"controlIndex": 126, "name": "INPUT VEH SUB PITCH UD", "qwerty": "NUMPAD 5", "controller": "LEFT STICK"}, {"controlIndex": 127, "name": "INPUT VEH SUB PITCH UP ONLY", "qwerty": "NUMPAD 8", "controller": "LEFT STICK"}, {"controlIndex": 128, "name": "INPUT VEH SUB PITCH DOWN ONLY", "qwerty": "NUMPAD 5", "controller": "LEFT STICK"}, {"controlIndex": 129, "name": "INPUT VEH SUB THROTTLE UP", "qwerty": "W", "controller": "RT"}, {"controlIndex": 130, "name": "INPUT VEH SUB THROTTLE DOWN", "qwerty": "S", "controller": "LT"}, {"controlIndex": 131, "name": "INPUT VEH SUB ASCEND", "qwerty": "LEFT SHIFT", "controller": "X"}, {"controlIndex": 132, "name": "INPUT VEH SUB DESCEND", "qwerty": "LEFT CTRL", "controller": "A"}, {"controlIndex": 133, "name": "INPUT VEH SUB TURN HARD LEFT", "qwerty": "A", "controller": "LB"}, {"controlIndex": 134, "name": "INPUT VEH SUB TURN HARD RIGHT", "qwerty": "D", "controller": "RB"}, {"controlIndex": 135, "name": "INPUT VEH SUB MOUSE CONTROL OVERRIDE", "qwerty": "LEFT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 136, "name": "INPUT VEH PUSHBIKE PEDAL", "qwerty": "W", "controller": "A"}, {"controlIndex": 137, "name": "INPUT VEH PUSHBIKE SPRINT", "qwerty": "CAPSLOCK", "controller": "A"}, {"controlIndex": 138, "name": "INPUT VEH PUSHBIKE FRONT BRAKE", "qwerty": "Q", "controller": "LT"}, {"controlIndex": 139, "name": "INPUT VEH PUSHBIKE REAR BRAKE", "qwerty": "S", "controller": "RT"}, {"controlIndex": 140, "name": "INPUT MELEE ATTACK LIGHT", "qwerty": "R", "controller": "B"}, {"controlIndex": 141, "name": "INPUT MELEE ATTACK HEAVY", "qwerty": "Q", "controller": "A"}, {"controlIndex": 142, "name": "INPUT MELEE ATTACK ALTERNATE", "qwerty": "LEFT MOUSE BUTTON", "controller": "RT"}, {"controlIndex": 143, "name": "INPUT MELEE BLOCK", "qwerty": "SPACEBAR", "controller": "X"}, {"controlIndex": 144, "name": "INPUT PARACHUTE DEPLOY", "qwerty": "F / LEFT MOUSE BUTTON", "controller": "Y"}, {"controlIndex": 145, "name": "INPUT PARACHUTE DETACH", "qwerty": "F", "controller": "Y"}, {"controlIndex": 146, "name": "INPUT PARACHUTE TURN LR", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 147, "name": "INPUT PARACHUTE TURN LEFT ONLY", "qwerty": "A", "controller": "LEFT STICK"}, {"controlIndex": 148, "name": "INPUT PARACHUTE TURN RIGHT ONLY", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 149, "name": "INPUT PARACHUTE PITCH UD", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 150, "name": "INPUT PARACHUTE PITCH UP ONLY", "qwerty": "W", "controller": "LEFT STICK"}, {"controlIndex": 151, "name": "INPUT PARACHUTE PITCH DOWN ONLY", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 152, "name": "INPUT PARACHUTE BRAKE LEFT", "qwerty": "Q", "controller": "LB"}, {"controlIndex": 153, "name": "INPUT PARACHUTE BRAKE RIGHT", "qwerty": "E", "controller": "RB"}, {"controlIndex": 154, "name": "INPUT PARACHUTE SMOKE", "qwerty": "X", "controller": "A"}, {"controlIndex": 155, "name": "INPUT PARACHUTE PRECISION LANDING", "qwerty": "LEFT SHIFT", "controller": "(NONE)"}, {"controlIndex": 156, "name": "INPUT MAP", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 157, "name": "INPUT SELECT WEAPON UNARMED", "qwerty": "1", "controller": "(NONE)"}, {"controlIndex": 158, "name": "INPUT SELECT WEAPON MELEE", "qwerty": "2", "controller": "(NONE)"}, {"controlIndex": 159, "name": "INPUT SELECT WEAPON HANDGUN", "qwerty": "6", "controller": "(NONE)"}, {"controlIndex": 160, "name": "INPUT SELECT WEAPON SHOTGUN", "qwerty": "3", "controller": "(NONE)"}, {"controlIndex": 161, "name": "INPUT SELECT WEAPON SMG", "qwerty": "7", "controller": "(NONE)"}, {"controlIndex": 162, "name": "INPUT SELECT WEAPON AUTO RIFLE", "qwerty": "8", "controller": "(NONE)"}, {"controlIndex": 163, "name": "INPUT SELECT WEAPON SNIPER", "qwerty": "9", "controller": "(NONE)"}, {"controlIndex": 164, "name": "INPUT SELECT WEAPON HEAVY", "qwerty": "4", "controller": "(NONE)"}, {"controlIndex": 165, "name": "INPUT SELECT WEAPON SPECIAL", "qwerty": "5", "controller": "(NONE)"}, {"controlIndex": 166, "name": "INPUT SELECT CHARACTER MICHAEL", "qwerty": "F5", "controller": "(NONE)"}, {"controlIndex": 167, "name": "INPUT SELECT CHARACTER FRANKLIN", "qwerty": "F6", "controller": "(NONE)"}, {"controlIndex": 168, "name": "INPUT SELECT CHARACTER TREVOR", "qwerty": "F7", "controller": "(NONE)"}, {"controlIndex": 169, "name": "INPUT SELECT CHARACTER MULTIPLAYER", "qwerty": "F8 (CONSOLE)", "controller": "(NONE)"}, {"controlIndex": 170, "name": "INPUT SAVE REPLAY CLIP", "qwerty": "F3", "controller": "B"}, {"controlIndex": 171, "name": "INPUT SPECIAL ABILITY PC", "qwerty": "CAPSLOCK", "controller": "(NONE)"}, {"controlIndex": 172, "name": "INPUT CELL<PERSON>HONE UP", "qwerty": "ARROW UP", "controller": "DPAD UP"}, {"controlIndex": 173, "name": "INPUT CELLPHONE DOWN", "qwerty": "ARROW DOWN", "controller": "DPAD DOWN"}, {"controlIndex": 174, "name": "INPUT CELLPHONE LEFT", "qwerty": "ARROW LEFT", "controller": "DPAD LEFT"}, {"controlIndex": 175, "name": "INPUT CELLPHONE RIGHT", "qwerty": "ARROW RIGHT", "controller": "DPAD RIGHT"}, {"controlIndex": 176, "name": "INPUT CELLPHONE SELECT", "qwerty": "ENTER / LEFT MOUSE BUTTON", "controller": "A"}, {"controlIndex": 177, "name": "INPUT CELLPHONE CANCEL", "qwerty": "BACKSPACE / ESC / RIGHT MOUSE BUTTON", "controller": "B"}, {"controlIndex": 178, "name": "INPUT CELLPHONE OPTION", "qwerty": "DELETE", "controller": "Y"}, {"controlIndex": 179, "name": "INPUT CELL<PERSON><PERSON>ONE EXTRA OPTION", "qwerty": "SPACEBAR", "controller": "X"}, {"controlIndex": 180, "name": "INPUT CELL<PERSON><PERSON><PERSON>E SCROLL FORWARD", "qwerty": "SCROLLWHEEL DOWN", "controller": "(NONE)"}, {"controlIndex": 181, "name": "INPUT CELL<PERSON><PERSON><PERSON><PERSON> SCROLL BACKWARD", "qwerty": "SCROLLWHEEL UP", "controller": "(NONE)"}, {"controlIndex": 182, "name": "INPUT CELL<PERSON><PERSON><PERSON><PERSON> CAMERA FOCUS LOCK", "qwerty": "L", "controller": "RT"}, {"controlIndex": 183, "name": "INPUT CELL<PERSON><PERSON>ONE CAMERA GRID", "qwerty": "G", "controller": "RB"}, {"controlIndex": 184, "name": "INPUT CELL<PERSON><PERSON>ONE CAMERA SELFIE", "qwerty": "E", "controller": "R3"}, {"controlIndex": 185, "name": "INPUT CELLPHONE CAMERA DOF", "qwerty": "F", "controller": "LB"}, {"controlIndex": 186, "name": "INPUT CELLP<PERSON>ONE CAMERA EXPRESSION", "qwerty": "X", "controller": "L3"}, {"controlIndex": 187, "name": "INPUT FRONTEND DOWN", "qwerty": "ARROW DOWN", "controller": "DPAD DOWN"}, {"controlIndex": 188, "name": "INPUT FRONTEND UP", "qwerty": "ARROW UP", "controller": "DPAD UP"}, {"controlIndex": 189, "name": "INPUT FRONTEND LEFT", "qwerty": "ARROW LEFT", "controller": "DPAD LEFT"}, {"controlIndex": 190, "name": "INPUT FRONTEND RIGHT", "qwerty": "ARROW RIGHT", "controller": "DPAD RIGHT"}, {"controlIndex": 191, "name": "INPUT FRONTEND RDOWN", "qwerty": "ENTER", "controller": "A"}, {"controlIndex": 192, "name": "INPUT FRONTEND RUP", "qwerty": "TAB", "controller": "Y"}, {"controlIndex": 193, "name": "INPUT FRONTEND RLEFT", "qwerty": "(NONE)", "controller": "X"}, {"controlIndex": 194, "name": "INPUT FRONTEND RRIGHT", "qwerty": "BACKSPACE", "controller": "B"}, {"controlIndex": 195, "name": "INPUT FRONTEND AXIS X", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 196, "name": "INPUT FRONTEND AXIS Y", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 197, "name": "INPUT FRONTEND RIGHT AXIS X", "qwerty": "]", "controller": "RIGHT STICK"}, {"controlIndex": 198, "name": "INPUT FRONTEND RIGHT AXIS Y", "qwerty": "SCROLLWHEEL DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 199, "name": "INPUT FRONTEND PAUSE", "qwerty": "P", "controller": "START"}, {"controlIndex": 200, "name": "INPUT FRONTEND PAUSE ALTERNATE", "qwerty": "ESC", "controller": "(NONE)"}, {"controlIndex": 201, "name": "INPUT FRONTEND ACCEPT", "qwerty": "ENTER / NUMPAD ENTER", "controller": "A"}, {"controlIndex": 202, "name": "INPUT FRONTEND CANCEL", "qwerty": "BACKSPACE / ESC", "controller": "B"}, {"controlIndex": 203, "name": "INPUT FRONTEND X", "qwerty": "SPACEBAR", "controller": "X"}, {"controlIndex": 204, "name": "INPUT FRONTEND Y", "qwerty": "TAB", "controller": "Y"}, {"controlIndex": 205, "name": "INPUT FRONTEND LB", "qwerty": "Q", "controller": "LB"}, {"controlIndex": 206, "name": "INPUT FRONTEND RB", "qwerty": "E", "controller": "RB"}, {"controlIndex": 207, "name": "INPUT FRONTEND LT", "qwerty": "PAGE DOWN", "controller": "LT"}, {"controlIndex": 208, "name": "INPUT FRONTEND RT", "qwerty": "PAGE UP", "controller": "RT"}, {"controlIndex": 209, "name": "INPUT FRONTEND LS", "qwerty": "LEFT SHIFT", "controller": "L3"}, {"controlIndex": 210, "name": "INPUT FRONTEND RS", "qwerty": "LEFT CONTROL", "controller": "R3"}, {"controlIndex": 211, "name": "INPUT FRONTEND LEADERBOARD", "qwerty": "TAB", "controller": "RB"}, {"controlIndex": 212, "name": "INPUT FRONTEND SOCIAL CLUB", "qwerty": "HOME", "controller": "BACK"}, {"controlIndex": 213, "name": "INPUT FRONTEND SOCIAL CLUB SECONDARY", "qwerty": "HOME", "controller": "RB"}, {"controlIndex": 214, "name": "INPUT FRONTEND DELETE", "qwerty": "DELETE", "controller": "X"}, {"controlIndex": 215, "name": "INPUT FRONTEND ENDSC<PERSON>EN ACCEPT", "qwerty": "ENTER", "controller": "A"}, {"controlIndex": 216, "name": "INPUT FRONTEND ENDSC<PERSON>EN EXPAND", "qwerty": "SPACEBAR", "controller": "X"}, {"controlIndex": 217, "name": "INPUT FRONTEND SELECT", "qwerty": "CAPSLOCK", "controller": "BACK"}, {"controlIndex": 218, "name": "INPUT SCRIPT LEFT AXIS X", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 219, "name": "INPUT SCRIPT LEFT AXIS Y", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 220, "name": "INPUT SCRIPT RIGHT AXIS X", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 221, "name": "INPUT SCRIPT RIGHT AXIS Y", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 222, "name": "INPUT SCRIPT RUP", "qwerty": "RIGHT MOUSE BUTTON", "controller": "Y"}, {"controlIndex": 223, "name": "INPUT SCRIPT RDOWN", "qwerty": "LEFT MOUSE BUTTON", "controller": "A"}, {"controlIndex": 224, "name": "INPUT SCRIPT RLEFT", "qwerty": "LEFT CTRL", "controller": "X"}, {"controlIndex": 225, "name": "INPUT SCRIPT RRIGHT", "qwerty": "RIGHT MOUSE BUTTON", "controller": "B"}, {"controlIndex": 226, "name": "INPUT SCRIPT LB", "qwerty": "(NONE)", "controller": "LB"}, {"controlIndex": 227, "name": "INPUT SCRIPT RB", "qwerty": "(NONE)", "controller": "RB"}, {"controlIndex": 228, "name": "INPUT SCRIPT LT", "qwerty": "(NONE)", "controller": "LT"}, {"controlIndex": 229, "name": "INPUT SCRIPT RT", "qwerty": "LEFT MOUSE BUTTON", "controller": "RT"}, {"controlIndex": 230, "name": "INPUT SCRIPT LS", "qwerty": "(NONE)", "controller": "L3"}, {"controlIndex": 231, "name": "INPUT SCRIPT RS", "qwerty": "(NONE)", "controller": "R3"}, {"controlIndex": 232, "name": "INPUT SCRIPT PAD UP", "qwerty": "W", "controller": "DPAD UP"}, {"controlIndex": 233, "name": "INPUT SCRIPT PAD DOWN", "qwerty": "S", "controller": "DPAD DOWN"}, {"controlIndex": 234, "name": "INPUT SCRIPT PAD LEFT", "qwerty": "A", "controller": "DPAD LEFT"}, {"controlIndex": 235, "name": "INPUT SCRIPT PAD RIGHT", "qwerty": "D", "controller": "DPAD RIGHT"}, {"controlIndex": 236, "name": "INPUT SCRIPT SELECT", "qwerty": "V", "controller": "BACK"}, {"controlIndex": 237, "name": "INPUT CURSOR ACCEPT", "qwerty": "LEFT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 238, "name": "INPUT CURSOR CANCEL", "qwerty": "RIGHT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 239, "name": "INPUT CURSOR X", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 240, "name": "INPUT CURSOR Y", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 241, "name": "INPUT CURSOR SCROLL UP", "qwerty": "SCROLLWHEEL UP", "controller": "(NONE)"}, {"controlIndex": 242, "name": "INPUT CURSOR SCROLL DOWN", "qwerty": "SCROLLWHEEL DOWN", "controller": "(NONE)"}, {"controlIndex": 243, "name": "INPUT ENTER CHEAT CODE", "qwerty": "~ / `", "controller": "(NONE)"}, {"controlIndex": 244, "name": "INPUT INTERACTION MENU", "qwerty": "M", "controller": "BACK"}, {"controlIndex": 245, "name": "INPUT MP TEXT CHAT ALL", "qwerty": "T", "controller": "(NONE)"}, {"controlIndex": 246, "name": "INPUT MP TEXT CHAT TEAM", "qwerty": "Y", "controller": "(NONE)"}, {"controlIndex": 247, "name": "INPUT MP TEXT CHAT FRIENDS", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 248, "name": "INPUT MP TEXT CHAT CREW", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 249, "name": "INPUT PUSH TO TALK", "qwerty": "N", "controller": "(NONE)"}, {"controlIndex": 250, "name": "INPUT CREATOR LS", "qwerty": "R", "controller": "L3"}, {"controlIndex": 251, "name": "INPUT CREATOR RS", "qwerty": "F", "controller": "R3"}, {"controlIndex": 252, "name": "INPUT CREATOR LT", "qwerty": "X", "controller": "LT"}, {"controlIndex": 253, "name": "INPUT CREATOR RT", "qwerty": "C", "controller": "RT"}, {"controlIndex": 254, "name": "INPUT CREATOR MENU TOGGLE", "qwerty": "LEFT SHIFT", "controller": "(NONE)"}, {"controlIndex": 255, "name": "INPUT CREATOR ACCEPT", "qwerty": "SPACEBAR", "controller": "A"}, {"controlIndex": 256, "name": "INPUT CREATOR DELETE", "qwerty": "DELETE", "controller": "X"}, {"controlIndex": 257, "name": "INPUT ATTACK2", "qwerty": "LEFT MOUSE BUTTON", "controller": "RT"}, {"controlIndex": 258, "name": "INPUT RAPPEL JUMP", "qwerty": "(NONE)", "controller": "A"}, {"controlIndex": 259, "name": "INPUT RAPPEL LONG JUMP", "qwerty": "(NONE)", "controller": "X"}, {"controlIndex": 260, "name": "INPUT RAPPEL SMASH WINDOW", "qwerty": "(NONE)", "controller": "RT"}, {"controlIndex": 261, "name": "INPUT PREV WEAPON", "qwerty": "SCROLLWHEEL UP", "controller": "DPAD LEFT"}, {"controlIndex": 262, "name": "INPUT NEXT WEAPON", "qwerty": "SCROLLWHEEL DOWN", "controller": "DPAD RIGHT"}, {"controlIndex": 263, "name": "INPUT MELEE ATTACK1", "qwerty": "R", "controller": "B"}, {"controlIndex": 264, "name": "INPUT MELEE ATTACK2", "qwerty": "Q", "controller": "A"}, {"controlIndex": 265, "name": "INPUT WHISTLE", "qwerty": "(NONE)", "controller": "(NONE)"}, {"controlIndex": 266, "name": "INPUT MOVE LEFT", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 267, "name": "INPUT MOVE RIGHT", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 268, "name": "INPUT MOVE UP", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 269, "name": "INPUT MOVE DOWN", "qwerty": "S", "controller": "LEFT STICK"}, {"controlIndex": 270, "name": "INPUT LOOK LEFT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 271, "name": "INPUT LOOK RIGHT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 272, "name": "INPUT LOOK UP", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 273, "name": "INPUT LOOK DOWN", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 274, "name": "INPUT SNIPER ZOOM IN", "qwerty": "[", "controller": "RIGHT STICK"}, {"controlIndex": 275, "name": "INPUT SNIPER ZOOM OUT", "qwerty": "[", "controller": "RIGHT STICK"}, {"controlIndex": 276, "name": "INPUT SNIPER ZOOM IN ALTERNATE", "qwerty": "[", "controller": "LEFT STICK"}, {"controlIndex": 277, "name": "INPUT SNIPER ZOOM OUT ALTERNATE", "qwerty": "[", "controller": "LEFT STICK"}, {"controlIndex": 278, "name": "INPUT VEH MOVE LEFT", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 279, "name": "INPUT VEH MOVE RIGHT", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 280, "name": "INPUT VEH MOVE UP", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 281, "name": "INPUT VEH MOVE DOWN", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 282, "name": "INPUT VEH GUN LEFT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 283, "name": "INPUT VEH GUN RIGHT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 284, "name": "INPUT VEH GUN UP", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 285, "name": "INPUT VEH GUN DOWN", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 286, "name": "INPUT VEH LOOK LEFT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 287, "name": "INPUT VEH LOOK RIGHT", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 288, "name": "INPUT REPLAY START STOP RECORDING", "qwerty": "F1", "controller": "A"}, {"controlIndex": 289, "name": "INPUT REPLAY START STOP RECORDING SECONDARY", "qwerty": "F2", "controller": "X"}, {"controlIndex": 290, "name": "INPUT SCALED LOOK LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 291, "name": "INPUT SCALED LOOK UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 292, "name": "INPUT SCALED LOOK UP ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 293, "name": "INPUT SCALED LOOK DOWN ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 294, "name": "INPUT SCALED LOOK LEFT ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 295, "name": "INPUT SCALED LOOK RIGHT ONLY", "qwerty": "(NONE)", "controller": "RIGHT STICK"}, {"controlIndex": 296, "name": "INPUT REPLAY MARKER DELETE", "qwerty": "DELETE", "controller": "X"}, {"controlIndex": 297, "name": "INPUT REPLAY CLIP DELETE", "qwerty": "DELETE", "controller": "Y"}, {"controlIndex": 298, "name": "INPUT REPLAY PAUSE", "qwerty": "SPACEBAR", "controller": "A"}, {"controlIndex": 299, "name": "INPUT REPLAY REWIND", "qwerty": "ARROW DOWN", "controller": "LB"}, {"controlIndex": 300, "name": "INPUT REPLAY FFWD", "qwerty": "ARROW UP", "controller": "RB"}, {"controlIndex": 301, "name": "INPUT REPLAY NEWMARKER", "qwerty": "M", "controller": "A"}, {"controlIndex": 302, "name": "INPUT REPLAY RECORD", "qwerty": "S", "controller": "(NONE)"}, {"controlIndex": 303, "name": "INPUT REPLAY SCREENSHOT", "qwerty": "U", "controller": "DPAD UP"}, {"controlIndex": 304, "name": "INPUT REPLAY HIDEHUD", "qwerty": "H", "controller": "R3"}, {"controlIndex": 305, "name": "INPUT REPLAY STARTPOINT", "qwerty": "B", "controller": "(NONE)"}, {"controlIndex": 306, "name": "INPUT REPLAY ENDPOINT", "qwerty": "N", "controller": "(NONE)"}, {"controlIndex": 307, "name": "INPUT REPLAY ADVANCE", "qwerty": "ARROW RIGHT", "controller": "DPAD RIGHT"}, {"controlIndex": 308, "name": "INPUT REPLAY BACK", "qwerty": "ARROW LEFT", "controller": "DPAD LEFT"}, {"controlIndex": 309, "name": "INPUT REPLAY TOOLS", "qwerty": "T", "controller": "DPAD DOWN"}, {"controlIndex": 310, "name": "INPUT REPLAY RESTART", "qwerty": "R", "controller": "BACK"}, {"controlIndex": 311, "name": "INPUT REPLAY SHOWHOTKEY", "qwerty": "K", "controller": "DPAD DOWN"}, {"controlIndex": 312, "name": "INPUT REPLAY CYCLEMARKERLEFT", "qwerty": "[", "controller": "DPAD LEFT"}, {"controlIndex": 313, "name": "INPUT REPLAY C<PERSON><PERSON>EMARKERRI<PERSON>HT", "qwerty": "]", "controller": "DPAD RIGHT"}, {"controlIndex": 314, "name": "INPUT REPLAY FOVINCREASE", "qwerty": "NUMPAD +", "controller": "RB"}, {"controlIndex": 315, "name": "INPUT REPLAY FOVDECREASE", "qwerty": "NUMPAD -", "controller": "LB"}, {"controlIndex": 316, "name": "INPUT REPLAY CAMERAUP", "qwerty": "PAGE UP", "controller": "(NONE)"}, {"controlIndex": 317, "name": "INPUT REPLAY CAMERADOWN", "qwerty": "PAGE DOWN", "controller": "(NONE)"}, {"controlIndex": 318, "name": "INPUT REPLAY SAVE", "qwerty": "F5", "controller": "START"}, {"controlIndex": 319, "name": "INPUT REPLAY TOGGLETIME", "qwerty": "C", "controller": "(NONE)"}, {"controlIndex": 320, "name": "INPUT REPLAY TOGGLETIPS", "qwerty": "V", "controller": "(NONE)"}, {"controlIndex": 321, "name": "INPUT REPLAY PREVIEW", "qwerty": "SPACEBAR", "controller": "(NONE)"}, {"controlIndex": 322, "name": "INPUT REPLAY TOGGLE TIMELINE", "qwerty": "ESC", "controller": "(NONE)"}, {"controlIndex": 323, "name": "INPUT REPLAY TIMELINE PICKUP CLIP", "qwerty": "X", "controller": "(NONE)"}, {"controlIndex": 324, "name": "INPUT REPLAY TIMELINE DUPLICATE CLIP", "qwerty": "C", "controller": "(NONE)"}, {"controlIndex": 325, "name": "INPUT REPLAY TIMELINE PLACE CLIP", "qwerty": "V", "controller": "(NONE)"}, {"controlIndex": 326, "name": "INPUT REPLAY CTRL", "qwerty": "LEFT CTRL", "controller": "(NONE)"}, {"controlIndex": 327, "name": "INPUT REPLAY TIMELINE SAVE", "qwerty": "F5", "controller": "(NONE)"}, {"controlIndex": 328, "name": "INPUT REPLAY PREVIEW AUDIO", "qwerty": "SPACEBAR", "controller": "RT"}, {"controlIndex": 329, "name": "INPUT VEH DRIVE LOOK", "qwerty": "LEFT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 330, "name": "INPUT VEH DRIVE LOOK2", "qwerty": "RIGHT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 331, "name": "INPUT VEH FLY ATTACK2", "qwerty": "RIGHT MOUSE BUTTON", "controller": "(NONE)"}, {"controlIndex": 332, "name": "INPUT RADIO WHEEL UD", "qwerty": "MOUSE DOWN", "controller": "RIGHT STICK"}, {"controlIndex": 333, "name": "INPUT RADIO WHEEL LR", "qwerty": "MOUSE RIGHT", "controller": "RIGHT STICK"}, {"controlIndex": 334, "name": "INPUT VEH SLOWMO UD", "qwerty": "SCROLLWHEEL DOWN", "controller": "LEFT STICK"}, {"controlIndex": 335, "name": "INPUT VEH SLOWMO UP ONLY", "qwerty": "SCROLLWHEEL UP", "controller": "LEFT STICK"}, {"controlIndex": 336, "name": "INPUT VEH SLOWMO DOWN ONLY", "qwerty": "SCROLLWHEEL DOWN", "controller": "LEFT STICK"}, {"controlIndex": 337, "name": "INPUT VEH HYDRAULICS CONTROL TOGGLE", "qwerty": "X", "controller": "A"}, {"controlIndex": 338, "name": "INPUT VEH HYDRAULICS CONTROL LEFT", "qwerty": "A", "controller": "LEFT STICK"}, {"controlIndex": 339, "name": "INPUT VEH HYDRAULICS CONTROL RIGHT", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 340, "name": "INPUT VEH HYDRAULICS CONTROL UP", "qwerty": "LEFT SHIFT", "controller": "LEFT STICK"}, {"controlIndex": 341, "name": "INPUT VEH HYDRAULICS CONTROL DOWN", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 342, "name": "INPUT VEH HYDRAULICS CONTROL UD", "qwerty": "D", "controller": "LEFT STICK"}, {"controlIndex": 343, "name": "INPUT VEH HYDRAULICS CONTROL LR", "qwerty": "LEFT CTRL", "controller": "LEFT STICK"}, {"controlIndex": 344, "name": "INPUT SWITCH VISOR", "qwerty": "F11", "controller": "DPAD RIGHT"}, {"controlIndex": 345, "name": "INPUT VEH MELEE HOLD", "qwerty": "X", "controller": "A"}, {"controlIndex": 346, "name": "INPUT VEH MELEE LEFT", "qwerty": "LEFT MOUSE BUTTON", "controller": "LB"}, {"controlIndex": 347, "name": "INPUT VEH MELEE RIGHT", "qwerty": "RIGHT MOUSE BUTTON", "controller": "RB"}, {"controlIndex": 348, "name": "INPUT MAP POI", "qwerty": "SCROLLWHEEL BUTTON (PRESS)", "controller": "Y"}, {"controlIndex": 349, "name": "INPUT REPLAY SNAPMATIC PHOTO", "qwerty": "TAB", "controller": "X"}, {"controlIndex": 350, "name": "INPUT VEH CAR JUMP", "qwerty": "E", "controller": "L3"}, {"controlIndex": 351, "name": "INPUT VEH ROCKET BOOST", "qwerty": "E", "controller": "L3"}, {"controlIndex": 352, "name": "INPUT VEH FLY BOOST", "qwerty": "LEFT SHIFT", "controller": "L3"}, {"controlIndex": 353, "name": "INPUT VEH PARACHUTE", "qwerty": "SPACEBAR", "controller": "A"}, {"controlIndex": 354, "name": "INPUT VEH BIKE WINGS", "qwerty": "X", "controller": "A"}, {"controlIndex": 355, "name": "INPUT VEH FLY BOMB BAY", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 356, "name": "INPUT VEH FLY COUNTER", "qwerty": "E", "controller": "DPAD RIGHT"}, {"controlIndex": 357, "name": "INPUT VEH TRANSFORM", "qwerty": "X", "controller": "A"}, {"controlIndex": 358, "name": "INPUT QUAD LOCO REVERSE", "qwerty": "", "controller": "RB"}, {"controlIndex": 359, "name": "INPUT RESPAWN FASTER", "qwerty": "", "controller": ""}, {"controlIndex": 360, "name": "INPUT HUDMARKER SELECT", "qwerty": "(NONE)", "controller": "(NONE)"}]