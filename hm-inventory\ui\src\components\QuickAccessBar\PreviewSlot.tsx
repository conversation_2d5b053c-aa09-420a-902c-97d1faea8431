import React from 'react';
import { InventoryItem, SlotType } from '@shared';

interface PreviewSlotProps {
  inventoryItem?: InventoryItem;
  quantity?: number;
  slotType?: SlotType;
  keyBinding?: number;
}

const slotTypeFaClass: Record<SlotType, string> = {
  [SlotType.PHONE]: 'fa-mobile-button',
  [SlotType.ARMOR]: 'fa-vest',
  [SlotType.PRIMARY_WEAPON]: 'fa-gun',
  [SlotType.TABLET]: 'fa-tablet-screen-button',
  [SlotType.BACKPACK]: 'fa-suitcase',
};

/**
 * PreviewSlot is a non-interactive version of the Slot component
 * Used for quickaccess previews that show items without drag/drop functionality
 */
const PreviewSlot: React.FC<PreviewSlotProps> = ({
  inventoryItem,
  quantity,
  slotType,
  keyBinding,
}) => {
  // Determine display quantity: prefer prop, else fallback to inventoryItem.quantity or 0
  const displayQuantity = typeof quantity === 'number' ? quantity : inventoryItem?.quantity ?? 0;

  return (
    <div className="relative flex flex-col items-center group">
      {/* Slot container with same styling as regular slots but no drag/drop */}
      <div className="relative w-[5.5rem] h-[5.5rem] rounded-lg bg-neutral-800/90 border-2 border-neutral-700 shadow-md transition-all duration-200 ease-out hover:border-neutral-600 hover:shadow-lg overflow-hidden">
        
        {/* Slot type icon (for action slots without items) */}
        {!inventoryItem && slotType && (
          <div className="absolute inset-0 flex items-center justify-center opacity-40">
            <i className={`fas ${slotTypeFaClass[slotType]} text-2xl text-neutral-500`} />
          </div>
        )}

        {inventoryItem && (
          <>
            {/* Item quantity badge */}
            {displayQuantity > 1 && (
              <div className="absolute top-1 right-1 bg-neutral-900/80 text-white text-xs font-semibold px-1.5 py-0.5 rounded-full shadow-sm z-20">
                {displayQuantity}
              </div>
            )}

            {/* Item icon */}
            <div className="w-18 h-16 flex items-center justify-center z-10 mt-1">
              {inventoryItem.icon && inventoryItem.icon.startsWith('fa-') ? (
                <i className={`fas ${inventoryItem.icon} text-3xl text-neutral-200`} />
              ) : inventoryItem.icon ? (
                <img src={inventoryItem.icon} alt={inventoryItem.label} className="max-w-full max-h-full object-contain" />
              ) : (
                <i className="fas fa-question-circle text-3xl text-neutral-400" />
              )}
            </div>            {/* Durability bar */}
            {typeof inventoryItem.currentDurability === 'number' && 
             typeof inventoryItem.maxDurability === 'number' && 
             inventoryItem.maxDurability > 0 && (
              <div className="absolute top-0 right-0 w-1 h-full bg-neutral-700/50 rounded-r-lg overflow-hidden z-20">
                <div
                  className="w-full bg-green-500/90 shadow-sm shadow-green-400/20 transition-all duration-300 ease-out absolute bottom-0 rounded-r-lg"
                  style={{ height: `${(inventoryItem.currentDurability / inventoryItem.maxDurability) * 100}%` }}
                />
              </div>
            )}

            {/* Item Label */}
            <div className="absolute bottom-0 left-0 right-0 z-25 slot-label-container">
              <div className="absolute bottom-0 left-0 right-0 h-0.5 slot-label-accent" />
              <div className="absolute inset-0 slot-label-gradient" />
              <div className="relative px-2 py-1">
                <p className="slot-label-text text-neutral-200 text-[10px] font-medium text-center truncate leading-tight">
                  {inventoryItem.label}
                </p>
              </div>
            </div>
          </>
        )}

        {/* Empty slot indicator */}
        {!inventoryItem && !slotType && (
          <div className="text-neutral-600 text-sm opacity-0"></div>
        )}
      </div>

      {/* Key binding indicator */}
      {keyBinding && (
        <span className="mt-2 px-1.5 py-0.5 rounded bg-green-900/60 border border-green-700/30 text-green-300 text-xs font-medium shadow-none tracking-widest select-none opacity-90 pointer-events-none w-6 h-6 flex items-center justify-center">
          {keyBinding}
        </span>
      )}
    </div>
  );
};

export default PreviewSlot;
