/**
 * Banking App - Server Side
 *
 * This file handles server-side functionality for the Banking app.
 * It integrates with both QBCore and QBox frameworks for banking operations.
 */

import config from '@shared/config';

// Account types are defined in the DEFAULT_ACCOUNT_COLORS and DEFAULT_ACCOUNT_ICONS objects

// Default account colors
const DEFAULT_ACCOUNT_COLORS = {
    checking: 'emerald',
    savings: 'blue',
    credit: 'purple'
};

// Default account icons
const DEFAULT_ACCOUNT_ICONS = {
    checking: 'wallet',
    savings: 'piggy-bank',
    credit: 'credit-card'
};

/**
 * Initialize the banking app
 */
export function initializeBankingApp(): void {
    // Register server events
    registerServerEvents();

    // Ensure database tables exist
    ensureDatabaseTables();
    
    // Listen for player loaded event to ensure banking data is available
    onNet('hm-core:playerLoaded', async (source: number) => {
        console.log(`[Banking] Player ${source} loaded, ensuring banking data is available`);
        
        // Initialize player data if needed
        await ensurePlayerBankingData(source);
    });
}

/**
 * Ensure player has banking data initialized
 */
async function ensurePlayerBankingData(source: number): Promise<void> {
    try {
        const player = global.getServerPlayerData(source);
        if (!player) return;

        const identifier = player.stateid || player.identifier;
        if (!identifier) return;

        // Check if player has banking accounts available
        const accounts = await getBankAccounts(source, identifier);
        console.log(`[Banking] Player ${source} has ${accounts.length} bank accounts`);
    } catch (error) {
        console.error(`[Banking] Error ensuring player banking data:`, error);
    }
}

/**
 * Register server events for the banking app
 */
function registerServerEvents(): void {
    // Register event for getting bank accounts
    onNet('hm-phone:getBankAccounts', async () => {
        const source = global.source;
        console.log(`[Banking] Received getBankAccounts event from player ${source}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Banking] Player ${source} not found`);
                emitNet('hm-phone:bankingError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Banking] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:bankingError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get accounts for the player
            const accounts = await getBankAccounts(source, playerIdentifier);

            // Send the accounts back to the client
            emitNet('hm-phone:bankAccounts', source, accounts);
        } catch (error) {
            console.error('[Banking] Error getting bank accounts:', error);
            emitNet('hm-phone:bankingError', source, 'Failed to get bank accounts');
        }
    });

    // Register event for getting bank transactions
    onNet('hm-phone:getBankTransactions', async (accountId: number) => {
        const source = global.source;
        console.log(`[Banking] Received getBankTransactions event from player ${source} for account ${accountId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Banking] Player ${source} not found`);
                emitNet('hm-phone:bankingError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Banking] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:bankingError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Get transactions for the account
            const transactions = await getBankTransactions(source, playerIdentifier, accountId);

            // Send the transactions back to the client
            emitNet('hm-phone:bankTransactions', source, transactions);
        } catch (error) {
            console.error('[Banking] Error getting bank transactions:', error);
            emitNet('hm-phone:bankingError', source, 'Failed to get bank transactions');
        }
    });

    // Register event for making a transfer
    onNet(
        'hm-phone:makeTransfer',
        async (fromAccountId: number, toAccountId: number, amount: number, description: string) => {
            const source = global.source;
            console.log(`[Banking] Received makeTransfer event from player ${source}`);

            try {
                // Get player data
                const player = global.getServerPlayerData(source);
                if (!player) {
                    console.error(`[Banking] Player ${source} not found`);
                    emitNet('hm-phone:bankingError', source, 'Player not found');
                    return;
                }

                // Use stateid if available, fall back to identifier
                const stateid = player.stateid;
                const identifier = player.identifier;

                if (!stateid && !identifier) {
                    console.error(`[Banking] Player ${source} has no stateid or identifier`);
                    emitNet('hm-phone:bankingError', source, 'Player has no stateid or identifier');
                    return;
                }

                // Prefer stateid for multi-character support
                const playerIdentifier = stateid || identifier;

                // Make the transfer
                const result = await makeTransfer(
                    source,
                    playerIdentifier,
                    fromAccountId,
                    toAccountId,
                    amount,
                    description
                );

                // Send the result back to the client
                emitNet('hm-phone:transactionResult', source, result);
            } catch (error) {
                console.error('[Banking] Error making transfer:', error);
                emitNet('hm-phone:bankingError', source, 'Failed to make transfer');
            }
        }
    );

    // Register event for making a payment
    onNet(
        'hm-phone:makePayment',
        async (fromAccountId: number, toPhone: string, amount: number, description: string) => {
            const source = global.source;
            console.log(`[Banking] Received makePayment event from player ${source}`);

            try {
                // Get player data
                const player = global.getServerPlayerData(source);
                if (!player) {
                    console.error(`[Banking] Player ${source} not found`);
                    emitNet('hm-phone:bankingError', source, 'Player not found');
                    return;
                }

                // Use stateid if available, fall back to identifier
                const stateid = player.stateid;
                const identifier = player.identifier;

                if (!stateid && !identifier) {
                    console.error(`[Banking] Player ${source} has no stateid or identifier`);
                    emitNet('hm-phone:bankingError', source, 'Player has no stateid or identifier');
                    return;
                }

                // Prefer stateid for multi-character support
                const playerIdentifier = stateid || identifier;

                // Make the payment
                const result = await makePayment(source, playerIdentifier, fromAccountId, toPhone, amount, description);

                // Send the result back to the client
                emitNet('hm-phone:transactionResult', source, result);
            } catch (error) {
                console.error('[Banking] Error making payment:', error);
                emitNet('hm-phone:bankingError', source, 'Failed to make payment');
            }
        }
    );

    // Register event for refreshing bank account
    onNet('hm-phone:refreshBankAccount', async (accountId: number) => {
        const source = global.source;
        console.log(`[Banking] Received refreshBankAccount event from player ${source} for account ${accountId}`);

        try {
            // Get player data
            const player = global.getServerPlayerData(source);
            if (!player) {
                console.error(`[Banking] Player ${source} not found`);
                emitNet('hm-phone:bankingError', source, 'Player not found');
                return;
            }

            // Use stateid if available, fall back to identifier
            const stateid = player.stateid;
            const identifier = player.identifier;

            if (!stateid && !identifier) {
                console.error(`[Banking] Player ${source} has no stateid or identifier`);
                emitNet('hm-phone:bankingError', source, 'Player has no stateid or identifier');
                return;
            }

            // Prefer stateid for multi-character support
            const playerIdentifier = stateid || identifier;

            // Refresh the account
            const accounts = await getBankAccounts(source, playerIdentifier);

            // Send the accounts back to the client
            emitNet('hm-phone:bankAccounts', source, accounts);
        } catch (error) {
            console.error('[Banking] Error refreshing bank account:', error);
            emitNet('hm-phone:bankingError', source, 'Failed to refresh bank account');
        }
    });
}

/**
 * Ensure database tables exist
 */
function ensureDatabaseTables(): void {
    // Check if auto-create tables is enabled in config
    if (!config.general.database.autoCreateTables) {
        console.log('[Banking] Auto-create tables is disabled, skipping table creation');
        return;
    }

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            console.error('[Banking] oxmysql is not available, skipping table creation');
            return;
        }

        // Check if query method exists
        if (typeof global.exports.oxmysql.query === 'function') {
            // Create the phone_banking_transactions table if it doesn't exist
            global.exports.oxmysql.query(
                `
                CREATE TABLE IF NOT EXISTS phone_banking_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    account_id INT NOT NULL,
                    merchant_name VARCHAR(255) NOT NULL,
                    amount DECIMAL(10, 2) NOT NULL,
                    type ENUM('debit', 'credit') NOT NULL,
                    description TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_identifier (identifier),
                    INDEX idx_account_id (account_id)
                )
            `,
                [],
                () => {
                    console.log('[Banking] Database tables initialized');
                }
            );
        } else {
            console.error('[Banking] oxmysql.query method not found, skipping table creation');
        }
    } catch (error: any) {
        console.error('[Banking] Error creating database tables:', error?.message || 'Unknown error');
    }
}

/**
 * Get bank accounts for a player
 * @param source Player source
 * @param identifier Player identifier
 * @returns Array of bank accounts
 */
async function getBankAccounts(source: number, identifier: string): Promise<any[]> {
    console.log(`[Banking] Getting bank accounts for player ${identifier}`);

    try {
        // Get player money based on framework
        let bankBalance = 0;

        try {
            // Check which framework is available
            if (global.exports['qb-core']) {
                // QBCore
                const QBCore = global.exports['qb-core'].GetCoreObject();
                const Player = QBCore.Functions.GetPlayer(source);
                if (Player) {
                    bankBalance = Player.Functions.GetMoney('bank');
                }
            } else if (global.exports['qbx_core']) {
                // QBox
                bankBalance = global.exports['qbx_core'].GetMoney(source, 'bank');
            }
        } catch (error) {
            console.error('[Banking] Error getting bank balance:', error);
        }

        // Create default accounts
        const accounts = [
            {
                id: 1,
                name: 'Main Checking',
                type: 'checking',
                number: `****${Math.floor(1000 + Math.random() * 9000)}`,
                balance: bankBalance,
                currency: 'USD',
                color: DEFAULT_ACCOUNT_COLORS.checking,
                icon: DEFAULT_ACCOUNT_ICONS.checking
            }
        ];

        return accounts;
    } catch (error: any) {
        console.error('[Banking] Error getting bank accounts:', error);
        throw new Error(`Failed to get bank accounts: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Get bank transactions for a player's account
 * @param _source Player source (unused)
 * @param identifier Player identifier
 * @param accountId Account ID
 * @returns Array of transactions
 */
async function getBankTransactions(_source: number | null, identifier: string, accountId: number): Promise<any[]> {
    console.log(`[Banking] Getting bank transactions for player ${identifier}, account ${accountId}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let transactions = [];

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.query_async === 'function') {
            // Use query_async if available
            transactions = await global.exports.oxmysql.query_async(
                `
                SELECT * FROM phone_banking_transactions
                WHERE identifier = ? AND account_id = ?
                ORDER BY timestamp DESC
                LIMIT 20
            `,
                [identifier, accountId]
            );
        } else if (typeof global.exports.oxmysql.query === 'function') {
            // Fall back to query if query_async is not available
            transactions = await new Promise<any[]>(resolve => {
                global.exports.oxmysql.query(
                    `
                    SELECT * FROM phone_banking_transactions
                    WHERE identifier = ? AND account_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 20
                `,
                    [identifier, accountId],
                    (result: any) => {
                        resolve(result || []);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database query method found');
        }

        // If no transactions found, return empty array
        if (!transactions || transactions.length === 0) {
            // Create some mock transactions for testing
            return createMockTransactions(accountId);
        }

        // Format transactions for the UI
        return transactions.map((transaction: any) => ({
            id: transaction.id,
            merchantName: transaction.merchant_name,
            amount: parseFloat(transaction.amount),
            timestamp: transaction.timestamp,
            type: transaction.type,
            description: transaction.description,
            accountId: transaction.account_id
        }));
    } catch (error: any) {
        console.error('[Banking] Error getting bank transactions:', error);
        throw new Error(`Failed to get bank transactions: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Make a transfer between accounts
 * @param source Player source
 * @param identifier Player identifier
 * @param fromAccountId From account ID
 * @param toAccountId To account ID
 * @param amount Amount to transfer
 * @param description Transfer description
 * @returns Result of the transfer
 */
async function makeTransfer(
    source: number,
    identifier: string,
    fromAccountId: number,
    toAccountId: number,
    amount: number,
    description: string
): Promise<any> {
    console.log(
        `[Banking] Making transfer for player ${identifier} from account ${fromAccountId} to account ${toAccountId}`
    );

    try {
        // Validate amount
        if (amount <= 0) {
            throw new Error('Amount must be greater than 0');
        }

        // Get accounts
        const accounts = await getBankAccounts(source, identifier);

        // Find from account
        const fromAccount = accounts.find(account => account.id === fromAccountId);
        if (!fromAccount) {
            throw new Error('From account not found');
        }

        // Find to account
        const toAccount = accounts.find(account => account.id === toAccountId);
        if (!toAccount) {
            throw new Error('To account not found');
        }

        // Check if from account has enough balance
        if (fromAccount.balance < amount) {
            throw new Error('Insufficient funds');
        }

        // Create debit transaction
        await createTransaction(
            identifier,
            fromAccountId,
            'Internal Transfer',
            amount,
            'debit',
            `Transfer to ${toAccount.name}: ${description}`
        );

        // Create credit transaction
        await createTransaction(
            identifier,
            toAccountId,
            'Internal Transfer',
            amount,
            'credit',
            `Transfer from ${fromAccount.name}: ${description}`
        );

        return {
            success: true,
            message: `Successfully transferred $${amount.toFixed(2)} to ${toAccount.name}`,
            fromAccount: {
                id: fromAccount.id,
                balance: fromAccount.balance - amount
            },
            toAccount: {
                id: toAccount.id,
                balance: toAccount.balance + amount
            }
        };
    } catch (error: any) {
        console.error('[Banking] Error making transfer:', error);
        throw new Error(`Failed to make transfer: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Make a payment to another player
 * @param source Player source
 * @param identifier Player identifier
 * @param fromAccountId From account ID
 * @param toPhone To player phone number
 * @param amount Amount to transfer
 * @param description Transfer description
 * @returns Result of the payment
 */
async function makePayment(
    source: number,
    identifier: string,
    fromAccountId: number,
    toPhone: string,
    amount: number,
    description: string
): Promise<any> {
    console.log(`[Banking] Making payment for player ${identifier} from account ${fromAccountId} to phone ${toPhone}`);

    try {
        // Validate amount
        if (amount <= 0) {
            throw new Error('Amount must be greater than 0');
        }

        // Get accounts
        const accounts = await getBankAccounts(source, identifier);

        // Find from account
        const fromAccount = accounts.find(account => account.id === fromAccountId);
        if (!fromAccount) {
            throw new Error('From account not found');
        }

        // Check if from account has enough balance
        if (fromAccount.balance < amount) {
            throw new Error('Insufficient funds');
        }

        // Find target player by phone number
        let targetPlayer = null;
        let targetSource = null;

        try {
            // Check which framework is available
            if (global.exports['qb-core']) {
                // QBCore
                const QBCore = global.exports['qb-core'].GetCoreObject();
                const players = QBCore.Functions.GetPlayers();

                for (const playerSource of players) {
                    const player = QBCore.Functions.GetPlayer(playerSource);
                    if (player && player.PlayerData.charinfo.phone === toPhone) {
                        targetPlayer = player;
                        targetSource = playerSource;
                        break;
                    }
                }
            } else if (global.exports['qbx_core']) {
                // QBox
                const players = global.exports['qbx_core'].GetPlayers();

                for (const playerSource of players) {
                    const player = global.exports['qbx_core'].GetPlayer(playerSource);
                    if (player && player.PlayerData.charinfo.phone === toPhone) {
                        targetPlayer = player;
                        targetSource = playerSource;
                        break;
                    }
                }
            }
        } catch (error) {
            console.error('[Banking] Error finding target player:', error);
        }

        if (!targetPlayer || !targetSource) {
            throw new Error('Player with that phone number not found or not online');
        }

        // Get target player identifier
        let targetIdentifier = '';

        // Extract identifier from target player
        if (targetPlayer.PlayerData && targetPlayer.PlayerData.stateid) {
            targetIdentifier = targetPlayer.PlayerData.stateid;
        } else if (targetPlayer.PlayerData && targetPlayer.PlayerData.license) {
            targetIdentifier = targetPlayer.PlayerData.license;
        }

        if (!targetIdentifier) {
            throw new Error('Target player identifier not found');
        }

        // Add money to target player
        let success = false;

        try {
            // Check which framework is available
            if (global.exports['qb-core']) {
                // QBCore
                success = targetPlayer.Functions.AddMoney(
                    'bank',
                    amount,
                    `Payment from ${fromAccount.name}: ${description}`
                );
            } else if (global.exports['qbx_core']) {
                // QBox
                success = global.exports['qbx_core'].AddMoney(
                    targetSource,
                    'bank',
                    amount,
                    `Payment from ${fromAccount.name}: ${description}`
                );
            }
        } catch (error) {
            console.error('[Banking] Error adding money to target player:', error);
        }

        if (!success) {
            throw new Error('Failed to add money to target player');
        }

        // Remove money from source player
        success = false;

        try {
            // Check which framework is available
            if (global.exports['qb-core']) {
                // QBCore
                const QBCore = global.exports['qb-core'].GetCoreObject();
                const Player = QBCore.Functions.GetPlayer(source);
                success = Player.Functions.RemoveMoney('bank', amount, `Payment to ${toPhone}: ${description}`);
            } else if (global.exports['qbx_core']) {
                // QBox
                success = global.exports['qbx_core'].RemoveMoney(
                    source,
                    'bank',
                    amount,
                    `Payment to ${toPhone}: ${description}`
                );
            }
        } catch (error) {
            console.error('[Banking] Error removing money from source player:', error);
        }

        if (!success) {
            // Rollback the transaction
            try {
                if (global.exports['qb-core']) {
                    // QBCore
                    targetPlayer.Functions.RemoveMoney('bank', amount, 'Payment rollback');
                } else if (global.exports['qbx_core']) {
                    // QBox
                    global.exports['qbx_core'].RemoveMoney(targetSource, 'bank', amount, 'Payment rollback');
                }
            } catch (error) {
                console.error('[Banking] Error rolling back transaction:', error);
            }

            throw new Error('Failed to remove money from source player');
        }

        // Create debit transaction for source player
        await createTransaction(identifier, fromAccountId, `Payment to ${toPhone}`, amount, 'debit', description);

        // Create credit transaction for target player
        // Note: We're not creating a transaction for the target player in the database
        // since they might not have the phone app installed

        // Notify target player
        emitNet('hm-phone:paymentReceived', targetSource, {
            from: fromAccount.name,
            amount,
            description
        });

        return {
            success: true,
            message: `Successfully sent $${amount.toFixed(2)} to ${toPhone}`,
            fromAccount: {
                id: fromAccount.id,
                balance: fromAccount.balance - amount
            }
        };
    } catch (error: any) {
        console.error('[Banking] Error making payment:', error);
        throw new Error(`Failed to make payment: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create a transaction in the database
 * @param identifier Player identifier
 * @param accountId Account ID
 * @param merchantName Merchant name
 * @param amount Amount
 * @param type Transaction type
 * @param description Transaction description
 * @returns Transaction ID
 */
async function createTransaction(
    identifier: string,
    accountId: number,
    merchantName: string,
    amount: number,
    type: 'debit' | 'credit',
    description: string
): Promise<number> {
    console.log(`[Banking] Creating transaction for player ${identifier}, account ${accountId}`);

    try {
        // Check if oxmysql is available
        if (!global.exports.oxmysql) {
            throw new Error('oxmysql is not available');
        }

        let transactionId = 0;

        // Check if query_async method exists
        if (typeof global.exports.oxmysql.insert_async === 'function') {
            // Use insert_async if available
            transactionId = await global.exports.oxmysql.insert_async(
                `
                INSERT INTO phone_banking_transactions
                (identifier, account_id, merchant_name, amount, type, description)
                VALUES (?, ?, ?, ?, ?, ?)
            `,
                [identifier, accountId, merchantName, amount, type, description]
            );
        } else if (typeof global.exports.oxmysql.insert === 'function') {
            // Fall back to insert if insert_async is not available
            transactionId = await new Promise<number>(resolve => {
                global.exports.oxmysql.insert(
                    `
                    INSERT INTO phone_banking_transactions
                    (identifier, account_id, merchant_name, amount, type, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                `,
                    [identifier, accountId, merchantName, amount, type, description],
                    (result: any) => {
                        resolve(result || 0);
                    }
                );
            });
        } else {
            // If neither method is available, throw an error
            throw new Error('No suitable database insert method found');
        }

        return transactionId;
    } catch (error: any) {
        console.error('[Banking] Error creating transaction:', error);
        throw new Error(`Failed to create transaction: ${error?.message || 'Unknown error'}`);
    }
}

/**
 * Create mock transactions for testing
 * @param accountId Account ID
 * @returns Array of mock transactions
 */
function createMockTransactions(accountId: number): any[] {
    const transactions = [];
    const now = Date.now();

    // Add more mock transactions (e.g., 15 for testing scroll and load more)
    for (let i = 1; i <= 15; i++) {
        const type = i % 3 === 0 ? 'credit' : 'debit';
        const amount = type === 'credit' ? Math.random() * 1000 + 500 : Math.random() * 200 + 10;
        transactions.push({
            id: i,
            merchantName: `Mock Merchant ${i}`,
            amount: parseFloat(amount.toFixed(2)),
            timestamp: new Date(now - 1000 * 60 * 60 * 24 * i).toISOString(), // Spread out over days
            type: type,
            description: `Mock transaction ${i} for account ${accountId}`,
            accountId
        });
    }

    return transactions;
}