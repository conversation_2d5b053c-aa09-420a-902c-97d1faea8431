import React, { useState } from 'react';
import { BaseHandlingData, Vector3, AIHandlingType } from '../../../scripts/shared/types';
import HandlingField from './HandlingField';
import Vector3Field from './Vector3Field';
import FlagField from './FlagField';

interface HandlingPanelProps {
  currentHandling: Partial<BaseHandlingData>;
  stockHandling: Partial<BaseHandlingData>;
  onValueChange: (field: keyof BaseHandlingData, value: number) => void;
  onReset: (field: keyof BaseHandlingData) => void;
  onVector3Change: (field: string, value: Vector3) => void;
  onVector3Reset: (field: string) => void;
  onStringChange?: (field: keyof BaseHandlingData, value: string) => void;
  onApplyField?: (field: keyof BaseHandlingData) => void;
  modifiedFields?: string[];
  appliedFields?: string[];
}

type CategoryType = 'main' | 'strModelFlags' | 'strHandlingFlags' | 'strDamageFlags';

const HandlingPanel: React.FC<HandlingPanelProps> = ({
  currentHandling,
  stockHandling,
  onValueChange,
  onReset,
  onVector3Change,
  onVector3Reset,
  onStringChange,
  onApplyField,
  modifiedFields = [],
  appliedFields = []
}) => {
  const [selectedCategory, setSelectedCategory] = useState<CategoryType>('main');

  const categories = [
    { id: 'main', name: 'Main Settings' },
    { id: 'strModelFlags', name: 'Model Flags' },
    { id: 'strHandlingFlags', name: 'Handling Flags' },
    { id: 'strDamageFlags', name: 'Damage Flags' }
  ] as const;
  // All fields in exact XML schema order
  const getAllFieldsInOrder = () => {    return [
      // General fields in XML order
      { field: 'fMass', type: 'regular' },
      { field: 'fInitialDragCoeff', type: 'regular' },
      { field: 'fPercentSubmerged', type: 'regular' },
      { field: 'vecCentreOfMassOffset', type: 'vector3' },
      { field: 'vecInertiaMultiplier', type: 'vector3' },
      
      // Engine fields
      { field: 'fDriveBiasFront', type: 'regular' },
      { field: 'nInitialDriveGears', type: 'regular' },
      { field: 'fInitialDriveForce', type: 'regular' },
      { field: 'fDriveInertia', type: 'regular' },
      { field: 'fClutchChangeRateScaleUpShift', type: 'regular' },
      { field: 'fClutchChangeRateScaleDownShift', type: 'regular' },
      { field: 'fInitialDriveMaxFlatVel', type: 'regular' },
      
      // Brakes
      { field: 'fBrakeForce', type: 'regular' },
      { field: 'fBrakeBiasFront', type: 'regular' },
      { field: 'fHandBrakeForce', type: 'regular' },
      { field: 'fSteeringLock', type: 'regular' },
      
      // Traction
      { field: 'fTractionCurveMax', type: 'regular' },
      { field: 'fTractionCurveMin', type: 'regular' },
      { field: 'fTractionCurveLateral', type: 'regular' },
      { field: 'fTractionSpringDeltaMax', type: 'regular' },
      { field: 'fLowSpeedTractionLossMult', type: 'regular' },
      { field: 'fCamberStiffnesss', type: 'regular' },
      { field: 'fTractionBiasFront', type: 'regular' },
      { field: 'fTractionLossMult', type: 'regular' },
      
      // Suspension
      { field: 'fSuspensionForce', type: 'regular' },
      { field: 'fSuspensionCompDamp', type: 'regular' },
      { field: 'fSuspensionReboundDamp', type: 'regular' },
      { field: 'fSuspensionUpperLimit', type: 'regular' },
      { field: 'fSuspensionLowerLimit', type: 'regular' },
      { field: 'fSuspensionRaise', type: 'regular' },
      { field: 'fSuspensionBiasFront', type: 'regular' },
      { field: 'fAntiRollBarForce', type: 'regular' },
      { field: 'fAntiRollBarBiasFront', type: 'regular' },
      { field: 'fRollCentreHeightFront', type: 'regular' },
      { field: 'fRollCentreHeightRear', type: 'regular' },
      
      // Damage
      { field: 'fCollisionDamageMult', type: 'regular' },
      { field: 'fWeaponDamageMult', type: 'regular' },
      { field: 'fDeformationDamageMult', type: 'regular' },
      { field: 'fEngineDamageMult', type: 'regular' },
      { field: 'fPetrolTankVolume', type: 'regular' },
      { field: 'fOilVolume', type: 'regular' },
      { field: 'fPetrolConsumptionRate', type: 'regular' },
      
      // Misc (seat offsets and monetary value)      { field: 'fSeatOffsetDistX', type: 'regular' },
      { field: 'fSeatOffsetDistY', type: 'regular' },
      { field: 'fSeatOffsetDistZ', type: 'regular' },
      { field: 'nMonetaryValue', type: 'regular' },
      
      // Final fields
      { field: 'fWeaponDamageScaledToVehHealthMult', type: 'regular' },
      { field: 'fDownforceModifier', type: 'regular' },
      { field: 'fPopUpLightRotation', type: 'regular' },
      { field: 'fRocketBoostCapacity', type: 'regular' },
      { field: 'fBoostMaxSpeed', type: 'regular' }
    ];
  };
  const fieldsInOrder = getAllFieldsInOrder();

  const renderField = (fieldName: keyof BaseHandlingData, fieldType: 'regular' | 'vector3' = 'regular') => {
    if (fieldType === 'vector3') {
      const currentVector3 = currentHandling[fieldName] as Vector3;
      const stockVector3 = stockHandling[fieldName] as Vector3;
      
      return (
        <Vector3Field
          key={fieldName}
          field={fieldName}
          currentValue={currentVector3}
          stockValue={stockVector3}
          onValueChange={onVector3Change}
          onReset={onVector3Reset}
        />
      );
    } else {
      const currentVal = currentHandling[fieldName];
      const stockVal = stockHandling[fieldName];      return (
        <HandlingField
          key={fieldName}
          field={fieldName}
          currentValue={currentVal as number | string}
          stockValue={stockVal as number | string}
          onValueChange={onValueChange}
          onReset={onReset}
          onStringChange={onStringChange}
          onApplyField={onApplyField}
          isModified={modifiedFields.includes(fieldName)}
          isApplied={appliedFields.includes(fieldName)}
        />
      );
    }
  };

  // Render AIHandling dropdown
  const renderAIHandlingField = () => {
    const currentValue = currentHandling.AIHandling as string || 'AVERAGE';
    const stockValue = stockHandling.AIHandling as string || 'AVERAGE';
    
    return (
      <div key="AIHandling" className="handling-field-compact">
        <button
          className="reset-btn"
          onClick={() => onReset('AIHandling')}
          title="Reset to stock value"
        >
          ↻
        </button>
        <span className="field-name">AIHandling</span>
        <span className="stock-value">{stockValue}</span>
        <select
          className="current-value-input"
          value={currentValue}
          onChange={(e) => onStringChange && onStringChange('AIHandling', e.target.value)}
        >
          {Object.values(AIHandlingType).map(type => (
            <option key={type} value={type}>{type}</option>
          ))}
        </select>
        <button
          className="confirm-btn"
          onClick={() => console.log(`Confirmed AIHandling: ${currentValue}`)}
          title="Confirm changes"
        >
          ✓
        </button>
      </div>
    );
  };  const renderCategoryContent = () => {
    switch (selectedCategory) {
      case 'strModelFlags':
        return (
          <div className="flag-category-content">
            <FlagField
              field="strModelFlags"
              currentValue={currentHandling.strModelFlags as string || '0'}
              stockValue={stockHandling.strModelFlags as string || '0'}
              onValueChange={(field, value) => onStringChange && onStringChange(field, value)}
              onReset={onReset}
            />
          </div>
        );
      
      case 'strHandlingFlags':
        return (
          <div className="flag-category-content">
            <FlagField
              field="strHandlingFlags"
              currentValue={currentHandling.strHandlingFlags as string || '0'}
              stockValue={stockHandling.strHandlingFlags as string || '0'}
              onValueChange={(field, value) => onStringChange && onStringChange(field, value)}
              onReset={onReset}
            />
          </div>
        );
      
      case 'strDamageFlags':
        return (
          <div className="flag-category-content">
            <FlagField
              field="strDamageFlags"
              currentValue={currentHandling.strDamageFlags as string || '0'}
              stockValue={stockHandling.strDamageFlags as string || '0'}
              onValueChange={(field, value) => onStringChange && onStringChange(field, value)}
              onReset={onReset}
            />
          </div>
        );
      
      case 'main':
      default:
        return (
          <div className="main-fields">
            {fieldsInOrder.map((fieldInfo) => {
              const fieldName = fieldInfo.field as keyof BaseHandlingData;
              const fieldType = fieldInfo.type as 'regular' | 'vector3';
              return renderField(fieldName, fieldType);
            })}
            {renderAIHandlingField()}
          </div>
        );
    }
  };

  return (
    <div className="handling-panel">
      {/* Category Selection */}
      <div className="category-selector">
        {categories.map(category => (
          <button
            key={category.id}
            className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
            onClick={() => setSelectedCategory(category.id as CategoryType)}
          >
            {category.name}
          </button>
        ))}
      </div>
      
      {/* Category Content */}
      <div className="handling-fields">
        {renderCategoryContent()}
      </div>
    </div>
  );
};

export default HandlingPanel;

