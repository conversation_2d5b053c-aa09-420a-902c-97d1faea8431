import React from 'react';
import { motion } from 'framer-motion';
import { useNotificationStore } from '../../../../notifications/stores/notificationStore';
import { useAppStore } from '../../../../common/stores/appStore';
import { slideTransition, staggeredAnimation, fadeTransition } from '../../../../utils/ui';

interface NotificationSettingsScreenProps {
  onBack: () => void;
}

interface AppType {
  id: number;
  name: string;
  icon: string;
  type: string;
  colors: {
    bg: string;
    text: string;
  };
}

const NotificationSettingsScreen: React.FC<NotificationSettingsScreenProps> = ({ onBack }) => {
  const {
    notificationSettings,
    toggleDoNotDisturb
  } = useNotificationStore();

  const { apps } = useAppStore();

  // Filter apps that should have notifications
  const notificationApps = apps.filter((app: AppType) =>
    [
      // System apps
      'Messages',
      'Contacts',
      'Phone',

      // Store apps
      'Banking',
      'Yellow Pages',
      'Dark Market',
      'LifeSnap',
      'LoveLink',
      'Job Center'
    ].includes(app.name)
  );

  // Group apps by type
  const systemApps = notificationApps.filter((app: AppType) => app.type === 'system');
  const storeApps = notificationApps.filter((app: AppType) => app.type === 'store');

  // Toggle notification for an app
  const toggleNotification = (appId: number) => {
    console.log(`Toggled notifications for app ${appId}`);
  };

  // Render app toggle item
  const renderAppToggle = (app: AppType, index: number) => (
    <motion.div
      key={app.id}
      className="w-full p-2.5 border-b border-white/10 flex items-center justify-between text-white"
      variants={staggeredAnimation().child}
      custom={index}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ delay: index * 0.05 }}
    >
      <div className="flex items-center gap-3">
        <div className={`w-8 h-8 rounded-lg ${app.colors.bg} flex items-center justify-center`}>
          <i className={`fas fa-${app.icon} text-white text-sm`}></i>
        </div>
        <span className="text-sm">{app.name}</span>
      </div>
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          className="sr-only peer"
          defaultChecked={true}
          onChange={() => toggleNotification(app.id)}
        />
        <div className="w-10 h-5 bg-gray-700 peer-focus:ring-blue-800 peer-focus:outline-none peer-focus:ring-2 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
      </label>
    </motion.div>
  );

  // Main container with pt-8 for topbar padding
  return (
    <motion.div
      className="h-full w-full flex flex-col bg-[#0a0f1a] pt-8"
      {...slideTransition('right')}
    >
      {/* Header */}
      <motion.div
        className="flex items-center gap-2 px-3 py-2 bg-[#0a0f1a] border-b border-white/10 sticky top-0 z-10"
        {...fadeTransition(0.2)}
      >
        <motion.button
          onClick={onBack}
          className="flex items-center text-white/80 hover:text-white"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <i className="fas fa-arrow-left text-lg"></i>
        </motion.button>
        <span className="text-white">Notifications</span>
      </motion.div>

      {/* Global Settings */}
      <motion.div
        className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03]"
        {...fadeTransition(0.3)}
      >
        GLOBAL SETTINGS
      </motion.div>

      {/* Do Not Disturb Toggle */}
      <motion.div
        className="w-full p-2.5 border-b border-white/10 flex items-center justify-between text-white"
        variants={staggeredAnimation().child}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-red-500/20 flex items-center justify-center">
            <i className="fas fa-moon text-white text-sm"></i>
          </div>
          <div>
            <span className="text-sm">Do Not Disturb</span>
            <p className="text-xs text-white/50">Mute all notifications</p>
          </div>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={notificationSettings.doNotDisturb}
            onChange={toggleDoNotDisturb}
          />
          <div className="w-10 h-5 bg-gray-700 peer-focus:ring-blue-800 peer-focus:outline-none peer-focus:ring-2 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </motion.div>

      {/* Group Notifications Toggle */}
      <motion.div
        className="w-full p-2.5 border-b border-white/10 flex items-center justify-between text-white"
        variants={staggeredAnimation().child}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center">
            <i className="fas fa-layer-group text-white text-sm"></i>
          </div>
          <div>
            <span className="text-sm">Group Similar</span>
            <p className="text-xs text-white/50">Combine related notifications</p>
          </div>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={notificationSettings.groupSimilarNotifications}
            onChange={() => {
              useNotificationStore.getState().updateNotificationSettings({
                groupSimilarNotifications: !notificationSettings.groupSimilarNotifications
              });
            }}
          />
          <div className="w-10 h-5 bg-gray-700 peer-focus:ring-blue-800 peer-focus:outline-none peer-focus:ring-2 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </motion.div>

      {/* Apps List */}
      <motion.div
        className="flex-grow overflow-y-auto"
        variants={staggeredAnimation().parent}
        initial="initial"
        animate="animate"
      >
        {/* System Apps Section */}
        <motion.div
          className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03] mt-2"
          {...fadeTransition(0.3)}
        >
          SYSTEM APPS
        </motion.div>
        {systemApps.map((app: AppType, index: number) => renderAppToggle(app, index))}

        {/* Store Apps Section */}
        {storeApps.length > 0 && (
          <>
            <motion.div
              className="px-3 py-2 text-xs text-white/50 font-medium bg-white/[0.03] mt-2"
              {...fadeTransition(0.4)}
            >
              INSTALLED APPS
            </motion.div>
            {storeApps.map((app: AppType, index: number) => renderAppToggle(app, index + systemApps.length))}
          </>
        )}
      </motion.div>
    </motion.div>
  );
};

export default NotificationSettingsScreen;