import { CraftingRecipe } from '../types/crafting.types';

/**
 * All available crafting recipes.
 * Streamlined format with minimal properties.
 */
export const craftingRecipes: Record<string, CraftingRecipe> = {
    bandage: {
        id: 'bandage',
        craftingTime: 3,
        quantity: 3,
        ingredients: [{ itemId: 'cloth', quantity: 4 }]
    },
    // lockpick
    lockpick: {
        id: 'lockpick',
        craftingTime: 5,
        quantity: 1,
        ingredients: [{ itemId: 'metal_fragment', quantity: 2 }],
        requiresWorkbench: true
    },

    metal_fragment: {
        id: 'metal_fragment',
        craftingTime: 6,
        quantity: 2,
        ingredients: [{ itemId: 'metal_ore', quantity: 3 }],
        requiresWorkbench: true
    },

    // Steel plate recipe
    steel_plate: {
        id: 'steel_plate',
        craftingTime: 10,
        quantity: 1,
        ingredients: [
            { itemId: 'metal_fragment', quantity: 5 },
            { itemId: 'iron_ingot', quantity: 2 }
        ],
        requiresWorkbench: true
    }
};

/**
 * Get a recipe by ID.
 */
export function getRecipe(id: string): CraftingRecipe | undefined {
    return craftingRecipes[id];
}

/**
 * Check if player has required items for a recipe.
 */
export function canCraftRecipe(recipe: CraftingRecipe, playerItems: Record<string, number>): boolean {
    return recipe.ingredients.every(ingredient => {
        const availableQuantity = playerItems[ingredient.itemId] || 0;
        return availableQuantity >= ingredient.quantity;
    });
}
