import React from 'react';
import { useNavigation } from '../../../navigation/hooks';
import { useMusicStore } from '../stores/musicStore';
import { SongCardProps } from '../types/musicTypes';

const SongCard: React.FC<SongCardProps> = ({
  song,
  index,
  showIndex = false,
  showDuration = false,
  showPlays = false,
  compact = false,
  onClick
}) => {
  const { openView } = useNavigation();
  const { setCurrentSong, currentSong, isPlaying, togglePlay, setCurrentArtistId } =
    useMusicStore();

  const isCurrentSong = currentSong?.id === song.id;
  const artistName = song.artist?.name || song.artistName || 'Unknown Artist';
  const artistId = song.artist?.id || 0;

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      openView('nowPlaying', { songId: song.id });
    }
  };

  const handlePlayClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (isCurrentSong) {
      togglePlay();
    } else {
      // Convert to the format expected by the store
      const fullSong = {
        id: song.id,
        title: song.title,
        artistId: artistId,
        artist: song.artist || { id: artistId, name: artistName },
        albumId: 0,
        duration: 210, // Default duration in seconds
        imageUrl: song.imageUrl
      };

      setCurrentSong(fullSong);
    }
  };

  const handleArtistClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (artistId) {
      setCurrentArtistId(artistId);
      openView('artist', { artistId });
    }
  };

  return (
    <div
      className={`flex items-center ${
        compact ? 'py-1 px-2' : 'p-2'
      } rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer group`}
      onClick={handleClick}
    >
      {showIndex && index !== undefined && (
        <div className="w-6 text-center text-gray-400 mr-2">{index + 1}</div>
      )}

      <div className="relative mr-3">
        <img
          src={song.imageUrl}
          alt={song.title}
          className={`${compact ? 'w-10 h-10' : 'w-12 h-12'} rounded-md`}
        />
        <button
          className={`absolute inset-0 flex items-center justify-center bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-md overflow-hidden`}
          onClick={handlePlayClick}
        >
          <i
            className={`fas ${isCurrentSong && isPlaying ? 'fa-pause' : 'fa-play'} text-white`}
          ></i>
        </button>
      </div>

      <div className="flex-1 min-w-0">
        <h3 className="text-sm font-medium text-white truncate">
          {song.title}
          {isCurrentSong && <span className="ml-1 text-pink-500">•</span>}
        </h3>
        <p
          className="text-xs text-gray-400 truncate cursor-pointer hover:text-pink-500"
          onClick={handleArtistClick}
        >
          {artistName}
        </p>
      </div>

      <div className="flex items-center">
        {showPlays && song.plays && (
          <span className="text-xs text-gray-400 mr-3">{song.plays}</span>
        )}

        {showDuration && song.duration && (
          <span className="text-xs text-gray-400 mr-3">{song.duration}</span>
        )}

        <button
          className="text-gray-400 hover:text-white opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={e => e.stopPropagation()}
        >
          <i className="fas fa-ellipsis-v"></i>
        </button>
      </div>
    </div>
  );
};

export default SongCard;
