/**
 * Environment detection utilities
 */

/**
 * Check if we're running in a browser (development mode)
 * @returns true if running in browser, false if in FiveM
 */
export const isBrowser = (): boolean => {
  return !window.invokeNative && !(window as any).GetParentResourceName;
};

/**
 * Check if we're running in FiveM
 * @returns true if running in FiveM, false if in browser
 */
export const isFiveM = (): boolean => {
  return !isBrowser();
};

/**
 * Get the parent resource name (only works in FiveM)
 * @returns The resource name or 'unknown' if not in FiveM
 */
export const getResourceName = (): string => {
  if (isBrowser()) {
    return 'hm-admin'; // Default for development
  }
  
  return (window as any).GetParentResourceName?.() || 'unknown';
};
