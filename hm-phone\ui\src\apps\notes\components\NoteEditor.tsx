import React, { useState, useEffect, useRef } from 'react';
import { useNotesStore, NoteAttachment } from '../stores/notesStore';

interface NoteEditorProps {
  noteId: number;
  onClose: () => void;
}

const NoteEditor: React.FC<NoteEditorProps> = ({ noteId, onClose }) => {
  const { notes, categories, addNote, updateNote, deleteNote, addAttachment, removeAttachment } =
    useNotesStore();

  // Use notes from the store
  const displayNotes = notes;

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [noteColor, setNoteColor] = useState('#4B5563');
  const [fontSize, setFontSize] = useState(16);
  const [showToolbar, setShowToolbar] = useState(false);
  const [attachments, setAttachments] = useState<NoteAttachment[]>([]);
  const [isFavorite, setIsFavorite] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (noteId !== -1) {
      const note = displayNotes?.find(n => n.id === noteId);
      if (note) {
        setTitle(note.title);
        setContent(note.content);
        setSelectedCategories(note.categories);
        setNoteColor(note.color || '#4B5563');
        setFontSize(note.fontSize || 16);
        setAttachments(note.attachments);
        setIsFavorite(note.isFavorite || false);
      }
    }
  }, [noteId, notes, displayNotes]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  const handleFormat = (command: string) => {
    document.execCommand(command, false);
  };

  const handleSave = () => {
    if (!title.trim() && !content.trim()) {
      onClose();
      return;
    }

    const noteData = {
      title: title.trim() || 'Untitled',
      content: content,
      color: noteColor,
      isPinned: false,
      categories: selectedCategories,
      attachments,
      isFavorite,
      fontSize,
      backgroundColor: 'transparent'
    };

    if (noteId === -1) {
      addNote(noteData);
    } else {
      const note = notes.find(n => n.id === noteId);
      if (note) {
        updateNote({
          ...note,
          ...noteData,
          timestamp: Date.now(),
          lastEdited: Date.now()
        });
      }
    }
    onClose();
  };

  const handleAttachment = async (file: File) => {
    const attachment: Omit<NoteAttachment, 'id'> = {
      type: file.type.startsWith('image/') ? 'image' : 'file',
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size
    };

    if (noteId !== -1) {
      addAttachment(noteId, attachment);
    } else {
      setAttachments([...attachments, { ...attachment, id: Date.now().toString() }]);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#0a0f1a]">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-white/10">
        <div className="flex items-center gap-2">
          <button onClick={handleSave} className="text-white/80 hover:text-white">
            <i className="fas fa-arrow-left text-lg" />
          </button>
          <button
            onClick={() => setShowToolbar(!showToolbar)}
            className="text-white/80 hover:text-white"
          >
            <i className="fas fa-ellipsis-v" />
          </button>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsFavorite(!isFavorite)}
            className={`${isFavorite ? 'text-yellow-500' : 'text-white/60'}`}
          >
            <i className="fas fa-star" />
          </button>

          {noteId !== -1 && (
            <button
              onClick={() => {
                deleteNote(noteId);
                onClose();
              }}
              className="text-red-500 hover:text-red-400"
            >
              <i className="fas fa-trash" />
            </button>
          )}
        </div>
      </div>

      {/* Toolbar */}
      {showToolbar && (
        <div className="px-4 py-2 border-b border-white/10 bg-white/20 backdrop-blur-sm">
          <div className="flex items-center gap-4">
            {/* Text Formatting */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleFormat('bold')}
                className="text-white/80 hover:text-white px-2"
              >
                <i className="fas fa-bold" />
              </button>
              <button
                onClick={() => handleFormat('italic')}
                className="text-white/80 hover:text-white px-2"
              >
                <i className="fas fa-italic" />
              </button>
              <button
                onClick={() => handleFormat('underline')}
                className="text-white/80 hover:text-white px-2"
              >
                <i className="fas fa-underline" />
              </button>
            </div>

            {/* Color Picker */}
            <div className="flex items-center gap-2">
              {['#4B5563', '#EF4444', '#F59E0B', '#10B981', '#3B82F6'].map(color => (
                <button
                  key={color}
                  onClick={() => setNoteColor(color)}
                  className={`w-6 h-6 rounded-full ${
                    noteColor === color ? 'ring-2 ring-white' : ''
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>

            {/* Font Size */}
            <select
              value={fontSize}
              onChange={e => setFontSize(Number(e.target.value))}
              className="bg-white/20 text-white rounded px-2 py-1 backdrop-blur-sm"
            >
              {[12, 14, 16, 18, 20, 24].map(size => (
                <option key={size} value={size}>
                  {size}px
                </option>
              ))}
            </select>

            {/* Attachment Button */}
            <button
              onClick={() => fileInputRef.current?.click()}
              className="text-white/80 hover:text-white"
            >
              <i className="fas fa-paperclip" />
            </button>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={e => e.target.files?.[0] && handleAttachment(e.target.files[0])}
            />
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-2 mt-2">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => {
                  setSelectedCategories(prev =>
                    prev.includes(category.id)
                      ? prev.filter(id => id !== category.id)
                      : [...prev, category.id]
                  );
                }}
                className={`px-3 py-1 rounded-full text-sm ${
                  selectedCategories.includes(category.id)
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/10 text-white/60'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="flex-1 px-4 overflow-y-auto">
        <input
          type="text"
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder="Title"
          className="w-full bg-transparent text-white placeholder-white/40 text-xl font-medium mb-4 focus:outline-none"
          style={{ color: noteColor }}
        />

        <textarea
          value={content}
          onChange={handleContentChange}
          placeholder="Start writing..."
          className="w-full h-full bg-transparent text-white resize-none focus:outline-none"
          style={{
            fontSize: `${fontSize}px`,
            color: noteColor,
            minHeight: '200px'
          }}
        />

        {/* Attachments */}
        {attachments.length > 0 && (
          <div className="mt-4 space-y-2">
            {attachments.map(attachment => (
              <div
                key={attachment.id}
                className="flex items-center justify-between bg-white/20 backdrop-blur-sm rounded p-2 shadow-sm"
              >
                <div className="flex items-center gap-2">
                  <i
                    className={`fas fa-${
                      attachment.type === 'image' ? 'image' : 'file'
                    } text-white/60`}
                  />
                  <span className="text-white/80">{attachment.name}</span>
                </div>
                <button
                  onClick={() => {
                    if (noteId !== -1) {
                      removeAttachment(noteId, attachment.id);
                    } else {
                      setAttachments(prev => prev.filter(a => a.id !== attachment.id));
                    }
                  }}
                  className="text-red-500 hover:text-red-400"
                >
                  <i className="fas fa-times" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NoteEditor;
