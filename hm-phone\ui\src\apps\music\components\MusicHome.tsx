import React from 'react';
import { useNavigation } from '../../../navigation/hooks';
import SongCard from './SongCard';
import { useMusicStore } from '../stores/musicStore';
import { settingsMockData } from '../../../fivem/mockData';

const MusicHome: React.FC = () => {
  const { openView } = useNavigation();
  const { artists, songs, albums, setCurrentArtistId, getUserProfileArtist, loading } =
    useMusicStore();

  // No need to load data here as it's already loaded in PhoneProvider

  // Get the user profile
  const userProfile = getUserProfileArtist(settingsMockData.playerData.identifier);

  return (
    <div className="h-full w-full flex flex-col bg-[#121212] text-white pt-[32px]">
      {/* Loading indicator */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <div className="w-12 h-12 border-4 border-pink-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      {/* Header with user info */}
      <div className="flex justify-between items-center px-4 py-2">
        <div className="text-xl font-bold text-pink-500">Music</div>
        <div
          className="flex items-center cursor-pointer hover:bg-white/10 rounded-full px-2 py-1 transition-colors"
          onClick={() => openView('profile')}
        >
          <div className="text-sm mr-2 hover:text-pink-500 transition-colors">
            {userProfile?.name || settingsMockData.playerData.name}
          </div>
          <img
            src={
              userProfile?.imageUrl ||
              settingsMockData.playerData.imageUrl ||
              'https://picsum.photos/32'
            }
            alt="Profile"
            className="w-8 h-8 rounded-full border border-transparent hover:border-pink-500 transition-colors object-cover"
          />
        </div>
      </div>

      {/* Search bar */}
      <div className="px-4 mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search songs, artists..."
            className="w-full bg-gray-800 text-white rounded-full px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-pink-500/50 cursor-text"
          />
          <i className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto px-4">
        {/* Artists section with circles */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Top Artists</h2>
            <button className="text-sm text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="flex overflow-x-auto space-x-5 pb-2">
            {artists.map(artist => (
              <div
                key={artist.id}
                className="flex-shrink-0 flex flex-col items-center cursor-pointer"
                onClick={() => {
                  setCurrentArtistId(artist.id);
                  openView('artist', { artistId: artist.id });
                }}
              >
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-2 border-2 border-pink-500/30">
                  <img
                    src={
                      artist.imageUrl || `https://picsum.photos/200/200?random=${100 + artist.id}`
                    }
                    alt={artist.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xs font-medium text-white text-center w-20 truncate">
                  {artist.name}
                </h3>
              </div>
            ))}
          </div>
        </div>

        {/* Recently Played section */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Recently Played</h2>
            <button className="text-sm text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="space-y-2">
            {songs.slice(0, 3).map(song => (
              <SongCard
                key={song.id}
                song={{
                  id: song.id,
                  title: song.title,
                  artist: song.artist,
                  imageUrl: song.imageUrl,
                  duration: `${Math.floor(song.duration / 60)}:${(song.duration % 60)
                    .toString()
                    .padStart(2, '0')}`
                }}
                showDuration={true}
              />
            ))}
          </div>
        </div>

        {/* For You section */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">For You</h2>
            <button className="text-sm text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="flex overflow-x-auto space-x-4 pb-2">
            {albums.slice(0, 5).map(album => (
              <div
                key={album.id}
                className="flex-shrink-0 w-32 cursor-pointer"
                onClick={() => openView('album', { albumId: album.id })}
              >
                <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
                  <img
                    src={album.imageUrl}
                    alt={album.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-sm font-medium text-white truncate">{album.title}</h3>
                <p className="text-xs text-gray-400 truncate">
                  {album.artist?.name || 'Unknown Artist'}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* New releases section */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">New Releases</h2>
            <button className="text-sm text-pink-500 cursor-pointer">more</button>
          </div>

          <div className="flex overflow-x-auto space-x-4 pb-2">
            {/* Reverse the albums to simulate "new releases" */}
            {[...albums]
              .reverse()
              .slice(0, 5)
              .map(album => (
                <div
                  key={album.id}
                  className="flex-shrink-0 w-32 cursor-pointer"
                  onClick={() => openView('album', { albumId: album.id })}
                >
                  <div className="relative aspect-square rounded-lg overflow-hidden mb-2">
                    <img
                      src={album.imageUrl}
                      alt={album.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-sm font-medium text-white truncate">{album.title}</h3>
                  <p className="text-xs text-gray-400 truncate">
                    {album.artist?.name || 'Unknown Artist'}
                  </p>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Bottom navigation is now handled by MusicNavigation component */}
    </div>
  );
};

export default MusicHome;
