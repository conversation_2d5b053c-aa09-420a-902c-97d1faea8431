import { createContext, useContext } from 'react';
import { PhoneStateEnum } from '../common/stores/phoneStateStore';

export interface PhoneContextValue {
  phoneState: PhoneStateEnum;
  setPhoneState: (state: PhoneStateEnum) => void;
  closePhone: () => void;
}

// Create singleton instance for state function
let setPhoneStateFunction: ((state: PhoneStateEnum) => void) | null = null;

// Function to set the phone state from outside the context
export function setPhoneState(state: PhoneStateEnum) {
  if (setPhoneStateFunction) {
    setPhoneStateFunction(state);
  } else {
    console.error('[PhoneContext] setPhoneStateFunction not initialized yet');
  }
}

// Function to register the setPhoneState function
export function registerSetPhoneStateFunction(fn: (state: PhoneStateEnum) => void) {
  setPhoneStateFunction = fn;
}

// Legacy function for backward compatibility with existing code
export function setPhoneVisibility(visible: boolean) {
  if (setPhoneStateFunction) {
    setPhoneStateFunction(visible ? PhoneStateEnum.OPEN : PhoneStateEnum.CLOSED);
  } else {
    console.error('[PhoneContext] setPhoneStateFunction not initialized yet');
  }
}

export const PhoneContext = createContext<PhoneContextValue | null>(null);

// Hook for easy access to the phone context
export const usePhoneContext = () => {
  const context = useContext(PhoneContext);
  if (!context) {
    throw new Error('usePhoneContext must be used within a PhoneProvider');
  }
  return context;
};
