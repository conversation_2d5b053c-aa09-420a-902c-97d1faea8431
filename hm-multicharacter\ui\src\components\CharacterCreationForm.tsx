import React, { useState } from 'react';
import { CharacterCreationData } from '../../../scripts/shared/character.types';

interface Props {
  onCancel: () => void;
  onSubmit: (data: CharacterCreationData) => void;
}

export const CharacterCreationForm: React.FC<Props> = ({ onCancel, onSubmit }: Props) => {
  const [form, setForm] = useState<CharacterCreationData>({
    // Initialize with default values
    id: undefined,
    stateid: '',
    license: '',
    // Use empty strings for first and last names
    first_name: '',
    last_name: '',
    // transform birthdate to date
    birthdate: new Date('1990-01-01'), // Default date of birth
    gender: 'male',
    appearance: {
      model: '', components: [], props: [], headBlend: null as any, faceFeatures: null as any,
      headOverlays: null as any, hair: null as any, eyeColor: 0, tattoos: []
    },
    state: 'alive',
    money: { cash: 500, bank: 5000, crypto: 0 },
    position: { x: 0, y: 0, z: 100, heading: 0 },
    tattoos: [],
    metadata: {},
    created_at: new Date(),
    updated_at: new Date(),
  });

  const [formError, setFormError] = useState<string>('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm((prev: CharacterCreationData) => ({ ...prev, [name]: value }));

    // Clear error when user makes changes
    if (formError) setFormError('');
  };

  const validateForm = (): boolean => {
    // Check if first name and last name are provided
    if (!form.first_name.trim()) {
      setFormError('First name is required');
      return false;
    }

    if (!form.last_name.trim()) {
      setFormError('Last name is required');
      return false;
    }

    // Check if date of birth is provided and valid
    if (!form.birthdate) {
      setFormError('Date of birth is required');
      return false;
    }

    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(form);
    }
  };

  return (
    <div className="flex flex-col">
      <div className="p-6">
        <h2 className="text-xl font-bold text-white">Create Character</h2>
        <p className="text-sm text-gray-400 mt-2">Fill in the details to begin your journey</p>
      </div>
      <form onSubmit={handleSubmit} className="px-6 pb-6 space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-6">
          {/* First Name */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">First Name</label>            <input
              type="text" name="first_name" value={form.first_name} onChange={handleChange}
              className="w-full bg-[#1f2937]/90 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="John" required />
          </div>
          {/* Last Name */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Last Name</label>
            <input
              type="text" name="last_name" value={form.last_name} onChange={handleChange}
              className="w-full bg-[#1f2937]/90 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Doe" required />
          </div>
        </div>
        {/* Date of Birth */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Date of Birth</label>          <input
            type="date" name="birthdate" value={form.birthdate.toISOString().split('T')[0]} onChange={handleChange}
            className="w-full bg-[#1f2937]/90 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            required />
        </div>

        {/* Gender */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Gender</label>
          <select name="gender" value={form.gender} onChange={handleChange}
            className="w-full bg-[#1f2937]/90 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none">
            <option value="male">Male</option>
            <option value="female">Female</option>
          </select>
        </div>

        {/* Error Message */}
        {formError && (
          <div className="text-red-500 text-sm">{formError}</div>
        )}

        {/* Controls */}
        <div className="flex justify-end gap-4">
          <button type="button" onClick={onCancel} className="px-4 py-2 text-sm text-gray-300 hover:text-white">Cancel</button>
          <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded-xl">Create</button>
        </div>
      </form>
    </div>
  );
};
