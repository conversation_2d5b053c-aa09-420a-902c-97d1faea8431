/**
 * Types for the Job Center app
 */

export type JobCategory =
  | 'GOVERNMENT'
  | 'PRIVATE'
  | 'FREELANCE'
  | 'CRIMINAL'
  | 'EMERGENCY'
  | 'OTHER';

export interface Job {
  id: number;
  title: string;
  company: string;
  description: string;
  requirements?: string[];
  payout: {
    amount: number;
    type: 'PER_PERSON' | 'SPLIT' | 'FIXED';
  };
  location: string;
  category: JobCategory;
  imageUrl?: string;
  isRecurring: boolean;
  minPlayers?: number;
  maxPlayers?: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD' | 'EXPERT';
  duration: string; // Estimated time to complete
  cooldown?: number; // Minutes before job can be done again
}

export interface JobGroup {
  id: number;
  jobId: number;
  name: string;
  leader: {
    id: string; // Citizen ID
    name: string;
    phone: string;
  };
  members: {
    id: string; // Citizen ID
    name: string;
    phone: string;
    role?: string;
    joinedAt: number;
  }[];
  status: 'RECRUITING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  isPrivate: boolean;
  password?: string;
  notes?: string;
  meetupLocation?: string;
}
