/**
 * Configuration for hm-events
 */
export const Config = {
  // Enable/disable event logging
  enabled: true,

  // Log level (debug, info, warn, error)
  logLevel: 'debug',

  // Log unknown events (events not in our catalog)
  logUnknownEvents: false,

  // Categories to enable
  enabledCategories: {
    combat: true,      // Combat-related events (gunshots, explosions, etc.)
    vehicle: true,     // Vehicle-related events (crashes, theft, etc.)
    player: true,      // Player-related events (deaths, injuries, etc.)
    environment: true, // Environment-related events (fires, weather, etc.)
    network: true,     // Network-related events
    acquaintance: true, // Acquaintance-related events (often high frequency)
    response: true,   // Response-related events (often high frequency)
    shocking: true,    // Shocking events
    system: true,      // System-related events
    other: true,      // Other miscellaneous events
  },

  // Throttling configuration
  throttling: {
    enabled: false, // Throttling is now enabled
    // How many events to skip before logging again (e.g., 10 means log every 10th event)
    rates: {
      // High frequency events
      CEventGunShot: 10,
      CEventGunShotBulletImpact: 10,
      // CEventEntityDamage: 5,
      // CEventNetworkEntityDamage: 5,
      CEventDataResponseTask: 20,
      CEventShockingGunshotFired: 10,
      // Add more as needed
    },
    // Reset counters every X milliseconds
    resetInterval: 60000, // 1 minute
  }
};