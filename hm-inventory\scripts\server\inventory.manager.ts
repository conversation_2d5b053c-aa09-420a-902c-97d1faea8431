import { InventoryItem, ItemSlot, ItemSlotStorage } from '@shared';
import { getItemDefinition, getItemDefinitionByHash, itemDefinitions } from '../shared/items/items';
import { weaponDefinitions, getWeaponDefinition } from '../shared/items/weapons';
import { convertToStorageFormat } from './migration';
import { get } from 'http';

// Simple instance ID generator
function generateInstanceId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// ===== DROPPED ITEMS SYSTEM =====
interface DroppedItemData {
    entityId: number;
    definitionId: string;
    quantity: number;
    metadata: any;
    position: [number, number, number];
    droppedBy: number;
    droppedAt: number;
}

interface GroundPanelItem {
    entityId: number;
    definitionId: string;
    quantity: number;
    distanceCategory: 'close' | 'nearby' | 'far';
    distance: number;
}

// Ground inventory capacity configuration
interface GroundInventoryConfig {
    maxWeight: number; // Maximum weight in grams (like main inventory)
    maxSlots: number;  // Maximum number of different item stacks
    regionSize: number; // Size of regional ground inventory zones
}

// Regional ground inventory system
interface GroundInventoryRegion {
    regionId: string;
    position: [number, number, number];
    items: Map<number, DroppedItemData>;
    totalWeight: number;
    usedSlots: number;
}

// Configuration - adjustable per server needs
const GROUND_INVENTORY_CONFIG: GroundInventoryConfig = {
    maxWeight: 20000, // 20kg in grams (half of main inventory)
    maxSlots: 24,     // 24 different item stacks
    regionSize: 50.0  // 50 unit radius for regional inventories
};

// Global registry for all dropped items - no temp inventories
const droppedItems: Map<number, DroppedItemData> = new Map();
const playerNearbyItems: Map<number, DroppedItemData[]> = new Map();
const groundRegions: Map<string, GroundInventoryRegion> = new Map();

// Cleanup timer for abandoned items (30 minutes)
const ITEM_CLEANUP_TIME = 30 * 60 * 1000;

// Global variables and tracking
let targetZones: Set<string> = new Set();

// Server-side debug export for admin tools
global.exports('getInventoryZoneDebugInfo', () => {
    const debugInfo: {
        totalZones: number;
        totalDroppedItems: number;
        zones: Array<{
            id: string;
            position: [number, number, number];
            itemCount: number;
            createdAt: number;
            age: number;
        }>;
    } = {
        totalZones: inventoryZones.size,
        totalDroppedItems: droppedItems.size,
        zones: []
    };
    
    for (const [zoneId, zone] of inventoryZones) {
        debugInfo.zones.push({
            id: zoneId,
            position: zone.position,
            itemCount: zone.itemEntities.size,
            createdAt: zone.createdAt,
            age: Date.now() - zone.createdAt
        });
    }
    
    return debugInfo;
});

// Export weapons list for admin panel and other resources
export function getWeapons() {
    return weaponDefinitions;
}

/**
 * Check if player has a specific item in their inventory
 */
function hasItem(itemName: string, requiredAmount: number = 1): boolean {
    try {
        const source = global.source;
        const playerInventory = getPlayerInventory();
        if (!playerInventory || playerInventory.length === 0) return false;

        // Find item by definitionId
        const item = playerInventory.find(slot => slot.item?.definitionId === itemName);
        return item ? (item.quantity || 0) >= requiredAmount : false;
    } catch (error) {
        const source = global.source;
        console.error(`[hm-inventory] Error checking item ${itemName} for player ${source}:`, error);
        return false;
    }
}

/**
 * Get all weapon components/attachments that player owns
 */
function getPlayerWeaponComponents(weaponType?: string): Array<{name: string, amount: number}> {
    try {
        const source = global.source;
        const playerInventory = getPlayerInventory();
        if (!playerInventory || playerInventory.length === 0) return [];

        // Filter items that are weapon components
        return playerInventory
            .filter(slot => {
                if (!slot.item) return false;
                
                // Check if item is a weapon component based on naming convention or category
                return slot.item.definitionId.includes('COMPONENT_') || 
                       (weaponType && slot.item.definitionId.includes(weaponType.toUpperCase()));
            })
            .map(slot => ({
                name: slot.item!.definitionId,
                amount: slot.quantity || 0
            }));
    } catch (error) {
        const source = global.source;
        console.error(`[hm-inventory] Error getting weapon components for player ${source}:`, error);
        return [];
    }
}

function getItemDefinitions() {
    return itemDefinitions;
}

// Register FiveM exports
global.exports('getPlayerInventory', getPlayerInventory);
global.exports('updatePlayerInventory', updatePlayerInventory);
global.exports('addItem', addItem);
global.exports('getItemDefinitions', getItemDefinitions);
global.exports('getItemDefinitionByHash', getItemDefinitionByHash);
global.exports('getWeapons', getWeapons);
global.exports('getWeaponDefinition', getWeaponDefinition);
global.exports('hasItem', hasItem);
global.exports('getPlayerWeaponComponents', getPlayerWeaponComponents);
global.exports('useItem', useItem);
global.exports('dropItem', dropItem);
global.exports('removeItem', removeItem);
global.exports('getDroppedItems', () => Array.from(droppedItems.values()));
global.exports('updateGroundPanelForPlayer', updateGroundPanelForPlayer);

onNet('hm-inventory:server:useItem', async (slotIndex: number) => {
    const source = global.source;
    try {
        console.log(`[hm-inventory] Player ${source} trying to use item in slot ${slotIndex}`);
        await useItem(slotIndex);
    } catch (error) {
        console.error(`[hm-inventory] Error using item for player ${source}:`, error);
        // Optionally, notify the player that an error occurred
    }
});

onNet('hm-inventory:server:dropItem', async (data: { slotIndex: number; quantity: number }) => {
    const source = global.source;
    try {
        console.log(`[hm-inventory] Player ${source} trying to drop ${data.quantity} items from slot ${data.slotIndex}`);
        await dropItem(data.slotIndex, data.quantity);
    } catch (error) {
        console.error(`[hm-inventory] Error dropping item for player ${source}:`, error);
    }
});

const STARTING_ITEMS = [
    { definitionId: 'bread', quantity: 5, slot: 0 },
    { definitionId: 'bandage', quantity: 3, slot: 1 },
    { definitionId: 'phone', quantity: 1, slot: 100 }
];

const playerInventories: { [playerId: string]: ItemSlot[] } = {};

function cleanIdentifier(identifier: string): string {
    return identifier.replace('license:', '');
}

function getPlayerIdentifier(): string {
    const source = global.source;
    const identifiers = getPlayerIdentifiers(source);
    const licenseIdentifier = identifiers.find(id => id.startsWith('license:'));
    return licenseIdentifier ? cleanIdentifier(licenseIdentifier) : '';
}

function getPlayerIdentifierForPlayer(playerId: number): string {
    const identifiers = getPlayerIdentifiers(playerId);
    const licenseIdentifier = identifiers.find(id => id.startsWith('license:'));
    return licenseIdentifier ? cleanIdentifier(licenseIdentifier) : '';
}

function generateItemInstance(definitionId: string, quantity: number, slot: number): ItemSlot {
    let definition = getItemDefinition(definitionId);
    
    // If not found as an item, try as a weapon
    if (!definition) {
        definition = getWeaponDefinition(definitionId);
    }
    
    if (!definition) {
        throw new Error(`Item/Weapon definition not found for ID: ${definitionId}`);
    }
    return {
        index: slot,
        quantity: quantity,
        item: {
            ...definition,
            instanceId: `inst-${definitionId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            definitionId: definitionId,
            quantity: quantity,
            currentDurability: definition.durability || definition.maxDurability || 100,
            metadata: {}
        }
    };
}

function createStartingItems(): ItemSlot[] {
    const items: ItemSlot[] = [];
    STARTING_ITEMS.forEach(config => {
        try {
            const item = generateItemInstance(config.definitionId, config.quantity, config.slot);
            items.push(item);
        } catch (error) {
            console.error(`[hm-inventory] Error creating starting item: ${config.definitionId} - ${error}`);
        }
    });
    return items;
}

async function loadPlayerInventory(identifier: string, stateid: string): Promise<ItemSlot[]> {
    try {
        const result = await global.exports.oxmysql.query_async(
            'SELECT items FROM player_inventories WHERE identifier = ? AND stateid = ?',
            [identifier, stateid]
        );
        if (result && result.length > 0 && result[0].items) {
            const parsedItems = JSON.parse(result[0].items);
            const migratedItems = migrateInventoryData(parsedItems);
            // console.log(`[hm-inventory] Loaded inventory for ${identifier} (${stateid}):`, migratedItems);
            return migratedItems;
        }
        return [];
    } catch (error) {
        console.error(`[hm-inventory] Error loading player inventory: ${error}`);
    }
    return [];
}

async function savePlayerInventory(identifier: string, stateid: string, items: ItemSlot[]): Promise<void> {
    try {
        // Convert to optimized storage format before saving
        const storageItems = items.map(convertToStorageFormat);
        await global.exports.oxmysql.query_async(
            'INSERT INTO player_inventories (identifier, stateid, items) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE items = VALUES(items)',
            [identifier, stateid, JSON.stringify(storageItems)]
        );
        console.log(`[hm-inventory] Player inventory saved for ${identifier} (${stateid}) - Optimized format`);
    } catch (error) {
        console.error(`[hm-inventory] Error saving player inventory: ${error}`);
    }
}

onNet('hm-core:playerLoaded', async (playerData: { characterId: string; stateid: string; characterData: any }) => {
    const source = global.source;
    const identifier = getPlayerIdentifier();

    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source}`);
        return;
    }

    const inventory = await loadPlayerInventory(identifier, playerData.stateid);
    playerInventories[source] = inventory;

    emitNet('hm-inventory:client:loadInventory', source, inventory);
    console.log(`[hm-inventory] Inventory loaded for player ${source} (${identifier})`);
});

// Handle manual inventory request (for resource restarts)
onNet('hm-inventory:server:requestInventory', async () => {
    const source = global.source;
    const identifier = getPlayerIdentifier();

    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source} requesting inventory`);
        // Send empty inventory to indicate no character loaded
        emitNet('hm-inventory:client:loadInventory', source, []);
        return;
    }

    console.log(`[hm-inventory] Manual inventory request from player ${source} (${identifier})`);

    // Try to get character data from hm-core - this is REQUIRED for inventory to work
    let activeCharacter: any;
    let stateid: string;
    
    try {
        // First try to get the full active character data
        activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        console.log(`[hm-inventory] Active character data:`, activeCharacter ? {
            stateid: activeCharacter.stateid,
            name: `${activeCharacter.first_name} ${activeCharacter.last_name}`
        } : 'null');
        
        if (!activeCharacter) {
            console.log(`[hm-inventory] No active character found for player ${source} - character not loaded`);
            // Send empty inventory to indicate no character loaded
            emitNet('hm-inventory:client:loadInventory', source, []);
            return;
        }
        
        stateid = activeCharacter.stateid;
        if (!stateid) {
            console.log(`[hm-inventory] Active character has no stateid for player ${source}`);
            // Send empty inventory to indicate invalid character data
            emitNet('hm-inventory:client:loadInventory', source, []);
            return;
        }
        
    } catch (error) {
        console.log(`[hm-inventory] Error accessing hm-core exports for player ${source}:`, error);
        console.log(`[hm-inventory] Attempting fallback to stateid export...`);
        
        // Fallback to just the stateid export
        try {
            stateid = global.exports['hm-core'].stateid(source);
            if (!stateid) {
                console.log(`[hm-inventory] Fallback stateid also returned null for player ${source}`);
                emitNet('hm-inventory:client:loadInventory', source, []);
                return;
            }
        } catch (fallbackError) {
            console.log(`[hm-inventory] hm-core not available or exports missing for player ${source}:`, fallbackError);
            // Send empty inventory to indicate no character loaded
            emitNet('hm-inventory:client:loadInventory', source, []);
            return;
        }
    }

    console.log(
        `[hm-inventory] Valid character detected - processing inventory request from player ${source} (${identifier}) with stateid ${stateid}`
    );

    try {
        const inventory = await loadPlayerInventory(identifier, stateid);
        playerInventories[source] = inventory;

        emitNet('hm-inventory:client:loadInventory', source, inventory);
        console.log(`[hm-inventory] Manual inventory load completed for player ${source} (${identifier}) - ${inventory.length} items loaded`);
    } catch (error) {
        console.error(`[hm-inventory] Error loading inventory for player ${source}:`, error);
        // Send empty inventory as fallback
        emitNet('hm-inventory:client:loadInventory', source, []);
    }
});

// Cleanup player data when they leave
on('playerDropped', async () => {
    const source = global.source;
    delete playerInventories[source];
    playerNearbyItems.delete(source);
});

/**
 * Add an item to a player's inventory
 * Supports two calling patterns:
 * 1. addItem(itemName, quantity?, metadata?) - adds to global.source player
 * 2. addItem(targetId, itemName, quantity?, metadata?) - adds to specified player (admin mode)
 * @param itemNameOrTargetId Either the item definitionId (string) or target player ID (number)
 * @param quantityOrItemName Either the quantity (number) or item definitionId (string) when using admin mode
 * @param metadataOrQuantity Either metadata (object) or quantity (number) when using admin mode
 * @param adminMetadata Optional metadata when using admin mode
 * @returns true if the item was added successfully, false otherwise
 */
async function addItem(
    itemNameOrTargetId: string | number, 
    quantityOrItemName?: number | string, 
    metadataOrQuantity?: any | number, 
    adminMetadata?: any
): Promise<boolean> {
    try {
        let targetPlayerId: number;
        let itemName: string;
        let quantity: number;
        let metadata: any;

        // Detect calling pattern based on first parameter type
        if (typeof itemNameOrTargetId === 'number') {
            // Admin mode: addItem(targetId, itemName, quantity?, metadata?)
            targetPlayerId = itemNameOrTargetId;
            itemName = quantityOrItemName as string;
            quantity = (metadataOrQuantity as number) || 1;
            metadata = adminMetadata || {};
            console.log(`[hm-inventory] Admin mode: Adding ${quantity}x ${itemName} to player ${targetPlayerId}`);
        } else {
            // Normal mode: addItem(itemName, quantity?, metadata?)
            targetPlayerId = global.source;
            itemName = itemNameOrTargetId;
            quantity = (quantityOrItemName as number) || 1;
            metadata = metadataOrQuantity || {};
            console.log(`[hm-inventory] Normal mode: Adding ${quantity}x ${itemName} to player ${targetPlayerId}`);
        }

        // Validate target player exists
        if (!GetPlayerName(targetPlayerId.toString())) {
            console.error(`[hm-inventory] Target player ${targetPlayerId} not found when adding item ${itemName}`);
            return false;
        }

        const identifier = getPlayerIdentifierForPlayer(targetPlayerId);
        if (!identifier) {
            console.error(`[hm-inventory] No valid identifier found for player ${targetPlayerId} when adding item`);
            return false;
        }

        // Get stateid from hm-core with better error handling
        let stateid: string;
        try {
            const activeCharacter = global.exports['hm-core'].getActiveCharacter(targetPlayerId);
            console.log(`[hm-inventory] addItem - Active character for player ${targetPlayerId}:`, activeCharacter ? {
                stateid: activeCharacter.stateid,
                name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
                id: activeCharacter.id
            } : 'null');
            
            if (!activeCharacter || !activeCharacter.stateid) {
                console.error(`[hm-inventory] No active character or stateid found for player ${targetPlayerId} when adding item`);
                return false;
            }
            stateid = activeCharacter.stateid;
            console.log(`[hm-inventory] addItem - Using stateid: ${stateid} for player ${targetPlayerId}`);
        } catch (error) {
            console.log(`[hm-inventory] addItem - Error getting active character for player ${targetPlayerId}:`, error);
            // Fallback to stateid export
            try {
                stateid = global.exports['hm-core'].stateid(targetPlayerId);
                console.log(`[hm-inventory] addItem - Fallback stateid for player ${targetPlayerId}: ${stateid || 'null'}`);
                if (!stateid) {
                    console.error(`[hm-inventory] No stateid found for player ${targetPlayerId} when adding item`);
                    return false;
                }
            } catch (fallbackError) {
                console.error(`[hm-inventory] hm-core not available for player ${targetPlayerId} when adding item:`, fallbackError);
                return false;
            }
        }

        // Get item definition to validate it exists (check both items and weapons)
        let itemDefinition = getItemDefinition(itemName);
        
        // If not found as an item, try as a weapon
        if (!itemDefinition) {
            itemDefinition = getWeaponDefinition(itemName);
        }
        
        if (!itemDefinition) {
            console.error(`[hm-inventory] Item/Weapon definition not found for ID: ${itemName}`);
            return false;
        }

        // Get current inventory
        let currentInventory = playerInventories[targetPlayerId] || [];
        
        // Find available slot - first try to stack with existing items if stackable
        let targetSlotIndex = -1;
        let targetSlot: ItemSlot | null = null;

        if (itemDefinition.stackable) {
            // Try to find existing item to stack with
            const existingSlotIndex = currentInventory.findIndex(slot => 
                slot.item?.definitionId === itemName
            );
            
            if (existingSlotIndex !== -1) {
                const existingSlot = currentInventory[existingSlotIndex];
                const maxStack = itemDefinition.maxStack || 999;
                const currentQuantity = existingSlot.quantity || 1;
                
                if (currentQuantity + quantity <= maxStack) {
                    // Stack with existing item
                    targetSlotIndex = existingSlotIndex;
                    targetSlot = existingSlot;
                }
            }
        }

        // If we couldn't stack, find an empty slot
        if (targetSlotIndex === -1) {
            // Find the first available slot (0-99 for main inventory)
            for (let i = 0; i < 100; i++) {
                const occupied = currentInventory.find(slot => slot.index === i);
                if (!occupied) {
                    targetSlotIndex = i;
                    break;
                }
            }
        }

        if (targetSlotIndex === -1) {
            console.error(`[hm-inventory] No available slot found for player ${targetPlayerId} to add item ${itemName}`);
            return false;
        }

        if (targetSlot) {
            // Stack with existing item
            targetSlot.quantity = (targetSlot.quantity || 1) + quantity;
            console.log(`[hm-inventory] Stacked ${quantity}x ${itemName} with existing item for player ${targetPlayerId}`);
        } else {
            // Create new item instance
            const newItem = generateItemInstance(itemName, quantity, targetSlotIndex);
            if (metadata && Object.keys(metadata).length > 0) {
                newItem.item!.metadata = { ...newItem.item!.metadata, ...metadata };
            }
            currentInventory.push(newItem);
            console.log(`[hm-inventory] Added new ${quantity}x ${itemName} to slot ${targetSlotIndex} for player ${targetPlayerId}`);
        }

        // Update inventory in memory and database
        playerInventories[targetPlayerId] = currentInventory;
        await savePlayerInventory(identifier, stateid, currentInventory);

        // Send updated inventory to client
        emitNet('hm-inventory:client:updateInventory', targetPlayerId, currentInventory);

        console.log(`[hm-inventory] Successfully added ${quantity}x ${itemName} to player ${targetPlayerId}'s inventory`);
        return true;

    } catch (error) {
        console.error(`[hm-inventory] Error adding item to player:`, error);
        return false;
    }
}

/**
 * Use an item from a player's inventory
 * @param slotIndex The inventory slot index of the item to use
 * @returns true if the item was used successfully, false otherwise
 */
async function useItem(slotIndex: number): Promise<boolean> {
    const source = global.source;
    const identifier = getPlayerIdentifier();
    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source} when using item`);
        return false;
    }

    // Get stateid from hm-core with better error handling
    let stateid: string;
    try {
        const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        console.log(`[hm-inventory] useItem - Active character for player ${source}:`, activeCharacter ? {
            stateid: activeCharacter.stateid,
            name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
            id: activeCharacter.id
        } : 'null');
        
        if (!activeCharacter || !activeCharacter.stateid) {
            console.error(`[hm-inventory] No active character or stateid found for player ${source} when using item`);
            return false;
        }
        stateid = activeCharacter.stateid;
        console.log(`[hm-inventory] useItem - Using stateid: ${stateid} for player ${source}`);
    } catch (error) {
        console.log(`[hm-inventory] useItem - Error getting active character for player ${source}:`, error);
        // Fallback to stateid export
        try {
            stateid = global.exports['hm-core'].stateid(source);
            console.log(`[hm-inventory] useItem - Fallback stateid for player ${source}: ${stateid || 'null'}`);
            if (!stateid) {
                console.error(`[hm-inventory] No stateid found for player ${source} when using item`);
                return false;
            }
        } catch (fallbackError) {
            console.error(`[hm-inventory] hm-core not available for player ${source} when using item:`, fallbackError);
            return false;
        }
    }

    let currentInventory = playerInventories[source] || [];
    const slot = currentInventory.find(s => s.index === slotIndex);

    if (!slot || !slot.item || typeof slot.quantity !== 'number') {
        console.error(`[hm-inventory] No valid item or quantity found at slot ${slotIndex} for player ${source}`);
        return false;
    }

    const itemName = slot.item.definitionId;
    console.log(`[hm-inventory] Player ${source} is using item: ${itemName}`);

    // Get the item definition to access usage properties
    const itemDefinition = getItemDefinition(itemName) || getWeaponDefinition(itemName);
    if (!itemDefinition) {
        console.error(`[hm-inventory] Item definition not found for: ${itemName}`);
        return false;
    }

    // Check if item is usable
    if (!itemDefinition.usable) {
        console.log(`[hm-inventory] Item ${itemName} is not usable`);
        return false;
    }

    const usage = itemDefinition.usage;
    if (!usage) {
        console.log(`[hm-inventory] No usage definition found for item: ${itemName}`);
        return false;
    }

    // Check cooldowns (you could implement a cooldown system here)
    if (usage.cooldown) {
        // TODO: Implement cooldown tracking per player/item
        console.log(`[hm-inventory] Item ${itemName} has ${usage.cooldown}ms cooldown`);
    }

    // Check requirements
    if (usage.requirements) {
        // TODO: Implement requirement checks (location, job, etc.)
        console.log(`[hm-inventory] Checking requirements for ${itemName}:`, usage.requirements);
    }

    // Emit events for different handling levels
    const eventData = {
        item: slot.item,
        usage: usage,
        source: source,
        slotIndex: slotIndex
    };

    // 1. Specific item event (highest priority)
    emit(`hm-inventory:server:itemUsed:${itemName}`, source, eventData);

    // 2. Category-based event (medium priority)
    if (usage.category) {
        emit(`hm-inventory:server:categoryUsed:${usage.category}`, source, eventData);
    }

    // 3. Type-based event (lower priority)
    emit(`hm-inventory:server:typeUsed:${itemDefinition.type}`, source, eventData);

    // 4. Generic event (lowest priority, for logging/analytics)
    emit(`hm-inventory:server:itemUsed`, source, itemName, slot.item.metadata, slot.item);

    // 5. Custom handler event (if specified)
    if (usage.customHandler) {
        emit(usage.customHandler, source, eventData);
    }

    // Handle built-in effects
    if (usage.effects) {
        console.log(`[hm-inventory] Applying effects for ${itemName}:`, usage.effects);
        
        // Emit effects to other systems (health, hunger, etc.)
        if (usage.effects.health) {
            emit('hm-inventory:server:effectHealth', source, usage.effects.health);
        }
        if (usage.effects.hunger) {
            emit('hm-inventory:server:effectHunger', source, usage.effects.hunger);
        }
        if (usage.effects.thirst) {
            emit('hm-inventory:server:effectThirst', source, usage.effects.thirst);
        }
        if (usage.effects.stress) {
            emit('hm-inventory:server:effectStress', source, usage.effects.stress);
        }
        if (usage.effects.armor) {
            emit('hm-inventory:server:effectArmor', source, usage.effects.armor);
        }
    }

    // Handle animations and progress bars on client
    if (usage.animation || usage.progressBar) {
        emitNet('hm-inventory:client:startUsage', source, {
            itemName: itemName,
            animation: usage.animation,
            progressBar: usage.progressBar
        });
    }

    // Determine if item should be consumed
    const shouldConsume = usage.consumable !== undefined ? usage.consumable : 
                         (itemDefinition.type === 'consumable'); // Default: consume if type is consumable

    if (shouldConsume) {
        console.log(`[hm-inventory] Consuming ${itemName}`);
        if (slot.quantity > 1) {
            slot.quantity -= 1;
        } else {
            // Remove the item from inventory
            currentInventory = currentInventory.filter(s => s.index !== slotIndex);
        }
    } else {
        console.log(`[hm-inventory] Item ${itemName} was used but not consumed`);
    }

    // Update inventory
    playerInventories[source] = currentInventory;
    await savePlayerInventory(identifier, stateid, currentInventory);

    // Send updated inventory to client
    emitNet('hm-inventory:client:updateInventory', source, currentInventory);

    console.log(`[hm-inventory] Successfully used ${itemName} for player ${source}`);
    return true;
}

/**
 * Drop an item from a player's inventory into the world
 * @param slotIndex The inventory slot index of the item to drop
 * @param quantity The quantity to drop (defaults to all)
 * @returns true if the item was dropped successfully, false otherwise
 */
async function dropItem(slotIndex: number, quantity?: number): Promise<boolean> {
    const source = global.source;
    const identifier = getPlayerIdentifier();
    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source} when dropping item`);
        return false;
    }

    // Get stateid from hm-core with better error handling
    let stateid: string;
    try {
        const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        console.log(`[hm-inventory] dropItem - Active character for player ${source}:`, activeCharacter ? {
            stateid: activeCharacter.stateid,
            name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
            id: activeCharacter.id
        } : 'null');
        
        if (!activeCharacter || !activeCharacter.stateid) {
            console.error(`[hm-inventory] No active character or stateid found for player ${source} when dropping item`);
            return false;
        }
        stateid = activeCharacter.stateid;
        console.log(`[hm-inventory] dropItem - Using stateid: ${stateid} for player ${source}`);
    } catch (error) {
        console.log(`[hm-inventory] dropItem - Error getting active character for player ${source}:`, error);
        // Fallback to stateid export
        try {
            stateid = global.exports['hm-core'].stateid(source);
            console.log(`[hm-inventory] dropItem - Fallback stateid for player ${source}: ${stateid || 'null'}`);
            if (!stateid) {
                console.error(`[hm-inventory] No stateid found for player ${source} when dropping item`);
                return false;
            }
        } catch (fallbackError) {
            console.error(`[hm-inventory] hm-core not available for player ${source} when dropping item:`, fallbackError);
            return false;
        }
    }

    let currentInventory = playerInventories[source] || [];
    const slot = currentInventory.find(s => s.index === slotIndex);

    if (!slot || !slot.item || typeof slot.quantity !== 'number') {
        console.error(`[hm-inventory] No valid item or quantity found at slot ${slotIndex} for player ${source}`);
        return false;
    }

    const itemName = slot.item.definitionId;
    const dropQuantity = quantity && quantity < slot.quantity ? quantity : slot.quantity;
    
    console.log(`[hm-inventory] Player ${source} is dropping ${dropQuantity}x ${itemName}`);

    // Get player position to drop the item nearby
    const playerPed = GetPlayerPed(source);
    const playerCoords = GetEntityCoords(playerPed);
    
    // Spawn the item model in the world if it has a model defined
    let droppedEntity: number | null = null;
    const modelToSpawn = slot.item.model || 'prop_food_bs_bag_01'; // Default bag model if no specific model
    
    try {
        // Create the object in the world
        const hash = typeof modelToSpawn === 'string' ? GetHashKey(modelToSpawn) : modelToSpawn;
        
        // Get player heading and calculate forward vector manually
        const playerHeading = GetEntityHeading(playerPed);
        const headingInRadians = (playerHeading * Math.PI) / 180; // Convert to radians
        
        // Calculate forward vector from heading
        const forwardX = -Math.sin(headingInRadians);
        const forwardY = Math.cos(headingInRadians);
        
        // Spawn slightly in front of player
        const spawnX = playerCoords[0] + forwardX * 1.5;
        const spawnY = playerCoords[1] + forwardY * 1.5;
        const spawnZ = playerCoords[2] - 0.9; // Ground level
        
        droppedEntity = CreateObject(hash, spawnX, spawnY, spawnZ, true, true, false);
        
        // Store item data immediately for the callback
        const itemData = {
            definitionId: slot.item.definitionId,
            quantity: dropQuantity,
            metadata: slot.item.metadata,
            droppedBy: source,
            droppedAt: Date.now()
        };
        const modelUsed = slot.item.model ? 'specific model' : 'default bag';
        
        // Give the object a moment to be created, then validate
        setTimeout(() => {
            if (droppedEntity && droppedEntity !== 0 && DoesEntityExist(droppedEntity) && slot.item) {
                // Set the object to have physics
                SetEntityDynamic(droppedEntity, true);
                
                // Store item data with the entity for potential pickup
                Entity(droppedEntity).state.itemData = itemData;
                
                // Add to global dropped items registry
                const droppedItemData: DroppedItemData = {
                    entityId: droppedEntity,
                    definitionId: slot.item.definitionId,
                    quantity: dropQuantity,
                    metadata: slot.item.metadata,
                    position: [spawnX, spawnY, spawnZ],
                    droppedBy: source,
                    droppedAt: Date.now()
                };
                droppedItems.set(droppedEntity, droppedItemData);
                
                // Register with target system for pickup (within 5 units)
                try {
                    global.exports['hm-target'].addTarget(droppedEntity, {
                        label: `Pick Up ${slot.item.definitionId} (x${dropQuantity})`,
                        distance: 5.0,
                        onSelect: () => {
                            emitNet('hm-inventory:server:pickupItem', source, droppedEntity);
                        },
                        // Enhanced target integration for better UX
                        onEnter: (playerId: number) => {
                            // Optional: Could send special UI notification when player gets close
                            console.log(`[hm-inventory] Player ${playerId} is near dropped item ${itemName}`);
                        },
                        onExit: (playerId: number) => {
                            // Optional: Could hide special UI notification when player moves away
                            console.log(`[hm-inventory] Player ${playerId} moved away from dropped item ${itemName}`);
                        }
                    });
                    console.log(`[hm-inventory] Registered enhanced target for ${itemName} with entity ${droppedEntity}`);
                } catch (error) {
                    console.error(`[hm-inventory] Error registering target for ${itemName}:`, error);
                }
                
                // Notify nearby players immediately about the new ground item
                notifyNearbyPlayersOfGroundItemChange([spawnX, spawnY, spawnZ], source);
                
                // Create inventory zone for this dropped item
                createInventoryZoneForItems([spawnX, spawnY, spawnZ], droppedEntity);
                
                console.log(`[hm-inventory] Spawned ${itemName} (${modelUsed}) in world at ${spawnX}, ${spawnY}, ${spawnZ} with entity ID ${droppedEntity}`);
            } else {
                console.error(`[hm-inventory] Failed to create object for ${itemName} - entity ID: ${droppedEntity}`);
            }
        }, 100); // Small delay to allow object creation to complete
    } catch (error) {
        console.error(`[hm-inventory] Error spawning model for ${itemName}:`, error);
    }
    
    // Create a dropped item in the world (this would typically involve another system)
    // For now, we'll emit an event that other resources can listen to
    emit('hm-inventory:server:itemDropped', source, {
        item: slot.item,
        quantity: dropQuantity,
        position: playerCoords,
        droppedBy: source,
        entity: droppedEntity // Include the spawned entity
    });

    // Remove the dropped quantity from inventory
    if (dropQuantity >= slot.quantity) {
        // Remove the entire item
        currentInventory = currentInventory.filter(s => s.index !== slotIndex);
    } else {
        // Reduce the quantity
        slot.quantity -= dropQuantity;
    }

    // Update inventory
    playerInventories[source] = currentInventory;
    await savePlayerInventory(identifier, stateid, currentInventory);

    // Send updated inventory to client
    emitNet('hm-inventory:client:updateInventory', source, currentInventory);

    console.log(`[hm-inventory] Successfully dropped ${dropQuantity}x ${itemName} for player ${source}`);
    return true;
}

/**
 * Remove an item from a player's inventory
 * @param itemName The definitionId of the item to remove
 * @param quantity The quantity to remove (defaults to 1)
 * @returns true if the item was removed successfully, false otherwise
 */
async function removeItem(itemName: string, quantity: number = 1): Promise<boolean> {
    try {
        const source = global.source;
        const identifier = getPlayerIdentifier();
        if (!identifier) {
            console.error(`[hm-inventory] No valid identifier found for player ${source} when removing item`);
            return false;
        }

        // Get stateid from hm-core with better error handling
        let stateid: string;
        try {
            const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
            console.log(`[hm-inventory] removeItem - Active character for player ${source}:`, activeCharacter ? {
                stateid: activeCharacter.stateid,
                name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
                id: activeCharacter.id
            } : 'null');
            
            if (!activeCharacter || !activeCharacter.stateid) {
                console.error(`[hm-inventory] No active character or stateid found for player ${source} when removing item`);
                return false;
            }
            stateid = activeCharacter.stateid;
            console.log(`[hm-inventory] removeItem - Using stateid: ${stateid} for player ${source}`);
        } catch (error) {
            console.log(`[hm-inventory] removeItem - Error getting active character for player ${source}:`, error);
            // Fallback to stateid export
            try {
                stateid = global.exports['hm-core'].stateid(source);
                console.log(`[hm-inventory] removeItem - Fallback stateid for player ${source}: ${stateid || 'null'}`);
                if (!stateid) {
                    console.error(`[hm-inventory] No stateid found for player ${source} when removing item`);
                    return false;
                }
            } catch (fallbackError) {
                console.error(`[hm-inventory] hm-core not available for player ${source} when removing item:`, fallbackError);
                return false;
            }
        }

        let currentInventory = playerInventories[source] || [];
        
        // Find the item in inventory
        const slot = currentInventory.find(s => s.item?.definitionId === itemName);
        
        if (!slot || !slot.item || typeof slot.quantity !== 'number') {
            console.error(`[hm-inventory] Item ${itemName} not found in inventory for player ${source}`);
            return false;
        }

        if (slot.quantity < quantity) {
            console.error(`[hm-inventory] Not enough ${itemName} to remove ${quantity} (only has ${slot.quantity}) for player ${source}`);
            return false;
        }

        console.log(`[hm-inventory] Removing ${quantity}x ${itemName} from player ${source}'s inventory`);

        // Remove the specified quantity
        if (slot.quantity > quantity) {
            slot.quantity -= quantity;
        } else {
            // Remove the entire item
            currentInventory = currentInventory.filter(s => s.index !== slot.index);
        }

        // Update inventory
        playerInventories[source] = currentInventory;
        await savePlayerInventory(identifier, stateid, currentInventory);

        // Send updated inventory to client
        emitNet('hm-inventory:client:updateInventory', source, currentInventory);

        console.log(`[hm-inventory] Successfully removed ${quantity}x ${itemName} from player ${source}'s inventory`);
        return true;

    } catch (error) {
        const source = global.source;
        console.error(`[hm-inventory] Error removing item ${itemName} from player ${source}:`, error);
        return false;
    }
}

function getPlayerInventory(): ItemSlot[] {
    const source = global.source;
    return playerInventories[source] || [];
}

async function updatePlayerInventory(items: ItemSlot[]): Promise<void> {
    const source = global.source;
    const identifier = getPlayerIdentifier();
    if (!identifier) return;

    // Get stateid from hm-core with better error handling
    let stateid: string;
    try {
        const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        console.log(`[hm-inventory] updateInventory - Active character for player ${source}:`, activeCharacter ? {
            stateid: activeCharacter.stateid,
            name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
            id: activeCharacter.id
        } : 'null');
        
        if (!activeCharacter || !activeCharacter.stateid) {
            console.error(`[hm-inventory] No active character or stateid found for player ${source} when updating inventory`);
            return;
        }
        stateid = activeCharacter.stateid;
        console.log(`[hm-inventory] updateInventory - Using stateid: ${stateid} for player ${source}`);
    } catch (error) {
        console.log(`[hm-inventory] updateInventory - Error getting active character for player ${source}:`, error);
        // Fallback to stateid export
        try {
            stateid = global.exports['hm-core'].stateid(source);
            console.log(`[hm-inventory] updateInventory - Fallback stateid for player ${source}: ${stateid || 'null'}`);
            if (!stateid) {
                console.error(`[hm-inventory] No stateid found for player ${source} when updating inventory`);
                return;
            }
        } catch (fallbackError) {
            console.error(`[hm-inventory] hm-core not available for player ${source} when updating inventory:`, fallbackError);
            return;
        }
    }

    playerInventories[source] = items;
    await savePlayerInventory(identifier, stateid, items);
}

/**
 * Converts optimized storage format back to full ItemSlot with definition data
 */
function convertFromStorageFormat(storageSlot: ItemSlotStorage): ItemSlot {
    if (!storageSlot.item) {
        return {
            index: storageSlot.index,
            slotType: storageSlot.slotType
        };
    }

    let definition = getItemDefinition(storageSlot.item.definitionId);
    
    // If not found as an item, try as a weapon
    if (!definition) {
        definition = getWeaponDefinition(storageSlot.item.definitionId);
    }
    
    if (!definition) {
        console.error(`[hm-inventory] Item/Weapon definition not found for ID: ${storageSlot.item.definitionId}`);
        return { index: storageSlot.index, slotType: storageSlot.slotType };
    }

    return {
        index: storageSlot.index,
        quantity: storageSlot.item.quantity,
        item: {
            ...definition,
            instanceId: storageSlot.item.instanceId,
            definitionId: storageSlot.item.definitionId,
            quantity: storageSlot.item.quantity,
            currentDurability: storageSlot.item.currentDurability,
            metadata: storageSlot.item.metadata || {}
        },
        slotType: storageSlot.slotType
    };
}

/**
 * Migrates old format data to optimized format
 */
function migrateInventoryData(items: any[]): ItemSlot[] {
    try {
        // Check if data is already in new format (has only instanceId, definitionId, etc.)
        if (
            items.length > 0 &&
            items[0].item &&
            !Object.prototype.hasOwnProperty.call(items[0].item, 'name') &&
            Object.prototype.hasOwnProperty.call(items[0].item, 'instanceId')
        ) {
            // Already in optimized format, convert to full format
            return items.map(convertFromStorageFormat);
        }
        
        // Old format with full definition data, return as-is for now
        return items as ItemSlot[];
    } catch (error) {
        console.error(`[hm-inventory] Error migrating inventory data: ${error}`);
        return [];
    }
}

// Handle move item request from client
onNet('hm-inventory:server:moveItem', async (moveData: {
    sourceIndex: number,
    sourcePanelType: string,
    sourcePanelId?: string,
    destinationIndex: number,
    destinationPanelType: string,
    destinationPanelId?: string,
    quantity?: number
}) => {
    const source = global.source;
    const identifier = getPlayerIdentifier();
    
    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source} moving item`);
        return;
    }        try {
            // Get stateid from hm-core with better error handling
            let stateid: string;
            try {
                const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
                console.log(`[hm-inventory] moveItem - Active character for player ${source}:`, activeCharacter ? {
                    stateid: activeCharacter.stateid,
                    name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
                    id: activeCharacter.id
                } : 'null');
                
                if (!activeCharacter || !activeCharacter.stateid) {
                    console.error(`[hm-inventory] No active character or stateid found for player ${source} moving item`);
                    return;
                }
                stateid = activeCharacter.stateid;
                console.log(`[hm-inventory] moveItem - Using stateid: ${stateid} for player ${source}`);
            } catch (error) {
                console.log(`[hm-inventory] moveItem - Error getting active character for player ${source}:`, error);
                // Fallback to stateid export
                try {
                    stateid = global.exports['hm-core'].stateid(source);
                    console.log(`[hm-inventory] moveItem - Fallback stateid for player ${source}: ${stateid || 'null'}`);
                    if (!stateid) {
                        console.error(`[hm-inventory] No stateid found for player ${source} moving item`);
                        return;
                    }
                } catch (fallbackError) {
                    console.error(`[hm-inventory] hm-core not available for player ${source} moving item:`, fallbackError);
                    return;
                }
            }

        console.log(`[hm-inventory] Processing move item request for player ${source}:`, moveData);
        
        // Get current inventory
        let currentInventory = playerInventories[source] || [];
        console.log(`[hm-inventory] Current player inventory before move:`, currentInventory.map(slot => ({ index: slot.index, itemId: slot.item?.definitionId })));
        
        // For now, only handle main panel to main panel moves
        if (moveData.sourcePanelType === 'main' && moveData.destinationPanelType === 'main') {
            // Find the source item
            const sourceSlotIndex = currentInventory.findIndex(slot => slot.index === moveData.sourceIndex);
            
            if (sourceSlotIndex === -1) {
                console.error(`[hm-inventory] Source item not found at index ${moveData.sourceIndex}`);
                return;
            }
            
            const sourceSlot = currentInventory[sourceSlotIndex];
            const destinationSlotIndex = currentInventory.findIndex(slot => slot.index === moveData.destinationIndex);
            
            console.log(`[hm-inventory] Source slot:`, sourceSlot);
            console.log(`[hm-inventory] Destination slot index:`, destinationSlotIndex);
            
            // Handle the move
            if (destinationSlotIndex === -1) {
                // Destination is empty, move item there
                // Simply update the index of the source slot
                currentInventory[sourceSlotIndex] = { ...sourceSlot, index: moveData.destinationIndex };
                console.log(`[hm-inventory] Moved item to empty slot ${moveData.destinationIndex}`);
            } else {
                // Destination has an item, swap them
                const destinationSlot = currentInventory[destinationSlotIndex];
                console.log(`[hm-inventory] Swapping with destination slot:`, destinationSlot);
                
                // Swap the indices
                currentInventory[sourceSlotIndex] = { ...destinationSlot, index: moveData.sourceIndex };
                currentInventory[destinationSlotIndex] = { ...sourceSlot, index: moveData.destinationIndex };
                console.log(`[hm-inventory] Swapped items between slots ${moveData.sourceIndex} and ${moveData.destinationIndex}`);
            }
            
            console.log(`[hm-inventory] Player inventory after move:`, currentInventory.map(slot => ({ index: slot.index, itemId: slot.item?.definitionId })));
            
            // Update inventory in memory and database
            playerInventories[source] = currentInventory;
            await savePlayerInventory(identifier, stateid, currentInventory);
            
            // Send updated inventory back to client
            emitNet('hm-inventory:client:updateInventory', source, currentInventory);
            
            console.log(`[hm-inventory] Successfully moved item from slot ${moveData.sourceIndex} to slot ${moveData.destinationIndex} for player ${source}`);
        } else {
            console.log(`[hm-inventory] Move operation between ${moveData.sourcePanelType} and ${moveData.destinationPanelType} not yet implemented`);
        }
        
    } catch (error) {
        console.error(`[hm-inventory] Error processing move item request for player ${source}:`, error);
    }
});

// Handle item pickup
onNet('hm-inventory:server:pickupItem', async (entityId: number) => {
    const source = global.source;
    
    // Validate entity exists and has item data
    if (!DoesEntityExist(entityId) || !Entity(entityId).state.itemData) {
        console.error(`[hm-inventory] Invalid pickup attempt by ${source} - entity ${entityId} doesn't exist or has no item data`);
        return;
    }
    
    const itemData = Entity(entityId).state.itemData;
    const identifier = getPlayerIdentifier();
    if (!identifier) {
        console.error(`[hm-inventory] No valid identifier found for player ${source} when picking up item`);
        return;
    }

    // Get stateid from hm-core with better error handling
    let stateid: string;
    try {
        const activeCharacter = global.exports['hm-core'].getActiveCharacter(source);
        console.log(`[hm-inventory] pickupItem - Active character for player ${source}:`, activeCharacter ? {
            stateid: activeCharacter.stateid,
            name: `${activeCharacter.first_name} ${activeCharacter.last_name}`,
            id: activeCharacter.id
        } : 'null');
        
        if (!activeCharacter || !activeCharacter.stateid) {
            console.error(`[hm-inventory] No active character or stateid found for player ${source} when picking up item`);
            return;
        }
        stateid = activeCharacter.stateid;
        console.log(`[hm-inventory] pickupItem - Using stateid: ${stateid} for player ${source}`);
    } catch (error) {
        console.log(`[hm-inventory] pickupItem - Error getting active character for player ${source}:`, error);
        // Fallback to stateid export
        try {
            stateid = global.exports['hm-core'].stateid(source);
            console.log(`[hm-inventory] pickupItem - Fallback stateid for player ${source}: ${stateid || 'null'}`);
            if (!stateid) {
                console.error(`[hm-inventory] No stateid found for player ${source} when picking up item`);
                return;
            }
        } catch (fallbackError) {
            console.error(`[hm-inventory] hm-core not available for player ${source} when picking up item:`, fallbackError);
            return;
        }
    }
    
    console.log(`[hm-inventory] Player ${source} picking up:`, itemData);
    
    try {
        let currentInventory = playerInventories[source] || [];
        
        // Find an empty slot or existing item that can stack
        let targetSlot: ItemSlot | null = null;
        
        // First, try to find an existing stack of the same item
        for (const slot of currentInventory) {
            if (slot.item && slot.item.definitionId === itemData.definitionId && 
                JSON.stringify(slot.item.metadata) === JSON.stringify(itemData.metadata)) {
                targetSlot = slot;
                break;
            }
        }
        
        // If no existing stack found, find an empty slot
        if (!targetSlot) {
            for (let i = 0; i < 40; i++) { // Assuming 40 slots max
                const existingSlot = currentInventory.find(s => s.index === i);
                if (!existingSlot || !existingSlot.item) {
                    // Create new slot
                    targetSlot = {
                        index: i,
                        item: undefined,
                        quantity: 0
                    };
                    currentInventory.push(targetSlot);
                    break;
                }
            }
        }
        
        if (!targetSlot) {
            console.log(`[hm-inventory] Could not pickup ${itemData.definitionId} - inventory full`);
            return;
        }
        
        // Add the item to the target slot
        if (!targetSlot.item) {
            // Empty slot, add the item by getting the definition and creating full item
            const itemDefinition = getItemDefinition(itemData.definitionId);
            if (!itemDefinition) {
                console.error(`[hm-inventory] Invalid item definition: ${itemData.definitionId}`);
                return;
            }
            
            targetSlot.item = {
                ...itemDefinition, // Spread the definition
                instanceId: generateInstanceId(), // Generate unique instance ID
                definitionId: itemData.definitionId,
                quantity: itemData.quantity,
                currentDurability: itemDefinition.durability,
                metadata: itemData.metadata
            };
            targetSlot.quantity = itemData.quantity;
        } else {
            // Stack with existing item
            const newQuantity = (targetSlot.quantity || 0) + itemData.quantity;
            targetSlot.quantity = newQuantity;
            targetSlot.item.quantity = newQuantity;
        }
        
        // Update inventory in memory and database
        playerInventories[source] = currentInventory;
        await savePlayerInventory(identifier, stateid, currentInventory);
        
        // Send updated inventory to client
        emitNet('hm-inventory:client:updateInventory', source, currentInventory);
        
        // Successfully added to inventory, remove from world
        try {
            // Remove from target system
            global.exports['hm-target'].removeTarget(entityId);
            console.log(`[hm-inventory] Removed target for entity ${entityId}`);
        } catch (error) {
            console.error(`[hm-inventory] Error removing target for entity ${entityId}:`, error);
        }
        
        // Remove from global registry
        droppedItems.delete(entityId);
        
        // Delete the entity from world
        DeleteEntity(entityId);
        
        // Notify nearby players immediately about the item being picked up
        notifyNearbyPlayersOfGroundItemChange(itemData.position);
        
        console.log(`[hm-inventory] Successfully picked up ${itemData.quantity}x ${itemData.definitionId}`);
        
        // Emit pickup event for other systems
        emit('hm-inventory:server:itemPickedUp', source, {
            item: itemData,
            pickedUpBy: source
        });
        
    } catch (error) {
        console.error(`[hm-inventory] Error during pickup:`, error);
    }
});

// ===== GROUND PANEL & PROXIMITY SYSTEM =====

/**
 * Calculate distance between two 3D points
 */
function getDistance(pos1: [number, number, number], pos2: [number, number, number]): number {
    const dx = pos1[0] - pos2[0];
    const dy = pos1[1] - pos2[1];
    const dz = pos1[2] - pos2[2];
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Get distance category for ground panel display
 */
function getDistanceCategory(distance: number): 'close' | 'nearby' | 'far' {
    if (distance <= 10) return 'close';
    if (distance <= 15) return 'nearby';
    return 'far';
}

/**
 * Cleanup abandoned items that have been on the ground too long
 */
function cleanupAbandonedItems(): void {
    const now = Date.now();
    const itemsToRemove: number[] = [];
    
    for (const [entityId, itemData] of droppedItems) {
        // Check if item has been on ground too long
        if (now - itemData.droppedAt > ITEM_CLEANUP_TIME) {
            itemsToRemove.push(entityId);
        }
        // Also check if entity no longer exists
        else if (!DoesEntityExist(entityId)) {
            itemsToRemove.push(entityId);
        }
    }
    
    // Remove abandoned items
    for (const entityId of itemsToRemove) {
        const itemData = droppedItems.get(entityId);
        if (itemData) {
            console.log(`[hm-inventory] Cleaning up abandoned item: ${itemData.definitionId} (entity ${entityId})`);
            
            // Remove from target system
            try {
                global.exports['hm-target'].removeTarget(entityId);
            } catch (error) {
                // Target might already be removed, ignore error
            }
            
            // Delete entity if it exists
            if (DoesEntityExist(entityId)) {
                DeleteEntity(entityId);
            }
            
            // Remove from registry
            droppedItems.delete(entityId);
        }
    }
    
    if (itemsToRemove.length > 0) {
        console.log(`[hm-inventory] Cleaned up ${itemsToRemove.length} abandoned items`);
        // Notify all players to refresh their ground panels after cleanup
        const players = getPlayers();
        for (const playerId of players) {
            updateGroundPanelForPlayer(parseInt(playerId));
        }
    }
}

// ===== ENTITY STREAMING EVENTS FOR REAL-TIME UPDATES =====

// Track when players enter/exit streaming range of dropped items
on('entityEnteringScope', (entity: number, playerId: number) => {
    // Check if the entity is a dropped item
    if (droppedItems.has(entity)) {
        console.log(`[hm-inventory] Player ${playerId} entered streaming range of dropped item ${entity}`);
        // Update ground panel for this specific player
        updateGroundPanelForPlayer(playerId);
    }
});

on('entityLeavingScope', (entity: number, playerId: number) => {
    // Check if the entity was a dropped item
    if (droppedItems.has(entity)) {
        console.log(`[hm-inventory] Player ${playerId} left streaming range of dropped item ${entity}`);
        // Update ground panel for this specific player
        updateGroundPanelForPlayer(playerId);
    }
});

// Handle entity deletion (when items are picked up or cleaned up)
on('entityRemoved', (entity: number) => {
    if (droppedItems.has(entity)) {
        const itemData = droppedItems.get(entity);
        if (itemData) {
            console.log(`[hm-inventory] Dropped item entity ${entity} was removed`);
            // Notify nearby players immediately
            notifyNearbyPlayersOfGroundItemChange(itemData.position);
            
            // Remove from inventory zone and cleanup if empty
            removeItemFromInventoryZone(entity);
        }
        droppedItems.delete(entity);
    }
});

// ===== POLYZONE INTEGRATION FOR INVENTORY =====
// Track inventory zones created for dropped items
const inventoryZones: Map<string, { 
    position: [number, number, number], 
    itemEntities: Set<number>,
    createdAt: number 
}> = new Map();

/**
 * Create inventory zone for dropped items using polyzones
 */
function createInventoryZoneForItems(position: [number, number, number], entityId: number): void {
    // Check for existing zone within merge radius
    const mergeRadius = 3.0;
    let existingZoneId: string | null = null;
    
    for (const [zoneId, zone] of inventoryZones) {
        const distance = Math.sqrt(
            Math.pow(position[0] - zone.position[0], 2) +
            Math.pow(position[1] - zone.position[1], 2) +
            Math.pow(position[2] - zone.position[2], 2)
        );
        
        if (distance <= mergeRadius) {
            existingZoneId = zoneId;
            break;
        }
    }
    
    if (existingZoneId) {
        // Add to existing zone
        inventoryZones.get(existingZoneId)?.itemEntities.add(entityId);
        console.log(`[Inventory] Added item entity ${entityId} to existing zone ${existingZoneId}`);
    } else {
        // Create new zone
        const zoneId = `inventory_zone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        inventoryZones.set(zoneId, {
            position,
            itemEntities: new Set([entityId]),
            createdAt: Date.now()
        });
        
        // Create polyzone on clients
        emitNet('hm-inventory:createInventoryZone', -1, {
            zoneId,
            position: { x: position[0], y: position[1], z: position[2] },
            radius: 2.0
        });
        
        console.log(`[Inventory] Created inventory zone ${zoneId} at`, position);
    }
}

/**
 * Remove item from inventory zone and cleanup if empty
 */
function removeItemFromInventoryZone(entityId: number): void {
    for (const [zoneId, zone] of inventoryZones) {
        if (zone.itemEntities.has(entityId)) {
            zone.itemEntities.delete(entityId);
            
            if (zone.itemEntities.size === 0) {
                // Zone is empty, remove it
                inventoryZones.delete(zoneId);
                emitNet('hm-inventory:destroyInventoryZone', -1, { zoneId });
                console.log(`[Inventory] Destroyed empty inventory zone ${zoneId}`);
            }
            break;
        }
    }
}

// Cleanup timer for expired zones
setInterval(() => {
    const now = Date.now();
    const expireTime = 30 * 60 * 1000; // 30 minutes
    
    for (const [zoneId, zone] of inventoryZones) {
        if (now - zone.createdAt > expireTime) {
            inventoryZones.delete(zoneId);
            emitNet('hm-inventory:destroyInventoryZone', -1, { zoneId });
            console.log(`[Inventory] Expired inventory zone ${zoneId}`);
        }
    }
}, 60000); // Check every minute

// Debug command to refresh all zones for a player
onNet('hm-inventory:server:requestAllZones', () => {
    const source = global.source;
    console.log(`[Inventory] Player ${source} requesting all zone data`);
    
    // Send all active zones to the requesting player
    for (const [zoneId, zone] of inventoryZones) {
        emitNet('hm-inventory:createInventoryZone', source, {
            zoneId,
            position: { x: zone.position[0], y: zone.position[1], z: zone.position[2] },
            radius: 2.0,
            items: {}
        });
    }
    
    console.log(`[Inventory] Sent ${inventoryZones.size} zones to player ${source}`);
});

// ===== EVENT-DRIVEN INVENTORY SYSTEM =====

// Cleanup abandoned items every 5 minutes (keep this timer for cleanup only)
setInterval(cleanupAbandonedItems, 5 * 60 * 1000);

// Track players who are near ground items for immediate notifications
const playersNearGroundItems: Map<number, Set<number>> = new Map(); // playerId -> Set of entityIds

/**
 * Notify nearby players when ground items change
 */
function notifyNearbyPlayersOfGroundItemChange(position: [number, number, number], excludePlayer?: number): void {
    const players = getPlayers();
    for (const playerId of players) {
        const playerIdNum = parseInt(playerId);
        if (excludePlayer && playerIdNum === excludePlayer) continue;
        
        if (!GetPlayerName(playerId)) continue;
        
        const playerPed = GetPlayerPed(playerIdNum);
        const playerPos = GetEntityCoords(playerPed);
        const playerPosition: [number, number, number] = [playerPos[0], playerPos[1], playerPos[2]];
        
        const distance = getDistance(playerPosition, position);
        
        // Notify players within streaming range (25 units)
        if (distance <= 25.0) {
            updateGroundPanelForPlayer(playerIdNum);
        }
    }
}

/**
 * Update ground panel for a specific player immediately
 */
function updateGroundPanelForPlayer(playerId: number): void {
    if (!GetPlayerName(playerId.toString())) return;
    
    const playerPed = GetPlayerPed(playerId);
    const playerPos = GetEntityCoords(playerPed);
    const playerPosition: [number, number, number] = [playerPos[0], playerPos[1], playerPos[2]];
    
    const groundPanelItems: GroundPanelItem[] = [];
    
    // Check all dropped items for proximity
    for (const [entityId, itemData] of droppedItems) {
        // Verify entity still exists
        if (!DoesEntityExist(entityId)) {
            droppedItems.delete(entityId);
            continue;
        }
        
        const distance = getDistance(playerPosition, itemData.position);
        
        // Only include items within 20 units for ground panel
        if (distance <= 20.0) {
            const groundPanelItem: GroundPanelItem = {
                entityId: itemData.entityId,
                definitionId: itemData.definitionId,
                quantity: itemData.quantity,
                distanceCategory: getDistanceCategory(distance),
                distance: Math.round(distance * 10) / 10 // Round to 1 decimal place
            };
            groundPanelItems.push(groundPanelItem);
        }
    }
    
    // Always send updated ground panel data (client will handle deduplication if needed)
    emitNet('hm-inventory:client:updateGroundPanel', playerId, groundPanelItems);
}

// Initialize player tracking when they join
on('playerJoining', (source: string) => {
    const playerId = parseInt(source);
    playerNearbyItems.set(playerId, []);
    playersNearGroundItems.set(playerId, new Set());
});

// Cleanup player data when they leave
on('playerDropped', async () => {
    const source = global.source;
    delete playerInventories[source];
    playerNearbyItems.delete(source);
    playersNearGroundItems.delete(source);
});

// Handle client request for ground panel update (for UI refresh)
onNet('hm-inventory:server:requestGroundPanelUpdate', () => {
    const source = global.source;
    updateGroundPanelForPlayer(source);
});