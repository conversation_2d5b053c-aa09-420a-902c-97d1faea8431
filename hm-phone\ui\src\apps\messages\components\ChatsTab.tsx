import React, { JSX, useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { useMessagesStore } from '../stores/messagesStore';
import { useNavigation } from '../../../navigation/hooks';
import { Conversation } from '../../../../../shared/types';
import { useContactsStore } from '../../contacts/stores/contactsStore';
import { usePhoneStore } from '../../../common/stores/phoneStateStore';
import { formatConversationDate } from '../../../utils/timeUtils';

interface ChatsTabProps {
  conversations: Conversation[];
  onNewConversation?: () => void;
}

interface LatestMessage {
  content: string | JSX.Element;
  timestamp: string;
  sender: string;
  isRead?: boolean;
  status?: string;
}

const ChatsTab: React.FC<ChatsTabProps> = ({ conversations, onNewConversation }) => {
  const { openView } = useNavigation();
  const { ui, loading, actions, hasMoreConversations } = useMessagesStore();
  const { markAsRead, setActiveChat } = ui;
  const [searchTerm, setSearchTerm] = useState('');
  const { contacts } = useContactsStore();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const getLatestMessage = (conversation: Conversation): LatestMessage | null => {
    // First check if we have a latest_message property (from server)
    if (conversation.latest_message) {
      const latestMessage = conversation.latest_message;

      // Get timestamp for display
      let timestamp = 0;
      if (latestMessage.metadata?.timestamp) {
        timestamp = latestMessage.metadata.timestamp;
      } else if (typeof latestMessage.timestamp === 'string') {
        timestamp = new Date(latestMessage.timestamp).getTime();
      }

      // Format the timestamp using our WhatsApp-style formatter
      const formattedTime = formatConversationDate(timestamp || Date.now());

      // Use message content or message field
      let content: string | JSX.Element = '';
      if (typeof latestMessage.content === 'string') {
        content = latestMessage.content;
      } else if (typeof latestMessage.message === 'string') {
        content = latestMessage.message;
      }

      // Handle different message types
      if (latestMessage.type) {
        switch (latestMessage.type) {
          case 'image': {
            let messageText = 'Image';
            if (typeof latestMessage.content === 'string') {
              messageText = latestMessage.content || 'Image';
            } else if (typeof latestMessage.message === 'string') {
              messageText = latestMessage.message || 'Image';
            }

            content = (
              <>
                <i className="fas fa-image mr-2" />
                {messageText}
              </>
            );
            break;
          }
          case 'location': {
            let locationData = null;
            if (typeof latestMessage.metadata === 'string') {
              try {
                const parsed = JSON.parse(latestMessage.metadata);
                locationData = parsed.location;
              } catch (e) {
                console.error('Failed to parse location metadata', e);
              }
            } else if (latestMessage.metadata && typeof latestMessage.metadata === 'object') {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              locationData = (latestMessage.metadata as any).location;
            }

            content = (
              <>
                <i className="fas fa-map-marker-alt mr-2" />
                {locationData?.name || 'Location'}
              </>
            );
            break;
          }
          case 'contact': {
            let contactData = null;
            if (typeof latestMessage.metadata === 'string') {
              try {
                const parsed = JSON.parse(latestMessage.metadata);
                contactData = parsed.contact;
              } catch (e) {
                console.error('Failed to parse contact metadata', e);
              }
            } else if (latestMessage.metadata && typeof latestMessage.metadata === 'object') {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              contactData = (latestMessage.metadata as any).contact;
            }

            content = (
              <>
                <i className="fas fa-user mr-2" />
                {contactData?.display_name || 'Contact'}
              </>
            );
            break;
          }
        }
      }

      const sender = typeof latestMessage.sender === 'string' ? latestMessage.sender : '';

      return {
        content,
        timestamp: formattedTime,
        sender: sender,
      };
    }

    // Fallback to using messages array if latest_message is not available
    if (!conversation.messages?.length) return null;

    // We need to use any here to handle the mix of DB and UI properties
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const messages = conversation.messages as any[];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const latestMessage = messages.reduce((latest: any, current: any) => {
      // Get timestamp from metadata or use the message timestamp
      const latestTimestamp = typeof latest.timestamp === 'string' ? new Date(latest.timestamp).getTime() : 0;
      const currentTimestamp = typeof current.timestamp === 'string' ? new Date(current.timestamp).getTime() : 0;

      // Get metadata timestamp if available
      const latestMetaTimestamp = latest.metadata?.timestamp || 0;
      const currentMetaTimestamp = current.metadata?.timestamp || 0;

      // Use the larger of the two timestamps
      const latestTime = Math.max(latestTimestamp, latestMetaTimestamp);
      const currentTime = Math.max(currentTimestamp, currentMetaTimestamp);

      return currentTime > latestTime ? current : latest;
    }, messages[0]);

    // Get timestamp for display
    let timestamp = 0;
    if (latestMessage.metadata?.timestamp) {
      timestamp = latestMessage.metadata.timestamp;
    } else if (typeof latestMessage.timestamp === 'string') {
      timestamp = new Date(latestMessage.timestamp).getTime();
    }

    // Format the timestamp using our WhatsApp-style formatter
    const formattedTime = formatConversationDate(timestamp || Date.now());

    // Use message content or message field
    let content: string | JSX.Element = '';
    if (typeof latestMessage.content === 'string') {
      content = latestMessage.content;
    } else if (typeof latestMessage.message === 'string') {
      content = latestMessage.message;
    }

    // Handle different message types
    if (latestMessage.type) {
      switch (latestMessage.type) {
        case 'image': {
          let messageText = 'Image';
          if (typeof latestMessage.content === 'string') {
            messageText = latestMessage.content || 'Image';
          } else if (typeof latestMessage.message === 'string') {
            messageText = latestMessage.message || 'Image';
          }

          content = (
            <>
              <i className="fas fa-image mr-2" />
              {messageText}
            </>
          );
          break;
        }
        case 'location': {
          let locationData = null;
          if (typeof latestMessage.metadata === 'string') {
            try {
              const parsed = JSON.parse(latestMessage.metadata);
              locationData = parsed.location;
            } catch (e) {
              console.error('Failed to parse location metadata', e);
            }
          } else if (latestMessage.metadata && typeof latestMessage.metadata === 'object') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            locationData = (latestMessage.metadata as any).location;
          }

          content = (
            <>
              <i className="fas fa-map-marker-alt mr-2" />
              {locationData?.name || 'Location'}
            </>
          );
          break;
        }
        case 'contact': {
          let contactData = null;
          if (typeof latestMessage.metadata === 'string') {
            try {
              const parsed = JSON.parse(latestMessage.metadata);
              contactData = parsed.contact;
            } catch (e) {
              console.error('Failed to parse contact metadata', e);
            }
          } else if (latestMessage.metadata && typeof latestMessage.metadata === 'object') {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            contactData = (latestMessage.metadata as any).contact;
          }

          content = (
            <>
              <i className="fas fa-user mr-2" />
              {contactData?.display_name || 'Contact'}
            </>
          );
          break;
        }
      }
    }
    const sender = typeof latestMessage.sender === 'string' ? latestMessage.sender : '';

    return {
      content,
      timestamp: formattedTime,
      sender: sender,
    };
  };

  const handleChatSelect = (conv: Conversation) => {
    openView('conversation', { chatId: conv.id });
    setActiveChat(conv.id);
    const phoneNumber = usePhoneStore.getState().userProfile.phoneNumber;
    if (phoneNumber && conv.members[phoneNumber] && conv.members[phoneNumber].unread_count > 0) {
      markAsRead(conv.id);
    }
  };
  const filteredConversations = useMemo(() => {
    if (!searchTerm.trim()) {
      return conversations;
    }

    const term = searchTerm.toLowerCase().trim();
    return conversations.filter(conv => {
      // Search in conversation name
      if (conv.name && conv.name.toLowerCase().includes(term)) {
        return true;
      }

      // Search in phone number
      if (conv.members && Object.keys(conv.members).some(phone => phone.includes(term))) {
        return true;
      }

      return false;
    });
  }, [conversations, searchTerm]);

  React.useEffect(() => {
    const { actions: contactsActions } = useContactsStore.getState();

    if (contacts.length === 0) {
      contactsActions.getContacts();
    } else {
      // do nothing
      console.log('[ChatsTab] Contacts already loaded');
    }
  }, [contacts]);

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current || isLoadingMore || !hasMoreConversations || searchTerm || loading) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
    const scrollPosition = scrollTop + clientHeight;

    if (scrollPosition >= scrollHeight * 0.85) {
      setIsLoadingMore(true);
      const scrollContainer = scrollContainerRef.current;
      const currentScrollTop = scrollContainer.scrollTop;

      actions.loadMoreConversations().finally(() => {
        if (scrollContainerRef.current) {
          setTimeout(() => {
            scrollContainer.scrollTop = currentScrollTop;
          }, 50); // Short timeout to ensure DOM is updated
        }
        setIsLoadingMore(false);
      });
    }
  }, [actions, hasMoreConversations, isLoadingMore, searchTerm, loading]);

  // Add scroll event listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  return (
    <div className="h-full relative">
      {/* Header */}
      <div className="px-4 py-3 border-b border-white/10">
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full bg-white/10 text-white rounded-full px-4 py-2 pl-9 focus:outline-none focus:ring-2 focus:ring-white/20 text-sm"
          />
          <i className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-white/60" />
        </div>
      </div>

      <div ref={scrollContainerRef} className="overflow-y-auto pb-24 pt-2" style={{ height: 'calc(100% - 70px)' }}>
        {/* Initial loading spinner - only shown when first loading conversations */}
        {loading && !isLoadingMore && conversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[80%]">
            <div className="text-white/60 text-center p-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white/20 mx-auto mb-4"></div>
              <p>Loading conversations...</p>
            </div>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[80%]">
            <div className="text-white/60 text-center p-4">
              {searchTerm.trim() && conversations.length > 0 ? (
                <>
                  <p>No conversations match your search</p>
                  <p className="text-xs mt-2">Try a different search term</p>
                </>
              ) : (
                <>
                  <p>No conversations found</p>
                  <p className="text-xs mt-2">Start a new conversation by clicking the + button</p>
                </>
              )}
            </div>
          </div>
        ) : (
          // Sort conversations by updated_at timestamp (most recent first)
          [...filteredConversations]
            .sort((a, b) => {
              // Use the updated_at field for sorting
              const timestampA = new Date(a.updated_at).getTime();
              const timestampB = new Date(b.updated_at).getTime();

              // Sort in descending order (newest first)
              return timestampB - timestampA;
            })
            .map(conv => {
              const latestMessage = getLatestMessage(conv);

              return (
                <div
                  key={`chat-${conv.id}`}
                  onClick={() => handleChatSelect(conv)}
                  className="conversation-item flex gap-3 p-3 hover:bg-white/5 cursor-pointer border-b border-white/10"
                >
                  <div className={`w-10 h-10 rounded-full ${conv.avatar && conv.avatar !== 'group_icon' ? 'ring-2 ring-blue-500' : 'bg-white/20'} flex-shrink-0 overflow-hidden self-center flex items-center justify-center`}>
                    {conv.type === 'group' || conv.avatar === 'group_icon' ? (
                      <i className="fas fa-users text-white/80 text-sm" />
                    ) : (
                      <>
                        {conv.avatar && conv.avatar !== 'group_icon' ? (
                          <img
                            src={conv.avatar}
                            alt={conv.name || 'Contact'}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500/30 to-blue-600/30 flex items-center justify-center">
                            <span className="text-white text-sm">
                              {(conv.name || '?').charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  <div className="flex-1 min-w-0 flex flex-col justify-between py-0.5">
                    <div className="flex items-center justify-between">
                      <div className="text-white text-sm font-medium truncate max-w-[70%] flex items-center">
                        <span className="truncate">{conv.name}</span>
                        {conv.type === 'group' && (
                          <span className="inline-flex items-center ml-2 flex-shrink-0">
                            <i className="fas fa-users text-[10px] text-blue-400" />
                            <span className="text-[10px] text-blue-400 ml-1 font-bold">
                              · {Object.keys(conv.members).length}
                            </span>
                          </span>
                        )}
                      </div>
                      {latestMessage && (
                        <div className="text-[10px] text-white/40 flex-shrink-0 ml-2">
                          {latestMessage.timestamp}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-white/40 truncate max-w-[80%]">
                        {latestMessage?.content}
                      </div>
                      {Object.keys(conv.members).some(phone => {
                        const member = conv.members[phone];
                        return member && member.unread_count > 0;
                      }) && (
                        <div className="bg-blue-500 text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full flex-shrink-0">
                          {Object.values(conv.members).find(member => member.unread_count > 0)?.unread_count || 0}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
        )}

        {/* Loading indicator for pagination - added as a separate element after the conversations */}
        {isLoadingMore && (
          <div className="flex justify-center items-center py-4 mt-2 border-t border-white/10 bg-black/20">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-white/80 text-sm font-medium">Loading more conversations (3s delay)...</span>
          </div>
        )}

        {/* Show "No more conversations" message when we've loaded all conversations */}
        {!isLoadingMore && !hasMoreConversations && conversations.length > 0 && (
          <div className="text-center py-4 mb-2 text-white/60 text-sm border-t border-white/10 bg-black/10">
            <i className="fas fa-check-circle mr-2"></i>
            No more conversations to load
          </div>
        )}
      </div>
      {onNewConversation && (
        <button
          onClick={() => {
            onNewConversation();
          }}
          className="absolute bottom-4 right-4 w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center shadow-lg hover:bg-blue-400 transition-colors"
        >
          <i className="fas fa-comment-dots text-white text-lg" />
        </button>
      )}
    </div>
  );
};

export default ChatsTab;
