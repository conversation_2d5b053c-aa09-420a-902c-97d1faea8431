{"name": "hm-handling", "version": "1.0.0", "description": "Vehicle Handling System for FiveM", "main": "index.js", "engines": {"node": "22.16.0"}, "scripts": {"watch:all": "concurrently -n \"GAME,UI\" -c \"blue,green\" \"npm run watch:game\" \"npm run watch:ui\"", "build:all": "webpack --config webpack.config.js && cd ui && npm run build", "lint": "eslint . --ext .ts && prettier --write \"**/*.{ts,tsx}\"", "watch:game": "webpack --watch --config webpack.config.js", "watch:ui": "cd ui && npm run dev"}, "keywords": ["fivem", "handling", "vehicle", "typescript"], "author": "HM", "license": "MIT", "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "concurrently": "^8.2.2", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.91.0", "webpack-cli": "^6.0.1"}, "dependencies": {"axios": "^1.9.0"}}