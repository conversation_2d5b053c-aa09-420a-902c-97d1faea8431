interface PolyZoneAPI {
    createBoxZone: (name: string, center: { x: number; y: number }, width: number, height: number, options?: any) => void;
    createCircleZone: (name: string, center: { x: number; y: number }, radius: number, options?: any) => void;
    createPolygonZone: (name: string, vertices: { x: number; y: number }[], options?: any) => void;
    toggleDebug: (show: boolean) => void;
}

export class GarageManager {
    private polyzones: PolyZoneAPI | null = null;
    private debugGarages = false;

    constructor() {
        console.log('GarageManager Initializing...');
        this.initializePolyZoneAPI();
        this.registerEventListeners();
        RegisterCommand('garagedebug', () => this.toggleDebugDisplay(), false);
    }

    private async initializePolyZoneAPI() {
        // Wait for hm-polyzones to be ready and export its functions
        const pzExports = global.exports['hm-polyzones'];
        if (pzExports) {
            // Perform a more robust check or cast to unknown first
            this.polyzones = pzExports as unknown as PolyZoneAPI;
            // console.log('[hm-garages] PolyZoneAPI loaded:', this.polyzones); // Avoid logging the entire exports object
            console.log('[hm-garages] PolyZoneAPI successfully loaded. Available functions:', Object.keys(this.polyzones).join(', '));
            this.defineSampleGarages();
        } else {
            console.error('hm-polyzones resource not found or not exporting API. Garages will not function.');
            // Optionally, retry after a delay
            setTimeout(() => this.initializePolyZoneAPI(), 5000); 
        }
    }

    private defineSampleGarages(): void {
        if (!this.polyzones) {
            console.error('PolyZone API not available, cannot define garages.');
            return;
        }

        console.log('Defining sample garages...');

        // Sample Box Garage
        this.polyzones.createBoxZone(
            'Downtown Garage',
            { x: 100.0, y: -75.0 },
            20.0, // width
            30.0, // height
            {
                minZ: 0.0,
                maxZ: 5.0,
                debugColor: [255, 165, 0, 150], // Orange
                heading: Math.PI / 4 // 45 degrees in radians
            }
        );

        // Sample Circle Garage
        this.polyzones.createCircleZone(
            'Vinewood Hills Garage',
            { x: -500.0, y: 800.0 },
            15.0, // radius
            {
                minZ: 18.0,
                maxZ: 22.0,
                debugColor: [0, 0, 255, 150] // Blue
            }
        );

        // Sample Polygon Garage (e.g., L-shape)
        this.polyzones.createPolygonZone(
            'Airport Parking Lot',
            [
                { x: 1200.0, y: -1800.0 },
                { x: 1250.0, y: -1800.0 },
                { x: 1250.0, y: -1850.0 },
                { x: 1225.0, y: -1850.0 },
                { x: 1225.0, y: -1825.0 },
                { x: 1200.0, y: -1825.0 },
            ],
            {
                minZ: 0.0,
                maxZ: 10.0,
                debugColor: [255, 255, 0, 150] // Yellow
            }
        );
        console.log('Sample garages defined.');
    }

    private registerEventListeners(): void {
        onNet('hm-polyzones:enterZone', (zoneName: string) => {
            // Check if the entered zone is one of our garages (could be more specific if needed)
            if (zoneName.toLowerCase().includes('garage') || zoneName.toLowerCase().includes('parking')) {
                console.log(`Entered garage: ${zoneName}`);
                // TODO: Show UI notification or similar
                this.showNotification(`Entered garage: ${zoneName}`, true);
            }
        });

        onNet('hm-polyzones:exitZone', (zoneName: string) => {
            if (zoneName.toLowerCase().includes('garage') || zoneName.toLowerCase().includes('parking')) {
                console.log(`Left garage: ${zoneName}`);
                // TODO: Show UI notification or similar
                this.showNotification(`Left garage: ${zoneName}`, false);
            }
        });
    }
    
    public toggleDebugDisplay(): void {
        this.debugGarages = !this.debugGarages;
        this.toggleGarageDebug(this.debugGarages);
        console.log(`Garage Debug Display: ${this.debugGarages ? 'Enabled' : 'Disabled'} (Uses hm-polyzones debug draw)`);
    }

    private toggleGarageDebug(show: boolean): void {
        // Ensure polyzones and its toggleDebug method exist before calling
        if (this.polyzones && typeof this.polyzones.toggleDebug === 'function') {
            this.polyzones.toggleDebug(show); // Call the function
        }
    }

    // Basic notification function (replace with your actual UI notification system)
    private showNotification(message: string, isEntry: boolean): void {
        SetNotificationTextEntry('STRING');
        AddTextComponentString(message);
        DrawNotification(false, !isEntry); // true for blip, false for no blip. !isEntry for different sounds.
    }
}
