/**
 * Modal Store
 *
 * This store manages the state of modals in the application.
 * It allows components to open and close modals, and to pass data to them.
 */
import { create } from 'zustand';

// Define a generic type for modal props
type ModalProps = Record<string, unknown>;

interface ModalState {
  isOpen: boolean;
  modalType: string | null;
  modalProps: ModalProps;

  openModal: (modalType: string, modalProps?: ModalProps) => void;
  closeModal: () => void;
}

export const useModalStore = create<ModalState>(set => ({
  isOpen: false,
  modalType: null,
  modalProps: {},

  openModal: (modalType, modalProps = {}) => {
    set({
      isOpen: true,
      modalType,
      modalProps
    });
  },

  closeModal: () => {
    set({
      isOpen: false,
      modalType: null,
      modalProps: {}
    });
  }
}));
