import { useInventoryStore } from '../stores/inventoryStore';
import { Toast } from '@shared/inventory.types';

// Toast notification types for the inventory system
export enum ToastType {
  INVENTORY_FULL = 'inventory_full',
  NOT_ENOUGH_SPACE = 'not_enough_space',
  WEIGHT_LIMIT = 'weight_limit',
  INVENTORY_BLOCKED = 'inventory_blocked',
  ITEM_ADDED = 'item_added',
  ITEM_REMOVED = 'item_removed',
  ITEM_MOVED = 'item_moved',
  ITEM_EQUIPPED = 'item_equipped',
  ITEM_UNEQUIPPED = 'item_unequipped',
  PURCHASE_SUCCESS = 'purchase_success',
  PURCHASE_FAILED = 'purchase_failed',
  CUSTOM = 'custom'
}

// Interface for toast parameters
export interface ToastParams {
  itemName?: string;
  quantity?: number;
  amount?: number;
  itemIcon?: string;
  duration?: number;
  customMessage?: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  icon?: string;
}

// Default toast configurations with both message and messageTemplate
interface ToastConfig {
  type: 'info' | 'success' | 'warning' | 'error';
  message: string; // Required for Omit<Toast, 'id'>
  messageTemplate: string; // For template string replacement
  icon: string;
  duration: number;
}

// Default toast configurations
const toastConfigs: Record<ToastType, ToastConfig> = {
  [ToastType.INVENTORY_FULL]: {
    type: 'warning',
    icon: 'fa-box',
    message: 'Inventory is full',
    messageTemplate: 'Inventory is full',
    duration: 3000
  },
  [ToastType.NOT_ENOUGH_SPACE]: {
    type: 'warning',
    icon: 'fa-exclamation-triangle',
    message: 'Not enough space',
    messageTemplate: 'Not enough space',
    duration: 3000
  },
  [ToastType.WEIGHT_LIMIT]: {
    type: 'error',
    icon: 'fa-weight-hanging',
    message: 'Weight limit exceeded',
    messageTemplate: 'Weight limit exceeded',
    duration: 3000
  },
  [ToastType.INVENTORY_BLOCKED]: {
    type: 'error',
    icon: 'fa-lock',
    message: 'Inventory can\'t open',
    messageTemplate: 'Inventory can\'t open',
    duration: 3000
  },
  [ToastType.ITEM_ADDED]: {
    type: 'success',
    icon: 'fa-plus',
    message: 'Added',
    messageTemplate: 'Added: {itemName}',
    duration: 2500
  },
  [ToastType.ITEM_REMOVED]: {
    type: 'info',
    icon: 'fa-minus',
    message: 'Removed',
    messageTemplate: 'Removed: {itemName}',
    duration: 2500
  },
  [ToastType.ITEM_MOVED]: {
    type: 'info',
    icon: 'fa-arrows-alt',
    message: 'Moved',
    messageTemplate: 'Moved: {itemName}',
    duration: 2500
  },
  [ToastType.ITEM_EQUIPPED]: {
    type: 'success',
    icon: 'fa-check',
    message: 'Equipped',
    messageTemplate: 'Equipped: {itemName}',
    duration: 2500
  },
  [ToastType.ITEM_UNEQUIPPED]: {
    type: 'info',
    icon: 'fa-times',
    message: 'Unequipped',
    messageTemplate: 'Unequipped: {itemName}',
    duration: 2500
  },
  [ToastType.PURCHASE_SUCCESS]: {
    type: 'success',
    icon: 'fa-shopping-cart',
    message: 'Purchase successful',
    messageTemplate: 'Purchase successful: {itemName}',
    duration: 3000
  },
  [ToastType.PURCHASE_FAILED]: {
    type: 'error',
    icon: 'fa-credit-card',
    message: 'Purchase failed: Insufficient funds',
    messageTemplate: 'Purchase failed: Insufficient funds',
    duration: 3000
  },
  [ToastType.CUSTOM]: {
    type: 'info',
    icon: 'fa-info-circle',
    message: 'Notification',
    messageTemplate: '{customMessage}',
    duration: 3000
  }
};

/**
 * Create a toast message with standardized formatting based on type
 * This is the primary function for creating standardized toast notifications.
 * 
 * @param toastType Type of toast notification from predefined list
 * @param params Optional parameters for the toast (item name, quantity, etc)
 * 
 * @example
 * ```typescript
 * import { ToastType } from '../utils/toastUtils';
 * import { useInventoryStore } from '../stores/inventoryStore';
 * 
 * // Simple usage
 * const store = useInventoryStore.getState();
 * store.showToast(ToastType.INVENTORY_FULL);
 * 
 * // With parameters
 * store.showToast(ToastType.ITEM_ADDED, { 
 *   itemName: 'Bandage', 
 *   quantity: 5, 
 *   itemIcon: 'fa-bandage' 
 * });
 * 
 * // Custom toast
 * store.showToast(ToastType.CUSTOM, {
 *   customMessage: 'Your custom message here',
 *   type: 'info',
 *   icon: 'fa-info-circle',
 *   duration: 4000
 * });
 * ```
 */
export function createToast(toastType: ToastType, params: ToastParams = {}): void {
  const store = useInventoryStore.getState();
  const config = toastConfigs[toastType];
  
  if (!config) {
    console.error(`Unknown toast type: ${toastType}`);
    return;
  }
  
  let message = config.messageTemplate;
  
  // Replace placeholders in message template
  if (params.itemName) {
    message = message.replace('{itemName}', params.itemName);
  }
  
  if (params.customMessage) {
    message = message.replace('{customMessage}', params.customMessage);
  }
  
  // Build the toast object
  const toast: Omit<Toast, 'id'> = {
    message,
    type: params.type || config.type,
    icon: params.icon || config.icon,
    duration: params.duration || config.duration
  };
  
  // Add optional parameters if provided
  if (params.itemIcon) toast.itemIcon = params.itemIcon;
  if (params.quantity) toast.quantity = params.quantity;
  if (params.amount) toast.amount = params.amount;
  
  // Show the toast
  store.addToast(toast);
}
