// Entry point for Tablet UI
import React from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import { setupNuiListener } from './services/nui';
import App from './App';

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Failed to find the root element');
}

// Register NUI listener for tablet show/hide
setupNuiListener();

const root = createRoot(rootElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);
