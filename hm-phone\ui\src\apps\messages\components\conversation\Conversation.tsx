import React, { useRef, useEffect, useState, useCallback, useLayoutEffect } from 'react';
import MessageBubble from '../MessageBubble';
import { useMessagesStore } from '../../stores/messagesStore';
import { useNavigation } from '../../../../navigation/hooks';
import { useContactsStore } from '../../../contacts/stores/contactsStore';
import { usePhoneStore } from '../../../../common/stores/phoneStateStore';
import { useSelectionStore } from '../../../../common/stores/selectionStore';
import { clientRequests } from '../../../../fivem/clientRequestSender';
import { Contact, Conversation as ConversationType, Message } from '@shared/types';



interface ConversationProps {
  activeChat: number | string | null;
  handleStartCall: (phone: string) => void;
  searchTerm?: string;
  setSearchTerm?: (term: string) => void;
}

const Conversation: React.FC<ConversationProps> = ({
  activeChat,
  handleStartCall,
  searchTerm = '',
  setSearchTerm
}) => {
  // Local state for conversation view
  const [newMessage, setNewMessage] = useState('');

  // Initialize selectedPhotos with a more robust approach
  const [selectedPhotos, setSelectedPhotos] = useState<Record<string | number, string[]>>(() => {
    try {
      const savedPhotos = localStorage.getItem('messages_selectedPhotos');
      if (savedPhotos) {
        try {
          const parsed = JSON.parse(savedPhotos);

          // Validate the parsed data structure
          if (parsed && typeof parsed === 'object') {
            // Convert any non-array values to arrays
            const validated: Record<number, string[]> = {};

            Object.entries(parsed).forEach(([key, value]) => {
              const chatId = parseInt(key);
              if (!isNaN(chatId)) {
                if (Array.isArray(value)) {
                  // Filter out any non-string values
                  validated[chatId] = (value as unknown[]).filter(item => typeof item === 'string');
                } else {
                  // Initialize as empty array if value is not an array
                  validated[chatId] = [];
                }
              }
            });

            return validated;
          }
        } catch (parseError) {
          console.error('[Conversation] Error parsing selectedPhotos JSON:', parseError);
        }
      }
    } catch (error) {
      console.error('[Conversation] Error loading selected photos from localStorage:', error);
    }

    // Return empty object as fallback
    return {};
  });
  const [showAttachMenu, setShowAttachMenu] = useState(false);

  // Store access
  const { conversations, actions, hasMoreMessages, messagesById, loading } = useMessagesStore();
  const { openAppView, openView, openAppAndReturn } = useNavigation();
  const { contacts } = useContactsStore();
  const { userProfile } = usePhoneStore();
  const playerPhone = userProfile?.phoneNumber;
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { startSelection } = useSelectionStore();
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Helper functions for managing selected photos
  const removePhotoFromConversation = (chatId: number | string, index: number) => {
    // Validate parameters
    if (!chatId || typeof index !== 'number' || index < 0) {
      return;
    }
    const chatIdStr = chatId.toString();
    setSelectedPhotos(prev => {
      const safePrev = prev || {};
      if (!Array.isArray(safePrev[chatIdStr]) || safePrev[chatIdStr].length === 0) {
        return safePrev;
      }
      if (index >= safePrev[chatIdStr].length) {
        return safePrev;
      }

      const photos = [...safePrev[chatIdStr]];
      photos.splice(index, 1);

      const newState = {
        ...safePrev,
        [chatIdStr]: photos
      };

      try {
        localStorage.setItem('messages_selectedPhotos', JSON.stringify(newState));
      } catch (error) {
        console.error('[Conversation] Error saving to localStorage:', error);
      }

      return newState;
    });
  };

  /**
   * Unified method to handle sending different types of attachments
   */
  const handleSendAttachment = async (
    type: 'text' | 'image' | 'contact' | 'location',
    data?: unknown
  ) => {
    if (!activeChat) return;

    switch (type) {
      case 'text': {
        const text = data as string;
        if (!text.trim()) return;

        // Send a text message
        actions.sendMessage(activeChat, {
          message: text,
          type: 'text'
        });

        // Clear the input field
        setNewMessage('');
        break;
      }

      case 'contact': {
        const contacts = data as Contact[];
        if (!contacts || !contacts.length) return;

        // Get the first contact (we only support sending one contact at a time)
        const contact = contacts[0];

        // Send the contact as a message - use the full contact object
        actions.sendMessage(activeChat, {
          message: `Contact: ${contact.name}`,
          type: 'contact',
          metadata: {
            contact: {
              id: contact.id,
              identifier: contact.identifier,
              stateid: contact.stateid,
              number: contact.number,
              name: contact.name,
              favorite: contact.favorite,
              avatar: contact.avatar,
              created_at: contact.created_at,
              updated_at: contact.updated_at
            }
          }
        });
        break;
      }

      case 'location':
        try {
          // Get the current location from the client
          const response = await clientRequests.send('messages', 'getCurrentLocation', {});

          if (response.success && 'data' in response) {
            const locationData = response.data as {
              coords: { x: number; y: number; z: number };
              address: string;
            };

            // Send the location as a message
            actions.sendMessage(activeChat, {
              message: locationData.address || 'GPS Location',
              type: 'location',
              metadata: {
                location: {
                  name: 'GPS Location',
                  address: locationData.address || 'Los Santos',
                  coordinates: {
                    lat: locationData.coords.y || 0,
                    lng: locationData.coords.x || 0
                  }
                }
              }
            });
          } else {
            throw new Error('Failed to get location data');
          }
        } catch (error) {
          console.error('Error getting location:', error);

          // Fallback to sending a generic location if the request fails
          actions.sendMessage(activeChat, {
            message: 'My Location',
            type: 'location',
            metadata: {
              location: {
                name: 'GPS Location',
                address: 'Los Santos',
                coordinates: {
                  lat: 0,
                  lng: 0
                }
              }
            }
          });
        }
        break;

      case 'image': {
        // Handle image attachments
        const photos = data as string[];

        if (!photos || !photos.length) {
          console.log('[Conversation] No photos to add');
          return;
        }

        console.log(
          '[Conversation] Processing image attachment:',
          typeof photos,
          Array.isArray(photos)
        );

        if (typeof photos === 'object' && Array.isArray(photos)) {
          // If we're sending from the photo selection, use our single source of truth
          handlePhotoSelection(photos);
        } else if (typeof photos === 'string') {
          // If we're sending a single photo directly
          console.log('[Conversation] Adding single photo to conversation', activeChat);
          actions.sendMessage(activeChat, {
            message: 'Photo',
            type: 'image',
            metadata: {
              mediaUrls: [photos]
            }
          });
        }
        break;
      }

      default:
        console.error(`Unsupported attachment type: ${type}`);
    }

    // Always scroll to bottom after sending any type of attachment
    setShouldScrollToBottom(true);
  };

  /**
   * Handle attachment button click
   */
  const handleAttachmentClick = (type: 'location' | 'contact' | 'camera') => {
    if (!activeChat) return;

    if (type === 'camera') {
      // Define the callback function to handle the photo
      const handleCameraPhoto = (result: unknown) => {
        const selectedPhotos = Array.isArray(result)
          ? (result.filter(item => typeof item === 'string') as string[])
          : [];

        // Ensure we have a valid photo
        if (!selectedPhotos || !Array.isArray(selectedPhotos) || selectedPhotos.length === 0) {
          return;
        }

        // Send the photo as an attachment
        handleSendAttachment('image', selectedPhotos);
      };

      // Navigate to the camera app with callback
      openAppAndReturn('messages', 'camera', {}, undefined, handleCameraPhoto);

      // Close the attachment menu
      setShowAttachMenu(false);
    } else if (type === 'contact') {
      // Use the selection store to start selection mode
      startSelection({
        mode: 'single', // Only select one contact at a time
        maxSelection: 1,
        initialSelected: [],
        purpose: 'share',
        targetApp: 'messages',
        returnPath: 'messages',
        callback: selectedContacts => {
          // Ensure we have valid contacts
          if (
            !selectedContacts ||
            !Array.isArray(selectedContacts) ||
            selectedContacts.length === 0
          ) {
            return;
          }

          // Send the contact as an attachment
          handleSendAttachment('contact', selectedContacts);
        }
      });

      // Navigate to the contacts app
      // Note: We don't need to pass the callback here as it's already set in startSelection
      openAppAndReturn('messages', 'contacts', {});
    } else if (type === 'location') {
      // Send the current location
      handleSendAttachment('location');

      // Close the attachment menu
      setShowAttachMenu(false);
    }
  };

  /**
   * Handle photo selection from Photos app
   * This is the single source of truth for updating selected photos
   */
  const handlePhotoSelection = (photos: string[]) => {
    if (!activeChat) {
      return;
    }

    // Convert activeChat to string to use as a key in the selectedPhotos object
    const chatId = activeChat.toString();

    // Validate photos array
    if (!photos || !Array.isArray(photos)) {
      return;
    }

    if (photos.length === 0) {
      return;
    }

    // Filter out any non-string values
    const validPhotos = photos.filter(photo => typeof photo === 'string');

    if (validPhotos.length === 0) {
      return;
    }

    // Force update the selectedPhotos state directly
    const existingPhotos = Array.isArray(selectedPhotos[chatId]) ? [...selectedPhotos[chatId]] : [];
    const newPhotos = [...existingPhotos];

    // Add each selected photo
    validPhotos.forEach(photo => {
      if (!newPhotos.includes(photo)) {
        newPhotos.push(photo);
      }
    });
    const newState = {
      ...selectedPhotos,
      [chatId]: newPhotos
    };

    // Save to localStorage first for persistence
    try {
      localStorage.setItem('messages_selectedPhotos', JSON.stringify(newState));
    } catch (error) {
      console.error('[Conversation] Error saving to localStorage:', error);
    }
    setSelectedPhotos(newState);
    setNewMessage(prev => prev + '');
  };

  // Get current conversation
  const currentConversation = conversations.find((c : ConversationType) => c.id === activeChat);

  const handleAvatarClick = () => {
    if (!currentConversation || currentConversation.type !== 'direct') return;

    // Get the other participant's phone number
    const otherParticipantPhone = getOtherParticipantPhone(currentConversation);

    if (!otherParticipantPhone) {
      console.error('Could not determine conversation phone number');
      return;
    }

    // Find the contact in contacts store
    const contact = contacts?.find(c => c.number === otherParticipantPhone);

    if (contact) {
      openAppView('contacts', 'detail', { id: contact.id });
    } else {
      openAppView('contacts', 'add', { phone: otherParticipantPhone });
    }
  };

  // Default theme values
  const colors = {
    bg: {
      primary: 'bg-gray-900'
    },
    text: {
      primary: 'text-white',
      secondary: 'text-gray-300',
      tertiary: 'text-gray-500'
    },
    border: {
      primary: 'border-gray-800'
    }
  };

  // More reliable method to scroll to bottom
  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  // Check if user is at the bottom of the conversation
  const isAtBottom = () => {
    if (!messagesContainerRef.current) return true;

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    // Allow a small buffer (30px) to consider "almost at bottom" as "at bottom"
    return scrollHeight - scrollTop - clientHeight < 30;
  };

  // Helper function to get the other participant's phone number in a direct conversation
  const getOtherParticipantPhone = (conv: ConversationType | undefined) => {
    if (!conv || conv.type !== 'direct' || !conv.members) return '';

    // For direct conversations, get the other participant's phone number
    const playerPhone = userProfile?.phoneNumber || '';
    return Object.keys(conv.members).find(phone => phone !== playerPhone) || '';
  };

  // Helper function to get the receiver for a conversation
  const getReceiver = (conv: ConversationType | undefined) => {
    if (!conv) return '';

    if (conv.type === 'group') {
      return `group_${conv.id}`;
    } else {
      return getOtherParticipantPhone(conv);
    }
  };

  // Track if we should scroll to bottom when messages change
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);

  // Load messages when conversation changes
  useEffect(() => {
    if (activeChat) {
      // Always scroll to bottom when changing conversations
      setShouldScrollToBottom(true);
      actions.loadMessages(activeChat);
    }
  }, [activeChat, actions]);

  // Check if we're at the bottom before messages update
  useEffect(() => {
    if (messagesContainerRef.current && activeChat) {
      setShouldScrollToBottom(isAtBottom());
    }
  }, [conversations, activeChat]);

  // Scroll to bottom after messages update if we were already at the bottom
  useLayoutEffect(() => {
    if (shouldScrollToBottom && activeChat) {
      scrollToBottom();
    }
  }, [shouldScrollToBottom, activeChat, conversations]);

  // Handle scroll event to load more messages
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current || isLoadingMore || !activeChat) return;

    const { scrollTop } = messagesContainerRef.current;
    const hasMore = hasMoreMessages[activeChat as number];

    // Load more when user scrolls to the top (for older messages)
    if (scrollTop < 50 && hasMore) {
      setIsLoadingMore(true);

      // Store the current scroll height to maintain position
      const scrollHeight = messagesContainerRef.current.scrollHeight;

      actions.loadMoreMessages(activeChat).finally(() => {
        // After loading more messages, adjust scroll position to maintain context
        if (messagesContainerRef.current) {
          // Calculate the new scroll position to maintain the same view
          const newScrollHeight = messagesContainerRef.current.scrollHeight;
          const scrollDifference = newScrollHeight - scrollHeight;

          // Set the scroll position to maintain the same view
          messagesContainerRef.current.scrollTop = scrollDifference;
        }

        setIsLoadingMore(false);
      });
    }
  }, [actions, activeChat, hasMoreMessages, isLoadingMore]);

  // Add scroll event listener
  useEffect(() => {
    const scrollContainer = messagesContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  if (!activeChat) return null;

  const conversation = conversations.find((c: ConversationType) => c.id === activeChat);
  // Get messages from messagesById store instead of directly from conversation
  // Convert activeChat to number to ensure consistent key type
  const numericChatId = typeof activeChat === 'string' ? parseInt(activeChat, 10) : Number(activeChat);
  const chatMessages = messagesById[numericChatId] || [];

  return (
    <div className="flex-1 flex flex-col h-full w-full overflow-hidden">
      {/* Header */}
      <div
        className={`flex items-center gap-2 px-3 py-2 ${colors.bg.primary} border-b ${colors.border.primary}`}
      >
        {/* Back button */}
        {activeChat && (
          <button
            onClick={() => openView('main', {}, { replace: true })}
            className={`${colors.text.secondary} hover:${colors.text.primary} flex-shrink-0 cursor-pointer`}
          >
            <i className="fas fa-arrow-left"></i>
          </button>
        )}

        {activeChat ? (
          <div className="flex flex-1 items-center justify-between min-w-0">
            <div className="flex items-center flex-1 min-w-0 gap-2">
              {/* Avatar - New clickable section */}
              <div
                onClick={handleAvatarClick}
                className={`w-8 h-8 rounded-full overflow-hidden flex-shrink-0 cursor-pointer ${
                  currentConversation?.type !== 'group' ? 'hover:opacity-80' : ''
                }`}
              >
                {currentConversation?.type === 'group' || currentConversation?.avatar === 'group_icon' ? (
                  <div className="w-full h-full bg-white/20 flex items-center justify-center">
                    <i className="fas fa-users text-white/80 text-sm" />
                  </div>
                ) : (
                  <>
                    {/* For direct conversations, find the contact that is not us */}
                    {(() => {
                      // Get the other participant's phone number
                      const otherParticipantPhone = getOtherParticipantPhone(currentConversation);

                      // Find the contact in contacts store
                      const contact = contacts?.find(c => c.number === otherParticipantPhone);

                      // If contact has avatar, use it
                      if (contact?.avatar) {
                        return (
                          <img
                            src={contact.avatar}
                            alt={contact.name}
                            className="w-full h-full object-cover"
                          />
                        );
                      }

                      // If conversation has avatar, use it
                      if (currentConversation?.avatar) {
                        return (
                          <img
                            src={currentConversation.avatar}
                            alt={currentConversation.name || 'Contact'}
                            className="w-full h-full object-cover"
                          />
                        );
                      }

                      // Otherwise, show initials
                      const displayName = contact?.name || currentConversation?.name || otherParticipantPhone || '?';

                      return (
                        <div className="w-full h-full bg-gradient-to-br from-blue-500/30 to-blue-600/30 flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {displayName[0]?.toUpperCase() || '?'}
                          </span>
                        </div>
                      );
                    })()}
                  </>
                )}
              </div>
              {/* Name and member count for groups */}
              <div className="flex-1 min-w-0">
                <div className={`${colors.text.primary} font-medium truncate flex items-center`}>
                  <span className="truncate">
                    {(() => {
                      // For group conversations, use the conversation name
                      if (currentConversation?.type === 'group') {
                        return currentConversation.name;
                      }

                      // For direct conversations, find the contact that is not us
                      const otherParticipantPhone = getOtherParticipantPhone(currentConversation);

                      // Find the contact in contacts store
                      const contact = contacts?.find(c => c.number === otherParticipantPhone);

                      // Use contact name if available, otherwise use conversation name or phone number
                      return contact?.name || currentConversation?.name || otherParticipantPhone || '';
                    })()}
                  </span>
                  {currentConversation?.type === 'group' && currentConversation?.members && (
                    <span className="inline-flex items-center ml-2 flex-shrink-0">
                      <span className="text-xs text-white/60 ml-1">
                        · {Object.keys(currentConversation.members).length} members
                      </span>
                    </span>
                  )}
                </div>
              </div>
            </div>
            {currentConversation?.type === 'group' ? (
              <button
                onClick={() => openView('info')}
                className={`p-2 ${colors.text.secondary} hover:${colors.text.primary} cursor-pointer flex-shrink-0`}
              >
                <i className="fas fa-info-circle"></i>
              </button>
            ) : (
              <div className="flex gap-1">
                <button
                  onClick={() => {
                    // Get the other participant's phone number
                    const otherParticipantPhone = getOtherParticipantPhone(currentConversation);
                    handleStartCall(otherParticipantPhone);
                  }}
                  className={`p-2 ${colors.text.secondary} hover:${colors.text.primary} cursor-pointer flex-shrink-0`}
                >
                  <i className="fas fa-phone"></i>
                </button>
                <button
                  onClick={() => {
                    // Get the other participant's phone number
                    const otherParticipantPhone = getOtherParticipantPhone(currentConversation);
                    handleStartCall(otherParticipantPhone);
                  }}
                  className={`p-2 ${colors.text.secondary} hover:${colors.text.primary} cursor-pointer flex-shrink-0`}
                >
                  <i className="fas fa-video"></i>
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={e => setSearchTerm && setSearchTerm(e.target.value)}
              className={`w-full bg-white/10 focus:ring-white/20 ${colors.text.primary} rounded-full px-4 py-1.5 focus:outline-none focus:ring-1`}
            />
          </div>
        )}
      </div>
      {/* Message List */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto overflow-x-hidden w-full">
        {/* Loading indicator for older messages */}
        {isLoadingMore && activeChat && hasMoreMessages[activeChat as number] && (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-white/20"></div>
            <span className="ml-2 text-white/60 text-sm">Loading older messages...</span>
          </div>
        )}

        {loading && !isLoadingMore && chatMessages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-white/60 text-center p-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white/20 mx-auto mb-4"></div>
              <p>Loading messages...</p>
            </div>
          </div>
        ) : chatMessages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-white/60 text-center p-4">
              <p>No messages yet. Start a conversation!</p>
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-2">
            {chatMessages.map((message) => {
              // Determine if this is a group conversation
              const isGroup = conversation?.type === 'group';

              // Find contact info for the sender
              const contacts = useContactsStore.getState().contacts;
              const senderContact = contacts.find(c => c.number === message.sender);
              const isContact = !!senderContact;

              // Add UI-specific properties at the component level
              const messageWithAvatar = {
                ...message,
                showAvatar: true,
                avatar: senderContact?.avatar || null, // Use null instead of placeholder to show initials
                isContact, // Pass whether this is a contact
                // Convert is_deleted to boolean if it's a number
                is_deleted: typeof message.is_deleted === 'number' ? Boolean(message.is_deleted) : message.is_deleted,
                displayName: isGroup
                  ? (() => {
                      // For group conversations, always try to find the sender's name from contacts
                      if (senderContact) {
                        return senderContact.name;
                      }
                      // If no contact found, use the sender's phone number
                      return message.sender || '';
                    })()
                  : (() => {
                      // For direct conversations, always use contact data if available
                      if (senderContact) {
                        return senderContact.name;
                      }

                      // If this is the player's message, use "You"
                      if (message.sender === playerPhone) {
                        return "You";
                      }

                      // Fallback to conversation name or sender phone number
                      return conversation?.name || message.sender || '';
                    })()
              };

              // Use type assertion to handle the mixed types with the new Message & UIProperties type
              // First cast to unknown to avoid type compatibility issues
              return (
                <MessageBubble
                  key={message.id}
                  message={(messageWithAvatar as unknown) as Message & {
                    showAvatar?: boolean;
                    avatar?: string | null;
                    displayName?: string;
                    isEdited?: boolean;
                    isDeleted?: boolean;
                    status?: string;
                    isContact?: boolean;
                  }}
                  onDelete={() => {
                    /* Implement delete functionality */
                  }}
                  onEdit={() => {
                    /* Implement edit functionality */
                  }}
                  isMine={message.sender === playerPhone}
                  onReply={() => {}}
                />
              );
            })}
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="p-4 flex flex-col gap-2">
        {/* Selected Photos Preview */}
        {(() => {
          // Safely check if we have photos to display
          const hasActiveChat = activeChat !== null;
          const hasSelectedPhotos = selectedPhotos && typeof selectedPhotos === 'object';
          const chatId = activeChat ? activeChat.toString() : null;
          const chatPhotos =
            hasActiveChat && hasSelectedPhotos && chatId ? selectedPhotos[chatId] : null;
          const hasPhotos = Array.isArray(chatPhotos) && chatPhotos.length > 0;

          if (hasActiveChat && hasPhotos && chatId) {
            return (
              <div className="flex gap-1.5 overflow-x-auto pb-2">
                {chatPhotos.map((photo, index) => {
                  // Skip invalid photos
                  if (typeof photo !== 'string') return null;

                  return (
                    <div key={index} className="relative w-12 h-12 flex-shrink-0">
                      <img
                        src={photo}
                        alt="Selected"
                        className="w-full h-full object-cover rounded-md"
                        onError={e => {
                          console.error('[Conversation] Failed to load photo:', photo);
                          e.currentTarget.src = 'https://via.placeholder.com/150?text=Error';
                        }}
                      />
                      <button
                        onClick={() => removePhotoFromConversation(chatId, index)}
                        className="absolute -top-1 -right-1 w-4 h-4 bg-black/50 rounded-full flex items-center justify-center"
                      >
                        <i className="fas fa-times text-[8px] text-white"></i>
                      </button>
                    </div>
                  );
                })}
              </div>
            );
          }

          return null;
        })()}

        {/* Input and Buttons */}
        <div className="flex items-center gap-2">
          <div className="relative attachment-menu-container">
            <button
              onClick={() => setShowAttachMenu(!showAttachMenu)}
              className={`${colors.text.tertiary} hover:${colors.text.primary} p-2 flex-shrink-0 cursor-pointer`}
            >
              <i className="fas fa-paperclip"></i>
            </button>

            {showAttachMenu && (
              <>
                <div className="fixed inset-0 z-40" onClick={() => setShowAttachMenu(false)} />
                <div className="absolute bottom-[48px] left-0 w-full z-50">
                  <div
                    className={`mx-auto bg-[#1a1f2d] rounded-full shadow-lg py-2 px-4 flex justify-center space-x-6 w-fit`}
                    style={{
                      animation: 'fadeInUp 0.2s ease-out forwards'
                    }}
                  >
                    <div
                      className="group cursor-pointer relative"
                      onClick={() => handleAttachmentClick('contact')}
                    >
                      <i className="fas fa-user text-blue-400"></i>
                      <span
                        className={`absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-white
                                     opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap
                                     bg-[#1a1f2d] px-2 py-1 rounded-md`}
                      >
                        Contact
                      </span>
                    </div>
                    <div
                      className="group cursor-pointer relative"
                      onClick={() => handleAttachmentClick('location')}
                    >
                      <i className="fas fa-map-marker-alt text-red-400"></i>
                      <span
                        className={`absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-white
                                     opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap
                                     bg-[#1a1f2d] px-2 py-1 rounded-md`}
                      >
                        Location
                      </span>
                    </div>
                    <div
                      className="group cursor-pointer relative"
                      onClick={() => handleAttachmentClick('camera')}
                    >
                      <i className="fas fa-camera text-green-400"></i>
                      <span
                        className={`absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-white
                                     opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap
                                     bg-[#1a1f2d] px-2 py-1 rounded-md`}
                      >
                        Camera
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>

          <div className="flex-1 min-w-0 relative">
            <input
              type="text"
              value={newMessage}
              onChange={e => setNewMessage(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter' && newMessage.trim()) {
                  // Trigger send message on Enter key
                  if (!activeChat) return;

                  const conversation = conversations.find(c => c.id === activeChat);

                  // Get the receiver for this conversation
                  const receiver = getReceiver(conversation);

                  if (!receiver) return;

                  handleSendAttachment('text', newMessage.trim());

                  // Always scroll to bottom after sending a message
                  setShouldScrollToBottom(true);
                }
              }}
              placeholder={
                activeChat && selectedPhotos && selectedPhotos[activeChat?.toString()]?.length > 0
                  ? 'Add a message...'
                  : 'Type a message...'
              }
              className={`w-full bg-white/10 focus:ring-white/20 ${colors.text.primary} rounded-full px-4 py-2 focus:outline-none focus:ring-1 pr-10`}
            />
            <button
              onClick={() => {
                if (activeChat) {

                  // Get existing photos for this chat
                  const chatId = activeChat.toString();
                  const existingPhotos = Array.isArray(selectedPhotos[chatId])
                    ? [...selectedPhotos[chatId]]
                    : [];

                  // Use the selection store to start selection mode
                  // This is the only place we set up the selection configuration
                  startSelection({
                    mode: 'multiple',
                    maxSelection: 6,
                    initialSelected: existingPhotos.map(photo => ({ id: photo, type: 'photo' })),
                    purpose: 'share',
                    targetApp: 'photos',
                    returnPath: 'messages',
                    callback: selected =>
                      handlePhotoSelection(selected.map(item => item.id as string))
                  });

                  // Navigate to the photos app
                  // We don't need to pass the callback here as it's already set in startSelection
                  openAppAndReturn('messages', 'photos', {});
                }
              }}
              className={`absolute right-2 top-1/2 -translate-y-1/2 ${colors.text.tertiary} hover:${colors.text.primary} p-2 flex-shrink-0 cursor-pointer`}
            >
              <i className="fas fa-image"></i>
            </button>
          </div>

          <button
            onClick={() => {
              if (!activeChat) {
                console.log('[Conversation] No active chat, cannot send message');
                return;
              }

              const conversation = conversations.find(c => c.id === activeChat);

              // Get the receiver for this conversation
              const receiver = getReceiver(conversation);

              if (!receiver) {
                console.log('[Conversation] No receiver found for conversation', activeChat);
                return;
              }

              // Check if we have text, photos, or both to send
              const hasText = newMessage.trim().length > 0;
              const chatId = activeChat.toString();
              const hasPhotos =
                Array.isArray(selectedPhotos[chatId]) && selectedPhotos[chatId].length > 0;

              if (!hasText && !hasPhotos) {
                return;
              }

              // If we have photos, prepare them
              let validPhotos: string[] = [];
              if (hasPhotos) {
                // Filter out any invalid photos
                validPhotos = selectedPhotos[chatId].filter(photo => typeof photo === 'string');

                if (validPhotos.length === 0) {
                  // If we have text but no valid photos, just send the text
                  if (hasText) {
                    handleSendAttachment('text', newMessage.trim());
                    return;
                  }
                  return;
                }
              }

              // Now handle the different cases
              if (hasText && hasPhotos) {
                // Case 1: Both text and photos
                // Send a message with both text and photos
                actions.sendMessage(activeChat, {
                  message: newMessage.trim(), // Use the text as message
                  type: 'image', // Still use image as the message type
                  metadata: {
                    mediaUrls: validPhotos,
                    hasText: true // Add a flag to indicate this has text
                  }
                });

                // Clear the input field
                setNewMessage('');
              } else if (hasText) {
                // Case 2: Text only
                handleSendAttachment('text', newMessage.trim());
              } else if (hasPhotos) {
                // Case 3: Photos only
                actions.sendMessage(activeChat, {
                  message: 'Photo', // Default message for image-only messages
                  type: 'image',
                  metadata: {
                    mediaUrls: validPhotos
                  }
                });
              }

              // Clear selected photos after sending
              setSelectedPhotos(prev => {
                // Validate previous state
                if (!prev || typeof prev !== 'object') {
                  return {};
                }

                // Check if there are photos to clear
                if (!Array.isArray(prev[chatId]) || prev[chatId].length === 0) {
                  return prev;
                }

                // Create new state with empty photos array for this chat
                const newState = {
                  ...prev,
                  [chatId]: []
                };

                // Update localStorage
                try {
                  localStorage.setItem('messages_selectedPhotos', JSON.stringify(newState));
                } catch (error) {
                  console.error('[Conversation] Error saving to localStorage:', error);
                }

                return newState;
              });
            }}
            disabled={
              !newMessage.trim() &&
              (!activeChat || !selectedPhotos || !selectedPhotos[activeChat?.toString()]?.length)
            }
            className={`p-2 rounded-full flex-shrink-0 cursor-pointer ${
              newMessage.trim() ||
              (activeChat && selectedPhotos && selectedPhotos[activeChat?.toString()]?.length > 0)
                ? 'text-green-500 hover:text-green-400'
                : 'text-white/30 cursor-not-allowed'
            }`}
          >
            <i className="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Conversation;
