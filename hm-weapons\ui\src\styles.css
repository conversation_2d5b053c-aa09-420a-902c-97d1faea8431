/* Banking UI Styles - FiveM NUI Optimized */

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: transparent !important; /* Transparent for game overlay */
  color: #1f2937; /* Default dark grey text */
  margin: 0;
  padding: 0;
  overflow: hidden; /* Prevent scrollbars on the body itself */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure proper viewport sizing */
#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: transparent !important;
}

/* Custom scrollbar for webkit browsers (Chrome, Safari, new Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(64, 64, 64, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.4);
  border-radius: 10px;
  border: 2px solid rgba(64, 64, 64, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.6);
}

/* Basic styling for Firefox scrollbar (less customizable) */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(34, 197, 94, 0.4) rgba(64, 64, 64, 0.3);
}

/* Smooth transitions for container size changes */
.banking-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Gradient radial utility for background effects */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Enhanced account selection animations */
.account-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.account-card:hover {
  transform: translateY(-2px);
}

.account-card:active {
  transform: translateY(0px) scale(0.98);
}

/* Modern fade-in animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Fade in animation for content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Scale in animation for account selection */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Slide in animation for dashboard */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transaction card hover effect */
.transaction-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form input focus styles */
input:focus {
  outline: none;
  border-color: #065f46 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

button:active {
  transform: translateY(0);
}

/* Sidebar menu item hover */
.sidebar-menu-item:hover {
  background-color: rgba(6, 95, 70, 0.05);
}

/* Success, error, warning colors for consistency */
.text-success {
  color: #059669;
}

.text-error {
  color: #dc2626;
}

.text-warning {
  color: #d97706;
}

.bg-success {
  background-color: #059669;
}

.bg-error {
  background-color: #dc2626;
}

.bg-warning {
  background-color: #d97706;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }
  
  .main-content {
    padding: 16px;
  }
}


