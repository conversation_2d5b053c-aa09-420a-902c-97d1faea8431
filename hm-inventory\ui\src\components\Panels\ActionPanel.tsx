import React from 'react';
import { useInventoryStore, InventoryState } from '../../stores/inventoryStore';
import { SlotType } from '@shared/inventory.types';
import Slot from '../InventoryGrid/Slot';

/**
 * ActionPanel component displays a vertical panel of action slots
 * that have the same size and padding as the main inventory slots.
 */
const ActionPanel: React.FC = () => {
  // Get action slots from inventory store
  const actionSlots = useInventoryStore((state: InventoryState) => state.actionSlots);

  // Grab each action slot by fixed index
  const phoneSlot = actionSlots.find(s => s.index === 0);
  const armorSlot = actionSlots.find(s => s.index === 1);
  const weaponSlot = actionSlots.find(s => s.index === 2);
  const tabletSlot = actionSlots.find(s => s.index === 3);
  const backpackSlot = actionSlots.find(s => s.index === 4);

  // Define the same constants as in InventoryGrid
  const SLOT_HEIGHT_REM = 5.5;
  const GAP_REM = 0.5;

  // Create a grid style that matches InventoryGrid
  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateRows: `repeat(4, ${SLOT_HEIGHT_REM}rem)`,
    gridAutoColumns: `${SLOT_HEIGHT_REM}rem`, // Match the slot width
    gap: `${GAP_REM}rem`,
    background: 'transparent',
    padding: 0, // No padding to avoid border conflicts
    border: 'none', // No border to avoid conflicts with slot borders
    borderRadius: 0, // No border radius to avoid conflicts
    width: 'fit-content', // Let it fit its content
  };

  return (
    <div className="bg-neutral-800 rounded-2xl shadow-xl p-4 border-l-4 border-green-400/10 flex flex-col items-center relative">
      <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-green-400/10 via-green-500/10 to-green-600/10 opacity-30 z-10 rounded-t-2xl" />
      <h2 className="text-lg font-bold text-neutral-100 tracking-wide flex items-center gap-2">
        <i className="fas fa-bolt text-green-400/80" /> Actions
      </h2>
      {/* Subtle divider under title */}
      <div className="h-px bg-gradient-to-r from-transparent via-green-500/10 to-transparent w-full mb-2"></div>

      <div
        style={gridStyle}
        className="inventory-grid-container p-0 bg-black bg-opacity-20 rounded-md">
        {/* Phone slot */}
        <Slot
          slotIndex={phoneSlot?.index ?? 0}
          inventoryItem={phoneSlot?.item}
          quantity={phoneSlot?.quantity}
          slotType={SlotType.PHONE}
          panelType="action"
          panelId="action_phone"
        />

        {/* Tablet slot */}
        <Slot
          slotIndex={tabletSlot?.index ?? 3}
          inventoryItem={tabletSlot?.item}
          quantity={tabletSlot?.quantity}
          slotType={SlotType.TABLET}
          panelType="action"
          panelId="action_tablet"
        />

        {/* Armor slot */}
        <Slot
          slotIndex={armorSlot?.index ?? 1}
          inventoryItem={armorSlot?.item}
          quantity={armorSlot?.quantity}
          slotType={SlotType.ARMOR}
          panelType="action"
          panelId="action_armor"
        />

        {/* Primary weapon slot */}
        <Slot
          slotIndex={weaponSlot?.index ?? 2}
          inventoryItem={weaponSlot?.item}
          quantity={weaponSlot?.quantity}
          slotType={SlotType.PRIMARY_WEAPON}
          panelType="action"
          panelId="action_primary_weapon"
        />

        {/* Backpack slot */}
        <Slot
          slotIndex={backpackSlot?.index ?? 4}
          inventoryItem={backpackSlot?.item}
          quantity={backpackSlot?.quantity}
          slotType={SlotType.BACKPACK}
          panelType="action"
          panelId="action_backpack"
        />
      </div>
    </div>
  );
};

export default ActionPanel;
