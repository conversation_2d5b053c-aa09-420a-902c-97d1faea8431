@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color: rgba(255, 255, 255, 0.87);
  background-color: transparent;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Admin UI specific styles */
.admin-container {
  @apply bg-admin-dark bg-opacity-95 text-white;
  @apply border border-admin-secondary rounded-lg shadow-lg;
  @apply overflow-hidden;
}

.admin-header {
  @apply bg-admin-primary p-4 border-b border-admin-secondary;
  @apply flex items-center justify-between;
}

.admin-content {
  @apply p-4 overflow-auto;
}

.admin-footer {
  @apply bg-admin-primary p-3 border-t border-admin-secondary;
  @apply flex items-center justify-between text-sm;
}

.admin-sidebar {
  @apply bg-admin-primary bg-opacity-50;
}

.admin-button {
  @apply bg-admin-secondary hover:bg-opacity-80 text-white;
  @apply px-4 py-2 rounded-md transition-colors;
  @apply flex items-center gap-2;
}

.admin-button-accent {
  @apply bg-admin-accent hover:bg-opacity-80 text-white;
  @apply px-4 py-2 rounded-md transition-colors;
  @apply flex items-center gap-2;
}

/* Form controls */
.admin-input {
  @apply bg-admin-dark border border-admin-secondary rounded-md px-3 py-2;
  @apply focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-transparent;
  @apply w-full transition-all;
}

.admin-select {
  @apply bg-admin-dark border border-admin-secondary rounded-md px-3 py-2;
  @apply focus:outline-none focus:ring-2 focus:ring-admin-accent focus:border-transparent;
  @apply w-full transition-all appearance-none;
  @apply bg-no-repeat bg-right;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-size: 1.25rem;
  padding-right: 2.5rem;
}

.admin-checkbox {
  @apply rounded border-admin-secondary text-admin-accent;
  @apply focus:ring-admin-accent focus:ring-offset-0 focus:ring-opacity-25;
  @apply transition-all;
}

.admin-radio {
  @apply border-admin-secondary text-admin-accent;
  @apply focus:ring-admin-accent focus:ring-offset-0 focus:ring-opacity-25;
  @apply transition-all;
}

/* Card styles */
.admin-card {
  @apply bg-admin-primary bg-opacity-50 border border-admin-secondary rounded-md;
  @apply p-4 mb-4;
}

/* Table styles */
.admin-table {
  @apply w-full border-collapse;
}

.admin-table th {
  @apply bg-admin-secondary bg-opacity-50 text-left p-2 font-semibold;
}

.admin-table td {
  @apply border-t border-admin-secondary p-2;
}

.admin-table tr:hover {
  @apply bg-admin-secondary bg-opacity-20;
}

/* Badge styles */
.admin-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.admin-badge-blue {
  @apply bg-blue-100 text-blue-800;
}

.admin-badge-green {
  @apply bg-green-100 text-green-800;
}

.admin-badge-red {
  @apply bg-red-100 text-red-800;
}

.admin-badge-yellow {
  @apply bg-yellow-100 text-yellow-800;
}

.admin-badge-purple {
  @apply bg-purple-100 text-purple-800;
}

/* Tabs */
.admin-tabs {
  @apply flex border-b border-admin-secondary;
}

.admin-tab {
  @apply px-4 py-2 border-b-2 font-medium;
  @apply transition-colors;
}

.admin-tab-active {
  @apply border-admin-accent text-admin-accent;
}

.admin-tab-inactive {
  @apply border-transparent hover:border-admin-secondary;
}