import { useEffect } from 'react';
import { useWeaponModdingStore, useWeaponModdingHandlers, initializeWeaponModdingStore } from './stores/weaponModdingStore';
import WeaponModdingScreen from './components/WeaponModdingScreen';

function App() {
  const { isVisible } = useWeaponModdingStore();
  const handlers = useWeaponModdingHandlers();

  // Initialize store and handle NUI messages
  useEffect(() => {
    const cleanup = initializeWeaponModdingStore();

    const handleMessage = (event: MessageEvent) => {
      const { type, data } = event.data;
      
      console.log(`[WeaponModding UI] Received message:`, { type, data });
      
      switch (type) {
        case 'OPEN_WEAPON_MODDING':
          handlers.onOpenWeaponModding(data);
          break;
        case 'CLOSE_WEAPON_MODDING':
          handlers.onCloseWeaponModding();
          break;
        case 'UPDATE_ATTACHMENT_POINTS':
          handlers.onUpdateAttachmentPoints(data);
          break;
        case 'UPDATE_AVAILABLE_MODS':
          handlers.onUpdateAvailableMods(data);
          break;
        case 'MOD_APPLIED':
          handlers.onModApplied(data);
          break;        case 'MOD_REMOVED':
          handlers.onModRemoved(data);
          break;
        case 'SHOW_POSITIONING_CONTROLS':
          handlers.onShowPositioningControls(data);
          break;
        case 'UPDATE_OFFSETS':
          handlers.onUpdateOffsets(data);
          break;
        default:
          console.log(`[WeaponModding UI] Unknown message type: ${type}`);
      }
    };
    
  window.addEventListener('message', handleMessage);
    
    return () => {
      window.removeEventListener('message', handleMessage);
      cleanup();
    };
  }, [handlers]);

  // Don't render anything if not visible - completely transparent
  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none">
      <WeaponModdingScreen />
    </div>
  );
}

export default App;
