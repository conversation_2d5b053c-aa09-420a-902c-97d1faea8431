import { useEffect } from 'react';
import { useInventoryStore } from '../stores/inventoryStore';
import { 
  sortInventoryItems, 
  autoOptimizeStacks,
  SortOptions 
} from '../utils/inventoryUtils';
import { ToastType } from '@/utils/toastUtils';

// Dynamic NUI callback function using GetParentResourceName
async function sendNuiMessage<T = unknown>(event: string, data: T): Promise<Response> {
  try {
    // Get the resource name dynamically to ensure correct URL
    const resourceName = (window as Window & { GetParentResourceName?: () => string }).GetParentResourceName?.() || 'hm-inventory';
    const url = `https://${resourceName}/${event}`;
    
    console.log(`[NUI] Sending message to: ${url}`);
    console.log(`[NUI] Event: ${event}, Data:`, data);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      console.error(`[NUI] Error: ${response.status} ${response.statusText} for event ${event}`);
    }
    
    console.log('[NUI] Response received:', {
      event,
      url,
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });
    
    return response;
  } catch (error) {
    console.error(`[NUI] Error sending message ${event} to resource:`, error);
    console.log(`[NUI] Data sent: ${JSON.stringify(data)}`);
    throw error;
  }
}

export const useKeyboardShortcuts = () => {
  const gridItems = useInventoryStore(state => state.gridItems);
  const updateGridItems = useInventoryStore(state => state.updateGridItems);
  const isMainVisible = useInventoryStore(state => state.isMainVisible);  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      console.log('[Keyboard] Key pressed:', {
        key: event.key,
        ctrlKey: event.ctrlKey,
        altKey: event.altKey,
        isMainVisible,
        target: (event.target as HTMLElement)?.tagName
      });

      // Only handle shortcuts when inventory is open
      if (!isMainVisible) {
        console.log('[Keyboard] Inventory not visible, ignoring key:', event.key);
        return;
      }

      // Prevent default behavior for our shortcuts
      const { key, ctrlKey, altKey } = event;

      // Tab key: Close inventory
      if (key === 'Tab' && !ctrlKey && !altKey) {
        console.log('[Keyboard] Tab key detected - attempting to close inventory');
        event.preventDefault();
        event.stopPropagation();
        
        // Send close request to client
        console.log('[Keyboard] Sending closeInventory NUI message');
        sendNuiMessage('closeInventory', {})
          .then(() => {
            console.log('[Keyboard] closeInventory NUI message sent successfully');
          })
          .catch((error) => {
            console.error('[Keyboard] Error sending closeInventory NUI message:', error);
          });
        return;
      }

      // Ctrl + S: Sort by type
      if (ctrlKey && key.toLowerCase() === 's') {
        event.preventDefault();
        const sortOptions: SortOptions = {
          method: 'type',
          direction: 'asc',
          groupStackable: true,
        };
        const sortedItems = sortInventoryItems(gridItems, sortOptions);
        updateGridItems(sortedItems);
        
        useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
          itemName: 'Sorted by type (Ctrl+S)',
        });
      }

      // Ctrl + O: Optimize stacks
      if (ctrlKey && key.toLowerCase() === 'o') {
        event.preventDefault();
        try {
          const optimizedItems = autoOptimizeStacks(gridItems);
          updateGridItems(optimizedItems);
          
          useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
            itemName: 'Inventory optimized (Ctrl+O)',
          });
        } catch (error) {
          console.error('Failed to optimize stacks:', error);
        }
      }

      // Alt + S: Sort by name
      if (altKey && key.toLowerCase() === 's') {
        event.preventDefault();
        const sortOptions: SortOptions = {
          method: 'name',
          direction: 'asc',
          groupStackable: true,
        };
        const sortedItems = sortInventoryItems(gridItems, sortOptions);
        updateGridItems(sortedItems);
        
        useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
          itemName: 'Sorted alphabetically (Alt+S)',
        });
      }

      // Alt + Q: Sort by quantity
      if (altKey && key.toLowerCase() === 'q') {
        event.preventDefault();
        const sortOptions: SortOptions = {
          method: 'quantity',
          direction: 'desc',
          groupStackable: true,
        };
        const sortedItems = sortInventoryItems(gridItems, sortOptions);
        updateGridItems(sortedItems);
        
        useInventoryStore.getState().showToast(ToastType.ITEM_MOVED, {
          itemName: 'Sorted by quantity (Alt+Q)',
        });
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [gridItems, updateGridItems, isMainVisible]);
};
