export const enum PedConfigFlags {
    AllowMedicsToAttend = 20,					// If this ped dies medics will be dispatched, false by default for mission peds, the ped wont be revived
    DontAllowToBeDraggedOutOfVehicle = 26,		// Prevents a ped from being able to be dragged out of a car	
    GetOutUndriveableVehicle = 29,				// <PERSON><PERSON><PERSON> can stop peds automatically getting out of car when it's upside down or undrivable, defaults to true
    WillFlyThroughWindscreen = 32,				// Ped will fly through the vehicle windscreen upon a forward impact at high velocity
    HasHelmet = 34,								// Ped has a helmet (/ The PedHelmetComponent has put the helmet on the ped via "put on" animations)
    DontTakeOffHelmet = 36,						// Disable the ped taking off his helmet automatically
	DontInfluenceWantedLevel	= 42,			// Can do any crime against this character and the cops turn a blind eye (no crime reported)
    DisableLockonToRandomPeds	= 44,			// Disable lockon to random ambient peds
    AllowLockonToFriendlyPlayers = 45,
	DisableHornAudioWhenDead = 46,				// Disable horn audio when the ped dies and rests his head on top of the steering wheel
    IsAimingGun					= 78,			// Is set when a ped is performing an aim task
    ForcedAim								= 101,  // If set, we will always behave like we have the aim trigger pressed, also works for AI in cars
    OpenDoorArmIK							= 104,  // Set if the ped should enable open door arm IK
    DontActivateRagdollFromVehicleImpact	= 106,  // Blocks ragdoll activation when hit by a vehicle
    DontActivateRagdollFromBulletImpact		= 107,  // Blocks ragdoll activation when hit by a bullet
    DontActivateRagdollFromExplosions		= 108,  // Blocks ragdoll activation when hit by an explosive
    DontActivateRagdollFromFire				= 109,  // Blocks ragdoll activation when set on fire
    DontActivateRagdollFromElectrocution	= 110,  // Blocks ragdoll activation when electrocuted
    KeepWeaponHolsteredUnlessFired = 113,		// Will keep the peds weapon holstered until they shoot or change weapons
    ForceControlledKnockout		= 114,			// Forces a melee knockout state for the victim ped
    FallsOutOfVehicleWhenKilled = 115,			// Forces a ped in a vehicle to collapse out onto the floor (TaskDyingDead launches TaskExitVehicle)
    GetOutBurningVehicle = 116,					// If set, a ped will escape a burning vehicle they are inside, defaults to true
    RunFromFiresAndExplosions = 118,			// If set, a ped will escape a burning vehicle they are inside, defaults to true
    TreatAsPlayerDuringTargeting = 119,			// If set, the ped will be given the same boost a player gets in the targeting scoring system
    DisableMelee = 122,							// Disable melee for a ped (only supported for player right now)
    DisableUnarmedDrivebys = 123,				// Disable unarmed driveby taunts for a ped
    JustGetsPulledOutWhenElectrocuted = 124,		// MP only - if this ped is tased or rubber bulleted in a vehicle and a ped jacks them, the jacker will only pull the ped out
    WillNotHotwireLawEnforcementVehicle = 126,	// If set, the ped won't hotwire a lawenforcement vehicle
    WillCommandeerRatherThanJack = 127,			// If set, the ped will play commandeering anims rather than jacking if available
    CanBeAgitated = 128,						// If set, the ped can be agitated
    ForcePedToFaceLeftInCover  = 129,			// If set ped will turn to face left in cover
    ForcePedToFaceRightInCover = 130,			// If set ped will turn to face right in cover
    BlockPedFromTurningInCover  = 131,			// If set ped will not turn in cover, unless one of the force flags is set
    KeepRelationshipGroupAfterCleanUp = 132,	// Ped keeps their relationship group when the mission is cleaned up or they are marked as no longer needed
    ForcePedToBeDragged = 133,					// Ped will loop the try locked door anim when they get to the door in order for them to automatically be dragged along
    PreventPedFromReactingToBeingJacked = 134,	// Ped doesn't react when being jacked
    RemoveDeadExtraFarAway = 137,				// We must be further away before ped polulation remove this ped when it is dead
    ArrestResult = 139,							// If set, the ped arrest task completed successfully
    CanAttackFriendly = 140,					// True allows this ped to attack peds they are friendly with
    WillJackAnyPlayer = 141,					// MP only, if set this ped will be allowed to jack any player peds, regardless of relationship
    WillJackWantedPlayersRatherThanStealCar = 144, //MP only, True if this player will jack hated players rather than try to steal a car (cops arresting crims)
    DisableLadderClimbing = 146,				// If this flag is set on a ped it will not scan for or climb ladders
    CowerInsteadOfFlee = 150,					// If set the ped should cower instead of fleeing
    CanActivateRagdollWhenVehicleUpsideDown = 151, //If set the ped will be allowed to ragdoll when the vehicle they are in gets turned upside down if the seat supports it
    AlwaysRespondToCriesForHelp = 152,			// If set the ped will respond to cries for help even if not friends with the injured ped.
    DisableBloodPoolCreation = 153,				// If set the ped will not create a blood pool when dead
    ShouldFixIfNoCollision = 154,				// If set, the ped will be fixed if there is no collision around.
    CanPerformArrest = 155,						// If set, the ped can perform arrests on peds that can be arrested
    CanPerformUncuff = 156,						// If set, the ped can uncuff peds that are handcuffed
    CanBeArrested = 157,						// If set, the ped can be arrested
    PlayerPreferFrontSeatMP = 159,				// When true, Prefer the front seat when getting in a car with buddies.
    DontEnterVehiclesInPlayersGroup = 167,		// When true, will follow the player around if in their group but wont enter vehicles.
    CannotBeTargeted = 169,						// DEPRECATED - Use the reset flag instead
	PreventAllMeleeTaunts = 169,				// Disable all melee taunts for this particular ped
    ForceDirectEntry = 170,						// Will force this ped to use the direct entry point for any vehicle they try to enter, or warp in
    AlwaysSeeApproachingVehicles = 171,			// This ped will always see approaching vehicles (even from behind).
    CanDiveAwayFromApproachingVehicles = 172,	// This ped can dive away from approaching vehicles.
    AllowPlayerToInterruptVehicleEntryExit = 173, // Will allow player to interrupt a peds scripted entry/exit task as if they had triggered it themselves
    OnlyAttackLawIfPlayerIsWanted = 174,		// This ped will only attack cops if the player is wanted
    PedsJackingMeDontGetIn = 177,				// If set the ped will not get in as part of the jack
    PedIgnoresAnimInterruptEvents = 179,		// AI peds only, will not early out of anims, default behaviour is to exit as early as possible
    IsInCustody = 180,							// Any targeting LoS checks will fail if any materials with 'see through' materials found.
    ForceStandardBumpReactionThresholds = 181,	// Setting this on an armed or buddy ped will make him more likely to perform an nm reaction when bumped by a player, friendly vehicle or ragdolling ped.
    LawWillOnlyAttackIfPlayerIsWanted = 182,	// If set on a ped, law peds will only attack if the local player is wanted
    PreventAutoShuffleToDriversSeat = 184,		// Prevents passenger from auto shuffling over to drivers seat if it becomes free
    UseKinematicModeWhenStationary = 185,		// When enabled, the ped will continually set the kinematic mode reset flag when stationary.
    DisableHurt = 188,							// Set to disable the combat hurt mode
    PlayerIsWeird = 189,						// Should this player ped periodically generate shocking events for being weird						
    DoNothingWhenOnFootByDefault = 193,			// Do nothing when on foot, by default
    DontReactivateRagdollOnPedCollisionWhenDead = 198, // Peds with this flag set won't be allowed to reactivate their ragdoll when hit by another ragdoll.
    DontActivateRagdollOnVehicleCollisionWhenDead = 199, // Peds with this flag set won't be allowed to reactivate their ragdoll when hit by a vehicle.
    HasBeenInArmedCombat = 200,					// True if we've ever been in non-melee combat
    Avoidance_Ignore_All = 202,					// True if we never steer around peds
    Avoidance_Ignored_by_All = 203,				// True if peds never steer around us
    Avoidance_Ignore_Group1 = 204,				// True if we steer around peds that are members of group 1
    Avoidance_Member_of_Group1 = 205,			// True if we are members of avoidance group 1
    ForcedToUseSpecificGroupSeatIndex = 206,	// Ped is forced to use specific seat index set by SET_PED_GROUP_MEMBER_PASSENGER_INDEX
    DisableExplosionReactions = 208,			// If set, ped will ignore explosion events
    WaitingForPlayerControlInterrupt = 210,		// Set when player switches to an ai ped and keeps the scripted task of the ai ped, if unset we won't check for interrupts or time out
    ForcedToStayInCover = 211,					// If set, ped will stay in cover (won't come out to fire or move out during combat)
    GeneratesSoundEvents = 212,					// Does this ped generate sound events?
	ListensToSoundEvents = 213,					// Does this ped have the ability to respond to sound events?
	AllowToBeTargetedInAVehicle = 214,			// Ped can be targeting inside a vehicle  
	WaitForDirectEntryPointToBeFreeWhenExiting = 215,	// When exiting a vehicle, the ped will wait for the direct entry point to be clear before exiting
	OnlyRequireOnePressToExitVehicle = 216,		// Player doesn't need to hold exit button to exit vehicles
	ForceExitToSkyDive = 217,					// Force the skydive exit if we're exiting the vehicle
	DisableExitToSkyDive = 221,					// Disable the skydive exit if we're exiting the vehicle
	DisablePedAvoidance = 226,					// This ped will not avoid other peds whilst navigating
	ForceRagdollUponDeath = 227,				// When the ped dies, it will ragdoll instead of potentially choosing an animated death
	DisablePanicInVehicle = 229,				// Disable panic in vehicle
	AllowedToDetachTrailer = 230,				// Allow this ped to detach trailers from vehicles
	ForceSkinCharacterCloth = 240,				// ForceSkin character cloth on creation when flag is set
	LeaveEngineOnWhenExitingVehicles = 241,		// Player will leave the engine on when exiting a vehicle normally
	PhoneDisableTextingAnimations = 242,		// tells taskmobile phone to not texting animations.  Currently don't play these in MP
	PhoneDisableTalkingAnimations = 243,		// tells taskmobile phone to not talking animations.  Currently don't play these in MP
	PhoneDisableCameraAnimations = 244,			// tells taskmobile phone to not camera animations.  Currently don't play these in SP
	DisableBlindFiringInShotReactions = 245,	// Stops the ped from accidentally firing his weapon when shot.
	AllowNearbyCoverUsage = 246,				// This makes it so that OTHER peds are allowed to take cover at points that would otherwise be rejected due to proximity
	CanAttackNonWantedPlayerAsLaw = 249,		// If this ped is a law enforcement ped then we will NOT quit combat due to a target player no longer having a wanted level
	WillTakeDamageWhenVehicleCrashes = 250,		// If set, this ped will take damage if the car they are in crashes
	AICanDrivePlayerAsRearPassenger = 251,		// If this ai ped is driving the vehicle, if the player taps to enter, they will enter as a rear passenger, if they hold, they'll jack the driver
	PlayerCanJackFriendlyPlayers = 252,			// If a friendly player is driving the vehicle, if the player taps to enter, they will enter as a passenger, if they hold, they'll jack the driver
	AIDriverAllowFriendlyPassengerSeatEntry = 255,	// If this ai ped is driving the vehicle, allow players to get in passenger seats
	AllowMissionPedToUseInjuredMovement = 259,	// Set the target ped to be allowed to use Injured movement clips
	PreventUsingLowerPrioritySeats = 261,		// Don't use certain seats (like hanging on the side of a vehicle)
	TeleportToLeaderVehicle = 268,				// If set, teleport if ped is not in the leader's vehicle before TaskEnterVehicle::m_SecondsBeforeWarpToLeader.
	Avoidance_Ignore_WeirdPedBuffer = 269,		// Don't walk extra far around weird peds like trevor
	DontBlipCop = 272,							// Don't add a blip for this cop
	KillWhenTrapped = 275,						// Kill this ped if it becomes trapped and cannot get up
	AvoidTearGas = 279,							// If set, this ped will avoid tear gas
	DisableGoToWritheWhenInjured = 281,			// If set, CPed::DAMAGED_GOTOWRITHE will no longer get set.  In particular, tazer hits wil no longer kill this ped in one hit
	OnlyUseForcedSeatWhenEnteringHeliInGroup = 282,	// If set this ped will only use their forced seat index if the vehicle they're entering is a heli as part of a group
	DisableWeirdPedEvents = 285,				// Disables weird ped events
	ShouldChargeNow = 286,						// This ped should charge if in combat right away, for use by scripts, cleared once ped charges
	DisableShockingEvents = 294,				// This ped should ignore shocking events.
	NeverReactToPedOnRoof = 296,				// If true, this ped will not react to a ped standing on the roof
	DisableShockingDrivingOnPavementEvents = 299, // If true, this ped will not react to peds driving on pavement
	ShouldThrowSmokeGrenadeNow = 300,			// This ped should throw a smoke grenade in combat right away, for use by scripts, cleared once ped throws
	ForceInitialPeekInCover = 302, // If set, ped will peek once before firing in cover. Cleared upon peeking.
	DisableJumpingFromVehiclesAfterLeader = 305,  // If true, disable followers jumping out of cars after their group leader
	ShoutToGroupOnPlayerMelee = 311,			// If set, ped will shout target position when melee attacked by a player
	IgnoredByAutoOpenDoors = 312,				// Set this for a ped to be ignored by the auto opened doors when checking to see if the door should be opened.
	ForceIgnoreMeleeActiveCombatant = 314,		// Purposely ignore the melee active combatant role and push them into a support or inactive combatant role
	CheckLoSForSoundEvents = 315,				// If set, ped will ignore sound events generated by entites it can't see.
	CanSayFollowedByPlayerAudio = 317,			// If set, ped can play FRIEND_FOLLOWED_BY_PLAYER lines.
	ActivateRagdollFromMinorPlayerContact = 318,// If set, the ped will activate ragdoll much more easily on contact with the player
	ForcePoseCharacterCloth = 320,				// Default cloth pose will be applied if is available in the character cloth when the cloth is created
	HasClothCollisionBounds = 321,				// Ped has cloth collision bounds
	DontBehaveLikeLaw = 324,					// If set on a non-law ped that has law like behavior (i.e. security) then that ped will not use the law like behaviors/logic
	DisablePoliceInvestigatingBody = 326,		// If set, police will not perform the CTaskShockingPoliceInvestigate behavior on the ped	
	DisableWritheShootFromGround = 327,			// If set, the ped will no longer shoot while writhing
	LowerPriorityOfWarpSeats = 328,				// If set the ped will only just the warp entry points if there are no animated entry points available
	DisableTalkTo = 329,						// If set the ped can't be talked to
	DontBlip = 330,								// If set the ped will not be blipped by the wanted system
	IgnoreLegIkRestrictions = 332,				// If set, the ped will ignore leg IK request restrictions for non-player peds
	ForceNoTimesliceIntelligenceUpdate = 333,	// If set, the ped will never have their intelligence update time sliced across frames.
	AllowTaskDoNothingTimeslicing = 339,		// If set, this ped will timeslice it's DoNothing Task when computing default task.	
	NotAllowedToJackAnyPlayers = 342,			// If set, this ped will not be allowed to jack any other players (not synced)
	AlwaysLeaveTrainUponArrival = 345,			// If set, this ped will always exit the train when it stops at a station.
	OnlyWritheFromWeaponDamage = 347,			// If set, Only allow ped to wrothe from weapon damage, not from other stuff, like small vehicle impacts
	UseSloMoBloodVfx = 348,						// If set, this ped will use slo mo blood vfx instead of the normal ones (these effects must be included in the script particle asset)
	PreventDraggedOutOfCarThreatResponse = 350,	// Don't do threat response when dragged out of a car
	ForceDeepSurfaceCheck = 356,				// Don't do distance from camera culling of the deep surface check, needed for detecting snow, mud, etc.
	DisableDeepSurfaceAnims = 357,				// Disable deep surface anims to prevent them slowing ped down.
	DontBlipNotSynced = 358,				    // If set the ped will not be blipped by the wanted system, this will not be synced and be set on clones so the behaviour can be changed per player
	IsDuckingInVehicle = 359,					// Query only, see if the ped is ducking in a vehicle
	PreventAutoShuffleToTurretSeat = 360,		// If set the ped will not automatically shuffle to the turret seat when it becomes free
	DisableEventInteriorStatusCheck = 361,		// Disables the ignore events based on interior status check which normally has peds inside ignore events from outside
	TreatDislikeAsHateWhenInCombat = 364,		// If the ped this is set on is in combat then any dislike feeling they have towards other peds will be treated as a hate feeling
	OnlyUpdateTargetWantedIfSeen = 365,			// Law with this set will only update the WL is the target player is seen. This includes on combat initialization as well as during normal LOS checks (ignoring "last known position" reports)
	AllowAutoShuffleToDriversSeat = 366,		// Allows this ped to auto shuffle to the driver seat of a vehicle if the driver is dead (law and MP peds would do this normally)
	PreventReactingToSilencedCloneBullets = 372,	// If set prevents the ped from reacting to silenced bullets fired from network clone peds (use for peds where stealth kills are important)
	DisableInjuredCryForHelpEvents = 373,		// Blocks ped from creating the injured cry for help events (run over, tazed or melee would usually do this)
	NeverLeaveTrain = 374,                      // Prevents peds riding trains from getting off them
	DontDropJetpackOnDeath = 375,				// Prevents ped dropping jetpack when they die
	DisableAutoEquipHelmetsInBikes = 380,		// Prevents ped from auto-equipping helmets when entering a bike (includes quadbikes)
	DisableAutoEquipHelmetsInAicraft = 381,		// Prevents ped from auto-equipping helmets when entering an aircraft
	HasBareFeet = 389,							// Flag to indicate that player has no shoes(used for first person aiming camera)  
	UNUSED_REPLACE_ME_2 = 390,					// 
	GoOnWithoutVehicleIfItIsUnableToGetBackToRoad = 391, // It will force the ped to abandon its vehicle (when using TaskGoToPointAnyMeans) if it is unable to get back to road
	BlockDroppingHealthSnacksOnDeath = 392,		// This will block health pickups from being created when the ped dies.
	ForceThreatResponseToNonFriendToFriendMeleeActions = 394, // Forces threat response to melee actions from non friend to friend peds.
	DontRespondToRandomPedsDamage = 395, // Do not respond to random peds damage.
	AllowContinuousThreatResponseWantedLevelUpdates = 396, // Shares the same logic of OnlyUpdateTargetWantedIfSeen but will continue to check even after the initial WL is set
	KeepTargetLossResponseOnCleanup = 397,		// The target loss response will not be reset to exit task on cleanup if this is set
	PlayersDontDragMeOutOfCar = 398,				// Similar to DontDragMeOutCar except it will still allow AI to drag the ped out of a vehicle
	BroadcastRepondedToThreatWhenGoingToPointShooting = 399, // Whenever the ped starts shooting while going to a point, it trigger a responded to threat broadcast
	IgnorePedTypeForIsFriendlyWith = 400,		// If this is set then IsFriendlyWith will ignore the ped type checks (i.e. two PEDTYPE_COP peds are not automatically friendly)
	TreatNonFriendlyAsHateWhenInCombat = 401, // Any non friendly ped will be considered as hated instead when in combat
	DontLeaveVehicleIfLeaderNotInVehicle = 402,	// Supresses "LeaderExistedCarAsDriver" events. Ped won't exit vehicle if leader isn't in it as well.
	AllowMeleeReactionIfMeleeProofIsOn = 404, //Allow melee reaction to come through even if proof is on
	UseNormalExplosionDamageWhenBlownUpInVehicle = 407,	// If this is set, ped won't be instantly killed if vehicle is blown up. Instead, they will take normal explosive damage and be forced to exit the vehicle if they're still alive.
	DisableHomingMissileLockForVehiclePedInside = 408,  // Blocks locking on of the vehicle that the ped is inside.
	DisableTakeOffScubaGear = 409,  // Disable taking off the scuba gear. Same as PRF_DisableTakeOffScubaGear but on a config flag.
	IgnoreMeleeFistWeaponDamageMult = 410,	// Melee fist weapons (ie knuckle duster) won't apply relative health damage scaler (MeleeRightFistTargetHealthDamageScaler in weapon info).
	LawPedsCanFleeFromNonWantedPlayer = 411,	// Law peds will be triggered to flee if player triggers an appropriate event (even if ped is not wanted) instead of entering combat. NB: Only synced over the network when set on players.
	ForceBlipSecurityPedsIfPlayerIsWanted =	412,	// Forces security peds (not cop peds) to be blipped on the minimap if the player is wanted. Set this on the local player.
	UseGoToPointForScenarioNavigation = 414,	// Don't use nav mesh for navigating to scenario points. DLC Hack for yachts
	DontClearLocalPassengersWantedLevel = 415,	// Don't clear local ped's wanted level when remote ped in the same car has his wanted level cleared by script.
	BlockAutoSwapOnWeaponPickups = 416,	// Block auto weapon swaps for weapon pickups.
	ThisPedIsATargetPriorityForAI = 417,	// Increase AI targeting score for peds with this flag.
	IsSwitchingHelmetVisor = 418, // Indicates ped is using switch helmet visor up/down anim
	ForceHelmetVisorSwitch = 419, // Indicates ped is using switch helmet visor up/down anim
	UseOverrideFootstepPtFx = 421, // Overrides ped footstep particle effects with the overriden footstep effect
	DisableVehicleCombat = 422, // Disables vehicle combat.
	AllowBikeAlternateAnimations = 424, // Allows transition into bike alternate animations (PI menu option)
	UseLockpickVehicleEntryAnimations = 426, // Use Franklin's alternate lockpicking animations for forced entry
	IgnoreInteriorCheckForSprinting = 427, // When set, player will be able to sprint inside interriors even if it is tagged to prevent it.
	SwatHeliSpawnWithinLastSpottedLocation = 428, // When set, swat helicopters will spawn within last spotted location instead of actual ped location (and target is a player)
	DisableStartEngine = 429, // Prevents ped from playing start engine anims (and turning engine on)
	IgnoreBeingOnFire = 430, // Makes ped ignore being on fire (fleeing, reacting to CEventOnFire event)
	DisableTurretOrRearSeatPreference = 431, // Disables turret seat and activity seat preference for vehicle entry for local player
	DisableWantedHelicopterSpawning = 432, // Will not spawn wanted helicopters to chase after this target
	UseTargetPerceptionForCreatingAimedAtEvents = 433, // Will only create aimed at events if player is within normal perception of the target
	DisableHomingMissileLockon = 434, // Will prevent homing lockon on this ped
	ForceIgnoreMaxMeleeActiveSupportCombatants = 435, //Ignore max number of active support combatants and let ped join them as such
	StayInDefensiveAreaWhenInVehicle = 436, // Will try to stay within set defensive area while driving a vehicle
	DontShoutTargetPosition = 437, // Will prevent the ped from communicating target position to all other friendly peds
	DisableHelmetArmor = 438, // Will apply full headshot damage, regardless if ped has a helmet (or armored one)
	PreventVehExitDueToInvalidWeapon = 441, // Will prevent ped from automatically being forced out of vehicle due to weapon being invalid (e.g. turret seats after going into water)
	IgnoreNetSessionFriendlyFireCheckForAllowDamage = 442, // Will ignore the friendly fire setting set by NETWORK_SET_FRIENDLY_FIRE_OPTION when checking if ped can be damaged
	DontLeaveCombatIfTargetPlayerIsAttackedByPolice = 443, // Will make ped stay in combat even if the player hes targeting starts being attacked by cops
	CheckLockedBeforeWarp = 444, // Will check when entering a vehicle if it is locked before warping
	DontShuffleInVehicleToMakeRoom = 445, // Will prevent a player from shuffling across to make room if another player is entering from the same side
	GiveWeaponOnGetup = 446, // Will give the ped a weapon to use once their weapon is removed for getups
	DontHitVehicleWithProjectiles = 447, // Ped fired projectiles will ignore the vehicle they are in
	DisableForcedEntryForOpenVehiclesFromTryLockedDoor = 448, // Will prevent ped from forcing entry into cars that are open from TryLockedDoor state
	FiresDummyRockets = 449, // his ped will fire rockets that explode when close to its target, and won't affect it
	DecoyPed = 451,  // Will make this ped a decoy ped that will focus targeting
	HasEstablishedDecoy = 452,  // This ped has created a decoy
	BlockDispatchedHelicoptersFromLanding = 453,  // Will prevent dispatched helicopters from landing and dropping off peds
	DontCryForHelpOnStun = 454,  // Will prevent peds from crying for help when shot with the stun gun
	CanBeIncapacitated = 456,  // If set, the ped may be incapacitated
	DontChangeTargetFromMelee = 458,  // If set, we wont set a new target after a melee attack
	RagdollFloatsIndefinitely = 460,  // Prevents a dead ped from sinking
	BlockElectricWeaponDamage = 461  // Blocks electric weapon damage
}