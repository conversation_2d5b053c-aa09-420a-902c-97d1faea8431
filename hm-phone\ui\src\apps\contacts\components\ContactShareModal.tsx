import React, { useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useContactsStore } from '../stores/contactsStore';
import { useModalStore } from '../../../common/stores/modalStore';
import { useNotificationStore } from '../../../notifications/stores/notificationStore';

/**
 * Contact Share Modal (Airdrop)
 *
 * This modal is shown when a player receives a contact share request.
 * It appears on top of whatever app is currently open.
 */
const ContactShareModal: React.FC = () => {
  const { pendingShareRequest } = useContactsStore();
  const { acceptContactShare, declineContactShare } = useContactsStore().actions;
  const { closeModal } = useModalStore();
  const { addNotification } = useNotificationStore();
  const [timeRemaining, setTimeRemaining] = useState(30); // 30 seconds countdown

  // Create a memoized function to convert the modal to a notification
  const convertToNotification = useCallback(() => {
    if (!pendingShareRequest) return;

    const { contact, senderName } = pendingShareRequest;

    // Create a notification with accept/decline actions
    addNotification({
      id: Date.now(),
      appId: 3, // Contacts app ID
      title: 'Contact Share',
      message: `${senderName} wants to share ${contact.name} with you`,
      type: 'info',
      timestamp: Date.now(),
      read: false,
      icon: 'fas fa-address-book',
      persistent: true,
      actions: [
        {
          label: 'Accept',
          onPress: () => {
            acceptContactShare(pendingShareRequest);
          },
          primary: true,
          dismissOnClick: true
        },
        {
          label: 'Decline',
          onPress: () => {
            declineContactShare(pendingShareRequest);
          },
          dismissOnClick: true
        }
      ],
      metadata: {
        contactData: { ...pendingShareRequest }
      }
    });

    // Close the modal
    closeModal();
  }, [pendingShareRequest, addNotification, closeModal, acceptContactShare, declineContactShare]);

  // Handle accept button click
  const handleAccept = useCallback(() => {
    if (!pendingShareRequest) return;
    acceptContactShare(pendingShareRequest);
    closeModal();
  }, [acceptContactShare, closeModal, pendingShareRequest]);

  // Handle decline button click
  const handleDecline = useCallback(() => {
    if (!pendingShareRequest) return;
    declineContactShare(pendingShareRequest);
    closeModal();
  }, [declineContactShare, closeModal, pendingShareRequest]);

  // Set up a timer to convert the modal to a notification after 30 seconds
  useEffect(() => {
    if (!pendingShareRequest) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          // Time's up, convert to notification
          convertToNotification();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Clean up the timer when the component unmounts
    return () => clearInterval(timer);
  }, [convertToNotification, pendingShareRequest]);

  // If no pending request, return null
  if (!pendingShareRequest) {
    return null;
  }

  const { contact, senderName } = pendingShareRequest;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    >
      <div className="w-[90%] max-w-xs bg-[#0a0f1a] rounded-xl overflow-hidden shadow-xl border border-white/10">
        <div className="p-4 bg-white/5 flex items-center justify-between">
          <h3 className="text-lg font-medium text-white">Contact Share</h3>
          <div className="flex items-center">
            <div className="mr-3 text-xs text-white/60">{timeRemaining}s</div>
            <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
              <i className="fas fa-share-alt text-blue-400"></i>
            </div>
          </div>
        </div>

        <div className="p-4">
          <div className="flex items-center mb-4 bg-white/5 p-3 rounded-lg">
            <div className="w-12 h-12 rounded-full overflow-hidden mr-3">
              {contact.avatar ? (
                <img
                  src={contact.avatar}
                  alt={contact.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center">
                  <span className="text-white text-lg font-medium">
                    {contact.name ? contact.name[0].toUpperCase() : '#'}
                  </span>
                </div>
              )}
            </div>
            <div>
              <p className="text-white font-medium">{contact.name}</p>
              <p className="text-white/60 text-sm">{contact.number}</p>
            </div>
          </div>

          <p className="text-white/80 mb-4">
            <span className="font-medium text-white">{senderName}</span> wants to share this contact
            with you.
          </p>

          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDecline}
              className="px-4 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition"
            >
              Decline
            </button>
            <button
              onClick={handleAccept}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition"
            >
              Accept
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ContactShareModal;
