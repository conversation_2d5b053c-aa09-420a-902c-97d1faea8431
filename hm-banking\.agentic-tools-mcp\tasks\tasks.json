{"projects": [{"id": "cd3820b5-c897-4954-aff4-b85db4afb198", "name": "HM Banking System Development", "description": "Development of the new hm-banking resource for FiveM, including UI, game scripts, and integration with existing systems.", "createdAt": "2025-06-08T12:26:21.143Z", "updatedAt": "2025-06-08T12:26:21.143Z"}], "tasks": [{"id": "13a62b9a-22e5-4d4c-bead-741104f97b83", "name": "Design Core Banking UI Mockups/Layouts", "details": "Design core banking UI mockups and layouts. Review if still needed given that initial UI implementation is progressing with style guidance from hm-inventory.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:30.545Z", "updatedAt": "2025-06-08T13:06:21.945Z", "dependsOn": [], "priority": 5, "complexity": 3, "status": "pending", "tags": ["ui", "design", "planning"], "estimatedHours": 4}, {"id": "2889e8b8-55ed-4293-9571-b9e3dda9e607", "name": "Implement Basic Banking UI Structure (React + Tailwind)", "details": "Implement the basic structure of the banking UI using React and Tailwind CSS. Integrate the Zustand store for state management. Style consistently with hm-inventory, focusing on dark slate themes and emerald/teal accents. Ensure responsive design for typical game UI dimensions. Iteratively refine based on hm-inventory color analysis: use slate-800/900 for main backgrounds, slate-600/700 for cards/sections, emerald/teal for primary actions/highlights, light text (slate-100/200) for main info, muted text (slate-300/400) for secondary info, and slate-600/700 for borders. Add hover effects for interactive elements.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:30.670Z", "updatedAt": "2025-06-08T13:08:18.560Z", "dependsOn": ["13a62b9a-22e5-4d4c-bead-741104f97b83", "35e0c58e-7bf4-4fc8-9f1f-c2a98e97eb11"], "priority": 5, "complexity": 5, "status": "in-progress", "tags": ["ui", "react", "tailwind", "nui"], "estimatedHours": 8}, {"id": "4decc3a7-fa69-47d5-9ae1-e7fd0555431d", "name": "Client Script: UI Interaction & Basic NUI Events", "details": "Develop client-side logic to handle player interactions with ATMs/Bank Tellers. This includes opening/closing the banking UI, and sending NUI messages for deposits, withdrawals, and transfers.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:30.766Z", "updatedAt": "2025-06-08T12:26:30.766Z", "dependsOn": [], "priority": 5, "complexity": 4, "status": "pending", "tags": ["client-script", "nui", "gameplay"], "estimatedHours": 6}, {"id": "bf357b0d-727f-4282-af54-22b478682f8f", "name": "Server Script: Core Transaction Logic & Database Interaction", "details": "Implement server-side logic to handle banking transactions: fetching balance, processing deposits, withdrawals, and player-to-player transfers. Integrate with hm-multicharacter (or equivalent) for balance storage and oxmysql for logging transactions.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:30.861Z", "updatedAt": "2025-06-08T12:26:30.861Z", "dependsOn": [], "priority": 5, "complexity": 6, "status": "pending", "tags": ["server-script", "database", "gameplay", "integration"], "estimatedHours": 10}, {"id": "9422e484-635f-475f-904d-70d136f8a3df", "name": "Implement Transaction History (UI and Server)", "details": "Create a detailed transaction history log accessible to players via the UI. Store transaction type, amount, counterparties (if any), and timestamp.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:30.954Z", "updatedAt": "2025-06-08T12:29:32.975Z", "dependsOn": ["2889e8b8-55ed-4293-9571-b9e3dda9e607", "bf357b0d-727f-4282-af54-22b478682f8f"], "priority": 5, "complexity": 3, "status": "pending", "tags": ["ui", "server-script", "feature"], "estimatedHours": 5}, {"id": "35e0c58e-7bf4-4fc8-9f1f-c2a98e97eb11", "name": "Shared Scripts: Define Banking Data Types", "details": "Define and implement shared types and interfaces for banking data structures (e.g., Account, Transaction) to be used by client, server, and UI.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": true, "createdAt": "2025-06-08T12:26:31.045Z", "updatedAt": "2025-06-08T12:40:29.662Z", "dependsOn": [], "priority": 5, "complexity": 3, "status": "done", "tags": ["shared-script", "types", "planning"], "estimatedHours": 2}, {"id": "5eb357cb-ffe5-49dd-9530-3d164364a172", "name": "Testing and Debugging - Core Features", "details": "Thoroughly test all banking functionalities: deposits, withdrawals, transfers, balance updates, and transaction history. Test UI responsiveness and NUI event handling.", "projectId": "cd3820b5-c897-4954-aff4-b85db4afb198", "completed": false, "createdAt": "2025-06-08T12:26:31.137Z", "updatedAt": "2025-06-08T12:29:33.094Z", "dependsOn": ["9422e484-635f-475f-904d-70d136f8a3df", "4decc3a7-fa69-47d5-9ae1-e7fd0555431d"], "priority": 5, "complexity": 4, "status": "pending", "tags": ["testing", "qa"], "estimatedHours": 6}], "subtasks": []}