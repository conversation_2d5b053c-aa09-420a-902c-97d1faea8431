import { create } from 'zustand';
import { BankingState, Transaction, Account } from '../types/bankingTypes';

const mockAccounts: Account[] = [
  {
    id: 1,
    name: 'Main Checking',
    type: 'checking',
    number: '****4321',
    balance: 2177.31,
    currency: 'EUR',
    color: 'emerald',
    icon: 'wallet'
  },
  {
    id: 2,
    name: 'Savings',
    type: 'savings',
    number: '****8765',
    balance: 5430.82,
    currency: 'EUR',
    color: 'blue',
    icon: 'piggy-bank'
  },
  {
    id: 3,
    name: 'Credit Card',
    type: 'credit',
    number: '****9012',
    balance: -320.45,
    currency: 'EUR',
    color: 'purple',
    icon: 'credit-card'
  }
];

const mockTransactions: Transaction[] = [
  {
    id: 1,
    merchantName: 'Supermarket',
    amount: 42.5,
    timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/96',
    description: 'Weekly groceries and household items',
    accountId: 1
  },
  {
    id: 2,
    merchantName: 'Salary',
    amount: 2500.0,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
    type: 'credit',
    icon: 'https://picsum.photos/97',
    description: 'Monthly salary payment',
    accountId: 1
  },
  {
    id: 3,
    merchantName: 'Netflix',
    amount: 14.99,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/98',
    description: 'Monthly subscription',
    accountId: 1
  },
  {
    id: 4,
    merchantName: 'Amazon',
    amount: 156.32,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/99',
    description: 'Electronics purchase',
    accountId: 1
  },
  {
    id: 5,
    merchantName: 'Interest',
    amount: 12.5,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 96).toISOString(),
    type: 'credit',
    icon: 'https://picsum.photos/100',
    description: 'Monthly interest',
    accountId: 2
  },
  {
    id: 6,
    merchantName: 'Savings Transfer',
    amount: 500.0,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 120).toISOString(),
    type: 'credit',
    icon: 'https://picsum.photos/101',
    description: 'Monthly savings',
    accountId: 2
  },
  {
    id: 7,
    merchantName: 'Restaurant',
    amount: 89.99,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 144).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/102',
    description: 'Dinner with friends',
    accountId: 3
  },
  {
    id: 8,
    merchantName: 'Gas Station',
    amount: 45.0,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 168).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/103',
    description: 'Fuel refill',
    accountId: 3
  },
  {
    id: 9,
    merchantName: 'Phone Bill',
    amount: 65.0,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 192).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/104',
    description: 'Monthly phone bill',
    accountId: 1
  },
  {
    id: 10,
    merchantName: 'Gym',
    amount: 29.99,
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 216).toISOString(),
    type: 'debit',
    icon: 'https://picsum.photos/105',
    description: 'Monthly membership',
    accountId: 3
  }
];

export const useBankingStore = create<BankingState>((set, get) => ({
  accounts: mockAccounts,
  selectedAccountId: 1,
  transactions: mockTransactions,
  isLoading: false,

  setSelectedAccount: (accountId: number) => set({ selectedAccountId: accountId }),

  addTransaction: (transaction: Transaction) =>
    set(state => {
      const updatedAccounts = state.accounts.map(account => {
        if (account.id === transaction.accountId) {
          return {
            ...account,
            balance:
              account.balance +
              (transaction.type === 'credit' ? transaction.amount : -transaction.amount)
          };
        }
        return account;
      });

      return {
        accounts: updatedAccounts,
        transactions: [transaction, ...state.transactions]
      };
    }),

  transferBetweenAccounts: (fromId: number, toId: number, amount: number) =>
    set(state => {
      const timestamp = new Date().toISOString();
      const transferId = Date.now();

      const debitTransaction: Transaction = {
        id: transferId,
        merchantName: 'Internal Transfer',
        amount: amount,
        timestamp,
        type: 'debit',
        description: 'Transfer to ' + state.accounts.find(a => a.id === toId)?.name,
        accountId: fromId
      };

      const creditTransaction: Transaction = {
        id: transferId + 1,
        merchantName: 'Internal Transfer',
        amount: amount,
        timestamp,
        type: 'credit',
        description: 'Transfer from ' + state.accounts.find(a => a.id === fromId)?.name,
        accountId: toId
      };

      const updatedAccounts = state.accounts.map(account => {
        if (account.id === fromId) {
          return { ...account, balance: account.balance - amount };
        }
        if (account.id === toId) {
          return { ...account, balance: account.balance + amount };
        }
        return account;
      });

      return {
        accounts: updatedAccounts,
        transactions: [creditTransaction, debitTransaction, ...state.transactions]
      };
    }),

  getAccountBalance: (accountId: number) => {
    const account = get().accounts.find(a => a.id === accountId);
    return account?.balance || 0;
  },

  getAccountTransactions: (accountId: number) => {
    return get().transactions.filter(t => t.accountId === accountId);
  }
}));
