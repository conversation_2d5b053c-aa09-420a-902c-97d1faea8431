/**
 * Player data message handlers
 */
import { registerEventHandler } from '../../fivem/clientEventReceiver';
import { usePhoneStore } from '../../common/stores/phoneStateStore';
import { PlayerData } from '../../apps/settings/types/playerTypes';

// Register handler for player data
registerEventHandler('phone', 'playerData', data => {
  console.log('[Phone] Received player data:', data);

  // Validate data
  if (data && typeof data === 'object') {
    // Update the store with the player data
    usePhoneStore.getState().handlers.onSetPlayerData(data as PlayerData);
  } else {
    console.error('[Phone] Received invalid player data:', data);
  }
});
